<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Daily Payment Summary - Phone Point Dar</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 0;
            background-color: #f4f4f4;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            background: #2563eb;
            color: white;
            padding: 30px 20px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 28px;
        }
        .content {
            padding: 30px 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .stat-card {
            background: #f8fafc;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            border-left: 4px solid #2563eb;
        }
        .stat-card.success {
            border-left-color: #10b981;
        }
        .stat-card.warning {
            border-left-color: #f59e0b;
        }
        .stat-card.revenue {
            border-left-color: #8b5cf6;
        }
        .stat-number {
            font-size: 32px;
            font-weight: bold;
            margin: 10px 0;
        }
        .stat-number.success {
            color: #10b981;
        }
        .stat-number.warning {
            color: #f59e0b;
        }
        .stat-number.revenue {
            color: #8b5cf6;
        }
        .stat-label {
            color: #6b7280;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        .target-status {
            background: #f0fdf4;
            border: 1px solid #10b981;
            color: #065f46;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            text-align: center;
        }
        .target-status.missed {
            background: #fef2f2;
            border-color: #dc2626;
            color: #991b1b;
        }
        .footer {
            background: #f8fafc;
            padding: 20px;
            text-align: center;
            color: #6b7280;
            font-size: 14px;
            border-top: 1px solid #e5e7eb;
        }
        .button {
            display: inline-block;
            background: #2563eb;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 6px;
            margin: 20px 0;
        }
        @media (max-width: 600px) {
            .stats-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 Daily Payment Summary</h1>
            <p>Phone Point Dar - {{date}}</p>
        </div>
        
        <div class="content">
            <h2>Payment Performance Overview</h2>
            
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-label">Total Transactions</div>
                    <div class="stat-number">{{transactions}}</div>
                </div>
                
                <div class="stat-card success">
                    <div class="stat-label">Successful Payments</div>
                    <div class="stat-number success">{{successful}}</div>
                </div>
                
                <div class="stat-card warning">
                    <div class="stat-label">Failed Payments</div>
                    <div class="stat-number warning">{{failed}}</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-label">Success Rate</div>
                    <div class="stat-number">{{successRate}}%</div>
                </div>
            </div>
            
            <div class="stat-card revenue" style="margin: 20px 0;">
                <div class="stat-label">Total Revenue</div>
                <div class="stat-number revenue">{{revenue}} TZS</div>
            </div>
            
            <h3>Revenue Target Status</h3>
            <div class="target-status {{#unless targetMet}}missed{{/unless}}">
                {{#if targetMet}}
                    ✅ <strong>Target Achieved!</strong> Daily revenue target of {{revenueTarget}} TZS was met.
                {{else}}
                    ❌ <strong>Target Missed.</strong> Daily revenue of {{revenue}} TZS fell short of the {{revenueTarget}} TZS target.
                {{/if}}
            </div>
            
            <h3>Key Insights</h3>
            <ul>
                {{#if (gt transactions 0)}}
                <li><strong>Transaction Volume:</strong> {{transactions}} payments processed</li>
                <li><strong>Success Rate:</strong> {{successRate}}% of payments completed successfully</li>
                {{#if (gt failed 0)}}
                <li><strong>Failed Payments:</strong> {{failed}} payments failed - investigate common causes</li>
                {{/if}}
                {{#if targetMet}}
                <li><strong>Revenue Performance:</strong> Excellent! Target exceeded by {{subtract revenue revenueTarget}} TZS</li>
                {{else}}
                <li><strong>Revenue Gap:</strong> {{subtract revenueTarget revenue}} TZS short of daily target</li>
                {{/if}}
                {{else}}
                <li><strong>No Transactions:</strong> No payments were processed yesterday</li>
                <li><strong>Action Required:</strong> Investigate low transaction volume</li>
                {{/if}}
            </ul>
            
            <h3>Recommended Actions</h3>
            <ul>
                {{#if (lt successRate 90)}}
                <li>🔍 Investigate payment failure causes - success rate below 90%</li>
                {{/if}}
                {{#if (gt failed 5)}}
                <li>📞 Contact ClickPesa support regarding payment failures</li>
                {{/if}}
                {{#unless targetMet}}
                <li>📈 Review marketing strategies to increase sales</li>
                <li>💰 Consider promotional campaigns to boost revenue</li>
                {{/unless}}
                {{#if (eq transactions 0)}}
                <li>🚨 Urgent: Investigate why no payments were processed</li>
                <li>🔧 Check payment system functionality</li>
                {{/if}}
                <li>📊 Review detailed analytics in the admin dashboard</li>
                <li>📱 Monitor customer feedback and support tickets</li>
            </ul>
            
            <a href="{{dashboardUrl}}" class="button">View Detailed Analytics</a>
        </div>
        
        <div class="footer">
            <p><strong>Phone Point Dar Payment Analytics</strong><br>
            Automated daily summary for payment performance tracking</p>
            <p>📱 Smartphones • 📟 Feature Phones • 📱 Accessories • 🔧 Repairs</p>
            <p style="margin-top: 20px; font-size: 12px;">
                Report generated automatically at midnight (EAT)<br>
                Date: {{date}} | Next report: Tomorrow
            </p>
        </div>
    </div>
</body>
</html>
