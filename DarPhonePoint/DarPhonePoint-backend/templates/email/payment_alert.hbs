<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Alert - Phone Point Dar</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 0;
            background-color: #f4f4f4;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            background: #dc2626;
            color: white;
            padding: 20px;
            text-align: center;
        }
        .header.warning {
            background: #f59e0b;
        }
        .header h1 {
            margin: 0;
            font-size: 24px;
        }
        .content {
            padding: 30px 20px;
        }
        .alert-box {
            background: #fef2f2;
            border-left: 4px solid #dc2626;
            padding: 20px;
            margin: 20px 0;
            border-radius: 4px;
        }
        .alert-box.warning {
            background: #fffbeb;
            border-left-color: #f59e0b;
        }
        .alert-box h3 {
            margin: 0 0 10px 0;
            color: #dc2626;
        }
        .alert-box.warning h3 {
            color: #f59e0b;
        }
        .data-section {
            background: #f8fafc;
            padding: 15px;
            border-radius: 4px;
            margin: 15px 0;
            font-family: monospace;
            font-size: 14px;
        }
        .footer {
            background: #f8fafc;
            padding: 20px;
            text-align: center;
            color: #6b7280;
            font-size: 14px;
            border-top: 1px solid #e5e7eb;
        }
        .button {
            display: inline-block;
            background: #2563eb;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 6px;
            margin: 20px 0;
        }
        .severity {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
        }
        .severity.critical {
            background: #dc2626;
            color: white;
        }
        .severity.warning {
            background: #f59e0b;
            color: white;
        }
        .severity.info {
            background: #2563eb;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header {{#if (eq severity 'warning')}}warning{{/if}}">
            <h1>🚨 Payment System Alert</h1>
            <p>Phone Point Dar - Payment Monitoring</p>
        </div>
        
        <div class="content">
            <h2>Alert Details</h2>
            
            <div class="alert-box {{#if (eq severity 'warning')}}warning{{/if}}">
                <h3>{{alertType}}</h3>
                <p><strong>Severity:</strong> <span class="severity {{severity}}">{{severity}}</span></p>
                <p><strong>Message:</strong> {{message}}</p>
                <p><strong>Timestamp:</strong> {{timestamp}}</p>
            </div>
            
            {{#if data}}
            <h3>Additional Data</h3>
            <div class="data-section">
                <pre>{{data}}</pre>
            </div>
            {{/if}}
            
            <h3>Recommended Actions</h3>
            <ul>
                {{#if (eq alertType 'consecutive_failures')}}
                <li>Check ClickPesa service status</li>
                <li>Verify API credentials and connectivity</li>
                <li>Review recent payment logs for error patterns</li>
                <li>Contact ClickPesa support if issue persists</li>
                {{/if}}
                
                {{#if (eq alertType 'high_failure_rate')}}
                <li>Investigate common failure reasons</li>
                <li>Check customer payment method preferences</li>
                <li>Verify payment gateway configuration</li>
                <li>Monitor customer support tickets</li>
                {{/if}}
                
                {{#if (eq alertType 'unusual_amount')}}
                <li>Verify transaction legitimacy</li>
                <li>Check customer identity and order details</li>
                <li>Monitor for potential fraud indicators</li>
                <li>Contact customer if necessary</li>
                {{/if}}
                
                {{#if (eq alertType 'stale_pending_payments')}}
                <li>Review pending payment queue</li>
                <li>Check webhook delivery status</li>
                <li>Manually verify payment status with ClickPesa</li>
                <li>Contact affected customers</li>
                {{/if}}
                
                <li>Access the admin dashboard for detailed analysis</li>
                <li>Review payment monitoring metrics</li>
                <li>Check system logs for additional context</li>
            </ul>
            
            {{#if dashboardUrl}}
            <a href="{{dashboardUrl}}" class="button">View Payment Dashboard</a>
            {{/if}}
        </div>
        
        <div class="footer">
            <p><strong>Phone Point Dar Payment Monitoring</strong><br>
            Automated alert system for payment processing</p>
            <p>This alert was generated automatically. Please investigate promptly.</p>
            <p style="margin-top: 20px; font-size: 12px;">
                Alert ID: {{alertType}}_{{timestamp}}
            </p>
        </div>
    </div>
</body>
</html>
