<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>System Alert - Phone Point Dar</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f4f4f4;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 3px solid #dc3545;
        }
        .logo {
            font-size: 24px;
            font-weight: bold;
            color: #2563eb;
            margin-bottom: 10px;
        }
        .alert-icon {
            font-size: 48px;
            color: #dc3545;
            margin-bottom: 15px;
        }
        .alert-title {
            color: #dc3545;
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .alert-details {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #dc3545;
        }
        .detail-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }
        .detail-row:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }
        .detail-label {
            font-weight: bold;
            color: #495057;
        }
        .detail-value {
            color: #212529;
        }
        .critical {
            color: #dc3545;
            font-weight: bold;
        }
        .message {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
            color: #6c757d;
            font-size: 14px;
        }
        .action-button {
            display: inline-block;
            background: #2563eb;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 5px;
            margin: 20px 0;
            font-weight: bold;
        }
        .timestamp {
            color: #6c757d;
            font-size: 14px;
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">📱 Phone Point Dar</div>
            <div class="alert-icon">🚨</div>
            <div class="alert-title">System Alert</div>
            <div class="timestamp">{{timestamp}}</div>
        </div>

        <div class="alert-details">
            <div class="detail-row">
                <span class="detail-label">Alert Type:</span>
                <span class="detail-value critical">{{alert_type}}</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">Current Value:</span>
                <span class="detail-value critical">{{current_value}}%</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">Threshold:</span>
                <span class="detail-value">{{threshold}}%</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">Server:</span>
                <span class="detail-value">{{server_info.server_name}}</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">Environment:</span>
                <span class="detail-value">{{server_info.environment}}</span>
            </div>
        </div>

        <div class="message">
            <strong>Alert Message:</strong><br>
            {{message}}
        </div>

        <div style="text-align: center;">
            <a href="https://your-monitoring-dashboard.com" class="action-button">
                View Monitoring Dashboard
            </a>
        </div>

        <div style="margin-top: 30px;">
            <h3 style="color: #495057;">Recommended Actions:</h3>
            <ul style="color: #6c757d;">
                {{#if (eq alert_type 'memory')}}
                <li>Check for memory leaks in the application</li>
                <li>Restart the server if memory usage is critically high</li>
                <li>Review recent deployments for memory-intensive changes</li>
                <li>Consider scaling up server resources</li>
                {{/if}}
                
                {{#if (eq alert_type 'cpu')}}
                <li>Identify CPU-intensive processes</li>
                <li>Check for infinite loops or heavy computations</li>
                <li>Consider load balancing or scaling</li>
                <li>Review recent code changes</li>
                {{/if}}
                
                {{#if (eq alert_type 'error_rate')}}
                <li>Check application logs for error patterns</li>
                <li>Review recent deployments</li>
                <li>Verify database connectivity</li>
                <li>Check external service dependencies</li>
                {{/if}}
                
                <li>Monitor the situation closely</li>
                <li>Contact the development team if issues persist</li>
            </ul>
        </div>

        <div class="footer">
            <p>This is an automated alert from Phone Point Dar monitoring system.</p>
            <p>If you believe this alert was sent in error, please contact the system administrator.</p>
            <p style="margin-top: 15px;">
                <strong>Phone Point Dar</strong><br>
                Tanzania's Premier Mobile Device Retailer<br>
                📧 <EMAIL> | 📞 +255-XXX-XXX-XXX
            </p>
        </div>
    </div>
</body>
</html>
