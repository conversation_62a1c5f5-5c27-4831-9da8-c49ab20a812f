const mongoose = require('mongoose');

const emailTrackingSchema = new mongoose.Schema({
  recipient: {
    type: String,
    required: true
  },
  subject: {
    type: String,
    required: true
  },
  template: {
    type: String,
    required: true
  },
  status: {
    type: String,
    enum: ['pending', 'sent', 'delivered', 'opened', 'clicked', 'failed'],
    default: 'pending'
  },
  messageId: {
    type: String
  },
  sentAt: Date,
  deliveredAt: Date,
  openedAt: Date,
  clickedAt: Date,
  failedAt: Date,
  error: String,
  metadata: {
    type: Map,
    of: mongoose.Schema.Types.Mixed
  },
  events: [{
    type: {
      type: String,
      enum: ['sent', 'delivered', 'opened', 'clicked', 'failed']
    },
    timestamp: Date,
    details: mongoose.Schema.Types.Mixed
  }]
}, {
  timestamps: true
});

// Indexes for common queries
emailTrackingSchema.index({ createdAt: -1 });
emailTrackingSchema.index({ updatedAt: -1 });
emailTrackingSchema.index({ 'events.timestamp': -1 });

// Methods
emailTrackingSchema.methods.addEvent = async function(type, details = {}) {
  this.events.push({
    type,
    timestamp: new Date(),
    details
  });

  // Update status and relevant timestamp
  this.status = type;
  this[`${type}At`] = new Date();

  if (type === 'failed') {
    this.error = details.error || 'Unknown error';
  }

  await this.save();
};

// Static methods
emailTrackingSchema.statics.getStats = async function(timeframe = '24h') {
  const startDate = new Date();
  switch (timeframe) {
    case '24h':
      startDate.setHours(startDate.getHours() - 24);
      break;
    case '7d':
      startDate.setDate(startDate.getDate() - 7);
      break;
    case '30d':
      startDate.setDate(startDate.getDate() - 30);
      break;
    default:
      startDate.setHours(startDate.getHours() - 24);
  }

  return this.aggregate([
    {
      $match: {
        createdAt: { $gte: startDate }
      }
    },
    {
      $group: {
        _id: '$status',
        count: { $sum: 1 }
      }
    }
  ]);
};

// Virtuals
emailTrackingSchema.virtual('deliveryTime').get(function() {
  if (this.sentAt && this.deliveredAt) {
    return this.deliveredAt - this.sentAt;
  }
  return null;
});

emailTrackingSchema.virtual('openTime').get(function() {
  if (this.deliveredAt && this.openedAt) {
    return this.openedAt - this.deliveredAt;
  }
  return null;
});

const EmailTracking = mongoose.model('EmailTracking', emailTrackingSchema);

module.exports = EmailTracking; 