const mongoose = require('mongoose');

const LeadSchema = new mongoose.Schema({
  name: {
    type: String,
    trim: true
  },
  email: {
    type: String,
    required: [true, 'Please add an email'],
    match: [
      /^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/,
      'Please add a valid email'
    ]
  },
  source: {
    type: String,
    default: 'website'
  },
  lead_magnet: {
    type: String,
    default: '50-essential-ai-prompts'
  },
  status: {
    type: String,
    enum: ['new', 'contacted', 'converted', 'unsubscribed'],
    default: 'new'
  },
  created_at: {
    type: Date,
    default: Date.now
  },
  updated_at: {
    type: Date,
    default: Date.now
  }
});

// Add indexes for better query performance
LeadSchema.index({ email: 1 }, { unique: true });
LeadSchema.index({ source: 1 });
LeadSchema.index({ status: 1 });
LeadSchema.index({ created_at: -1 });
LeadSchema.index({ lead_magnet: 1 });

// Compound indexes for common query patterns
LeadSchema.index({ status: 1, created_at: -1 });
LeadSchema.index({ source: 1, created_at: -1 });

module.exports = mongoose.model('Lead', LeadSchema);