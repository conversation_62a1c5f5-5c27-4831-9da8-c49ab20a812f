const mongoose = require('mongoose');

/**
 * Serial Number Schema for Phone Point Dar
 * Tracks individual product serial numbers for warranty and inventory management
 */
const serialNumberSchema = new mongoose.Schema({
  // Product reference
  product: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Product',
    required: true,
    index: true
  },

  // Serial number details
  serialNumber: {
    type: String,
    required: true,
    unique: true,
    uppercase: true,
    trim: true,
    index: true
  },

  // IMEI for mobile devices (15 digits)
  imei: {
    type: String,
    sparse: true,
    unique: true,
    validate: {
      validator: function(v) {
        return !v || /^\d{15}$/.test(v);
      },
      message: 'IMEI must be exactly 15 digits'
    },
    index: true
  },

  // MAC address for networking equipment
  macAddress: {
    type: String,
    sparse: true,
    unique: true,
    validate: {
      validator: function(v) {
        return !v || /^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$/.test(v);
      },
      message: 'Invalid MAC address format'
    }
  },

  // Status tracking
  status: {
    type: String,
    enum: ['available', 'sold', 'reserved', 'defective', 'returned', 'warranty_claim'],
    default: 'available',
    index: true
  },

  // Condition assessment
  condition: {
    type: String,
    enum: ['new', 'open_box', 'refurbished', 'used_excellent', 'used_good', 'used_fair', 'defective'],
    default: 'new',
    index: true
  },

  // Purchase and supplier information
  supplier: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Supplier',
    index: true
  },

  purchaseDate: {
    type: Date,
    index: true
  },

  purchasePrice: {
    type: Number,
    min: 0
  },

  // Warranty information
  warrantyStartDate: {
    type: Date,
    default: Date.now
  },

  warrantyEndDate: {
    type: Date,
    index: true
  },

  warrantyProvider: {
    type: String,
    enum: ['manufacturer', 'supplier', 'phone_point_dar', 'extended'],
    default: 'manufacturer'
  },

  // Sale information
  saleDate: {
    type: Date,
    sparse: true,
    index: true
  },

  salePrice: {
    type: Number,
    min: 0
  },

  customer: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    sparse: true
  },

  order: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Order',
    sparse: true
  },

  // Location tracking
  location: {
    warehouse: {
      type: String,
      default: 'main'
    },
    shelf: String,
    bin: String
  },

  // Quality control
  qualityCheck: {
    performed: {
      type: Boolean,
      default: false
    },
    performedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    performedAt: Date,
    notes: String,
    passed: Boolean
  },

  // Return/exchange information
  returnInfo: {
    returned: {
      type: Boolean,
      default: false
    },
    returnDate: Date,
    returnReason: String,
    refundAmount: Number,
    exchangedFor: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'SerialNumber'
    }
  },

  // Repair history
  repairHistory: [{
    date: {
      type: Date,
      default: Date.now
    },
    issue: {
      type: String,
      required: true
    },
    resolution: String,
    cost: {
      type: Number,
      min: 0
    },
    technician: String,
    warrantyRepair: {
      type: Boolean,
      default: false
    }
  }],

  // Notes and additional information
  notes: String,

  // Metadata
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },

  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for performance
serialNumberSchema.index({ product: 1, status: 1 });
serialNumberSchema.index({ supplier: 1, purchaseDate: -1 });
serialNumberSchema.index({ warrantyEndDate: 1 });
serialNumberSchema.index({ saleDate: -1 });
serialNumberSchema.index({ 'location.warehouse': 1, 'location.shelf': 1 });

// Virtual for warranty status
serialNumberSchema.virtual('warrantyStatus').get(function() {
  if (!this.warrantyEndDate) return 'unknown';
  
  const now = new Date();
  const daysRemaining = Math.ceil((this.warrantyEndDate - now) / (1000 * 60 * 60 * 24));
  
  if (daysRemaining < 0) return 'expired';
  if (daysRemaining <= 30) return 'expiring_soon';
  return 'active';
});

// Virtual for age in days
serialNumberSchema.virtual('ageInDays').get(function() {
  const purchaseDate = this.purchaseDate || this.createdAt;
  return Math.floor((new Date() - purchaseDate) / (1000 * 60 * 60 * 24));
});

// Pre-save middleware
serialNumberSchema.pre('save', function(next) {
  // Auto-generate warranty end date if not provided
  if (!this.warrantyEndDate && this.warrantyStartDate) {
    // Default 1 year warranty
    this.warrantyEndDate = new Date(this.warrantyStartDate);
    this.warrantyEndDate.setFullYear(this.warrantyEndDate.getFullYear() + 1);
  }

  // Update status based on sale information
  if (this.saleDate && this.status === 'available') {
    this.status = 'sold';
  }

  next();
});

// Static methods
serialNumberSchema.statics.findByProduct = function(productId) {
  return this.find({ product: productId }).populate('supplier customer order');
};

serialNumberSchema.statics.findAvailable = function(productId) {
  return this.find({ 
    product: productId, 
    status: 'available',
    condition: { $in: ['new', 'open_box', 'refurbished'] }
  });
};

serialNumberSchema.statics.findExpiringWarranties = function(days = 30) {
  const futureDate = new Date();
  futureDate.setDate(futureDate.getDate() + days);
  
  return this.find({
    warrantyEndDate: { $lte: futureDate, $gte: new Date() },
    status: 'sold'
  }).populate('product customer');
};

// Instance methods
serialNumberSchema.methods.markAsSold = function(customerId, orderId, salePrice) {
  this.status = 'sold';
  this.customer = customerId;
  this.order = orderId;
  this.saleDate = new Date();
  this.salePrice = salePrice;
  return this.save();
};

serialNumberSchema.methods.processReturn = function(reason, refundAmount) {
  this.status = 'returned';
  this.returnInfo = {
    returned: true,
    returnDate: new Date(),
    returnReason: reason,
    refundAmount: refundAmount
  };
  return this.save();
};

serialNumberSchema.methods.addRepair = function(issue, resolution, cost, technician, isWarranty = false) {
  this.repairHistory.push({
    issue,
    resolution,
    cost,
    technician,
    warrantyRepair: isWarranty
  });
  return this.save();
};

module.exports = mongoose.model('SerialNumber', serialNumberSchema);
