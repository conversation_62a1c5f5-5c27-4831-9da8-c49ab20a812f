const mongoose = require('mongoose');

const BrandSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Please add a brand name'],
    trim: true,
    unique: true,
    maxlength: [100, 'Name cannot be more than 100 characters']
  },
  slug: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    lowercase: true
  },
  description: {
    type: String,
    maxlength: [1000, 'Description cannot be more than 1000 characters']
  },
  
  // Brand identity
  logo_url: {
    type: String,
    trim: true
  },
  website_url: {
    type: String,
    trim: true
  },
  country_of_origin: {
    type: String,
    trim: true
  },
  
  // Display and ordering
  display_order: {
    type: Number,
    default: 0
  },
  is_featured: {
    type: Boolean,
    default: false
  },
  
  // SEO
  meta_title: String,
  meta_description: String,
  
  // Status
  is_active: {
    type: Boolean,
    default: true
  },
  
  // Statistics (will be calculated)
  product_count: {
    type: Number,
    default: 0
  },
  
  created_at: {
    type: Date,
    default: Date.now
  },
  updated_at: {
    type: Date,
    default: Date.now
  }
});

// Pre-save middleware to update timestamps and generate slug
BrandSchema.pre('save', function(next) {
  this.updated_at = new Date();
  
  // Generate slug if not provided
  if (!this.slug && this.name) {
    this.slug = this.name
      .toLowerCase()
      .replace(/[^\w\s-]/g, '') // Remove special characters
      .replace(/\s+/g, '-')     // Replace spaces with hyphens
      .replace(/-+/g, '-')      // Replace multiple hyphens with single
      .trim();
  }
  
  next();
});

// Method to update product count
BrandSchema.methods.updateProductCount = async function() {
  const Product = mongoose.model('Product');
  const count = await Product.countDocuments({ 
    brand: this.name, 
    is_active: true 
  });
  this.product_count = count;
  await this.save();
  return count;
};

// Static method to get popular brands (with most products)
BrandSchema.statics.getPopularBrands = function(limit = 10) {
  return this.find({ is_active: true })
    .sort({ product_count: -1, name: 1 })
    .limit(limit);
};

// Static method to get featured brands
BrandSchema.statics.getFeaturedBrands = function() {
  return this.find({ is_active: true, is_featured: true })
    .sort({ display_order: 1, name: 1 });
};

// Indexes for better query performance
BrandSchema.index({ slug: 1 });
BrandSchema.index({ name: 1 });
BrandSchema.index({ is_active: 1 });
BrandSchema.index({ is_featured: 1 });
BrandSchema.index({ display_order: 1 });
BrandSchema.index({ product_count: -1 });

// Compound indexes for common query patterns
BrandSchema.index({ is_active: 1, is_featured: 1, display_order: 1 });
BrandSchema.index({ is_active: 1, product_count: -1 });

module.exports = mongoose.model('Brand', BrandSchema);
