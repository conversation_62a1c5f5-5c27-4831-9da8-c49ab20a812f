/**
 * TradeIn Model
 * Handles trade-in device information and valuation
 */

const mongoose = require('mongoose');

const TradeInSchema = new mongoose.Schema({
  // Customer information
  customer: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  customer_email: {
    type: String,
    required: true,
    trim: true,
    lowercase: true
  },
  customer_phone: {
    type: String,
    required: true,
    trim: true
  },

  // Device information
  device_brand: {
    type: String,
    required: true,
    trim: true
  },
  device_model: {
    type: String,
    required: true,
    trim: true
  },
  device_storage: {
    type: String,
    trim: true
  },
  device_color: {
    type: String,
    trim: true
  },
  imei: {
    type: String,
    required: true,
    unique: true,
    trim: true
  },

  // Condition assessment
  device_condition: {
    type: String,
    required: true,
    enum: ['excellent', 'good', 'fair', 'poor', 'damaged']
  },
  screen_condition: {
    type: String,
    required: true,
    enum: ['perfect', 'minor_scratches', 'major_scratches', 'cracked', 'broken']
  },
  battery_health: {
    type: Number,
    min: 0,
    max: 100
  },
  functional_issues: [{
    type: String,
    enum: [
      'none',
      'camera_issues',
      'speaker_issues',
      'microphone_issues',
      'charging_issues',
      'button_issues',
      'wifi_issues',
      'cellular_issues',
      'touch_issues',
      'other'
    ]
  }],
  accessories_included: [{
    type: String,
    enum: ['charger', 'earphones', 'box', 'manual', 'case', 'screen_protector']
  }],

  // Valuation
  estimated_value: {
    type: Number,
    required: true,
    min: 0
  },
  final_value: {
    type: Number,
    min: 0
  },
  valuation_notes: {
    type: String,
    trim: true
  },

  // Status tracking
  status: {
    type: String,
    required: true,
    enum: ['pending', 'evaluated', 'accepted', 'rejected', 'completed'],
    default: 'pending'
  },
  evaluation_date: Date,
  acceptance_date: Date,
  completion_date: Date,

  // Images
  device_images: [{
    url: String,
    description: String,
    uploaded_at: {
      type: Date,
      default: Date.now
    }
  }],

  // Related order
  related_order: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Order'
  },

  // Internal notes
  internal_notes: String,
  evaluated_by: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true
});

// Indexes for common queries
TradeInSchema.index({ customer: 1, createdAt: -1 });
TradeInSchema.index({ imei: 1 });
TradeInSchema.index({ status: 1, createdAt: -1 });
TradeInSchema.index({ device_brand: 1, device_model: 1 });

// Virtual for device full name
TradeInSchema.virtual('device_full_name').get(function() {
  return `${this.device_brand} ${this.device_model}`;
});

// Method to calculate trade-in value based on condition
TradeInSchema.methods.calculateValue = function() {
  // Base value lookup (in production, this would be from a pricing database)
  const baseValues = {
    'iPhone 14': 800000,
    'iPhone 13': 650000,
    'Samsung Galaxy S23': 700000,
    'Samsung Galaxy S22': 550000,
    'Tecno Spark 10': 150000,
    'Infinix Hot 12': 120000
  };

  const deviceKey = `${this.device_brand} ${this.device_model}`;
  let baseValue = baseValues[deviceKey] || 100000; // Default base value

  // Condition multipliers
  const conditionMultipliers = {
    'excellent': 0.85,
    'good': 0.70,
    'fair': 0.55,
    'poor': 0.35,
    'damaged': 0.15
  };

  // Screen condition adjustments
  const screenAdjustments = {
    'perfect': 1.0,
    'minor_scratches': 0.95,
    'major_scratches': 0.85,
    'cracked': 0.70,
    'broken': 0.50
  };

  // Battery health adjustment
  const batteryAdjustment = this.battery_health ? (this.battery_health / 100) * 0.2 + 0.8 : 1.0;

  // Functional issues penalty
  const functionalPenalty = this.functional_issues.length > 0 ? 0.9 : 1.0;

  // Accessories bonus
  const accessoriesBonus = this.accessories_included.length > 2 ? 1.05 : 1.0;

  // Calculate final value
  const calculatedValue = baseValue * 
    conditionMultipliers[this.device_condition] * 
    screenAdjustments[this.screen_condition] * 
    batteryAdjustment * 
    functionalPenalty * 
    accessoriesBonus;

  return Math.round(calculatedValue);
};

// Method to update status
TradeInSchema.methods.updateStatus = function(newStatus, userId = null) {
  this.status = newStatus;
  
  switch (newStatus) {
    case 'evaluated':
      this.evaluation_date = new Date();
      this.evaluated_by = userId;
      this.final_value = this.calculateValue();
      break;
    case 'accepted':
      this.acceptance_date = new Date();
      break;
    case 'completed':
      this.completion_date = new Date();
      break;
  }
};

// Set virtuals to be included in JSON output
TradeInSchema.set('toJSON', { virtuals: true });
TradeInSchema.set('toObject', { virtuals: true });

module.exports = mongoose.model('TradeIn', TradeInSchema);
