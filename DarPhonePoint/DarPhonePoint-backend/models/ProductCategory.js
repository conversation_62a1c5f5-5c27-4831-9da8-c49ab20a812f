const mongoose = require('mongoose');

const ProductCategorySchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Please add a category name'],
    trim: true,
    maxlength: [100, 'Name cannot be more than 100 characters']
  },
  slug: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    lowercase: true
  },
  description: {
    type: String,
    maxlength: [500, 'Description cannot be more than 500 characters']
  },
  
  // Category hierarchy
  parent_category: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'ProductCategory',
    default: null
  },
  level: {
    type: Number,
    default: 0,
    min: [0, 'Level cannot be negative']
  },
  
  // Display and ordering
  display_order: {
    type: Number,
    default: 0
  },
  icon: {
    type: String,
    trim: true
  },
  image_url: {
    type: String,
    trim: true
  },
  
  // SEO
  meta_title: String,
  meta_description: String,
  
  // Status
  is_active: {
    type: Boolean,
    default: true
  },
  is_featured: {
    type: Boolean,
    default: false
  },
  
  // Product count (virtual will be calculated)
  product_count: {
    type: Number,
    default: 0
  },
  
  created_at: {
    type: Date,
    default: Date.now
  },
  updated_at: {
    type: Date,
    default: Date.now
  }
});

// Virtual for full category path
ProductCategorySchema.virtual('full_path').get(function() {
  // This would need to be populated with parent categories
  // Implementation would require recursive population
  return this.name;
});

// Virtual for subcategories
ProductCategorySchema.virtual('subcategories', {
  ref: 'ProductCategory',
  localField: '_id',
  foreignField: 'parent_category'
});

// Set virtuals to be included in JSON output
ProductCategorySchema.set('toJSON', { virtuals: true });
ProductCategorySchema.set('toObject', { virtuals: true });

// Pre-save middleware to update timestamps and generate slug
ProductCategorySchema.pre('save', function(next) {
  this.updated_at = new Date();
  
  // Generate slug if not provided
  if (!this.slug && this.name) {
    this.slug = this.name
      .toLowerCase()
      .replace(/[^\w\s-]/g, '') // Remove special characters
      .replace(/\s+/g, '-')     // Replace spaces with hyphens
      .replace(/-+/g, '-')      // Replace multiple hyphens with single
      .trim();
  }
  
  next();
});

// Static method to get category tree
ProductCategorySchema.statics.getCategoryTree = async function() {
  const categories = await this.find({ is_active: true })
    .sort({ level: 1, display_order: 1, name: 1 })
    .lean();
  
  // Build tree structure
  const categoryMap = {};
  const tree = [];
  
  // First pass: create map
  categories.forEach(cat => {
    categoryMap[cat._id] = { ...cat, children: [] };
  });
  
  // Second pass: build tree
  categories.forEach(cat => {
    if (cat.parent_category) {
      const parent = categoryMap[cat.parent_category];
      if (parent) {
        parent.children.push(categoryMap[cat._id]);
      }
    } else {
      tree.push(categoryMap[cat._id]);
    }
  });
  
  return tree;
};

// Static method to get all subcategory IDs
ProductCategorySchema.statics.getSubcategoryIds = async function(categoryId) {
  const subcategories = await this.find({ parent_category: categoryId });
  let allIds = [categoryId];
  
  for (const subcat of subcategories) {
    const subIds = await this.getSubcategoryIds(subcat._id);
    allIds = allIds.concat(subIds);
  }
  
  return allIds;
};

// Method to update product count
ProductCategorySchema.methods.updateProductCount = async function() {
  const Product = mongoose.model('Product');
  const count = await Product.countDocuments({ 
    category: this.slug, 
    is_active: true 
  });
  this.product_count = count;
  await this.save();
  return count;
};

// Indexes for better query performance
ProductCategorySchema.index({ slug: 1 });
ProductCategorySchema.index({ parent_category: 1 });
ProductCategorySchema.index({ is_active: 1 });
ProductCategorySchema.index({ is_featured: 1 });
ProductCategorySchema.index({ level: 1 });
ProductCategorySchema.index({ display_order: 1 });

// Compound indexes for common query patterns
ProductCategorySchema.index({ is_active: 1, level: 1, display_order: 1 });
ProductCategorySchema.index({ parent_category: 1, is_active: 1, display_order: 1 });
ProductCategorySchema.index({ is_featured: 1, is_active: 1 });

module.exports = mongoose.model('ProductCategory', ProductCategorySchema);
