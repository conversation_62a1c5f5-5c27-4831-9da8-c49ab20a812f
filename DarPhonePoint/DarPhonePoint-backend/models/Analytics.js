const mongoose = require('mongoose');

const AnalyticsSchema = new mongoose.Schema({
  event_type: {
    type: String,
    required: true,
    enum: [
      'page_view',
      'lead_capture',
      'lead_magnet_delivered',
      'product_view',
      'add_to_cart',
      'checkout_start',
      'checkout_complete',
      'purchase',
      'purchase_attempt',
      'download',
      'file_download',
      'login',
      'registration',
      'email_open',
      'email_click',
      'email_sent'
    ]
  },
  user_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  session_id: {
    type: String,
    required: true
  },
  page_url: String,
  product_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Product'
  },
  lead_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Lead'
  },
  order_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Order'
  },
  device_type: {
    type: String,
    enum: ['desktop', 'tablet', 'mobile', 'unknown'],
    default: 'unknown'
  },
  browser: String,
  referrer: String,
  ip_address: String,
  metadata: {
    type: Object,
    default: {}
  },
  created_at: {
    type: Date,
    default: Date.now
  }
});

// Add index for faster queries
AnalyticsSchema.index({ event_type: 1, created_at: -1 });
AnalyticsSchema.index({ session_id: 1 });
AnalyticsSchema.index({ user_id: 1 });

module.exports = mongoose.model('Analytics', AnalyticsSchema);
