const mongoose = require('mongoose');

const EmailDeliverySchema = new mongoose.Schema({
  email_template_id: {
    type: mongoose.Schema.Types.ObjectId,
    required: true
  },
  position_in_sequence: {
    type: Number,
    required: true
  },
  variant_id: {
    type: mongoose.Schema.Types.ObjectId
  },
  sent_at: {
    type: Date,
    default: Date.now
  },
  opened_at: Date,
  clicked_at: Date,
  status: {
    type: String,
    enum: ['pending', 'sent', 'failed', 'opened', 'clicked'],
    default: 'pending'
  },
  subject: String,
  is_ab_test: {
    type: Boolean,
    default: false
  }
});

const EmailSequenceTrackingSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  lead: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Lead'
  },
  email: {
    type: String,
    required: true,
    match: [
      /^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/,
      'Please add a valid email'
    ]
  },
  email_sequence: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'EmailSequence',
    required: true
  },
  current_position: {
    type: Number,
    default: 0
  },
  next_email_date: {
    type: Date,
    required: true
  },
  is_completed: {
    type: Boolean,
    default: false
  },
  is_paused: {
    type: Boolean,
    default: false
  },
  unsubscribed: {
    type: Boolean,
    default: false
  },
  unsubscribed_at: Date,
  unsubscribe_reason: {
    type: String,
    enum: ['not_interested', 'too_many_emails', 'content_not_relevant', 'other'],
    default: 'other'
  },
  unsubscribe_feedback: String,
  emails_delivered: [EmailDeliverySchema],
  metadata: {
    type: Object,
    default: {}
  },
  created_at: {
    type: Date,
    default: Date.now
  },
  updated_at: {
    type: Date,
    default: Date.now
  }
});

// Add indexes for faster queries
EmailSequenceTrackingSchema.index({ email: 1, email_sequence: 1 });
EmailSequenceTrackingSchema.index({ next_email_date: 1, is_completed: 1, is_paused: 1, unsubscribed: 1 });

module.exports = mongoose.model('EmailSequenceTracking', EmailSequenceTrackingSchema);
