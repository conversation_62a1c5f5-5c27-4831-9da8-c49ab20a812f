const mongoose = require('mongoose');

const AuditLogSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: false // System actions may not have a user
  },
  action: {
    type: String,
    required: true,
    enum: [
      'create',
      'update',
      'delete',
      'login',
      'logout',
      'register',
      'email_verified',
      'password_change',
      'view',
      'export',
      'import',
      'settings_change',
      'password_reset',
      'email_sent',
      'payment_processed',
      'refund_processed',
      'email_sequence_trigger',
      'email_sequence_update',
      'cart_abandoned',
      'user_status_change',
      'download_access',
      'file_download',
      'lead_capture',
      'api_access'
    ]
  },
  resource_type: {
    type: String,
    required: true,
    enum: [
      'user',
      'product',
      'order',
      'lead',
      'email_sequence',
      'email_template',
      'settings',
      'analytics',
      'auth',
      'payment',
      'cart',
      'download',
      'file',
      'api'
    ]
  },
  resource_id: {
    type: String,
    required: false // Some actions may not have a specific resource ID
  },
  description: {
    type: String,
    required: true
  },
  previous_state: {
    type: Object,
    default: null,
    description: 'The state of the resource before the action (for updates)'
  },
  new_state: {
    type: Object,
    default: null,
    description: 'The state of the resource after the action (for updates)'
  },
  metadata: {
    type: Object,
    default: {},
    description: 'Additional contextual information about the action'
  },
  ip_address: {
    type: String,
    required: false
  },
  user_agent: {
    type: String,
    required: false
  },
  created_at: {
    type: Date,
    default: Date.now
  }
});

// Add indexes for better query performance
AuditLogSchema.index({ user: 1 });
AuditLogSchema.index({ action: 1 });
AuditLogSchema.index({ resource_type: 1 });
AuditLogSchema.index({ resource_id: 1 });
AuditLogSchema.index({ created_at: -1 });

// Add compound indexes for common query patterns
AuditLogSchema.index({ resource_type: 1, resource_id: 1, created_at: -1 });
AuditLogSchema.index({ user: 1, created_at: -1 });
AuditLogSchema.index({ action: 1, created_at: -1 });

module.exports = mongoose.model('AuditLog', AuditLogSchema);
