const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const crypto = require('crypto');
const config = require('../config/config');

const UserSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Please add a name'],
    trim: true,
    maxlength: [50, 'Name cannot be more than 50 characters']
  },
  email: {
    type: String,
    required: [true, 'Please add an email'],
    unique: true,
    match: [
      /^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/,
      'Please add a valid email'
    ],
    trim: true,
    lowercase: true
  },
  password: {
    type: String,
    required: function() {
      return !this.googleId; // Password is required only if not using Google auth
    },
    minlength: [8, 'Password must be at least 8 characters'],
    validate: {
      validator: function (password) {
        if (!password) return true; // Skip validation if no password (Google auth)
        return /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(password);
      },
      message: 'Password must contain at least one uppercase letter, one lowercase letter, and one number'
    },
    select: false
  },
  googleId: {
    type: String,
    unique: true,
    sparse: true
  },
  user_type: {
    type: String,
    enum: ['free', 'basic', 'premium'],
    default: 'free'
  },
  role: {
    type: String,
    enum: ['user', 'admin'],
    default: 'user'
  },
  purchased_products: {
    type: [mongoose.Schema.Types.ObjectId],
    ref: 'Product',
    default: []
  },

  // Customer profile information
  phone: {
    type: String,
    trim: true
  },
  date_of_birth: Date,

  // Shipping addresses
  shipping_addresses: [{
    first_name: {
      type: String,
      required: true,
      trim: true
    },
    last_name: {
      type: String,
      required: true,
      trim: true
    },
    company: {
      type: String,
      trim: true
    },
    address_line_1: {
      type: String,
      required: true,
      trim: true
    },
    address_line_2: {
      type: String,
      trim: true
    },
    city: {
      type: String,
      required: true,
      trim: true
    },
    state: {
      type: String,
      required: true,
      trim: true
    },
    postal_code: {
      type: String,
      required: true,
      trim: true
    },
    country: {
      type: String,
      required: true,
      trim: true,
      default: 'Tanzania'
    },
    phone: {
      type: String,
      trim: true
    },
    is_default: {
      type: Boolean,
      default: false
    },
    created_at: {
      type: Date,
      default: Date.now
    }
  }],

  // Customer preferences
  preferences: {
    newsletter_subscribed: {
      type: Boolean,
      default: true
    },
    sms_notifications: {
      type: Boolean,
      default: false
    },
    order_updates: {
      type: Boolean,
      default: true
    },
    marketing_emails: {
      type: Boolean,
      default: false
    }
  },

  // Wishlist
  wishlist: [{
    product: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Product',
      required: true
    },
    variant_sku: String,
    added_at: {
      type: Date,
      default: Date.now
    }
  }],

  status: {
    type: String,
    enum: ['active', 'inactive'],
    default: 'active'
  },
  email_sequences: [{
    sequence_id: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'EmailSequence',
      required: true
    },
    emails_sent: {
      type: Number,
      default: 0
    },
    next_email_index: {
      type: Number,
      default: 0
    },
    next_send_date: {
      type: Date,
      required: true
    },
    metadata: {
      type: Object,
      default: {}
    }
  }],
  last_login: {
    type: Date,
    default: Date.now
  },
  re_engagement_sent: {
    type: Boolean,
    default: false
  },
  resetPasswordToken: {
    type: String,
    select: false
  },
  resetPasswordExpire: {
    type: Date,
    select: false
  },
  emailVerificationToken: {
    type: String,
    select: false
  },
  emailVerificationExpire: {
    type: Date,
    select: false
  },
  isEmailVerified: {
    type: Boolean,
    default: false
  },
  created_at: {
    type: Date,
    default: Date.now
  },
  updated_at: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true,
  toJSON: {
    transform: function (doc, ret) {
      delete ret.password;
      return ret;
    }
  }
});

// Compound index for common queries
UserSchema.index({ role: 1, status: 1 });

// Encrypt password using bcrypt
UserSchema.pre('save', async function (next) {
  if (!this.isModified('password')) {
    next();
  }

  const salt = await bcrypt.genSalt(12);
  this.password = await bcrypt.hash(this.password, salt);
});

// Sign JWT and return
UserSchema.methods.getSignedJwtToken = function () {
  return jwt.sign(
    { user: { id: this._id } },
    config.JWT_SECRET,
    { expiresIn: config.JWT_EXPIRE }
  );
};

// Match user entered password to hashed password in database
UserSchema.methods.matchPassword = async function (enteredPassword) {
  return await bcrypt.compare(enteredPassword, this.password);
};

// Alias for matchPassword to maintain compatibility with both method names
UserSchema.methods.comparePassword = async function (enteredPassword) {
  return await this.matchPassword(enteredPassword);
};

// Static method to find active users
UserSchema.statics.findActiveUsers = function () {
  return this.find({ status: 'active' }).select('-password');
};

// Static method to find users by role
UserSchema.statics.findByRole = function (role) {
  return this.find({ role }).select('-password');
};

// Generate email verification token
UserSchema.methods.generateEmailVerificationToken = function () {
  // Generate token
  const verificationToken = crypto.randomBytes(20).toString('hex');

  // Hash token and set to emailVerificationToken field
  this.emailVerificationToken = crypto
    .createHash('sha256')
    .update(verificationToken)
    .digest('hex');

  // Set expire time (24 hours)
  this.emailVerificationExpire = Date.now() + 24 * 60 * 60 * 1000;

  return verificationToken;
};

// Verify email with token
UserSchema.statics.verifyEmailToken = async function (token) {
  // Get hashed token
  const emailVerificationToken = crypto
    .createHash('sha256')
    .update(token)
    .digest('hex');

  const user = await this.findOne({
    emailVerificationToken,
    emailVerificationExpire: { $gt: Date.now() }
  });

  if (!user) {
    return null;
  }

  // Mark email as verified and clear verification fields
  user.isEmailVerified = true;
  user.emailVerificationToken = undefined;
  user.emailVerificationExpire = undefined;
  await user.save();

  return user;
};

// Method to add shipping address
UserSchema.methods.addShippingAddress = function(addressData) {
  // If this is set as default, unset other defaults
  if (addressData.is_default) {
    this.shipping_addresses.forEach(addr => {
      addr.is_default = false;
    });
  }

  // If this is the first address, make it default
  if (this.shipping_addresses.length === 0) {
    addressData.is_default = true;
  }

  this.shipping_addresses.push(addressData);
  return this.shipping_addresses[this.shipping_addresses.length - 1];
};

// Method to update shipping address
UserSchema.methods.updateShippingAddress = function(addressId, updateData) {
  const address = this.shipping_addresses.id(addressId);
  if (!address) return null;

  // If setting as default, unset other defaults
  if (updateData.is_default) {
    this.shipping_addresses.forEach(addr => {
      if (addr._id.toString() !== addressId) {
        addr.is_default = false;
      }
    });
  }

  Object.assign(address, updateData);
  return address;
};

// Method to remove shipping address
UserSchema.methods.removeShippingAddress = function(addressId) {
  const address = this.shipping_addresses.id(addressId);
  if (!address) return false;

  const wasDefault = address.is_default;
  address.remove();

  // If removed address was default, make first remaining address default
  if (wasDefault && this.shipping_addresses.length > 0) {
    this.shipping_addresses[0].is_default = true;
  }

  return true;
};

// Method to get default shipping address
UserSchema.methods.getDefaultShippingAddress = function() {
  return this.shipping_addresses.find(addr => addr.is_default) ||
         (this.shipping_addresses.length > 0 ? this.shipping_addresses[0] : null);
};

// Method to add to wishlist
UserSchema.methods.addToWishlist = function(productId, variantSku = null) {
  // Check if already in wishlist
  const existingIndex = this.wishlist.findIndex(item =>
    item.product.toString() === productId && item.variant_sku === variantSku
  );

  if (existingIndex === -1) {
    this.wishlist.push({
      product: productId,
      variant_sku: variantSku
    });
    return true;
  }

  return false; // Already in wishlist
};

// Method to remove from wishlist
UserSchema.methods.removeFromWishlist = function(productId, variantSku = null) {
  const index = this.wishlist.findIndex(item =>
    item.product.toString() === productId && item.variant_sku === variantSku
  );

  if (index > -1) {
    this.wishlist.splice(index, 1);
    return true;
  }

  return false;
};

// Method to check if product is in wishlist
UserSchema.methods.isInWishlist = function(productId, variantSku = null) {
  return this.wishlist.some(item =>
    item.product.toString() === productId && item.variant_sku === variantSku
  );
};

const User = mongoose.model('User', UserSchema);

// Create indexes
User.createIndexes().catch(console.error);

module.exports = User;