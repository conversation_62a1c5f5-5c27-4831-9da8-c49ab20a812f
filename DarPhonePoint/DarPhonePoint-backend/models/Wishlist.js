const mongoose = require('mongoose');

const wishlistSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    unique: true
  },
  items: [{
    product: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Product',
      required: true
    },
    added_at: {
      type: Date,
      default: Date.now
    }
  }],
  created_at: {
    type: Date,
    default: Date.now
  },
  updated_at: {
    type: Date,
    default: Date.now
  }
});

// Index for efficient queries
// user index already created by unique: true constraint in schema
wishlistSchema.index({ 'items.product': 1 });

// Update the updated_at field before saving
wishlistSchema.pre('save', function(next) {
  this.updated_at = new Date();
  next();
});

// Virtual for item count
wishlistSchema.virtual('itemCount').get(function() {
  return this.items.length;
});

// Method to check if product is in wishlist
wishlistSchema.methods.hasProduct = function(productId) {
  return this.items.some(item => item.product.toString() === productId.toString());
};

// Method to add product
wishlistSchema.methods.addProduct = function(productId) {
  if (!this.hasProduct(productId)) {
    this.items.push({
      product: productId,
      added_at: new Date()
    });
  }
  return this;
};

// Method to remove product
wishlistSchema.methods.removeProduct = function(productId) {
  this.items = this.items.filter(item => item.product.toString() !== productId.toString());
  return this;
};

// Static method to find or create wishlist
wishlistSchema.statics.findOrCreate = async function(userId) {
  let wishlist = await this.findOne({ user: userId });
  
  if (!wishlist) {
    wishlist = new this({
      user: userId,
      items: []
    });
    await wishlist.save();
  }
  
  return wishlist;
};

module.exports = mongoose.model('Wishlist', wishlistSchema);
