const mongoose = require('mongoose');

// Schema for product specifications (flexible for different product types)
const SpecificationSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true
  },
  value: {
    type: String,
    required: true
  },
  category: {
    type: String,
    required: true,
    enum: [
      // Common categories
      'display', 'performance', 'storage', 'connectivity', 'battery', 'camera',
      'audio', 'dimensions', 'build', 'software', 'security', 'sensors',

      // Computer-specific
      'processor', 'graphics', 'memory', 'motherboard', 'cooling', 'ports',

      // Gaming-specific
      'gaming_performance', 'compatibility', 'controller', 'online_features',

      // Camera-specific
      'lens', 'video', 'flash', 'stabilization', 'recording',

      // Networking-specific
      'wireless', 'ethernet', 'range', 'speed', 'protocols',

      // Power-specific
      'power_output', 'efficiency', 'protection', 'capacity',

      // Other
      'warranty', 'accessories', 'special_features', 'other'
    ]
  },
  unit: {
    type: String,
    default: '' // e.g., 'GB', 'MHz', 'inches', 'hours', 'kg'
  },
  is_key_spec: {
    type: Boolean,
    default: false // Mark important specs for display priority
  }
}, { _id: false });

// Schema for product images
const ImageSchema = new mongoose.Schema({
  url: {
    type: String,
    required: true
  },
  alt_text: {
    type: String,
    default: ''
  },
  is_primary: {
    type: Boolean,
    default: false
  },
  order: {
    type: Number,
    default: 0
  }
}, { _id: false });

// Schema for product variants (different colors, storage options, etc.)
const VariantSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true // e.g., "128GB Space Gray", "256GB Blue"
  },
  sku: {
    type: String,
    required: true,
    unique: true
  },
  price: {
    type: Number,
    required: true,
    min: [0, 'Price must be at least 0']
  },
  compare_at_price: {
    type: Number,
    min: [0, 'Compare at price must be at least 0']
  },
  color: String,
  storage: String,
  memory: String,
  stock_quantity: {
    type: Number,
    required: true,
    min: [0, 'Stock quantity cannot be negative'],
    default: 0
  },
  low_stock_threshold: {
    type: Number,
    default: 5
  },
  weight: {
    type: Number, // in grams
    min: [0, 'Weight must be positive']
  },
  dimensions: {
    length: Number, // in mm
    width: Number,  // in mm
    height: Number  // in mm
  },
  images: [ImageSchema],
  is_active: {
    type: Boolean,
    default: true
  }
});

const ProductSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Please add a product name'],
    trim: true,
    maxlength: [200, 'Name cannot be more than 200 characters']
  },
  slug: {
    type: String,
    required: true,
    unique: true,
    trim: true
  },
  description: {
    type: String,
    required: [true, 'Please add a description'],
    maxlength: [2000, 'Description cannot be more than 2000 characters']
  },
  short_description: {
    type: String,
    maxlength: [500, 'Short description cannot be more than 500 characters']
  },

  // Product categorization
  category: {
    type: String,
    required: [true, 'Please add a product category'],
    enum: [
      // Mobile & Tablets
      'smartphone', 'tablet', 'smartwatch', 'feature_phone',

      // Computers
      'laptop', 'desktop', 'mini_pc', 'workstation', 'server',

      // Gaming
      'gaming_console', 'gaming_laptop', 'gaming_accessories', 'gaming_chair',

      // Audio/Visual
      'earbuds', 'headphones', 'speaker', 'camera', 'webcam', 'microphone',
      'projector', 'tv', 'monitor',

      // Computer Accessories
      'keyboard', 'mouse', 'mousepad', 'printer', 'scanner', 'webcam',

      // Storage & Memory
      'external_drive', 'ssd', 'hdd', 'usb_drive', 'memory_card', 'ram',

      // Networking
      'router', 'modem', 'wifi_extender', 'network_switch', 'ethernet_cable',

      // Power & Cables
      'charger', 'cable', 'power_bank', 'ups', 'surge_protector', 'adapter',

      // Protection & Cases
      'case', 'screen_protector', 'laptop_bag', 'camera_bag', 'phone_case',

      // Smart Home & IoT
      'smart_bulb', 'smart_plug', 'security_camera', 'smart_speaker',

      // Other
      'repair_tool', 'cleaning_kit', 'mount', 'stand', 'drone', 'other'
    ]
  },
  subcategory: {
    type: String,
    trim: true
  },
  brand: {
    type: String,
    required: [true, 'Please add a brand'],
    trim: true
  },
  model: {
    type: String,
    required: [true, 'Please add a model'],
    trim: true
  },

  // Pricing (base price, variants can override)
  price: {
    type: Number,
    required: [true, 'Please add a price'],
    min: [0, 'Price must be at least 0']
  },
  compare_at_price: {
    type: Number,
    min: [0, 'Compare at price must be at least 0']
  },
  cost_price: {
    type: Number,
    min: [0, 'Cost price must be at least 0']
  },

  // Product variants (different colors, storage, etc.)
  variants: [VariantSchema],

  // Product specifications
  specifications: [SpecificationSchema],

  // Product images
  images: [ImageSchema],

  // Inventory management
  track_inventory: {
    type: Boolean,
    default: true
  },
  stock_quantity: {
    type: Number,
    required: function() { return this.track_inventory; },
    min: [0, 'Stock quantity cannot be negative'],
    default: 0
  },
  low_stock_threshold: {
    type: Number,
    default: 5
  },

  // Physical product attributes
  sku: {
    type: String,
    required: [true, 'Please add a SKU'],
    unique: true,
    trim: true,
    uppercase: true
  },
  barcode: {
    type: String,
    trim: true
  },
  weight: {
    type: Number, // in grams
    min: [0, 'Weight must be positive']
  },
  dimensions: {
    length: Number, // in mm
    width: Number,  // in mm
    height: Number  // in mm
  },

  // Shipping
  requires_shipping: {
    type: Boolean,
    default: true
  },
  shipping_class: {
    type: String,
    enum: ['standard', 'fragile', 'oversized', 'express_only'],
    default: 'standard'
  },

  // SEO and marketing
  meta_title: String,
  meta_description: String,
  tags: [String],

  // Status and visibility
  is_active: {
    type: Boolean,
    default: true
  },
  is_featured: {
    type: Boolean,
    default: false
  },

  // Legacy fields (for backward compatibility during transition)
  product_type: {
    type: String,
    enum: ['smartphone', 'accessory', 'lead_magnet', 'basic', 'premium'], // keeping old values for transition
    default: 'smartphone'
  },
  file_path: {
    type: String,
    required: false // no longer required for physical products
  },
  features: {
    type: [String],
    default: []
  },
  whop_price_id: {
    type: String,
    required: false,
    trim: true
  },
  whop_product_id: {
    type: String,
    required: false,
    trim: true
  },
  whop_checkout_link: {
    type: String,
    required: false,
    trim: true
  },

  created_at: {
    type: Date,
    default: Date.now
  },
  updated_at: {
    type: Date,
    default: Date.now
  }
});

// Virtual for checking if product is in stock
ProductSchema.virtual('in_stock').get(function() {
  if (!this.track_inventory) return true;
  if (this.variants && this.variants.length > 0) {
    return this.variants.some(variant => variant.stock_quantity > 0 && variant.is_active);
  }
  return this.stock_quantity > 0;
});

// Virtual for checking if product is low stock
ProductSchema.virtual('is_low_stock').get(function() {
  if (!this.track_inventory) return false;
  if (this.variants && this.variants.length > 0) {
    return this.variants.some(variant =>
      variant.stock_quantity <= variant.low_stock_threshold &&
      variant.stock_quantity > 0 &&
      variant.is_active
    );
  }
  return this.stock_quantity <= this.low_stock_threshold && this.stock_quantity > 0;
});

// Virtual for total stock across all variants
ProductSchema.virtual('total_stock').get(function() {
  if (!this.track_inventory) return null;
  if (this.variants && this.variants.length > 0) {
    return this.variants.reduce((total, variant) => {
      return total + (variant.is_active ? variant.stock_quantity : 0);
    }, 0);
  }
  return this.stock_quantity;
});

// Virtual for primary image
ProductSchema.virtual('primary_image').get(function() {
  if (this.images && this.images.length > 0) {
    const primary = this.images.find(img => img.is_primary);
    return primary || this.images[0];
  }
  return null;
});

// Virtual for price range (for products with variants)
ProductSchema.virtual('price_range').get(function() {
  if (this.variants && this.variants.length > 0) {
    const activePrices = this.variants
      .filter(variant => variant.is_active)
      .map(variant => variant.price);

    if (activePrices.length === 0) return { min: this.price, max: this.price };

    const min = Math.min(...activePrices);
    const max = Math.max(...activePrices);
    return { min, max };
  }
  return { min: this.price, max: this.price };
});

// Set virtuals to be included in JSON output
ProductSchema.set('toJSON', { virtuals: true });
ProductSchema.set('toObject', { virtuals: true });

// Pre-save middleware to update timestamps
ProductSchema.pre('save', function(next) {
  this.updated_at = new Date();
  next();
});

// Add indexes for better query performance
// Note: slug index with unique constraint is already defined in schema
ProductSchema.index({ category: 1 });
ProductSchema.index({ brand: 1 });
ProductSchema.index({ is_active: 1 });
ProductSchema.index({ is_featured: 1 });
ProductSchema.index({ price: 1 });
ProductSchema.index({ created_at: -1 });
// variants.sku index already created by unique: true constraint in VariantSchema

// Compound indexes for common query patterns
ProductSchema.index({ category: 1, is_active: 1 });
ProductSchema.index({ brand: 1, category: 1 });
ProductSchema.index({ is_active: 1, is_featured: 1 });
ProductSchema.index({ is_active: 1, created_at: -1 });
ProductSchema.index({ category: 1, brand: 1, is_active: 1 });
ProductSchema.index({ price: 1, is_active: 1 });

// Enhanced text index for search functionality
ProductSchema.index({
  name: 'text',
  description: 'text',
  short_description: 'text',
  brand: 'text',
  model: 'text',
  tags: 'text',
  'specifications.value': 'text'
}, {
  weights: {
    name: 10,
    brand: 8,
    model: 6,
    short_description: 4,
    description: 2,
    tags: 3,
    'specifications.value': 1
  },
  name: 'product_text_index'
});

// Remove duplicate indexes - they are already defined above

// Create and export the model
const Product = mongoose.model('Product', ProductSchema);
module.exports = Product;