const mongoose = require('mongoose');

/**
 * Supplier Schema for Phone Point Dar
 * Manages supplier information and relationships
 */
const supplierSchema = new mongoose.Schema({
  // Basic Information
  name: {
    type: String,
    required: true,
    trim: true,
    index: true
  },

  code: {
    type: String,
    required: true,
    unique: true,
    uppercase: true,
    trim: true,
    index: true
  },

  // Contact Information
  contactInfo: {
    email: {
      type: String,
      lowercase: true,
      trim: true,
      validate: {
        validator: function(v) {
          return !v || /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/.test(v);
        },
        message: 'Invalid email format'
      }
    },
    phone: {
      type: String,
      trim: true
    },
    website: {
      type: String,
      trim: true
    },
    contactPerson: {
      name: String,
      title: String,
      email: String,
      phone: String
    }
  },

  // Address Information
  address: {
    street: String,
    city: String,
    state: String,
    country: {
      type: String,
      default: 'Tanzania'
    },
    postalCode: String
  },

  // Business Information
  businessInfo: {
    registrationNumber: String,
    taxId: String,
    businessType: {
      type: String,
      enum: ['manufacturer', 'distributor', 'wholesaler', 'retailer', 'importer', 'other'],
      default: 'distributor'
    },
    establishedYear: Number
  },

  // Product Categories
  categories: [{
    type: String,
    enum: [
      'smartphone', 'tablet', 'laptop', 'desktop', 'smartwatch', 'earbuds',
      'headphones', 'speaker', 'charger', 'cable', 'case', 'screen_protector',
      'power_bank', 'memory_card', 'usb_drive', 'external_drive', 'ssd', 'hdd',
      'monitor', 'keyboard', 'mouse', 'webcam', 'microphone', 'router',
      'modem', 'wifi_extender', 'switch', 'camera', 'lens', 'tripod',
      'gaming_console', 'gaming_controller', 'gaming_headset', 'vr_headset',
      'drone', 'action_camera', 'projector', 'smart_tv', 'streaming_device',
      'smart_home', 'fitness_tracker', 'e_reader', 'graphics_card',
      'motherboard', 'processor', 'ram', 'cooling_system'
    ]
  }],

  // Brands supplied
  brands: [String],

  // Financial Information
  paymentTerms: {
    creditDays: {
      type: Number,
      default: 30
    },
    paymentMethods: [{
      type: String,
      enum: ['bank_transfer', 'cash', 'check', 'credit_card', 'mobile_money', 'letter_of_credit']
    }],
    currency: {
      type: String,
      default: 'TZS'
    },
    creditLimit: {
      type: Number,
      min: 0
    }
  },

  // Performance Metrics
  performance: {
    rating: {
      type: Number,
      min: 1,
      max: 5,
      default: 3
    },
    totalOrders: {
      type: Number,
      default: 0
    },
    totalValue: {
      type: Number,
      default: 0
    },
    averageDeliveryDays: {
      type: Number,
      default: 0
    },
    onTimeDeliveryRate: {
      type: Number,
      min: 0,
      max: 100,
      default: 0
    },
    qualityRating: {
      type: Number,
      min: 1,
      max: 5,
      default: 3
    },
    lastOrderDate: Date
  },

  // Status and Preferences
  status: {
    type: String,
    enum: ['active', 'inactive', 'suspended', 'pending_approval'],
    default: 'active',
    index: true
  },

  isPreferred: {
    type: Boolean,
    default: false,
    index: true
  },

  // Shipping Information
  shipping: {
    methods: [{
      name: String,
      cost: Number,
      estimatedDays: Number
    }],
    freeShippingThreshold: Number,
    handlingTime: {
      type: Number,
      default: 1
    }
  },

  // Warranty and Support
  warranty: {
    defaultPeriod: {
      type: Number,
      default: 365 // days
    },
    supportLevel: {
      type: String,
      enum: ['basic', 'standard', 'premium'],
      default: 'standard'
    },
    returnPolicy: {
      period: {
        type: Number,
        default: 30 // days
      },
      conditions: String
    }
  },

  // Documents and Certifications
  documents: [{
    type: {
      type: String,
      enum: ['business_license', 'tax_certificate', 'quality_certificate', 'authorization_letter', 'other']
    },
    name: String,
    url: String,
    expiryDate: Date,
    verified: {
      type: Boolean,
      default: false
    }
  }],

  // Notes and Comments
  notes: String,

  // Metadata
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },

  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for performance
supplierSchema.index({ name: 'text', code: 'text' });
supplierSchema.index({ categories: 1 });
supplierSchema.index({ brands: 1 });
supplierSchema.index({ status: 1, isPreferred: -1 });
supplierSchema.index({ 'performance.rating': -1 });

// Virtual for outstanding balance (would need to calculate from purchase orders)
supplierSchema.virtual('outstandingBalance').get(function() {
  // This would be calculated from related purchase orders
  return 0; // Placeholder
});

// Virtual for active products count
supplierSchema.virtual('activeProductsCount').get(function() {
  // This would be calculated from related products
  return 0; // Placeholder
});

// Pre-save middleware
supplierSchema.pre('save', function(next) {
  // Generate supplier code if not provided
  if (!this.code && this.name) {
    const nameCode = this.name.replace(/[^a-zA-Z0-9]/g, '').substring(0, 6).toUpperCase();
    const timestamp = Date.now().toString().slice(-4);
    this.code = `SUP${nameCode}${timestamp}`;
  }

  next();
});

// Static methods
supplierSchema.statics.findByCategory = function(category) {
  return this.find({ 
    categories: category,
    status: 'active'
  }).sort({ isPreferred: -1, 'performance.rating': -1 });
};

supplierSchema.statics.findByBrand = function(brand) {
  return this.find({ 
    brands: { $regex: new RegExp(brand, 'i') },
    status: 'active'
  }).sort({ isPreferred: -1, 'performance.rating': -1 });
};

supplierSchema.statics.getTopPerformers = function(limit = 10) {
  return this.find({ status: 'active' })
    .sort({ 
      'performance.rating': -1, 
      'performance.onTimeDeliveryRate': -1,
      'performance.totalValue': -1 
    })
    .limit(limit);
};

// Instance methods
supplierSchema.methods.updatePerformance = function(orderValue, deliveryDays, onTime, qualityRating) {
  this.performance.totalOrders += 1;
  this.performance.totalValue += orderValue;
  this.performance.lastOrderDate = new Date();
  
  // Update average delivery days
  const currentAvg = this.performance.averageDeliveryDays || 0;
  const totalOrders = this.performance.totalOrders;
  this.performance.averageDeliveryDays = ((currentAvg * (totalOrders - 1)) + deliveryDays) / totalOrders;
  
  // Update on-time delivery rate
  const currentRate = this.performance.onTimeDeliveryRate || 0;
  const onTimeCount = Math.round((currentRate / 100) * (totalOrders - 1)) + (onTime ? 1 : 0);
  this.performance.onTimeDeliveryRate = (onTimeCount / totalOrders) * 100;
  
  // Update quality rating
  if (qualityRating) {
    const currentQuality = this.performance.qualityRating || 3;
    this.performance.qualityRating = ((currentQuality * (totalOrders - 1)) + qualityRating) / totalOrders;
  }
  
  // Update overall rating (weighted average)
  const deliveryScore = Math.max(1, 5 - (this.performance.averageDeliveryDays / 2));
  const onTimeScore = (this.performance.onTimeDeliveryRate / 100) * 5;
  const qualityScore = this.performance.qualityRating;
  
  this.performance.rating = (deliveryScore * 0.3 + onTimeScore * 0.4 + qualityScore * 0.3);
  
  return this.save();
};

supplierSchema.methods.addDocument = function(type, name, url, expiryDate) {
  this.documents.push({
    type,
    name,
    url,
    expiryDate,
    verified: false
  });
  return this.save();
};

module.exports = mongoose.model('Supplier', supplierSchema);
