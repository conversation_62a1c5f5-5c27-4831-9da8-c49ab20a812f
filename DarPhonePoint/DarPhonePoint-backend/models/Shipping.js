const mongoose = require('mongoose');

// Schema for shipping address
const AddressSchema = new mongoose.Schema({
  first_name: {
    type: String,
    required: true,
    trim: true
  },
  last_name: {
    type: String,
    required: true,
    trim: true
  },
  company: {
    type: String,
    trim: true
  },
  address_line_1: {
    type: String,
    required: true,
    trim: true
  },
  address_line_2: {
    type: String,
    trim: true
  },
  city: {
    type: String,
    required: true,
    trim: true
  },
  state: {
    type: String,
    required: true,
    trim: true
  },
  postal_code: {
    type: String,
    required: true,
    trim: true
  },
  country: {
    type: String,
    required: true,
    trim: true,
    default: 'Tanzania'
  },
  phone: {
    type: String,
    trim: true
  },
  is_default: {
    type: Boolean,
    default: false
  }
}, { _id: false });

// Schema for tracking events
const TrackingEventSchema = new mongoose.Schema({
  status: {
    type: String,
    required: true,
    enum: ['label_created', 'picked_up', 'in_transit', 'out_for_delivery', 'delivered', 'failed_delivery', 'returned']
  },
  description: {
    type: String,
    required: true
  },
  location: String,
  timestamp: {
    type: Date,
    required: true,
    default: Date.now
  },
  carrier_status: String, // Raw status from shipping carrier
  notes: String
});

// Main shipping schema
const ShippingSchema = new mongoose.Schema({
  order: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Order',
    required: true,
    unique: true
  },
  
  // Shipping method and carrier
  shipping_method: {
    type: String,
    required: true,
    enum: ['standard', 'express', 'overnight', 'pickup', 'same_day']
  },
  carrier: {
    type: String,
    required: true,
    enum: ['dhl', 'fedex', 'ups', 'tnt', 'local_courier', 'pickup']
  },
  service_type: String, // Specific service from carrier
  
  // Addresses
  shipping_address: {
    type: AddressSchema,
    required: true
  },
  billing_address: AddressSchema,
  
  // Package details
  package_weight: {
    type: Number, // in grams
    required: true,
    min: [0, 'Package weight must be positive']
  },
  package_dimensions: {
    length: Number, // in mm
    width: Number,
    height: Number
  },
  declared_value: {
    type: Number,
    min: [0, 'Declared value must be positive']
  },
  
  // Costs
  shipping_cost: {
    type: Number,
    required: true,
    min: [0, 'Shipping cost cannot be negative']
  },
  insurance_cost: {
    type: Number,
    default: 0,
    min: [0, 'Insurance cost cannot be negative']
  },
  tax_amount: {
    type: Number,
    default: 0,
    min: [0, 'Tax amount cannot be negative']
  },
  
  // Tracking information
  tracking_number: {
    type: String,
    trim: true
  },
  tracking_url: String,
  label_url: String,
  
  // Status and timeline
  status: {
    type: String,
    required: true,
    enum: ['pending', 'label_created', 'picked_up', 'in_transit', 'out_for_delivery', 'delivered', 'failed_delivery', 'returned', 'cancelled'],
    default: 'pending'
  },
  tracking_events: [TrackingEventSchema],
  
  // Important dates
  estimated_delivery_date: Date,
  actual_delivery_date: Date,
  shipped_at: Date,
  
  // Special instructions
  delivery_instructions: String,
  signature_required: {
    type: Boolean,
    default: false
  },
  
  // Internal tracking
  warehouse_location: {
    type: String,
    default: 'main_warehouse'
  },
  packed_by: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  packed_at: Date,
  
  created_at: {
    type: Date,
    default: Date.now
  },
  updated_at: {
    type: Date,
    default: Date.now
  }
});

// Virtual for full shipping address
ShippingSchema.virtual('full_shipping_address').get(function() {
  const addr = this.shipping_address;
  if (!addr) return '';
  
  let address = `${addr.first_name} ${addr.last_name}\n`;
  if (addr.company) address += `${addr.company}\n`;
  address += `${addr.address_line_1}\n`;
  if (addr.address_line_2) address += `${addr.address_line_2}\n`;
  address += `${addr.city}, ${addr.state} ${addr.postal_code}\n`;
  address += addr.country;
  
  return address;
});

// Virtual for checking if delivery is overdue
ShippingSchema.virtual('is_overdue').get(function() {
  if (!this.estimated_delivery_date || this.status === 'delivered') return false;
  return new Date() > this.estimated_delivery_date;
});

// Virtual for latest tracking event
ShippingSchema.virtual('latest_tracking_event').get(function() {
  if (!this.tracking_events || this.tracking_events.length === 0) return null;
  return this.tracking_events[this.tracking_events.length - 1];
});

// Set virtuals to be included in JSON output
ShippingSchema.set('toJSON', { virtuals: true });
ShippingSchema.set('toObject', { virtuals: true });

// Pre-save middleware to update timestamps
ShippingSchema.pre('save', function(next) {
  this.updated_at = new Date();
  next();
});

// Method to add tracking event
ShippingSchema.methods.addTrackingEvent = function(status, description, options = {}) {
  const event = {
    status,
    description,
    location: options.location,
    timestamp: options.timestamp || new Date(),
    carrier_status: options.carrier_status,
    notes: options.notes
  };
  
  this.tracking_events.push(event);
  this.status = status;
  
  // Update special dates based on status
  if (status === 'delivered' && !this.actual_delivery_date) {
    this.actual_delivery_date = event.timestamp;
  }
  if (status === 'picked_up' && !this.shipped_at) {
    this.shipped_at = event.timestamp;
  }
};

// Indexes for better query performance
// order index already created by unique: true constraint
ShippingSchema.index({ tracking_number: 1 });
ShippingSchema.index({ status: 1 });
ShippingSchema.index({ carrier: 1 });
ShippingSchema.index({ estimated_delivery_date: 1 });
ShippingSchema.index({ created_at: -1 });

// Compound indexes for common queries
ShippingSchema.index({ status: 1, estimated_delivery_date: 1 });
ShippingSchema.index({ carrier: 1, status: 1 });

module.exports = mongoose.model('Shipping', ShippingSchema);
