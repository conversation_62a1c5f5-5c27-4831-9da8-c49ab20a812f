const mongoose = require('mongoose');

/**
 * Settings Schema
 * Stores system-wide configuration settings
 */
const settingsSchema = new mongoose.Schema({
  category: {
    type: String,
    required: true,
    enum: ['general', 'email', 'payment', 'storage', 'system']
  },
  key: {
    type: String,
    required: true
  },
  value: {
    type: mongoose.Schema.Types.Mixed,
    required: true
  },
  type: {
    type: String,
    enum: ['string', 'number', 'boolean', 'object', 'array'],
    default: 'string'
  },
  description: {
    type: String,
    default: ''
  },
  isPublic: {
    type: Boolean,
    default: false // Whether this setting can be accessed by non-admin users
  },
  isEncrypted: {
    type: Boolean,
    default: false // Whether this setting contains sensitive data
  },
  validation: {
    required: {
      type: Boolean,
      default: false
    },
    min: Number,
    max: Number,
    pattern: String,
    enum: [String]
  },
  lastModified: {
    type: Date,
    default: Date.now
  },
  modifiedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Compound index for category and key (unique combination)
settingsSchema.index({ category: 1, key: 1 }, { unique: true });

// Static method to get settings by category
settingsSchema.statics.getByCategory = async function(category) {
  const settings = await this.find({ category });
  const result = {};
  
  settings.forEach(setting => {
    result[setting.key] = setting.value;
  });
  
  return result;
};

// Static method to get all settings grouped by category
settingsSchema.statics.getAllGrouped = async function() {
  const settings = await this.find({});
  const result = {};
  
  settings.forEach(setting => {
    if (!result[setting.category]) {
      result[setting.category] = {};
    }
    result[setting.category][setting.key] = setting.value;
  });
  
  return result;
};

// Static method to update or create setting
settingsSchema.statics.setSetting = async function(category, key, value, options = {}) {
  const { type = 'string', description = '', isPublic = false, isEncrypted = false, modifiedBy = null } = options;
  
  const setting = await this.findOneAndUpdate(
    { category, key },
    {
      value,
      type,
      description,
      isPublic,
      isEncrypted,
      lastModified: new Date(),
      modifiedBy
    },
    {
      upsert: true,
      new: true,
      runValidators: true
    }
  );
  
  return setting;
};

// Static method to get public settings (for frontend)
settingsSchema.statics.getPublicSettings = async function() {
  const settings = await this.find({ isPublic: true });
  const result = {};
  
  settings.forEach(setting => {
    if (!result[setting.category]) {
      result[setting.category] = {};
    }
    result[setting.category][setting.key] = setting.value;
  });
  
  return result;
};

// Instance method to encrypt sensitive values
settingsSchema.methods.encryptValue = function() {
  if (this.isEncrypted && typeof this.value === 'string') {
    // In a real implementation, you would use proper encryption
    // For now, we'll just mark it as encrypted
    this.value = `[ENCRYPTED]${this.value}`;
  }
  return this;
};

// Instance method to decrypt sensitive values
settingsSchema.methods.decryptValue = function() {
  if (this.isEncrypted && typeof this.value === 'string' && this.value.startsWith('[ENCRYPTED]')) {
    // In a real implementation, you would use proper decryption
    this.value = this.value.replace('[ENCRYPTED]', '');
  }
  return this;
};

// Pre-save middleware to handle encryption
settingsSchema.pre('save', function(next) {
  if (this.isEncrypted && this.isModified('value')) {
    this.encryptValue();
  }
  next();
});

// Virtual for formatted last modified date
settingsSchema.virtual('lastModifiedFormatted').get(function() {
  return this.lastModified.toLocaleDateString();
});

module.exports = mongoose.model('Settings', settingsSchema);
