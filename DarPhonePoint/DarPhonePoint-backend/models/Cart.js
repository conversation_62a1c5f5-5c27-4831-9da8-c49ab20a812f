/**
 * Cart Model
 * Stores user shopping cart information
 */

const mongoose = require('mongoose');

const CartItemSchema = new mongoose.Schema({
  product: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Product',
    required: true
  },
  variant_sku: {
    type: String,
    trim: true
  },
  quantity: {
    type: Number,
    required: true,
    min: 1,
    default: 1
  },
  price: {
    type: Number,
    required: true,
    min: 0
  },
  name: {
    type: String,
    required: true,
    trim: true
  },
  sku: {
    type: String,
    required: true,
    trim: true
  },
  // Store variant details for quick access
  variant_details: {
    color: String,
    storage: String,
    memory: String
  },
  // Phone-specific fields for Phone Point Dar
  imei: {
    type: String,
    trim: true,
    sparse: true // Allow multiple null values but unique non-null values
  },
  warranty_option: {
    type: String,
    enum: ['standard', 'extended', 'premium', 'none'],
    default: 'standard'
  },
  warranty_price: {
    type: Number,
    default: 0,
    min: [0, 'Warranty price cannot be negative']
  },
  warranty_duration: {
    type: Number, // in months
    default: 12
  },
  trade_in_device: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'TradeIn'
  },
  trade_in_value: {
    type: Number,
    default: 0,
    min: [0, 'Trade-in value cannot be negative']
  },
  accessories: [{
    accessory: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Product'
    },
    name: String,
    price: Number,
    quantity: {
      type: Number,
      default: 1,
      min: [1, 'Accessory quantity must be at least 1']
    }
  }],
  device_condition: {
    type: String,
    enum: ['new', 'refurbished', 'used', 'open_box'],
    default: 'new'
  },
  added_at: {
    type: Date,
    default: Date.now
  }
});

const CartSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  items: [CartItemSchema],
  abandoned_cart_processed: {
    type: Boolean,
    default: false
  },
  created_at: {
    type: Date,
    default: Date.now
  },
  updated_at: {
    type: Date,
    default: Date.now
  }
});

// Virtual for subtotal (before shipping and tax)
CartSchema.virtual('subtotal').get(function() {
  return this.items.reduce((total, item) => {
    return total + (item.price * item.quantity);
  }, 0);
});

// Virtual for total items count
CartSchema.virtual('item_count').get(function() {
  return this.items.reduce((count, item) => {
    return count + item.quantity;
  }, 0);
});

// Virtual for unique products count
CartSchema.virtual('unique_items_count').get(function() {
  return this.items.length;
});

// Virtual for checking if cart is empty
CartSchema.virtual('is_empty').get(function() {
  return this.items.length === 0;
});

// Pre-save middleware to update timestamps
CartSchema.pre('save', function(next) {
  this.updated_at = new Date();
  next();
});

// Method to find item by product and variant
CartSchema.methods.findItem = function(productId, variantSku = null) {
  return this.items.find(item =>
    item.product.toString() === productId.toString() &&
    (item.variant_sku || null) === (variantSku || null)
  );
};

// Method to remove item by product and variant
CartSchema.methods.removeItem = function(productId, variantSku = null) {
  const itemIndex = this.items.findIndex(item =>
    item.product.toString() === productId.toString() &&
    (item.variant_sku || null) === (variantSku || null)
  );

  if (itemIndex > -1) {
    this.items.splice(itemIndex, 1);
    return true;
  }
  return false;
};

// Method to update item quantity
CartSchema.methods.updateItemQuantity = function(productId, variantSku = null, quantity) {
  const item = this.findItem(productId, variantSku);
  if (item) {
    if (quantity <= 0) {
      return this.removeItem(productId, variantSku);
    } else {
      item.quantity = quantity;
      return true;
    }
  }
  return false;
};

// Method to clear all items
CartSchema.methods.clearItems = function() {
  this.items = [];
  this.updated_at = new Date();
};

// Static method to find abandoned carts
CartSchema.statics.findAbandonedCarts = function(hoursAgo = 24) {
  const cutoffDate = new Date(Date.now() - (hoursAgo * 60 * 60 * 1000));

  return this.find({
    updated_at: { $lt: cutoffDate },
    abandoned_cart_processed: false,
    items: { $exists: true, $not: { $size: 0 } }
  }).populate('user', 'name email');
};

// Set virtuals to be included in JSON output
CartSchema.set('toJSON', { virtuals: true });
CartSchema.set('toObject', { virtuals: true });

// Indexes for better performance
CartSchema.index({ user: 1 });
CartSchema.index({ updated_at: 1 });
CartSchema.index({ abandoned_cart_processed: 1 });
CartSchema.index({ user: 1, updated_at: -1 });

module.exports = mongoose.model('Cart', CartSchema);
