const mongoose = require('mongoose');

const EmailVariantSchema = new mongoose.Schema({
  subject: {
    type: String,
    required: [true, 'Email subject is required']
  },
  body: {
    type: String,
    required: [true, 'Email body is required']
  },
  isActive: {
    type: Boolean,
    default: true
  },
  weight: {
    type: Number,
    default: 50,
    min: 1,
    max: 100
  },
  stats: {
    sent: {
      type: Number,
      default: 0
    },
    opened: {
      type: Number,
      default: 0
    },
    clicked: {
      type: Number,
      default: 0
    }
  }
}, { _id: true });

const EmailSchema = new mongoose.Schema({
  subject: {
    type: String,
    required: [true, 'Email subject is required']
  },
  body: {
    type: String,
    required: [true, 'Email body is required']
  },
  delayDays: {
    type: Number,
    default: 0,
    min: 0
  },
  order: {
    type: Number,
    required: true
  },
  isActive: {
    type: Boolean,
    default: true
  },
  isABTest: {
    type: Boolean,
    default: false
  },
  variants: [EmailVariantSchema]
}, { _id: true });

const EmailSequenceSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Sequence name is required'],
    trim: true
  },
  description: {
    type: String,
    trim: true
  },
  trigger: {
    type: String,
    enum: ['lead_capture', 'purchase', 'user_inactive'],
    default: 'lead_capture'
  },
  isActive: {
    type: Boolean,
    default: true
  },
  emails: [EmailSchema],
  created_by: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at'
  }
});

// Add a method to add an email to the sequence
EmailSequenceSchema.methods.addEmail = function(emailData) {
  const order = this.emails.length + 1;
  this.emails.push({
    ...emailData,
    order
  });
  return this;
};

// Add a method to remove an email from the sequence
EmailSequenceSchema.methods.removeEmail = function(emailId) {
  const index = this.emails.findIndex(email => email._id.toString() === emailId);
  if (index !== -1) {
    this.emails.splice(index, 1);

    // Reorder remaining emails
    this.emails.forEach((email, i) => {
      email.order = i + 1;
    });
  }
  return this;
};

// Add a method to update an email in the sequence
EmailSequenceSchema.methods.updateEmail = function(emailId, emailData) {
  const email = this.emails.id(emailId);
  if (email) {
    Object.assign(email, emailData);
  }
  return this;
};

// Add a method to reorder emails in the sequence
EmailSequenceSchema.methods.reorderEmail = function(emailId, newOrder) {
  const email = this.emails.id(emailId);
  if (!email) return this;

  const oldOrder = email.order;

  // Update orders of affected emails
  this.emails.forEach(e => {
    if (oldOrder < newOrder && e.order > oldOrder && e.order <= newOrder) {
      e.order--;
    } else if (oldOrder > newOrder && e.order < oldOrder && e.order >= newOrder) {
      e.order++;
    }
  });

  // Set new order for the email
  email.order = newOrder;

  return this;
};

module.exports = mongoose.model('EmailSequence', EmailSequenceSchema);