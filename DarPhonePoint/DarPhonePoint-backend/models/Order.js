const mongoose = require('mongoose');

// Schema for order items
const OrderItemSchema = new mongoose.Schema({
  product: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Product',
    required: true
  },
  variant_sku: {
    type: String,
    trim: true
  },
  name: {
    type: String,
    required: true
  },
  sku: {
    type: String,
    required: true
  },
  quantity: {
    type: Number,
    required: true,
    min: [1, 'Quantity must be at least 1']
  },
  unit_price: {
    type: Number,
    required: true,
    min: [0, 'Unit price cannot be negative']
  },
  total_price: {
    type: Number,
    required: true,
    min: [0, 'Total price cannot be negative']
  },
  // Phone-specific fields
  imei: {
    type: String,
    trim: true
  },
  condition: {
    type: String,
    enum: ['new', 'refurbished', 'used', 'open_box', 'damaged'],
    default: 'new'
  },
  warranty_months: {
    type: Number,
    min: 0,
    default: 12
  },
  product_snapshot: {
    // Store product details at time of order
    name: String,
    description: String,
    brand: String,
    model: String,
    specifications: [{
      name: String,
      value: String,
      category: String
    }],
    image_url: String
  }
});

// Schema for shipping address
const ShippingAddressSchema = new mongoose.Schema({
  first_name: {
    type: String,
    required: true,
    trim: true
  },
  last_name: {
    type: String,
    required: true,
    trim: true
  },
  company: {
    type: String,
    trim: true
  },
  address_line_1: {
    type: String,
    required: true,
    trim: true
  },
  address_line_2: {
    type: String,
    trim: true
  },
  city: {
    type: String,
    required: true,
    trim: true
  },
  state: {
    type: String,
    required: true,
    trim: true
  },
  postal_code: {
    type: String,
    required: true,
    trim: true
  },
  country: {
    type: String,
    required: true,
    trim: true,
    default: 'Tanzania'
  },
  phone: {
    type: String,
    trim: true
  }
}, { _id: false });

const OrderSchema = new mongoose.Schema({
  // Order identification
  order_number: {
    type: String,
    required: true,
    unique: true,
    trim: true
  },

  // Customer information
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  customer_email: {
    type: String,
    required: true,
    trim: true,
    lowercase: true
  },
  customer_name: {
    type: String,
    required: true,
    trim: true
  },
  customer_phone: {
    type: String,
    trim: true
  },
  is_guest: {
    type: Boolean,
    default: false
  },

  // Order items
  items: [OrderItemSchema],

  // Legacy single product support (for backward compatibility)
  product: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Product'
  },

  // Pricing breakdown
  subtotal: {
    type: Number,
    required: true,
    min: [0, 'Subtotal cannot be negative']
  },
  shipping_cost: {
    type: Number,
    default: 0,
    min: [0, 'Shipping cost cannot be negative']
  },
  tax_amount: {
    type: Number,
    default: 0,
    min: [0, 'Tax amount cannot be negative']
  },
  discount_amount: {
    type: Number,
    default: 0,
    min: [0, 'Discount amount cannot be negative']
  },
  total_amount: {
    type: Number,
    required: true,
    min: [0, 'Total amount cannot be negative']
  },

  // Legacy amount field (for backward compatibility)
  amount: {
    type: Number,
    required: function() {
      return !this.total_amount; // Only required if total_amount is not set
    }
  },

  // Payment information
  payment_status: {
    type: String,
    enum: ['pending', 'completed', 'failed', 'refunded', 'partially_refunded'],
    default: 'pending'
  },
  payment_method: {
    type: String,
    required: true,
    enum: ['credit_card', 'debit_card', 'mobile_money', 'bank_transfer', 'cash_on_delivery', 'whop']
  },
  transaction_id: {
    type: String,
    trim: true
  },
  payment_reference: {
    type: String,
    trim: true
  },

  // Order status and fulfillment
  order_status: {
    type: String,
    enum: ['pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled', 'returned'],
    default: 'pending'
  },
  fulfillment_status: {
    type: String,
    enum: ['unfulfilled', 'partially_fulfilled', 'fulfilled'],
    default: 'unfulfilled'
  },

  // Shipping information
  requires_shipping: {
    type: Boolean,
    default: true
  },
  shipping_address: ShippingAddressSchema,
  billing_address: ShippingAddressSchema,
  shipping_method: {
    type: String,
    enum: ['standard', 'express', 'overnight', 'pickup', 'same_day']
  },

  // Discounts and promotions
  discount_code: {
    type: String,
    trim: true
  },
  discount_type: {
    type: String,
    enum: ['percentage', 'fixed_amount', 'free_shipping']
  },

  // Order notes and metadata
  notes: String,
  internal_notes: String,
  source: {
    type: String,
    default: 'website',
    enum: ['website', 'mobile_app', 'phone', 'admin', 'api']
  },

  // Important dates
  confirmed_at: Date,
  shipped_at: Date,
  delivered_at: Date,
  cancelled_at: Date,

  created_at: {
    type: Date,
    default: Date.now
  },
  updated_at: {
    type: Date,
    default: Date.now
  }
});

// Pre-save middleware to generate order number and update timestamps
OrderSchema.pre('save', function(next) {
  this.updated_at = new Date();

  // Generate order number if not exists
  if (!this.order_number) {
    const timestamp = Date.now().toString();
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    this.order_number = `PPD-${timestamp.slice(-6)}${random}`;
  }

  // Calculate total_amount if not set (for backward compatibility)
  if (!this.total_amount && this.amount) {
    this.total_amount = this.amount;
  }

  next();
});

// Virtual for order total items count
OrderSchema.virtual('total_items').get(function() {
  if (this.items && this.items.length > 0) {
    return this.items.reduce((total, item) => total + item.quantity, 0);
  }
  return 1; // For legacy single product orders
});

// Virtual for checking if order can be cancelled
OrderSchema.virtual('can_cancel').get(function() {
  return ['pending', 'confirmed'].includes(this.order_status) &&
         this.payment_status !== 'completed';
});

// Virtual for checking if order can be refunded
OrderSchema.virtual('can_refund').get(function() {
  return this.payment_status === 'completed' &&
         ['delivered', 'shipped'].includes(this.order_status);
});

// Virtual for full shipping address
OrderSchema.virtual('full_shipping_address').get(function() {
  const addr = this.shipping_address;
  if (!addr) return '';

  let address = `${addr.first_name} ${addr.last_name}\n`;
  if (addr.company) address += `${addr.company}\n`;
  address += `${addr.address_line_1}\n`;
  if (addr.address_line_2) address += `${addr.address_line_2}\n`;
  address += `${addr.city}, ${addr.state} ${addr.postal_code}\n`;
  address += addr.country;

  return address;
});

// Set virtuals to be included in JSON output
OrderSchema.set('toJSON', { virtuals: true });
OrderSchema.set('toObject', { virtuals: true });

// Method to calculate order totals
OrderSchema.methods.calculateTotals = function() {
  if (this.items && this.items.length > 0) {
    this.subtotal = this.items.reduce((total, item) => total + item.total_price, 0);
  } else if (this.amount) {
    this.subtotal = this.amount;
  }

  this.total_amount = this.subtotal + this.shipping_cost + this.tax_amount - this.discount_amount;
  return this.total_amount;
};

// Method to update order status
OrderSchema.methods.updateStatus = function(newStatus, notes) {
  const oldStatus = this.order_status;
  this.order_status = newStatus;

  // Update timestamp fields based on status
  const now = new Date();
  switch (newStatus) {
    case 'confirmed':
      if (!this.confirmed_at) this.confirmed_at = now;
      break;
    case 'shipped':
      if (!this.shipped_at) this.shipped_at = now;
      break;
    case 'delivered':
      if (!this.delivered_at) this.delivered_at = now;
      break;
    case 'cancelled':
      if (!this.cancelled_at) this.cancelled_at = now;
      break;
  }

  if (notes) {
    this.internal_notes = (this.internal_notes || '') +
      `\n${now.toISOString()}: Status changed from ${oldStatus} to ${newStatus}. ${notes}`;
  }
};

// Add indexes for better query performance
// order_number index already created by unique: true constraint
OrderSchema.index({ user: 1 });
OrderSchema.index({ product: 1 });
OrderSchema.index({ customer_email: 1 });
OrderSchema.index({ payment_status: 1 });
OrderSchema.index({ order_status: 1 });
OrderSchema.index({ transaction_id: 1 });
OrderSchema.index({ created_at: -1 });

// Compound indexes for common query patterns
OrderSchema.index({ user: 1, created_at: -1 });
OrderSchema.index({ payment_status: 1, created_at: -1 });
OrderSchema.index({ order_status: 1, created_at: -1 });
OrderSchema.index({ customer_email: 1, payment_status: 1 });
OrderSchema.index({ order_status: 1, payment_status: 1 });

module.exports = mongoose.model('Order', OrderSchema);