const mongoose = require('mongoose');

// Schema for inventory movements/transactions
const InventoryMovementSchema = new mongoose.Schema({
  type: {
    type: String,
    required: true,
    enum: ['purchase', 'sale', 'adjustment', 'return', 'damage', 'transfer']
  },
  quantity: {
    type: Number,
    required: true
  },
  reason: {
    type: String,
    required: true
  },
  reference_id: {
    type: String, // Order ID, Purchase Order ID, etc.
    trim: true
  },
  reference_type: {
    type: String,
    enum: ['order', 'purchase_order', 'manual_adjustment', 'return', 'damage_report']
  },
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  notes: String,
  created_at: {
    type: Date,
    default: Date.now
  }
});

// Main inventory tracking schema
const InventorySchema = new mongoose.Schema({
  product: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Product',
    required: true
  },
  variant_sku: {
    type: String,
    required: false // null for products without variants
  },
  location: {
    type: String,
    required: true,
    default: 'main_warehouse'
  },
  
  // Current stock levels
  quantity_on_hand: {
    type: Number,
    required: true,
    min: [0, 'Quantity on hand cannot be negative'],
    default: 0
  },
  quantity_reserved: {
    type: Number,
    required: true,
    min: [0, 'Quantity reserved cannot be negative'],
    default: 0
  },
  quantity_available: {
    type: Number,
    required: true,
    min: [0, 'Quantity available cannot be negative'],
    default: 0
  },
  
  // Stock thresholds
  reorder_point: {
    type: Number,
    required: true,
    min: [0, 'Reorder point cannot be negative'],
    default: 10
  },
  reorder_quantity: {
    type: Number,
    required: true,
    min: [0, 'Reorder quantity cannot be negative'],
    default: 50
  },
  max_stock_level: {
    type: Number,
    min: [0, 'Max stock level cannot be negative']
  },
  
  // Cost tracking
  average_cost: {
    type: Number,
    min: [0, 'Average cost cannot be negative'],
    default: 0
  },
  last_cost: {
    type: Number,
    min: [0, 'Last cost cannot be negative'],
    default: 0
  },

  // Individual device tracking (for phones with IMEI)
  devices: [{
    imei: {
      type: String,
      validate: {
        validator: function(v) {
          return !v || /^\d{15}$/.test(v);
        },
        message: 'IMEI must be exactly 15 digits'
      }
    },
    serial_number: {
      type: String,
      trim: true
    },
    condition: {
      type: String,
      enum: ['new', 'refurbished', 'used', 'open_box', 'damaged'],
      default: 'new'
    },
    warranty_months: {
      type: Number,
      default: 12,
      min: 0
    },
    status: {
      type: String,
      enum: ['available', 'reserved', 'sold', 'damaged', 'returned'],
      default: 'available'
    },
    reserved_for: {
      type: String, // User ID or session ID
      default: null
    },
    reserved_at: {
      type: Date,
      default: null
    },
    reservation_expires: {
      type: Date,
      default: null
    },
    sold_to_order: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Order',
      default: null
    },
    sold_at: {
      type: Date,
      default: null
    },
    received_at: {
      type: Date,
      default: Date.now
    },
    purchase_price: {
      type: Number,
      min: 0
    },
    supplier: {
      type: String,
      trim: true
    },
    notes: {
      type: String,
      trim: true
    }
  }],

  // Tracking
  last_counted_at: Date,
  last_movement_at: Date,
  movements: [InventoryMovementSchema],
  
  created_at: {
    type: Date,
    default: Date.now
  },
  updated_at: {
    type: Date,
    default: Date.now
  }
});

// Virtual for checking if reorder is needed
InventorySchema.virtual('needs_reorder').get(function() {
  return this.quantity_available <= this.reorder_point;
});

// Virtual for checking if overstocked
InventorySchema.virtual('is_overstocked').get(function() {
  return this.max_stock_level && this.quantity_on_hand > this.max_stock_level;
});

// Set virtuals to be included in JSON output
InventorySchema.set('toJSON', { virtuals: true });
InventorySchema.set('toObject', { virtuals: true });

// Pre-save middleware to update timestamps and calculate available quantity
InventorySchema.pre('save', function(next) {
  this.updated_at = new Date();
  this.quantity_available = this.quantity_on_hand - this.quantity_reserved;
  next();
});

// Method to add inventory movement
InventorySchema.methods.addMovement = function(type, quantity, reason, user, options = {}) {
  const movement = {
    type,
    quantity,
    reason,
    user,
    reference_id: options.reference_id,
    reference_type: options.reference_type,
    notes: options.notes
  };
  
  this.movements.push(movement);
  this.last_movement_at = new Date();
  
  // Update quantities based on movement type
  switch (type) {
    case 'purchase':
    case 'return':
    case 'adjustment':
      if (quantity > 0) {
        this.quantity_on_hand += quantity;
      } else {
        this.quantity_on_hand += quantity; // quantity is negative for adjustments down
      }
      break;
    case 'sale':
    case 'damage':
      this.quantity_on_hand -= Math.abs(quantity);
      break;
  }
  
  // Ensure quantities don't go negative
  this.quantity_on_hand = Math.max(0, this.quantity_on_hand);
  this.quantity_reserved = Math.max(0, this.quantity_reserved);
};

// Method to reserve inventory
InventorySchema.methods.reserveQuantity = function(quantity) {
  if (this.quantity_available >= quantity) {
    this.quantity_reserved += quantity;
    return true;
  }
  return false;
};

// Method to release reserved inventory
InventorySchema.methods.releaseReservedQuantity = function(quantity) {
  this.quantity_reserved = Math.max(0, this.quantity_reserved - quantity);
};

// Indexes for better query performance
InventorySchema.index({ product: 1 });
InventorySchema.index({ variant_sku: 1 });
InventorySchema.index({ location: 1 });
InventorySchema.index({ product: 1, variant_sku: 1, location: 1 }, { unique: true });
InventorySchema.index({ quantity_available: 1 });
InventorySchema.index({ reorder_point: 1 });
InventorySchema.index({ last_movement_at: -1 });

// Compound indexes for common queries
InventorySchema.index({ product: 1, location: 1 });
InventorySchema.index({ quantity_available: 1, reorder_point: 1 });

module.exports = mongoose.model('Inventory', InventorySchema);
