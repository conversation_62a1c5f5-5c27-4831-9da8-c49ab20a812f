const express = require('express');
const connectDB = require('./config/db');
const databaseHealthService = require('./services/databaseHealthService');
const env = require('./config/env');
const cors = require('cors');
const helmet = require('helmet');
const path = require('path');
const morgan = require('morgan');
const errorHandler = require('./middleware/errorHandler');
const logger = require('./utils/logger');
const { initCronJobs } = require('./config/cron');
const warmupCache = require('./utils/cacheWarmup');
const performanceMiddleware = require('./middleware/performanceMiddleware');
const { addCorrelationId, logRequestPerformance, logErrorContext } = require('./middleware/requestLogger');
const { apiLimiter, authLimiter, paymentLimiter, adminLimiter, webhookLimiter } = require('./middleware/rateLimiter');
const {
  preventMongoInjection,
  enhancedXSSProtection,
  requestSizeLimiter,
  detectSuspiciousActivity
} = require('./middleware/inputValidation');
const https = require('https');
const http = require('http');
const fs = require('fs');
const mongoose = require('mongoose');
const passport = require('./config/passport');
const session = require('express-session');
const config = require('./config/config');

// Initialize express app
const app = express();

// Connect to database with health monitoring
connectDB().then(() => {
  // Start database health monitoring after successful connection
  databaseHealthService.startMonitoring();
  logger.info('Database health monitoring initialized');
}).catch(error => {
  logger.error('Database connection failed:', error);
  process.exit(1);
});

// Development mode - skip heavy production services
logger.info('Development mode - skipping heavy production services');

// Initialize email templates
const emailTemplateService = require('./services/emailTemplateService');
emailTemplateService.loadTemplates().catch(error => {
  logger.error('Failed to load email templates:', error);
});

// Security middleware with enhanced configuration
const helmetConfig = {
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
      fontSrc: ["'self'", "https://fonts.gstatic.com"],
      imgSrc: ["'self'", "data:", "https:", "blob:"],
      scriptSrc: ["'self'"],
      connectSrc: ["'self'", "https://api.stripe.com"],
      frameSrc: ["'self'", "https://js.stripe.com"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      workerSrc: ["'self'", "blob:"],
    },
  },
  crossOriginEmbedderPolicy: false,
  hsts: {
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true
  }
};

app.use(helmet(helmetConfig));

// Trust proxy for accurate IP addresses
app.set('trust proxy', 1);

// Body parsing middleware
app.use(express.json({ 
  limit: '50mb',
  verify: (req, res, buf) => {
    req.rawBody = buf;
  }
}));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));

// CORS configuration - simplified for development
const corsOptions = {
  origin: ['http://localhost:5173', 'http://localhost:3000', 'http://127.0.0.1:5173', 'http://127.0.0.1:3000'],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'Accept', 'Origin'],
  exposedHeaders: ['X-Total-Count', 'X-Page-Count'],
  maxAge: 86400 // 24 hours
};

app.use(cors(corsOptions));

// Special CORS handling for email tracking routes (allow all origins)
app.use('/api/email/track', cors({
  origin: true, // Allow all origins for email tracking
  credentials: false,
  methods: ['GET', 'OPTIONS'],
  allowedHeaders: ['Content-Type'],
}));

// Request logging
if (env.NODE_ENV === 'development') {
  app.use(morgan('dev'));
} else {
  app.use(morgan('combined'));
}

// Add correlation ID to all requests
app.use(addCorrelationId);

// Log request performance
app.use(logRequestPerformance);

// Add performance monitoring middleware
app.use(performanceMiddleware);

// Apply general rate limiting to all API routes
app.use('/api', apiLimiter);

// Define routes with specific rate limiting
app.use('/api/auth', authLimiter, require('./routes/auth'));
app.use('/api/leads', require('./routes/leads'));
app.use('/api/products', require('./routes/products'));
app.use('/api/orders', require('./routes/orders')); // Payment limiter is applied per-route inside orders.js

app.use('/api/analytics', require('./routes/analytics'));
app.use('/api/email-sequences', require('./routes/emailSequences'));
app.use('/api/email', require('./routes/email'));
app.use('/api/users', require('./routes/users'));
app.use('/api/cart', require('./routes/cart'));
app.use('/api/inventory', require('./routes/inventory'));
app.use('/api/phone-inventory', require('./routes/phoneInventory'));
app.use('/api/shipping', require('./routes/shipping'));
app.use('/api/trade-in', require('./routes/tradeIn'));

app.use('/api/unsubscribe', require('./routes/unsubscribeRoutes'));
app.use('/api/audit-logs', require('./routes/auditLogRoutes'));
app.use('/api/health', require('./routes/health'));
app.use('/api/monitoring', require('./routes/monitoringRoutes'));
app.use('/api/admin', adminLimiter, require('./routes/admin'));
app.use('/api/admin/dashboard', adminLimiter, require('./routes/adminDashboard'));

app.use('/api/webhooks', webhookLimiter, require('./routes/webhooks'));
app.use('/api/settings', require('./routes/settings'));
app.use('/api/search', require('./routes/search'));
app.use('/api/wishlist', require('./routes/wishlist'));
app.use('/api/placeholder', require('./routes/placeholder'));

// Serve static files from the public directory
app.use('/public', express.static(path.join(__dirname, 'public')));
app.use('/uploads', express.static(path.join(__dirname, 'public/uploads'), {
  maxAge: '1d',
  etag: true,
  lastModified: true,
  setHeaders: (res, path) => {
    if (path.endsWith('.jpg') || path.endsWith('.jpeg') || path.endsWith('.png') || path.endsWith('.gif')) {
      res.setHeader('Cache-Control', 'public, max-age=86400'); // 1 day for images
    }
  }
}));

// Initialize Passport
app.use(passport.initialize());

// Session middleware (required for Passport)
app.use(session({
  secret: config.JWT_SECRET,
  resave: false,
  saveUninitialized: false,
  cookie: {
    secure: env.NODE_ENV === 'production',
    httpOnly: true,
    maxAge: 24 * 60 * 60 * 1000 // 24 hours
  }
}));

// Simple health check endpoint for development
app.get('/api/health', async (req, res) => {
  try {
    const health = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      environment: env.NODE_ENV,
      database: mongoose.connection.readyState === 1 ? 'connected' : 'disconnected',
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      version: process.version
    };

    res.json(health);
  } catch (error) {
    logger.error('Health check error:', error);
    res.status(500).json({
      status: 'unhealthy',
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// Error context logging middleware
app.use(logErrorContext);

// Error handling middleware
app.use(errorHandler);

// Create server
let server;
if (env.NODE_ENV === 'production' && env.SSL_CERT && env.SSL_KEY) {
  const sslOptions = {
    key: fs.readFileSync(env.SSL_KEY),
    cert: fs.readFileSync(env.SSL_CERT)
  };
  server = https.createServer(sslOptions, app);

  // Redirect HTTP to HTTPS in production
  const httpApp = express();
  httpApp.use((req, res) => {
    res.redirect(`https://${env.DOMAIN}${req.url}`);
  });

  http.createServer(httpApp).listen(80, () => {
    logger.info('HTTP server running on port 80 (redirecting to HTTPS)');
  });
} else {
  server = http.createServer(app);
}

// Start server
server.listen(env.PORT, env.HOST, () => {
  logger.info(`Server running in ${env.NODE_ENV} mode on ${env.HOST}:${env.PORT}`);

  // Warm up cache
  warmupCache().catch(error => {
    logger.error('Cache warmup failed:', error);
  });

  // Initialize cron jobs
  initCronJobs();
});

// Graceful shutdown
const gracefulShutdown = async (signal) => {
  logger.info(`Received ${signal}. Starting graceful shutdown...`);
  
  try {
    // Close server
    server.close(() => {
      logger.info('HTTP server closed');
    });

    // Close database connection
    await mongoose.connection.close();
    logger.info('Database connection closed');

    logger.info('Graceful shutdown completed');
    process.exit(0);
  } catch (error) {
    logger.error('Error during graceful shutdown:', error);
    process.exit(1);
  }
};

// Listen for termination signals
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

// Handle unhandled promise rejections
process.on('unhandledRejection', (err, promise) => {
  logger.error('Unhandled Promise Rejection:', err);
  gracefulShutdown('Unhandled Promise Rejection');
});

// Handle uncaught exceptions
process.on('uncaughtException', (err) => {
  logger.error('Uncaught Exception:', err);
  gracefulShutdown('Uncaught Exception');
});

module.exports = app;
