const cacheService = require('../services/cacheService');
const logger = require('../utils/logger');

/**
 * Cache middleware for Express routes
 * @param {number} ttl - Time to live in seconds
 * @param {Function} keyGenerator - Custom function to generate cache key (optional)
 * @returns {Function} - Express middleware
 */
exports.cacheMiddleware = (ttl = 3600, keyGenerator = null) => {
  return async (req, res, next) => {
    // Skip caching for non-GET requests
    if (req.method !== 'GET') {
      return next();
    }

    // Skip caching if explicitly disabled
    if (req.query.noCache === 'true') {
      return next();
    }

    // Generate cache key
    let cacheKey;
    if (keyGenerator && typeof keyGenerator === 'function') {
      cacheKey = keyGenerator(req);
    } else {
      // Default key generation based on URL and auth status
      const userId = req.user ? req.user.id : 'guest';
      cacheKey = `${userId}:${req.originalUrl}`;
    }

    try {
      // Try to get from cache
      const cachedData = await cacheService.get(cacheKey);

      if (cachedData) {
        logger.debug(`Cache hit for ${cacheKey}`);
        return res.json(cachedData);
      }

      // Cache miss, continue to handler
      logger.debug(`Cache miss for ${cacheKey}`);

      // Store original send function
      const originalSend = res.json;

      // Override send function to cache response
      res.json = function(data) {
        // Only cache successful responses
        if (res.statusCode >= 200 && res.statusCode < 300) {
          cacheService.set(cacheKey, data, ttl)
            .catch(err => logger.error(`Error caching response: ${err.message}`));
        }

        // Call original send function
        return originalSend.call(this, data);
      };

      next();
    } catch (error) {
      logger.error(`Cache middleware error: ${error.message}`);
      next();
    }
  };
};

/**
 * Clear cache for specific patterns
 * @param {string} pattern - Cache key pattern to clear
 * @returns {Function} - Express middleware
 */
exports.clearCache = (pattern) => {
  return async (req, res, next) => {
    try {
      // Clear cache after response is sent
      res.on('finish', async () => {
        try {
          // Only clear cache for successful requests
          if (res.statusCode >= 200 && res.statusCode < 300) {
            await cacheService.invalidatePattern(pattern);
            logger.debug(`Cache cleared for pattern: ${pattern}`);
          }
        } catch (error) {
          logger.error(`Error clearing cache pattern ${pattern}:`, error);
        }
      });

      next();
    } catch (error) {
      logger.error(`Clear cache middleware error: ${error.message}`);
      next();
    }
  };
};

/**
 * Clear all cache
 * @returns {Function} - Express middleware
 */
exports.clearAllCache = () => {
  return async (req, res, next) => {
    try {
      // Clear cache after response is sent
      res.on('finish', async () => {
        try {
          // Only clear cache for successful requests
          if (res.statusCode >= 200 && res.statusCode < 300) {
            await cacheService.clear();
            logger.debug('All cache cleared');
          }
        } catch (error) {
          logger.error('Error clearing all cache:', error);
        }
      });

      next();
    } catch (error) {
      logger.error(`Clear all cache middleware error: ${error.message}`);
      next();
    }
  };
};
