const compression = require('compression');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const config = require('../config/config');
const logger = require('../utils/logger');
const enhancedMonitoringService = require('../services/enhancedMonitoringService');
const advancedCacheService = require('../services/advancedCacheService');
const cdnService = require('../services/cdnService');

/**
 * Production-ready middleware stack
 */
class ProductionMiddleware {
  /**
   * Initialize production middleware
   * @param {Object} app - Express app
   */
  static init(app) {
    // Security headers
    this.setupSecurity(app);
    
    // Performance optimizations
    this.setupPerformance(app);
    
    // Monitoring and analytics
    this.setupMonitoring(app);
    
    // Rate limiting
    this.setupRateLimiting(app);
    
    // CDN and static assets
    this.setupCDN(app);
    
    logger.info('Production middleware initialized');
  }

  /**
   * Setup security middleware
   * @param {Object} app - Express app
   */
  static setupSecurity(app) {
    // Helmet for security headers
    app.use(helmet({
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ["'self'"],
          styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
          fontSrc: ["'self'", "https://fonts.gstatic.com"],
          imgSrc: ["'self'", "data:", "https:"],
          scriptSrc: ["'self'"],
          connectSrc: ["'self'", "https://api.whop.com"],
          frameSrc: ["'none'"],
          objectSrc: ["'none'"],
          upgradeInsecureRequests: process.env.NODE_ENV === 'production' ? [] : null
        }
      },
      hsts: {
        maxAge: 31536000,
        includeSubDomains: true,
        preload: true
      },
      noSniff: true,
      xssFilter: true,
      referrerPolicy: { policy: 'same-origin' }
    }));

    // Trust proxy for production deployment
    if (process.env.NODE_ENV === 'production') {
      app.set('trust proxy', 1);
    }
  }

  /**
   * Setup performance middleware
   * @param {Object} app - Express app
   */
  static setupPerformance(app) {
    // Compression middleware
    app.use(compression({
      filter: (req, res) => {
        if (req.headers['x-no-compression']) {
          return false;
        }
        return compression.filter(req, res);
      },
      level: 6,
      threshold: 1024
    }));

    // Response time tracking
    app.use((req, res, next) => {
      const startTime = Date.now();
      
      res.on('finish', () => {
        const responseTime = Date.now() - startTime;
        
        // Record metrics
        enhancedMonitoringService.recordRequest({
          method: req.method,
          path: req.path,
          statusCode: res.statusCode,
          responseTime,
          userId: req.user?.id
        });
        
        // Log slow requests
        if (responseTime > 1000) {
          logger.warn('Slow request detected:', {
            method: req.method,
            path: req.path,
            responseTime,
            userAgent: req.get('User-Agent')
          });
        }
      });
      
      next();
    });

    // Cache control for API responses
    app.use('/api', (req, res, next) => {
      // Set cache headers for GET requests
      if (req.method === 'GET') {
        const cacheTime = this.getCacheTime(req.path);
        res.set('Cache-Control', `public, max-age=${cacheTime}`);
      } else {
        res.set('Cache-Control', 'no-cache, no-store, must-revalidate');
      }
      next();
    });
  }

  /**
   * Setup monitoring middleware
   * @param {Object} app - Express app
   */
  static setupMonitoring(app) {
    // Request logging
    app.use((req, res, next) => {
      logger.info('Request received:', {
        method: req.method,
        path: req.path,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        timestamp: new Date().toISOString()
      });
      next();
    });

    // Error tracking
    app.use((err, req, res, next) => {
      enhancedMonitoringService.recordError({
        error: err,
        context: `${req.method} ${req.path}`,
        userId: req.user?.id,
        severity: err.status >= 500 ? 'error' : 'warning'
      });
      next(err);
    });

    // Health check endpoint
    app.get('/health', async (req, res) => {
      try {
        const health = {
          status: 'healthy',
          timestamp: new Date().toISOString(),
          uptime: process.uptime(),
          memory: process.memoryUsage(),
          services: {
            database: await this.checkDatabaseHealth(),
            cache: await advancedCacheService.healthCheck(),
            cdn: await cdnService.healthCheck(),
            monitoring: await enhancedMonitoringService.healthCheck()
          }
        };

        const isHealthy = Object.values(health.services)
          .every(service => service.status === 'healthy');

        res.status(isHealthy ? 200 : 503).json(health);
      } catch (error) {
        res.status(503).json({
          status: 'unhealthy',
          error: error.message,
          timestamp: new Date().toISOString()
        });
      }
    });
  }

  /**
   * Setup rate limiting
   * @param {Object} app - Express app
   */
  static setupRateLimiting(app) {
    // General API rate limiting
    const generalLimiter = rateLimit({
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: 100, // limit each IP to 100 requests per windowMs
      message: {
        error: 'Too many requests from this IP, please try again later.',
        retryAfter: '15 minutes'
      },
      standardHeaders: true,
      legacyHeaders: false,
      skip: (req) => {
        // Skip rate limiting for health checks
        return req.path === '/health' || req.path === '/api/health';
      }
    });

    // Authentication rate limiting
    const authLimiter = rateLimit({
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: 5, // limit each IP to 5 auth requests per windowMs
      message: {
        error: 'Too many authentication attempts, please try again later.',
        retryAfter: '15 minutes'
      },
      skipSuccessfulRequests: true
    });

    // Payment rate limiting
    const paymentLimiter = rateLimit({
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: 10, // limit each IP to 10 payment requests per windowMs
      message: {
        error: 'Too many payment requests, please try again later.',
        retryAfter: '15 minutes'
      }
    });

    // Apply rate limiting
    app.use('/api', generalLimiter);
    app.use('/api/auth', authLimiter);
    app.use('/api/orders', paymentLimiter);
    app.use('/api/whop', paymentLimiter);
  }

  /**
   * Setup CDN and static asset handling
   * @param {Object} app - Express app
   */
  static setupCDN(app) {
    // Static file serving with optimization
    app.use('/static', async (req, res, next) => {
      try {
        const assetPath = req.path;
        const cacheHeaders = cdnService.getCacheHeaders(assetPath);
        
        // Set cache headers
        Object.entries(cacheHeaders).forEach(([key, value]) => {
          res.set(key, value);
        });
        
        next();
      } catch (error) {
        logger.error('CDN middleware error:', error);
        next();
      }
    });

    // Preload critical assets
    app.use((req, res, next) => {
      if (req.path === '/' || req.path === '/dashboard') {
        const criticalAssets = [
          '/static/css/main.css',
          '/static/js/main.js',
          '/static/fonts/main.woff2'
        ];
        
        cdnService.generatePreloadHeaders(criticalAssets)
          .then(linkHeader => {
            if (linkHeader) {
              res.set('Link', linkHeader);
            }
          })
          .catch(error => {
            logger.error('Preload header generation error:', error);
          });
      }
      next();
    });
  }

  /**
   * Get cache time for different endpoints
   * @param {string} path - Request path
   * @returns {number} - Cache time in seconds
   */
  static getCacheTime(path) {
    if (path.includes('/products')) return 300; // 5 minutes
    if (path.includes('/analytics')) return 60; // 1 minute
    if (path.includes('/health')) return 0; // No cache
    return 30; // Default 30 seconds
  }

  /**
   * Check database health
   * @returns {Promise<Object>} - Database health status
   */
  static async checkDatabaseHealth() {
    try {
      const mongoose = require('mongoose');
      const isConnected = mongoose.connection.readyState === 1;
      
      if (isConnected) {
        // Test database operation
        await mongoose.connection.db.admin().ping();
        return { status: 'healthy', connected: true };
      } else {
        return { status: 'unhealthy', connected: false };
      }
    } catch (error) {
      return { status: 'unhealthy', error: error.message };
    }
  }

  /**
   * Setup graceful shutdown
   * @param {Object} app - Express app
   */
  static setupGracefulShutdown(app) {
    const gracefulShutdown = (signal) => {
      logger.info(`Received ${signal}, starting graceful shutdown...`);
      
      const server = app.get('server');
      if (server) {
        server.close(() => {
          logger.info('HTTP server closed');
          
          // Close database connections
          require('mongoose').connection.close(() => {
            logger.info('Database connection closed');
            process.exit(0);
          });
        });
      }
    };

    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));
  }

  /**
   * Setup error handling
   * @param {Object} app - Express app
   */
  static setupErrorHandling(app) {
    // 404 handler
    app.use('*', (req, res) => {
      res.status(404).json({
        success: false,
        message: 'Resource not found',
        path: req.originalUrl
      });
    });

    // Global error handler
    app.use((err, req, res, next) => {
      logger.error('Unhandled error:', {
        error: err.message,
        stack: err.stack,
        path: req.path,
        method: req.method
      });

      const status = err.status || 500;
      const message = process.env.NODE_ENV === 'production' 
        ? 'Internal server error' 
        : err.message;

      res.status(status).json({
        success: false,
        message,
        ...(process.env.NODE_ENV !== 'production' && { stack: err.stack })
      });
    });
  }
}

module.exports = ProductionMiddleware;
