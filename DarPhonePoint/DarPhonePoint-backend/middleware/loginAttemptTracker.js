/**
 * Login Attempt Tracker Middleware
 * Tracks failed login attempts and implements account lockout
 */

const redisClient = require('../utils/redisClient');
const logger = require('../utils/logger');
const config = require('../config/config');

class LoginAttemptTracker {
  constructor() {
    this.FAILED_ATTEMPTS_PREFIX = 'failed_attempts:';
    this.ACCOUNT_LOCKOUT_PREFIX = 'account_lockout:';
    this.MAX_ATTEMPTS = parseInt(process.env.MAX_LOGIN_ATTEMPTS) || 5;
    this.LOCKOUT_DURATION = parseInt(process.env.LOCKOUT_DURATION) || 900; // 15 minutes
    this.ATTEMPT_WINDOW = parseInt(process.env.ATTEMPT_WINDOW) || 900; // 15 minutes
  }

  /**
   * Check if account is locked
   * @param {string} identifier - Email or IP address
   * @returns {Promise<Object>} Lock status and remaining time
   */
  async checkAccountLock(identifier) {
    try {
      const lockKey = `${this.ACCOUNT_LOCKOUT_PREFIX}${identifier}`;
      const lockData = await redisClient.get(lockKey);
      
      if (lockData) {
        const { lockedUntil, attempts } = JSON.parse(lockData);
        const now = Date.now();
        
        if (now < lockedUntil) {
          const remainingTime = Math.ceil((lockedUntil - now) / 1000);
          return {
            isLocked: true,
            remainingTime,
            attempts
          };
        } else {
          // Lock expired, remove it
          await redisClient.del(lockKey);
        }
      }
      
      return { isLocked: false };
    } catch (error) {
      logger.error('Error checking account lock:', error);
      return { isLocked: false };
    }
  }

  /**
   * Record failed login attempt
   * @param {string} identifier - Email or IP address
   * @param {Object} metadata - Additional metadata
   * @returns {Promise<Object>} Updated attempt status
   */
  async recordFailedAttempt(identifier, metadata = {}) {
    try {
      const attemptKey = `${this.FAILED_ATTEMPTS_PREFIX}${identifier}`;
      const lockKey = `${this.ACCOUNT_LOCKOUT_PREFIX}${identifier}`;
      
      // Get current attempts
      const currentAttempts = await redisClient.get(attemptKey);
      let attempts = currentAttempts ? parseInt(currentAttempts) : 0;
      attempts++;
      
      // Store updated attempt count
      await redisClient.setex(attemptKey, this.ATTEMPT_WINDOW, attempts);
      
      logger.warn(`Failed login attempt ${attempts}/${this.MAX_ATTEMPTS} for ${identifier}`, metadata);
      
      // Check if we need to lock the account
      if (attempts >= this.MAX_ATTEMPTS) {
        const lockedUntil = Date.now() + (this.LOCKOUT_DURATION * 1000);
        
        await redisClient.setex(lockKey, this.LOCKOUT_DURATION, JSON.stringify({
          lockedUntil,
          attempts,
          lockedAt: new Date().toISOString(),
          metadata
        }));
        
        // Clear attempt counter since account is now locked
        await redisClient.del(attemptKey);
        
        logger.error(`Account locked for ${identifier} after ${attempts} failed attempts`, {
          lockDuration: this.LOCKOUT_DURATION,
          ...metadata
        });
        
        return {
          isLocked: true,
          attempts,
          lockDuration: this.LOCKOUT_DURATION
        };
      }
      
      return {
        isLocked: false,
        attempts,
        remainingAttempts: this.MAX_ATTEMPTS - attempts
      };
    } catch (error) {
      logger.error('Error recording failed attempt:', error);
      return { isLocked: false, attempts: 0 };
    }
  }

  /**
   * Clear failed attempts (on successful login)
   * @param {string} identifier - Email or IP address
   * @returns {Promise<boolean>}
   */
  async clearFailedAttempts(identifier) {
    try {
      const attemptKey = `${this.FAILED_ATTEMPTS_PREFIX}${identifier}`;
      await redisClient.del(attemptKey);
      return true;
    } catch (error) {
      logger.error('Error clearing failed attempts:', error);
      return false;
    }
  }

  /**
   * Get current attempt count
   * @param {string} identifier - Email or IP address
   * @returns {Promise<number>}
   */
  async getAttemptCount(identifier) {
    try {
      const attemptKey = `${this.FAILED_ATTEMPTS_PREFIX}${identifier}`;
      const attempts = await redisClient.get(attemptKey);
      return attempts ? parseInt(attempts) : 0;
    } catch (error) {
      logger.error('Error getting attempt count:', error);
      return 0;
    }
  }

  /**
   * Middleware to check account lock before login
   * @param {Object} req - Express request
   * @param {Object} res - Express response
   * @param {Function} next - Next middleware
   */
  async checkLockMiddleware(req, res, next) {
    try {
      const { email } = req.body;
      const ip = req.ip;
      
      if (!email) {
        return next();
      }
      
      // Check both email and IP locks
      const emailLock = await this.checkAccountLock(email);
      const ipLock = await this.checkAccountLock(ip);
      
      if (emailLock.isLocked) {
        return res.status(423).json({
          success: false,
          message: `Account temporarily locked due to too many failed login attempts. Try again in ${Math.ceil(emailLock.remainingTime / 60)} minutes.`,
          lockType: 'account',
          remainingTime: emailLock.remainingTime
        });
      }
      
      if (ipLock.isLocked) {
        return res.status(423).json({
          success: false,
          message: `Too many failed login attempts from this IP address. Try again in ${Math.ceil(ipLock.remainingTime / 60)} minutes.`,
          lockType: 'ip',
          remainingTime: ipLock.remainingTime
        });
      }
      
      next();
    } catch (error) {
      logger.error('Error in lock check middleware:', error);
      next(); // Continue on error to avoid blocking legitimate users
    }
  }

  /**
   * Middleware to handle failed login attempts
   * @param {string} email - User email
   * @param {Object} req - Express request
   * @returns {Promise<Object>}
   */
  async handleFailedLogin(email, req) {
    const ip = req.ip;
    const metadata = {
      userAgent: req.headers['user-agent'],
      timestamp: new Date().toISOString()
    };
    
    // Record failed attempt for both email and IP
    const emailResult = await this.recordFailedAttempt(email, metadata);
    const ipResult = await this.recordFailedAttempt(ip, metadata);
    
    return {
      email: emailResult,
      ip: ipResult
    };
  }

  /**
   * Handle successful login
   * @param {string} email - User email
   * @param {Object} req - Express request
   * @returns {Promise<boolean>}
   */
  async handleSuccessfulLogin(email, req) {
    const ip = req.ip;
    
    // Clear failed attempts for both email and IP
    await this.clearFailedAttempts(email);
    await this.clearFailedAttempts(ip);
    
    return true;
  }
}

module.exports = new LoginAttemptTracker();
