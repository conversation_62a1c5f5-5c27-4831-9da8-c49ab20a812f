/**
 * Performance Tracking Middleware
 * Integrates with the performance monitoring service to track API performance
 */

const logger = require('../utils/logger');

/**
 * Middleware to track API request performance
 */
const trackApiPerformance = (req, res, next) => {
  const startTime = Date.now();
  
  // Store start time on request object
  req.startTime = startTime;
  
  // Override res.end to capture response time
  const originalEnd = res.end;
  
  res.end = function(chunk, encoding) {
    const endTime = Date.now();
    const responseTime = endTime - startTime;
    
    // Track the request performance
    try {
      performanceMonitoringService.trackApiRequest(req, res, responseTime);
      
      // Add performance headers
      res.set({
        'X-Response-Time': `${responseTime}ms`,
        'X-Request-ID': req.correlationId || 'unknown'
      });
      
      // Log slow requests
      if (responseTime > 1000) { // 1 second threshold
        logger.warn('Very slow API request', {
          method: req.method,
          url: req.originalUrl,
          responseTime,
          statusCode: res.statusCode,
          userAgent: req.get('User-Agent'),
          ip: req.ip,
          userId: req.user?.id
        });
      }
      
    } catch (error) {
      logger.error('Error tracking API performance:', error);
    }
    
    // Call original end method
    originalEnd.call(this, chunk, encoding);
  };
  
  next();
};

/**
 * Middleware to track database query performance
 * This should be used with mongoose middleware
 */
const trackDbQueryPerformance = () => {
  // Pre-hook to capture start time
  function preHook() {
    this.startTime = Date.now();
  }
  
  // Post-hook to calculate execution time
  function postHook() {
    if (this.startTime) {
      const executionTime = Date.now() - this.startTime;
      const operation = this.op || this.getQuery ? 'find' : 'unknown';
      const collection = this.model?.collection?.name || 'unknown';
      
      try {
        performanceMonitoringService.trackDbQuery(
          operation,
          collection,
          executionTime,
          this.getQuery ? this.getQuery() : {}
        );
      } catch (error) {
        logger.error('Error tracking DB query performance:', error);
      }
    }
  }
  
  return { preHook, postHook };
};

/**
 * Middleware to add performance metrics to API responses
 */
const addPerformanceMetrics = (req, res, next) => {
  // Override res.json to add performance data
  const originalJson = res.json;
  
  res.json = function(data) {
    // Only add performance metrics in development or for admin users
    if (process.env.NODE_ENV === 'development' || req.user?.role === 'admin') {
      const responseTime = req.startTime ? Date.now() - req.startTime : 0;
      
      // Add performance metadata
      if (data && typeof data === 'object' && data.success !== undefined) {
        data.performance = {
          responseTime: `${responseTime}ms`,
          timestamp: new Date().toISOString(),
          requestId: req.correlationId || 'unknown'
        };
      }
    }
    
    return originalJson.call(this, data);
  };
  
  next();
};

/**
 * Middleware to handle performance alerts
 */
const handlePerformanceAlerts = (req, res, next) => {
  // Check for performance alerts every 100 requests
  if (Math.random() < 0.01) { // 1% chance
    try {
      const alerts = performanceMonitoringService.getPerformanceAlerts();
      
      if (alerts.length > 0) {
        const criticalAlerts = alerts.filter(alert => alert.severity === 'critical');
        const warningAlerts = alerts.filter(alert => alert.severity === 'warning');
        
        if (criticalAlerts.length > 0) {
          logger.error('Critical performance alerts detected', {
            alerts: criticalAlerts,
            endpoint: req.originalUrl,
            method: req.method
          });
        }
        
        if (warningAlerts.length > 0) {
          logger.warn('Performance warnings detected', {
            alerts: warningAlerts,
            endpoint: req.originalUrl,
            method: req.method
          });
        }
      }
    } catch (error) {
      logger.error('Error checking performance alerts:', error);
    }
  }
  
  next();
};

/**
 * Setup mongoose performance monitoring
 */
const setupMongoosePerformanceMonitoring = (mongoose) => {
  const { preHook, postHook } = trackDbQueryPerformance();
  
  // Add hooks to all mongoose operations
  const operations = [
    'find', 'findOne', 'findOneAndUpdate', 'findOneAndDelete',
    'updateOne', 'updateMany', 'deleteOne', 'deleteMany',
    'aggregate', 'countDocuments', 'distinct'
  ];
  
  operations.forEach(operation => {
    mongoose.Query.prototype[operation] = function(...args) {
      // Add pre and post hooks
      this.pre(operation, preHook);
      this.post(operation, postHook);
      
      // Call original method
      return mongoose.Query.prototype[operation].apply(this, args);
    };
  });
};

/**
 * Performance monitoring dashboard endpoint
 */
const getPerformanceMetrics = (req, res) => {
  try {
    const summary = performanceMonitoringService.getPerformanceSummary();
    const alerts = performanceMonitoringService.getPerformanceAlerts();
    
    // Get detailed metrics for admin users
    let detailedMetrics = null;
    if (req.user?.role === 'admin') {
      detailedMetrics = performanceMonitoringService.getDetailedMetrics();
    }
    
    const response = {
      summary,
      alerts,
      ...(detailedMetrics && { detailed: detailedMetrics }),
      timestamp: new Date().toISOString()
    };
    
    return res.apiSuccess(response, 'Performance metrics retrieved successfully');
  } catch (error) {
    logger.error('Error getting performance metrics:', error);
    return res.apiError('Failed to retrieve performance metrics', 500);
  }
};

/**
 * Health check endpoint with performance data
 */
const healthCheckWithPerformance = (req, res) => {
  try {
    const summary = performanceMonitoringService.getPerformanceSummary();
    const alerts = performanceMonitoringService.getPerformanceAlerts();
    
    const health = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      performance: {
        avgResponseTime: summary.apiMetrics.avgResponseTime,
        errorRate: summary.apiMetrics.errorRate,
        totalRequests: summary.apiMetrics.totalRequests,
        slowQueries: summary.dbMetrics.slowQueries
      },
      alerts: alerts.length
    };
    
    // Determine overall health status
    const criticalAlerts = alerts.filter(alert => alert.severity === 'critical');
    if (criticalAlerts.length > 0) {
      health.status = 'critical';
    } else if (alerts.length > 0) {
      health.status = 'warning';
    }
    
    const statusCode = health.status === 'critical' ? 503 : 200;
    return res.status(statusCode).json({
      success: health.status !== 'critical',
      data: health,
      message: `System is ${health.status}`
    });
    
  } catch (error) {
    logger.error('Error in health check:', error);
    return res.status(503).json({
      success: false,
      data: {
        status: 'error',
        timestamp: new Date().toISOString(),
        error: error.message
      },
      message: 'Health check failed'
    });
  }
};

module.exports = {
  trackApiPerformance,
  trackDbQueryPerformance,
  addPerformanceMetrics,
  handlePerformanceAlerts,
  setupMongoosePerformanceMonitoring,
  getPerformanceMetrics,
  healthCheckWithPerformance
};
