const jwt = require('jsonwebtoken');
const config = require('../config/config');
const User = require('../models/User');
const jwtBlacklistService = require('../services/jwtBlacklistService');
const logger = require('../utils/logger');

// Protect middleware - verify JWT token (standardized to Bearer token only)
const protect = async function(req, res, next) {
  // Get token from Authorization header (Bearer token standard)
  let token = null;
  const authHeader = req.header('Authorization');

  if (authHeader && authHeader.startsWith('Bearer ')) {
    token = authHeader.substring(7); // Remove 'Bearer ' prefix
  }

  // Check if no token
  if (!token) {
    return res.status(401).json({
      success: false,
      message: 'No token, authorization denied'
    });
  }

  try {
    // Check if token is blacklisted
    const isBlacklisted = await jwtBlacklistService.isTokenBlacklisted(token);
    if (isBlacklisted) {
      logger.warn('Attempt to use blacklisted token');
      return res.status(401).json({
        success: false,
        message: 'Token has been revoked'
      });
    }

    // Verify token
    const decoded = jwt.verify(token, config.JWT_SECRET);

    // Find user by id
    const userId = decoded.user?.id || decoded.id;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'Invalid token format'
      });
    }

    const user = await User.findById(userId).select('-password');

    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'User not found'
      });
    }

    // Store token in request for potential blacklisting later
    req.token = token;
    req.user = user;

    // Track user session (if not already tracked)
    await jwtBlacklistService.trackUserSession(userId, token);

    // Enforce concurrent session limit
    await jwtBlacklistService.enforceConcurrentSessionLimit(
      userId,
      config.MAX_CONCURRENT_SESSIONS
    );

    next();
  } catch (err) {
    logger.error('Token verification error:', err);
    res.status(401).json({
      success: false,
      message: 'Token is not valid'
    });
  }
};

// Authorize middleware - check user role
const authorize = (...roles) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'User not authenticated'
      });
    }

    if (!roles.includes(req.user.role)) {
      return res.status(403).json({
        success: false,
        message: `User role ${req.user.role} is not authorized to access this route`
      });
    }

    next();
  };
};

// Optional auth middleware - sets req.user if token is valid, but doesn't require it
const optionalAuth = async function(req, res, next) {
  // Get token from header (support both x-auth-token and Authorization Bearer)
  let token = req.header('x-auth-token');

  // If no x-auth-token, check for Authorization header
  if (!token) {
    const authHeader = req.header('Authorization');
    if (authHeader && authHeader.startsWith('Bearer ')) {
      token = authHeader.substring(7); // Remove 'Bearer ' prefix
    }
  }

  // If no token, continue without authentication
  if (!token) {
    return next();
  }

  try {
    // Check if token is blacklisted
    const isBlacklisted = await jwtBlacklistService.isTokenBlacklisted(token);
    if (isBlacklisted) {
      return next(); // Continue without authentication for optional auth
    }

    // Verify token
    const decoded = jwt.verify(token, config.JWT_SECRET);

    // Find user by id
    const user = await User.findById(decoded.user.id).select('-password');

    if (user) {
      req.user = user;
    }

    next();
  } catch (err) {
    // If token is invalid, continue without authentication
    next();
  }
};

module.exports = {
  protect,
  authorize,
  optionalAuth
};