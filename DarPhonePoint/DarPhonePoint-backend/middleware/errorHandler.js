const AppError = require('../utils/AppError');
const logger = require('../utils/logger');

/**
 * Global error handling middleware
 * Handles all errors thrown in the application
 */
module.exports = function(err, req, res, next) {
  // Log error details using proper logger
  logger.error('Application Error', {
    message: err.message,
    method: req.method,
    url: req.originalUrl,
    ip: req.ip,
    stack: err.stack,
    statusCode: err.statusCode
  });

  // Set default values
  err.statusCode = err.statusCode || 500;
  err.status = err.status || 'error';

  // Handle specific error types
  if (err.name === 'CastError') {
    // MongoDB invalid ObjectId
    err = new AppError(`Invalid ${err.path}: ${err.value}`, 400);
  } else if (err.code === 11000) {
    // MongoDB duplicate key error
    const field = Object.keys(err.keyValue)[0];
    err = new AppError(`Duplicate field value: ${field}. Please use another value.`, 400);
  } else if (err.name === 'ValidationError') {
    // MongoDB validation error
    const errors = Object.values(err.errors).map(val => val.message);
    err = new AppError(`Invalid input data: ${errors.join(', ')}`, 400);
  } else if (err.name === 'JsonWebTokenError') {
    // JWT validation error
    err = new AppError('Invalid token. Please log in again.', 401);
  } else if (err.name === 'TokenExpiredError') {
    // JWT expired error
    err = new AppError('Your token has expired. Please log in again.', 401);
  }

  // Send error response
  res.status(err.statusCode).json({
    success: false,
    status: err.status,
    message: err.message,
    ...(process.env.NODE_ENV === 'development' && {
      stack: err.stack,
      error: err
    })
  });
};