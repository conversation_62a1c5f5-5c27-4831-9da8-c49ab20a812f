const Joi = require('joi');
const ErrorResponse = require('../utils/errorResponse');

const validateRequest = (schema) => {
  return (req, res, next) => {
    const { error } = schema.validate(req.body, {
      abortEarly: false,
      stripUnknown: true
    });

    if (error) {
      const errorMessage = error.details
        .map((detail) => detail.message)
        .join(', ');
      return next(new ErrorResponse(errorMessage, 400));
    }

    next();
  };
};

// Validation schemas
const schemas = {
  createOrder: Joi.object({
    shipping_address: Joi.object({
      first_name: Joi.string().required(),
      last_name: Joi.string().required(),
      company: Joi.string().optional(),
      address_line_1: Joi.string().required(),
      address_line_2: Joi.string().optional(),
      city: Joi.string().required(),
      state: Joi.string().required(),
      postal_code: Joi.string().required(),
      country: Joi.string().required()
    }).required(),
    billing_address: Joi.object({
      first_name: Joi.string().required(),
      last_name: Joi.string().required(),
      company: Joi.string().optional(),
      address_line_1: Joi.string().required(),
      address_line_2: Joi.string().optional(),
      city: Joi.string().required(),
      state: Joi.string().required(),
      postal_code: Joi.string().required(),
      country: Joi.string().required()
    }).optional(),
    payment_method: Joi.string().valid('credit_card', 'debit_card', 'mobile_money', 'bank_transfer', 'cash_on_delivery', 'whop').default('mobile_money'),
    shipping_method: Joi.string().valid('standard', 'express', 'overnight').default('standard'),
    delivery_instructions: Joi.string().optional(),
    use_cart: Joi.boolean().default(true),
    items: Joi.array().items(
      Joi.object({
        productId: Joi.string().required(),
        variantSku: Joi.string().allow(null).optional(),
        quantity: Joi.number().integer().min(1).required(),
        price: Joi.number().positive().required(),
        imei: Joi.string().optional(),
        condition: Joi.string().optional(),
        warrantyMonths: Joi.number().integer().optional()
      })
    ).optional(),
    is_guest: Joi.boolean().default(false),
    guest_email: Joi.string().email().when('is_guest', {
      is: true,
      then: Joi.required(),
      otherwise: Joi.optional()
    }),
    guest_name: Joi.string().min(2).max(100).when('is_guest', {
      is: true,
      then: Joi.required(),
      otherwise: Joi.optional()
    }),
    cart_items: Joi.array().items(
      Joi.object({
        productId: Joi.string().required(),
        variantSku: Joi.string().allow(null).optional(),
        quantity: Joi.number().integer().min(1).required(),
        price: Joi.number().positive().required(),
        imei: Joi.string().optional(),
        condition: Joi.string().optional(),
        warrantyMonths: Joi.number().integer().optional()
      })
    ).optional()
  }),

  updateOrderStatus: Joi.object({
    status: Joi.string()
      .valid('pending', 'completed', 'failed', 'cancelled')
      .required()
      .messages({
        'any.only': 'Status must be one of: pending, completed, failed, cancelled',
        'any.required': 'Status is required'
      })
  }),

  webhook: Joi.object({
    paymentId: Joi.string().required().messages({
      'string.empty': 'Payment ID is required',
      'any.required': 'Payment ID is required'
    }),
    status: Joi.string()
      .valid('completed', 'failed', 'cancelled')
      .required()
      .messages({
        'any.only': 'Status must be one of: completed, failed, cancelled',
        'any.required': 'Status is required'
      }),
    signature: Joi.string().required().messages({
      'string.empty': 'Signature is required',
      'any.required': 'Signature is required'
    })
  }),

  leadCapture: Joi.object({
    email: Joi.string().email().required().messages({
      'string.email': 'Please provide a valid email address',
      'string.empty': 'Email is required',
      'any.required': 'Email is required'
    }),
    name: Joi.string().min(2).max(100).trim().messages({
      'string.min': 'Name must be at least 2 characters long',
      'string.max': 'Name must not exceed 100 characters'
    }),
    source: Joi.string().max(50).trim().messages({
      'string.max': 'Source must not exceed 50 characters'
    }),
    lead_magnet: Joi.string().max(100).trim().messages({
      'string.max': 'Lead magnet must not exceed 100 characters'
    })
  }),

  // Inventory validation schemas
  createInventory: Joi.object({
    product: Joi.string().required().messages({
      'string.empty': 'Product ID is required',
      'any.required': 'Product ID is required'
    }),
    variant_sku: Joi.string().allow(null, '').messages({
      'string.base': 'Variant SKU must be a string'
    }),
    location: Joi.string().default('main_warehouse').messages({
      'string.base': 'Location must be a string'
    }),
    quantity_on_hand: Joi.number().integer().min(0).required().messages({
      'number.base': 'Quantity on hand must be a number',
      'number.integer': 'Quantity on hand must be an integer',
      'number.min': 'Quantity on hand cannot be negative',
      'any.required': 'Quantity on hand is required'
    }),
    reorder_point: Joi.number().integer().min(0).required().messages({
      'number.base': 'Reorder point must be a number',
      'number.integer': 'Reorder point must be an integer',
      'number.min': 'Reorder point cannot be negative',
      'any.required': 'Reorder point is required'
    }),
    reorder_quantity: Joi.number().integer().min(0).required().messages({
      'number.base': 'Reorder quantity must be a number',
      'number.integer': 'Reorder quantity must be an integer',
      'number.min': 'Reorder quantity cannot be negative',
      'any.required': 'Reorder quantity is required'
    }),
    max_stock_level: Joi.number().integer().min(0).messages({
      'number.base': 'Max stock level must be a number',
      'number.integer': 'Max stock level must be an integer',
      'number.min': 'Max stock level cannot be negative'
    }),
    average_cost: Joi.number().min(0).messages({
      'number.base': 'Average cost must be a number',
      'number.min': 'Average cost cannot be negative'
    }),
    last_cost: Joi.number().min(0).messages({
      'number.base': 'Last cost must be a number',
      'number.min': 'Last cost cannot be negative'
    })
  }),

  inventoryMovement: Joi.object({
    type: Joi.string()
      .valid('purchase', 'sale', 'adjustment', 'return', 'damage', 'transfer')
      .required()
      .messages({
        'any.only': 'Type must be one of: purchase, sale, adjustment, return, damage, transfer',
        'any.required': 'Movement type is required'
      }),
    quantity: Joi.number().required().messages({
      'number.base': 'Quantity must be a number',
      'any.required': 'Quantity is required'
    }),
    reason: Joi.string().required().max(255).messages({
      'string.empty': 'Reason is required',
      'string.max': 'Reason must not exceed 255 characters',
      'any.required': 'Reason is required'
    }),
    reference_id: Joi.string().max(100).messages({
      'string.max': 'Reference ID must not exceed 100 characters'
    }),
    reference_type: Joi.string()
      .valid('order', 'purchase_order', 'manual_adjustment', 'return', 'damage_report')
      .messages({
        'any.only': 'Reference type must be one of: order, purchase_order, manual_adjustment, return, damage_report'
      }),
    notes: Joi.string().max(500).messages({
      'string.max': 'Notes must not exceed 500 characters'
    })
  }),

  reserveInventory: Joi.object({
    items: Joi.array().items(
      Joi.object({
        product: Joi.string().required().messages({
          'string.empty': 'Product ID is required',
          'any.required': 'Product ID is required'
        }),
        variant_sku: Joi.string().allow(null, '').messages({
          'string.base': 'Variant SKU must be a string'
        }),
        quantity: Joi.number().integer().min(1).required().messages({
          'number.base': 'Quantity must be a number',
          'number.integer': 'Quantity must be an integer',
          'number.min': 'Quantity must be at least 1',
          'any.required': 'Quantity is required'
        }),
        location: Joi.string().default('main_warehouse').messages({
          'string.base': 'Location must be a string'
        })
      })
    ).min(1).required().messages({
      'array.min': 'At least one item is required',
      'any.required': 'Items array is required'
    })
  }),

  // Shipping validation schemas
  createShipping: Joi.object({
    order: Joi.string().required().messages({
      'string.empty': 'Order ID is required',
      'any.required': 'Order ID is required'
    }),
    shipping_method: Joi.string()
      .valid('standard', 'express', 'overnight', 'pickup', 'same_day')
      .required()
      .messages({
        'any.only': 'Shipping method must be one of: standard, express, overnight, pickup, same_day',
        'any.required': 'Shipping method is required'
      }),
    carrier: Joi.string()
      .valid('dhl', 'fedex', 'ups', 'tnt', 'local_courier', 'pickup')
      .required()
      .messages({
        'any.only': 'Carrier must be one of: dhl, fedex, ups, tnt, local_courier, pickup',
        'any.required': 'Carrier is required'
      }),
    service_type: Joi.string().messages({
      'string.base': 'Service type must be a string'
    }),
    shipping_address: Joi.object({
      first_name: Joi.string().required().messages({
        'string.empty': 'First name is required',
        'any.required': 'First name is required'
      }),
      last_name: Joi.string().required().messages({
        'string.empty': 'Last name is required',
        'any.required': 'Last name is required'
      }),
      company: Joi.string().allow('').messages({
        'string.base': 'Company must be a string'
      }),
      address_line_1: Joi.string().required().messages({
        'string.empty': 'Address line 1 is required',
        'any.required': 'Address line 1 is required'
      }),
      address_line_2: Joi.string().allow('').messages({
        'string.base': 'Address line 2 must be a string'
      }),
      city: Joi.string().required().messages({
        'string.empty': 'City is required',
        'any.required': 'City is required'
      }),
      state: Joi.string().required().messages({
        'string.empty': 'State is required',
        'any.required': 'State is required'
      }),
      postal_code: Joi.string().required().messages({
        'string.empty': 'Postal code is required',
        'any.required': 'Postal code is required'
      }),
      country: Joi.string().required().messages({
        'string.empty': 'Country is required',
        'any.required': 'Country is required'
      }),
      phone: Joi.string().messages({
        'string.base': 'Phone must be a string'
      })
    }).required().messages({
      'any.required': 'Shipping address is required'
    }),
    billing_address: Joi.object({
      first_name: Joi.string().required(),
      last_name: Joi.string().required(),
      company: Joi.string().allow(''),
      address_line_1: Joi.string().required(),
      address_line_2: Joi.string().allow(''),
      city: Joi.string().required(),
      state: Joi.string().required(),
      postal_code: Joi.string().required(),
      country: Joi.string().required(),
      phone: Joi.string()
    }),
    package_weight: Joi.number().min(0).required().messages({
      'number.base': 'Package weight must be a number',
      'number.min': 'Package weight cannot be negative',
      'any.required': 'Package weight is required'
    }),
    package_dimensions: Joi.object({
      length: Joi.number().min(0),
      width: Joi.number().min(0),
      height: Joi.number().min(0)
    }),
    declared_value: Joi.number().min(0).messages({
      'number.base': 'Declared value must be a number',
      'number.min': 'Declared value cannot be negative'
    }),
    shipping_cost: Joi.number().min(0).required().messages({
      'number.base': 'Shipping cost must be a number',
      'number.min': 'Shipping cost cannot be negative',
      'any.required': 'Shipping cost is required'
    }),
    insurance_cost: Joi.number().min(0).default(0).messages({
      'number.base': 'Insurance cost must be a number',
      'number.min': 'Insurance cost cannot be negative'
    }),
    delivery_instructions: Joi.string().max(500).messages({
      'string.max': 'Delivery instructions must not exceed 500 characters'
    }),
    signature_required: Joi.boolean().default(false).messages({
      'boolean.base': 'Signature required must be a boolean'
    })
  }),

  updateTrackingStatus: Joi.object({
    status: Joi.string()
      .valid('label_created', 'picked_up', 'in_transit', 'out_for_delivery', 'delivered', 'failed_delivery', 'returned')
      .required()
      .messages({
        'any.only': 'Status must be one of: label_created, picked_up, in_transit, out_for_delivery, delivered, failed_delivery, returned',
        'any.required': 'Status is required'
      }),
    description: Joi.string().required().max(255).messages({
      'string.empty': 'Description is required',
      'string.max': 'Description must not exceed 255 characters',
      'any.required': 'Description is required'
    }),
    location: Joi.string().max(100).messages({
      'string.max': 'Location must not exceed 100 characters'
    }),
    carrier_status: Joi.string().max(100).messages({
      'string.max': 'Carrier status must not exceed 100 characters'
    }),
    notes: Joi.string().max(500).messages({
      'string.max': 'Notes must not exceed 500 characters'
    })
  })
};

module.exports = {
  validateRequest,
  schemas
};