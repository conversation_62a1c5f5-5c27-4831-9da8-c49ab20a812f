const { validateRequest, schemas } = require('./validateRequest');

// Export validation middleware for different operations
module.exports = {
  validateInventoryData: validateRequest(schemas.createInventory),
  validateMovementData: validateRequest(schemas.inventoryMovement),
  validateReserveInventory: validateRequest(schemas.reserveInventory),
  validateShippingData: validateRequest(schemas.createShipping),
  validateTrackingUpdate: validateRequest(schemas.updateTrackingStatus),
  validateOrderData: validateRequest(schemas.createOrder),
  validateOrderStatus: validateRequest(schemas.updateOrderStatus),
  validateLeadCapture: validateRequest(schemas.leadCapture),
  validateWebhook: validateRequest(schemas.webhook)
};
