/**
 * Enhanced Validation Middleware for Phone Point Dar
 * Provides comprehensive input validation with Phone Point Dar specific rules
 */

const { body, param, query, validationResult } = require('express-validator');
const rateLimit = require('express-rate-limit');
const ApiResponse = require('../utils/ApiResponse');
const logger = require('../utils/logger');

/**
 * Phone Point Dar specific validation rules
 */
const phonePointValidation = {
  // Tanzania phone number validation
  tanzaniaPhone: (field = 'phone') => 
    body(field)
      .matches(/^(\+255|0)[67]\d{8}$/)
      .withMessage('Please provide a valid Tanzania phone number (e.g., +255712345678 or 0712345678)'),

  // IMEI validation (15 digits)
  imei: (field = 'imei') =>
    body(field)
      .optional()
      .matches(/^\d{15}$/)
      .withMessage('IMEI must be exactly 15 digits'),

  // Tanzania postal code validation
  postalCode: (field = 'postal_code') =>
    body(field)
      .optional()
      .matches(/^\d{5}$/)
      .withMessage('Tanzania postal code must be 5 digits'),

  // Product category validation for Phone Point Dar
  productCategory: (field = 'category') =>
    body(field)
      .isIn([
        // Mobile & Tablets
        'smartphone', 'tablet', 'feature_phone', 'smartwatch',

        // Computers
        'laptop', 'desktop', 'mini_pc', 'workstation', 'server',

        // Gaming
        'gaming_console', 'gaming_laptop', 'gaming_accessories', 'gaming_chair',

        // Audio/Visual
        'earbuds', 'headphones', 'speaker', 'camera', 'webcam', 'microphone',
        'projector', 'tv', 'monitor',

        // Computer Accessories
        'keyboard', 'mouse', 'mousepad', 'printer', 'scanner',

        // Storage & Memory
        'external_drive', 'ssd', 'hdd', 'usb_drive', 'memory_card', 'ram',

        // Networking
        'router', 'modem', 'wifi_extender', 'network_switch', 'ethernet_cable',

        // Power & Cables
        'charger', 'cable', 'power_bank', 'ups', 'surge_protector', 'adapter',

        // Protection & Cases
        'case', 'screen_protector', 'laptop_bag', 'camera_bag', 'phone_case',

        // Smart Home & IoT
        'smart_bulb', 'smart_plug', 'security_camera', 'smart_speaker',

        // Other
        'repair_tool', 'cleaning_kit', 'mount', 'stand', 'drone', 'other'
      ])
      .withMessage('Invalid product category for Phone Point Dar'),

  // Comprehensive brand validation for all tech products
  techBrand: (field = 'brand') =>
    body(field)
      .isIn([
        // Mobile Brands
        'Apple', 'Samsung', 'Huawei', 'Xiaomi', 'Oppo', 'Vivo',
        'Realme', 'OnePlus', 'Google', 'Sony', 'Nokia', 'Tecno',
        'Infinix', 'Itel', 'Redmi', 'Honor', 'Motorola', 'Nothing',

        // Computer Brands
        'Dell', 'HP', 'Lenovo', 'ASUS', 'Acer', 'MSI', 'Razer',
        'Alienware', 'ThinkPad', 'Surface', 'MacBook', 'iMac',

        // Gaming Brands
        'PlayStation', 'Xbox', 'Nintendo', 'Steam', 'ROG', 'Predator',

        // Audio Brands
        'Bose', 'JBL', 'Beats', 'Sennheiser', 'Audio-Technica', 'Skullcandy',
        'Anker', 'Jabra', 'Plantronics', 'HyperX',

        // Camera Brands
        'Canon', 'Nikon', 'Sony', 'Fujifilm', 'Olympus', 'Panasonic',
        'GoPro', 'DJI', 'Insta360',

        // Networking Brands
        'TP-Link', 'Netgear', 'Linksys', 'D-Link', 'Ubiquiti', 'Cisco',
        'ASUS', 'Tenda', 'Mercusys',

        // Storage Brands
        'SanDisk', 'Western Digital', 'Seagate', 'Kingston', 'Crucial',
        'Samsung', 'Transcend', 'Lexar',

        // Other Tech Brands
        'Logitech', 'Corsair', 'Cooler Master', 'Thermaltake', 'NZXT',
        'Belkin', 'Anker', 'Baseus', 'Ugreen', 'Generic', 'Other'
      ])
      .withMessage('Invalid brand for tech products'),

  // Storage capacity validation
  storageCapacity: (field = 'storage') =>
    body(field)
      .optional()
      .isIn(['16GB', '32GB', '64GB', '128GB', '256GB', '512GB', '1TB'])
      .withMessage('Invalid storage capacity'),

  // Memory (RAM) validation
  memoryCapacity: (field = 'memory') =>
    body(field)
      .optional()
      .isIn(['1GB', '2GB', '3GB', '4GB', '6GB', '8GB', '12GB', '16GB'])
      .withMessage('Invalid memory capacity'),

  // Tanzania Shilling price validation
  tzsPrice: (field = 'price') =>
    body(field)
      .isFloat({ min: 1000, max: ******** })
      .withMessage('Price must be between TZS 1,000 and TZS 10,000,000'),

  // Order status validation
  orderStatus: (field = 'order_status') =>
    body(field)
      .isIn(['pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled', 'refunded'])
      .withMessage('Invalid order status'),

  // Payment method validation for Tanzania
  paymentMethod: (field = 'payment_method') =>
    body(field)
      .isIn(['mpesa', 'tigo_pesa', 'airtel_money', 'cash_on_delivery', 'bank_transfer', 'stripe'])
      .withMessage('Invalid payment method for Tanzania market'),

  // Warranty option validation
  warrantyOption: (field = 'warranty_option') =>
    body(field)
      .optional()
      .isIn(['standard', 'extended_1_year', 'extended_2_years', 'premium'])
      .withMessage('Invalid warranty option'),

  // Device condition validation
  deviceCondition: (field = 'condition') =>
    body(field)
      .optional()
      .isIn(['new', 'like_new', 'good', 'fair', 'poor', 'refurbished'])
      .withMessage('Invalid device condition')
};

/**
 * Common validation rules
 */
const commonValidation = {
  // Enhanced email validation
  email: (field = 'email') =>
    body(field)
      .isEmail()
      .normalizeEmail()
      .isLength({ max: 254 })
      .withMessage('Please provide a valid email address'),

  // Strong password validation
  password: (field = 'password') =>
    body(field)
      .isLength({ min: 8, max: 128 })
      .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
      .withMessage('Password must be 8-128 characters with uppercase, lowercase, number, and special character'),

  // Name validation
  name: (field = 'name') =>
    body(field)
      .trim()
      .isLength({ min: 2, max: 100 })
      .matches(/^[a-zA-Z\s\-'\.]+$/)
      .withMessage('Name must be 2-100 characters and contain only letters, spaces, hyphens, apostrophes, and periods'),

  // MongoDB ObjectId validation
  mongoId: (field = 'id') =>
    param(field)
      .isMongoId()
      .withMessage('Invalid ID format'),

  // Pagination validation
  pagination: () => [
    query('page')
      .optional()
      .isInt({ min: 1, max: 1000 })
      .withMessage('Page must be between 1 and 1000'),
    query('limit')
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage('Limit must be between 1 and 100')
  ],

  // Search query validation
  searchQuery: (field = 'search') =>
    query(field)
      .optional()
      .trim()
      .isLength({ min: 1, max: 100 })
      .escape()
      .withMessage('Search query must be 1-100 characters'),

  // URL slug validation
  slug: (field = 'slug') =>
    body(field)
      .optional()
      .matches(/^[a-z0-9-]+$/)
      .isLength({ min: 1, max: 100 })
      .withMessage('Slug must contain only lowercase letters, numbers, and hyphens'),

  // Quantity validation
  quantity: (field = 'quantity') =>
    body(field)
      .isInt({ min: 0, max: 10000 })
      .withMessage('Quantity must be between 0 and 10,000'),

  // SKU validation
  sku: (field = 'sku') =>
    body(field)
      .matches(/^[A-Z0-9-]{3,20}$/)
      .withMessage('SKU must be 3-20 characters with uppercase letters, numbers, and hyphens')
};

/**
 * Validation middleware factory
 */
const createValidationMiddleware = (validations) => {
  return [
    ...validations,
    (req, res, next) => {
      const errors = validationResult(req);
      
      if (!errors.isEmpty()) {
        const formattedErrors = errors.array().map(error => ({
          field: error.path || error.param,
          message: error.msg,
          value: error.value
        }));

        logger.warn('Validation failed:', {
          endpoint: req.originalUrl,
          method: req.method,
          errors: formattedErrors,
          ip: req.ip,
          userAgent: req.get('User-Agent')
        });

        return res.status(400).json(
          ApiResponse.validationError(formattedErrors, 'Validation failed')
        );
      }
      
      next();
    }
  ];
};

/**
 * Dynamic rate limiting based on user type and endpoint
 */
const createDynamicRateLimit = (baseConfig) => {
  return rateLimit({
    windowMs: baseConfig.windowMs || 15 * 60 * 1000, // 15 minutes
    max: (req) => {
      // Admin users get higher limits
      if (req.user?.role === 'admin') {
        return (baseConfig.max || 100) * 5;
      }
      
      // Premium users get higher limits
      if (req.user?.user_type === 'premium') {
        return (baseConfig.max || 100) * 2;
      }
      
      // Regular authenticated users
      if (req.user) {
        return (baseConfig.max || 100) * 1.5;
      }
      
      // Guest users get base limit
      return baseConfig.max || 100;
    },
    keyGenerator: (req) => {
      // Use user ID for authenticated users, IP for guests
      return req.user ? `user:${req.user.id}` : `ip:${req.ip}`;
    },
    standardHeaders: true,
    legacyHeaders: false,
    message: (req, res) => {
      return ApiResponse.error(
        'Too many requests. Please try again later.',
        429,
        [],
        {
          retryAfter: Math.ceil(baseConfig.windowMs / 1000),
          limit: res.getHeader('X-RateLimit-Limit'),
          remaining: res.getHeader('X-RateLimit-Remaining')
        }
      );
    },
    onLimitReached: (req, res, options) => {
      logger.warn('Rate limit exceeded:', {
        ip: req.ip,
        userId: req.user?.id,
        endpoint: req.originalUrl,
        method: req.method,
        userAgent: req.get('User-Agent')
      });
    }
  });
};

/**
 * Specific validation sets for Phone Point Dar endpoints
 */
const validationSets = {
  // Product validation
  createProduct: createValidationMiddleware([
    commonValidation.name(),
    phonePointValidation.productCategory(),
    phonePointValidation.techBrand(),
    phonePointValidation.tzsPrice(),
    commonValidation.sku(),
    phonePointValidation.storageCapacity(),
    phonePointValidation.memoryCapacity(),
    phonePointValidation.deviceCondition(),
    body('description').optional().trim().isLength({ max: 2000 }).escape(),
    body('short_description').optional().trim().isLength({ max: 500 }).escape(),
    body('model').optional().trim().isLength({ max: 100 }).escape(),
    body('subcategory').optional().trim().isLength({ max: 50 }).escape()
  ]),

  // Order validation
  createOrder: createValidationMiddleware([
    commonValidation.email('customer_email'),
    commonValidation.name('customer_name'),
    phonePointValidation.tanzaniaPhone('customer_phone'),
    phonePointValidation.paymentMethod(),
    phonePointValidation.orderStatus().optional(),
    body('shipping_address.city').trim().isLength({ min: 2, max: 50 }).escape(),
    body('shipping_address.address_line_1').trim().isLength({ min: 5, max: 200 }).escape(),
    phonePointValidation.postalCode('shipping_address.postal_code')
  ]),

  // User registration validation
  registerUser: createValidationMiddleware([
    commonValidation.name(),
    commonValidation.email(),
    commonValidation.password(),
    phonePointValidation.tanzaniaPhone(),
    body('date_of_birth').optional().isISO8601().toDate()
  ]),

  // Inventory validation
  updateInventory: createValidationMiddleware([
    commonValidation.mongoId('productId'),
    commonValidation.quantity(),
    phonePointValidation.imei().optional(),
    phonePointValidation.deviceCondition(),
    body('location').optional().trim().isLength({ max: 100 }).escape()
  ]),

  // Search validation
  searchProducts: createValidationMiddleware([
    commonValidation.searchQuery(),
    phonePointValidation.productCategory().optional(),
    phonePointValidation.techBrand().optional(),
    ...commonValidation.pagination(),
    query('minPrice').optional().isFloat({ min: 0 }),
    query('maxPrice').optional().isFloat({ min: 0 })
  ])
};

/**
 * Rate limiting configurations for different endpoints
 */
const rateLimitConfigs = {
  // Authentication endpoints - stricter limits
  auth: createDynamicRateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 5 // 5 attempts per window
  }),

  // API endpoints - moderate limits
  api: createDynamicRateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100 // 100 requests per window
  }),

  // Search endpoints - higher limits
  search: createDynamicRateLimit({
    windowMs: 1 * 60 * 1000, // 1 minute
    max: 30 // 30 searches per minute
  }),

  // File upload endpoints - lower limits
  upload: createDynamicRateLimit({
    windowMs: 60 * 60 * 1000, // 1 hour
    max: 10 // 10 uploads per hour
  })
};

module.exports = {
  phonePointValidation,
  commonValidation,
  createValidationMiddleware,
  createDynamicRateLimit,
  validationSets,
  rateLimitConfigs
};
