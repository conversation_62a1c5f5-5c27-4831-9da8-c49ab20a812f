/**
 * Input Validation Middleware
 * Comprehensive input validation and sanitization middleware
 */

const mongoSanitize = require('express-mongo-sanitize');
const inputSanitizationService = require('../services/inputSanitizationService');
const logger = require('../utils/logger');
const { body, validationResult, param, query } = require('express-validator');

/**
 * MongoDB injection prevention middleware
 */
const preventMongoInjection = mongoSanitize({
  replaceWith: '_',
  onSanitize: ({ req, key }) => {
    logger.warn(`MongoDB injection attempt detected`, {
      ip: req.ip,
      userAgent: req.headers['user-agent'],
      field: key,
      url: req.originalUrl
    });
  }
});

/**
 * XSS prevention middleware
 */
const preventXSS = (req, res, next) => {
  try {
    // Sanitize request body
    if (req.body && typeof req.body === 'object') {
      req.body = sanitizeRequestData(req.body, req.route?.path);
    }

    // Sanitize query parameters
    if (req.query && typeof req.query === 'object') {
      req.query = sanitizeRequestData(req.query, req.route?.path, 'query');
    }

    // Sanitize URL parameters
    if (req.params && typeof req.params === 'object') {
      req.params = sanitizeRequestData(req.params, req.route?.path, 'params');
    }

    next();
  } catch (error) {
    logger.error('XSS prevention middleware error:', error);
    res.status(400).json({
      success: false,
      message: 'Invalid input data'
    });
  }
};

/**
 * Sanitize request data based on context
 */
function sanitizeRequestData(data, routePath, context = 'body') {
  const sanitized = {};

  for (const [key, value] of Object.entries(data)) {
    // Skip if value is null or undefined
    if (value === null || value === undefined) {
      sanitized[key] = value;
      continue;
    }

    // Handle arrays
    if (Array.isArray(value)) {
      sanitized[key] = value.map(item => 
        typeof item === 'string' ? sanitizeBasedOnField(key, item, routePath) : item
      );
      continue;
    }

    // Handle objects (nested)
    if (typeof value === 'object') {
      sanitized[key] = sanitizeRequestData(value, routePath, context);
      continue;
    }

    // Handle strings
    if (typeof value === 'string') {
      sanitized[key] = sanitizeBasedOnField(key, value, routePath);
      continue;
    }

    // Keep other types as-is (numbers, booleans, etc.)
    sanitized[key] = value;
  }

  return sanitized;
}

/**
 * Sanitize based on field name and route context
 */
function sanitizeBasedOnField(fieldName, value, routePath) {
  // Email fields
  if (fieldName.toLowerCase().includes('email')) {
    return inputSanitizationService.sanitizeEmail(value);
  }

  // URL fields
  if (fieldName.toLowerCase().includes('url') || fieldName.toLowerCase().includes('link')) {
    return inputSanitizationService.sanitizeUrl(value);
  }

  // Phone fields
  if (fieldName.toLowerCase().includes('phone') || fieldName.toLowerCase().includes('mobile')) {
    return inputSanitizationService.sanitizePhone(value);
  }

  // HTML content fields (allow basic formatting)
  if (fieldName.toLowerCase().includes('description') || 
      fieldName.toLowerCase().includes('content') ||
      fieldName.toLowerCase().includes('message')) {
    return inputSanitizationService.sanitizeHtml(value, true);
  }

  // Price/amount fields
  if (fieldName.toLowerCase().includes('price') || 
      fieldName.toLowerCase().includes('amount') ||
      fieldName.toLowerCase().includes('cost')) {
    const num = inputSanitizationService.sanitizeNumber(value, { 
      min: 0, 
      allowFloat: true, 
      allowNegative: false 
    });
    return num !== null ? num : 0;
  }

  // Default text sanitization
  return inputSanitizationService.sanitizeText(value, {
    maxLength: getMaxLengthForField(fieldName),
    allowSpecialChars: true,
    trim: true
  });
}

/**
 * Get maximum length for specific fields
 */
function getMaxLengthForField(fieldName) {
  const fieldLimits = {
    name: 100,
    title: 200,
    description: 2000,
    message: 1000,
    address: 500,
    city: 100,
    country: 100,
    password: 128,
    token: 500
  };

  for (const [field, limit] of Object.entries(fieldLimits)) {
    if (fieldName.toLowerCase().includes(field)) {
      return limit;
    }
  }

  return 500; // Default limit
}

/**
 * CSRF protection middleware
 */
const csrfProtection = (req, res, next) => {
  // Skip CSRF for GET requests and API endpoints with valid JWT
  if (req.method === 'GET' || req.user) {
    return next();
  }

  const token = req.headers['x-csrf-token'] || req.body._csrf;
  const sessionToken = req.session?.csrfToken;

  if (!token || !sessionToken || token !== sessionToken) {
    logger.warn('CSRF token validation failed', {
      ip: req.ip,
      userAgent: req.headers['user-agent'],
      url: req.originalUrl,
      hasToken: !!token,
      hasSessionToken: !!sessionToken
    });

    return res.status(403).json({
      success: false,
      message: 'Invalid CSRF token'
    });
  }

  next();
};

/**
 * Request size limiter
 */
const requestSizeLimiter = (maxSize = '10mb') => {
  return (req, res, next) => {
    const contentLength = parseInt(req.headers['content-length'] || '0');
    const maxBytes = parseSize(maxSize);

    if (contentLength > maxBytes) {
      logger.warn('Request size limit exceeded', {
        ip: req.ip,
        contentLength,
        maxSize,
        url: req.originalUrl
      });

      return res.status(413).json({
        success: false,
        message: 'Request entity too large'
      });
    }

    next();
  };
};

/**
 * Parse size string to bytes
 */
function parseSize(size) {
  const units = {
    b: 1,
    kb: 1024,
    mb: 1024 * 1024,
    gb: 1024 * 1024 * 1024
  };

  const match = size.toString().toLowerCase().match(/^(\d+(?:\.\d+)?)\s*(b|kb|mb|gb)?$/);
  if (!match) return 0;

  const value = parseFloat(match[1]);
  const unit = match[2] || 'b';

  return Math.floor(value * units[unit]);
}

/**
 * Validation result handler
 */
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  
  if (!errors.isEmpty()) {
    const errorMessages = errors.array().map(error => ({
      field: error.param,
      message: error.msg,
      value: error.value
    }));

    logger.warn('Validation errors', {
      ip: req.ip,
      url: req.originalUrl,
      errors: errorMessages
    });

    return res.status(400).json({
      success: false,
      message: 'Validation failed',
      errors: errorMessages
    });
  }

  next();
};

/**
 * Suspicious activity detector
 */
const detectSuspiciousActivity = (req, res, next) => {
  const suspiciousPatterns = [
    // SQL injection patterns
    /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)/gi,
    // Script injection patterns
    /<script[^>]*>.*?<\/script>/gi,
    // Path traversal patterns
    /\.\.[\/\\]/g,
    // Command injection patterns
    /[;&|`$(){}[\]]/g
  ];

  const checkData = (data, path = '') => {
    if (typeof data === 'string') {
      for (const pattern of suspiciousPatterns) {
        if (pattern.test(data)) {
          logger.error('Suspicious activity detected', {
            ip: req.ip,
            userAgent: req.headers['user-agent'],
            url: req.originalUrl,
            field: path,
            pattern: pattern.toString(),
            value: data.substring(0, 100) // Log first 100 chars
          });

          return res.status(400).json({
            success: false,
            message: 'Invalid input detected'
          });
        }
      }
    } else if (typeof data === 'object' && data !== null) {
      for (const [key, value] of Object.entries(data)) {
        const result = checkData(value, path ? `${path}.${key}` : key);
        if (result) return result;
      }
    }
  };

  // Check body, query, and params
  checkData(req.body, 'body');
  checkData(req.query, 'query');
  checkData(req.params, 'params');

  next();
};

/**
 * Enhanced validation rules for common endpoints
 */
const validationRules = {
  // User validation
  userRegistration: [
    body('name')
      .trim()
      .isLength({ min: 2, max: 50 })
      .withMessage('Name must be between 2 and 50 characters')
      .matches(/^[a-zA-Z\s]+$/)
      .withMessage('Name can only contain letters and spaces'),

    body('email')
      .isEmail()
      .normalizeEmail()
      .withMessage('Please provide a valid email')
      .isLength({ max: 100 })
      .withMessage('Email must be less than 100 characters'),

    body('password')
      .isLength({ min: 8, max: 128 })
      .withMessage('Password must be between 8 and 128 characters')
      .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
      .withMessage('Password must contain at least one lowercase letter, one uppercase letter, and one number'),

    body('phone')
      .optional()
      .matches(/^[\+]?[\d\s\-\(\)]{7,20}$/)
      .withMessage('Please provide a valid phone number')
  ],

  userLogin: [
    body('email')
      .isEmail()
      .normalizeEmail()
      .withMessage('Please provide a valid email'),

    body('password')
      .notEmpty()
      .withMessage('Password is required')
      .isLength({ max: 128 })
      .withMessage('Password too long')
  ],

  // Product validation
  productCreation: [
    body('name')
      .trim()
      .isLength({ min: 2, max: 200 })
      .withMessage('Product name must be between 2 and 200 characters'),

    body('description')
      .optional()
      .trim()
      .isLength({ max: 2000 })
      .withMessage('Description must be less than 2000 characters'),

    body('price')
      .isFloat({ min: 0 })
      .withMessage('Price must be a positive number'),

    body('category')
      .trim()
      .isLength({ min: 2, max: 50 })
      .withMessage('Category must be between 2 and 50 characters'),

    body('imei')
      .optional()
      .matches(/^\d{15}$/)
      .withMessage('IMEI must be exactly 15 digits')
  ],

  // Order validation
  orderCreation: [
    body('items')
      .isArray({ min: 1 })
      .withMessage('Order must contain at least one item'),

    body('items.*.product')
      .isMongoId()
      .withMessage('Invalid product ID'),

    body('items.*.quantity')
      .isInt({ min: 1, max: 100 })
      .withMessage('Quantity must be between 1 and 100'),

    body('shipping_address.street')
      .trim()
      .isLength({ min: 5, max: 200 })
      .withMessage('Street address must be between 5 and 200 characters'),

    body('shipping_address.city')
      .trim()
      .isLength({ min: 2, max: 50 })
      .withMessage('City must be between 2 and 50 characters'),

    body('shipping_address.phone')
      .matches(/^[\+]?[\d\s\-\(\)]{7,20}$/)
      .withMessage('Please provide a valid phone number')
  ],

  // MongoDB ID validation
  mongoId: [
    param('id')
      .isMongoId()
      .withMessage('Invalid ID format')
  ],

  // Pagination validation
  pagination: [
    query('page')
      .optional()
      .isInt({ min: 1, max: 1000 })
      .withMessage('Page must be between 1 and 1000'),

    query('limit')
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage('Limit must be between 1 and 100')
  ]
};

/**
 * Enhanced XSS detection using the sanitization service
 */
const enhancedXSSProtection = (req, res, next) => {
  try {
    // Check for XSS in request body
    if (req.body && typeof req.body === 'object') {
      for (const [key, value] of Object.entries(req.body)) {
        if (typeof value === 'string' && inputSanitizationService.detectXSS(value)) {
          logger.warn(`XSS attempt detected in body field: ${key}`, {
            ip: req.ip,
            userAgent: req.headers['user-agent'],
            value: value.substring(0, 100),
            url: req.originalUrl
          });

          return res.status(400).json({
            success: false,
            message: 'Invalid input detected',
            field: key
          });
        }
      }
    }

    // Check for XSS in query parameters
    if (req.query && typeof req.query === 'object') {
      for (const [key, value] of Object.entries(req.query)) {
        if (typeof value === 'string' && inputSanitizationService.detectXSS(value)) {
          logger.warn(`XSS attempt detected in query parameter: ${key}`, {
            ip: req.ip,
            userAgent: req.headers['user-agent'],
            value: value.substring(0, 100),
            url: req.originalUrl
          });

          return res.status(400).json({
            success: false,
            message: 'Invalid query parameter',
            field: key
          });
        }
      }
    }

    next();
  } catch (error) {
    logger.error('Enhanced XSS protection error:', error);
    next();
  }
};

module.exports = {
  preventMongoInjection,
  preventXSS,
  enhancedXSSProtection,
  csrfProtection,
  requestSizeLimiter,
  handleValidationErrors,
  detectSuspiciousActivity,
  sanitizeRequestData,
  validationRules,
  inputSanitizationService
};
