const monitoringService = require('../services/monitoringService');

const performanceMiddleware = (req, res, next) => {
  const start = process.hrtime();

  // Track response
  res.on('finish', () => {
    const [seconds, nanoseconds] = process.hrtime(start);
    const responseTime = seconds * 1000 + nanoseconds / 1000000; // Convert to milliseconds

    monitoringService.trackRequest(
      `${req.method} ${req.originalUrl}`,
      res.statusCode,
      responseTime
    );
  });

  next();
};

module.exports = performanceMiddleware; 