const { v4: uuidv4 } = require('uuid');
const logger = require('../utils/logger');

/**
 * Add correlation ID to requests for tracking
 */
const addCorrelationId = (req, res, next) => {
  // Generate or use existing correlation ID
  req.correlationId = req.headers['x-correlation-id'] || uuidv4();
  
  // Add correlation ID to response headers
  res.setHeader('x-correlation-id', req.correlationId);
  
  // Add to logger context
  req.logger = logger.child({ correlationId: req.correlationId });
  
  next();
};

/**
 * Log request performance metrics
 */
const logRequestPerformance = (req, res, next) => {
  const startTime = Date.now();
  
  // Override res.end to capture response time
  const originalEnd = res.end;
  res.end = function(...args) {
    const duration = Date.now() - startTime;
    
    // Log request details
    const logData = {
      method: req.method,
      url: req.originalUrl,
      statusCode: res.statusCode,
      duration: `${duration}ms`,
      userAgent: req.get('User-Agent'),
      ip: req.ip,
      correlationId: req.correlationId
    };
    
    // Add user info if authenticated
    if (req.user) {
      logData.userId = req.user._id;
      logData.userRole = req.user.role;
    }
    
    // Log based on status code
    if (res.statusCode >= 500) {
      logger.error('Request failed:', logData);
    } else if (res.statusCode >= 400) {
      logger.warn('Request error:', logData);
    } else if (duration > 1000) {
      logger.warn('Slow request:', logData);
    } else {
      logger.info('Request completed:', logData);
    }
    
    // Call original end method
    originalEnd.apply(this, args);
  };
  
  next();
};

/**
 * Log error context for debugging
 */
const logErrorContext = (err, req, res, next) => {
  if (err) {
    const errorContext = {
      error: err.message,
      stack: err.stack,
      method: req.method,
      url: req.originalUrl,
      body: req.body,
      params: req.params,
      query: req.query,
      headers: req.headers,
      correlationId: req.correlationId,
      timestamp: new Date().toISOString()
    };
    
    // Add user context if available
    if (req.user) {
      errorContext.user = {
        id: req.user._id,
        email: req.user.email,
        role: req.user.role
      };
    }
    
    logger.error('Request error context:', errorContext);
  }
  
  next(err);
};

/**
 * Log business events for analytics
 */
const logBusinessEvent = (eventType, data, req = null) => {
  const eventLog = {
    eventType,
    data,
    timestamp: new Date().toISOString()
  };
  
  // Add request context if available
  if (req) {
    eventLog.correlationId = req.correlationId;
    eventLog.userAgent = req.get('User-Agent');
    eventLog.ip = req.ip;
    
    if (req.user) {
      eventLog.userId = req.user._id;
      eventLog.userRole = req.user.role;
    }
  }
  
  logger.info('Business event:', eventLog);
};

module.exports = {
  addCorrelationId,
  logRequestPerformance,
  logErrorContext,
  logBusinessEvent
};
