const nodemailer = require('nodemailer');
const config = require('../config/config');
const logger = require('../utils/logger');
const emailTemplateService = require('./emailTemplateService');
const EmailTracking = require('../models/EmailTracking');

class EmailSender {
  constructor() {
    this.transporter = null;
    this.initializeTransporter();
  }

  /**
   * Initialize email transporter
   */
  initializeTransporter() {
    try {
      // Check development mode
      const developmentMode = process.env.EMAIL_DEVELOPMENT_MODE === 'true';

      if (developmentMode) {
        logger.info('Email service in development mode - emails will be mocked');
        this.transporter = null;
        return;
      }

      // Check if email configuration is available
      if (!config.EMAIL_HOST || !config.EMAIL_USER || !config.EMAIL_PASSWORD) {
        logger.warn('Email configuration incomplete, using mock mode');
        this.transporter = null;
        return;
      }

      this.transporter = nodemailer.createTransport({
        host: config.EMAIL_HOST,
        port: parseInt(config.EMAIL_PORT),
        secure: config.EMAIL_SECURE, // true for 465, false for other ports
        auth: {
          user: config.EMAIL_USER,
          pass: config.EMAIL_PASSWORD
        },
        tls: {
          rejectUnauthorized: false // Allow self-signed certificates for development
        }
      });

      // Verify connection
      this.transporter.verify((error, success) => {
        if (error) {
          logger.error('Email transporter verification failed:', error);
          logger.error('Email configuration:', {
            host: config.EMAIL_HOST,
            port: config.EMAIL_PORT,
            user: config.EMAIL_USER,
            hasPassword: !!config.EMAIL_PASSWORD
          });
          this.transporter = null;
        } else {
          logger.info('Email transporter ready and verified');
        }
      });
    } catch (error) {
      logger.error('Failed to initialize email transporter:', error);
      this.transporter = null;
    }
  }

  /**
   * Create tracking record
   * @param {Object} data - Tracking data
   * @returns {Promise<Object>} Tracking record
   */
  async createTrackingRecord(data) {
    try {
      const tracking = await EmailTracking.create({
        recipient: data.to,
        subject: data.subject,
        template: data.template,
        status: 'pending',
        metadata: data.metadata || {}
      });
      return tracking;
    } catch (error) {
      logger.error('Failed to create tracking record:', error);
      throw error;
    }
  }

  /**
   * Update tracking record
   * @param {string} trackingId - Tracking ID
   * @param {Object} updates - Updates to apply
   */
  async updateTrackingRecord(trackingId, updates) {
    try {
      await EmailTracking.findByIdAndUpdate(trackingId, updates);
    } catch (error) {
      logger.error('Failed to update tracking record:', error);
    }
  }

  /**
   * Send email directly
   * @param {Object} data - Email data
   * @returns {Promise<Object>} Send result
   */
  async sendEmail(data) {
    const { template, data: templateData, to, subject, metadata } = data;

    // Validate email
    if (!to || !to.includes('@')) {
      throw new Error('Invalid email address');
    }

    // Create tracking record
    const tracking = await this.createTrackingRecord({
      to,
      subject,
      template,
      metadata
    });

    try {
      // If no transporter (mock mode), simulate sending
      if (!this.transporter) {
        logger.info('Mock email sent (no transporter configured)', {
          to,
          subject,
          template
        });

        await this.updateTrackingRecord(tracking._id, {
          messageId: `mock-${Date.now()}`,
          status: 'sent',
          sentAt: new Date()
        });

        return {
          success: true,
          trackingId: tracking._id,
          messageId: `mock-${Date.now()}`,
          mock: true
        };
      }

      // Load templates if not already loaded
      if (emailTemplateService.templates.size === 0) {
        await emailTemplateService.loadTemplates();
      }

      // Render template
      const html = emailTemplateService.render(template, {
        ...templateData,
        trackingUrl: `${config.BASE_URL}/api/email/track/${tracking._id}`,
        currentYear: new Date().getFullYear()
      });

      // Send email
      const info = await this.transporter.sendMail({
        from: config.EMAIL_FROM,
        to,
        subject,
        html
      });

      // Update tracking record
      await this.updateTrackingRecord(tracking._id, {
        messageId: info.messageId,
        status: 'sent',
        sentAt: new Date()
      });

      logger.info('Email sent successfully', {
        to,
        subject,
        template,
        messageId: info.messageId
      });

      return {
        success: true,
        trackingId: tracking._id,
        messageId: info.messageId
      };
    } catch (error) {
      // Update tracking record with error
      await this.updateTrackingRecord(tracking._id, {
        status: 'failed',
        failedAt: new Date(),
        error: error.message
      });

      logger.error('Email sending failed', {
        to,
        subject,
        template,
        error: error.message
      });

      throw error;
    }
  }

  /**
   * Get email statistics
   * @returns {Promise<Object>} Email stats
   */
  async getStats() {
    try {
      const stats = await EmailTracking.aggregate([
        {
          $group: {
            _id: '$status',
            count: { $sum: 1 }
          }
        }
      ]);

      const result = {
        total: 0,
        sent: 0,
        failed: 0,
        pending: 0
      };

      stats.forEach(stat => {
        result[stat._id] = stat.count;
        result.total += stat.count;
      });

      return result;
    } catch (error) {
      logger.error('Failed to get email stats:', error);
      return {
        total: 0,
        sent: 0,
        failed: 0,
        pending: 0
      };
    }
  }
}

module.exports = new EmailSender();
