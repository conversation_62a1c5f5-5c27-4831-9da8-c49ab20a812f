const nodemailer = require('nodemailer');
const config = require('../config/config');
const logger = require('../utils/logger');
const emailTemplateService = require('./emailTemplateService');
const emailQueueService = require('./emailQueueService');
const EmailTracking = require('../models/EmailTracking');
const cacheService = require('./cacheService');

class EmailService {
  constructor() {
    this.transporter = nodemailer.createTransport({
      host: config.EMAIL_HOST,
      port: config.EMAIL_PORT,
      secure: config.EMAIL_SECURE,
      auth: {
        user: config.EMAIL_USER,
        pass: config.EMAIL_PASSWORD
      }
    });

    // Initialize rate limiting cache
    this.rateLimitWindow = 60; // 1 minute
    this.maxEmailsPerWindow = 100;
  }

  /**
   * Check rate limit for recipient
   * @param {string} recipient - Email recipient
   * @returns {Promise<boolean>} - Whether rate limit is exceeded
   */
  async checkRateLimit(recipient) {
    const key = `email_rate_limit:${recipient}`;
    const count = await cacheService.incr(key, this.rateLimitWindow);
    return count <= this.maxEmailsPerWindow;
  }

  /**
   * Create tracking record
   * @param {Object} data - Email data
   * @returns {Promise<Object>} - Tracking record
   */
  async createTrackingRecord(data) {
    const tracking = new EmailTracking({
      recipient: data.to,
      subject: data.subject,
      template: data.template,
      metadata: data.metadata || {}
    });

    await tracking.save();
    return tracking;
  }

  /**
   * Update tracking record
   * @param {string} trackingId - Tracking record ID
   * @param {Object} update - Update data
   * @returns {Promise<Object>} - Updated tracking record
   */
  async updateTrackingRecord(trackingId, update) {
    const tracking = await EmailTracking.findById(trackingId);
    if (!tracking) {
      throw new Error('Tracking record not found');
    }

    Object.assign(tracking, update);
    await tracking.save();
    return tracking;
  }

  /**
   * Send email
   * @param {Object} data - Email data
   * @returns {Promise<Object>} - Send result
   */
  async sendEmail(data) {
    const { template, data: templateData, to, subject, metadata } = data;

    // Validate email
    if (!to || !to.includes('@')) {
      throw new Error('Invalid email address');
    }

    // Check rate limit
    const isRateLimited = await this.checkRateLimit(to);
    if (!isRateLimited) {
      throw new Error('Rate limit exceeded');
    }

    // Create tracking record
    const tracking = await this.createTrackingRecord({
      to,
      subject,
      template,
      metadata
    });

    try {
      // Render template
      const html = emailTemplateService.render(template, {
        ...templateData,
        trackingUrl: `${config.BASE_URL}/api/email/track/${tracking._id}`
      });

      // Send email
      const info = await this.transporter.sendMail({
        from: config.EMAIL_FROM,
        to,
        subject,
        html
      });

      // Update tracking record
      await this.updateTrackingRecord(tracking._id, {
        messageId: info.messageId,
        status: 'sent',
        sentAt: new Date()
      });

      return {
        success: true,
        trackingId: tracking._id,
        messageId: info.messageId
      };
    } catch (error) {
      // Update tracking record with error
      await this.updateTrackingRecord(tracking._id, {
        status: 'failed',
        failedAt: new Date(),
        error: error.message
      });

      throw error;
    }
  }

  /**
   * Queue email for sending
   * @param {Object} data - Email data
   * @returns {Promise<Object>} - Queue job
   */
  async queueEmail(data) {
    return emailQueueService.addToQueue(data);
  }

  /**
   * Get email statistics
   * @returns {Promise<Object>} - Email stats
   */
  async getStats() {
    const [queueStats, trackingStats] = await Promise.all([
      emailQueueService.getQueueStats(),
      EmailTracking.getStats()
    ]);

    return {
      queue: queueStats,
      tracking: trackingStats
    };
  }
}

module.exports = new EmailService(); 