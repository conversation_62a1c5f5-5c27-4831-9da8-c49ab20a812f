/**
 * Order Notification Service for Phone Point Dar
 * Orchestrates email, SMS, and WhatsApp notifications for order events
 */

const emailService = require('./emailService');
const smsService = require('./smsService');
const whatsappService = require('./whatsappService');
const auditLogService = require('./auditLogService');
const logger = require('../utils/logger');

class OrderNotificationService {
  constructor() {
    this.channels = {
      email: process.env.EMAIL_NOTIFICATIONS_ENABLED === 'true',
      sms: process.env.SMS_NOTIFICATIONS_ENABLED === 'true',
      whatsapp: process.env.WHATSAPP_NOTIFICATIONS_ENABLED === 'true'
    };
  }

  /**
   * Send notifications for order confirmation
   * @param {Object} order - Order object
   * @param {Object} user - User object
   * @returns {Promise<Object>} Notification results
   */
  async sendOrderConfirmation(order, user) {
    const results = {
      email: null,
      sms: null,
      whatsapp: null,
      success: false
    };

    try {
      logger.info('Sending order confirmation notifications:', {
        orderId: order._id,
        orderNumber: order.order_number,
        userId: user._id,
        userEmail: user.email,
        userPhone: user.phone
      });

      // Send email notification
      if (this.channels.email && user.email) {
        try {
          await emailService.sendEmail({
            template: 'order_confirmation',
            to: user.email,
            subject: `Oda Yako Imepokewa - ${order.order_number} | Phone Point Dar`,
            data: {
              customer_name: user.name,
              order_number: order.order_number,
              order_total: order.total,
              order_items: order.items,
              payment_method: order.payment_method,
              shipping_address: order.shipping_address,
              estimated_delivery: this.calculateEstimatedDelivery(order),
              order_date: order.createdAt
            }
          });
          results.email = { success: true };
          logger.info('Order confirmation email sent successfully');
        } catch (error) {
          results.email = { success: false, error: error.message };
          logger.error('Order confirmation email failed:', error);
        }
      }

      // Send SMS notification
      if (this.channels.sms && user.phone) {
        try {
          const smsResult = await smsService.sendOrderConfirmationSMS(order, user);
          results.sms = smsResult;
          if (smsResult.success) {
            logger.info('Order confirmation SMS sent successfully');
          }
        } catch (error) {
          results.sms = { success: false, error: error.message };
          logger.error('Order confirmation SMS failed:', error);
        }
      }

      // Send WhatsApp notification
      if (this.channels.whatsapp && user.phone) {
        try {
          const whatsappResult = await whatsappService.sendOrderConfirmation(order, user);
          results.whatsapp = whatsappResult;
          if (whatsappResult.success) {
            logger.info('Order confirmation WhatsApp sent successfully');
          }
        } catch (error) {
          results.whatsapp = { success: false, error: error.message };
          logger.error('Order confirmation WhatsApp failed:', error);
        }
      }

      // Mark as successful if at least one channel succeeded
      results.success = results.email?.success || results.sms?.success || results.whatsapp?.success;

      // Log notification attempt
      await auditLogService.logNotification(
        user._id,
        'order_confirmation',
        {
          order_id: order._id,
          order_number: order.order_number,
          channels_attempted: Object.keys(this.channels).filter(ch => this.channels[ch]),
          results
        }
      );

      return results;
    } catch (error) {
      logger.error('Order confirmation notification failed:', error);
      throw error;
    }
  }

  /**
   * Send notifications for order status update
   * @param {Object} order - Order object
   * @param {Object} user - User object
   * @param {string} previousStatus - Previous order status
   * @returns {Promise<Object>} Notification results
   */
  async sendOrderStatusUpdate(order, user, previousStatus) {
    const results = {
      email: null,
      sms: null,
      whatsapp: null,
      success: false
    };

    try {
      logger.info('Sending order status update notifications:', {
        orderId: order._id,
        orderNumber: order.order_number,
        previousStatus,
        newStatus: order.order_status
      });

      // Determine notification type based on status
      let notificationType = 'order_status_update';
      if (order.order_status === 'shipped') {
        notificationType = 'order_shipped';
      } else if (order.order_status === 'delivered') {
        notificationType = 'order_delivered';
      } else if (order.order_status === 'cancelled') {
        notificationType = 'order_cancelled';
      }

      // Send email notification
      if (this.channels.email && user.email) {
        try {
          await emailService.sendEmail({
            template: notificationType,
            to: user.email,
            subject: this.getEmailSubject(order.order_status, order.order_number),
            data: {
              customer_name: user.name,
              order_number: order.order_number,
              order_status: order.order_status,
              previous_status: previousStatus,
              order_total: order.total,
              tracking_number: order.tracking_number,
              estimated_delivery: this.calculateEstimatedDelivery(order),
              order_items: order.items
            }
          });
          results.email = { success: true };
        } catch (error) {
          results.email = { success: false, error: error.message };
          logger.error('Order status email failed:', error);
        }
      }

      // Send SMS notification
      if (this.channels.sms && user.phone) {
        try {
          let smsResult;
          if (order.order_status === 'shipped') {
            smsResult = await smsService.sendOrderShippedSMS(order, user);
          } else if (order.order_status === 'delivered') {
            smsResult = await smsService.sendOrderDeliveredSMS(order, user);
          }
          
          if (smsResult) {
            results.sms = smsResult;
          }
        } catch (error) {
          results.sms = { success: false, error: error.message };
          logger.error('Order status SMS failed:', error);
        }
      }

      // Send WhatsApp notification
      if (this.channels.whatsapp && user.phone) {
        try {
          let whatsappResult;
          if (order.order_status === 'shipped') {
            whatsappResult = await whatsappService.sendOrderShipped(order, user);
          } else if (order.order_status === 'delivered') {
            whatsappResult = await whatsappService.sendOrderDelivered(order, user);
          }
          
          if (whatsappResult) {
            results.whatsapp = whatsappResult;
          }
        } catch (error) {
          results.whatsapp = { success: false, error: error.message };
          logger.error('Order status WhatsApp failed:', error);
        }
      }

      results.success = results.email?.success || results.sms?.success || results.whatsapp?.success;

      // Log notification attempt
      await auditLogService.logNotification(
        user._id,
        notificationType,
        {
          order_id: order._id,
          order_number: order.order_number,
          previous_status: previousStatus,
          new_status: order.order_status,
          results
        }
      );

      return results;
    } catch (error) {
      logger.error('Order status update notification failed:', error);
      throw error;
    }
  }

  /**
   * Send payment reminder notifications
   * @param {Object} order - Order object
   * @param {Object} user - User object
   * @returns {Promise<Object>} Notification results
   */
  async sendPaymentReminder(order, user) {
    const results = {
      email: null,
      sms: null,
      whatsapp: null,
      success: false
    };

    try {
      logger.info('Sending payment reminder notifications:', {
        orderId: order._id,
        orderNumber: order.order_number
      });

      // Send email reminder
      if (this.channels.email && user.email) {
        try {
          await emailService.sendEmail({
            template: 'payment_reminder',
            to: user.email,
            subject: `Kumbuka Malipo - Oda ${order.order_number} | Phone Point Dar`,
            data: {
              customer_name: user.name,
              order_number: order.order_number,
              order_total: order.total,
              payment_methods: ['M-Pesa', 'Tigo Pesa', 'Airtel Money'],
              order_items: order.items,
              payment_deadline: this.calculatePaymentDeadline(order)
            }
          });
          results.email = { success: true };
        } catch (error) {
          results.email = { success: false, error: error.message };
          logger.error('Payment reminder email failed:', error);
        }
      }

      // Send SMS reminder
      if (this.channels.sms && user.phone) {
        try {
          const smsResult = await smsService.sendPaymentReminderSMS(order, user);
          results.sms = smsResult;
        } catch (error) {
          results.sms = { success: false, error: error.message };
          logger.error('Payment reminder SMS failed:', error);
        }
      }

      // Send WhatsApp reminder
      if (this.channels.whatsapp && user.phone) {
        try {
          const whatsappResult = await whatsappService.sendPaymentReminder(order, user);
          results.whatsapp = whatsappResult;
        } catch (error) {
          results.whatsapp = { success: false, error: error.message };
          logger.error('Payment reminder WhatsApp failed:', error);
        }
      }

      results.success = results.email?.success || results.sms?.success || results.whatsapp?.success;

      return results;
    } catch (error) {
      logger.error('Payment reminder notification failed:', error);
      throw error;
    }
  }

  /**
   * Send warranty expiry reminder notifications
   * @param {Object} serialNumber - Serial number object
   * @param {Object} user - User object
   * @returns {Promise<Object>} Notification results
   */
  async sendWarrantyExpiryReminder(serialNumber, user) {
    const results = {
      email: null,
      sms: null,
      whatsapp: null,
      success: false
    };

    try {
      const daysLeft = Math.ceil((serialNumber.warrantyEndDate - new Date()) / (1000 * 60 * 60 * 24));

      // Send email reminder
      if (this.channels.email && user.email) {
        try {
          await emailService.sendEmail({
            template: 'warranty_expiry_reminder',
            to: user.email,
            subject: `Warranty Inakaribia Kuisha - IMEI ${serialNumber.imei} | Phone Point Dar`,
            data: {
              customer_name: user.name,
              imei: serialNumber.imei,
              product_name: serialNumber.product?.name,
              days_remaining: daysLeft,
              warranty_end_date: serialNumber.warrantyEndDate,
              contact_info: {
                phone: process.env.BUSINESS_PHONE,
                address: process.env.BUSINESS_ADDRESS
              }
            }
          });
          results.email = { success: true };
        } catch (error) {
          results.email = { success: false, error: error.message };
          logger.error('Warranty reminder email failed:', error);
        }
      }

      // Send SMS reminder
      if (this.channels.sms && user.phone) {
        try {
          const smsResult = await smsService.sendWarrantyExpiryReminderSMS(serialNumber, user);
          results.sms = smsResult;
        } catch (error) {
          results.sms = { success: false, error: error.message };
          logger.error('Warranty reminder SMS failed:', error);
        }
      }

      // Send WhatsApp reminder
      if (this.channels.whatsapp && user.phone) {
        try {
          const whatsappResult = await whatsappService.sendWarrantyReminder(serialNumber, user);
          results.whatsapp = whatsappResult;
        } catch (error) {
          results.whatsapp = { success: false, error: error.message };
          logger.error('Warranty reminder WhatsApp failed:', error);
        }
      }

      results.success = results.email?.success || results.sms?.success || results.whatsapp?.success;

      return results;
    } catch (error) {
      logger.error('Warranty expiry reminder notification failed:', error);
      throw error;
    }
  }

  /**
   * Calculate estimated delivery date
   * @param {Object} order - Order object
   * @returns {Date} Estimated delivery date
   */
  calculateEstimatedDelivery(order) {
    const deliveryDays = order.shipping_method === 'express' ? 1 : 3;
    const estimatedDate = new Date();
    estimatedDate.setDate(estimatedDate.getDate() + deliveryDays);
    return estimatedDate;
  }

  /**
   * Calculate payment deadline
   * @param {Object} order - Order object
   * @returns {Date} Payment deadline
   */
  calculatePaymentDeadline(order) {
    const deadlineDays = 3; // 3 days to complete payment
    const deadline = new Date(order.createdAt);
    deadline.setDate(deadline.getDate() + deadlineDays);
    return deadline;
  }

  /**
   * Get email subject based on order status
   * @param {string} status - Order status
   * @param {string} orderNumber - Order number
   * @returns {string} Email subject
   */
  getEmailSubject(status, orderNumber) {
    const subjects = {
      confirmed: `Oda Yako Imethibitishwa - ${orderNumber} | Phone Point Dar`,
      processing: `Oda Yako Inachakatwa - ${orderNumber} | Phone Point Dar`,
      shipped: `Oda Yako Imetumwa - ${orderNumber} | Phone Point Dar`,
      delivered: `Oda Yako Imefika - ${orderNumber} | Phone Point Dar`,
      cancelled: `Oda Yako Imeghairiwa - ${orderNumber} | Phone Point Dar`
    };
    
    return subjects[status] || `Oda Yako Imebadilika - ${orderNumber} | Phone Point Dar`;
  }

  /**
   * Get notification service status
   * @returns {Object} Service status
   */
  getStatus() {
    return {
      channels: this.channels,
      services: {
        email: emailService.getStatus ? emailService.getStatus() : { enabled: this.channels.email },
        sms: smsService.getStatus(),
        whatsapp: whatsappService.getStatus()
      }
    };
  }
}

module.exports = new OrderNotificationService();
