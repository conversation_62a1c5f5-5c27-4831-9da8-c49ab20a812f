const Product = require('../models/Product');
const SerialNumber = require('../models/SerialNumber');
const Order = require('../models/Order');
const User = require('../models/User');
const Supplier = require('../models/Supplier');

/**
 * Advanced Search Service for Phone Point Dar
 * Provides comprehensive search and filtering capabilities
 */
class AdvancedSearchService {
  
  /**
   * Advanced product search with multiple filters and sorting
   */
  static async searchProducts(searchParams) {
    const {
      query,
      category,
      brand,
      priceMin,
      priceMax,
      inStock,
      condition,
      warranty,
      supplier,
      tags,
      specifications,
      sortBy = 'relevance',
      sortOrder = 'desc',
      page = 1,
      limit = 20,
      includeInactive = false
    } = searchParams;

    // Build base query
    let baseQuery = {};
    
    // Status filter
    if (!includeInactive) {
      baseQuery.status = 'active';
    }

    // Text search
    if (query) {
      baseQuery.$or = [
        { name: { $regex: query, $options: 'i' } },
        { description: { $regex: query, $options: 'i' } },
        { brand: { $regex: query, $options: 'i' } },
        { model: { $regex: query, $options: 'i' } },
        { sku: { $regex: query, $options: 'i' } },
        { tags: { $regex: query, $options: 'i' } }
      ];
    }

    // Category filter
    if (category) {
      if (Array.isArray(category)) {
        baseQuery.category = { $in: category };
      } else {
        baseQuery.category = category;
      }
    }

    // Brand filter
    if (brand) {
      if (Array.isArray(brand)) {
        baseQuery.brand = { $in: brand };
      } else {
        baseQuery.brand = { $regex: brand, $options: 'i' };
      }
    }

    // Price range filter
    if (priceMin !== undefined || priceMax !== undefined) {
      baseQuery.price = {};
      if (priceMin !== undefined) baseQuery.price.$gte = priceMin;
      if (priceMax !== undefined) baseQuery.price.$lte = priceMax;
    }

    // Stock filter
    if (inStock !== undefined) {
      if (inStock) {
        baseQuery.stock_quantity = { $gt: 0 };
      } else {
        baseQuery.stock_quantity = { $lte: 0 };
      }
    }

    // Condition filter
    if (condition) {
      baseQuery.condition = condition;
    }

    // Warranty filter
    if (warranty) {
      baseQuery.warranty_period = { $gte: warranty };
    }

    // Supplier filter
    if (supplier) {
      baseQuery.supplier = supplier;
    }

    // Tags filter
    if (tags && tags.length > 0) {
      baseQuery.tags = { $in: tags };
    }

    // Specifications filter
    if (specifications && Object.keys(specifications).length > 0) {
      Object.keys(specifications).forEach(key => {
        baseQuery[`specifications.${key}`] = specifications[key];
      });
    }

    // Build sort criteria
    let sortCriteria = {};
    switch (sortBy) {
      case 'price_low':
        sortCriteria.price = 1;
        break;
      case 'price_high':
        sortCriteria.price = -1;
        break;
      case 'name':
        sortCriteria.name = sortOrder === 'asc' ? 1 : -1;
        break;
      case 'created':
        sortCriteria.createdAt = sortOrder === 'asc' ? 1 : -1;
        break;
      case 'stock':
        sortCriteria.stock_quantity = sortOrder === 'asc' ? 1 : -1;
        break;
      case 'popularity':
        sortCriteria.sales_count = -1;
        break;
      case 'rating':
        sortCriteria.average_rating = -1;
        break;
      default: // relevance
        if (query) {
          sortCriteria.score = { $meta: 'textScore' };
        } else {
          sortCriteria.createdAt = -1;
        }
    }

    // Execute search with aggregation for better performance
    const pipeline = [
      { $match: baseQuery },
      {
        $lookup: {
          from: 'suppliers',
          localField: 'supplier',
          foreignField: '_id',
          as: 'supplierInfo'
        }
      },
      {
        $addFields: {
          supplierName: { $arrayElemAt: ['$supplierInfo.name', 0] },
          stockStatus: {
            $cond: {
              if: { $eq: ['$stock_quantity', 0] },
              then: 'out_of_stock',
              else: {
                $cond: {
                  if: { $lte: ['$stock_quantity', '$low_stock_threshold'] },
                  then: 'low_stock',
                  else: 'in_stock'
                }
              }
            }
          }
        }
      },
      { $sort: sortCriteria },
      {
        $facet: {
          data: [
            { $skip: (page - 1) * limit },
            { $limit: limit }
          ],
          totalCount: [
            { $count: 'count' }
          ],
          aggregations: [
            {
              $group: {
                _id: null,
                categories: { $addToSet: '$category' },
                brands: { $addToSet: '$brand' },
                priceRange: {
                  $push: {
                    min: { $min: '$price' },
                    max: { $max: '$price' }
                  }
                },
                stockStatuses: { $addToSet: '$stockStatus' }
              }
            }
          ]
        }
      }
    ];

    const results = await Product.aggregate(pipeline);
    
    const data = results[0].data;
    const totalCount = results[0].totalCount[0]?.count || 0;
    const aggregations = results[0].aggregations[0] || {};

    return {
      products: data,
      pagination: {
        page,
        limit,
        total: totalCount,
        pages: Math.ceil(totalCount / limit)
      },
      aggregations: {
        categories: aggregations.categories || [],
        brands: aggregations.brands || [],
        priceRange: {
          min: Math.min(...(aggregations.priceRange?.map(p => p.min) || [0])),
          max: Math.max(...(aggregations.priceRange?.map(p => p.max) || [0]))
        },
        stockStatuses: aggregations.stockStatuses || []
      }
    };
  }

  /**
   * Search serial numbers with advanced filters
   */
  static async searchSerialNumbers(searchParams) {
    const {
      query,
      product,
      status,
      condition,
      supplier,
      warrantyStatus,
      location,
      dateFrom,
      dateTo,
      page = 1,
      limit = 20
    } = searchParams;

    let baseQuery = {};

    // Text search
    if (query) {
      baseQuery.$or = [
        { serialNumber: { $regex: query, $options: 'i' } },
        { imei: { $regex: query, $options: 'i' } },
        { macAddress: { $regex: query, $options: 'i' } }
      ];
    }

    // Product filter
    if (product) {
      baseQuery.product = product;
    }

    // Status filter
    if (status) {
      baseQuery.status = status;
    }

    // Condition filter
    if (condition) {
      baseQuery.condition = condition;
    }

    // Supplier filter
    if (supplier) {
      baseQuery.supplier = supplier;
    }

    // Location filter
    if (location) {
      if (location.warehouse) {
        baseQuery['location.warehouse'] = location.warehouse;
      }
      if (location.shelf) {
        baseQuery['location.shelf'] = location.shelf;
      }
    }

    // Date range filter
    if (dateFrom || dateTo) {
      baseQuery.createdAt = {};
      if (dateFrom) baseQuery.createdAt.$gte = new Date(dateFrom);
      if (dateTo) baseQuery.createdAt.$lte = new Date(dateTo);
    }

    // Warranty status filter
    if (warrantyStatus) {
      const now = new Date();
      switch (warrantyStatus) {
        case 'active':
          baseQuery.warrantyEndDate = { $gt: now };
          break;
        case 'expired':
          baseQuery.warrantyEndDate = { $lte: now };
          break;
        case 'expiring_soon':
          const thirtyDaysFromNow = new Date();
          thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30);
          baseQuery.warrantyEndDate = { $gte: now, $lte: thirtyDaysFromNow };
          break;
      }
    }

    const totalCount = await SerialNumber.countDocuments(baseQuery);
    const serialNumbers = await SerialNumber.find(baseQuery)
      .populate('product', 'name brand model category')
      .populate('supplier', 'name code')
      .populate('customer', 'name email')
      .sort({ createdAt: -1 })
      .skip((page - 1) * limit)
      .limit(limit);

    return {
      serialNumbers,
      pagination: {
        page,
        limit,
        total: totalCount,
        pages: Math.ceil(totalCount / limit)
      }
    };
  }

  /**
   * Advanced order search
   */
  static async searchOrders(searchParams) {
    const {
      query,
      status,
      customer,
      dateFrom,
      dateTo,
      amountMin,
      amountMax,
      paymentMethod,
      shippingMethod,
      page = 1,
      limit = 20
    } = searchParams;

    let baseQuery = {};

    // Text search
    if (query) {
      baseQuery.$or = [
        { orderNumber: { $regex: query, $options: 'i' } },
        { 'customer.name': { $regex: query, $options: 'i' } },
        { 'customer.email': { $regex: query, $options: 'i' } },
        { 'shippingAddress.city': { $regex: query, $options: 'i' } }
      ];
    }

    // Status filter
    if (status) {
      baseQuery.status = status;
    }

    // Customer filter
    if (customer) {
      baseQuery.customer = customer;
    }

    // Date range filter
    if (dateFrom || dateTo) {
      baseQuery.createdAt = {};
      if (dateFrom) baseQuery.createdAt.$gte = new Date(dateFrom);
      if (dateTo) baseQuery.createdAt.$lte = new Date(dateTo);
    }

    // Amount range filter
    if (amountMin !== undefined || amountMax !== undefined) {
      baseQuery.total = {};
      if (amountMin !== undefined) baseQuery.total.$gte = amountMin;
      if (amountMax !== undefined) baseQuery.total.$lte = amountMax;
    }

    // Payment method filter
    if (paymentMethod) {
      baseQuery.paymentMethod = paymentMethod;
    }

    // Shipping method filter
    if (shippingMethod) {
      baseQuery.shippingMethod = shippingMethod;
    }

    const totalCount = await Order.countDocuments(baseQuery);
    const orders = await Order.find(baseQuery)
      .populate('customer', 'name email phone')
      .populate('items.product', 'name brand model category')
      .sort({ createdAt: -1 })
      .skip((page - 1) * limit)
      .limit(limit);

    return {
      orders,
      pagination: {
        page,
        limit,
        total: totalCount,
        pages: Math.ceil(totalCount / limit)
      }
    };
  }

  /**
   * Global search across all entities
   */
  static async globalSearch(query, options = {}) {
    const { limit = 5 } = options;
    
    const results = await Promise.all([
      // Search products
      Product.find({
        $or: [
          { name: { $regex: query, $options: 'i' } },
          { brand: { $regex: query, $options: 'i' } },
          { sku: { $regex: query, $options: 'i' } }
        ],
        status: 'active'
      }).limit(limit).select('name brand price category'),

      // Search customers
      User.find({
        $or: [
          { name: { $regex: query, $options: 'i' } },
          { email: { $regex: query, $options: 'i' } }
        ],
        role: 'customer'
      }).limit(limit).select('name email'),

      // Search orders
      Order.find({
        orderNumber: { $regex: query, $options: 'i' }
      }).limit(limit).select('orderNumber status total createdAt'),

      // Search suppliers
      Supplier.find({
        $or: [
          { name: { $regex: query, $options: 'i' } },
          { code: { $regex: query, $options: 'i' } }
        ],
        status: 'active'
      }).limit(limit).select('name code categories')
    ]);

    return {
      products: results[0],
      customers: results[1],
      orders: results[2],
      suppliers: results[3]
    };
  }
}

module.exports = AdvancedSearchService;
