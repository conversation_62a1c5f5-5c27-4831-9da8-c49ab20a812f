/**
 * Tanzania Payment Service - ClickPesa Integration
 * Handles payment processing for Tanzania-specific payment methods
 * including M-Pesa, Tigo Pesa, Airtel Money, Bank Transfer, and Cash on Delivery
 *
 * ClickPesa API Integration for Phone Point Dar
 * Client ID: IDUSrll2waj3bgl0q9YczUllxAsLSdTF
 * API Documentation: https://docs.clickpesa.com/payment-api/payment-api-overview
 */

const axios = require('axios');
const crypto = require('crypto');
const config = require('../config/config');
const logger = require('../utils/logger');
const AppError = require('../utils/AppError');
const { v4: uuidv4 } = require('uuid');
const mockPaymentService = require('./mockPaymentService');

// ClickPesa Configuration
const CLICKPESA_CONFIG = {
  CLIENT_ID: process.env.CLICKPESA_CLIENT_ID || 'IDUSrll2waj3bgl0q9YczUllxAsLSdTF',
  API_KEY: process.env.CLICKPESA_API_KEY || 'SKeb3hEF8zyvj7srjJXowTInlpxVIPf7 NiN3gwcO8K',
  API_URL: process.env.CLICKPESA_API_URL || 'https://api.clickpesa.com',
  WEBHOOK_SECRET: process.env.CLICKPESA_WEBHOOK_SECRET || 'your_webhook_secret_here',
  BILLPAY_NAMBA: process.env.CLICKPESA_BILLPAY_NAMBA || '1804',
  TIMEOUT: 30000 // 30 seconds
};

// Payment method constants (ClickPesa supported methods)
const PAYMENT_METHODS = {
  MPESA: 'mpesa',
  TIGO_PESA: 'tigo_pesa',
  AIRTEL_MONEY: 'airtel_money',
  BANK_TRANSFER: 'bank_transfer',
  CASH_ON_DELIVERY: 'cash_on_delivery'
};

// Payment status constants
const PAYMENT_STATUS = {
  PENDING: 'pending',
  COMPLETED: 'completed',
  FAILED: 'failed',
  CANCELLED: 'cancelled'
};

/**
 * Initialize payment based on payment method
 * @param {Object} paymentData - Payment data including amount, currency, method, phone, etc.
 * @returns {Promise<Object>} Payment initialization result
 */
exports.initializePayment = async (paymentData) => {
  try {
    // Use mock payment service for testing
    if (mockPaymentService.isInTestMode()) {
      logger.info('Using mock payment service for testing');
      return await mockPaymentService.initializePayment(paymentData);
    }

    const { method, amount, currency, phone, orderId, customerName, customerEmail } = paymentData;

    // Generate unique transaction ID
    const transactionId = `TZ${Date.now()}${Math.floor(Math.random() * 1000)}`;
    
    // Common payment data
    const paymentResult = {
      transaction_id: transactionId,
      status: PAYMENT_STATUS.PENDING,
      amount,
      currency,
      method,
      created_at: new Date(),
      metadata: {
        order_id: orderId,
        customer_name: customerName,
        customer_email: customerEmail
      }
    };
    
    // Process based on payment method
    switch (method) {
      case PAYMENT_METHODS.MPESA:
        return await processMpesaPayment(paymentResult, phone);
      
      case PAYMENT_METHODS.TIGO_PESA:
        return await processTigoPesaPayment(paymentResult, phone);
      
      case PAYMENT_METHODS.AIRTEL_MONEY:
        return await processAirtelMoneyPayment(paymentResult, phone);
      
      case PAYMENT_METHODS.BANK_TRANSFER:
        return await processBankTransferPayment(paymentResult);
      
      case PAYMENT_METHODS.CASH_ON_DELIVERY:
        return await processCashOnDeliveryPayment(paymentResult);
      
      default:
        throw new AppError(`Unsupported payment method: ${method}`, 400);
    }
  } catch (error) {
    logger.error('Payment initialization error:', error);
    throw new AppError('Failed to initialize payment', 500);
  }
};

/**
 * Create ClickPesa API client with authentication
 */
const createClickPesaClient = () => {
  return axios.create({
    baseURL: CLICKPESA_CONFIG.API_URL,
    timeout: CLICKPESA_CONFIG.TIMEOUT,
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${CLICKPESA_CONFIG.API_KEY}`,
      'X-Client-ID': CLICKPESA_CONFIG.CLIENT_ID
    }
  });
};

/**
 * Format Tanzania phone number for ClickPesa (255XXXXXXXXX format)
 * @param {string} phone - Phone number in various formats
 * @returns {string} Formatted phone number
 */
const formatTanzaniaPhone = (phone) => {
  if (!phone) return null;

  // Remove all non-digit characters
  const digits = phone.replace(/\D/g, '');

  // Handle different formats
  if (digits.startsWith('255') && digits.length === 12) {
    return digits; // Already in correct format
  } else if (digits.startsWith('0') && digits.length === 10) {
    return '255' + digits.substring(1); // Remove leading 0 and add 255
  } else if (digits.length === 9) {
    return '255' + digits; // Add 255 prefix
  }

  return null; // Invalid format
};

/**
 * Process M-Pesa payment via ClickPesa
 * @param {Object} paymentResult - Base payment result object
 * @param {String} phone - Customer phone number
 * @returns {Promise<Object>} Updated payment result
 */
const processMpesaPayment = async (paymentResult, phone) => {
  try {
    // Format phone number for Tanzania M-Pesa (255XXXXXXXXX format)
    const formattedPhone = formatTanzaniaPhone(phone);
    if (!formattedPhone) {
      throw new AppError('Invalid Tanzania phone number format', 400);
    }

    const clickPesaClient = createClickPesaClient();

    // ClickPesa M-Pesa STK Push request
    const requestData = {
      client_id: CLICKPESA_CONFIG.CLIENT_ID,
      request_id: paymentResult.transaction_id,
      msisdn: formattedPhone,
      amount: paymentResult.amount,
      currency: paymentResult.currency || 'TZS',
      reference: `PPD-${paymentResult.transaction_id}`,
      description: `Phone Point Dar - Order Payment`,
      callback_url: `${config.BASE_URL}/api/payments/clickpesa/callback`,
      redirect_url: `${config.FRONTEND_URL}/payment/success`,
      cancel_url: `${config.FRONTEND_URL}/payment/cancel`
    };

    logger.info('Initiating ClickPesa M-Pesa payment:', {
      request_id: requestData.request_id,
      amount: requestData.amount,
      msisdn: formattedPhone.substring(0, 6) + 'XXXX' // Log partial phone for privacy
    });

    const response = await clickPesaClient.post('/v1/payments/request', requestData);

    if (response.data && response.data.success) {
      return {
        ...paymentResult,
        provider_reference: response.data.request_id || response.data.transaction_id,
        clickpesa_checkout_url: response.data.checkout_url,
        payment_instructions: {
          message: 'Please check your phone for the M-Pesa payment prompt and enter your PIN to complete the payment.',
          expires_at: new Date(Date.now() + 15 * 60 * 1000), // 15 minutes expiry
          checkout_url: response.data.checkout_url
        },
        raw_response: response.data
      };
    } else {
      throw new AppError('ClickPesa M-Pesa payment initialization failed', 400);
    }
  } catch (error) {
    logger.error('ClickPesa M-Pesa payment processing error:', error);

    if (error.response) {
      logger.error('ClickPesa API Error Response:', error.response.data);
      throw new AppError(`M-Pesa payment failed: ${error.response.data.message || 'Unknown error'}`, 400);
    }

    throw new AppError('Failed to process M-Pesa payment', 500);
  }
};

/**
 * Process Tigo Pesa payment
 * @param {Object} paymentResult - Base payment result object
 * @param {String} phone - Customer phone number
 * @returns {Promise<Object>} Updated payment result
 */
const processTigoPesaPayment = async (paymentResult, phone) => {
  try {
    // Validate phone number format
    const formattedPhone = formatPhoneNumber(phone);
    if (!formattedPhone) {
      throw new AppError('Invalid phone number format', 400);
    }
    
    // For development, simulate successful initialization
    return {
      ...paymentResult,
      provider_reference: `TIGO${Math.floor(Math.random() * **********)}`,
      payment_instructions: {
        message: 'Please check your phone for the Tigo Pesa payment prompt and enter your PIN to complete the payment.',
        expires_at: new Date(Date.now() + 15 * 60 * 1000) // 15 minutes expiry
      }
    };
  } catch (error) {
    logger.error('Tigo Pesa payment processing error:', error);
    throw new AppError('Failed to process Tigo Pesa payment', 500);
  }
};

/**
 * Process Airtel Money payment
 * @param {Object} paymentResult - Base payment result object
 * @param {String} phone - Customer phone number
 * @returns {Promise<Object>} Updated payment result
 */
const processAirtelMoneyPayment = async (paymentResult, phone) => {
  try {
    // Validate phone number format
    const formattedPhone = formatPhoneNumber(phone);
    if (!formattedPhone) {
      throw new AppError('Invalid phone number format', 400);
    }
    
    // For development, simulate successful initialization
    return {
      ...paymentResult,
      provider_reference: `AIRTEL${Math.floor(Math.random() * **********)}`,
      payment_instructions: {
        message: 'Please check your phone for the Airtel Money payment prompt and enter your PIN to complete the payment.',
        expires_at: new Date(Date.now() + 15 * 60 * 1000) // 15 minutes expiry
      }
    };
  } catch (error) {
    logger.error('Airtel Money payment processing error:', error);
    throw new AppError('Failed to process Airtel Money payment', 500);
  }
};

/**
 * Process Bank Transfer payment
 * @param {Object} paymentResult - Base payment result object
 * @returns {Promise<Object>} Updated payment result
 */
const processBankTransferPayment = async (paymentResult) => {
  try {
    // For bank transfers, we provide account details and a reference number
    return {
      ...paymentResult,
      provider_reference: `BT${Math.floor(Math.random() * **********)}`,
      payment_instructions: {
        message: 'Please transfer the exact amount to the following bank account:',
        bank_name: 'CRDB Bank',
        account_name: 'Phone Point Dar Ltd',
        account_number: '01J1070955500',
        reference: paymentResult.transaction_id,
        expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours expiry
      }
    };
  } catch (error) {
    logger.error('Bank transfer processing error:', error);
    throw new AppError('Failed to process bank transfer', 500);
  }
};

/**
 * Process Cash on Delivery payment
 * @param {Object} paymentResult - Base payment result object
 * @returns {Promise<Object>} Updated payment result
 */
const processCashOnDeliveryPayment = async (paymentResult) => {
  try {
    // For COD, we simply mark it as pending until delivery
    return {
      ...paymentResult,
      provider_reference: `COD${Math.floor(Math.random() * **********)}`,
      payment_instructions: {
        message: 'Please have the exact amount ready for the delivery person.',
        notes: 'Payment will be collected upon delivery of your order.'
      }
    };
  } catch (error) {
    logger.error('Cash on delivery processing error:', error);
    throw new AppError('Failed to process cash on delivery', 500);
  }
};

/**
 * Verify payment status
 * @param {String} transactionId - Transaction ID to verify
 * @returns {Promise<Object>} Payment status
 */
exports.verifyPayment = async (transactionId) => {
  try {
    const clickPesaClient = createClickPesaClient();

    logger.info('Verifying ClickPesa payment:', { transaction_id: transactionId });

    const response = await clickPesaClient.get(`/v1/payments/status/${transactionId}`);

    if (response.data) {
      const status = mapClickPesaStatus(response.data.status);

      return {
        transaction_id: transactionId,
        status: status,
        verified_at: new Date(),
        provider_reference: response.data.transaction_id || response.data.request_id,
        amount_verified: response.data.amount_verified || true,
        raw_response: response.data
      };
    } else {
      throw new AppError('Invalid response from ClickPesa verification', 400);
    }
  } catch (error) {
    logger.error('ClickPesa payment verification error:', error);

    if (error.response) {
      logger.error('ClickPesa Verification Error Response:', error.response.data);
      throw new AppError(`Payment verification failed: ${error.response.data.message || 'Unknown error'}`, 400);
    }

    throw new AppError('Failed to verify payment', 500);
  }
};

/**
 * Verify ClickPesa webhook signature
 * @param {Object} payload - Webhook payload
 * @param {String} signature - Webhook signature from headers
 * @returns {Boolean} Whether signature is valid
 */
const verifyWebhookSignature = (payload, signature) => {
  try {
    if (!CLICKPESA_CONFIG.WEBHOOK_SECRET || !signature) {
      logger.warn('Webhook signature verification skipped - missing secret or signature');
      return true; // Allow in development, but log warning
    }

    const crypto = require('crypto');
    const expectedSignature = crypto
      .createHmac('sha256', CLICKPESA_CONFIG.WEBHOOK_SECRET)
      .update(JSON.stringify(payload))
      .digest('hex');

    return signature === expectedSignature || signature === `sha256=${expectedSignature}`;
  } catch (error) {
    logger.error('Webhook signature verification error:', error);
    return false;
  }
};

/**
 * Process webhook from ClickPesa payment provider
 * @param {Object} payload - Webhook payload
 * @param {String} signature - Webhook signature for verification
 * @returns {Promise<Object>} Processed webhook data
 */
exports.processWebhook = async (payload, signature) => {
  try {
    // Use mock payment service for testing
    if (mockPaymentService.isInTestMode()) {
      logger.info('Using mock webhook processing for testing');
      return await mockPaymentService.processWebhook(payload, signature);
    }

    // Verify webhook signature for security
    if (!verifyWebhookSignature(payload, signature)) {
      logger.error('Invalid webhook signature received');
      throw new AppError('Invalid webhook signature', 401);
    }

    const { request_id, transaction_id, status, amount, currency } = payload;

    if (!request_id && !transaction_id) {
      throw new AppError('Invalid webhook payload: missing request_id or transaction_id', 400);
    }

    // Map ClickPesa status to our internal status
    const mappedStatus = mapClickPesaStatus(status);

    logger.info('ClickPesa webhook processed successfully:', {
      request_id,
      transaction_id,
      status: mappedStatus,
      amount,
      currency
    });

    return {
      paymentId: request_id || transaction_id,
      status: mappedStatus,
      amount,
      currency,
      processedAt: new Date(),
      rawPayload: payload
    };
  } catch (error) {
    logger.error('Webhook processing error:', error);
    throw new AppError('Failed to process webhook', 500);
  }
};

/**
 * Format phone number to international format
 * @param {String} phone - Phone number to format
 * @returns {String|null} Formatted phone number or null if invalid
 */
const formatPhoneNumber = (phone) => {
  if (!phone) return null;
  
  // Remove non-numeric characters
  const numericPhone = phone.replace(/\D/g, '');
  
  // Check if it's a valid length
  if (numericPhone.length < 9 || numericPhone.length > 12) {
    return null;
  }
  
  // If it starts with 0, replace with country code
  if (numericPhone.startsWith('0')) {
    return `255${numericPhone.substring(1)}`;
  }
  
  // If it doesn't have country code, add it
  if (numericPhone.length === 9) {
    return `255${numericPhone}`;
  }
  
  // If it already has country code, return as is
  return numericPhone;
};

/**
 * Map ClickPesa payment status to our internal status
 * @param {string} clickPesaStatus - ClickPesa status
 * @returns {string} Internal payment status
 */
const mapClickPesaStatus = (clickPesaStatus) => {
  const statusMap = {
    'PENDING': 'pending',
    'PROCESSING': 'pending',
    'COMPLETED': 'completed',
    'SUCCESS': 'completed',
    'FAILED': 'failed',
    'CANCELLED': 'cancelled',
    'EXPIRED': 'failed'
  };

  return statusMap[clickPesaStatus?.toUpperCase()] || 'pending';
};

// Export constants for use in other modules
exports.PAYMENT_METHODS = PAYMENT_METHODS;
exports.PAYMENT_STATUS = PAYMENT_STATUS;
exports.CLICKPESA_CONFIG = CLICKPESA_CONFIG;
