const cacheService = require('./cacheService');
const logger = require('../utils/logger');

class PaymentCacheService {
  constructor() {
    this.CACHE_KEYS = {
      PAYMENT: 'payment',
      ORDER: 'order',
      USER_PAYMENTS: 'user_payments',
      PRODUCT: 'product'
    };

    this.CACHE_TTL = {
      PAYMENT: 3600, // 1 hour
      ORDER: 7200, // 2 hours
      USER_PAYMENTS: 1800, // 30 minutes
      PRODUCT: 86400 // 24 hours
    };
  }

  /**
   * Get payment details from cache
   * @param {string} paymentId - Payment ID
   * @returns {Promise<Object|null>} Payment details
   */
  async getPayment(paymentId) {
    const key = `${this.CACHE_KEYS.PAYMENT}:${paymentId}`;
    return cacheService.get(key);
  }

  /**
   * Cache payment details
   * @param {string} paymentId - Payment ID
   * @param {Object} paymentData - Payment data
   */
  async setPayment(paymentId, paymentData) {
    const key = `${this.CACHE_KEYS.PAYMENT}:${paymentId}`;
    await cacheService.set(key, paymentData, this.CACHE_TTL.PAYMENT);
  }

  /**
   * Get order details from cache
   * @param {string} orderId - Order ID
   * @returns {Promise<Object|null>} Order details
   */
  async getOrder(orderId) {
    const key = `${this.CACHE_KEYS.ORDER}:${orderId}`;
    return cacheService.get(key);
  }

  /**
   * Cache order details
   * @param {string} orderId - Order ID
   * @param {Object} orderData - Order data
   */
  async setOrder(orderId, orderData) {
    const key = `${this.CACHE_KEYS.ORDER}:${orderId}`;
    await cacheService.set(key, orderData, this.CACHE_TTL.ORDER);
  }

  /**
   * Get user's payment history from cache
   * @param {string} userId - User ID
   * @returns {Promise<Array|null>} Payment history
   */
  async getUserPayments(userId) {
    const key = `${this.CACHE_KEYS.USER_PAYMENTS}:${userId}`;
    return cacheService.get(key);
  }

  /**
   * Cache user's payment history
   * @param {string} userId - User ID
   * @param {Array} payments - Payment history
   */
  async setUserPayments(userId, payments) {
    const key = `${this.CACHE_KEYS.USER_PAYMENTS}:${userId}`;
    await cacheService.set(key, payments, this.CACHE_TTL.USER_PAYMENTS);
  }

  /**
   * Get product details from cache
   * @param {string} productId - Product ID
   * @returns {Promise<Object|null>} Product details
   */
  async getProduct(productId) {
    const key = `${this.CACHE_KEYS.PRODUCT}:${productId}`;
    return cacheService.get(key);
  }

  /**
   * Cache product details
   * @param {string} productId - Product ID
   * @param {Object} productData - Product data
   */
  async setProduct(productId, productData) {
    const key = `${this.CACHE_KEYS.PRODUCT}:${productId}`;
    await cacheService.set(key, productData, this.CACHE_TTL.PRODUCT);
  }

  /**
   * Invalidate payment cache
   * @param {string} paymentId - Payment ID
   */
  async invalidatePayment(paymentId) {
    const key = `${this.CACHE_KEYS.PAYMENT}:${paymentId}`;
    await cacheService.del(key);
  }

  /**
   * Invalidate order cache
   * @param {string} orderId - Order ID
   */
  async invalidateOrder(orderId) {
    const key = `${this.CACHE_KEYS.ORDER}:${orderId}`;
    await cacheService.del(key);
  }

  /**
   * Invalidate user's payment history cache
   * @param {string} userId - User ID
   */
  async invalidateUserPayments(userId) {
    const key = `${this.CACHE_KEYS.USER_PAYMENTS}:${userId}`;
    await cacheService.del(key);
  }

  /**
   * Invalidate product cache
   * @param {string} productId - Product ID
   */
  async invalidateProduct(productId) {
    const key = `${this.CACHE_KEYS.PRODUCT}:${productId}`;
    await cacheService.del(key);
  }

  /**
   * Warm up cache for a product
   * @param {Object} product - Product data
   */
  async warmProductCache(product) {
    try {
      await this.setProduct(product._id, product);
      logger.info('Product cache warmed up:', { productId: product._id });
    } catch (error) {
      logger.error('Product cache warm up failed:', error);
    }
  }

  /**
   * Warm up cache for multiple products
   * @param {Array} products - Array of product data
   */
  async warmProductsCache(products) {
    try {
      const cacheData = products.reduce((acc, product) => {
        acc[`${this.CACHE_KEYS.PRODUCT}:${product._id}`] = product;
        return acc;
      }, {});

      await cacheService.mset(cacheData, this.CACHE_TTL.PRODUCT);
      logger.info('Products cache warmed up:', { count: products.length });
    } catch (error) {
      logger.error('Products cache warm up failed:', error);
    }
  }
}

module.exports = new PaymentCacheService(); 