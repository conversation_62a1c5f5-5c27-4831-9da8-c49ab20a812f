const cacheService = require('./cacheService');
const Product = require('../models/Product');
const Inventory = require('../models/Inventory');
const logger = require('./errorLoggingService').logger;

/**
 * Cache optimization service for Phone Point Dar
 * Implements intelligent caching strategies for better performance
 */
class CacheOptimizationService {
  constructor() {
    this.cacheStats = {
      hits: 0,
      misses: 0,
      warmups: 0,
      invalidations: 0,
      errors: 0
    };
    
    // Cache TTL configurations for different data types
    this.cacheTTL = {
      products: 3600,        // 1 hour - products don't change frequently
      inventory: 300,        // 5 minutes - inventory changes more often
      categories: 7200,      // 2 hours - categories are very stable
      brands: 7200,          // 2 hours - brands are very stable
      featured: 1800,        // 30 minutes - featured products change occasionally
      search: 900,           // 15 minutes - search results can be cached briefly
      analytics: 600         // 10 minutes - analytics data
    };
  }

  /**
   * Intelligent cache warming for Phone Point Dar
   * Prioritizes most accessed and important data
   */
  async warmCriticalCaches() {
    try {
      logger.info('Starting intelligent cache warming for Phone Point Dar...');
      
      const startTime = Date.now();
      let warmedItems = 0;

      // 1. Warm featured products (homepage priority)
      const featuredProducts = await Product.find({ 
        is_featured: true, 
        is_active: true 
      }).limit(10);
      
      for (const product of featuredProducts) {
        await cacheService.cacheProduct(product._id, product, this.cacheTTL.featured);
        warmedItems++;
      }
      logger.info(`Warmed ${featuredProducts.length} featured products`);

      // 2. Warm popular categories
      const popularCategories = ['smartphone', 'earbuds', 'case', 'charger'];
      for (const category of popularCategories) {
        const categoryProducts = await Product.find({ 
          category, 
          is_active: true 
        }).limit(20);
        
        const queryHash = cacheService.generateHash({ category, is_active: true });
        await cacheService.cacheProductList(queryHash, {
          data: categoryProducts,
          total: categoryProducts.length,
          category
        }, this.cacheTTL.categories);
        warmedItems++;
      }
      logger.info(`Warmed ${popularCategories.length} popular categories`);

      // 3. Warm brand data
      const popularBrands = ['Apple', 'Samsung', 'Xiaomi', 'Oppo'];
      for (const brand of popularBrands) {
        const brandProducts = await Product.find({ 
          brand, 
          is_active: true 
        }).limit(15);
        
        const queryHash = cacheService.generateHash({ brand, is_active: true });
        await cacheService.cacheProductList(queryHash, {
          data: brandProducts,
          total: brandProducts.length,
          brand
        }, this.cacheTTL.brands);
        warmedItems++;
      }
      logger.info(`Warmed ${popularBrands.length} popular brands`);

      // 4. Warm inventory for featured products
      for (const product of featuredProducts) {
        const inventory = await Inventory.findOne({ product: product._id });
        if (inventory) {
          await cacheService.cacheInventory(
            product._id.toString(), 
            null, 
            inventory, 
            this.cacheTTL.inventory
          );
          warmedItems++;
        }
      }

      const duration = Date.now() - startTime;
      this.cacheStats.warmups++;
      
      logger.info(`Cache warming completed: ${warmedItems} items in ${duration}ms`);
      return { success: true, items: warmedItems, duration };
      
    } catch (error) {
      this.cacheStats.errors++;
      logger.error('Cache warming failed:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Smart cache invalidation based on data relationships
   * @param {string} type - Type of data changed (product, inventory, etc.)
   * @param {string} id - ID of changed item
   * @param {Object} metadata - Additional metadata for smart invalidation
   */
  async smartInvalidation(type, id, metadata = {}) {
    try {
      this.cacheStats.invalidations++;
      
      switch (type) {
        case 'product':
          await this.invalidateProductCaches(id, metadata);
          break;
        case 'inventory':
          await this.invalidateInventoryCaches(id, metadata);
          break;
        case 'category':
          await this.invalidateCategoryCaches(metadata.category);
          break;
        case 'brand':
          await this.invalidateBrandCaches(metadata.brand);
          break;
        default:
          logger.warn(`Unknown invalidation type: ${type}`);
      }
      
      logger.info(`Smart invalidation completed for ${type}:${id}`);
    } catch (error) {
      this.cacheStats.errors++;
      logger.error('Smart invalidation failed:', error);
    }
  }

  /**
   * Invalidate product-related caches intelligently
   */
  async invalidateProductCaches(productId, metadata) {
    // Direct product cache
    await cacheService.del(`phonepoint:product:${productId}`);
    
    // Product list caches that might contain this product
    if (metadata.category) {
      const categoryKeys = await cacheService.client.keys(`phonepoint:products:*${metadata.category}*`);
      if (categoryKeys.length > 0) {
        await cacheService.client.del(...categoryKeys);
      }
    }
    
    if (metadata.brand) {
      const brandKeys = await cacheService.client.keys(`phonepoint:products:*${metadata.brand}*`);
      if (brandKeys.length > 0) {
        await cacheService.client.del(...brandKeys);
      }
    }
    
    // Featured products cache if this was featured
    if (metadata.is_featured) {
      const featuredKeys = await cacheService.client.keys('phonepoint:products:*featured*');
      if (featuredKeys.length > 0) {
        await cacheService.client.del(...featuredKeys);
      }
    }
  }

  /**
   * Invalidate inventory-related caches
   */
  async invalidateInventoryCaches(productId, metadata) {
    // Direct inventory caches
    const inventoryKeys = await cacheService.client.keys(`phonepoint:inventory:${productId}*`);
    if (inventoryKeys.length > 0) {
      await cacheService.client.del(...inventoryKeys);
    }
    
    // If stock status changed significantly, invalidate product lists
    if (metadata.stockStatusChanged) {
      const productListKeys = await cacheService.client.keys('phonepoint:products:*');
      if (productListKeys.length > 0) {
        await cacheService.client.del(...productListKeys);
      }
    }
  }

  /**
   * Get cache performance statistics
   */
  getCacheStats() {
    const hitRate = this.cacheStats.hits / (this.cacheStats.hits + this.cacheStats.misses) || 0;
    
    return {
      ...this.cacheStats,
      hitRate: Math.round(hitRate * 100) / 100,
      totalRequests: this.cacheStats.hits + this.cacheStats.misses
    };
  }

  /**
   * Track cache hit
   */
  trackCacheHit() {
    this.cacheStats.hits++;
  }

  /**
   * Track cache miss
   */
  trackCacheMiss() {
    this.cacheStats.misses++;
  }

  /**
   * Optimize cache memory usage
   * Remove least recently used items when memory is high
   */
  async optimizeMemoryUsage() {
    try {
      logger.info('Starting cache memory optimization...');
      
      // Get cache memory info
      const memoryInfo = await cacheService.client.memory('usage');
      const memoryUsage = parseInt(memoryInfo);
      
      // If memory usage is high (> 100MB), clean up old entries
      if (memoryUsage > 100 * 1024 * 1024) {
        logger.warn(`High cache memory usage: ${Math.round(memoryUsage / 1024 / 1024)}MB`);
        
        // Remove expired keys
        await this.cleanupExpiredKeys();
        
        // Remove old product list caches (keep only recent ones)
        await this.cleanupOldProductLists();
        
        logger.info('Cache memory optimization completed');
      }
      
    } catch (error) {
      logger.error('Cache memory optimization failed:', error);
    }
  }

  /**
   * Cleanup expired keys manually
   */
  async cleanupExpiredKeys() {
    try {
      // Redis should handle this automatically, but we can force cleanup
      const keys = await cacheService.client.keys('phonepoint:*');
      let cleanedCount = 0;
      
      for (const key of keys) {
        const ttl = await cacheService.client.ttl(key);
        if (ttl === -1) { // No expiration set
          // Set a default expiration for orphaned keys
          await cacheService.client.expire(key, 3600);
        } else if (ttl === -2) { // Key doesn't exist
          cleanedCount++;
        }
      }
      
      if (cleanedCount > 0) {
        logger.info(`Cleaned up ${cleanedCount} expired cache keys`);
      }
    } catch (error) {
      logger.error('Cleanup expired keys failed:', error);
    }
  }

  /**
   * Remove old product list caches to free memory
   */
  async cleanupOldProductLists() {
    try {
      const productListKeys = await cacheService.client.keys('phonepoint:products:*');
      
      // Keep only the 50 most recent product list caches
      if (productListKeys.length > 50) {
        const keysToDelete = productListKeys.slice(50);
        if (keysToDelete.length > 0) {
          await cacheService.client.del(...keysToDelete);
          logger.info(`Cleaned up ${keysToDelete.length} old product list caches`);
        }
      }
    } catch (error) {
      logger.error('Cleanup old product lists failed:', error);
    }
  }

  /**
   * Schedule periodic cache optimization
   */
  startPeriodicOptimization() {
    // Optimize memory every 30 minutes
    setInterval(() => {
      this.optimizeMemoryUsage();
    }, 30 * 60 * 1000);
    
    // Warm critical caches every 2 hours
    setInterval(() => {
      this.warmCriticalCaches();
    }, 2 * 60 * 60 * 1000);
    
    logger.info('Started periodic cache optimization');
  }
}

module.exports = new CacheOptimizationService();
