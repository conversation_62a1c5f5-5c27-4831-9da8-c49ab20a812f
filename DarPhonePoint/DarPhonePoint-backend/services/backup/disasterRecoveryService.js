const fs = require('fs').promises;
const path = require('path');
const logger = require('../utils/logger');
const config = require('../config/config');
const mongoose = require('mongoose');

class DisasterRecoveryService {
  constructor() {
    this.backupConfig = {
      schedule: '0 0 * * *', // Daily at midnight
      retention: '30d', // 30 days
      locations: ['primary', 'secondary'],
      types: ['database', 'files', 'config']
    };
    
    this.recoveryPoints = [];
    this.isBackupInProgress = false;
  }

  async initialize() {
    try {
      // Create backup directories if they don't exist
      await this.createBackupDirectories();
      
      // Schedule regular backups
      this.scheduleBackups();
      
      logger.info('Disaster recovery service initialized successfully');
      return { status: 'healthy', message: 'Recovery service active' };
    } catch (error) {
      logger.error('Failed to initialize disaster recovery service:', error);
      throw error;
    }
  }

  async createBackupDirectories() {
    const backupDirs = [
      path.join(process.cwd(), 'backups'),
      path.join(process.cwd(), 'backups', 'database'),
      path.join(process.cwd(), 'backups', 'files'),
      path.join(process.cwd(), 'backups', 'config')
    ];

    for (const dir of backupDirs) {
      await fs.mkdir(dir, { recursive: true });
    }
  }

  scheduleBackups() {
    // Schedule daily backups
    setInterval(async () => {
      if (!this.isBackupInProgress) {
        await this.performBackup();
      }
    }, 24 * 60 * 60 * 1000); // 24 hours
  }

  async performBackup() {
    try {
      this.isBackupInProgress = true;
      const timestamp = new Date().toISOString();
      
      // Backup database
      await this.backupDatabase(timestamp);
      
      // Backup files
      await this.backupFiles(timestamp);
      
      // Backup configuration
      await this._backupConfig(timestamp);
      
      // Update recovery points
      this.recoveryPoints.push({
        timestamp,
        location: 'primary',
        status: 'completed'
      });
      
      // Clean old backups
      await this.cleanOldBackups();
      
      logger.info('Backup completed successfully:', { timestamp });
    } catch (error) {
      logger.error('Backup failed:', error);
      throw error;
    } finally {
      this.isBackupInProgress = false;
    }
  }

  async backupDatabase(timestamp) {
    try {
      // Ensure mongoose is connected before proceeding
      if (mongoose.connection.readyState !== 1) {
        logger.warn('Database not connected, skipping database backup.');
        return null;
      }

      const db = mongoose.connection.db;
      const collections = await db.listCollections().toArray();

      // Create backup directory
      const backupDir = path.join(process.cwd(), 'backups', 'database');
      await fs.mkdir(backupDir, { recursive: true });

      const backupInfo = {
        timestamp,
        collections: [],
        total_documents: 0,
        backup_size: 0
      };

      logger.info(`Starting database backup for ${collections.length} collections`);

      for (const collection of collections) {
        const collectionName = collection.name;
        const data = await db.collection(collectionName).find({}).toArray();

        const backupPath = path.join(
          backupDir,
          `${collectionName}_${timestamp.replace(/[:.]/g, '-')}.json`
        );

        const jsonData = JSON.stringify(data, null, 2);
        await fs.writeFile(backupPath, jsonData);

        const stats = await fs.stat(backupPath);

        backupInfo.collections.push({
          name: collectionName,
          document_count: data.length,
          file_size: stats.size,
          backup_path: backupPath
        });

        backupInfo.total_documents += data.length;
        backupInfo.backup_size += stats.size;

        logger.info(`Backed up collection ${collectionName}: ${data.length} documents, ${stats.size} bytes`);
      }

      // Save backup metadata
      const metadataPath = path.join(backupDir, `backup_metadata_${timestamp.replace(/[:.]/g, '-')}.json`);
      await fs.writeFile(metadataPath, JSON.stringify(backupInfo, null, 2));

      logger.info('Database backup completed:', {
        timestamp,
        collections: backupInfo.collections.length,
        total_documents: backupInfo.total_documents,
        total_size: `${(backupInfo.backup_size / 1024 / 1024).toFixed(2)} MB`
      });

      return backupInfo;
    } catch (error) {
      logger.error('Database backup failed:', error);
      throw error;
    }
  }

  async backupFiles(timestamp) {
    try {
      const publicDir = path.join(process.cwd(), 'public');
      const backupDir = path.join(process.cwd(), 'backups', 'files', timestamp.replace(/[:.]/g, '-'));

      await fs.mkdir(backupDir, { recursive: true });

      const backupInfo = {
        timestamp,
        directories: [],
        total_files: 0,
        total_size: 0
      };

      // Backup important directories
      const importantDirs = ['uploads', 'products', 'images', 'documents'];
      for (const dirName of importantDirs) {
        const dirPath = path.join(publicDir, dirName);
        if (await this.directoryExists(dirPath)) {
          const dirBackupInfo = await this.backupDirectory(dirPath, path.join(backupDir, dirName));
          backupInfo.directories.push({
            name: dirName,
            ...dirBackupInfo
          });
          backupInfo.total_files += dirBackupInfo.file_count;
          backupInfo.total_size += dirBackupInfo.total_size;

          logger.info(`Backed up directory ${dirName}: ${dirBackupInfo.file_count} files, ${(dirBackupInfo.total_size / 1024).toFixed(2)} KB`);
        }
      }

      // Save backup metadata
      const metadataPath = path.join(backupDir, 'file_backup_metadata.json');
      await fs.writeFile(metadataPath, JSON.stringify(backupInfo, null, 2));

      logger.info('File backup completed:', {
        timestamp,
        directories: backupInfo.directories.length,
        total_files: backupInfo.total_files,
        total_size: `${(backupInfo.total_size / 1024 / 1024).toFixed(2)} MB`
      });

      return backupInfo;
    } catch (error) {
      logger.error('Files backup failed:', error);
      throw error;
    }
  }

  async directoryExists(dirPath) {
    try {
      const stats = await fs.stat(dirPath);
      return stats.isDirectory();
    } catch {
      return false;
    }
  }

  async backupDirectory(sourceDir, destDir) {
    await fs.mkdir(destDir, { recursive: true });

    const files = await fs.readdir(sourceDir, { withFileTypes: true });
    let fileCount = 0;
    let totalSize = 0;

    for (const file of files) {
      const sourcePath = path.join(sourceDir, file.name);
      const destPath = path.join(destDir, file.name);

      if (file.isDirectory()) {
        const subDirInfo = await this.backupDirectory(sourcePath, destPath);
        fileCount += subDirInfo.file_count;
        totalSize += subDirInfo.total_size;
      } else if (file.isFile()) {
        await fs.copyFile(sourcePath, destPath);
        const stats = await fs.stat(sourcePath);
        fileCount++;
        totalSize += stats.size;
      }
    }

    return { file_count: fileCount, total_size: totalSize };
  }

  async _backupConfig(timestamp) {
    try {
      const configFiles = [
        'config.js',
        'env.js',
        'db.js'
      ];
      
      for (const file of configFiles) {
        const sourcePath = path.join(process.cwd(), 'config', file);
        logger.debug(`Attempting to backup config file from: ${sourcePath}`);
        const destPath = path.join(
          process.cwd(),
          'backups',
          'config',
          `${file}_${timestamp}`
        );
        
        await fs.copyFile(sourcePath, destPath);
      }
    } catch (error) {
      logger.error('Config backup failed:', error);
      throw error;
    }
  }

  async cleanOldBackups() {
    try {
      const retentionDays = parseInt(this.backupConfig.retention);
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - retentionDays);
      
      const backupTypes = ['database', 'files', 'config'];
      
      for (const type of backupTypes) {
        const backupDir = path.join(process.cwd(), 'backups', type);
        const files = await fs.readdir(backupDir);
        
        for (const file of files) {
          const filePath = path.join(backupDir, file);
          const stats = await fs.stat(filePath);
          
          if (stats.mtime < cutoffDate) {
            await fs.unlink(filePath);
          }
        }
      }
    } catch (error) {
      logger.error('Failed to clean old backups:', error);
      throw error;
    }
  }

  async performRecovery(recoveryPoint) {
    try {
      // Validate recovery point
      const isValid = this.recoveryPoints.find(
        point => point.timestamp === recoveryPoint
      );
      
      if (!isValid) {
        throw new Error('Invalid recovery point');
      }
      
      // Recover database
      await this.recoverDatabase(recoveryPoint);
      
      // Recover files
      await this.recoverFiles(recoveryPoint);
      
      // Recover configuration
      await this.recoverConfig(recoveryPoint);
      
      logger.info('Recovery completed successfully:', { recoveryPoint });
    } catch (error) {
      logger.error('Recovery failed:', error);
      throw error;
    }
  }

  async healthCheck() {
    try {
      const lastBackup = this.recoveryPoints[this.recoveryPoints.length - 1];
      return {
        status: 'healthy',
        lastBackup: lastBackup?.timestamp || null,
        backupInProgress: this.isBackupInProgress,
        recoveryPoints: this.recoveryPoints.length
      };
    } catch (error) {
      logger.error('Health check failed:', error);
      return {
        status: 'unhealthy',
        error: error.message
      };
    }
  }
}

module.exports = new DisasterRecoveryService(); 