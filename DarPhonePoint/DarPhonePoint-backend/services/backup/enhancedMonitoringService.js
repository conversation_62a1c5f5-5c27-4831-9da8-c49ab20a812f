const os = require('os');
const process = require('process');
const mongoose = require('mongoose');
const config = require('../config/config');
const logger = require('../utils/logger');
const advancedCacheService = require('./advancedCacheService');

class EnhancedMonitoringService {
  constructor() {
    this.metrics = {
      responseTime: [],
      memoryUsage: [],
      cpuUsage: [],
      errorRates: {},
      apiCalls: {}
    };
    
    this.thresholds = {
      responseTime: 2000, // ms
      memoryUsage: 0.8, // 80%
      cpuUsage: 0.7, // 70%
      errorRate: 0.05 // 5%
    };
    
    this.alertChannels = {
      email: true,
      slack: false,
      webhook: false
    };
  }

  async initialize() {
    try {
      // Start collecting metrics
      this.startMetricsCollection();
      
      // Initialize alert channels
      await this.setupAlertChannels();
      
      logger.info('Enhanced monitoring service initialized successfully');
      return { status: 'healthy', message: 'Monitoring service active' };
    } catch (error) {
      logger.error('Failed to initialize monitoring service:', error);
      throw error;
    }
  }

  startMetricsCollection() {
    // Collect system metrics every minute
    setInterval(() => {
      this.collectSystemMetrics();
      this.checkThresholds();
    }, 60000);
  }

  collectSystemMetrics() {
    const memoryUsage = process.memoryUsage();
    const cpuUsage = process.cpuUsage();
    
    this.metrics.memoryUsage.push({
      timestamp: Date.now(),
      value: memoryUsage.heapUsed / memoryUsage.heapTotal
    });
    
    this.metrics.cpuUsage.push({
      timestamp: Date.now(),
      value: (cpuUsage.user + cpuUsage.system) / 1000000
    });
    
    // Keep only last 24 hours of metrics
    this.pruneOldMetrics();
  }

  async checkThresholds() {
    const currentMetrics = this.getCurrentMetrics();
    
    if (currentMetrics.memoryUsage > this.thresholds.memoryUsage) {
      await this.sendAlert('memory', currentMetrics.memoryUsage);
    }
    
    if (currentMetrics.cpuUsage > this.thresholds.cpuUsage) {
      await this.sendAlert('cpu', currentMetrics.cpuUsage);
    }
    
    if (currentMetrics.errorRate > this.thresholds.errorRate) {
      await this.sendAlert('error', currentMetrics.errorRate);
    }
  }

  async sendAlert(type, value) {
    const alert = {
      type,
      value,
      timestamp: Date.now(),
      message: `Alert: ${type} usage exceeded threshold (${value})`
    };
    
    if (this.alertChannels.email) {
      await this.sendEmailAlert(alert);
    }
    
    if (this.alertChannels.slack) {
      await this.sendSlackAlert(alert);
    }
    
    if (this.alertChannels.webhook) {
      await this.sendWebhookAlert(alert);
    }

    if (this.smsEnabled && process.env.ADMIN_PHONE) {
      await this.sendSMSAlert(alert);
    }

    logger.warn('Alert sent:', alert);
  }

  async sendEmailAlert(alert) {
    try {
      if (!this.emailService) return;

      await this.emailService.sendEmail({
        template: 'system_alert',
        to: process.env.ADMIN_EMAIL || '<EMAIL>',
        subject: `🚨 Phone Point Dar System Alert: ${alert.type}`,
        data: {
          alert_type: alert.type,
          threshold: alert.threshold,
          current_value: alert.value,
          message: alert.message,
          timestamp: new Date(alert.timestamp).toLocaleString(),
          server_info: {
            environment: process.env.NODE_ENV,
            server_name: process.env.SERVER_NAME || 'Phone Point Dar Server'
          }
        }
      });

      logger.info('Email alert sent successfully');
    } catch (error) {
      logger.error('Failed to send email alert:', error);
    }
  }

  async sendSlackAlert(alert) {
    try {
      if (!this.slackWebhookUrl) return;

      const axios = require('axios');
      const payload = {
        text: `🚨 Phone Point Dar System Alert`,
        attachments: [{
          color: 'danger',
          fields: [
            { title: 'Alert Type', value: alert.type, short: true },
            { title: 'Current Value', value: `${(alert.value * 100).toFixed(1)}%`, short: true },
            { title: 'Threshold', value: `${(alert.threshold * 100).toFixed(1)}%`, short: true },
            { title: 'Time', value: new Date(alert.timestamp).toLocaleString(), short: true },
            { title: 'Message', value: alert.message, short: false }
          ],
          footer: 'Phone Point Dar Monitoring',
          ts: Math.floor(alert.timestamp / 1000)
        }]
      };

      await axios.post(this.slackWebhookUrl, payload);
      logger.info('Slack alert sent successfully');
    } catch (error) {
      logger.error('Failed to send Slack alert:', error);
    }
  }

  async sendWebhookAlert(alert) {
    try {
      if (!this.webhookUrl) return;

      const axios = require('axios');
      const payload = {
        alert_type: alert.type,
        threshold: alert.threshold,
        current_value: alert.value,
        message: alert.message,
        timestamp: alert.timestamp,
        server: {
          environment: process.env.NODE_ENV,
          name: process.env.SERVER_NAME || 'Phone Point Dar Server'
        }
      };

      await axios.post(this.webhookUrl, payload, {
        headers: { 'Content-Type': 'application/json' },
        timeout: 5000
      });

      logger.info('Webhook alert sent successfully');
    } catch (error) {
      logger.error('Failed to send webhook alert:', error);
    }
  }

  async sendSMSAlert(alert) {
    try {
      if (!this.smsEnabled || !this.smsApiKey) return;

      const axios = require('axios');
      const message = `🚨 Phone Point Dar Alert: ${alert.type} at ${(alert.value * 100).toFixed(1)}% (threshold: ${(alert.threshold * 100).toFixed(1)}%). Time: ${new Date(alert.timestamp).toLocaleString()}`;

      const payload = {
        source_addr: 'PhonePointDar',
        encoding: 0,
        schedule_time: '',
        message: message,
        recipients: [{
          recipient_id: 1,
          dest_addr: process.env.ADMIN_PHONE
        }]
      };

      await axios.post(this.smsApiUrl, payload, {
        headers: {
          'Authorization': `Bearer ${this.smsApiKey}`,
          'Content-Type': 'application/json'
        },
        timeout: 10000
      });

      logger.info('SMS alert sent successfully');
    } catch (error) {
      logger.error('Failed to send SMS alert:', error);
    }
  }

  async setupAlertChannels() {
    try {
      // Configure email alerts
      if (this.alertChannels.email) {
        this.emailService = require('./emailService');
        logger.info('Email alert channel configured');
      }

      // Configure Slack alerts
      if (this.alertChannels.slack && process.env.SLACK_WEBHOOK_URL) {
        this.slackWebhookUrl = process.env.SLACK_WEBHOOK_URL;
        logger.info('Slack alert channel configured');
      }

      // Configure custom webhook
      if (this.alertChannels.webhook && process.env.ALERT_WEBHOOK_URL) {
        this.webhookUrl = process.env.ALERT_WEBHOOK_URL;
        logger.info('Custom webhook alert channel configured');
      }

      // Configure SMS alerts via Tanzania providers
      if (process.env.SMS_PROVIDER_API_KEY) {
        this.smsEnabled = true;
        this.smsApiKey = process.env.SMS_PROVIDER_API_KEY;
        this.smsApiUrl = process.env.SMS_PROVIDER_URL || 'https://api.beem.africa/v1/send';
        logger.info('SMS alert channel configured');
      }
    } catch (error) {
      logger.error('Failed to setup alert channels:', error);
    }
  }

  pruneOldMetrics() {
    const oneDayAgo = Date.now() - (24 * 60 * 60 * 1000);
    
    Object.keys(this.metrics).forEach(key => {
      if (Array.isArray(this.metrics[key])) {
        this.metrics[key] = this.metrics[key].filter(
          metric => metric.timestamp > oneDayAgo
        );
      }
    });
  }

  getCurrentMetrics() {
    return {
      memoryUsage: this.metrics.memoryUsage[this.metrics.memoryUsage.length - 1]?.value || 0,
      cpuUsage: this.metrics.cpuUsage[this.metrics.cpuUsage.length - 1]?.value || 0,
      errorRate: this.calculateErrorRate()
    };
  }

  calculateErrorRate() {
    const totalErrors = Object.values(this.metrics.errorRates).reduce(
      (sum, count) => sum + count, 0
    );
    const totalCalls = Object.values(this.metrics.apiCalls).reduce(
      (sum, count) => sum + count, 0
    );
    
    return totalCalls > 0 ? totalErrors / totalCalls : 0;
  }

  async healthCheck() {
    try {
      const metrics = this.getCurrentMetrics();
      return {
        status: 'healthy',
        metrics,
        thresholds: this.thresholds,
        alertChannels: this.alertChannels
      };
    } catch (error) {
      logger.error('Health check failed:', error);
      return {
        status: 'unhealthy',
        error: error.message
      };
    }
  }
}

module.exports = new EnhancedMonitoringService();
