const os = require('os');
const fs = require('fs').promises;
const path = require('path');
const mongoose = require('mongoose');
const logger = require('../utils/logger');
const redisClient = require('../utils/redisClient');

/**
 * Production Monitoring Service
 * Comprehensive monitoring for production deployment
 */
class ProductionMonitoringService {
  constructor() {
    this.metrics = {
      system: {},
      application: {},
      database: {},
      cache: {},
      errors: [],
      alerts: []
    };
    
    this.thresholds = {
      cpu: 80, // CPU usage percentage
      memory: 85, // Memory usage percentage
      disk: 90, // Disk usage percentage
      responseTime: 2000, // Response time in ms
      errorRate: 5, // Error rate percentage
      dbConnections: 80 // Database connection usage percentage
    };

    this.startTime = Date.now();
    this.requestCount = 0;
    this.errorCount = 0;
    this.lastHealthCheck = null;
    
    // Start monitoring intervals
    this.startMonitoring();
  }

  /**
   * Start monitoring intervals
   */
  startMonitoring() {
    // System metrics every 30 seconds
    setInterval(() => this.collectSystemMetrics(), 30000);
    
    // Application metrics every 60 seconds
    setInterval(() => this.collectApplicationMetrics(), 60000);
    
    // Database metrics every 60 seconds
    setInterval(() => this.collectDatabaseMetrics(), 60000);
    
    // Cache metrics every 60 seconds
    setInterval(() => this.collectCacheMetrics(), 60000);
    
    // Health check every 5 minutes
    setInterval(() => this.performHealthCheck(), 300000);
    
    logger.info('Production monitoring started');
  }

  /**
   * Collect system metrics
   */
  async collectSystemMetrics() {
    try {
      const cpuUsage = await this.getCPUUsage();
      const memoryUsage = this.getMemoryUsage();
      const diskUsage = await this.getDiskUsage();
      const loadAverage = os.loadavg();

      this.metrics.system = {
        timestamp: new Date(),
        cpu: {
          usage: cpuUsage,
          cores: os.cpus().length,
          loadAverage: loadAverage
        },
        memory: memoryUsage,
        disk: diskUsage,
        uptime: os.uptime(),
        platform: os.platform(),
        arch: os.arch()
      };

      // Check thresholds and create alerts
      this.checkSystemThresholds();
      
    } catch (error) {
      logger.error('Error collecting system metrics:', error);
    }
  }

  /**
   * Get CPU usage percentage
   */
  async getCPUUsage() {
    return new Promise((resolve) => {
      const startMeasure = this.cpuAverage();
      
      setTimeout(() => {
        const endMeasure = this.cpuAverage();
        const idleDifference = endMeasure.idle - startMeasure.idle;
        const totalDifference = endMeasure.total - startMeasure.total;
        const percentageCPU = 100 - ~~(100 * idleDifference / totalDifference);
        resolve(percentageCPU);
      }, 1000);
    });
  }

  /**
   * Calculate CPU average
   */
  cpuAverage() {
    const cpus = os.cpus();
    let user = 0, nice = 0, sys = 0, idle = 0, irq = 0;
    
    for (let cpu of cpus) {
      user += cpu.times.user;
      nice += cpu.times.nice;
      sys += cpu.times.sys;
      idle += cpu.times.idle;
      irq += cpu.times.irq;
    }
    
    const total = user + nice + sys + idle + irq;
    return { idle, total };
  }

  /**
   * Get memory usage
   */
  getMemoryUsage() {
    const totalMemory = os.totalmem();
    const freeMemory = os.freemem();
    const usedMemory = totalMemory - freeMemory;
    const usagePercentage = (usedMemory / totalMemory) * 100;

    return {
      total: totalMemory,
      free: freeMemory,
      used: usedMemory,
      percentage: usagePercentage
    };
  }

  /**
   * Get disk usage
   */
  async getDiskUsage() {
    try {
      const stats = await fs.stat(process.cwd());
      // This is a simplified disk usage check
      // In production, you might want to use a more comprehensive solution
      return {
        available: true,
        path: process.cwd(),
        accessible: true
      };
    } catch (error) {
      return {
        available: false,
        error: error.message
      };
    }
  }

  /**
   * Collect application metrics
   */
  collectApplicationMetrics() {
    const processMemory = process.memoryUsage();
    const uptime = process.uptime();
    
    this.metrics.application = {
      timestamp: new Date(),
      uptime: uptime,
      requests: {
        total: this.requestCount,
        errors: this.errorCount,
        errorRate: this.requestCount > 0 ? (this.errorCount / this.requestCount) * 100 : 0
      },
      memory: {
        rss: processMemory.rss,
        heapTotal: processMemory.heapTotal,
        heapUsed: processMemory.heapUsed,
        external: processMemory.external,
        arrayBuffers: processMemory.arrayBuffers
      },
      eventLoop: {
        delay: this.getEventLoopDelay()
      }
    };

    // Check application thresholds
    this.checkApplicationThresholds();
  }

  /**
   * Get event loop delay
   */
  getEventLoopDelay() {
    const start = process.hrtime.bigint();
    setImmediate(() => {
      const delay = Number(process.hrtime.bigint() - start) / 1000000; // Convert to ms
      return delay;
    });
    return 0; // Simplified for now
  }

  /**
   * Collect database metrics
   */
  async collectDatabaseMetrics() {
    try {
      if (mongoose.connection.readyState === 1) {
        const dbStats = await mongoose.connection.db.stats();
        
        this.metrics.database = {
          timestamp: new Date(),
          connected: true,
          readyState: mongoose.connection.readyState,
          collections: dbStats.collections,
          dataSize: dbStats.dataSize,
          storageSize: dbStats.storageSize,
          indexes: dbStats.indexes,
          indexSize: dbStats.indexSize,
          connections: mongoose.connection.db.serverConfig?.connections?.length || 0
        };
      } else {
        this.metrics.database = {
          timestamp: new Date(),
          connected: false,
          readyState: mongoose.connection.readyState,
          error: 'Database not connected'
        };
        
        this.createAlert('DATABASE_DISCONNECTED', 'Database connection lost', 'critical');
      }
    } catch (error) {
      logger.error('Error collecting database metrics:', error);
      this.metrics.database = {
        timestamp: new Date(),
        connected: false,
        error: error.message
      };
    }
  }

  /**
   * Collect cache metrics
   */
  async collectCacheMetrics() {
    try {
      if (redisClient && redisClient.client && redisClient.client.connected) {
        // Try to get basic info - simplified since we don't have direct access to info command
        const testKey = 'health_check_' + Date.now();
        await redisClient.setAsync(testKey, 'test');
        await redisClient.delAsync(testKey);

        this.metrics.cache = {
          timestamp: new Date(),
          connected: true,
          status: 'ready',
          note: 'Basic Redis operations working'
        };
      } else {
        this.metrics.cache = {
          timestamp: new Date(),
          connected: false,
          status: redisClient?.client?.connected ? 'connected' : 'disconnected',
          error: 'Redis not available or not connected'
        };
      }
    } catch (error) {
      logger.error('Error collecting cache metrics:', error);
      this.metrics.cache = {
        timestamp: new Date(),
        connected: false,
        error: error.message
      };
    }
  }

  /**
   * Parse Redis info response
   */
  parseRedisInfo(info) {
    const result = {};
    const lines = info.split('\r\n');
    
    for (const line of lines) {
      if (line.includes(':')) {
        const [key, value] = line.split(':');
        result[key] = isNaN(value) ? value : Number(value);
      }
    }
    
    return result;
  }

  /**
   * Perform comprehensive health check
   */
  async performHealthCheck() {
    const healthCheck = {
      timestamp: new Date(),
      status: 'healthy',
      checks: {}
    };

    try {
      // Database health
      healthCheck.checks.database = await this.checkDatabaseHealth();
      
      // Cache health
      healthCheck.checks.cache = await this.checkCacheHealth();
      
      // File system health
      healthCheck.checks.filesystem = await this.checkFileSystemHealth();
      
      // External services health
      healthCheck.checks.external = await this.checkExternalServicesHealth();
      
      // Overall status
      const allHealthy = Object.values(healthCheck.checks).every(check => check.status === 'healthy');
      healthCheck.status = allHealthy ? 'healthy' : 'unhealthy';
      
      this.lastHealthCheck = healthCheck;
      
      if (!allHealthy) {
        this.createAlert('HEALTH_CHECK_FAILED', 'System health check failed', 'warning');
      }
      
    } catch (error) {
      logger.error('Health check error:', error);
      healthCheck.status = 'error';
      healthCheck.error = error.message;
    }

    return healthCheck;
  }

  /**
   * Check database health
   */
  async checkDatabaseHealth() {
    try {
      if (mongoose.connection.readyState === 1) {
        await mongoose.connection.db.admin().ping();
        return { status: 'healthy', responseTime: Date.now() };
      } else {
        return { status: 'unhealthy', error: 'Database not connected' };
      }
    } catch (error) {
      return { status: 'unhealthy', error: error.message };
    }
  }

  /**
   * Check cache health
   */
  async checkCacheHealth() {
    try {
      if (redisClient && redisClient.client && redisClient.client.connected) {
        const start = Date.now();
        // Test basic Redis operation instead of ping
        const testKey = 'health_check_' + Date.now();
        await redisClient.setAsync(testKey, 'test');
        await redisClient.delAsync(testKey);
        const responseTime = Date.now() - start;
        return { status: 'healthy', responseTime };
      } else {
        return { status: 'unhealthy', error: 'Redis not connected' };
      }
    } catch (error) {
      return { status: 'unhealthy', error: error.message };
    }
  }

  /**
   * Check file system health
   */
  async checkFileSystemHealth() {
    try {
      const testFile = path.join(process.cwd(), 'temp', 'health-check.txt');
      await fs.writeFile(testFile, 'health check');
      await fs.unlink(testFile);
      return { status: 'healthy' };
    } catch (error) {
      return { status: 'unhealthy', error: error.message };
    }
  }

  /**
   * Check external services health
   */
  async checkExternalServicesHealth() {
    // This would check external APIs like Whop, email service, etc.
    // Simplified for now
    return { status: 'healthy', note: 'External service checks not implemented' };
  }

  /**
   * Check system thresholds and create alerts
   */
  checkSystemThresholds() {
    const { cpu, memory } = this.metrics.system;
    
    if (cpu.usage > this.thresholds.cpu) {
      this.createAlert('HIGH_CPU_USAGE', `CPU usage: ${cpu.usage}%`, 'warning');
    }
    
    if (memory.percentage > this.thresholds.memory) {
      this.createAlert('HIGH_MEMORY_USAGE', `Memory usage: ${memory.percentage.toFixed(2)}%`, 'warning');
    }
  }

  /**
   * Check application thresholds
   */
  checkApplicationThresholds() {
    const { requests } = this.metrics.application;
    
    if (requests.errorRate > this.thresholds.errorRate) {
      this.createAlert('HIGH_ERROR_RATE', `Error rate: ${requests.errorRate.toFixed(2)}%`, 'critical');
    }
  }

  /**
   * Create alert
   */
  createAlert(type, message, severity = 'info') {
    const alert = {
      id: Date.now().toString(),
      type,
      message,
      severity,
      timestamp: new Date(),
      acknowledged: false
    };

    this.metrics.alerts.push(alert);

    // Keep only last 100 alerts
    if (this.metrics.alerts.length > 100) {
      this.metrics.alerts = this.metrics.alerts.slice(-100);
    }

    // Use proper logger methods based on severity
    switch (severity) {
      case 'critical':
      case 'error':
        logger.error(`ALERT [${type}]: ${message}`);
        break;
      case 'warning':
      case 'warn':
        logger.warn(`ALERT [${type}]: ${message}`);
        break;
      case 'info':
      default:
        logger.info(`ALERT [${type}]: ${message}`);
        break;
    }
  }

  /**
   * Record request
   */
  recordRequest() {
    this.requestCount++;
  }

  /**
   * Record error
   */
  recordError() {
    this.errorCount++;
  }

  /**
   * Get current metrics
   */
  getMetrics() {
    return {
      ...this.metrics,
      summary: {
        uptime: Date.now() - this.startTime,
        totalRequests: this.requestCount,
        totalErrors: this.errorCount,
        errorRate: this.requestCount > 0 ? (this.errorCount / this.requestCount) * 100 : 0,
        lastHealthCheck: this.lastHealthCheck
      }
    };
  }

  /**
   * Get health status
   */
  getHealthStatus() {
    return this.lastHealthCheck || { status: 'unknown', message: 'No health check performed yet' };
  }
}

// Export singleton instance
module.exports = new ProductionMonitoringService();
