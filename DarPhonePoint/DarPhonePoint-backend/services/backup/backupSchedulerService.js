/**
 * Backup Scheduler Service
 * Handles automated backup scheduling for Phone Point Dar
 */

const cron = require('node-cron');
const disasterRecoveryService = require('./disasterRecoveryService');
const logger = require('../utils/logger');
const fs = require('fs').promises;
const path = require('path');

class BackupSchedulerService {
  constructor() {
    this.scheduledTasks = new Map();
    this.isInitialized = false;
  }

  /**
   * Initialize backup scheduler
   */
  async initialize() {
    try {
      if (this.isInitialized) {
        logger.warn('Backup scheduler already initialized');
        return;
      }

      // Create backup directories
      await this.ensureBackupDirectories();

      // Schedule daily database backup at 2 AM
      this.scheduleTask('daily-database-backup', '0 2 * * *', async () => {
        await this.performDatabaseBackup();
      });

      // Schedule weekly file backup on Sundays at 3 AM
      this.scheduleTask('weekly-file-backup', '0 3 * * 0', async () => {
        await this.performFileBackup();
      });

      // Schedule monthly full backup on 1st of month at 4 AM
      this.scheduleTask('monthly-full-backup', '0 4 1 * *', async () => {
        await this.performFullBackup();
      });

      // Schedule backup cleanup every day at 5 AM
      this.scheduleTask('daily-cleanup', '0 5 * * *', async () => {
        await this.cleanupOldBackups();
      });

      this.isInitialized = true;
      logger.info('Backup scheduler initialized successfully');

      // Perform initial backup check
      await this.performInitialBackupCheck();

    } catch (error) {
      logger.error('Failed to initialize backup scheduler:', error);
      throw error;
    }
  }

  /**
   * Schedule a backup task
   */
  scheduleTask(name, cronExpression, taskFunction) {
    try {
      if (this.scheduledTasks.has(name)) {
        logger.warn(`Task ${name} already scheduled, skipping`);
        return;
      }

      const task = cron.schedule(cronExpression, async () => {
        logger.info(`Starting scheduled task: ${name}`);
        try {
          await taskFunction();
          logger.info(`Completed scheduled task: ${name}`);
        } catch (error) {
          logger.error(`Failed scheduled task ${name}:`, error);
        }
      }, {
        scheduled: false,
        timezone: 'Africa/Dar_es_Salaam'
      });

      this.scheduledTasks.set(name, task);
      task.start();

      logger.info(`Scheduled backup task: ${name} (${cronExpression})`);
    } catch (error) {
      logger.error(`Failed to schedule task ${name}:`, error);
      throw error;
    }
  }

  /**
   * Perform database backup
   */
  async performDatabaseBackup() {
    try {
      logger.info('Starting scheduled database backup');
      const timestamp = new Date().toISOString();
      const backupInfo = await disasterRecoveryService.backupDatabase(timestamp);
      
      if (backupInfo) {
        logger.info('Scheduled database backup completed:', {
          collections: backupInfo.collections.length,
          total_documents: backupInfo.total_documents,
          size: `${(backupInfo.backup_size / 1024 / 1024).toFixed(2)} MB`
        });
      }
    } catch (error) {
      logger.error('Scheduled database backup failed:', error);
      throw error;
    }
  }

  /**
   * Perform file backup
   */
  async performFileBackup() {
    try {
      logger.info('Starting scheduled file backup');
      const timestamp = new Date().toISOString();
      const backupInfo = await disasterRecoveryService.backupFiles(timestamp);
      
      logger.info('Scheduled file backup completed:', {
        directories: backupInfo.directories.length,
        total_files: backupInfo.total_files,
        size: `${(backupInfo.total_size / 1024 / 1024).toFixed(2)} MB`
      });
    } catch (error) {
      logger.error('Scheduled file backup failed:', error);
      throw error;
    }
  }

  /**
   * Perform full backup (database + files)
   */
  async performFullBackup() {
    try {
      logger.info('Starting scheduled full backup');
      const timestamp = new Date().toISOString();
      
      const [databaseBackup, fileBackup] = await Promise.all([
        disasterRecoveryService.backupDatabase(timestamp),
        disasterRecoveryService.backupFiles(timestamp)
      ]);
      
      logger.info('Scheduled full backup completed:', {
        database: {
          collections: databaseBackup?.collections?.length || 0,
          documents: databaseBackup?.total_documents || 0
        },
        files: {
          directories: fileBackup.directories.length,
          files: fileBackup.total_files
        }
      });
    } catch (error) {
      logger.error('Scheduled full backup failed:', error);
      throw error;
    }
  }

  /**
   * Clean up old backups based on retention policy
   */
  async cleanupOldBackups() {
    try {
      const retentionDays = parseInt(process.env.BACKUP_RETENTION_DAYS) || 30;
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - retentionDays);

      const backupDir = path.join(process.cwd(), 'backups');
      const subdirs = ['database', 'files'];

      let deletedCount = 0;
      let freedSpace = 0;

      for (const subdir of subdirs) {
        const subdirPath = path.join(backupDir, subdir);
        
        try {
          const items = await fs.readdir(subdirPath, { withFileTypes: true });
          
          for (const item of items) {
            const itemPath = path.join(subdirPath, item.name);
            const stats = await fs.stat(itemPath);
            
            if (stats.mtime < cutoffDate) {
              if (item.isDirectory()) {
                await fs.rmdir(itemPath, { recursive: true });
              } else {
                await fs.unlink(itemPath);
              }
              
              deletedCount++;
              freedSpace += stats.size;
              
              logger.debug(`Deleted old backup: ${item.name}`);
            }
          }
        } catch (error) {
          logger.warn(`Failed to cleanup ${subdir} backups:`, error);
        }
      }

      if (deletedCount > 0) {
        logger.info('Backup cleanup completed:', {
          deleted_items: deletedCount,
          freed_space: `${(freedSpace / 1024 / 1024).toFixed(2)} MB`,
          retention_days: retentionDays
        });
      }
    } catch (error) {
      logger.error('Backup cleanup failed:', error);
    }
  }

  /**
   * Ensure backup directories exist
   */
  async ensureBackupDirectories() {
    const backupDirs = [
      path.join(process.cwd(), 'backups'),
      path.join(process.cwd(), 'backups', 'database'),
      path.join(process.cwd(), 'backups', 'files')
    ];

    for (const dir of backupDirs) {
      await fs.mkdir(dir, { recursive: true });
    }
  }

  /**
   * Perform initial backup check
   */
  async performInitialBackupCheck() {
    try {
      const backupDir = path.join(process.cwd(), 'backups', 'database');
      const files = await fs.readdir(backupDir);
      
      if (files.length === 0) {
        logger.info('No existing backups found, creating initial backup');
        await this.performDatabaseBackup();
      } else {
        logger.info(`Found ${files.length} existing backup files`);
      }
    } catch (error) {
      logger.warn('Initial backup check failed:', error);
    }
  }

  /**
   * Stop all scheduled tasks
   */
  stopAll() {
    for (const [name, task] of this.scheduledTasks) {
      task.stop();
      logger.info(`Stopped backup task: ${name}`);
    }
    this.scheduledTasks.clear();
    this.isInitialized = false;
  }

  /**
   * Get backup status
   */
  getStatus() {
    return {
      initialized: this.isInitialized,
      scheduled_tasks: Array.from(this.scheduledTasks.keys()),
      task_count: this.scheduledTasks.size
    };
  }
}

module.exports = new BackupSchedulerService();
