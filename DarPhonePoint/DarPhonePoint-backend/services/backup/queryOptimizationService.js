/**
 * Query Optimization Service for Phone Point Dar
 * Provides optimized database queries using aggregation pipelines
 * Eliminates N+1 query problems and improves performance
 */

const mongoose = require('mongoose');
const logger = require('../utils/logger');
const cacheService = require('./cacheService');

class QueryOptimizationService {
  /**
   * Get products with optimized aggregation pipeline
   * @param {Object} filters - Query filters
   * @param {Object} options - Query options (sort, limit, skip, etc.)
   * @returns {Promise<Object>} Optimized product results
   */
  async getProductsOptimized(filters = {}, options = {}) {
    try {
      const {
        page = 1,
        limit = 20,
        sort = { created_at: -1 },
        includeInventory = true,
        includeVariants = true
      } = options;

      const skip = (page - 1) * limit;

      // Build match stage
      const matchStage = {
        is_active: true,
        ...filters
      };

      // Build aggregation pipeline
      const pipeline = [
        { $match: matchStage },
        
        // Lookup inventory data
        ...(includeInventory ? [{
          $lookup: {
            from: 'inventories',
            localField: '_id',
            foreignField: 'product',
            as: 'inventory'
          }
        }] : []),

        // Add computed fields
        {
          $addFields: {
            stock_quantity: includeInventory ? { $sum: '$inventory.quantity_available' } : '$stock_quantity',
            is_low_stock: includeInventory ? {
              $lt: [{ $sum: '$inventory.quantity_available' }, '$low_stock_threshold']
            } : '$is_low_stock',
            inventory_value: includeInventory ? {
              $multiply: ['$price', { $sum: '$inventory.quantity_available' }]
            } : { $multiply: ['$price', '$stock_quantity'] },
            // Add stock status for frontend (using is_active)
            stock_status: {
              $cond: {
                if: { $eq: ['$track_inventory', false] },
                then: 'in_stock',
                else: {
                  $cond: {
                    if: includeInventory,
                    then: {
                      $cond: {
                        if: { $gt: [{ $sum: '$inventory.quantity_available' }, 0] },
                        then: 'in_stock',
                        else: 'out_of_stock'
                      }
                    },
                    else: {
                      $cond: {
                        if: { $gt: [{ $size: { $ifNull: ['$variants', []] } }, 0] },
                        then: {
                          $cond: {
                            if: {
                              $gt: [
                                {
                                  $size: {
                                    $filter: {
                                      input: '$variants',
                                      cond: {
                                        $and: [
                                          { $eq: ['$$this.is_active', true] },
                                          { $gt: ['$$this.stock_quantity', 0] }
                                        ]
                                      }
                                    }
                                  }
                                },
                                0
                              ]
                            },
                            then: 'in_stock',
                            else: 'out_of_stock'
                          }
                        },
                        else: {
                          $cond: {
                            if: { $gt: ['$stock_quantity', 0] },
                            then: 'in_stock',
                            else: 'out_of_stock'
                          }
                        }
                      }
                    }
                  }
                }
              }
            },
            // Add boolean in_stock field for frontend compatibility
            in_stock: {
              $cond: {
                if: { $eq: ['$track_inventory', false] },
                then: true,
                else: {
                  $cond: {
                    if: includeInventory,
                    then: { $gt: [{ $sum: '$inventory.quantity_available' }, 0] },
                    else: {
                      $cond: {
                        if: { $gt: [{ $size: { $ifNull: ['$variants', []] } }, 0] },
                        then: {
                          $gt: [
                            {
                              $size: {
                                $filter: {
                                  input: '$variants',
                                  cond: {
                                    $and: [
                                      { $eq: ['$$this.is_active', true] },
                                      { $gt: ['$$this.stock_quantity', 0] }
                                    ]
                                  }
                                }
                              }
                            },
                            0
                          ]
                        },
                        else: { $gt: ['$stock_quantity', 0] }
                      }
                    }
                  }
                }
              }
            }
          }
        },

        // Lookup variants if needed
        ...(includeVariants ? [{
          $lookup: {
            from: 'productvariants',
            localField: '_id',
            foreignField: 'product',
            as: 'variants',
            pipeline: [
              { $match: { is_active: true } },
              { $sort: { price: 1 } }
            ]
          }
        }] : []),

        // Sort
        { $sort: sort },

        // Facet for pagination
        {
          $facet: {
            data: [
              { $skip: skip },
              { $limit: parseInt(limit) }
            ],
            totalCount: [
              { $count: 'count' }
            ]
          }
        }
      ];

      // Check cache first
      const cacheKey = cacheService.generateHash({ filters, options, pipeline: 'products' });
      const cached = await cacheService.getCachedProductList(cacheKey);
      
      if (cached) {
        logger.debug('Returning cached product list');
        return cached;
      }

      // Execute aggregation
      const Product = mongoose.model('Product');
      const [result] = await Product.aggregate(pipeline);

      const products = result.data || [];
      const total = result.totalCount[0]?.count || 0;

      const response = {
        data: products,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / limit),
          hasNextPage: page < Math.ceil(total / limit),
          hasPrevPage: page > 1
        }
      };

      // Cache the result
      await cacheService.cacheProductList(cacheKey, response);

      logger.debug(`Optimized product query executed: ${products.length} products`);
      return response;

    } catch (error) {
      logger.error('Error in optimized product query:', error);
      throw error;
    }
  }

  /**
   * Get orders with optimized aggregation pipeline
   * @param {Object} filters - Query filters
   * @param {Object} options - Query options
   * @returns {Promise<Object>} Optimized order results
   */
  async getOrdersOptimized(filters = {}, options = {}) {
    try {
      const {
        page = 1,
        limit = 20,
        sort = { created_at: -1 },
        includeItems = true,
        includeCustomer = true
      } = options;

      const skip = (page - 1) * limit;

      // Build aggregation pipeline
      const pipeline = [
        { $match: filters },

        // Lookup customer data if needed
        ...(includeCustomer ? [{
          $lookup: {
            from: 'users',
            localField: 'user',
            foreignField: '_id',
            as: 'customer',
            pipeline: [
              {
                $project: {
                  name: 1,
                  email: 1,
                  phone: 1,
                  user_type: 1
                }
              }
            ]
          }
        }] : []),

        // Unwind customer (optional)
        ...(includeCustomer ? [{
          $addFields: {
            customer: { $arrayElemAt: ['$customer', 0] }
          }
        }] : []),

        // Lookup order items with product details
        ...(includeItems ? [{
          $lookup: {
            from: 'products',
            localField: 'items.product',
            foreignField: '_id',
            as: 'productDetails'
          }
        }] : []),

        // Transform items to include product details
        ...(includeItems ? [{
          $addFields: {
            items: {
              $map: {
                input: '$items',
                as: 'item',
                in: {
                  $mergeObjects: [
                    '$$item',
                    {
                      product_details: {
                        $arrayElemAt: [
                          {
                            $filter: {
                              input: '$productDetails',
                              cond: { $eq: ['$$this._id', '$$item.product'] }
                            }
                          },
                          0
                        ]
                      }
                    }
                  ]
                }
              }
            }
          }
        }] : []),

        // Remove temporary productDetails field
        ...(includeItems ? [{
          $unset: 'productDetails'
        }] : []),

        // Add computed fields
        {
          $addFields: {
            item_count: { $size: '$items' },
            total_quantity: { $sum: '$items.quantity' }
          }
        },

        // Sort
        { $sort: sort },

        // Facet for pagination
        {
          $facet: {
            data: [
              { $skip: skip },
              { $limit: parseInt(limit) }
            ],
            totalCount: [
              { $count: 'count' }
            ]
          }
        }
      ];

      // Execute aggregation
      const Order = mongoose.model('Order');
      const [result] = await Order.aggregate(pipeline);

      const orders = result.data || [];
      const total = result.totalCount[0]?.count || 0;

      const response = {
        data: orders,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / limit),
          hasNextPage: page < Math.ceil(total / limit),
          hasPrevPage: page > 1
        }
      };

      logger.debug(`Optimized order query executed: ${orders.length} orders`);
      return response;

    } catch (error) {
      logger.error('Error in optimized order query:', error);
      throw error;
    }
  }

  /**
   * Get inventory with optimized aggregation pipeline
   * @param {Object} filters - Query filters
   * @param {Object} options - Query options
   * @returns {Promise<Object>} Optimized inventory results
   */
  async getInventoryOptimized(filters = {}, options = {}) {
    try {
      const {
        page = 1,
        limit = 20,
        sort = { updated_at: -1 },
        includeProduct = true,
        lowStockOnly = false
      } = options;

      const skip = (page - 1) * limit;

      // Build match stage
      const matchStage = { ...filters };
      if (lowStockOnly) {
        matchStage.is_low_stock = true;
      }

      // Build aggregation pipeline
      const pipeline = [
        { $match: matchStage },

        // Lookup product details
        ...(includeProduct ? [{
          $lookup: {
            from: 'products',
            localField: 'product',
            foreignField: '_id',
            as: 'product_details',
            pipeline: [
              {
                $project: {
                  name: 1,
                  brand: 1,
                  model: 1,
                  category: 1,
                  price: 1,
                  images: 1
                }
              }
            ]
          }
        }] : []),

        // Unwind product details
        ...(includeProduct ? [{
          $addFields: {
            product_details: { $arrayElemAt: ['$product_details', 0] }
          }
        }] : []),

        // Add computed fields
        {
          $addFields: {
            inventory_value: { $multiply: ['$quantity', '$product_details.price'] },
            stock_status: {
              $cond: {
                if: { $lte: ['$quantity', 0] },
                then: 'out_of_stock',
                else: {
                  $cond: {
                    if: { $lte: ['$quantity', '$low_stock_threshold'] },
                    then: 'low_stock',
                    else: 'in_stock'
                  }
                }
              }
            }
          }
        },

        // Sort
        { $sort: sort },

        // Facet for pagination
        {
          $facet: {
            data: [
              { $skip: skip },
              { $limit: parseInt(limit) }
            ],
            totalCount: [
              { $count: 'count' }
            ],
            stats: [
              {
                $group: {
                  _id: null,
                  totalValue: { $sum: '$inventory_value' },
                  totalQuantity: { $sum: '$quantity' },
                  lowStockCount: {
                    $sum: {
                      $cond: [{ $eq: ['$stock_status', 'low_stock'] }, 1, 0]
                    }
                  },
                  outOfStockCount: {
                    $sum: {
                      $cond: [{ $eq: ['$stock_status', 'out_of_stock'] }, 1, 0]
                    }
                  }
                }
              }
            ]
          }
        }
      ];

      // Execute aggregation
      const Inventory = mongoose.model('Inventory');
      const [result] = await Inventory.aggregate(pipeline);

      const inventory = result.data || [];
      const total = result.totalCount[0]?.count || 0;
      const stats = result.stats[0] || {};

      const response = {
        data: inventory,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / limit),
          hasNextPage: page < Math.ceil(total / limit),
          hasPrevPage: page > 1
        },
        stats: {
          totalValue: stats.totalValue || 0,
          totalQuantity: stats.totalQuantity || 0,
          lowStockCount: stats.lowStockCount || 0,
          outOfStockCount: stats.outOfStockCount || 0
        }
      };

      logger.debug(`Optimized inventory query executed: ${inventory.length} items`);
      return response;

    } catch (error) {
      logger.error('Error in optimized inventory query:', error);
      throw error;
    }
  }

  /**
   * Get analytics data with optimized aggregation
   * @param {Object} filters - Query filters
   * @param {Object} options - Query options
   * @returns {Promise<Object>} Analytics results
   */
  async getAnalyticsOptimized(filters = {}, options = {}) {
    try {
      const {
        groupBy = 'day',
        dateRange = 30, // days
        metrics = ['revenue', 'orders', 'customers']
      } = options;

      const endDate = new Date();
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - dateRange);

      // Build aggregation pipeline for orders
      const orderPipeline = [
        {
          $match: {
            created_at: { $gte: startDate, $lte: endDate },
            order_status: { $ne: 'cancelled' },
            ...filters
          }
        },
        {
          $group: {
            _id: {
              $dateToString: {
                format: groupBy === 'day' ? '%Y-%m-%d' : '%Y-%m',
                date: '$created_at'
              }
            },
            revenue: { $sum: '$total_amount' },
            orders: { $sum: 1 },
            customers: { $addToSet: '$customer_email' }
          }
        },
        {
          $addFields: {
            customers: { $size: '$customers' }
          }
        },
        { $sort: { _id: 1 } }
      ];

      // Execute aggregation
      const Order = mongoose.model('Order');
      const analyticsData = await Order.aggregate(orderPipeline);

      // Calculate totals
      const totals = analyticsData.reduce((acc, item) => ({
        revenue: acc.revenue + item.revenue,
        orders: acc.orders + item.orders,
        customers: acc.customers + item.customers
      }), { revenue: 0, orders: 0, customers: 0 });

      const response = {
        data: analyticsData,
        totals,
        period: {
          startDate,
          endDate,
          days: dateRange
        }
      };

      logger.debug(`Analytics query executed: ${analyticsData.length} data points`);
      return response;

    } catch (error) {
      logger.error('Error in analytics query:', error);
      throw error;
    }
  }
}

// Create singleton instance
const queryOptimizationService = new QueryOptimizationService();

module.exports = queryOptimizationService;
