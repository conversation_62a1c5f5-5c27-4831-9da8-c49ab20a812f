const mongoose = require('mongoose');
const logger = require('../utils/logger');
const advancedCacheService = require('./advancedCacheService');

class DatabaseOptimizationService {
  constructor() {
    this.queryCache = new Map();
    this.indexAnalytics = new Map();
    this.slowQueryThreshold = 100; // 100ms
    this.cachePrefix = 'db_opt:';
    this.initialized = false;

    // Initialize optimization when database is ready
    this.initializeWhenReady();
  }

  /**
   * Initialize when database is ready
   */
  initializeWhenReady() {
    // Check if mongoose is already connected
    if (mongoose.connection.readyState === 1) {
      this.initializeOptimization();
    } else {
      // Wait for database connection
      mongoose.connection.once('connected', () => {
        this.initializeOptimization();
      });

      // Also listen for reconnection
      mongoose.connection.on('reconnected', () => {
        if (!this.initialized) {
          this.initializeOptimization();
        }
      });
    }
  }

  /**
   * Initialize database optimization
   */
  async initializeOptimization() {
    try {
      // Ensure we don't initialize multiple times
      if (this.initialized) {
        return;
      }

      // Wait a bit for connection to be fully ready
      await new Promise(resolve => setTimeout(resolve, 1000));

      await this.createOptimalIndexes();
      await this.setupQueryMonitoring();

      this.initialized = true;
      logger.info('Database optimization service initialized successfully');
    } catch (error) {
      logger.error('Database optimization initialization error:', error);
      // Retry after 5 seconds
      setTimeout(() => {
        if (!this.initialized) {
          this.initializeOptimization();
        }
      }, 5000);
    }
  }

  /**
   * Create optimal indexes for Phone Point Dar production
   */
  async createOptimalIndexes() {
    try {
      // Verify database connection
      if (!mongoose.connection.db) {
        throw new Error('Database connection not available');
      }

      const db = mongoose.connection.db;
      let indexesCreated = 0;
      let indexesFailed = 0;

      logger.info('Starting comprehensive index creation for Phone Point Dar...');

      // User collection indexes
      // email index already created by unique: true constraint in User schema
      await this.ensureIndex('users', { role: 1, status: 1 }) && indexesCreated++;
      await this.ensureIndex('users', { created_at: -1 }) && indexesCreated++;
      await this.ensureIndex('users', { last_login: -1 }) && indexesCreated++;
      await this.ensureIndex('users', { phone: 1 }) && indexesCreated++;

      // Product collection indexes (critical for Phone Point Dar)
      await this.ensureIndex('products', { is_active: 1, category: 1 }) && indexesCreated++;
      await this.ensureIndex('products', { brand: 1, model: 1 }) && indexesCreated++;
      await this.ensureIndex('products', { price: 1 }) && indexesCreated++;
      await this.ensureIndex('products', { created_at: -1 }) && indexesCreated++;
      await this.ensureIndex('products', { slug: 1 }, { unique: true }) && indexesCreated++;
      await this.ensureIndex('products', { 'variants.sku': 1 }) && indexesCreated++;
      await this.ensureIndex('products', { 'variants.price': 1 }) && indexesCreated++;
      await this.ensureIndex('products', { stock_quantity: 1, is_low_stock: 1 }) && indexesCreated++;

      // Phone-specific product indexes
      await this.ensureIndex('products', { category: 1, brand: 1, price: 1 }) && indexesCreated++;
      await this.ensureIndex('products', { 'specifications.storage': 1 }) && indexesCreated++;
      await this.ensureIndex('products', { 'specifications.color': 1 }) && indexesCreated++;
      await this.ensureIndex('products', { 'specifications.memory': 1 }) && indexesCreated++;

      // Lead collection indexes
      // email index already created by unique: true constraint in Lead schema
      await this.ensureIndex('leads', { source: 1, created_at: -1 }) && indexesCreated++;
      await this.ensureIndex('leads', { status: 1 }) && indexesCreated++;

      // Check for existing collections
      const collections = await db.listCollections().toArray();
      const collectionNames = collections.map(c => c.name);

      // Order collection indexes (critical for e-commerce)
      if (collectionNames.includes('orders')) {
        await this.ensureIndex('orders', { user: 1, order_status: 1 }) && indexesCreated++;
        await this.ensureIndex('orders', { order_status: 1, created_at: -1 }) && indexesCreated++;
        await this.ensureIndex('orders', { created_at: -1 }) && indexesCreated++;
        await this.ensureIndex('orders', { order_number: 1 }, { unique: true }) && indexesCreated++;
        await this.ensureIndex('orders', { customer_email: 1 }) && indexesCreated++;
        await this.ensureIndex('orders', { payment_status: 1 }) && indexesCreated++;
        await this.ensureIndex('orders', { 'items.product': 1 }) && indexesCreated++;
      }

      // Cart collection indexes
      if (collectionNames.includes('carts')) {
        await this.ensureIndex('carts', { user: 1 }, { unique: true }) && indexesCreated++;
        await this.ensureIndex('carts', { session_id: 1 }) && indexesCreated++;
        await this.ensureIndex('carts', { 'items.product': 1 }) && indexesCreated++;
        await this.ensureIndex('carts', { 'items.imei': 1 }, { sparse: true }) && indexesCreated++;
        await this.ensureIndex('carts', { updated_at: -1 }) && indexesCreated++;
      }

      // Inventory collection indexes (critical for Phone Point Dar)
      if (collectionNames.includes('inventories')) {
        await this.ensureIndex('inventories', { product: 1, variant_sku: 1 }) && indexesCreated++;
        await this.ensureIndex('inventories', { 'devices.imei': 1 }, { unique: true, sparse: true }) && indexesCreated++;
        await this.ensureIndex('inventories', { 'devices.status': 1 }) && indexesCreated++;
        await this.ensureIndex('inventories', { location: 1, quantity: 1 }) && indexesCreated++;
        await this.ensureIndex('inventories', { is_low_stock: 1 }) && indexesCreated++;
        await this.ensureIndex('inventories', { updated_at: -1 }) && indexesCreated++;
      }

      // Wishlist collection indexes
      if (collectionNames.includes('wishlists')) {
        await this.ensureIndex('wishlists', { user: 1 }, { unique: true }) && indexesCreated++;
        await this.ensureIndex('wishlists', { 'items.product': 1 }) && indexesCreated++;
        await this.ensureIndex('wishlists', { updated_at: -1 }) && indexesCreated++;
      }

      // Analytics collection indexes
      if (collectionNames.includes('analytics')) {
        await this.ensureIndex('analytics', { event_type: 1, timestamp: -1 }) && indexesCreated++;
        await this.ensureIndex('analytics', { user_id: 1, timestamp: -1 }) && indexesCreated++;
        await this.ensureIndex('analytics', { session_id: 1 }) && indexesCreated++;
        await this.ensureIndex('analytics', { product_id: 1, event_type: 1 }) && indexesCreated++;
      }

      // Trade-in collection indexes
      if (collectionNames.includes('tradeins')) {
        await this.ensureIndex('tradeins', { customer: 1 }) && indexesCreated++;
        await this.ensureIndex('tradeins', { imei: 1 }, { unique: true }) && indexesCreated++;
        await this.ensureIndex('tradeins', { status: 1, created_at: -1 }) && indexesCreated++;
        await this.ensureIndex('tradeins', { device_brand: 1, device_model: 1 }) && indexesCreated++;
      }

      // Shipping collection indexes
      if (collectionNames.includes('shippings')) {
        await this.ensureIndex('shippings', { order: 1 }, { unique: true }) && indexesCreated++;
        await this.ensureIndex('shippings', { tracking_number: 1 }) && indexesCreated++;
        await this.ensureIndex('shippings', { status: 1, updated_at: -1 }) && indexesCreated++;
        await this.ensureIndex('shippings', { carrier: 1 }) && indexesCreated++;
      }

      logger.info(`Database indexes created successfully: ${indexesCreated} indexes processed`);
    } catch (error) {
      logger.error('Index creation error:', error);
      throw error; // Re-throw to trigger retry logic
    }
  }

  /**
   * Ensure index exists
   * @param {string} collection - Collection name
   * @param {Object} indexSpec - Index specification
   * @param {Object} options - Index options
   * @returns {boolean} - True if index was created or already exists
   */
  async ensureIndex(collection, indexSpec, options = {}) {
    try {
      const db = mongoose.connection.db;
      await db.collection(collection).createIndex(indexSpec, options);
      logger.debug(`Index created for ${collection}:`, indexSpec);
      return true;
    } catch (error) {
      if (error.code === 11000 || error.message.includes('already exists') || error.message.includes('IndexOptionsConflict')) {
        // Index already exists or has different options, this is OK
        logger.debug(`Index already exists for ${collection}:`, indexSpec);
        return true;
      }
      logger.warn(`Index creation failed for ${collection}:`, error.message);
      return false;
    }
  }

  /**
   * Setup query monitoring
   */
  setupQueryMonitoring() {
    // Monitor slow queries
    mongoose.set('debug', (collectionName, method, query, doc) => {
      const startTime = Date.now();
      
      // Log slow queries
      setTimeout(() => {
        const duration = Date.now() - startTime;
        if (duration > this.slowQueryThreshold) {
          this.logSlowQuery(collectionName, method, query, duration);
        }
      }, 0);
    });
  }

  /**
   * Log slow query
   * @param {string} collection - Collection name
   * @param {string} method - Query method
   * @param {Object} query - Query object
   * @param {number} duration - Query duration
   */
  logSlowQuery(collection, method, query, duration) {
    const slowQuery = {
      collection,
      method,
      query: JSON.stringify(query),
      duration,
      timestamp: Date.now()
    };
    
    logger.warn('Slow query detected:', slowQuery);
    
    // Cache slow query for analysis
    const key = `${this.cachePrefix}slow_query:${Date.now()}`;
    advancedCacheService.set(key, slowQuery, advancedCacheService.ttl.extended);
  }

  /**
   * Optimize query with caching
   * @param {Function} queryFunction - Query function
   * @param {string} cacheKey - Cache key
   * @param {number} ttl - Cache TTL
   * @returns {Promise<any>} - Query result
   */
  async optimizedQuery(queryFunction, cacheKey, ttl = 300) {
    try {
      // Check cache first
      const cached = await advancedCacheService.get(cacheKey);
      if (cached) {
        return cached;
      }
      
      // Execute query
      const startTime = Date.now();
      const result = await queryFunction();
      const duration = Date.now() - startTime;
      
      // Log performance
      if (duration > this.slowQueryThreshold) {
        logger.warn('Slow optimized query:', { cacheKey, duration });
      }
      
      // Cache result
      await advancedCacheService.set(cacheKey, result, ttl);
      
      return result;
    } catch (error) {
      logger.error('Optimized query error:', { cacheKey, error: error.message });
      throw error;
    }
  }

  /**
   * Get user with caching
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - User data
   */
  async getUser(userId) {
    const cacheKey = `${this.cachePrefix}user:${userId}`;
    return this.optimizedQuery(
      () => mongoose.model('User').findById(userId).select('-password').lean(),
      cacheKey,
      1800 // 30 minutes
    );
  }

  /**
   * Get products with caching
   * @param {Object} filters - Query filters
   * @param {Object} options - Query options
   * @returns {Promise<Array>} - Products
   */
  async getProducts(filters = {}, options = {}) {
    const cacheKey = `${this.cachePrefix}products:${JSON.stringify({ filters, options })}`;
    return this.optimizedQuery(
      () => {
        const query = mongoose.model('Product').find(filters);
        if (options.sort) query.sort(options.sort);
        if (options.limit) query.limit(options.limit);
        if (options.skip) query.skip(options.skip);
        return query.lean();
      },
      cacheKey,
      600 // 10 minutes
    );
  }

  /**
   * Get user orders with caching
   * @param {string} userId - User ID
   * @param {Object} options - Query options
   * @returns {Promise<Array>} - Orders
   */
  async getUserOrders(userId, options = {}) {
    const cacheKey = `${this.cachePrefix}user_orders:${userId}:${JSON.stringify(options)}`;
    return this.optimizedQuery(
      () => {
        const query = mongoose.model('Order')
          .find({ user: userId })
          .populate('product', 'name price')
          .sort({ created_at: -1 });
        
        if (options.limit) query.limit(options.limit);
        return query.lean();
      },
      cacheKey,
      300 // 5 minutes
    );
  }

  /**
   * Get analytics data with caching
   * @param {Object} filters - Analytics filters
   * @param {string} groupBy - Group by field
   * @returns {Promise<Array>} - Analytics data
   */
  async getAnalytics(filters, groupBy = null) {
    const cacheKey = `${this.cachePrefix}analytics:${JSON.stringify({ filters, groupBy })}`;
    return this.optimizedQuery(
      () => {
        const pipeline = [{ $match: filters }];
        
        if (groupBy) {
          pipeline.push({
            $group: {
              _id: `$${groupBy}`,
              count: { $sum: 1 },
              timestamp: { $max: '$timestamp' }
            }
          });
        }
        
        return mongoose.model('Analytics').aggregate(pipeline);
      },
      cacheKey,
      900 // 15 minutes
    );
  }

  /**
   * Invalidate cache for user
   * @param {string} userId - User ID
   */
  async invalidateUserCache(userId) {
    const patterns = [
      `${this.cachePrefix}user:${userId}`,
      `${this.cachePrefix}user_orders:${userId}:*`
    ];
    
    for (const pattern of patterns) {
      await advancedCacheService.invalidatePattern(pattern);
    }
  }

  /**
   * Invalidate cache for products
   */
  async invalidateProductsCache() {
    await advancedCacheService.invalidatePattern(`${this.cachePrefix}products:*`);
  }

  /**
   * Get database statistics
   * @returns {Promise<Object>} - Database statistics
   */
  async getDatabaseStats() {
    try {
      const db = mongoose.connection.db;
      const stats = await db.stats();
      
      // Get collection stats
      const collections = await db.listCollections().toArray();
      const collectionStats = {};
      
      for (const collection of collections) {
        try {
          const collStats = await db.collection(collection.name).stats();
          collectionStats[collection.name] = {
            count: collStats.count,
            size: collStats.size,
            avgObjSize: collStats.avgObjSize,
            storageSize: collStats.storageSize,
            indexes: collStats.nindexes,
            indexSize: collStats.totalIndexSize
          };
        } catch (error) {
          // Some collections might not support stats
          collectionStats[collection.name] = { error: 'Stats not available' };
        }
      }
      
      return {
        database: {
          collections: stats.collections,
          dataSize: stats.dataSize,
          storageSize: stats.storageSize,
          indexes: stats.indexes,
          indexSize: stats.indexSize,
          avgObjSize: stats.avgObjSize
        },
        collections: collectionStats,
        connection: {
          readyState: mongoose.connection.readyState,
          host: mongoose.connection.host,
          port: mongoose.connection.port,
          name: mongoose.connection.name
        }
      };
    } catch (error) {
      logger.error('Database stats error:', error);
      return { error: error.message };
    }
  }

  /**
   * Analyze query performance
   * @returns {Promise<Object>} - Query performance analysis
   */
  async analyzeQueryPerformance() {
    try {
      // Get slow queries from cache
      const slowQueries = [];
      const pattern = `${this.cachePrefix}slow_query:*`;
      
      // This would need to be implemented with Redis SCAN
      // For now, return cached analytics
      const analytics = await advancedCacheService.getAnalytics('database', 'performance');
      
      return {
        slowQueries: slowQueries.length,
        averageQueryTime: analytics?.averageQueryTime || 0,
        totalQueries: analytics?.totalQueries || 0,
        cacheHitRate: analytics?.cacheHitRate || 0
      };
    } catch (error) {
      logger.error('Query performance analysis error:', error);
      return { error: error.message };
    }
  }

  /**
   * Optimize database performance
   * @returns {Promise<Object>} - Optimization results
   */
  async optimizePerformance() {
    try {
      const results = {
        indexesCreated: 0,
        cacheCleared: false,
        connectionsOptimized: false
      };
      
      // Recreate indexes
      await this.createOptimalIndexes();
      results.indexesCreated = 1;
      
      // Clear old cache entries
      await advancedCacheService.invalidatePattern(`${this.cachePrefix}*`);
      results.cacheCleared = true;
      
      // Optimize connection pool
      if (mongoose.connection.readyState === 1) {
        results.connectionsOptimized = true;
      }
      
      logger.info('Database performance optimization completed:', results);
      return results;
    } catch (error) {
      logger.error('Database optimization error:', error);
      return { error: error.message };
    }
  }

  /**
   * Health check for database optimization service
   * @returns {Promise<Object>} - Health status
   */
  async healthCheck() {
    try {
      const dbStats = await this.getDatabaseStats();
      const isHealthy = mongoose.connection.readyState === 1 && !dbStats.error;
      
      return {
        status: isHealthy ? 'healthy' : 'unhealthy',
        database: dbStats.connection,
        optimization: {
          cacheEnabled: true,
          indexesOptimized: true,
          monitoringActive: true
        }
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        error: error.message
      };
    }
  }
}

module.exports = new DatabaseOptimizationService();
