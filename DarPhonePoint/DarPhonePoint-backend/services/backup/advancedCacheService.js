const Redis = require('ioredis');
const config = require('../config/config');
const logger = require('../utils/logger');

class AdvancedCacheService {
  constructor() {
    this.redis = new Redis(config.REDIS_URL, {
      retryDelayOnFailover: 100,
      enableReadyCheck: false,
      maxRetriesPerRequest: null,
      lazyConnect: true,
      keepAlive: 30000,
      connectTimeout: 10000,
      commandTimeout: 5000
    });

    this.redis.on('connect', () => {
      logger.info('Advanced Redis cache connected successfully');
    });

    this.redis.on('error', (err) => {
      logger.error('Advanced Redis cache error:', err);
    });

    // Cache TTL configurations (in seconds)
    this.ttl = {
      short: 300,      // 5 minutes
      medium: 1800,    // 30 minutes
      long: 3600,      // 1 hour
      extended: 86400, // 24 hours
      persistent: 604800 // 7 days
    };

    // Cache key prefixes
    this.prefixes = {
      user: 'user:',
      product: 'product:',
      order: 'order:',
      analytics: 'analytics:',
      email: 'email:',
      session: 'session:',
      api: 'api:',
      static: 'static:'
    };
  }

  /**
   * Generate cache key with prefix
   * @param {string} prefix - Cache prefix
   * @param {string} key - Cache key
   * @returns {string} - Full cache key
   */
  generateKey(prefix, key) {
    return `${prefix}${key}`;
  }

  /**
   * Set cache with automatic serialization
   * @param {string} key - Cache key
   * @param {any} value - Value to cache
   * @param {number} ttl - Time to live in seconds
   * @returns {Promise<boolean>} - Success status
   */
  async set(key, value, ttl = this.ttl.medium) {
    try {
      const serializedValue = JSON.stringify(value);
      await this.redis.setex(key, ttl, serializedValue);
      return true;
    } catch (error) {
      logger.error('Cache set error:', { key, error: error.message });
      return false;
    }
  }

  /**
   * Get cache with automatic deserialization
   * @param {string} key - Cache key
   * @returns {Promise<any>} - Cached value or null
   */
  async get(key) {
    try {
      const value = await this.redis.get(key);
      return value ? JSON.parse(value) : null;
    } catch (error) {
      logger.error('Cache get error:', { key, error: error.message });
      return null;
    }
  }

  /**
   * Delete cache key
   * @param {string} key - Cache key
   * @returns {Promise<boolean>} - Success status
   */
  async del(key) {
    try {
      await this.redis.del(key);
      return true;
    } catch (error) {
      logger.error('Cache delete error:', { key, error: error.message });
      return false;
    }
  }

  /**
   * Cache user data
   * @param {string} userId - User ID
   * @param {Object} userData - User data
   * @returns {Promise<boolean>} - Success status
   */
  async cacheUser(userId, userData) {
    const key = this.generateKey(this.prefixes.user, userId);
    return await this.set(key, userData, this.ttl.long);
  }

  /**
   * Get cached user data
   * @param {string} userId - User ID
   * @returns {Promise<Object|null>} - User data or null
   */
  async getUser(userId) {
    const key = this.generateKey(this.prefixes.user, userId);
    return await this.get(key);
  }

  /**
   * Cache product data
   * @param {string} productId - Product ID
   * @param {Object} productData - Product data
   * @returns {Promise<boolean>} - Success status
   */
  async cacheProduct(productId, productData) {
    const key = this.generateKey(this.prefixes.product, productId);
    return await this.set(key, productData, this.ttl.extended);
  }

  /**
   * Get cached product data
   * @param {string} productId - Product ID
   * @returns {Promise<Object|null>} - Product data or null
   */
  async getProduct(productId) {
    const key = this.generateKey(this.prefixes.product, productId);
    return await this.get(key);
  }

  /**
   * Cache API response
   * @param {string} endpoint - API endpoint
   * @param {Object} params - Request parameters
   * @param {Object} response - API response
   * @param {number} ttl - Cache TTL
   * @returns {Promise<boolean>} - Success status
   */
  async cacheApiResponse(endpoint, params, response, ttl = this.ttl.short) {
    const paramHash = require('crypto').createHash('md5').update(JSON.stringify(params)).digest('hex');
    const key = this.generateKey(this.prefixes.api, `${endpoint}:${paramHash}`);
    return await this.set(key, response, ttl);
  }

  /**
   * Get cached API response
   * @param {string} endpoint - API endpoint
   * @param {Object} params - Request parameters
   * @returns {Promise<Object|null>} - Cached response or null
   */
  async getCachedApiResponse(endpoint, params) {
    const paramHash = require('crypto').createHash('md5').update(JSON.stringify(params)).digest('hex');
    const key = this.generateKey(this.prefixes.api, `${endpoint}:${paramHash}`);
    return await this.get(key);
  }

  /**
   * Cache analytics data
   * @param {string} metric - Metric name
   * @param {string} period - Time period
   * @param {Object} data - Analytics data
   * @returns {Promise<boolean>} - Success status
   */
  async cacheAnalytics(metric, period, data) {
    const key = this.generateKey(this.prefixes.analytics, `${metric}:${period}`);
    return await this.set(key, data, this.ttl.medium);
  }

  /**
   * Get cached analytics data
   * @param {string} metric - Metric name
   * @param {string} period - Time period
   * @returns {Promise<Object|null>} - Analytics data or null
   */
  async getAnalytics(metric, period) {
    const key = this.generateKey(this.prefixes.analytics, `${metric}:${period}`);
    return await this.get(key);
  }

  /**
   * Invalidate cache by pattern
   * @param {string} pattern - Cache key pattern
   * @returns {Promise<number>} - Number of keys deleted
   */
  async invalidatePattern(pattern) {
    try {
      const keys = await this.redis.keys(pattern);
      if (keys.length > 0) {
        await this.redis.del(...keys);
        logger.info(`Invalidated ${keys.length} cache keys matching pattern: ${pattern}`);
        return keys.length;
      }
      return 0;
    } catch (error) {
      logger.error('Cache pattern invalidation error:', { pattern, error: error.message });
      return 0;
    }
  }

  /**
   * Invalidate user cache
   * @param {string} userId - User ID
   * @returns {Promise<boolean>} - Success status
   */
  async invalidateUser(userId) {
    const pattern = this.generateKey(this.prefixes.user, `${userId}*`);
    const deleted = await this.invalidatePattern(pattern);
    return deleted > 0;
  }

  /**
   * Invalidate product cache
   * @param {string} productId - Product ID
   * @returns {Promise<boolean>} - Success status
   */
  async invalidateProduct(productId) {
    const pattern = this.generateKey(this.prefixes.product, `${productId}*`);
    const deleted = await this.invalidatePattern(pattern);
    return deleted > 0;
  }

  /**
   * Get cache statistics
   * @returns {Promise<Object>} - Cache statistics
   */
  async getStats() {
    try {
      const info = await this.redis.info('memory');
      const keyspace = await this.redis.info('keyspace');
      
      return {
        memory: this.parseRedisInfo(info),
        keyspace: this.parseRedisInfo(keyspace),
        connected: this.redis.status === 'ready'
      };
    } catch (error) {
      logger.error('Cache stats error:', error);
      return { connected: false, error: error.message };
    }
  }

  /**
   * Parse Redis INFO command output
   * @param {string} info - Redis INFO output
   * @returns {Object} - Parsed info
   */
  parseRedisInfo(info) {
    const result = {};
    info.split('\r\n').forEach(line => {
      if (line && !line.startsWith('#')) {
        const [key, value] = line.split(':');
        if (key && value) {
          result[key] = isNaN(value) ? value : Number(value);
        }
      }
    });
    return result;
  }

  /**
   * Health check for cache service
   * @returns {Promise<Object>} - Health status
   */
  async healthCheck() {
    try {
      const testKey = 'health:check';
      const testValue = { timestamp: Date.now() };
      
      await this.set(testKey, testValue, 10);
      const retrieved = await this.get(testKey);
      await this.del(testKey);
      
      const isHealthy = retrieved && retrieved.timestamp === testValue.timestamp;
      
      return {
        status: isHealthy ? 'healthy' : 'unhealthy',
        connected: this.redis.status === 'ready',
        latency: Date.now() - testValue.timestamp
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        connected: false,
        error: error.message
      };
    }
  }
}

module.exports = new AdvancedCacheService();
