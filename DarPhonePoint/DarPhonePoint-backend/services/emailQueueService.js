const Queue = require('bull');
const config = require('../config/config');
const logger = require('../utils/logger');
const emailSender = require('./emailSender');

// Create email queue
const emailQueue = new Queue('email-queue', {
  redis: {
    host: config.REDIS_HOST,
    port: config.REDIS_PORT,
    password: config.REDIS_PASSWORD
  },
  limiter: {
    max: 100, // Max 100 emails
    duration: 1000 * 60 // Per minute
  }
});

// Process email jobs
emailQueue.process(async (job) => {
  const { template, data, to, subject } = job.data;

  try {
    await emailSender.sendEmail({
      template,
      data,
      to,
      subject
    });

    // Log successful email
    logger.info('Email sent successfully', {
      template,
      to,
      subject,
      jobId: job.id
    });

    return { success: true };
  } catch (error) {
    logger.error('Email sending failed', {
      error: error.message,
      template,
      to,
      subject,
      jobId: job.id
    });

    // Retry logic based on error type
    if (error.code === 'RATE_LIMIT_EXCEEDED') {
      throw new Error('Rate limit exceeded');
    } else if (error.code === 'INVALID_EMAIL') {
      // Don't retry for invalid emails
      return { success: false, error: 'Invalid email address' };
    }

    // Retry for other errors
    throw error;
  }
});

// Handle failed jobs
emailQueue.on('failed', (job, error) => {
  logger.error('Email job failed', {
    jobId: job.id,
    error: error.message,
    attempts: job.attemptsMade
  });
});

// Handle completed jobs
emailQueue.on('completed', (job) => {
  logger.info('Email job completed', {
    jobId: job.id,
    attempts: job.attemptsMade
  });
});

// Add email to queue
const addToQueue = async (emailData) => {
  try {
    const job = await emailQueue.add(emailData, {
      attempts: 3,
      backoff: {
        type: 'exponential',
        delay: 1000 // Start with 1 second delay
      },
      removeOnComplete: true, // Remove completed jobs
      removeOnFail: false // Keep failed jobs for inspection
    });

    return job;
  } catch (error) {
    logger.error('Failed to add email to queue', {
      error: error.message,
      emailData
    });
    throw error;
  }
};

// Get queue statistics
const getQueueStats = async () => {
  const stats = await emailQueue.getJobCounts();
  return stats;
};

// Clean up old jobs
const cleanupOldJobs = async (days = 7) => {
  const timestamp = Date.now() - (days * 24 * 60 * 60 * 1000);

  try {
    const jobs = await emailQueue.getJobs(['completed', 'failed']);
    for (const job of jobs) {
      if (job.timestamp < timestamp) {
        await job.remove();
      }
    }
  } catch (error) {
    logger.error('Failed to cleanup old jobs', {
      error: error.message
    });
  }
};

module.exports = {
  addToQueue,
  getQueueStats,
  cleanupOldJobs,
  emailQueue
};