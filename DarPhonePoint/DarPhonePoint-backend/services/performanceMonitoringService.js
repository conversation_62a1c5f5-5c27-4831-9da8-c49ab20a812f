/**
 * Performance Monitoring Service for Phone Point Dar
 * Tracks API performance, database queries, and system metrics
 */

const logger = require('../utils/logger');
const cacheService = require('./cacheService');

class PerformanceMonitoringService {
  constructor() {
    this.metrics = {
      requests: {
        total: 0,
        successful: 0,
        failed: 0,
        averageResponseTime: 0
      },
      database: {
        queries: 0,
        averageQueryTime: 0,
        slowQueries: 0
      },
      cache: {
        hits: 0,
        misses: 0,
        hitRate: 0
      },
      memory: {
        usage: 0,
        peak: 0
      }
    };

    this.activeRequests = new Map();
    this.slowQueryThreshold = 1000; // 1 second
    this.slowRequestThreshold = 2000; // 2 seconds

    // Start periodic metrics collection
    this.startMetricsCollection();
  }

  /**
   * Start a performance measurement
   * @param {string} operationType - Type of operation (api, database, etc.)
   * @param {string} operationName - Name of the operation
   * @param {Object} metadata - Additional metadata
   * @returns {string} Measurement ID
   */
  startMeasurement(operationType, operationName, metadata = {}) {
    const measurementId = `${operationType}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const measurement = {
      id: measurementId,
      type: operationType,
      name: operationName,
      startTime: process.hrtime.bigint(),
      startTimestamp: new Date().toISOString(),
      metadata
    };

    this.activeRequests.set(measurementId, measurement);
    
    return measurementId;
  }

  /**
   * End a performance measurement
   * @param {string} measurementId - Measurement ID
   * @param {boolean} success - Whether the operation was successful
   * @param {Object} additionalData - Additional data to log
   * @returns {Object} Performance data
   */
  endMeasurement(measurementId, success = true, additionalData = {}) {
    const measurement = this.activeRequests.get(measurementId);
    
    if (!measurement) {
      logger.warn(`Performance measurement not found: ${measurementId}`);
      return null;
    }

    const endTime = process.hrtime.bigint();
    const duration = Number(endTime - measurement.startTime) / 1000000; // Convert to milliseconds

    const performanceData = {
      ...measurement,
      endTime,
      endTimestamp: new Date().toISOString(),
      duration,
      success,
      ...additionalData
    };

    // Remove from active requests
    this.activeRequests.delete(measurementId);

    // Update metrics
    this.updateMetrics(performanceData);

    // Log performance data
    this.logPerformanceData(performanceData);

    // Check for performance issues
    this.checkPerformanceThresholds(performanceData);

    return performanceData;
  }

  /**
   * Update performance metrics
   * @param {Object} performanceData - Performance data
   */
  updateMetrics(performanceData) {
    const { type, duration, success } = performanceData;

    switch (type) {
      case 'api':
        this.metrics.requests.total++;
        if (success) {
          this.metrics.requests.successful++;
        } else {
          this.metrics.requests.failed++;
        }
        this.updateAverageResponseTime(duration);
        break;

      case 'database':
        this.metrics.database.queries++;
        this.updateAverageDatabaseTime(duration);
        if (duration > this.slowQueryThreshold) {
          this.metrics.database.slowQueries++;
        }
        break;

      case 'cache':
        if (success) {
          this.metrics.cache.hits++;
        } else {
          this.metrics.cache.misses++;
        }
        this.updateCacheHitRate();
        break;
    }
  }

  /**
   * Update average response time
   * @param {number} duration - Request duration
   */
  updateAverageResponseTime(duration) {
    const { total, averageResponseTime } = this.metrics.requests;
    this.metrics.requests.averageResponseTime = 
      ((averageResponseTime * (total - 1)) + duration) / total;
  }

  /**
   * Update average database time
   * @param {number} duration - Query duration
   */
  updateAverageDatabaseTime(duration) {
    const { queries, averageQueryTime } = this.metrics.database;
    this.metrics.database.averageQueryTime = 
      ((averageQueryTime * (queries - 1)) + duration) / queries;
  }

  /**
   * Update cache hit rate
   */
  updateCacheHitRate() {
    const { hits, misses } = this.metrics.cache;
    const total = hits + misses;
    this.metrics.cache.hitRate = total > 0 ? (hits / total) * 100 : 0;
  }

  /**
   * Log performance data
   * @param {Object} performanceData - Performance data
   */
  logPerformanceData(performanceData) {
    const { type, name, duration, success } = performanceData;

    if (duration > this.getThresholdForType(type)) {
      logger.warn(`Slow ${type} operation detected:`, {
        name,
        duration: `${duration.toFixed(2)}ms`,
        success,
        threshold: `${this.getThresholdForType(type)}ms`
      });
    } else {
      logger.debug(`${type} operation completed:`, {
        name,
        duration: `${duration.toFixed(2)}ms`,
        success
      });
    }

    // Store performance data for analysis
    this.storePerformanceData(performanceData);
  }

  /**
   * Get threshold for operation type
   * @param {string} type - Operation type
   * @returns {number} Threshold in milliseconds
   */
  getThresholdForType(type) {
    const thresholds = {
      api: this.slowRequestThreshold,
      database: this.slowQueryThreshold,
      cache: 100,
      file: 500
    };
    return thresholds[type] || 1000;
  }

  /**
   * Check performance thresholds and alert if needed
   * @param {Object} performanceData - Performance data
   */
  checkPerformanceThresholds(performanceData) {
    const { type, name, duration } = performanceData;
    const threshold = this.getThresholdForType(type);

    if (duration > threshold) {
      this.handleSlowOperation(performanceData);
    }

    // Check for critical performance issues
    if (duration > threshold * 3) {
      this.handleCriticalPerformanceIssue(performanceData);
    }
  }

  /**
   * Handle slow operation
   * @param {Object} performanceData - Performance data
   */
  async handleSlowOperation(performanceData) {
    const { type, name, duration } = performanceData;

    logger.warn(`Slow ${type} operation:`, {
      operation: name,
      duration: `${duration.toFixed(2)}ms`,
      timestamp: new Date().toISOString()
    });

    // Store slow operation for analysis
    try {
      const key = `performance:slow:${type}:${Date.now()}`;
      await cacheService.set(key, performanceData, 86400); // Store for 24 hours
    } catch (error) {
      logger.error('Failed to store slow operation data:', error);
    }
  }

  /**
   * Handle critical performance issue
   * @param {Object} performanceData - Performance data
   */
  async handleCriticalPerformanceIssue(performanceData) {
    const { type, name, duration } = performanceData;

    logger.error(`CRITICAL PERFORMANCE ISSUE:`, {
      type,
      operation: name,
      duration: `${duration.toFixed(2)}ms`,
      timestamp: new Date().toISOString(),
      system: 'Phone Point Dar'
    });

    // Store critical issue
    try {
      const key = `performance:critical:${Date.now()}`;
      await cacheService.set(key, {
        ...performanceData,
        severity: 'critical',
        alertSent: new Date().toISOString()
      }, 604800); // Store for 7 days
    } catch (error) {
      logger.error('Failed to store critical performance issue:', error);
    }

    // In a real application, you would send alerts here
    // - Email to development team
    // - Slack/Discord notification
    // - PagerDuty alert
  }

  /**
   * Store performance data for analysis
   * @param {Object} performanceData - Performance data
   */
  async storePerformanceData(performanceData) {
    try {
      const key = `performance:data:${performanceData.type}:${Date.now()}`;
      await cacheService.set(key, performanceData, 3600); // Store for 1 hour
    } catch (error) {
      logger.error('Failed to store performance data:', error);
    }
  }

  /**
   * Start periodic metrics collection
   */
  startMetricsCollection() {
    // Collect system metrics every 30 seconds
    setInterval(() => {
      this.collectSystemMetrics();
    }, 30000);

    // Generate performance report every 5 minutes
    setInterval(() => {
      this.generatePerformanceReport();
    }, 300000);
  }

  /**
   * Collect system metrics
   */
  collectSystemMetrics() {
    try {
      const memoryUsage = process.memoryUsage();
      
      this.metrics.memory.usage = memoryUsage.heapUsed;
      this.metrics.memory.peak = Math.max(this.metrics.memory.peak, memoryUsage.heapUsed);

      // Log memory usage if it's high
      const memoryUsageMB = memoryUsage.heapUsed / 1024 / 1024;
      if (memoryUsageMB > 500) { // Alert if using more than 500MB
        logger.warn(`High memory usage: ${memoryUsageMB.toFixed(2)}MB`);
      }

      // Store system metrics
      this.storeSystemMetrics({
        memory: memoryUsage,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      logger.error('Failed to collect system metrics:', error);
    }
  }

  /**
   * Store system metrics
   * @param {Object} metrics - System metrics
   */
  async storeSystemMetrics(metrics) {
    try {
      const key = `performance:system:${Date.now()}`;
      await cacheService.set(key, metrics, 3600); // Store for 1 hour
    } catch (error) {
      logger.error('Failed to store system metrics:', error);
    }
  }

  /**
   * Generate performance report
   */
  async generatePerformanceReport() {
    try {
      const report = {
        timestamp: new Date().toISOString(),
        metrics: { ...this.metrics },
        activeRequests: this.activeRequests.size,
        systemHealth: this.getSystemHealthStatus()
      };

      logger.info('Performance Report:', report);

      // Store report
      const key = `performance:report:${Date.now()}`;
      await cacheService.set(key, report, 86400); // Store for 24 hours

    } catch (error) {
      logger.error('Failed to generate performance report:', error);
    }
  }

  /**
   * Get system health status
   * @returns {string} Health status
   */
  getSystemHealthStatus() {
    const { averageResponseTime } = this.metrics.requests;
    const { averageQueryTime } = this.metrics.database;
    const { hitRate } = this.metrics.cache;
    const memoryUsageMB = this.metrics.memory.usage / 1024 / 1024;

    if (averageResponseTime > 3000 || averageQueryTime > 2000 || memoryUsageMB > 1000) {
      return 'critical';
    } else if (averageResponseTime > 1500 || averageQueryTime > 1000 || hitRate < 50) {
      return 'warning';
    } else {
      return 'healthy';
    }
  }

  /**
   * Get performance metrics
   * @returns {Object} Current performance metrics
   */
  getMetrics() {
    return {
      ...this.metrics,
      activeRequests: this.activeRequests.size,
      systemHealth: this.getSystemHealthStatus(),
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Get performance summary for admin dashboard
   * @returns {Object} Performance summary
   */
  getPerformanceSummary() {
    const { requests, database, cache, memory } = this.metrics;
    
    return {
      requests: {
        total: requests.total,
        successRate: requests.total > 0 ? (requests.successful / requests.total) * 100 : 0,
        averageResponseTime: Math.round(requests.averageResponseTime)
      },
      database: {
        queries: database.queries,
        averageQueryTime: Math.round(database.averageQueryTime),
        slowQueryRate: database.queries > 0 ? (database.slowQueries / database.queries) * 100 : 0
      },
      cache: {
        hitRate: Math.round(cache.hitRate),
        totalOperations: cache.hits + cache.misses
      },
      memory: {
        currentUsageMB: Math.round(memory.usage / 1024 / 1024),
        peakUsageMB: Math.round(memory.peak / 1024 / 1024)
      },
      systemHealth: this.getSystemHealthStatus(),
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Express middleware for API performance monitoring
   */
  createAPIMonitoringMiddleware() {
    return (req, res, next) => {
      const measurementId = this.startMeasurement('api', `${req.method} ${req.route?.path || req.path}`, {
        method: req.method,
        path: req.path,
        ip: req.ip,
        userAgent: req.get('User-Agent')
      });

      // Store measurement ID in request for later use
      req.performanceMeasurementId = measurementId;

      // Override res.end to capture response
      const originalEnd = res.end;
      res.end = function(...args) {
        const success = res.statusCode < 400;
        
        performanceMonitoringService.endMeasurement(measurementId, success, {
          statusCode: res.statusCode,
          responseSize: res.get('Content-Length') || 0
        });

        originalEnd.apply(this, args);
      };

      next();
    };
  }
}

// Create singleton instance
const performanceMonitoringService = new PerformanceMonitoringService();

module.exports = performanceMonitoringService;
