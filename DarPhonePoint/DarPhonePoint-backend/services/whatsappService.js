/**
 * WhatsApp Service for Phone Point Dar
 * Handles WhatsApp Business API integration for customer communication
 */

const axios = require('axios');
const logger = require('../utils/logger');
const AppError = require('../utils/AppError');

class WhatsAppService {
  constructor() {
    this.config = {
      apiUrl: process.env.WHATSAPP_API_URL || 'https://graph.facebook.com/v18.0',
      accessToken: process.env.WHATSAPP_ACCESS_TOKEN,
      phoneNumberId: process.env.WHATSAPP_PHONE_NUMBER_ID,
      businessAccountId: process.env.WHATSAPP_BUSINESS_ACCOUNT_ID,
      webhookVerifyToken: process.env.WHATSAPP_WEBHOOK_VERIFY_TOKEN
    };
    
    this.isEnabled = process.env.WHATSAPP_ENABLED === 'true' && this.config.accessToken;
  }

  /**
   * Format phone number for WhatsApp
   * @param {string} phone - Phone number
   * @returns {string} Formatted phone number
   */
  formatPhoneNumber(phone) {
    if (!phone) return null;
    
    // Remove all non-digit characters
    let cleaned = phone.replace(/\D/g, '');
    
    // Handle different formats
    if (cleaned.startsWith('0')) {
      // Convert 0XXXXXXXXX to 255XXXXXXXXX
      cleaned = '255' + cleaned.substring(1);
    } else if (!cleaned.startsWith('255') && cleaned.length === 9) {
      // Add country code
      cleaned = '255' + cleaned;
    }
    
    return cleaned;
  }

  /**
   * Send WhatsApp message
   * @param {string} phone - Phone number
   * @param {string} message - Message text
   * @param {string} type - Message type (text, template)
   * @returns {Promise<Object>} Message result
   */
  async sendMessage(phone, message, type = 'text') {
    if (!this.isEnabled) {
      logger.info('WhatsApp service is disabled, skipping message send');
      return { success: false, reason: 'WhatsApp service disabled' };
    }

    if (!phone || !message) {
      throw new AppError('Phone number and message are required', 400);
    }

    try {
      const formattedPhone = this.formatPhoneNumber(phone);
      const url = `${this.config.apiUrl}/${this.config.phoneNumberId}/messages`;
      
      const payload = {
        messaging_product: 'whatsapp',
        to: formattedPhone,
        type: type,
        text: {
          body: message
        }
      };

      const response = await axios.post(url, payload, {
        headers: {
          'Authorization': `Bearer ${this.config.accessToken}`,
          'Content-Type': 'application/json'
        },
        timeout: 10000
      });

      if (response.data.messages && response.data.messages[0].id) {
        logger.info('WhatsApp message sent successfully:', {
          phone: formattedPhone,
          message_id: response.data.messages[0].id
        });
        
        return {
          success: true,
          messageId: response.data.messages[0].id,
          phone: formattedPhone
        };
      } else {
        throw new Error('WhatsApp message sending failed');
      }
    } catch (error) {
      logger.error('WhatsApp message sending failed:', error);
      throw new AppError(`WhatsApp message sending failed: ${error.message}`, 500);
    }
  }

  /**
   * Send WhatsApp template message
   * @param {string} phone - Phone number
   * @param {string} templateName - Template name
   * @param {Array} parameters - Template parameters
   * @returns {Promise<Object>} Message result
   */
  async sendTemplateMessage(phone, templateName, parameters = []) {
    if (!this.isEnabled) {
      logger.info('WhatsApp service is disabled, skipping template message send');
      return { success: false, reason: 'WhatsApp service disabled' };
    }

    try {
      const formattedPhone = this.formatPhoneNumber(phone);
      const url = `${this.config.apiUrl}/${this.config.phoneNumberId}/messages`;
      
      const payload = {
        messaging_product: 'whatsapp',
        to: formattedPhone,
        type: 'template',
        template: {
          name: templateName,
          language: {
            code: 'sw' // Swahili
          },
          components: parameters.length > 0 ? [{
            type: 'body',
            parameters: parameters.map(param => ({
              type: 'text',
              text: param
            }))
          }] : []
        }
      };

      const response = await axios.post(url, payload, {
        headers: {
          'Authorization': `Bearer ${this.config.accessToken}`,
          'Content-Type': 'application/json'
        },
        timeout: 10000
      });

      if (response.data.messages && response.data.messages[0].id) {
        logger.info('WhatsApp template message sent successfully:', {
          phone: formattedPhone,
          template: templateName,
          message_id: response.data.messages[0].id
        });
        
        return {
          success: true,
          messageId: response.data.messages[0].id,
          phone: formattedPhone,
          template: templateName
        };
      } else {
        throw new Error('WhatsApp template message sending failed');
      }
    } catch (error) {
      logger.error('WhatsApp template message sending failed:', error);
      throw new AppError(`WhatsApp template message sending failed: ${error.message}`, 500);
    }
  }

  /**
   * Send order confirmation WhatsApp message
   * @param {Object} order - Order object
   * @param {Object} user - User object
   * @returns {Promise<Object>} Message result
   */
  async sendOrderConfirmation(order, user) {
    if (!user.phone) {
      logger.info('User has no phone number, skipping WhatsApp');
      return { success: false, reason: 'No phone number' };
    }

    const message = `🎉 *Habari ${user.name}!*

Oda yako imepokewa kikamilifu!

📱 *Maelezo ya Oda:*
• Namba ya Oda: *${order.order_number}*
• Jumla: *TZS ${order.total.toLocaleString()}*
• Njia ya Malipo: *${order.payment_method.toUpperCase()}*

📦 *Bidhaa:*
${order.items.map(item => `• ${item.name} (${item.quantity}x)`).join('\n')}

⏰ Tutakupigia simu ndani ya dakika 30 kwa maelezo zaidi ya malipo na utoaji.

Asante kwa kuchagua *Phone Point Dar* - Simu bora, bei nzuri! 📱✨`;

    return await this.sendMessage(user.phone, message);
  }

  /**
   * Send order shipped WhatsApp message
   * @param {Object} order - Order object
   * @param {Object} user - User object
   * @returns {Promise<Object>} Message result
   */
  async sendOrderShipped(order, user) {
    if (!user.phone) {
      logger.info('User has no phone number, skipping WhatsApp');
      return { success: false, reason: 'No phone number' };
    }

    const trackingInfo = order.tracking_number ? 
      `\n🔍 *Namba ya Ufuatiliaji:* ${order.tracking_number}` : '';

    const message = `🚚 *Habari ${user.name}!*

Oda yako imetumwa! 📦

📱 *Maelezo ya Oda:*
• Namba ya Oda: *${order.order_number}*
• Hali: *Imetumwa*${trackingInfo}

📍 Oda yako inakuja kwako. Tutakupigia simu kabla ya kufika.

⏰ *Muda wa Kufika:* Siku 1-3 za kazi

Asante kwa uvumilivu wako! *Phone Point Dar* 📱✨`;

    return await this.sendMessage(user.phone, message);
  }

  /**
   * Send order delivered WhatsApp message
   * @param {Object} order - Order object
   * @param {Object} user - User object
   * @returns {Promise<Object>} Message result
   */
  async sendOrderDelivered(order, user) {
    if (!user.phone) {
      logger.info('User has no phone number, skipping WhatsApp');
      return { success: false, reason: 'No phone number' };
    }

    const message = `✅ *Habari ${user.name}!*

Oda yako imefika salama! 🎉

📱 *Maelezo ya Oda:*
• Namba ya Oda: *${order.order_number}*
• Hali: *Imefika*

🌟 *Je, umeridhika na huduma yetu?*
Tupe review kwenye Google na utuambie jinsi tulivyokufanya!

🔧 *Warranty:* Simu yako ina warranty ya miezi 12
📞 *Msaada:* Kama una swali lolote, tupigie simu

Asante kwa kuchagua *Phone Point Dar*! 📱✨

_Karibu tena!_ 🙏`;

    return await this.sendMessage(user.phone, message);
  }

  /**
   * Send payment reminder WhatsApp message
   * @param {Object} order - Order object
   * @param {Object} user - User object
   * @returns {Promise<Object>} Message result
   */
  async sendPaymentReminder(order, user) {
    if (!user.phone) {
      logger.info('User has no phone number, skipping WhatsApp');
      return { success: false, reason: 'No phone number' };
    }

    const message = `💰 *Habari ${user.name}!*

Oda yako inasubiri malipo.

📱 *Maelezo ya Oda:*
• Namba ya Oda: *${order.order_number}*
• Jumla: *TZS ${order.total.toLocaleString()}*

💳 *Njia za Malipo:*
• M-Pesa: *Lipa kwa M-Pesa*
• Tigo Pesa: *Lipa kwa Tigo Pesa*
• Airtel Money: *Lipa kwa Airtel Money*

📞 Tutakupigia simu kwa maelezo zaidi ya malipo.

*Phone Point Dar* - Simu bora, bei nzuri! 📱✨`;

    return await this.sendMessage(user.phone, message);
  }

  /**
   * Send warranty reminder WhatsApp message
   * @param {Object} serialNumber - Serial number object
   * @param {Object} user - User object
   * @returns {Promise<Object>} Message result
   */
  async sendWarrantyReminder(serialNumber, user) {
    if (!user.phone) {
      logger.info('User has no phone number, skipping WhatsApp');
      return { success: false, reason: 'No phone number' };
    }

    const daysLeft = Math.ceil((serialNumber.warrantyEndDate - new Date()) / (1000 * 60 * 60 * 24));
    
    const message = `⚠️ *Habari ${user.name}!*

Warranty ya simu yako inakaribia kuisha.

📱 *Maelezo ya Simu:*
• IMEI: *${serialNumber.imei}*
• Warranty itaisha: *Siku ${daysLeft}*

🔧 *Kama una tatizo lolote:*
• Lete simu yako haraka kabla warranty haijaisha
• Tutakusaidia bila malipo

📞 Wasiliana nasi: *Phone Point Dar*

_Asante kwa kuchagua huduma zetu!_ 📱✨`;

    return await this.sendMessage(user.phone, message);
  }

  /**
   * Handle incoming WhatsApp webhook
   * @param {Object} webhookData - Webhook data from WhatsApp
   * @returns {Promise<Object>} Processing result
   */
  async handleWebhook(webhookData) {
    try {
      if (webhookData.entry && webhookData.entry[0].changes) {
        const changes = webhookData.entry[0].changes[0];
        
        if (changes.field === 'messages' && changes.value.messages) {
          const message = changes.value.messages[0];
          const from = message.from;
          const messageText = message.text?.body;
          
          logger.info('Received WhatsApp message:', {
            from,
            message: messageText,
            timestamp: message.timestamp
          });
          
          // Handle customer service automation
          await this.handleCustomerMessage(from, messageText);
        }
      }
      
      return { success: true };
    } catch (error) {
      logger.error('WhatsApp webhook handling failed:', error);
      throw error;
    }
  }

  /**
   * Handle customer service automation
   * @param {string} from - Customer phone number
   * @param {string} message - Customer message
   * @returns {Promise<void>}
   */
  async handleCustomerMessage(from, message) {
    if (!message) return;
    
    const lowerMessage = message.toLowerCase();
    
    // Auto-responses for common queries
    if (lowerMessage.includes('bei') || lowerMessage.includes('price')) {
      await this.sendMessage(from, `📱 *Bei za Simu - Phone Point Dar*

Tunatoa simu za kila aina na bei nzuri:
• iPhone: Kuanzia TZS 800,000
• Samsung: Kuanzia TZS 300,000  
• Xiaomi: Kuanzia TZS 200,000

📍 Tembelea duka letu au tupigie simu kwa maelezo zaidi!`);
    } else if (lowerMessage.includes('warranty') || lowerMessage.includes('dhamana')) {
      await this.sendMessage(from, `🔧 *Warranty - Phone Point Dar*

Simu zote zina warranty ya miezi 12:
• Tatizo la hardware: Tunarekebishia bure
• Tatizo la software: Tunarekebishia bure
• Kuvunjika kwa makosa: Bei ya punguzo

📞 Lete simu yako na receipt kwa msaada!`);
    } else if (lowerMessage.includes('location') || lowerMessage.includes('mahali')) {
      await this.sendMessage(from, `📍 *Mahali tulipo - Phone Point Dar*

Duka letu liko:
• Dar es Salaam, Tanzania
• Karibu na kituo cha basi

🕒 *Masaa ya kufungua:*
• Jumatatu - Jumamosi: 8:00 - 18:00
• Jumapili: 10:00 - 16:00

Karibu sana! 🙏`);
    } else {
      // General response for other messages
      await this.sendMessage(from, `🙏 *Asante kwa kutuandikia!*

Tumepokea ujumbe wako. Mtu wa huduma za wateja atakujibu hivi karibuni.

📞 *Kwa haraka zaidi:*
Tupigie simu moja kwa moja.

*Phone Point Dar* - Simu bora, bei nzuri! 📱✨`);
    }
  }

  /**
   * Get WhatsApp service status
   * @returns {Object} Service status
   */
  getStatus() {
    return {
      enabled: this.isEnabled,
      configured: {
        accessToken: !!this.config.accessToken,
        phoneNumberId: !!this.config.phoneNumberId,
        businessAccountId: !!this.config.businessAccountId
      },
      apiUrl: this.config.apiUrl
    };
  }
}

module.exports = new WhatsAppService();
