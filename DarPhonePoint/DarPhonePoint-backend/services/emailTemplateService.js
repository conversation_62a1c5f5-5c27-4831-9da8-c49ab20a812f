const fs = require('fs').promises;
const path = require('path');
const handlebars = require('handlebars');
const logger = require('../utils/logger');

class EmailTemplateService {
  constructor() {
    this.templates = new Map();
    this.templateDir = path.join(__dirname, '../templates/email');
    this.registerHelpers();
  }

  /**
   * Register Handlebars helpers
   */
  registerHelpers() {
    handlebars.registerHelper('formatDate', (date) => {
      return new Date(date).toLocaleDateString();
    });

    handlebars.registerHelper('formatCurrency', (amount, currency = 'USD') => {
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency
      }).format(amount);
    });

    handlebars.registerHelper('ifEquals', function(arg1, arg2, options) {
      return (arg1 == arg2) ? options.fn(this) : options.inverse(this);
    });
  }

  /**
   * Load all email templates
   */
  async loadTemplates() {
    try {
      const files = await fs.readdir(this.templateDir);
      
      for (const file of files) {
        if (file.endsWith('.hbs')) {
          const templateName = path.basename(file, '.hbs');
          const templatePath = path.join(this.templateDir, file);
          const templateContent = await fs.readFile(templatePath, 'utf-8');
          
          this.templates.set(templateName, handlebars.compile(templateContent));
        }
      }

      logger.info('Email templates loaded successfully');
    } catch (error) {
      logger.error('Failed to load email templates', { error: error.message });
      throw error;
    }
  }

  /**
   * Get compiled template
   * @param {string} templateName - Name of the template
   * @returns {Function} Compiled template
   */
  getTemplate(templateName) {
    const template = this.templates.get(templateName);
    if (!template) {
      throw new Error(`Template '${templateName}' not found`);
    }
    return template;
  }

  /**
   * Render email template
   * @param {string} templateName - Name of the template
   * @param {Object} data - Template data
   * @returns {string} Rendered HTML
   */
  render(templateName, data) {
    try {
      const template = this.getTemplate(templateName);
      return template(data);
    } catch (error) {
      logger.error('Failed to render email template', {
        templateName,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Get available templates
   * @returns {string[]} List of template names
   */
  getAvailableTemplates() {
    return Array.from(this.templates.keys());
  }
}

module.exports = new EmailTemplateService(); 