/**
 * Mock Payment Service for Automated Testing
 * Provides a testing-friendly payment service that bypasses external dependencies
 */

const { v4: uuidv4 } = require('uuid');
const logger = require('../utils/logger');

class MockPaymentService {
  constructor() {
    this.isTestMode = process.env.NODE_ENV === 'test' || process.env.ENABLE_MOCK_PAYMENTS === 'true';
    this.mockPayments = new Map(); // In-memory storage for test payments
  }

  /**
   * Initialize a mock payment for testing
   */
  async initializePayment(paymentData) {
    if (!this.isTestMode) {
      throw new Error('Mock payment service only available in test mode');
    }

    const transactionId = `MOCK-${uuidv4()}`;
    const mockPayment = {
      transaction_id: transactionId,
      amount: paymentData.amount,
      currency: paymentData.currency || 'TZS',
      status: 'pending',
      method: paymentData.method,
      phone: paymentData.phone,
      orderId: paymentData.orderId,
      customerName: paymentData.customerName,
      customerEmail: paymentData.customerEmail,
      created_at: new Date(),
      provider_reference: `MOCK-REF-${Date.now()}`,
      payment_instructions: {
        message: 'Mock payment for testing - automatically succeeds after 2 seconds',
        expires_at: new Date(Date.now() + 15 * 60 * 1000), // 15 minutes
        checkout_url: `http://localhost:5173/payment/mock/${transactionId}`
      }
    };

    // Store mock payment
    this.mockPayments.set(transactionId, mockPayment);

    // Auto-complete payment after 2 seconds for testing
    setTimeout(() => {
      this.completePayment(transactionId);
    }, 2000);

    logger.info('Mock payment initialized:', {
      transaction_id: transactionId,
      amount: paymentData.amount,
      method: paymentData.method
    });

    return mockPayment;
  }

  /**
   * Complete a mock payment (simulates successful payment)
   */
  async completePayment(transactionId) {
    const payment = this.mockPayments.get(transactionId);
    if (!payment) {
      throw new Error('Mock payment not found');
    }

    payment.status = 'completed';
    payment.completed_at = new Date();
    payment.provider_transaction_id = `MOCK-PROVIDER-${Date.now()}`;

    this.mockPayments.set(transactionId, payment);

    logger.info('Mock payment completed:', {
      transaction_id: transactionId,
      status: 'completed'
    });

    return payment;
  }

  /**
   * Get payment status
   */
  async getPaymentStatus(transactionId) {
    const payment = this.mockPayments.get(transactionId);
    if (!payment) {
      return { status: 'not_found' };
    }

    return {
      transaction_id: transactionId,
      status: payment.status,
      amount: payment.amount,
      currency: payment.currency,
      provider_reference: payment.provider_reference,
      completed_at: payment.completed_at
    };
  }

  /**
   * Process mock webhook (simulates payment gateway callback)
   */
  async processWebhook(webhookData, signature) {
    if (!this.isTestMode) {
      throw new Error('Mock webhook processing only available in test mode');
    }

    // Simulate webhook validation
    const isValidSignature = this.validateMockSignature(webhookData, signature);
    if (!isValidSignature) {
      throw new Error('Invalid webhook signature');
    }

    const transactionId = webhookData.request_id || webhookData.transaction_id;
    const payment = this.mockPayments.get(transactionId);

    if (!payment) {
      throw new Error('Payment not found for webhook');
    }

    // Update payment status based on webhook
    payment.status = webhookData.status === 'success' ? 'completed' : 'failed';
    payment.webhook_received_at = new Date();
    payment.webhook_data = webhookData;

    this.mockPayments.set(transactionId, payment);

    logger.info('Mock webhook processed:', {
      transaction_id: transactionId,
      status: payment.status,
      webhook_status: webhookData.status
    });

    return {
      paymentId: transactionId,
      status: payment.status,
      amount: payment.amount,
      currency: payment.currency,
      verified: true
    };
  }

  /**
   * Validate mock webhook signature (always returns true for testing)
   */
  validateMockSignature(data, signature) {
    // In real implementation, this would validate HMAC signature
    // For testing, we just check if signature exists
    return signature && signature.startsWith('mock-signature-');
  }

  /**
   * Generate mock webhook data for testing
   */
  generateMockWebhook(transactionId, status = 'success') {
    const payment = this.mockPayments.get(transactionId);
    if (!payment) {
      throw new Error('Payment not found');
    }

    return {
      request_id: transactionId,
      transaction_id: `PROVIDER-${Date.now()}`,
      status: status,
      amount: payment.amount,
      currency: payment.currency,
      phone: payment.phone,
      timestamp: new Date().toISOString(),
      signature: `mock-signature-${transactionId}`
    };
  }

  /**
   * Clear all mock payments (for test cleanup)
   */
  clearMockPayments() {
    this.mockPayments.clear();
    logger.info('Mock payments cleared');
  }

  /**
   * Get all mock payments (for testing/debugging)
   */
  getAllMockPayments() {
    return Array.from(this.mockPayments.values());
  }

  /**
   * Check if service is in test mode
   */
  isInTestMode() {
    return this.isTestMode;
  }
}

// Export singleton instance
module.exports = new MockPaymentService();
