/**
 * JWT Token Blacklist Service
 * Manages blacklisted JWT tokens to prevent reuse after logout or security events
 */

const redisClient = require('../utils/redisClient');
const jwt = require('jsonwebtoken');
const config = require('../config/config');
const logger = require('../utils/logger');

class JWTBlacklistService {
  constructor() {
    this.BLACKLIST_PREFIX = 'jwt_blacklist:';
    this.SESSION_PREFIX = 'user_session:';
  }

  /**
   * Add token to blacklist
   * @param {string} token - JWT token to blacklist
   * @param {string} reason - Reason for blacklisting (logout, security, etc.)
   * @returns {Promise<boolean>}
   */
  async blacklistToken(token, reason = 'logout') {
    try {
      // Decode token to get expiration time
      const decoded = jwt.decode(token);
      if (!decoded || !decoded.exp) {
        logger.warn('Invalid token format for blacklisting');
        return false;
      }

      // Calculate TTL (time until token expires)
      const now = Math.floor(Date.now() / 1000);
      const ttl = decoded.exp - now;

      // Only blacklist if token hasn't expired
      if (ttl > 0) {
        const key = `${this.BLACKLIST_PREFIX}${token}`;
        await redisClient.setex(key, ttl, JSON.stringify({
          blacklisted_at: new Date().toISOString(),
          reason,
          user_id: decoded.user?.id || decoded.id,
          expires_at: new Date(decoded.exp * 1000).toISOString()
        }));

        logger.info(`Token blacklisted for user ${decoded.user?.id || decoded.id}`, {
          reason,
          expires_at: new Date(decoded.exp * 1000).toISOString()
        });
      }

      return true;
    } catch (error) {
      logger.error('Error blacklisting token:', error);
      return false;
    }
  }

  /**
   * Check if token is blacklisted
   * @param {string} token - JWT token to check
   * @returns {Promise<boolean>}
   */
  async isTokenBlacklisted(token) {
    try {
      const key = `${this.BLACKLIST_PREFIX}${token}`;
      const result = await redisClient.get(key);
      return result !== null;
    } catch (error) {
      logger.error('Error checking token blacklist:', error);
      // Fail secure - if we can't check, assume it's blacklisted
      return true;
    }
  }

  /**
   * Blacklist all tokens for a user (security event)
   * @param {string} userId - User ID
   * @param {string} reason - Reason for blacklisting
   * @returns {Promise<boolean>}
   */
  async blacklistAllUserTokens(userId, reason = 'security_event') {
    try {
      // Get all active sessions for user
      const sessionKey = `${this.SESSION_PREFIX}${userId}`;
      const sessions = await redisClient.smembers(sessionKey);

      // Blacklist each token
      const promises = sessions.map(token => this.blacklistToken(token, reason));
      await Promise.all(promises);

      // Clear user sessions
      await redisClient.del(sessionKey);

      logger.info(`All tokens blacklisted for user ${userId}`, { reason });
      return true;
    } catch (error) {
      logger.error('Error blacklisting all user tokens:', error);
      return false;
    }
  }

  /**
   * Track active session for user
   * @param {string} userId - User ID
   * @param {string} token - JWT token
   * @returns {Promise<boolean>}
   */
  async trackUserSession(userId, token) {
    try {
      const sessionKey = `${this.SESSION_PREFIX}${userId}`;
      
      // Add token to user's active sessions
      await redisClient.sadd(sessionKey, token);
      
      // Set expiration for session tracking (same as JWT expiration)
      const decoded = jwt.decode(token);
      if (decoded && decoded.exp) {
        const ttl = decoded.exp - Math.floor(Date.now() / 1000);
        if (ttl > 0) {
          await redisClient.expire(sessionKey, ttl);
        }
      }

      return true;
    } catch (error) {
      logger.error('Error tracking user session:', error);
      return false;
    }
  }

  /**
   * Remove session tracking for token
   * @param {string} userId - User ID
   * @param {string} token - JWT token
   * @returns {Promise<boolean>}
   */
  async removeUserSession(userId, token) {
    try {
      const sessionKey = `${this.SESSION_PREFIX}${userId}`;
      await redisClient.srem(sessionKey, token);
      return true;
    } catch (error) {
      logger.error('Error removing user session:', error);
      return false;
    }
  }

  /**
   * Get active session count for user
   * @param {string} userId - User ID
   * @returns {Promise<number>}
   */
  async getUserSessionCount(userId) {
    try {
      const sessionKey = `${this.SESSION_PREFIX}${userId}`;
      return await redisClient.scard(sessionKey);
    } catch (error) {
      logger.error('Error getting user session count:', error);
      return 0;
    }
  }

  /**
   * Enforce concurrent session limit
   * @param {string} userId - User ID
   * @param {number} maxSessions - Maximum allowed sessions
   * @returns {Promise<boolean>}
   */
  async enforceConcurrentSessionLimit(userId, maxSessions = 5) {
    try {
      const sessionKey = `${this.SESSION_PREFIX}${userId}`;
      const sessionCount = await redisClient.scard(sessionKey);

      if (sessionCount >= maxSessions) {
        // Get oldest sessions and blacklist them
        const sessions = await redisClient.smembers(sessionKey);
        const sessionsToRemove = sessions.slice(0, sessionCount - maxSessions + 1);

        for (const token of sessionsToRemove) {
          await this.blacklistToken(token, 'session_limit_exceeded');
          await redisClient.srem(sessionKey, token);
        }

        logger.info(`Enforced session limit for user ${userId}`, {
          removed_sessions: sessionsToRemove.length,
          max_sessions: maxSessions
        });
      }

      return true;
    } catch (error) {
      logger.error('Error enforcing session limit:', error);
      return false;
    }
  }
}

module.exports = new JWTBlacklistService();
