/**
 * Input Sanitization Service
 * Provides comprehensive input sanitization and validation
 */

const xss = require('xss');
const { JSDOM } = require('jsdom');
const DOMPurify = require('dompurify');
const validator = require('validator');
const logger = require('../utils/logger');

// Create DOMPurify instance
const window = new JSDOM('').window;
const purify = DOMPurify(window);

class InputSanitizationService {
  constructor() {
    // Configure XSS options
    this.xssOptions = {
      whiteList: {
        // Allow basic formatting tags for rich text
        p: [],
        br: [],
        strong: [],
        b: [],
        em: [],
        i: [],
        u: [],
        ul: [],
        ol: [],
        li: [],
        h1: [],
        h2: [],
        h3: [],
        h4: [],
        h5: [],
        h6: []
      },
      stripIgnoreTag: true,
      stripIgnoreTagBody: ['script', 'style'],
      allowCommentTag: false,
      onIgnoreTag: (tag, html, options) => {
        logger.warn(`Potentially malicious tag removed: ${tag}`);
        return '';
      },
      onIgnoreTagAttr: (tag, name, value, isWhiteAttr) => {
        logger.warn(`Potentially malicious attribute removed: ${name}="${value}" from ${tag}`);
        return '';
      }
    };

    // Configure DOMPurify
    this.purifyConfig = {
      ALLOWED_TAGS: ['p', 'br', 'strong', 'b', 'em', 'i', 'u', 'ul', 'ol', 'li', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6'],
      ALLOWED_ATTR: [],
      KEEP_CONTENT: true,
      RETURN_DOM: false,
      RETURN_DOM_FRAGMENT: false,
      RETURN_DOM_IMPORT: false
    };
  }

  /**
   * Sanitize HTML content
   * @param {string} input - HTML content to sanitize
   * @param {boolean} allowBasicFormatting - Allow basic HTML formatting
   * @returns {string} Sanitized HTML
   */
  sanitizeHtml(input, allowBasicFormatting = false) {
    if (!input || typeof input !== 'string') {
      return '';
    }

    try {
      if (allowBasicFormatting) {
        // Use DOMPurify for rich text content
        return purify.sanitize(input, this.purifyConfig);
      } else {
        // Strip all HTML for plain text fields
        return xss(input, { whiteList: {} });
      }
    } catch (error) {
      logger.error('HTML sanitization error:', error);
      return validator.escape(input); // Fallback to basic escaping
    }
  }

  /**
   * Sanitize plain text input
   * @param {string} input - Text to sanitize
   * @param {Object} options - Sanitization options
   * @returns {string} Sanitized text
   */
  sanitizeText(input, options = {}) {
    if (!input || typeof input !== 'string') {
      return '';
    }

    const {
      maxLength = 1000,
      allowNewlines = true,
      allowSpecialChars = true,
      trim = true
    } = options;

    try {
      let sanitized = input;

      // Trim whitespace
      if (trim) {
        sanitized = sanitized.trim();
      }

      // Remove null bytes and control characters
      sanitized = sanitized.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '');

      // Handle newlines
      if (!allowNewlines) {
        sanitized = sanitized.replace(/[\r\n]/g, ' ');
      }

      // Remove potentially dangerous characters if not allowed
      if (!allowSpecialChars) {
        sanitized = sanitized.replace(/[<>'"&]/g, '');
      } else {
        // Escape HTML entities
        sanitized = validator.escape(sanitized);
      }

      // Truncate to max length
      if (sanitized.length > maxLength) {
        sanitized = sanitized.substring(0, maxLength);
      }

      return sanitized;
    } catch (error) {
      logger.error('Text sanitization error:', error);
      return '';
    }
  }

  /**
   * Sanitize email input
   * @param {string} email - Email to sanitize
   * @returns {string} Sanitized email
   */
  sanitizeEmail(email) {
    if (!email || typeof email !== 'string') {
      return '';
    }

    try {
      // Normalize and sanitize email
      let sanitized = email.toLowerCase().trim();
      
      // Remove dangerous characters
      sanitized = sanitized.replace(/[<>'"&\x00-\x1F\x7F]/g, '');
      
      // Validate email format
      if (!validator.isEmail(sanitized)) {
        return '';
      }
      
      return sanitized;
    } catch (error) {
      logger.error('Email sanitization error:', error);
      return '';
    }
  }

  /**
   * Sanitize URL input
   * @param {string} url - URL to sanitize
   * @returns {string} Sanitized URL
   */
  sanitizeUrl(url) {
    if (!url || typeof url !== 'string') {
      return '';
    }

    try {
      let sanitized = url.trim();
      
      // Remove dangerous characters
      sanitized = sanitized.replace(/[\x00-\x1F\x7F]/g, '');
      
      // Check for valid URL
      if (!validator.isURL(sanitized, {
        protocols: ['http', 'https'],
        require_protocol: true,
        require_valid_protocol: true,
        allow_underscores: false,
        allow_trailing_dot: false,
        allow_protocol_relative_urls: false
      })) {
        return '';
      }
      
      return sanitized;
    } catch (error) {
      logger.error('URL sanitization error:', error);
      return '';
    }
  }

  /**
   * Sanitize phone number
   * @param {string} phone - Phone number to sanitize
   * @returns {string} Sanitized phone number
   */
  sanitizePhone(phone) {
    if (!phone || typeof phone !== 'string') {
      return '';
    }

    try {
      // Remove all non-digit characters except + and spaces
      let sanitized = phone.replace(/[^\d\s+()-]/g, '');
      
      // Trim whitespace
      sanitized = sanitized.trim();
      
      // Basic phone validation (allow international format)
      if (!/^[\+]?[\d\s\-\(\)]{7,20}$/.test(sanitized)) {
        return '';
      }
      
      return sanitized;
    } catch (error) {
      logger.error('Phone sanitization error:', error);
      return '';
    }
  }

  /**
   * Sanitize numeric input
   * @param {any} input - Numeric input to sanitize
   * @param {Object} options - Validation options
   * @returns {number|null} Sanitized number or null if invalid
   */
  sanitizeNumber(input, options = {}) {
    const { min, max, allowFloat = true, allowNegative = true } = options;

    try {
      let num;
      
      if (typeof input === 'string') {
        // Remove non-numeric characters except decimal point and minus
        let cleaned = input.replace(/[^\d.-]/g, '');
        num = allowFloat ? parseFloat(cleaned) : parseInt(cleaned, 10);
      } else if (typeof input === 'number') {
        num = input;
      } else {
        return null;
      }

      // Check if valid number
      if (isNaN(num) || !isFinite(num)) {
        return null;
      }

      // Check negative numbers
      if (!allowNegative && num < 0) {
        return null;
      }

      // Check bounds
      if (typeof min === 'number' && num < min) {
        return null;
      }
      if (typeof max === 'number' && num > max) {
        return null;
      }

      return num;
    } catch (error) {
      logger.error('Number sanitization error:', error);
      return null;
    }
  }

  /**
   * Sanitize object recursively
   * @param {Object} obj - Object to sanitize
   * @param {Object} schema - Sanitization schema
   * @returns {Object} Sanitized object
   */
  sanitizeObject(obj, schema = {}) {
    if (!obj || typeof obj !== 'object') {
      return {};
    }

    try {
      const sanitized = {};

      for (const [key, value] of Object.entries(obj)) {
        const fieldSchema = schema[key] || { type: 'text' };
        
        switch (fieldSchema.type) {
          case 'html':
            sanitized[key] = this.sanitizeHtml(value, fieldSchema.allowFormatting);
            break;
          case 'email':
            sanitized[key] = this.sanitizeEmail(value);
            break;
          case 'url':
            sanitized[key] = this.sanitizeUrl(value);
            break;
          case 'phone':
            sanitized[key] = this.sanitizePhone(value);
            break;
          case 'number':
            sanitized[key] = this.sanitizeNumber(value, fieldSchema.options);
            break;
          case 'text':
          default:
            sanitized[key] = this.sanitizeText(value, fieldSchema.options);
            break;
        }
      }

      return sanitized;
    } catch (error) {
      logger.error('Object sanitization error:', error);
      return {};
    }
  }

  /**
   * Check for potential XSS patterns
   * @param {string} input - Input to check
   * @returns {boolean} True if potentially malicious
   */
  detectXSS(input) {
    if (!input || typeof input !== 'string') {
      return false;
    }

    const xssPatterns = [
      /<script[^>]*>.*?<\/script>/gi,
      /javascript:/gi,
      /on\w+\s*=/gi,
      /<iframe[^>]*>.*?<\/iframe>/gi,
      /<object[^>]*>.*?<\/object>/gi,
      /<embed[^>]*>/gi,
      /expression\s*\(/gi,
      /vbscript:/gi,
      /data:text\/html/gi
    ];

    return xssPatterns.some(pattern => pattern.test(input));
  }
}

module.exports = new InputSanitizationService();
