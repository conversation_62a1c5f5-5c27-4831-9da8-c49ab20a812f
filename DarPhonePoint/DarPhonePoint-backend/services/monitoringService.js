const os = require('os');
const mongoose = require('mongoose');
const logger = require('./errorLoggingService').logger;
const cacheService = require('./cacheService');
const cacheOptimizationService = require('./cacheOptimizationService');
const databaseHealthService = require('./databaseHealthService');

class MonitoringService {
  constructor() {
    this.metrics = {
      requests: {
        total: 0,
        success: 0,
        error: 0,
        byEndpoint: new Map()
      },
      performance: {
        responseTime: [],
        memoryUsage: [],
        cpuUsage: []
      },
      database: {
        operations: 0,
        slowQueries: []
      },
      cache: {
        hits: 0,
        misses: 0
      }
    };

    // Start periodic metrics collection
    this.startMetricsCollection();
  }

  // Start collecting metrics periodically
  startMetricsCollection() {
    setInterval(() => {
      this.collectSystemMetrics();
    }, 60000); // Collect every minute
  }

  // Collect system metrics
  async collectSystemMetrics() {
    try {
      const metrics = {
        timestamp: new Date(),
        memory: {
          total: os.totalmem(),
          free: os.freemem(),
          used: os.totalmem() - os.freemem()
        },
        cpu: {
          loadAvg: os.loadavg(),
          cpus: os.cpus()
        },
        uptime: os.uptime()
      };

      this.metrics.performance.memoryUsage.push(metrics.memory);
      this.metrics.performance.cpuUsage.push(metrics.cpu);

      // Keep only last 24 hours of metrics
      const oneDayAgo = Date.now() - (24 * 60 * 60 * 1000);
      this.metrics.performance.memoryUsage = this.metrics.performance.memoryUsage
        .filter(m => m.timestamp > oneDayAgo);
      this.metrics.performance.cpuUsage = this.metrics.performance.cpuUsage
        .filter(m => m.timestamp > oneDayAgo);

      // Clean up response time metrics (keep only last 24 hours)
      this.metrics.performance.responseTime = this.metrics.performance.responseTime
        .filter(r => r.timestamp > oneDayAgo);

      // Clean up slow queries (keep only last 24 hours)
      this.metrics.database.slowQueries = this.metrics.database.slowQueries
        .filter(q => q.timestamp > oneDayAgo);

      // Clean up endpoint metrics if they get too large (keep only top 100 endpoints)
      if (this.metrics.requests.byEndpoint.size > 100) {
        const sortedEndpoints = Array.from(this.metrics.requests.byEndpoint.entries())
          .sort((a, b) => b[1].total - a[1].total)
          .slice(0, 100);
        this.metrics.requests.byEndpoint = new Map(sortedEndpoints);
      }

      logger.info('System metrics collected', { metrics });
    } catch (error) {
      logger.error('Error collecting system metrics:', error);
    }
  }

  // Track request metrics
  trackRequest(endpoint, statusCode, responseTime) {
    this.metrics.requests.total++;

    if (statusCode >= 200 && statusCode < 400) {
      this.metrics.requests.success++;
    } else {
      this.metrics.requests.error++;
    }

    const endpointMetrics = this.metrics.requests.byEndpoint.get(endpoint) || {
      total: 0,
      success: 0,
      error: 0,
      avgResponseTime: 0
    };

    endpointMetrics.total++;
    if (statusCode >= 200 && statusCode < 400) {
      endpointMetrics.success++;
    } else {
      endpointMetrics.error++;
    }

    endpointMetrics.avgResponseTime =
      (endpointMetrics.avgResponseTime * (endpointMetrics.total - 1) + responseTime) /
      endpointMetrics.total;

    this.metrics.requests.byEndpoint.set(endpoint, endpointMetrics);
    this.metrics.performance.responseTime.push({
      timestamp: new Date(),
      endpoint,
      responseTime
    });

    // Prevent response time array from growing too large (keep max 1000 entries)
    if (this.metrics.performance.responseTime.length > 1000) {
      this.metrics.performance.responseTime = this.metrics.performance.responseTime.slice(-500);
    }
  }

  // Track database operations
  trackDatabaseOperation(operation, duration) {
    this.metrics.database.operations++;

    if (duration > 1000) { // Log slow queries (over 1 second)
      this.metrics.database.slowQueries.push({
        timestamp: new Date(),
        operation,
        duration
      });

      logger.warn('Slow database query detected', {
        operation,
        duration,
        timestamp: new Date()
      });

      // Prevent slow queries array from growing too large (keep max 100 entries)
      if (this.metrics.database.slowQueries.length > 100) {
        this.metrics.database.slowQueries = this.metrics.database.slowQueries.slice(-50);
      }
    }
  }

  // Track cache operations
  trackCacheOperation(hit) {
    if (hit) {
      this.metrics.cache.hits++;
    } else {
      this.metrics.cache.misses++;
    }
  }

  // Get current metrics
  getMetrics() {
    const cacheStats = cacheOptimizationService.getCacheStats();
    const dbHealthStats = databaseHealthService.getHealthStats();
    const dbConnectionInfo = databaseHealthService.getConnectionInfo();

    return {
      ...this.metrics,
      cacheHitRate: this.metrics.cache.hits /
        (this.metrics.cache.hits + this.metrics.cache.misses) || 0,
      optimizedCache: cacheStats,
      database: {
        health: dbHealthStats,
        connection: dbConnectionInfo
      }
    };
  }

  // Get performance report
  getPerformanceReport() {
    const responseTimes = this.metrics.performance.responseTime;
    const avgResponseTime = responseTimes.reduce((acc, curr) => acc + curr.responseTime, 0) /
      responseTimes.length || 0;

    return {
      requests: {
        total: this.metrics.requests.total,
        successRate: this.metrics.requests.success / this.metrics.requests.total || 0,
        errorRate: this.metrics.requests.error / this.metrics.requests.total || 0
      },
      performance: {
        avgResponseTime,
        memoryUsage: this.metrics.performance.memoryUsage[this.metrics.performance.memoryUsage.length - 1],
        cpuUsage: this.metrics.performance.cpuUsage[this.metrics.performance.cpuUsage.length - 1]
      },
      database: {
        operations: this.metrics.database.operations,
        slowQueries: this.metrics.database.slowQueries.length
      },
      cache: {
        hitRate: this.metrics.cache.hits / (this.metrics.cache.hits + this.metrics.cache.misses) || 0
      }
    };
  }

  // Log error
  logError(error, context = {}) {
    logger.error('Error logged by monitoring service', {
      error: error.message,
      stack: error.stack,
      context
    });
  }

  // Reset metrics
  resetMetrics() {
    this.metrics = {
      requests: {
        total: 0,
        success: 0,
        error: 0,
        byEndpoint: new Map()
      },
      performance: {
        responseTime: [],
        memoryUsage: [],
        cpuUsage: []
      },
      database: {
        operations: 0,
        slowQueries: []
      },
      cache: {
        hits: 0,
        misses: 0
      }
    };
  }
}

// Create singleton instance
const monitoringService = new MonitoringService();

module.exports = monitoringService;