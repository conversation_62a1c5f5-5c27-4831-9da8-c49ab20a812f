const mongoose = require('mongoose');
const logger = require('./errorLoggingService').logger;

/**
 * Database health monitoring service for Phone Point Dar
 * Monitors connection pool, performance, and database health
 */
class DatabaseHealthService {
  constructor() {
    this.healthStats = {
      connectionPool: {
        active: 0,
        available: 0,
        created: 0,
        destroyed: 0
      },
      performance: {
        avgResponseTime: 0,
        slowQueries: 0,
        totalQueries: 0
      },
      errors: {
        connectionErrors: 0,
        queryErrors: 0,
        timeouts: 0
      },
      lastHealthCheck: null
    };

    this.queryTimes = [];
    this.maxQueryTimeHistory = 100; // Keep last 100 query times
  }

  /**
   * Start monitoring database health
   */
  startMonitoring() {
    // Monitor connection pool events
    this.setupConnectionPoolMonitoring();
    
    // Monitor query performance
    this.setupQueryPerformanceMonitoring();
    
    // Periodic health checks
    this.startPeriodicHealthChecks();
    
    logger.info('Database health monitoring started');
  }

  /**
   * Setup connection pool monitoring
   */
  setupConnectionPoolMonitoring() {
    const db = mongoose.connection;

    db.on('connectionPoolCreated', () => {
      logger.info('Database connection pool created');
    });

    db.on('connectionCreated', () => {
      this.healthStats.connectionPool.created++;
      logger.debug('New database connection created');
    });

    db.on('connectionClosed', () => {
      this.healthStats.connectionPool.destroyed++;
      logger.debug('Database connection closed');
    });

    db.on('error', (error) => {
      this.healthStats.errors.connectionErrors++;
      logger.error('Database connection error:', error);
    });
  }

  /**
   * Setup query performance monitoring
   */
  setupQueryPerformanceMonitoring() {
    // Monitor slow queries using mongoose debug
    if (process.env.NODE_ENV === 'development') {
      mongoose.set('debug', (collectionName, method, query, doc, options) => {
        const startTime = Date.now();
        
        // Track query completion time
        process.nextTick(() => {
          const duration = Date.now() - startTime;
          this.trackQueryPerformance(collectionName, method, duration);
        });
      });
    }
  }

  /**
   * Track query performance metrics
   */
  trackQueryPerformance(collection, method, duration) {
    this.healthStats.performance.totalQueries++;
    
    // Track query times for average calculation
    this.queryTimes.push(duration);
    if (this.queryTimes.length > this.maxQueryTimeHistory) {
      this.queryTimes.shift();
    }
    
    // Calculate average response time
    this.healthStats.performance.avgResponseTime = 
      this.queryTimes.reduce((sum, time) => sum + time, 0) / this.queryTimes.length;
    
    // Track slow queries (> 1000ms)
    if (duration > 1000) {
      this.healthStats.performance.slowQueries++;
      logger.warn(`Slow query detected: ${collection}.${method} took ${duration}ms`);
    }
    
    // Track very slow queries (> 5000ms)
    if (duration > 5000) {
      this.healthStats.errors.timeouts++;
      logger.error(`Very slow query: ${collection}.${method} took ${duration}ms`);
    }
  }

  /**
   * Start periodic health checks
   */
  startPeriodicHealthChecks() {
    // Health check every 5 minutes
    setInterval(() => {
      this.performHealthCheck();
    }, 5 * 60 * 1000);
    
    // Detailed stats every 30 minutes
    setInterval(() => {
      this.logDetailedStats();
    }, 30 * 60 * 1000);
  }

  /**
   * Perform comprehensive health check
   */
  async performHealthCheck() {
    try {
      const startTime = Date.now();
      
      // Test basic connectivity
      const isConnected = mongoose.connection.readyState === 1;
      
      if (!isConnected) {
        logger.error('Database health check failed: Not connected');
        return { healthy: false, reason: 'Not connected' };
      }
      
      // Test query performance
      await mongoose.connection.db.admin().ping();
      const pingTime = Date.now() - startTime;
      
      // Update connection pool stats
      this.updateConnectionPoolStats();
      
      // Check for concerning metrics
      const concerns = this.identifyHealthConcerns();
      
      this.healthStats.lastHealthCheck = new Date();
      
      const healthStatus = {
        healthy: concerns.length === 0,
        pingTime,
        concerns,
        timestamp: new Date()
      };
      
      if (concerns.length > 0) {
        logger.warn('Database health concerns detected:', concerns);
      } else {
        logger.debug(`Database health check passed (ping: ${pingTime}ms)`);
      }
      
      return healthStatus;
      
    } catch (error) {
      logger.error('Database health check failed:', error);
      return { healthy: false, error: error.message };
    }
  }

  /**
   * Update connection pool statistics
   */
  updateConnectionPoolStats() {
    try {
      const db = mongoose.connection;
      
      // Get connection pool stats if available
      if (db.client && db.client.topology) {
        const servers = db.client.topology.s.servers;
        let totalConnections = 0;
        let availableConnections = 0;
        
        servers.forEach(server => {
          if (server.pool) {
            totalConnections += server.pool.totalConnectionCount || 0;
            availableConnections += server.pool.availableConnectionCount || 0;
          }
        });
        
        this.healthStats.connectionPool.active = totalConnections - availableConnections;
        this.healthStats.connectionPool.available = availableConnections;
      }
    } catch (error) {
      logger.debug('Could not update connection pool stats:', error.message);
    }
  }

  /**
   * Identify health concerns based on metrics
   */
  identifyHealthConcerns() {
    const concerns = [];
    
    // Check average response time
    if (this.healthStats.performance.avgResponseTime > 500) {
      concerns.push(`High average response time: ${Math.round(this.healthStats.performance.avgResponseTime)}ms`);
    }
    
    // Check slow query ratio
    const slowQueryRatio = this.healthStats.performance.slowQueries / this.healthStats.performance.totalQueries;
    if (slowQueryRatio > 0.1) {
      concerns.push(`High slow query ratio: ${Math.round(slowQueryRatio * 100)}%`);
    }
    
    // Check connection errors
    if (this.healthStats.errors.connectionErrors > 5) {
      concerns.push(`High connection errors: ${this.healthStats.errors.connectionErrors}`);
    }
    
    // Check timeout errors
    if (this.healthStats.errors.timeouts > 3) {
      concerns.push(`Query timeouts detected: ${this.healthStats.errors.timeouts}`);
    }
    
    return concerns;
  }

  /**
   * Log detailed database statistics
   */
  logDetailedStats() {
    logger.info('Database Health Statistics:', {
      connectionPool: this.healthStats.connectionPool,
      performance: {
        ...this.healthStats.performance,
        avgResponseTime: Math.round(this.healthStats.performance.avgResponseTime)
      },
      errors: this.healthStats.errors,
      lastHealthCheck: this.healthStats.lastHealthCheck
    });
  }

  /**
   * Get current health statistics
   */
  getHealthStats() {
    return {
      ...this.healthStats,
      performance: {
        ...this.healthStats.performance,
        avgResponseTime: Math.round(this.healthStats.performance.avgResponseTime)
      }
    };
  }

  /**
   * Reset statistics (useful for testing)
   */
  resetStats() {
    this.healthStats = {
      connectionPool: { active: 0, available: 0, created: 0, destroyed: 0 },
      performance: { avgResponseTime: 0, slowQueries: 0, totalQueries: 0 },
      errors: { connectionErrors: 0, queryErrors: 0, timeouts: 0 },
      lastHealthCheck: null
    };
    this.queryTimes = [];
    logger.info('Database health statistics reset');
  }

  /**
   * Get database connection info
   */
  getConnectionInfo() {
    const db = mongoose.connection;
    return {
      readyState: db.readyState,
      host: db.host,
      port: db.port,
      name: db.name,
      collections: Object.keys(db.collections),
      connectionCount: this.healthStats.connectionPool.active + this.healthStats.connectionPool.available
    };
  }
}

module.exports = new DatabaseHealthService();
