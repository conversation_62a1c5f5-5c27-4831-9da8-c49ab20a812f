const AuditLog = require('../models/AuditLog');
const logger = require('../utils/logger');

/**
 * Create an audit log entry
 *
 * @param {Object} logData - Audit log data
 * @param {String} logData.userId - User ID (optional for system actions)
 * @param {String} logData.action - Action performed
 * @param {String} logData.resourceType - Type of resource
 * @param {String} logData.resourceId - ID of resource (optional)
 * @param {String} logData.description - Description of the action
 * @param {Object} logData.previousState - Previous state of the resource (optional)
 * @param {Object} logData.newState - New state of the resource (optional)
 * @param {Object} logData.metadata - Additional metadata about the action (optional)
 * @param {String} logData.ipAddress - IP address (optional)
 * @param {String} logData.userAgent - User agent (optional)
 * @returns {Promise<Object>} Created audit log
 */
const createLog = async (logData) => {
  try {
    const {
      userId,
      action,
      resourceType,
      resourceId,
      description,
      previousState,
      newState,
      metadata,
      ipAddress,
      userAgent
    } = logData;

    // Create log
    const log = await AuditLog.create({
      user: userId,
      action,
      resource_type: resourceType,
      resource_id: resourceId,
      description,
      previous_state: previousState || null,
      new_state: newState || null,
      metadata: metadata || {},
      ip_address: ipAddress,
      user_agent: userAgent
    });

    return log;
  } catch (error) {
    logger.error('Error creating audit log', { error: error.message, logData });
    // Don't throw error to prevent disrupting the main flow
    return null;
  }
};

/**
 * Log user login
 *
 * @param {Object} user - User object
 * @param {Object} req - Express request object
 * @param {Object} metadata - Additional metadata (optional)
 */
const logLogin = async (user, req, metadata = {}) => {
  return createLog({
    userId: user._id,
    action: 'login',
    resourceType: 'auth',
    resourceId: user._id.toString(),
    description: `User ${user.name} (${user.email}) logged in`,
    metadata: {
      ...metadata,
      user_role: user.role,
      user_type: user.user_type
    },
    ipAddress: req.ip,
    userAgent: req.headers['user-agent']
  });
};

/**
 * Log user logout
 *
 * @param {Object} user - User object
 * @param {Object} req - Express request object
 */
const logLogout = async (user, req) => {
  return createLog({
    userId: user._id,
    action: 'logout',
    resourceType: 'auth',
    resourceId: user._id.toString(),
    description: `User ${user.name} (${user.email}) logged out`,
    ipAddress: req.ip,
    userAgent: req.headers['user-agent']
  });
};

/**
 * Log security event
 *
 * @param {Object} user - User object
 * @param {String} event - Security event type
 * @param {Object} req - Express request object
 * @param {Object} metadata - Additional metadata (optional)
 */
const logSecurityEvent = async (user, event, req, metadata = {}) => {
  return createLog({
    userId: user._id,
    action: 'security_event',
    resourceType: 'auth',
    resourceId: user._id.toString(),
    description: `Security event: ${event} for user ${user.name} (${user.email})`,
    metadata: {
      event,
      ...metadata
    },
    ipAddress: req.ip,
    userAgent: req.headers['user-agent']
  });
};

/**
 * Log resource creation
 *
 * @param {Object} user - User object
 * @param {String} resourceType - Type of resource
 * @param {String} resourceId - ID of resource
 * @param {String} resourceName - Name of resource
 * @param {Object} newState - The created resource data
 * @param {Object} req - Express request object
 * @param {Object} metadata - Additional metadata (optional)
 */
const logCreate = async (user, resourceType, resourceId, resourceName, newState, req, metadata = {}) => {
  return createLog({
    userId: user._id,
    action: 'create',
    resourceType,
    resourceId,
    description: `Created ${resourceType}: ${resourceName}`,
    newState: sanitizeState(newState),
    metadata,
    ipAddress: req.ip,
    userAgent: req.headers['user-agent']
  });
};

/**
 * Log resource update
 *
 * @param {Object} user - User object
 * @param {String} resourceType - Type of resource
 * @param {String} resourceId - ID of resource
 * @param {String} resourceName - Name of resource
 * @param {Object} previousState - The resource state before update
 * @param {Object} newState - The resource state after update
 * @param {Object} req - Express request object
 * @param {Object} metadata - Additional metadata (optional)
 */
const logUpdate = async (user, resourceType, resourceId, resourceName, previousState, newState, req, metadata = {}) => {
  // Identify what fields were changed
  const changedFields = identifyChanges(previousState, newState);

  return createLog({
    userId: user._id,
    action: 'update',
    resourceType,
    resourceId,
    description: `Updated ${resourceType}: ${resourceName} (Changed: ${Object.keys(changedFields).join(', ')})`,
    previousState: sanitizeState(previousState),
    newState: sanitizeState(newState),
    metadata: {
      ...metadata,
      changed_fields: Object.keys(changedFields)
    },
    ipAddress: req.ip,
    userAgent: req.headers['user-agent']
  });
};

/**
 * Log resource deletion
 *
 * @param {Object} user - User object
 * @param {String} resourceType - Type of resource
 * @param {String} resourceId - ID of resource
 * @param {String} resourceName - Name of resource
 * @param {Object} previousState - The resource state before deletion
 * @param {Object} req - Express request object
 * @param {Object} metadata - Additional metadata (optional)
 */
const logDelete = async (user, resourceType, resourceId, resourceName, previousState, req, metadata = {}) => {
  return createLog({
    userId: user._id,
    action: 'delete',
    resourceType,
    resourceId,
    description: `Deleted ${resourceType}: ${resourceName}`,
    previousState: sanitizeState(previousState),
    metadata,
    ipAddress: req.ip,
    userAgent: req.headers['user-agent']
  });
};

/**
 * Log email sequence trigger
 *
 * @param {Object} user - User object (optional)
 * @param {String} sequenceId - Email sequence ID
 * @param {String} sequenceName - Email sequence name
 * @param {String} email - Recipient email
 * @param {String} triggerType - Trigger type
 * @param {Object} req - Express request object
 * @param {Object} metadata - Additional metadata (optional)
 */
const logEmailSequenceTrigger = async (user, sequenceId, sequenceName, email, triggerType, req, metadata = {}) => {
  return createLog({
    userId: user ? user._id : null,
    action: 'email_sequence_trigger',
    resourceType: 'email_sequence',
    resourceId: sequenceId,
    description: `Triggered email sequence: ${sequenceName} for ${email} (${triggerType})`,
    metadata: {
      ...metadata,
      email,
      trigger_type: triggerType
    },
    ipAddress: req ? req.ip : null,
    userAgent: req ? req.headers['user-agent'] : null
  });
};

/**
 * Log payment processing
 *
 * @param {Object} user - User object (optional)
 * @param {String} orderId - Order ID
 * @param {Number} amount - Payment amount
 * @param {String} status - Payment status
 * @param {String} transactionId - Transaction ID
 * @param {Object} req - Express request object
 * @param {Object} metadata - Additional metadata (optional)
 */
const logPaymentProcessed = async (user, orderId, amount, status, transactionId, req, metadata = {}) => {
  return createLog({
    userId: user ? user._id : null,
    action: 'payment_processed',
    resourceType: 'payment',
    resourceId: orderId,
    description: `Processed payment for order ${orderId}: ${status} (${amount})`,
    metadata: {
      ...metadata,
      amount,
      status,
      transaction_id: transactionId
    },
    ipAddress: req ? req.ip : null,
    userAgent: req ? req.headers['user-agent'] : null
  });
};

/**
 * Log lead capture (public action, no user required)
 *
 * @param {String} email - Lead email
 * @param {String} leadId - Lead ID
 * @param {String} source - Lead source
 * @param {Object} leadData - Lead data
 * @param {Object} req - Express request object
 * @param {Object} metadata - Additional metadata (optional)
 */
const logLeadCapture = async (email, leadId, source, leadData, req, metadata = {}) => {
  return createLog({
    userId: null, // No user for public lead capture
    action: 'lead_capture',
    resourceType: 'lead',
    resourceId: leadId,
    description: `Lead captured: ${email} from ${source}`,
    newState: sanitizeState(leadData),
    metadata: {
      ...metadata,
      email,
      source,
      lead_magnet: metadata.lead_magnet || 'unknown'
    },
    ipAddress: req ? req.ip : null,
    userAgent: req ? req.headers['user-agent'] : null
  });
};

/**
 * Log file download
 *
 * @param {Object} user - User object
 * @param {String} fileName - File name
 * @param {Object} req - Express request object
 * @param {Object} metadata - Additional metadata (optional)
 */
const logFileDownload = async (user, fileName, req, metadata = {}) => {
  return createLog({
    userId: user._id,
    action: 'file_download',
    resourceType: 'file',
    resourceId: fileName,
    description: `User ${user.name || user.email} downloaded file: ${fileName}`,
    metadata,
    ipAddress: req.ip,
    userAgent: req.headers['user-agent']
  });
};

/**
 * Log download access
 *
 * @param {Object} user - User object
 * @param {String} productId - Product ID
 * @param {String} productName - Product name
 * @param {Object} req - Express request object
 * @param {Object} metadata - Additional metadata (optional)
 */
const logDownloadAccess = async (user, productId, productName, req, metadata = {}) => {
  return createLog({
    userId: user._id,
    action: 'download_access',
    resourceType: 'download',
    resourceId: productId,
    description: `User ${user.name} accessed download for ${productName}`,
    metadata,
    ipAddress: req.ip,
    userAgent: req.headers['user-agent']
  });
};

/**
 * Log password reset
 *
 * @param {Object} user - User object
 * @param {Object} req - Express request object
 * @param {Object} metadata - Additional metadata (optional)
 */
const logPasswordReset = async (user, req, metadata = {}) => {
  return createLog({
    userId: user._id,
    action: 'password_reset',
    resourceType: 'user',
    resourceId: user._id,
    description: `Password reset completed for user: ${user.email}`,
    metadata,
    ipAddress: req.ip,
    userAgent: req.headers['user-agent']
  });
};

/**
 * Log password change
 *
 * @param {Object} user - User object
 * @param {Object} req - Express request object
 * @param {Object} metadata - Additional metadata (optional)
 */
const logPasswordChange = async (user, req, metadata = {}) => {
  return createLog({
    userId: user._id,
    action: 'password_change',
    resourceType: 'user',
    resourceId: user._id,
    description: `Password changed for user: ${user.email}`,
    metadata,
    ipAddress: req.ip,
    userAgent: req.headers['user-agent']
  });
};

/**
 * Log profile update
 *
 * @param {Object} user - User object
 * @param {Object} req - Express request object
 * @param {Object} changes - Changes made to profile
 * @param {Object} metadata - Additional metadata (optional)
 */
const logProfileUpdate = async (user, req, changes, metadata = {}) => {
  return createLog({
    userId: user._id,
    action: 'profile_update',
    resourceType: 'user',
    resourceId: user._id,
    description: `Profile updated for user: ${user.email} (Changed: ${Object.keys(changes).join(', ')})`,
    metadata: {
      ...metadata,
      changed_fields: Object.keys(changes),
      changes: sanitizeState(changes)
    },
    ipAddress: req.ip,
    userAgent: req.headers['user-agent']
  });
};

/**
 * Sanitize state object to remove sensitive information
 *
 * @param {Object} state - State object to sanitize
 * @returns {Object} Sanitized state object
 */
const sanitizeState = (state) => {
  if (!state) return null;

  // Create a deep copy to avoid modifying the original
  const sanitized = JSON.parse(JSON.stringify(state));

  // Remove sensitive fields
  const sensitiveFields = ['password', 'token', 'secret', 'credit_card', 'cvv'];

  // Function to recursively sanitize objects
  const sanitizeObject = (obj) => {
    if (!obj || typeof obj !== 'object') return;

    Object.keys(obj).forEach(key => {
      // Check if this is a sensitive field
      if (sensitiveFields.some(field => key.toLowerCase().includes(field))) {
        obj[key] = '[REDACTED]';
      }
      // Recursively sanitize nested objects
      else if (typeof obj[key] === 'object' && obj[key] !== null) {
        sanitizeObject(obj[key]);
      }
    });
  };

  sanitizeObject(sanitized);
  return sanitized;
};

/**
 * Identify changes between two state objects
 *
 * @param {Object} oldState - Previous state
 * @param {Object} newState - New state
 * @returns {Object} Object with changed fields as keys and new values
 */
const identifyChanges = (oldState, newState) => {
  if (!oldState || !newState) return {};

  const changes = {};

  // Get all keys from both objects
  const allKeys = new Set([...Object.keys(oldState), ...Object.keys(newState)]);

  allKeys.forEach(key => {
    // Skip internal MongoDB fields
    if (key.startsWith('_') && key !== '_id') return;

    // Check if the field exists in both states and has changed
    if (!oldState.hasOwnProperty(key) || !newState.hasOwnProperty(key) ||
        JSON.stringify(oldState[key]) !== JSON.stringify(newState[key])) {
      changes[key] = newState[key];
    }
  });

  return changes;
};

/**
 * Log trade-in creation
 *
 * @param {Object} user - User object
 * @param {String} tradeInId - Trade-in ID
 * @param {Number} estimatedValue - Estimated value
 * @param {Object} metadata - Additional metadata (optional)
 */
const logTradeInCreated = async (user, tradeInId, estimatedValue, metadata = {}) => {
  return createLog({
    userId: user._id,
    action: 'trade_in_created',
    resourceType: 'trade_in',
    resourceId: tradeInId,
    description: `Trade-in request created with estimated value: TZS ${estimatedValue?.toLocaleString()}`,
    metadata: {
      ...metadata,
      estimated_value: estimatedValue
    }
  });
};

/**
 * Log trade-in status update
 *
 * @param {Object} user - User object (admin)
 * @param {String} tradeInId - Trade-in ID
 * @param {String} newStatus - New status
 * @param {Number} finalValue - Final value (optional)
 * @param {Object} metadata - Additional metadata (optional)
 */
const logTradeInStatusUpdated = async (user, tradeInId, newStatus, finalValue, metadata = {}) => {
  return createLog({
    userId: user._id,
    action: 'trade_in_status_updated',
    resourceType: 'trade_in',
    resourceId: tradeInId,
    description: `Trade-in status updated to: ${newStatus}${finalValue ? ` with final value: TZS ${finalValue.toLocaleString()}` : ''}`,
    metadata: {
      ...metadata,
      new_status: newStatus,
      final_value: finalValue
    }
  });
};

/**
 * Log trade-in cancellation
 *
 * @param {Object} user - User object
 * @param {String} tradeInId - Trade-in ID
 * @param {Object} metadata - Additional metadata (optional)
 */
const logTradeInCancelled = async (user, tradeInId, metadata = {}) => {
  return createLog({
    userId: user._id,
    action: 'trade_in_cancelled',
    resourceType: 'trade_in',
    resourceId: tradeInId,
    description: `Trade-in request cancelled by user`,
    metadata
  });
};

/**
 * Log payment creation
 *
 * @param {Object} user - User object
 * @param {String} orderId - Order ID
 * @param {Number} amount - Payment amount
 * @param {String} transactionId - Transaction ID
 * @param {Object} metadata - Additional metadata (optional)
 */
const logPaymentCreated = async (user, orderId, amount, transactionId, metadata = {}) => {
  return createLog({
    userId: user ? user._id : null,
    action: 'payment_created',
    resourceType: 'payment',
    resourceId: orderId,
    description: `Payment created for order ${orderId}: TZS ${amount?.toLocaleString()}`,
    metadata: {
      ...metadata,
      amount,
      transaction_id: transactionId
    }
  });
};

module.exports = {
  createLog,
  logLogin,
  logLogout,
  logSecurityEvent,
  logCreate,
  logUpdate,
  logDelete,
  logEmailSequenceTrigger,
  logPaymentProcessed,
  logLeadCapture,
  logFileDownload,
  logDownloadAccess,
  logPasswordReset,
  logPasswordChange,
  logProfileUpdate,
  sanitizeState,
  identifyChanges,
  // Trade-in functions
  logTradeInCreated,
  logTradeInStatusUpdated,
  logTradeInCancelled,
  // Payment functions
  logPaymentCreated
};
