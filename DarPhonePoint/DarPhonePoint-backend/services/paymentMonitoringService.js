/**
 * Payment Monitoring Service for Phone Point Dar
 * Monitors ClickPesa transactions, tracks payment metrics, and sends alerts
 */

const logger = require('../utils/logger');
const emailSender = require('./emailSender');
const smsService = require('./smsService');
const Order = require('../models/Order');
const AuditLog = require('../models/AuditLog');

class PaymentMonitoringService {
  constructor() {
    this.metrics = {
      totalTransactions: 0,
      successfulPayments: 0,
      failedPayments: 0,
      pendingPayments: 0,
      totalRevenue: 0,
      averageTransactionValue: 0,
      paymentMethods: {
        mpesa: { count: 0, revenue: 0 },
        tigo_pesa: { count: 0, revenue: 0 },
        airtel_money: { count: 0, revenue: 0 },
        bank_transfer: { count: 0, revenue: 0 },
        cash_on_delivery: { count: 0, revenue: 0 }
      },
      dailyStats: new Map(),
      alerts: [],
      lastUpdated: new Date()
    };
    
    this.alertThresholds = {
      failureRate: 0.15, // Alert if failure rate > 15%
      pendingTimeout: 30 * 60 * 1000, // Alert if payment pending > 30 minutes
      dailyRevenueTarget: 5000000, // 5M TZS daily target
      consecutiveFailures: 5, // Alert after 5 consecutive failures
      unusualAmount: ******** // Alert for transactions > 10M TZS
    };
    
    this.consecutiveFailures = 0;
    this.isMonitoring = false;
    
    // Start monitoring
    this.startMonitoring();
  }

  /**
   * Start payment monitoring
   */
  startMonitoring() {
    if (this.isMonitoring) return;
    
    this.isMonitoring = true;
    
    // Update metrics every 5 minutes
    setInterval(() => {
      this.updateMetrics();
    }, 5 * 60 * 1000);
    
    // Check for alerts every 2 minutes
    setInterval(() => {
      this.checkAlerts();
    }, 2 * 60 * 1000);
    
    // Daily summary at midnight
    setInterval(() => {
      const now = new Date();
      if (now.getHours() === 0 && now.getMinutes() === 0) {
        this.sendDailySummary();
      }
    }, 60 * 1000);
    
    logger.info('Payment monitoring service started');
  }

  /**
   * Record payment attempt
   */
  async recordPaymentAttempt(paymentData) {
    try {
      this.metrics.totalTransactions++;
      
      const { method, amount, status, orderId, transactionId } = paymentData;
      
      // Update payment method stats
      if (this.metrics.paymentMethods[method]) {
        this.metrics.paymentMethods[method].count++;
        if (status === 'completed') {
          this.metrics.paymentMethods[method].revenue += amount;
        }
      }
      
      // Update status counts
      switch (status) {
        case 'completed':
          this.metrics.successfulPayments++;
          this.metrics.totalRevenue += amount;
          this.consecutiveFailures = 0;
          break;
        case 'failed':
          this.metrics.failedPayments++;
          this.consecutiveFailures++;
          break;
        case 'pending':
          this.metrics.pendingPayments++;
          break;
      }
      
      // Calculate average transaction value
      if (this.metrics.successfulPayments > 0) {
        this.metrics.averageTransactionValue = this.metrics.totalRevenue / this.metrics.successfulPayments;
      }
      
      // Update daily stats
      const today = new Date().toISOString().split('T')[0];
      if (!this.metrics.dailyStats.has(today)) {
        this.metrics.dailyStats.set(today, {
          transactions: 0,
          revenue: 0,
          successful: 0,
          failed: 0
        });
      }
      
      const dailyStats = this.metrics.dailyStats.get(today);
      dailyStats.transactions++;
      if (status === 'completed') {
        dailyStats.revenue += amount;
        dailyStats.successful++;
      } else if (status === 'failed') {
        dailyStats.failed++;
      }
      
      this.metrics.lastUpdated = new Date();
      
      // Log payment attempt
      logger.info('Payment attempt recorded:', {
        orderId,
        transactionId,
        method,
        amount,
        status,
        consecutiveFailures: this.consecutiveFailures
      });
      
      // Check for immediate alerts
      await this.checkPaymentAlerts(paymentData);
      
    } catch (error) {
      logger.error('Error recording payment attempt:', error);
    }
  }

  /**
   * Update metrics from database
   */
  async updateMetrics() {
    try {
      const today = new Date();
      const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
      
      // Get today's orders
      const todayOrders = await Order.find({
        created_at: { $gte: startOfDay },
        payment_status: { $in: ['completed', 'failed', 'pending'] }
      });
      
      // Reset daily stats
      const todayKey = today.toISOString().split('T')[0];
      this.metrics.dailyStats.set(todayKey, {
        transactions: todayOrders.length,
        revenue: 0,
        successful: 0,
        failed: 0
      });
      
      const dailyStats = this.metrics.dailyStats.get(todayKey);
      
      // Calculate daily metrics
      todayOrders.forEach(order => {
        if (order.payment_status === 'completed') {
          dailyStats.revenue += order.total;
          dailyStats.successful++;
        } else if (order.payment_status === 'failed') {
          dailyStats.failed++;
        }
      });
      
      logger.debug('Payment metrics updated:', {
        todayTransactions: dailyStats.transactions,
        todayRevenue: dailyStats.revenue,
        successRate: dailyStats.transactions > 0 ? (dailyStats.successful / dailyStats.transactions) * 100 : 0
      });
      
    } catch (error) {
      logger.error('Error updating payment metrics:', error);
    }
  }

  /**
   * Check for payment alerts
   */
  async checkPaymentAlerts(paymentData) {
    const alerts = [];
    
    // Check for consecutive failures
    if (this.consecutiveFailures >= this.alertThresholds.consecutiveFailures) {
      alerts.push({
        type: 'consecutive_failures',
        severity: 'critical',
        message: `${this.consecutiveFailures} consecutive payment failures detected`,
        data: { consecutiveFailures: this.consecutiveFailures }
      });
    }
    
    // Check for unusual transaction amount
    if (paymentData.amount > this.alertThresholds.unusualAmount) {
      alerts.push({
        type: 'unusual_amount',
        severity: 'warning',
        message: `Unusually large transaction: ${paymentData.amount} TZS`,
        data: { amount: paymentData.amount, orderId: paymentData.orderId }
      });
    }
    
    // Check failure rate
    const today = new Date().toISOString().split('T')[0];
    const dailyStats = this.metrics.dailyStats.get(today);
    if (dailyStats && dailyStats.transactions >= 10) {
      const failureRate = dailyStats.failed / dailyStats.transactions;
      if (failureRate > this.alertThresholds.failureRate) {
        alerts.push({
          type: 'high_failure_rate',
          severity: 'warning',
          message: `High payment failure rate: ${(failureRate * 100).toFixed(1)}%`,
          data: { failureRate, transactions: dailyStats.transactions, failures: dailyStats.failed }
        });
      }
    }
    
    // Send alerts
    for (const alert of alerts) {
      await this.sendAlert(alert);
    }
  }

  /**
   * Check for general alerts
   */
  async checkAlerts() {
    try {
      // Check for pending payments that are too old
      const cutoffTime = new Date(Date.now() - this.alertThresholds.pendingTimeout);
      const stalePendingOrders = await Order.find({
        payment_status: 'pending',
        created_at: { $lt: cutoffTime }
      });
      
      if (stalePendingOrders.length > 0) {
        await this.sendAlert({
          type: 'stale_pending_payments',
          severity: 'warning',
          message: `${stalePendingOrders.length} payments have been pending for over 30 minutes`,
          data: { count: stalePendingOrders.length, orders: stalePendingOrders.map(o => o._id) }
        });
      }
      
    } catch (error) {
      logger.error('Error checking alerts:', error);
    }
  }

  /**
   * Send alert notification
   */
  async sendAlert(alert) {
    try {
      this.metrics.alerts.push({
        ...alert,
        timestamp: new Date(),
        id: `alert_${Date.now()}_${Math.random().toString(36).substr(2, 5)}`
      });
      
      // Keep only last 100 alerts
      if (this.metrics.alerts.length > 100) {
        this.metrics.alerts = this.metrics.alerts.slice(-100);
      }
      
      logger.warn('Payment alert triggered:', alert);
      
      // Send email alert to admin
      await this.sendEmailAlert(alert);
      
      // Send SMS for critical alerts
      if (alert.severity === 'critical') {
        await this.sendSMSAlert(alert);
      }
      
    } catch (error) {
      logger.error('Error sending alert:', error);
    }
  }

  /**
   * Send email alert
   */
  async sendEmailAlert(alert) {
    try {
      const adminEmail = process.env.ADMIN_EMAIL || process.env.EMAIL_FROM;
      if (!adminEmail) return;
      
      await emailSender.sendEmail({
        to: adminEmail,
        subject: `🚨 Phone Point Dar Payment Alert - ${alert.type}`,
        template: 'payment_alert',
        templateData: {
          alertType: alert.type,
          severity: alert.severity,
          message: alert.message,
          timestamp: new Date().toISOString(),
          data: JSON.stringify(alert.data, null, 2),
          dashboardUrl: `${process.env.FRONTEND_URL}/admin/payments`
        }
      });
      
    } catch (error) {
      logger.error('Error sending email alert:', error);
    }
  }

  /**
   * Send SMS alert for critical issues
   */
  async sendSMSAlert(alert) {
    try {
      const adminPhone = process.env.ADMIN_PHONE;
      if (!adminPhone || !smsService.isEnabled) return;
      
      const message = `🚨 CRITICAL: Phone Point Dar payment alert - ${alert.message}. Check admin dashboard immediately.`;
      
      await smsService.sendSMS(adminPhone, message);
      
    } catch (error) {
      logger.error('Error sending SMS alert:', error);
    }
  }

  /**
   * Send daily summary
   */
  async sendDailySummary() {
    try {
      const yesterday = new Date();
      yesterday.setDate(yesterday.getDate() - 1);
      const yesterdayKey = yesterday.toISOString().split('T')[0];
      
      const dailyStats = this.metrics.dailyStats.get(yesterdayKey) || {
        transactions: 0,
        revenue: 0,
        successful: 0,
        failed: 0
      };
      
      const successRate = dailyStats.transactions > 0 ? 
        (dailyStats.successful / dailyStats.transactions) * 100 : 0;
      
      const adminEmail = process.env.ADMIN_EMAIL || process.env.EMAIL_FROM;
      if (!adminEmail) return;
      
      await emailSender.sendEmail({
        to: adminEmail,
        subject: `📊 Phone Point Dar Daily Payment Summary - ${yesterdayKey}`,
        template: 'daily_payment_summary',
        templateData: {
          date: yesterdayKey,
          transactions: dailyStats.transactions,
          revenue: dailyStats.revenue.toLocaleString(),
          successful: dailyStats.successful,
          failed: dailyStats.failed,
          successRate: successRate.toFixed(1),
          revenueTarget: this.alertThresholds.dailyRevenueTarget.toLocaleString(),
          targetMet: dailyStats.revenue >= this.alertThresholds.dailyRevenueTarget
        }
      });
      
      logger.info('Daily payment summary sent:', dailyStats);
      
    } catch (error) {
      logger.error('Error sending daily summary:', error);
    }
  }

  /**
   * Get current metrics
   */
  getMetrics() {
    return {
      ...this.metrics,
      successRate: this.metrics.totalTransactions > 0 ? 
        (this.metrics.successfulPayments / this.metrics.totalTransactions) * 100 : 0,
      failureRate: this.metrics.totalTransactions > 0 ? 
        (this.metrics.failedPayments / this.metrics.totalTransactions) * 100 : 0
    };
  }

  /**
   * Get payment health status
   */
  getHealthStatus() {
    const metrics = this.getMetrics();
    
    let status = 'healthy';
    const issues = [];
    
    if (metrics.failureRate > 15) {
      status = 'warning';
      issues.push(`High failure rate: ${metrics.failureRate.toFixed(1)}%`);
    }
    
    if (this.consecutiveFailures >= 3) {
      status = 'critical';
      issues.push(`${this.consecutiveFailures} consecutive failures`);
    }
    
    if (metrics.pendingPayments > 10) {
      status = 'warning';
      issues.push(`${metrics.pendingPayments} pending payments`);
    }
    
    return {
      status,
      issues,
      metrics: {
        totalTransactions: metrics.totalTransactions,
        successRate: metrics.successRate,
        failureRate: metrics.failureRate,
        pendingPayments: metrics.pendingPayments,
        totalRevenue: metrics.totalRevenue
      },
      lastUpdated: this.metrics.lastUpdated
    };
  }
}

module.exports = new PaymentMonitoringService();
