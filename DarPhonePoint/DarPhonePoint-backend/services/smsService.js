/**
 * SMS Service for Phone Point Dar
 * Handles SMS notifications via Beem Africa and other Tanzania SMS providers
 */

const axios = require('axios');
const logger = require('../utils/logger');
const AppError = require('../utils/AppError');

class SMSService {
  constructor() {
    this.providers = {
      beem: {
        apiUrl: process.env.BEEM_API_URL || 'https://api.beem.africa/v1/send',
        apiKey: process.env.BEEM_API_KEY,
        sourceAddr: process.env.BEEM_SOURCE_ADDR || 'PhonePointDar'
      },
      clickatell: {
        apiUrl: process.env.CLICKATELL_API_URL || 'https://platform.clickatell.com/messages/http/send',
        apiKey: process.env.CLICKATELL_API_KEY
      }
    };
    
    this.defaultProvider = process.env.SMS_PROVIDER || 'beem';
    this.isEnabled = process.env.SMS_ENABLED === 'true';
  }

  /**
   * Format phone number for Tanzania
   * @param {string} phone - Phone number
   * @returns {string} Formatted phone number
   */
  formatPhoneNumber(phone) {
    if (!phone) return null;
    
    // Remove all non-digit characters
    let cleaned = phone.replace(/\D/g, '');
    
    // Handle different formats
    if (cleaned.startsWith('0')) {
      // Convert 0XXXXXXXXX to 255XXXXXXXXX
      cleaned = '255' + cleaned.substring(1);
    } else if (cleaned.startsWith('255')) {
      // Already in correct format
      return cleaned;
    } else if (cleaned.length === 9) {
      // Add country code
      cleaned = '255' + cleaned;
    }
    
    // Validate Tanzania phone number format
    if (!/^255[67]\d{8}$/.test(cleaned)) {
      throw new AppError('Invalid Tanzania phone number format', 400);
    }
    
    return cleaned;
  }

  /**
   * Send SMS via Beem Africa
   * @param {string} phone - Phone number
   * @param {string} message - SMS message
   * @returns {Promise<Object>} SMS result
   */
  async sendViaBeem(phone, message) {
    try {
      const formattedPhone = this.formatPhoneNumber(phone);
      
      const payload = {
        source_addr: this.providers.beem.sourceAddr,
        encoding: 0,
        schedule_time: '',
        message: message,
        recipients: [{
          recipient_id: 1,
          dest_addr: formattedPhone
        }]
      };

      const response = await axios.post(this.providers.beem.apiUrl, payload, {
        headers: {
          'Authorization': `Bearer ${this.providers.beem.apiKey}`,
          'Content-Type': 'application/json'
        },
        timeout: 10000
      });

      if (response.data.successful) {
        logger.info('SMS sent successfully via Beem:', {
          phone: formattedPhone,
          message_id: response.data.request_id
        });
        
        return {
          success: true,
          provider: 'beem',
          messageId: response.data.request_id,
          phone: formattedPhone
        };
      } else {
        throw new Error(response.data.message || 'SMS sending failed');
      }
    } catch (error) {
      logger.error('Beem SMS sending failed:', error);
      throw new AppError(`SMS sending failed: ${error.message}`, 500);
    }
  }

  /**
   * Send SMS via Clickatell
   * @param {string} phone - Phone number
   * @param {string} message - SMS message
   * @returns {Promise<Object>} SMS result
   */
  async sendViaClickatell(phone, message) {
    try {
      const formattedPhone = this.formatPhoneNumber(phone);
      
      const params = {
        apiKey: this.providers.clickatell.apiKey,
        to: formattedPhone,
        content: message
      };

      const response = await axios.get(this.providers.clickatell.apiUrl, {
        params,
        timeout: 10000
      });

      if (response.data.messages && response.data.messages[0].accepted) {
        logger.info('SMS sent successfully via Clickatell:', {
          phone: formattedPhone,
          message_id: response.data.messages[0].apiMessageId
        });
        
        return {
          success: true,
          provider: 'clickatell',
          messageId: response.data.messages[0].apiMessageId,
          phone: formattedPhone
        };
      } else {
        throw new Error('SMS sending failed');
      }
    } catch (error) {
      logger.error('Clickatell SMS sending failed:', error);
      throw new AppError(`SMS sending failed: ${error.message}`, 500);
    }
  }

  /**
   * Send SMS using configured provider
   * @param {string} phone - Phone number
   * @param {string} message - SMS message
   * @param {string} provider - SMS provider (optional)
   * @returns {Promise<Object>} SMS result
   */
  async sendSMS(phone, message, provider = null) {
    if (!this.isEnabled) {
      logger.info('SMS service is disabled, skipping SMS send');
      return { success: false, reason: 'SMS service disabled' };
    }

    if (!phone || !message) {
      throw new AppError('Phone number and message are required', 400);
    }

    const selectedProvider = provider || this.defaultProvider;
    
    try {
      switch (selectedProvider) {
        case 'beem':
          return await this.sendViaBeem(phone, message);
        case 'clickatell':
          return await this.sendViaClickatell(phone, message);
        default:
          throw new AppError(`Unsupported SMS provider: ${selectedProvider}`, 400);
      }
    } catch (error) {
      // Try fallback provider if primary fails
      if (!provider && selectedProvider === 'beem' && this.providers.clickatell.apiKey) {
        logger.warn('Primary SMS provider failed, trying fallback');
        return await this.sendViaClickatell(phone, message);
      } else if (!provider && selectedProvider === 'clickatell' && this.providers.beem.apiKey) {
        logger.warn('Primary SMS provider failed, trying fallback');
        return await this.sendViaBeem(phone, message);
      }
      
      throw error;
    }
  }

  /**
   * Send order confirmation SMS
   * @param {Object} order - Order object
   * @param {Object} user - User object
   * @returns {Promise<Object>} SMS result
   */
  async sendOrderConfirmationSMS(order, user) {
    if (!user.phone) {
      logger.info('User has no phone number, skipping SMS');
      return { success: false, reason: 'No phone number' };
    }

    const message = `Habari ${user.name}! Oda yako #${order.order_number} ya TZS ${order.total.toLocaleString()} imepokewa. Tutakupigia simu kwa maelezo zaidi. Phone Point Dar`;

    return await this.sendSMS(user.phone, message);
  }

  /**
   * Send order shipped SMS
   * @param {Object} order - Order object
   * @param {Object} user - User object
   * @returns {Promise<Object>} SMS result
   */
  async sendOrderShippedSMS(order, user) {
    if (!user.phone) {
      logger.info('User has no phone number, skipping SMS');
      return { success: false, reason: 'No phone number' };
    }

    const trackingInfo = order.tracking_number ? ` Namba ya ufuatiliaji: ${order.tracking_number}` : '';
    const message = `Habari ${user.name}! Oda yako #${order.order_number} imetumwa.${trackingInfo} Tutakupigia baada ya kufika. Phone Point Dar`;

    return await this.sendSMS(user.phone, message);
  }

  /**
   * Send order delivered SMS
   * @param {Object} order - Order object
   * @param {Object} user - User object
   * @returns {Promise<Object>} SMS result
   */
  async sendOrderDeliveredSMS(order, user) {
    if (!user.phone) {
      logger.info('User has no phone number, skipping SMS');
      return { success: false, reason: 'No phone number' };
    }

    const message = `Habari ${user.name}! Oda yako #${order.order_number} imefika salama. Asante kwa kununua Phone Point Dar. Tupe review kwenye Google!`;

    return await this.sendSMS(user.phone, message);
  }

  /**
   * Send payment reminder SMS
   * @param {Object} order - Order object
   * @param {Object} user - User object
   * @returns {Promise<Object>} SMS result
   */
  async sendPaymentReminderSMS(order, user) {
    if (!user.phone) {
      logger.info('User has no phone number, skipping SMS');
      return { success: false, reason: 'No phone number' };
    }

    const message = `Habari ${user.name}! Oda yako #${order.order_number} ya TZS ${order.total.toLocaleString()} inasubiri malipo. Lipa kupitia M-Pesa au Tigo Pesa. Phone Point Dar`;

    return await this.sendSMS(user.phone, message);
  }

  /**
   * Send warranty expiry reminder SMS
   * @param {Object} serialNumber - Serial number object
   * @param {Object} user - User object
   * @returns {Promise<Object>} SMS result
   */
  async sendWarrantyExpiryReminderSMS(serialNumber, user) {
    if (!user.phone) {
      logger.info('User has no phone number, skipping SMS');
      return { success: false, reason: 'No phone number' };
    }

    const daysLeft = Math.ceil((serialNumber.warrantyEndDate - new Date()) / (1000 * 60 * 60 * 24));
    const message = `Habari ${user.name}! Warranty ya simu yako (IMEI: ${serialNumber.imei}) itaisha baada ya siku ${daysLeft}. Kama una tatizo, wasiliana nasi haraka. Phone Point Dar`;

    return await this.sendSMS(user.phone, message);
  }

  /**
   * Send promotional SMS
   * @param {string} phone - Phone number
   * @param {string} promoMessage - Promotional message
   * @returns {Promise<Object>} SMS result
   */
  async sendPromotionalSMS(phone, promoMessage) {
    const message = `${promoMessage} Phone Point Dar - Simu bora, bei nzuri! Tembelea duka letu Dar es Salaam.`;
    return await this.sendSMS(phone, message);
  }

  /**
   * Get SMS service status
   * @returns {Object} Service status
   */
  getStatus() {
    return {
      enabled: this.isEnabled,
      defaultProvider: this.defaultProvider,
      providers: {
        beem: {
          configured: !!this.providers.beem.apiKey,
          url: this.providers.beem.apiUrl
        },
        clickatell: {
          configured: !!this.providers.clickatell.apiKey,
          url: this.providers.clickatell.apiUrl
        }
      }
    };
  }
}

module.exports = new SMSService();
