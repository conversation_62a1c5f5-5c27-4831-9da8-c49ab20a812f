// Phone Point Dar - Tanzania ClickPesa Payment Integration
const axios = require('axios');
const crypto = require('crypto');
const logger = require('../utils/logger');
const config = require('../config/config');

/**
 * Payment Service for Phone Point Dar
 * Real ClickPesa integration for Tanzania payment methods
 */
class PaymentService {
  constructor() {
    this.baseURL = process.env.CLICKPESA_BASE_URL || 'https://api.clickpesa.com/v1';
    this.apiKey = process.env.CLICKPESA_API_KEY;
    this.secretKey = process.env.CLICKPESA_SECRET_KEY;
    this.merchantId = process.env.CLICKPESA_MERCHANT_ID;

    this.supportedMethods = [
      'mpesa',
      'tigo_pesa',
      'airtel_money',
      'bank_transfer',
      'cash_on_delivery'
    ];

    // Initialize ClickPesa client
    this.clickpesaClient = axios.create({
      baseURL: this.baseURL,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.apiKey}`
      }
    });
  }

  /**
   * Create payment session with ClickPesa integration
   * @param {Object} paymentData - Payment data
   * @returns {Promise<Object>} Payment result
   */
  async createPayment(paymentData) {
    try {
      logger.info('Creating ClickPesa payment session:', {
        method: paymentData.payment_method,
        amount: paymentData.amount,
        currency: 'TZS'
      });

      // Handle cash on delivery without ClickPesa
      if (paymentData.payment_method === 'cash_on_delivery') {
        return this.createCashOnDeliveryPayment(paymentData);
      }

      // Validate required fields for ClickPesa
      if (!this.apiKey || !this.secretKey || !this.merchantId) {
        logger.warn('ClickPesa credentials not configured, falling back to manual payment');
        return this.createManualPayment(paymentData);
      }

      // Create ClickPesa payment request
      const clickpesaPayload = {
        merchant_reference: `PPD-${paymentData.order_id}-${Date.now()}`,
        amount: paymentData.amount,
        currency: 'TZS',
        payment_method: this.mapPaymentMethod(paymentData.payment_method),
        customer_phone: paymentData.customer_phone,
        customer_email: paymentData.customer_email,
        description: `Phone Point Dar Order #${paymentData.order_id}`,
        callback_url: `${config.API_URL}/api/payments/clickpesa/callback`,
        return_url: `${config.FRONTEND_URL}/payment/success`,
        cancel_url: `${config.FRONTEND_URL}/payment/cancel`
      };

      // Generate signature for ClickPesa
      const signature = this.generateClickPesaSignature(clickpesaPayload);
      clickpesaPayload.signature = signature;

      const response = await this.clickpesaClient.post('/payments', clickpesaPayload);

      if (response.data && response.data.success) {
        return {
          success: true,
          data: {
            id: response.data.payment_id,
            amount: paymentData.amount,
            currency: 'TZS',
            payment_method: paymentData.payment_method,
            status: 'pending',
            payment_url: response.data.payment_url,
            reference: clickpesaPayload.merchant_reference,
            expires_at: new Date(Date.now() + 30 * 60 * 1000).toISOString(),
            instructions: this.getPaymentInstructions(paymentData.payment_method, response.data)
          },
          provider: 'clickpesa',
          created_at: new Date().toISOString()
        };
      } else {
        throw new Error('ClickPesa payment creation failed');
      }

    } catch (error) {
      logger.error('ClickPesa payment creation error:', {
        error: error.message,
        response: error.response?.data
      });

      // Fallback to manual payment on ClickPesa failure
      logger.info('Falling back to manual payment processing');
      return this.createManualPayment(paymentData);
    }
  }

  /**
   * Map internal payment methods to ClickPesa format
   */
  mapPaymentMethod(method) {
    const methodMap = {
      'mpesa': 'MPESA',
      'tigo_pesa': 'TIGO_PESA',
      'airtel_money': 'AIRTEL_MONEY',
      'bank_transfer': 'BANK_TRANSFER'
    };
    return methodMap[method] || 'MPESA';
  }

  /**
   * Generate ClickPesa signature
   */
  generateClickPesaSignature(payload) {
    const sortedKeys = Object.keys(payload).sort();
    const signatureString = sortedKeys
      .map(key => `${key}=${payload[key]}`)
      .join('&') + this.secretKey;

    return crypto.createHash('sha256').update(signatureString).digest('hex');
  }

  /**
   * Get payment instructions based on method
   */
  getPaymentInstructions(method, clickpesaData) {
    const instructions = {
      'mpesa': `Complete payment using M-Pesa. You will receive an SMS prompt shortly.`,
      'tigo_pesa': `Complete payment using Tigo Pesa. You will receive an SMS prompt shortly.`,
      'airtel_money': `Complete payment using Airtel Money. You will receive an SMS prompt shortly.`,
      'bank_transfer': `Complete payment via bank transfer. Reference: ${clickpesaData.reference}`
    };

    return instructions[method] || `Complete payment using ${method}. Reference: ${clickpesaData.reference}`;
  }

  /**
   * Create cash on delivery payment
   */
  createCashOnDeliveryPayment(paymentData) {
    const paymentSession = {
      id: `PPD-COD-${Date.now()}`,
      amount: paymentData.amount,
      currency: 'TZS',
      payment_method: 'cash_on_delivery',
      status: 'confirmed',
      reference: `COD-${paymentData.order_id}`,
      instructions: `Pay TZS ${paymentData.amount.toLocaleString()} in cash upon delivery.`,
      created_at: new Date().toISOString()
    };

    return {
      success: true,
      data: paymentSession,
      provider: 'cash_on_delivery',
      created_at: new Date().toISOString()
    };
  }

  /**
   * Create manual payment (fallback)
   */
  createManualPayment(paymentData) {
    const paymentSession = {
      id: `PPD-MANUAL-${Date.now()}`,
      amount: paymentData.amount,
      currency: 'TZS',
      payment_method: paymentData.payment_method,
      status: 'pending_manual',
      reference: `MANUAL-${paymentData.order_id}`,
      instructions: this.getManualPaymentInstructions(paymentData.payment_method, paymentData.amount),
      created_at: new Date().toISOString(),
      expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString() // 24 hours
    };

    return {
      success: true,
      data: paymentSession,
      provider: 'manual',
      created_at: new Date().toISOString()
    };
  }

  /**
   * Get manual payment instructions
   */
  getManualPaymentInstructions(method, amount) {
    const instructions = {
      'mpesa': `Send TZS ${amount.toLocaleString()} via M-Pesa to: 0755-PHONEPOINT (0755-*********). Reference: Your order number.`,
      'tigo_pesa': `Send TZS ${amount.toLocaleString()} via Tigo Pesa to: 0714-PHONEPOINT (0714-*********). Reference: Your order number.`,
      'airtel_money': `Send TZS ${amount.toLocaleString()} via Airtel Money to: 0786-PHONEPOINT (0786-*********). Reference: Your order number.`,
      'bank_transfer': `Transfer TZS ${amount.toLocaleString()} to Account: 0150-294-567, CRDB Bank, Account Name: Phone Point Dar Ltd. Reference: Your order number.`
    };

    return instructions[method] || `Contact Phone Point Dar at +255 755 746 637 for payment instructions. Amount: TZS ${amount.toLocaleString()}`;
  }

  /**
   * Process ClickPesa webhook callback
   * @param {Object} webhookData - ClickPesa webhook data
   * @returns {Promise<Object>} Processed webhook result
   */
  async processWebhook(webhookData) {
    try {
      logger.info('Processing ClickPesa webhook:', {
        paymentId: webhookData.payment_id,
        status: webhookData.status
      });

      // Verify webhook signature
      if (!this.verifyWebhookSignature(webhookData)) {
        throw new Error('Invalid webhook signature');
      }

      const confirmation = {
        payment_id: webhookData.payment_id,
        status: webhookData.status,
        transaction_id: webhookData.transaction_id,
        confirmed_amount: webhookData.amount,
        payment_method: webhookData.payment_method,
        confirmed_at: new Date().toISOString(),
        provider: 'clickpesa',
        raw_data: webhookData
      };

      return confirmation;
    } catch (error) {
      logger.error('ClickPesa webhook processing error:', {
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Verify ClickPesa webhook signature
   */
  verifyWebhookSignature(webhookData) {
    if (!webhookData.signature || !this.secretKey) {
      return false;
    }

    const { signature, ...dataToVerify } = webhookData;
    const expectedSignature = this.generateClickPesaSignature(dataToVerify);

    return signature === expectedSignature;
  }

  /**
   * Get payment status
   * @param {string} paymentId - Payment ID
   * @returns {Promise<Object>} Payment status
   */
  async getPaymentStatus(paymentId) {
    try {
      logger.info('Getting ClickPesa payment status:', { paymentId });

      // If ClickPesa is not configured, return manual status
      if (!this.apiKey) {
        return {
          payment_id: paymentId,
          status: 'pending_manual',
          message: 'Manual verification required',
          provider: 'manual'
        };
      }

      const response = await this.clickpesaClient.get(`/payments/${paymentId}`);

      if (response.data && response.data.success) {
        return {
          payment_id: paymentId,
          status: response.data.status,
          amount: response.data.amount,
          currency: response.data.currency,
          payment_method: response.data.payment_method,
          transaction_id: response.data.transaction_id,
          created_at: response.data.created_at,
          updated_at: response.data.updated_at,
          provider: 'clickpesa'
        };
      } else {
        throw new Error('Failed to retrieve payment status');
      }

    } catch (error) {
      logger.error('ClickPesa payment status error:', {
        error: error.message,
        paymentId
      });

      // Return error status instead of throwing
      return {
        payment_id: paymentId,
        status: 'error',
        error: error.message,
        provider: 'clickpesa'
      };
    }
  }

  /**
   * Get supported payment methods for Tanzania
   * @returns {Array} Supported payment methods
   */
  getSupportedMethods() {
    return [
      {
        id: 'mpesa',
        name: 'M-Pesa',
        description: 'Mobile money payment via M-Pesa',
        icon: 'fas fa-mobile-alt',
        popular: true
      },
      {
        id: 'tigo_pesa',
        name: 'Tigo Pesa',
        description: 'Mobile money payment via Tigo Pesa',
        icon: 'fas fa-mobile-alt',
        popular: true
      },
      {
        id: 'airtel_money',
        name: 'Airtel Money',
        description: 'Mobile money payment via Airtel Money',
        icon: 'fas fa-mobile-alt',
        popular: true
      },
      {
        id: 'bank_transfer',
        name: 'Bank Transfer',
        description: 'Direct bank transfer',
        icon: 'fas fa-university',
        popular: false
      },
      {
        id: 'cash_on_delivery',
        name: 'Cash on Delivery',
        description: 'Pay cash when your phone is delivered',
        icon: 'fas fa-money-bill-wave',
        popular: true
      }
    ];
  }

  /**
   * Get payment service status
   * @returns {Object} Service status
   */
  getStatus() {
    return {
      provider: 'phone_point_dar',
      status: 'active',
      supported_methods: this.supportedMethods,
      currency: 'TZS',
      country: 'Tanzania'
    };
  }
}

module.exports = new PaymentService();
