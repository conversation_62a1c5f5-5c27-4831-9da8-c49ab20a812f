// Phone Point Dar - Tanzania Local Payment Processing
const logger = require('../utils/logger');

/**
 * Payment Service for Phone Point Dar
 * Handles local Tanzania payment methods (M-Pesa, Tigo Pesa, Airtel Money, Bank Transfer)
 */
class PaymentService {
  constructor() {
    this.supportedMethods = [
      'mpesa',
      'tigo_pesa', 
      'airtel_money',
      'bank_transfer',
      'cash_on_delivery'
    ];
  }

  /**
   * Create payment session for Phone Point Dar
   * @param {Object} paymentData - Payment data
   * @returns {Promise<Object>} Payment result
   */
  async createPayment(paymentData) {
    try {
      logger.info('Creating Phone Point Dar payment session:', {
        method: paymentData.payment_method,
        amount: paymentData.amount,
        currency: 'TZS'
      });

      const paymentSession = {
        id: `ppd_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        amount: paymentData.amount,
        currency: 'TZS',
        payment_method: paymentData.payment_method || 'cash_on_delivery',
        status: 'pending',
        customer_phone: paymentData.customer_phone,
        order_id: paymentData.order_id,
        created_at: new Date().toISOString(),
        expires_at: new Date(Date.now() + 30 * 60 * 1000).toISOString()
      };

      // Simulate different payment method flows
      switch (paymentData.payment_method) {
        case 'mpesa':
          paymentSession.instructions = `Send TZS ${paymentData.amount} to M-Pesa number: 0755-123-456. Reference: ${paymentSession.id}`;
          break;
        case 'tigo_pesa':
          paymentSession.instructions = `Send TZS ${paymentData.amount} to Tigo Pesa number: 0714-123-456. Reference: ${paymentSession.id}`;
          break;
        case 'airtel_money':
          paymentSession.instructions = `Send TZS ${paymentData.amount} to Airtel Money number: 0786-123-456. Reference: ${paymentSession.id}`;
          break;
        case 'bank_transfer':
          paymentSession.instructions = `Transfer TZS ${paymentData.amount} to Account: **********, Bank: CRDB Bank. Reference: ${paymentSession.id}`;
          break;
        case 'cash_on_delivery':
          paymentSession.instructions = `Pay TZS ${paymentData.amount} in cash upon delivery. Order: ${paymentSession.id}`;
          paymentSession.status = 'confirmed';
          break;
        default:
          paymentSession.instructions = `Contact Phone Point Dar for payment instructions. Order: ${paymentSession.id}`;
      }

      return {
        success: true,
        data: paymentSession,
        provider: 'phone_point_dar',
        created_at: new Date().toISOString()
      };
    } catch (error) {
      logger.error('Phone Point Dar payment creation error:', {
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Process payment confirmation (replaces webhook processing)
   * @param {Object} confirmationData - Payment confirmation data
   * @returns {Promise<Object>} Processed confirmation result
   */
  async confirmPayment(confirmationData) {
    try {
      logger.info('Processing Phone Point Dar payment confirmation:', {
        paymentId: confirmationData.payment_id,
        method: confirmationData.payment_method
      });

      const confirmation = {
        payment_id: confirmationData.payment_id,
        status: 'completed',
        transaction_id: `txn_${Date.now()}`,
        confirmed_amount: confirmationData.amount,
        payment_method: confirmationData.payment_method,
        confirmed_at: new Date().toISOString(),
        provider: 'phone_point_dar'
      };

      return confirmation;
    } catch (error) {
      logger.error('Phone Point Dar payment confirmation error:', {
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Get payment status
   * @param {string} paymentId - Payment ID
   * @returns {Promise<Object>} Payment status
   */
  async getPaymentStatus(paymentId) {
    try {
      logger.info('Retrieving payment status:', { paymentId });

      return {
        payment_id: paymentId,
        status: 'pending',
        provider: 'phone_point_dar',
        checked_at: new Date().toISOString()
      };
    } catch (error) {
      logger.error('Payment status retrieval error:', {
        paymentId,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Get supported payment methods for Tanzania
   * @returns {Array} Supported payment methods
   */
  getSupportedMethods() {
    return [
      {
        id: 'mpesa',
        name: 'M-Pesa',
        description: 'Mobile money payment via M-Pesa',
        icon: 'fas fa-mobile-alt',
        popular: true
      },
      {
        id: 'tigo_pesa',
        name: 'Tigo Pesa',
        description: 'Mobile money payment via Tigo Pesa',
        icon: 'fas fa-mobile-alt',
        popular: true
      },
      {
        id: 'airtel_money',
        name: 'Airtel Money',
        description: 'Mobile money payment via Airtel Money',
        icon: 'fas fa-mobile-alt',
        popular: true
      },
      {
        id: 'bank_transfer',
        name: 'Bank Transfer',
        description: 'Direct bank transfer',
        icon: 'fas fa-university',
        popular: false
      },
      {
        id: 'cash_on_delivery',
        name: 'Cash on Delivery',
        description: 'Pay cash when your phone is delivered',
        icon: 'fas fa-money-bill-wave',
        popular: true
      }
    ];
  }

  /**
   * Get payment service status
   * @returns {Object} Service status
   */
  getStatus() {
    return {
      provider: 'phone_point_dar',
      status: 'active',
      supported_methods: this.supportedMethods,
      currency: 'TZS',
      country: 'Tanzania'
    };
  }
}

module.exports = new PaymentService();
