/**
 * Session Management Service
 * Manages user sessions, detects suspicious activity, and enforces security policies
 */

const redisClient = require('../utils/redisClient');
const logger = require('../utils/logger');
const config = require('../config/config');
const jwtBlacklistService = require('./jwtBlacklistService');
const auditLogService = require('./auditLogService');
const User = require('../models/User');

class SessionManagementService {
  constructor() {
    this.SESSION_PREFIX = 'user_session:';
    this.SESSION_DATA_PREFIX = 'session_data:';
    this.SUSPICIOUS_ACTIVITY_PREFIX = 'suspicious_activity:';
    this.LOGIN_ATTEMPTS_PREFIX = 'login_attempts:';
    this.DEVICE_HISTORY_PREFIX = 'device_history:';
  }

  /**
   * Create a new session
   * @param {string} userId - User ID
   * @param {string} token - JWT token
   * @param {Object} sessionData - Session data (IP, device, etc.)
   * @returns {Promise<boolean>}
   */
  async createSession(userId, token, sessionData) {
    try {
      // Track token in user's active sessions
      await jwtBlacklistService.trackUserSession(userId, token);
      
      // Store session data
      const sessionKey = `${this.SESSION_DATA_PREFIX}${token}`;
      await redisClient.setex(
        sessionKey,
        this.getTokenTTL(token),
        JSON.stringify({
          ...sessionData,
          created_at: new Date().toISOString(),
          user_id: userId
        })
      );
      
      // Check for suspicious activity
      const isSuspicious = await this.checkSuspiciousActivity(userId, sessionData);
      if (isSuspicious) {
        logger.warn(`Suspicious activity detected for user ${userId}`, {
          ip: sessionData.ip,
          device: sessionData.userAgent
        });
      }
      
      // Add to device history
      await this.addToDeviceHistory(userId, sessionData);
      
      // Enforce session limits
      await jwtBlacklistService.enforceConcurrentSessionLimit(
        userId, 
        config.MAX_CONCURRENT_SESSIONS
      );
      
      return true;
    } catch (error) {
      logger.error('Error creating session:', error);
      return false;
    }
  }

  /**
   * End a session
   * @param {string} userId - User ID
   * @param {string} token - JWT token
   * @returns {Promise<boolean>}
   */
  async endSession(userId, token) {
    try {
      // Blacklist token
      await jwtBlacklistService.blacklistToken(token, 'logout');
      
      // Remove from user sessions
      await jwtBlacklistService.removeUserSession(userId, token);
      
      // Remove session data
      const sessionKey = `${this.SESSION_DATA_PREFIX}${token}`;
      await redisClient.del(sessionKey);
      
      return true;
    } catch (error) {
      logger.error('Error ending session:', error);
      return false;
    }
  }

  /**
   * End all sessions for a user
   * @param {string} userId - User ID
   * @param {string} reason - Reason for ending sessions
   * @returns {Promise<boolean>}
   */
  async endAllSessions(userId, reason = 'logout_all') {
    try {
      // Blacklist all tokens
      await jwtBlacklistService.blacklistAllUserTokens(userId, reason);
      
      // Get all session keys
      const sessionKey = `${this.SESSION_PREFIX}${userId}`;
      const sessions = await redisClient.smembers(sessionKey);
      
      // Remove all session data
      const sessionDataKeys = sessions.map(token => `${this.SESSION_DATA_PREFIX}${token}`);
      if (sessionDataKeys.length > 0) {
        await redisClient.del(...sessionDataKeys);
      }
      
      return true;
    } catch (error) {
      logger.error('Error ending all sessions:', error);
      return false;
    }
  }

  /**
   * Get active sessions for a user
   * @param {string} userId - User ID
   * @returns {Promise<Array>}
   */
  async getActiveSessions(userId) {
    try {
      const sessionKey = `${this.SESSION_PREFIX}${userId}`;
      const tokens = await redisClient.smembers(sessionKey);
      
      const sessions = [];
      for (const token of tokens) {
        const sessionDataKey = `${this.SESSION_DATA_PREFIX}${token}`;
        const sessionData = await redisClient.get(sessionDataKey);
        
        if (sessionData) {
          sessions.push({
            ...JSON.parse(sessionData),
            token_preview: this.getTokenPreview(token)
          });
        }
      }
      
      return sessions;
    } catch (error) {
      logger.error('Error getting active sessions:', error);
      return [];
    }
  }

  /**
   * Check for suspicious activity
   * @param {string} userId - User ID
   * @param {Object} sessionData - Session data
   * @returns {Promise<boolean>}
   */
  async checkSuspiciousActivity(userId, sessionData) {
    try {
      // Get device history
      const deviceHistoryKey = `${this.DEVICE_HISTORY_PREFIX}${userId}`;
      const deviceHistory = await redisClient.lrange(deviceHistoryKey, 0, -1);
      
      // If this is the first login, not suspicious
      if (deviceHistory.length === 0) {
        return false;
      }
      
      // Check if this device/IP has been used before
      const knownDevice = deviceHistory.some(item => {
        const data = JSON.parse(item);
        return data.ip === sessionData.ip || data.userAgent === sessionData.userAgent;
      });
      
      // If unknown device/location, mark as suspicious
      if (!knownDevice) {
        // Record suspicious activity
        const suspiciousKey = `${this.SUSPICIOUS_ACTIVITY_PREFIX}${userId}`;
        await redisClient.lpush(suspiciousKey, JSON.stringify({
          timestamp: new Date().toISOString(),
          ip: sessionData.ip,
          userAgent: sessionData.userAgent,
          reason: 'unknown_device'
        }));
        
        // Trim list to last 10 entries
        await redisClient.ltrim(suspiciousKey, 0, 9);
        
        return true;
      }
      
      return false;
    } catch (error) {
      logger.error('Error checking suspicious activity:', error);
      return false;
    }
  }

  /**
   * Add to device history
   * @param {string} userId - User ID
   * @param {Object} sessionData - Session data
   * @returns {Promise<boolean>}
   */
  async addToDeviceHistory(userId, sessionData) {
    try {
      const deviceHistoryKey = `${this.DEVICE_HISTORY_PREFIX}${userId}`;
      
      // Add to device history
      await redisClient.lpush(deviceHistoryKey, JSON.stringify({
        timestamp: new Date().toISOString(),
        ip: sessionData.ip,
        userAgent: sessionData.userAgent
      }));
      
      // Keep only last 10 devices
      await redisClient.ltrim(deviceHistoryKey, 0, 9);
      
      return true;
    } catch (error) {
      logger.error('Error adding to device history:', error);
      return false;
    }
  }

  /**
   * Get token TTL (time to live)
   * @param {string} token - JWT token
   * @returns {number} TTL in seconds
   */
  getTokenTTL(token) {
    try {
      const decoded = require('jsonwebtoken').decode(token);
      if (!decoded || !decoded.exp) {
        return 86400; // Default 1 day
      }
      
      const now = Math.floor(Date.now() / 1000);
      const ttl = decoded.exp - now;
      
      return ttl > 0 ? ttl : 0;
    } catch (error) {
      logger.error('Error getting token TTL:', error);
      return 86400; // Default 1 day
    }
  }

  /**
   * Get token preview (first and last few characters)
   * @param {string} token - JWT token
   * @returns {string} Token preview
   */
  getTokenPreview(token) {
    if (!token || token.length < 10) {
      return token;
    }
    
    return `${token.substring(0, 6)}...${token.substring(token.length - 4)}`;
  }
}

module.exports = new SessionManagementService();
