/**
 * Security Monitoring Service for Phone Point Dar
 * Monitors and logs security events, detects suspicious activities
 */

const logger = require('../utils/logger');
const cacheService = require('./cacheService');

class SecurityMonitoringService {
  constructor() {
    this.suspiciousActivities = new Map();
    this.securityMetrics = {
      failedLogins: 0,
      blockedIPs: 0,
      suspiciousRequests: 0,
      rateLimitHits: 0
    };
  }

  /**
   * Log security event
   * @param {string} eventType - Type of security event
   * @param {Object} details - Event details
   * @param {Object} req - Express request object
   */
  logSecurityEvent(eventType, details, req = null) {
    const securityEvent = {
      eventType,
      timestamp: new Date().toISOString(),
      details,
      ip: req?.ip || 'unknown',
      userAgent: req?.get('User-Agent') || 'unknown',
      userId: req?.user?.id || null,
      endpoint: req?.originalUrl || 'unknown',
      method: req?.method || 'unknown'
    };

    // Log to console and file
    logger.warn(`Security Event [${eventType}]:`, securityEvent);

    // Store in cache for analysis
    this.storeSecurityEvent(securityEvent);

    // Check for suspicious patterns
    this.analyzeSecurityEvent(securityEvent);

    return securityEvent;
  }

  /**
   * Store security event for analysis
   * @param {Object} event - Security event
   */
  async storeSecurityEvent(event) {
    try {
      const key = `security:events:${Date.now()}`;
      await cacheService.set(key, event, 86400); // Store for 24 hours

      // Update metrics
      this.updateSecurityMetrics(event.eventType);
    } catch (error) {
      logger.error('Failed to store security event:', error);
    }
  }

  /**
   * Update security metrics
   * @param {string} eventType - Type of security event
   */
  updateSecurityMetrics(eventType) {
    switch (eventType) {
      case 'failed_login':
        this.securityMetrics.failedLogins++;
        break;
      case 'ip_blocked':
        this.securityMetrics.blockedIPs++;
        break;
      case 'suspicious_request':
        this.securityMetrics.suspiciousRequests++;
        break;
      case 'rate_limit_hit':
        this.securityMetrics.rateLimitHits++;
        break;
    }
  }

  /**
   * Analyze security event for suspicious patterns
   * @param {Object} event - Security event
   */
  analyzeSecurityEvent(event) {
    const { ip, eventType, userId } = event;

    // Track activities by IP
    if (!this.suspiciousActivities.has(ip)) {
      this.suspiciousActivities.set(ip, {
        failedLogins: 0,
        suspiciousRequests: 0,
        lastActivity: new Date(),
        events: []
      });
    }

    const ipActivity = this.suspiciousActivities.get(ip);
    ipActivity.events.push(event);
    ipActivity.lastActivity = new Date();

    // Analyze patterns
    switch (eventType) {
      case 'failed_login':
        ipActivity.failedLogins++;
        if (ipActivity.failedLogins >= 5) {
          this.handleSuspiciousActivity('multiple_failed_logins', ip, event);
        }
        break;

      case 'suspicious_request':
        ipActivity.suspiciousRequests++;
        if (ipActivity.suspiciousRequests >= 3) {
          this.handleSuspiciousActivity('multiple_suspicious_requests', ip, event);
        }
        break;
    }

    // Clean up old activities (older than 1 hour)
    this.cleanupOldActivities();
  }

  /**
   * Handle suspicious activity
   * @param {string} activityType - Type of suspicious activity
   * @param {string} ip - IP address
   * @param {Object} event - Triggering event
   */
  async handleSuspiciousActivity(activityType, ip, event) {
    logger.error(`Suspicious Activity Detected [${activityType}]:`, {
      ip,
      activityType,
      event,
      timestamp: new Date().toISOString()
    });

    // Store suspicious activity
    const suspiciousActivity = {
      activityType,
      ip,
      event,
      timestamp: new Date().toISOString(),
      severity: this.getSeverityLevel(activityType)
    };

    try {
      const key = `security:suspicious:${ip}:${Date.now()}`;
      await cacheService.set(key, suspiciousActivity, 86400);
    } catch (error) {
      logger.error('Failed to store suspicious activity:', error);
    }

    // Take action based on severity
    await this.takeSecurityAction(activityType, ip, suspiciousActivity);
  }

  /**
   * Get severity level for activity type
   * @param {string} activityType - Type of activity
   * @returns {string} Severity level
   */
  getSeverityLevel(activityType) {
    const severityMap = {
      'multiple_failed_logins': 'high',
      'multiple_suspicious_requests': 'medium',
      'sql_injection_attempt': 'critical',
      'xss_attempt': 'high',
      'brute_force_attack': 'critical',
      'unusual_user_agent': 'low',
      'rapid_requests': 'medium'
    };

    return severityMap[activityType] || 'low';
  }

  /**
   * Take security action based on activity
   * @param {string} activityType - Type of activity
   * @param {string} ip - IP address
   * @param {Object} activity - Activity details
   */
  async takeSecurityAction(activityType, ip, activity) {
    const { severity } = activity;

    switch (severity) {
      case 'critical':
        await this.blockIP(ip, 3600); // Block for 1 hour
        await this.sendSecurityAlert('critical', activity);
        break;

      case 'high':
        await this.blockIP(ip, 1800); // Block for 30 minutes
        await this.sendSecurityAlert('high', activity);
        break;

      case 'medium':
        await this.increaseRateLimit(ip, 900); // Increase rate limiting for 15 minutes
        break;

      case 'low':
        // Just log for now
        logger.info(`Low severity security event logged for IP: ${ip}`);
        break;
    }
  }

  /**
   * Block IP address
   * @param {string} ip - IP address to block
   * @param {number} duration - Block duration in seconds
   */
  async blockIP(ip, duration) {
    try {
      const blockKey = `security:blocked:${ip}`;
      await cacheService.set(blockKey, {
        blockedAt: new Date().toISOString(),
        duration,
        reason: 'Suspicious activity detected'
      }, duration);

      logger.warn(`IP blocked: ${ip} for ${duration} seconds`);
      this.logSecurityEvent('ip_blocked', { ip, duration });
    } catch (error) {
      logger.error('Failed to block IP:', error);
    }
  }

  /**
   * Check if IP is blocked
   * @param {string} ip - IP address to check
   * @returns {Promise<boolean>} True if blocked
   */
  async isIPBlocked(ip) {
    try {
      const blockKey = `security:blocked:${ip}`;
      const blockInfo = await cacheService.get(blockKey);
      return !!blockInfo;
    } catch (error) {
      logger.error('Failed to check IP block status:', error);
      return false;
    }
  }

  /**
   * Increase rate limiting for IP
   * @param {string} ip - IP address
   * @param {number} duration - Duration in seconds
   */
  async increaseRateLimit(ip, duration) {
    try {
      const rateLimitKey = `security:ratelimit:${ip}`;
      await cacheService.set(rateLimitKey, {
        increasedAt: new Date().toISOString(),
        duration,
        multiplier: 0.5 // Reduce allowed requests by 50%
      }, duration);

      logger.info(`Rate limit increased for IP: ${ip} for ${duration} seconds`);
    } catch (error) {
      logger.error('Failed to increase rate limit:', error);
    }
  }

  /**
   * Send security alert
   * @param {string} severity - Alert severity
   * @param {Object} activity - Activity details
   */
  async sendSecurityAlert(severity, activity) {
    // In a real application, you would send alerts via:
    // - Email
    // - Slack/Discord webhook
    // - SMS
    // - Push notification to admin app
    
    logger.error(`SECURITY ALERT [${severity.toUpperCase()}]:`, {
      severity,
      activity,
      timestamp: new Date().toISOString(),
      system: 'Phone Point Dar'
    });

    // Store alert for admin dashboard
    try {
      const alertKey = `security:alerts:${Date.now()}`;
      await cacheService.set(alertKey, {
        severity,
        activity,
        timestamp: new Date().toISOString(),
        acknowledged: false
      }, 604800); // Store for 7 days
    } catch (error) {
      logger.error('Failed to store security alert:', error);
    }
  }

  /**
   * Clean up old activities
   */
  cleanupOldActivities() {
    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
    
    for (const [ip, activity] of this.suspiciousActivities.entries()) {
      if (activity.lastActivity < oneHourAgo) {
        this.suspiciousActivities.delete(ip);
      }
    }
  }

  /**
   * Get security metrics
   * @returns {Object} Security metrics
   */
  getSecurityMetrics() {
    return {
      ...this.securityMetrics,
      activeMonitoredIPs: this.suspiciousActivities.size,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Get recent security events
   * @param {number} limit - Number of events to return
   * @returns {Promise<Array>} Recent security events
   */
  async getRecentSecurityEvents(limit = 50) {
    try {
      // This would typically query a database
      // For now, we'll return cached events
      const events = [];
      // Implementation would fetch from cache/database
      return events;
    } catch (error) {
      logger.error('Failed to get recent security events:', error);
      return [];
    }
  }

  /**
   * Middleware to check for blocked IPs
   */
  createIPBlockMiddleware() {
    return async (req, res, next) => {
      const ip = req.ip;
      
      try {
        const isBlocked = await this.isIPBlocked(ip);
        
        if (isBlocked) {
          this.logSecurityEvent('blocked_ip_access_attempt', { ip }, req);
          
          return res.status(403).json({
            success: false,
            message: 'Access denied. Your IP has been temporarily blocked due to suspicious activity.',
            statusCode: 403,
            timestamp: new Date().toISOString()
          });
        }
        
        next();
      } catch (error) {
        logger.error('Error in IP block middleware:', error);
        next(); // Continue on error to avoid blocking legitimate users
      }
    };
  }

  /**
   * Middleware to detect suspicious requests
   */
  createSuspiciousRequestMiddleware() {
    return (req, res, next) => {
      const suspiciousPatterns = [
        /(\<script\>|\<\/script\>)/i, // XSS attempts
        /(union|select|insert|delete|drop|create|alter)/i, // SQL injection
        /(\.\.\/|\.\.\\)/g, // Path traversal
        /(<|>|"|'|&|;|\|)/g // Potential injection characters
      ];

      const checkString = `${req.originalUrl} ${JSON.stringify(req.query)} ${JSON.stringify(req.body)}`;
      
      for (const pattern of suspiciousPatterns) {
        if (pattern.test(checkString)) {
          this.logSecurityEvent('suspicious_request', {
            pattern: pattern.toString(),
            url: req.originalUrl,
            method: req.method,
            query: req.query,
            body: req.body
          }, req);
          break;
        }
      }

      next();
    };
  }
}

// Create singleton instance
const securityMonitoringService = new SecurityMonitoringService();

module.exports = securityMonitoringService;
