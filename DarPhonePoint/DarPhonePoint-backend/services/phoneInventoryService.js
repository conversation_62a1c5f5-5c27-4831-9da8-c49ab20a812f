/**
 * Phone Inventory Service
 * Handles IMEI tracking and phone-specific inventory management
 */

const Inventory = require('../models/Inventory');
const Product = require('../models/Product');
const AppError = require('../utils/AppError');
const logger = require('../utils/logger');

/**
 * Reserve a specific phone by IMEI for cart/order
 * @param {String} productId - Product ID
 * @param {String} variantSku - Variant SKU
 * @param {String} imei - IMEI number
 * @param {String} reservedFor - User ID or session ID
 * @returns {Promise<Object>} Reservation result
 */
exports.reservePhoneByIMEI = async (productId, variantSku, imei, reservedFor) => {
  try {
    // Find the inventory item with the specific IMEI
    const inventoryItem = await Inventory.findOne({
      product: productId,
      variant_sku: variantSku,
      'devices.imei': imei,
      'devices.status': 'available'
    });

    if (!inventoryItem) {
      throw new AppError('Phone with this IMEI not found or not available', 404);
    }

    // Find the specific device
    const device = inventoryItem.devices.find(d => d.imei === imei);
    if (!device) {
      throw new AppError('Device not found', 404);
    }

    // Reserve the device
    device.status = 'reserved';
    device.reserved_for = reservedFor;
    device.reserved_at = new Date();
    device.reservation_expires = new Date(Date.now() + 30 * 60 * 1000); // 30 minutes

    await inventoryItem.save();

    logger.info(`Phone reserved: IMEI ${imei} for ${reservedFor}`);

    return {
      success: true,
      device: {
        imei: device.imei,
        condition: device.condition,
        warranty_months: device.warranty_months,
        reserved_until: device.reservation_expires
      }
    };
  } catch (error) {
    logger.error('Error reserving phone by IMEI:', error);
    throw error;
  }
};

/**
 * Release reservation for a phone
 * @param {String} imei - IMEI number
 * @returns {Promise<Object>} Release result
 */
exports.releasePhoneReservation = async (imei) => {
  try {
    const inventoryItem = await Inventory.findOne({
      'devices.imei': imei,
      'devices.status': 'reserved'
    });

    if (!inventoryItem) {
      throw new AppError('Reserved phone not found', 404);
    }

    const device = inventoryItem.devices.find(d => d.imei === imei);
    if (device) {
      device.status = 'available';
      device.reserved_for = null;
      device.reserved_at = null;
      device.reservation_expires = null;

      await inventoryItem.save();
      logger.info(`Phone reservation released: IMEI ${imei}`);
    }

    return { success: true };
  } catch (error) {
    logger.error('Error releasing phone reservation:', error);
    throw error;
  }
};

/**
 * Mark phone as sold
 * @param {String} imei - IMEI number
 * @param {String} orderId - Order ID
 * @returns {Promise<Object>} Sale result
 */
exports.markPhoneAsSold = async (imei, orderId) => {
  try {
    const inventoryItem = await Inventory.findOne({
      'devices.imei': imei
    });

    if (!inventoryItem) {
      throw new AppError('Phone not found in inventory', 404);
    }

    const device = inventoryItem.devices.find(d => d.imei === imei);
    if (!device) {
      throw new AppError('Device not found', 404);
    }

    // Mark as sold
    device.status = 'sold';
    device.sold_to_order = orderId;
    device.sold_at = new Date();
    device.reserved_for = null;
    device.reserved_at = null;
    device.reservation_expires = null;

    // Update inventory quantities
    inventoryItem.quantity_available = Math.max(0, inventoryItem.quantity_available - 1);
    inventoryItem.quantity_sold += 1;

    await inventoryItem.save();

    logger.info(`Phone marked as sold: IMEI ${imei} to order ${orderId}`);

    return { success: true };
  } catch (error) {
    logger.error('Error marking phone as sold:', error);
    throw error;
  }
};

/**
 * Get available phones for a product variant
 * @param {String} productId - Product ID
 * @param {String} variantSku - Variant SKU
 * @returns {Promise<Array>} Available phones
 */
exports.getAvailablePhones = async (productId, variantSku) => {
  try {
    const inventoryItem = await Inventory.findOne({
      product: productId,
      variant_sku: variantSku
    }).populate('product', 'name brand model');

    if (!inventoryItem) {
      return [];
    }

    const availableDevices = inventoryItem.devices.filter(device => 
      device.status === 'available'
    );

    return availableDevices.map(device => ({
      imei: device.imei,
      condition: device.condition,
      warranty_months: device.warranty_months,
      location: inventoryItem.location,
      product_name: inventoryItem.product.name
    }));
  } catch (error) {
    logger.error('Error getting available phones:', error);
    throw error;
  }
};

/**
 * Clean up expired reservations
 * @returns {Promise<Number>} Number of reservations cleaned up
 */
exports.cleanupExpiredReservations = async () => {
  try {
    const now = new Date();
    let cleanedCount = 0;

    const inventoryItems = await Inventory.find({
      'devices.status': 'reserved',
      'devices.reservation_expires': { $lt: now }
    });

    for (const item of inventoryItems) {
      let updated = false;
      
      for (const device of item.devices) {
        if (device.status === 'reserved' && device.reservation_expires < now) {
          device.status = 'available';
          device.reserved_for = null;
          device.reserved_at = null;
          device.reservation_expires = null;
          updated = true;
          cleanedCount++;
        }
      }

      if (updated) {
        await item.save();
      }
    }

    if (cleanedCount > 0) {
      logger.info(`Cleaned up ${cleanedCount} expired phone reservations`);
    }

    return cleanedCount;
  } catch (error) {
    logger.error('Error cleaning up expired reservations:', error);
    throw error;
  }
};

/**
 * Add new phone to inventory
 * @param {Object} phoneData - Phone data including IMEI
 * @returns {Promise<Object>} Added phone result
 */
exports.addPhoneToInventory = async (phoneData) => {
  try {
    const {
      productId,
      variantSku,
      imei,
      condition = 'new',
      warrantyMonths = 12,
      location = 'main_warehouse',
      purchasePrice,
      supplier
    } = phoneData;

    // Validate IMEI format
    if (!/^\d{15}$/.test(imei)) {
      throw new AppError('Invalid IMEI format. IMEI must be 15 digits', 400);
    }

    // Check if IMEI already exists
    const existingPhone = await Inventory.findOne({
      'devices.imei': imei
    });

    if (existingPhone) {
      throw new AppError('Phone with this IMEI already exists in inventory', 400);
    }

    // Find or create inventory item
    let inventoryItem = await Inventory.findOne({
      product: productId,
      variant_sku: variantSku,
      location: location
    });

    if (!inventoryItem) {
      inventoryItem = new Inventory({
        product: productId,
        variant_sku: variantSku,
        location: location,
        quantity_available: 0,
        quantity_reserved: 0,
        quantity_sold: 0,
        devices: []
      });
    }

    // Add the new device
    const newDevice = {
      imei: imei,
      condition: condition,
      warranty_months: warrantyMonths,
      status: 'available',
      received_at: new Date(),
      purchase_price: purchasePrice,
      supplier: supplier
    };

    inventoryItem.devices.push(newDevice);
    inventoryItem.quantity_available += 1;

    await inventoryItem.save();

    logger.info(`New phone added to inventory: IMEI ${imei}`);

    return {
      success: true,
      device: newDevice
    };
  } catch (error) {
    logger.error('Error adding phone to inventory:', error);
    throw error;
  }
};

/**
 * Get phone details by IMEI
 * @param {String} imei - IMEI number
 * @returns {Promise<Object>} Phone details
 */
exports.getPhoneByIMEI = async (imei) => {
  try {
    const inventoryItem = await Inventory.findOne({
      'devices.imei': imei
    }).populate('product', 'name brand model price');

    if (!inventoryItem) {
      throw new AppError('Phone not found', 404);
    }

    const device = inventoryItem.devices.find(d => d.imei === imei);
    if (!device) {
      throw new AppError('Device not found', 404);
    }

    return {
      imei: device.imei,
      condition: device.condition,
      warranty_months: device.warranty_months,
      status: device.status,
      location: inventoryItem.location,
      product: inventoryItem.product,
      variant_sku: inventoryItem.variant_sku,
      received_at: device.received_at,
      sold_at: device.sold_at,
      reserved_for: device.reserved_for,
      reservation_expires: device.reservation_expires
    };
  } catch (error) {
    logger.error('Error getting phone by IMEI:', error);
    throw error;
  }
};
