const Redis = require('ioredis');
const config = require('../config/config');
const logger = require('./errorLoggingService').logger;

/**
 * Cache service using Redis
 */
class CacheService {
  constructor() {
    this.client = new Redis({
      host: config.REDIS_HOST,
      port: config.REDIS_PORT,
      password: config.REDIS_PASSWORD,
      retryStrategy: (times) => {
        const delay = Math.min(times * 50, 2000);
        return delay;
      }
    });

    this.client.on('error', (err) => {
      logger.error('Redis Client Error:', err);
    });

    this.client.on('connect', () => {
      logger.info('Redis Client Connected');
    });
  }

  /**
   * Get value from cache
   * @param {string} key - Cache key
   * @returns {Promise<any>} Cached value
   */
  async get(key) {
    try {
      const value = await this.client.get(key);
      return value ? JSON.parse(value) : null;
    } catch (error) {
      logger.error('Cache Get Error:', error);
      return null;
    }
  }

  /**
   * Set value in cache
   * @param {string} key - Cache key
   * @param {any} value - Value to cache
   * @param {number} ttl - Time to live in seconds
   */
  async set(key, value, ttl = 3600) {
    try {
      const stringValue = JSON.stringify(value);
      await this.client.set(key, stringValue, 'EX', ttl);
      return true;
    } catch (error) {
      logger.error('Cache Set Error:', error);
      return false;
    }
  }

  /**
   * Delete value from cache
   * @param {string} key - Cache key
   */
  async del(key) {
    try {
      await this.client.del(key);
      return true;
    } catch (error) {
      logger.error('Cache Delete Error:', error);
      return false;
    }
  }

  /**
   * Clear all cache
   */
  async clear() {
    try {
      await this.client.flushall();
      return true;
    } catch (error) {
      logger.error('Cache Clear Error:', error);
      return false;
    }
  }

  /**
   * Get multiple values from cache
   * @param {string[]} keys - Array of cache keys
   * @returns {Promise<Object>} Object with key-value pairs
   */
  async mget(keys) {
    try {
      const values = await this.client.mget(keys);
      return keys.reduce((acc, key, index) => {
        acc[key] = values[index] ? JSON.parse(values[index]) : null;
        return acc;
      }, {});
    } catch (error) {
      logger.error('Cache mget error:', error);
      return {};
    }
  }

  /**
   * Set multiple values in cache
   * @param {Object} keyValues - Object with key-value pairs
   * @param {number} ttl - Time to live in seconds
   */
  async mset(keyValues, ttl = 3600) {
    try {
      const pipeline = this.client.pipeline();
      Object.entries(keyValues).forEach(([key, value]) => {
        pipeline.set(key, JSON.stringify(value), 'EX', ttl);
      });
      await pipeline.exec();
    } catch (error) {
      logger.error('Cache mset error:', error);
    }
  }

  /**
   * Get hash field
   * @param {string} key - Hash key
   * @param {string} field - Hash field
   * @returns {Promise<any>} - Cached data or null
   */
  async hget(key, field) {
    try {
      const data = await this.client.hget(key, field);
      return data ? JSON.parse(data) : null;
    } catch (error) {
      logger.error(`Cache hget error: ${error.message}`);
      return null;
    }
  }

  /**
   * Set hash field
   * @param {string} key - Hash key
   * @param {string} field - Hash field
   * @param {any} data - Data to cache
   * @param {number} ttl - Time to live in seconds
   * @returns {Promise<boolean>} - Success status
   */
  async hset(key, field, data, ttl = 3600) {
    try {
      await this.client.hset(key, field, JSON.stringify(data));

      if (ttl) {
        await this.client.expire(key, ttl);
      }

      return true;
    } catch (error) {
      logger.error(`Cache hset error: ${error.message}`);
      return false;
    }
  }

  /**
   * Get all hash fields
   * @param {string} key - Hash key
   * @returns {Promise<Object>} - Hash data or empty object
   */
  async hgetall(key) {
    try {
      const data = await this.client.hgetall(key);

      if (!data) return {};

      // Parse all values
      const result = {};
      Object.keys(data).forEach((field, index) => {
        if (index % 2 === 0) {
          result[field] = JSON.parse(data[index + 1]);
        }
      });

      return result;
    } catch (error) {
      logger.error(`Cache hgetall error: ${error.message}`);
      return {};
    }
  }

  /**
   * Delete hash field
   * @param {string} key - Hash key
   * @param {string} field - Hash field
   * @returns {Promise<boolean>} - Success status
   */
  async hdel(key, field) {
    try {
      await this.client.hdel(key, field);
      return true;
    } catch (error) {
      logger.error(`Cache hdel error: ${error.message}`);
      return false;
    }
  }

  /**
   * Increment counter
   * @param {string} key - Counter key
   * @param {number} ttl - Time to live in seconds
   * @returns {Promise<number>} - New counter value
   */
  async incr(key, ttl = 3600) {
    try {
      const value = await this.client.incr(key);

      if (ttl) {
        await this.client.expire(key, ttl);
      }

      return value;
    } catch (error) {
      logger.error(`Cache incr error: ${error.message}`);
      return 0;
    }
  }

  /**
   * Decrement counter
   * @param {string} key - Counter key
   * @param {number} ttl - Time to live in seconds
   * @returns {Promise<number>} - New counter value
   */
  async decr(key, ttl = 3600) {
    try {
      const value = await this.client.decr(key);

      if (ttl) {
        await this.client.expire(key, ttl);
      }

      return value;
    } catch (error) {
      logger.error(`Cache decr error: ${error.message}`);
      return 0;
    }
  }

  /**
   * Cache middleware for Express routes
   * @param {number} duration - Cache duration in seconds
   * @returns {Function} Express middleware
   */
  cacheMiddleware(duration = 3600) {
    return async (req, res, next) => {
      if (req.method !== 'GET') {
        return next();
      }

      const key = `cache:${req.originalUrl || req.url}`;
      const cachedResponse = await this.get(key);

      if (cachedResponse) {
        return res.json(cachedResponse);
      }

      res.originalJson = res.json;
      res.json = (body) => {
        this.set(key, body, duration);
        res.originalJson(body);
      };

      next();
    };
  }

  /**
   * Cache user data
   * @param {string} userId - User ID
   * @param {any} userData - User data to cache
   * @returns {Promise<boolean>} - Success status
   */
  async cacheUser(userId, userData) {
    const key = `user:${userId}`;
    return this.set(key, userData, 3600); // Cache for 1 hour
  }

  /**
   * Get cached user data
   * @param {string} userId - User ID
   * @returns {Promise<any>} - Cached user data or null
   */
  async getCachedUser(userId) {
    const key = `user:${userId}`;
    return this.get(key);
  }

  /**
   * Cache product data
   * @param {string} productId - Product ID
   * @param {any} productData - Product data to cache
   * @returns {Promise<boolean>} - Success status
   */
  async cacheProduct(productId, productData) {
    const key = `product:${productId}`;
    return this.set(key, productData, 3600); // Cache for 1 hour
  }

  /**
   * Get cached product data
   * @param {string} productId - Product ID
   * @returns {Promise<any>} - Cached product data or null
   */
  async getCachedProduct(productId) {
    const key = `product:${productId}`;
    return this.get(key);
  }

  /**
   * Cache product list with pagination
   * @param {number} page - Page number
   * @param {number} limit - Number of items per page
   * @param {Object} filters - Filter criteria
   * @param {any[]} productList - List of products
   * @returns {Promise<boolean>} - Success status
   */
  async cacheProductList(page, limit, filters, productList) {
    const key = `products:${page}:${limit}:${JSON.stringify(filters)}`;
    return this.set(key, productList, 1800); // Cache for 30 minutes
  }

  /**
   * Get cached product list
   * @param {number} page - Page number
   * @param {number} limit - Number of items per page
   * @param {Object} filters - Filter criteria
   * @returns {Promise<any[]>} - Cached product list or null
   */
  async getCachedProductList(page, limit, filters) {
    const key = `products:${page}:${limit}:${JSON.stringify(filters)}`;
    return this.get(key);
  }

  /**
   * Invalidate cache by pattern
   * @param {string} pattern - Cache pattern
   * @returns {Promise<boolean>} - Success status
   */
  async invalidatePattern(pattern) {
    try {
      const keys = await this.client.keys(pattern);
      if (keys.length > 0) {
        await this.client.del(keys);
      }
      return true;
    } catch (error) {
      logger.error('Cache Invalidate Pattern Error:', error);
      return false;
    }
  }

  /**
   * Phone Point Dar specific cache methods
   */

  /**
   * Cache product data with TZS formatting
   * @param {string} productId - Product ID
   * @param {Object} product - Product data
   * @param {number} ttl - Time to live in seconds (default: 1 hour)
   */
  async cacheProduct(productId, product, ttl = 3600) {
    const key = `phonepoint:product:${productId}`;

    // Add formatted pricing for Tanzania market
    const enhancedProduct = {
      ...product,
      formattedPrice: this.formatTZSPrice(product.price),
      formattedComparePrice: product.compare_at_price ? this.formatTZSPrice(product.compare_at_price) : null,
      cachedAt: new Date().toISOString()
    };

    return await this.set(key, enhancedProduct, ttl);
  }

  /**
   * Get cached product
   * @param {string} productId - Product ID
   * @returns {Promise<Object|null>} Product data
   */
  async getCachedProduct(productId) {
    const key = `phonepoint:product:${productId}`;
    return await this.get(key);
  }

  /**
   * Cache product list with pagination info
   * @param {string} queryHash - Hash of query parameters
   * @param {Object} data - Product list data with pagination
   * @param {number} ttl - Time to live in seconds (default: 30 minutes)
   */
  async cacheProductList(queryHash, data, ttl = 1800) {
    const key = `phonepoint:products:${queryHash}`;

    // Enhance products with TZS formatting
    const enhancedData = {
      ...data,
      data: data.data?.map(product => ({
        ...product,
        formattedPrice: this.formatTZSPrice(product.price),
        formattedComparePrice: product.compare_at_price ? this.formatTZSPrice(product.compare_at_price) : null
      })) || [],
      cachedAt: new Date().toISOString()
    };

    return await this.set(key, enhancedData, ttl);
  }

  /**
   * Get cached product list
   * @param {string} queryHash - Hash of query parameters
   * @returns {Promise<Object|null>} Product list data
   */
  async getCachedProductList(queryHash) {
    const key = `phonepoint:products:${queryHash}`;
    return await this.get(key);
  }

  /**
   * Cache inventory data for Phone Point Dar
   * @param {string} productId - Product ID
   * @param {string} variantSku - Variant SKU (optional)
   * @param {Object} inventory - Inventory data
   * @param {number} ttl - Time to live in seconds (default: 10 minutes)
   */
  async cacheInventory(productId, variantSku, inventory, ttl = 600) {
    const key = variantSku
      ? `phonepoint:inventory:${productId}:${variantSku}`
      : `phonepoint:inventory:${productId}`;

    const enhancedInventory = {
      ...inventory,
      stockStatus: this.getStockStatus(inventory.quantity, inventory.low_stock_threshold),
      cachedAt: new Date().toISOString()
    };

    return await this.set(key, enhancedInventory, ttl);
  }

  /**
   * Get cached inventory
   * @param {string} productId - Product ID
   * @param {string} variantSku - Variant SKU (optional)
   * @returns {Promise<Object|null>} Inventory data
   */
  async getCachedInventory(productId, variantSku = null) {
    const key = variantSku
      ? `phonepoint:inventory:${productId}:${variantSku}`
      : `phonepoint:inventory:${productId}`;
    return await this.get(key);
  }

  /**
   * Invalidate product-related caches
   * @param {string} productId - Product ID
   */
  async invalidateProduct(productId) {
    try {
      // Delete specific product cache
      await this.delete(`phonepoint:product:${productId}`);

      // Find and delete related inventory caches
      const inventoryKeys = await this.client.keys(`phonepoint:inventory:${productId}*`);
      if (inventoryKeys.length > 0) {
        await this.client.del(...inventoryKeys);
      }

      // Clear product list caches (they might contain this product)
      const productListKeys = await this.client.keys('phonepoint:products:*');
      if (productListKeys.length > 0) {
        await this.client.del(...productListKeys);
      }

      logger.info(`Invalidated caches for product: ${productId}`);
    } catch (error) {
      logger.error('Error invalidating product cache:', error);
    }
  }

  /**
   * Helper method to format TZS currency
   * @param {number} amount - Amount to format
   * @returns {string} Formatted currency string
   */
  formatTZSPrice(amount) {
    if (typeof amount !== 'number') return 'TZS 0';

    return new Intl.NumberFormat('sw-TZ', {
      style: 'currency',
      currency: 'TZS',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  }

  /**
   * Helper method to get stock status
   * @param {number} quantity - Current stock quantity
   * @param {number} threshold - Low stock threshold
   * @returns {string} Stock status
   */
  getStockStatus(quantity, threshold = 5) {
    if (!quantity || quantity <= 0) return 'out_of_stock';
    if (quantity <= threshold) return 'low_stock';
    return 'in_stock';
  }

  /**
   * Generate cache key hash from object
   * @param {Object} obj - Object to hash
   * @returns {string} Hash string
   */
  generateHash(obj) {
    const crypto = require('crypto');
    const str = JSON.stringify(obj, Object.keys(obj).sort());
    return crypto.createHash('md5').update(str).digest('hex');
  }
}

// Create singleton instance
const cacheService = new CacheService();

module.exports = cacheService;
