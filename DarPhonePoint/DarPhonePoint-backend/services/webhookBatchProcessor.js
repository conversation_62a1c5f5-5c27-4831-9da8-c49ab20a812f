const Queue = require('bull');
const config = require('../config/config');
const logger = require('../utils/logger');
const MonitoringService = require('./monitoringService');
const paymentService = require('./paymentService');

class WebhookBatchProcessor {
  constructor() {
    this.queue = new Queue('webhook-processing', {
      redis: {
        host: config.REDIS_HOST,
        port: config.REDIS_PORT,
        password: config.REDIS_PASSWORD
      }
    });
    this.batchSize = 10;
    this.batchTimeout = 5000; // 5 seconds
    this.currentBatch = [];
    this.processingTimeout = null;

    this.setupQueue();
  }

  setupQueue() {
    // Process jobs in batches
    this.queue.process(async (job) => {
      try {
        const webhookData = job.data;
        await this.addToBatch(webhookData);
        return { success: true };
      } catch (error) {
        logger.error('Webhook batch processing error:', error);
        throw error;
      }
    });

    // Handle failed jobs
    this.queue.on('failed', (job, error) => {
      logger.error('Webhook job failed:', {
        jobId: job.id,
        error: error.message
      });
      MonitoringService.logError(error, {
        context: 'webhook-batch',
        jobId: job.id
      });
    });
  }

  async addToBatch(webhookData) {
    this.currentBatch.push(webhookData);

    // Clear existing timeout if any
    if (this.processingTimeout) {
      clearTimeout(this.processingTimeout);
    }

    // Process batch if it reaches the size limit
    if (this.currentBatch.length >= this.batchSize) {
      await this.processBatch();
    } else {
      // Set timeout to process batch after delay
      this.processingTimeout = setTimeout(() => {
        this.processBatch();
      }, this.batchTimeout);
    }
  }

  async processBatch() {
    if (this.currentBatch.length === 0) return;

    const batch = [...this.currentBatch];
    this.currentBatch = [];

    try {
      // Process webhooks in parallel (Stripe only)
      const results = await Promise.allSettled(
        batch.map(webhookData => paymentService.processWebhook(webhookData.payload, webhookData.signature))
      );

      // Log results
      results.forEach((result, index) => {
        if (result.status === 'fulfilled') {
          logger.info('Webhook processed successfully:', {
            webhookId: batch[index].id
          });
        } else {
          logger.error('Webhook processing failed:', {
            webhookId: batch[index].id,
            error: result.reason
          });
          MonitoringService.logError(result.reason, {
            context: 'webhook-batch',
            webhookId: batch[index].id
          });
        }
      });
    } catch (error) {
      logger.error('Batch processing error:', error);
      MonitoringService.logError(error, {
        context: 'webhook-batch',
        batchSize: batch.length
      });
    }
  }

  async addWebhook(webhookData) {
    return this.queue.add(webhookData, {
      attempts: 3,
      backoff: {
        type: 'exponential',
        delay: 1000
      }
    });
  }

  async getQueueStats() {
    const [waiting, active, completed, failed] = await Promise.all([
      this.queue.getWaitingCount(),
      this.queue.getActiveCount(),
      this.queue.getCompletedCount(),
      this.queue.getFailedCount()
    ]);

    return {
      waiting,
      active,
      completed,
      failed,
      batchSize: this.batchSize,
      batchTimeout: this.batchTimeout
    };
  }
}

module.exports = new WebhookBatchProcessor();