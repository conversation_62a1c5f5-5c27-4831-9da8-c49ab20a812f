const path = require('path');
const fs = require('fs').promises;
const crypto = require('crypto');
const config = require('../config/config');
const logger = require('../utils/logger');
const advancedCacheService = require('./advancedCacheService');

class CDNService {
  constructor() {
    this.cdnBaseUrl = process.env.CDN_BASE_URL || config.BASE_URL;
    this.staticPath = path.join(__dirname, '../public');
    this.cachePrefix = 'cdn:';
    
    // Asset optimization settings
    this.optimization = {
      images: {
        maxAge: 31536000, // 1 year
        formats: ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg'],
        compression: true
      },
      scripts: {
        maxAge: 31536000, // 1 year
        formats: ['.js', '.mjs'],
        minify: true,
        gzip: true
      },
      styles: {
        maxAge: 31536000, // 1 year
        formats: ['.css'],
        minify: true,
        gzip: true
      },
      fonts: {
        maxAge: 31536000, // 1 year
        formats: ['.woff', '.woff2', '.ttf', '.eot'],
        compression: false
      },
      documents: {
        maxAge: 86400, // 1 day
        formats: ['.pdf', '.doc', '.docx'],
        compression: true
      }
    };
  }

  /**
   * Generate asset URL with CDN optimization
   * @param {string} assetPath - Asset path
   * @param {Object} options - Optimization options
   * @returns {string} - Optimized asset URL
   */
  getAssetUrl(assetPath, options = {}) {
    try {
      // Remove leading slash if present
      const cleanPath = assetPath.startsWith('/') ? assetPath.slice(1) : assetPath;
      
      // Generate cache-busting hash
      const hash = this.generateAssetHash(cleanPath);
      
      // Build optimized URL
      const baseUrl = options.cdnUrl || this.cdnBaseUrl;
      const versionedPath = this.addVersionToPath(cleanPath, hash);
      
      return `${baseUrl}/${versionedPath}`;
    } catch (error) {
      logger.error('CDN asset URL generation error:', { assetPath, error: error.message });
      return `${this.cdnBaseUrl}/${assetPath}`;
    }
  }

  /**
   * Generate asset hash for cache busting
   * @param {string} assetPath - Asset path
   * @returns {string} - Asset hash
   */
  generateAssetHash(assetPath) {
    try {
      const content = `${assetPath}:${Date.now()}`;
      return crypto.createHash('md5').update(content).digest('hex').substring(0, 8);
    } catch (error) {
      logger.error('Asset hash generation error:', { assetPath, error: error.message });
      return 'default';
    }
  }

  /**
   * Add version hash to asset path
   * @param {string} assetPath - Asset path
   * @param {string} hash - Version hash
   * @returns {string} - Versioned path
   */
  addVersionToPath(assetPath, hash) {
    const ext = path.extname(assetPath);
    const baseName = path.basename(assetPath, ext);
    const dirName = path.dirname(assetPath);
    
    const versionedName = `${baseName}.${hash}${ext}`;
    return dirName === '.' ? versionedName : `${dirName}/${versionedName}`;
  }

  /**
   * Get asset optimization settings
   * @param {string} assetPath - Asset path
   * @returns {Object} - Optimization settings
   */
  getOptimizationSettings(assetPath) {
    const ext = path.extname(assetPath).toLowerCase();
    
    for (const [type, settings] of Object.entries(this.optimization)) {
      if (settings.formats.includes(ext)) {
        return { type, ...settings };
      }
    }
    
    // Default settings for unknown file types
    return {
      type: 'default',
      maxAge: 3600, // 1 hour
      compression: false,
      minify: false,
      gzip: false
    };
  }

  /**
   * Generate cache headers for asset
   * @param {string} assetPath - Asset path
   * @returns {Object} - Cache headers
   */
  getCacheHeaders(assetPath) {
    const settings = this.getOptimizationSettings(assetPath);
    
    const headers = {
      'Cache-Control': `public, max-age=${settings.maxAge}`,
      'ETag': this.generateAssetHash(assetPath),
      'Vary': 'Accept-Encoding'
    };

    // Add immutable directive for long-lived assets
    if (settings.maxAge >= 31536000) {
      headers['Cache-Control'] += ', immutable';
    }

    // Add compression headers
    if (settings.gzip) {
      headers['Content-Encoding'] = 'gzip';
    }

    return headers;
  }

  /**
   * Optimize asset for delivery
   * @param {string} assetPath - Asset path
   * @param {Buffer} content - Asset content
   * @returns {Promise<Buffer>} - Optimized content
   */
  async optimizeAsset(assetPath, content) {
    try {
      const settings = this.getOptimizationSettings(assetPath);
      let optimizedContent = content;

      // Apply compression if enabled
      if (settings.compression || settings.gzip) {
        optimizedContent = await this.compressContent(optimizedContent);
      }

      // Apply minification for scripts and styles
      if (settings.minify) {
        optimizedContent = await this.minifyContent(assetPath, optimizedContent);
      }

      return optimizedContent;
    } catch (error) {
      logger.error('Asset optimization error:', { assetPath, error: error.message });
      return content; // Return original content on error
    }
  }

  /**
   * Compress content using gzip
   * @param {Buffer} content - Content to compress
   * @returns {Promise<Buffer>} - Compressed content
   */
  async compressContent(content) {
    try {
      const zlib = require('zlib');
      return new Promise((resolve, reject) => {
        zlib.gzip(content, (err, compressed) => {
          if (err) reject(err);
          else resolve(compressed);
        });
      });
    } catch (error) {
      logger.error('Content compression error:', error);
      return content;
    }
  }

  /**
   * Minify content based on file type
   * @param {string} assetPath - Asset path
   * @param {Buffer} content - Content to minify
   * @returns {Promise<Buffer>} - Minified content
   */
  async minifyContent(assetPath, content) {
    try {
      const ext = path.extname(assetPath).toLowerCase();
      const contentStr = content.toString();

      if (ext === '.js' || ext === '.mjs') {
        // Basic JavaScript minification (remove comments and extra whitespace)
        const minified = contentStr
          .replace(/\/\*[\s\S]*?\*\//g, '') // Remove block comments
          .replace(/\/\/.*$/gm, '') // Remove line comments
          .replace(/\s+/g, ' ') // Collapse whitespace
          .trim();
        return Buffer.from(minified);
      }

      if (ext === '.css') {
        // Basic CSS minification
        const minified = contentStr
          .replace(/\/\*[\s\S]*?\*\//g, '') // Remove comments
          .replace(/\s+/g, ' ') // Collapse whitespace
          .replace(/;\s*}/g, '}') // Remove unnecessary semicolons
          .trim();
        return Buffer.from(minified);
      }

      return content;
    } catch (error) {
      logger.error('Content minification error:', { assetPath, error: error.message });
      return content;
    }
  }

  /**
   * Cache asset metadata
   * @param {string} assetPath - Asset path
   * @param {Object} metadata - Asset metadata
   * @returns {Promise<boolean>} - Success status
   */
  async cacheAssetMetadata(assetPath, metadata) {
    const cacheKey = `${this.cachePrefix}metadata:${assetPath}`;
    return await advancedCacheService.set(cacheKey, metadata, advancedCacheService.ttl.extended);
  }

  /**
   * Get cached asset metadata
   * @param {string} assetPath - Asset path
   * @returns {Promise<Object|null>} - Asset metadata or null
   */
  async getCachedAssetMetadata(assetPath) {
    const cacheKey = `${this.cachePrefix}metadata:${assetPath}`;
    return await advancedCacheService.get(cacheKey);
  }

  /**
   * Preload critical assets
   * @param {Array<string>} assetPaths - Critical asset paths
   * @returns {Promise<Array<string>>} - Preload URLs
   */
  async preloadCriticalAssets(assetPaths) {
    const preloadUrls = [];
    
    for (const assetPath of assetPaths) {
      try {
        const optimizedUrl = this.getAssetUrl(assetPath);
        const settings = this.getOptimizationSettings(assetPath);
        
        // Cache metadata for faster subsequent requests
        await this.cacheAssetMetadata(assetPath, {
          url: optimizedUrl,
          settings,
          preloaded: true,
          timestamp: Date.now()
        });
        
        preloadUrls.push(optimizedUrl);
      } catch (error) {
        logger.error('Asset preload error:', { assetPath, error: error.message });
      }
    }
    
    return preloadUrls;
  }

  /**
   * Generate preload headers for critical assets
   * @param {Array<string>} assetPaths - Critical asset paths
   * @returns {Promise<string>} - Link header value
   */
  async generatePreloadHeaders(assetPaths) {
    const preloadLinks = [];
    
    for (const assetPath of assetPaths) {
      try {
        const url = this.getAssetUrl(assetPath);
        const settings = this.getOptimizationSettings(assetPath);
        
        let rel = 'preload';
        let as = 'fetch';
        
        // Determine resource type
        if (settings.type === 'styles') as = 'style';
        else if (settings.type === 'scripts') as = 'script';
        else if (settings.type === 'images') as = 'image';
        else if (settings.type === 'fonts') as = 'font';
        
        preloadLinks.push(`<${url}>; rel=${rel}; as=${as}`);
      } catch (error) {
        logger.error('Preload header generation error:', { assetPath, error: error.message });
      }
    }
    
    return preloadLinks.join(', ');
  }

  /**
   * Get CDN service statistics
   * @returns {Promise<Object>} - CDN statistics
   */
  async getStats() {
    try {
      const cacheStats = await advancedCacheService.getStats();
      
      return {
        cdnBaseUrl: this.cdnBaseUrl,
        optimization: this.optimization,
        cache: cacheStats,
        status: 'operational'
      };
    } catch (error) {
      logger.error('CDN stats error:', error);
      return {
        status: 'error',
        error: error.message
      };
    }
  }

  /**
   * Health check for CDN service
   * @returns {Promise<Object>} - Health status
   */
  async healthCheck() {
    try {
      // Test asset URL generation
      const testUrl = this.getAssetUrl('test/asset.js');
      const isUrlValid = testUrl.includes(this.cdnBaseUrl);
      
      // Test cache connectivity
      const cacheHealth = await advancedCacheService.healthCheck();
      
      return {
        status: isUrlValid && cacheHealth.status === 'healthy' ? 'healthy' : 'degraded',
        urlGeneration: isUrlValid,
        cache: cacheHealth,
        timestamp: Date.now()
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        error: error.message,
        timestamp: Date.now()
      };
    }
  }
}

module.exports = new CDNService();
