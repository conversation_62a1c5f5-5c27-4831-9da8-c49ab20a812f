# Test Environment Configuration
NODE_ENV=test
PORT=5001

# Database
MONGODB_URI=mongodb://localhost:27017/phonepointdar_test

# JWT
JWT_SECRET=test_jwt_secret_key_for_testing_only
JWT_EXPIRE=7d

# Rate Limiting (More lenient for testing)
MAX_LOGIN_ATTEMPTS=100
LOCKOUT_DURATION=60
ATTEMPT_WINDOW=60

# Email (Mock for testing)
EMAIL_FROM=<EMAIL>
SMTP_HOST=localhost
SMTP_PORT=587
SMTP_USER=test
SMTP_PASS=test

# Frontend URL
FRONTEND_URL=http://localhost:5173

# Testing flags
DISABLE_RATE_LIMITING=true
DISABLE_LOGIN_TRACKING=true
