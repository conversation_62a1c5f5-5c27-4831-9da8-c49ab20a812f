/**
 * Phone Inventory Controller
 * Handles IMEI tracking and phone-specific inventory operations
 */

const phoneInventoryService = require('../services/phoneInventoryService');
const Inventory = require('../models/Inventory');
const AppError = require('../utils/AppError');
const { catchAsync } = require('../utils/errorHandlers');
const logger = require('../utils/logger');

/**
 * @desc    Get available phones for a product variant
 * @route   GET /api/inventory/phones/:productId/:variantSku
 * @access  Private
 */
exports.getAvailablePhones = catchAsync(async (req, res, next) => {
  const { productId, variantSku } = req.params;

  const availablePhones = await phoneInventoryService.getAvailablePhones(productId, variantSku);

  res.status(200).json({
    success: true,
    count: availablePhones.length,
    data: availablePhones
  });
});

/**
 * @desc    Get phone details by IMEI
 * @route   GET /api/inventory/phones/imei/:imei
 * @access  Private
 */
exports.getPhoneByIMEI = catchAsync(async (req, res, next) => {
  const { imei } = req.params;

  const phoneDetails = await phoneInventoryService.getPhoneByIMEI(imei);

  res.status(200).json({
    success: true,
    data: phoneDetails
  });
});

/**
 * @desc    Reserve a phone by IMEI
 * @route   POST /api/inventory/phones/reserve
 * @access  Private
 */
exports.reservePhone = catchAsync(async (req, res, next) => {
  const { productId, variantSku, imei } = req.body;
  const userId = req.user.id;

  const reservation = await phoneInventoryService.reservePhoneByIMEI(
    productId,
    variantSku,
    imei,
    userId
  );

  res.status(200).json({
    success: true,
    data: reservation,
    message: 'Phone reserved successfully'
  });
});

/**
 * @desc    Release phone reservation
 * @route   POST /api/inventory/phones/release
 * @access  Private
 */
exports.releaseReservation = catchAsync(async (req, res, next) => {
  const { imei } = req.body;

  const result = await phoneInventoryService.releasePhoneReservation(imei);

  res.status(200).json({
    success: true,
    data: result,
    message: 'Phone reservation released successfully'
  });
});

/**
 * @desc    Mark phone as sold
 * @route   POST /api/inventory/phones/sold
 * @access  Private (Admin)
 */
exports.markPhoneAsSold = catchAsync(async (req, res, next) => {
  const { imei, orderId } = req.body;

  if (req.user.role !== 'admin') {
    return next(new AppError('Not authorized to mark phones as sold', 403));
  }

  const result = await phoneInventoryService.markPhoneAsSold(imei, orderId);

  res.status(200).json({
    success: true,
    data: result,
    message: 'Phone marked as sold successfully'
  });
});

/**
 * @desc    Add new phone to inventory
 * @route   POST /api/inventory/phones
 * @access  Private (Admin)
 */
exports.addPhoneToInventory = catchAsync(async (req, res, next) => {
  if (req.user.role !== 'admin') {
    return next(new AppError('Not authorized to add phones to inventory', 403));
  }

  const phoneData = req.body;
  const result = await phoneInventoryService.addPhoneToInventory(phoneData);

  res.status(201).json({
    success: true,
    data: result,
    message: 'Phone added to inventory successfully'
  });
});

/**
 * @desc    Get inventory summary with IMEI tracking
 * @route   GET /api/inventory/summary
 * @access  Private (Admin)
 */
exports.getInventorySummary = catchAsync(async (req, res, next) => {
  if (req.user.role !== 'admin') {
    return next(new AppError('Not authorized to view inventory summary', 403));
  }

  const inventoryItems = await Inventory.find({})
    .populate('product', 'name brand model price')
    .sort({ 'product.name': 1 });

  const summary = inventoryItems.map(item => {
    const devicesByStatus = item.devices.reduce((acc, device) => {
      acc[device.status] = (acc[device.status] || 0) + 1;
      return acc;
    }, {});

    const devicesByCondition = item.devices.reduce((acc, device) => {
      acc[device.condition] = (acc[device.condition] || 0) + 1;
      return acc;
    }, {});

    return {
      product: item.product,
      variant_sku: item.variant_sku,
      location: item.location,
      total_devices: item.devices.length,
      quantity_available: item.quantity_available,
      quantity_reserved: item.quantity_reserved,
      devices_by_status: devicesByStatus,
      devices_by_condition: devicesByCondition,
      reorder_needed: item.quantity_available <= item.reorder_point,
      last_updated: item.updated_at
    };
  });

  res.status(200).json({
    success: true,
    count: summary.length,
    data: summary
  });
});

/**
 * @desc    Clean up expired reservations
 * @route   POST /api/inventory/cleanup-reservations
 * @access  Private (Admin)
 */
exports.cleanupExpiredReservations = catchAsync(async (req, res, next) => {
  if (req.user.role !== 'admin') {
    return next(new AppError('Not authorized to cleanup reservations', 403));
  }

  const cleanedCount = await phoneInventoryService.cleanupExpiredReservations();

  res.status(200).json({
    success: true,
    data: {
      cleaned_reservations: cleanedCount
    },
    message: `Cleaned up ${cleanedCount} expired reservations`
  });
});

/**
 * @desc    Get phones by status
 * @route   GET /api/inventory/phones/status/:status
 * @access  Private (Admin)
 */
exports.getPhonesByStatus = catchAsync(async (req, res, next) => {
  if (req.user.role !== 'admin') {
    return next(new AppError('Not authorized to view phone status', 403));
  }

  const { status } = req.params;
  const validStatuses = ['available', 'reserved', 'sold', 'damaged', 'returned'];
  
  if (!validStatuses.includes(status)) {
    return next(new AppError('Invalid status parameter', 400));
  }

  const inventoryItems = await Inventory.find({
    'devices.status': status
  }).populate('product', 'name brand model');

  const phones = [];
  inventoryItems.forEach(item => {
    const filteredDevices = item.devices.filter(device => device.status === status);
    filteredDevices.forEach(device => {
      phones.push({
        imei: device.imei,
        condition: device.condition,
        warranty_months: device.warranty_months,
        status: device.status,
        product: item.product,
        variant_sku: item.variant_sku,
        location: item.location,
        reserved_for: device.reserved_for,
        reserved_at: device.reserved_at,
        reservation_expires: device.reservation_expires,
        sold_at: device.sold_at,
        received_at: device.received_at
      });
    });
  });

  res.status(200).json({
    success: true,
    count: phones.length,
    data: phones
  });
});

/**
 * @desc    Update phone condition
 * @route   PATCH /api/inventory/phones/:imei/condition
 * @access  Private (Admin)
 */
exports.updatePhoneCondition = catchAsync(async (req, res, next) => {
  if (req.user.role !== 'admin') {
    return next(new AppError('Not authorized to update phone condition', 403));
  }

  const { imei } = req.params;
  const { condition, notes } = req.body;

  const validConditions = ['new', 'refurbished', 'used', 'open_box', 'damaged'];
  if (!validConditions.includes(condition)) {
    return next(new AppError('Invalid condition parameter', 400));
  }

  const inventoryItem = await Inventory.findOne({
    'devices.imei': imei
  });

  if (!inventoryItem) {
    return next(new AppError('Phone not found', 404));
  }

  const device = inventoryItem.devices.find(d => d.imei === imei);
  if (!device) {
    return next(new AppError('Device not found', 404));
  }

  device.condition = condition;
  if (notes) {
    device.notes = notes;
  }

  await inventoryItem.save();

  logger.info(`Phone condition updated: IMEI ${imei} to ${condition} by ${req.user.email}`);

  res.status(200).json({
    success: true,
    data: {
      imei: device.imei,
      condition: device.condition,
      notes: device.notes
    },
    message: 'Phone condition updated successfully'
  });
});
