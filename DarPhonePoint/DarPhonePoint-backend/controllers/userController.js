const { validationResult } = require('express-validator');
const bcrypt = require('bcrypt');
const User = require('../models/User');
const logger = require('../utils/logger');
const { catchAsync } = require('../utils/errorHandlers');
const AppError = require('../utils/AppError');

// @desc    Update user profile
// @route   PUT /api/users/profile
// @access  Private
exports.updateProfile = async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ success: false, errors: errors.array() });
  }

  try {
    const { name, email, currentPassword, newPassword } = req.body;

    // Get user from database
    let user = await User.findById(req.user.id).select('+password');

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Check if email is already in use by another user
    if (email !== user.email) {
      const existingUser = await User.findOne({ email });
      if (existingUser) {
        return res.status(400).json({
          success: false,
          message: 'Email is already in use'
        });
      }
    }

    // Update basic profile info
    user.name = name;
    user.email = email;

    // If user is trying to change password
    if (currentPassword && newPassword) {
      // Verify current password
      const isMatch = await user.matchPassword(currentPassword);

      if (!isMatch) {
        return res.status(400).json({
          success: false,
          message: 'Current password is incorrect'
        });
      }

      // Hash new password
      const salt = await bcrypt.genSalt(10);
      user.password = await bcrypt.hash(newPassword, salt);
    }

    // Save updated user
    await user.save();

    // Return user without password
    const updatedUser = await User.findById(req.user.id);

    logger.info(`User ${user._id} updated their profile`);

    res.json({
      success: true,
      data: updatedUser
    });
  } catch (err) {
    logger.error(`Error updating user profile: ${err.message}`);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Get all users
// @route   GET /api/users
// @access  Admin
exports.getAllUsers = catchAsync(async (req, res) => {
  const users = await User.find().select('-password');

  res.status(200).json({
    success: true,
    count: users.length,
    data: users
  });
});

// @desc    Get user by ID
// @route   GET /api/users/:id
// @access  Admin
exports.getUserById = catchAsync(async (req, res, next) => {
  const user = await User.findById(req.params.id).select('-password');

  if (!user) {
    return next(new AppError('User not found', 404));
  }

  res.status(200).json({
    success: true,
    data: user
  });
});

// @desc    Create new user
// @route   POST /api/users
// @access  Admin
exports.createUser = catchAsync(async (req, res) => {
  const { name, email, password, role } = req.body;

  // Check if user already exists
  const existingUser = await User.findOne({ email });

  if (existingUser) {
    return next(new AppError('User already exists', 400));
  }

  // Create new user
  const salt = await bcrypt.genSalt(10);
  const hashedPassword = await bcrypt.hash(password, salt);

  const user = await User.create({
    name,
    email,
    password: hashedPassword,
    role: role || 'user'
  });

  res.status(201).json({
    success: true,
    data: {
      _id: user._id,
      name: user.name,
      email: user.email,
      role: user.role
    }
  });
});

// @desc    Update user
// @route   PUT /api/users/:id
// @access  Admin
exports.updateUser = catchAsync(async (req, res, next) => {
  const { name, email, role, password } = req.body;

  // Find user
  let user = await User.findById(req.params.id);

  if (!user) {
    return next(new AppError('User not found', 404));
  }

  // Check if email is already in use by another user
  if (email && email !== user.email) {
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      return next(new AppError('Email is already in use', 400));
    }
  }

  // Update user fields
  const updateData = { name, email, role };

  // If password is provided, hash it
  if (password) {
    const salt = await bcrypt.genSalt(10);
    updateData.password = await bcrypt.hash(password, salt);
  }

  // Update user
  user = await User.findByIdAndUpdate(
    req.params.id,
    { $set: updateData },
    { new: true, runValidators: true }
  ).select('-password');

  res.status(200).json({
    success: true,
    data: user
  });
});

// @desc    Delete user
// @route   DELETE /api/users/:id
// @access  Admin
exports.deleteUser = catchAsync(async (req, res, next) => {
  const user = await User.findById(req.params.id);

  if (!user) {
    return next(new AppError('User not found', 404));
  }

  await user.remove();

  res.status(200).json({
    success: true,
    data: {}
  });
});

// @desc    Get user's saved addresses
// @route   GET /api/users/addresses
// @access  Private
exports.getUserAddresses = catchAsync(async (req, res, next) => {
  const user = await User.findById(req.user.id);

  if (!user) {
    return next(new AppError('User not found', 404));
  }

  res.status(200).json({
    success: true,
    data: user.shipping_addresses || []
  });
});

// @desc    Add new address for user
// @route   POST /api/users/addresses
// @access  Private
exports.addUserAddress = catchAsync(async (req, res, next) => {
  const user = await User.findById(req.user.id);

  if (!user) {
    return next(new AppError('User not found', 404));
  }

  const newAddress = user.addShippingAddress(req.body);
  await user.save();

  res.status(201).json({
    success: true,
    data: newAddress
  });
});

// @desc    Update user address
// @route   PUT /api/users/addresses/:addressId
// @access  Private
exports.updateUserAddress = catchAsync(async (req, res, next) => {
  const user = await User.findById(req.user.id);

  if (!user) {
    return next(new AppError('User not found', 404));
  }

  const updatedAddress = user.updateShippingAddress(req.params.addressId, req.body);

  if (!updatedAddress) {
    return next(new AppError('Address not found', 404));
  }

  await user.save();

  res.status(200).json({
    success: true,
    data: updatedAddress
  });
});

// @desc    Delete user address
// @route   DELETE /api/users/addresses/:addressId
// @access  Private
exports.deleteUserAddress = catchAsync(async (req, res, next) => {
  const user = await User.findById(req.user.id);

  if (!user) {
    return next(new AppError('User not found', 404));
  }

  const success = user.removeShippingAddress(req.params.addressId);

  if (!success) {
    return next(new AppError('Address not found', 404));
  }

  await user.save();

  res.status(200).json({
    success: true,
    data: {}
  });
});
