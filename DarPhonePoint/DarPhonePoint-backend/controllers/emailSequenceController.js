const EmailSequence = require('../models/EmailSequence');
const EmailSequenceTracking = require('../models/EmailSequenceTracking');
const User = require('../models/User');
const Lead = require('../models/Lead');
const AppError = require('../utils/AppError');
const config = require('../config/config');
const { sendSequenceEmail } = require('../utils/email');
const { catchAsync } = require('../utils/errorHandlers');

/**
 * Create a new email sequence
 * @route POST /api/email-sequences
 * @access Admin
 */
exports.createEmailSequence = async (req, res, next) => {
  try {
    const { name, description, sequence_type, templates } = req.body;

    // Validate required fields
    if (!name || !sequence_type || !templates || !templates.length) {
      return next(new AppError('Name, sequence type, and at least one template are required', 400));
    }

    // Create email sequence
    const emailSequence = new EmailSequence({
      name,
      description,
      sequence_type,
      templates
    });

    await emailSequence.save();

    res.status(201).json({
      success: true,
      data: emailSequence
    });
  } catch (error) {
    next(new AppError(`Error creating email sequence: ${error.message}`, 500));
  }
};

/**
 * Create a new email sequence (using new model format)
 * @route POST /api/email-sequences/v2
 * @access Private (Admin)
 */
exports.createSequence = catchAsync(async (req, res) => {
  // Add the current user as the creator
  req.body.created_by = req.user._id;

  const sequence = await EmailSequence.create(req.body);

  res.status(201).json({
    status: 'success',
    data: sequence
  });
});

/**
 * Get all email sequences
 * @route GET /api/email-sequences
 * @access Admin
 */
exports.getEmailSequences = async (req, res, next) => {
  try {
    const emailSequences = await EmailSequence.find();

    res.status(200).json({
      success: true,
      count: emailSequences.length,
      data: emailSequences
    });
  } catch (error) {
    next(new AppError(`Error getting email sequences: ${error.message}`, 500));
  }
};

/**
 * Get all email sequences (using new model format)
 * @route GET /api/email-sequences/v2
 * @access Private (Admin)
 */
exports.getAllSequences = catchAsync(async (req, res) => {
  const sequences = await EmailSequence.find()
    .sort({ created_at: -1 });

  res.status(200).json({
    status: 'success',
    results: sequences.length,
    data: sequences
  });
});

/**
 * Get email sequence by ID
 * @route GET /api/email-sequences/:id
 * @access Admin
 */
exports.getEmailSequence = async (req, res, next) => {
  try {
    const emailSequence = await EmailSequence.findById(req.params.id);

    if (!emailSequence) {
      return next(new AppError('Email sequence not found', 404));
    }

    res.status(200).json({
      success: true,
      data: emailSequence
    });
  } catch (error) {
    next(new AppError(`Error getting email sequence: ${error.message}`, 500));
  }
};

/**
 * Get a single email sequence (using new model format)
 * @route GET /api/email-sequences/v2/:id
 * @access Private (Admin)
 */
exports.getSequence = catchAsync(async (req, res, next) => {
  const sequence = await EmailSequence.findById(req.params.id);

  if (!sequence) {
    return next(new AppError('No email sequence found with that ID', 404));
  }

  res.status(200).json({
    status: 'success',
    data: sequence
  });
});

/**
 * Update email sequence
 * @route PUT /api/email-sequences/:id
 * @access Admin
 */
exports.updateEmailSequence = async (req, res, next) => {
  try {
    const { name, description, sequence_type, templates, is_active } = req.body;

    let emailSequence = await EmailSequence.findById(req.params.id);

    if (!emailSequence) {
      return next(new AppError('Email sequence not found', 404));
    }

    emailSequence = await EmailSequence.findByIdAndUpdate(
      req.params.id,
      {
        name,
        description,
        sequence_type,
        templates,
        is_active
      },
      { new: true, runValidators: true }
    );

    res.status(200).json({
      success: true,
      data: emailSequence
    });
  } catch (error) {
    next(new AppError(`Error updating email sequence: ${error.message}`, 500));
  }
};

/**
 * Update an email sequence (using new model format)
 * @route PATCH /api/email-sequences/v2/:id
 * @access Private (Admin)
 */
exports.updateSequence = catchAsync(async (req, res, next) => {
  const sequence = await EmailSequence.findById(req.params.id);

  if (!sequence) {
    return next(new AppError('No email sequence found with that ID', 404));
  }

  // Update basic sequence properties
  const { name, description, trigger, isActive } = req.body;

  if (name) sequence.name = name;
  if (description !== undefined) sequence.description = description;
  if (trigger) sequence.trigger = trigger;
  if (isActive !== undefined) sequence.isActive = isActive;

  // Handle emails array if provided
  if (req.body.emails) {
    sequence.emails = req.body.emails;
  }

  await sequence.save();

  res.status(200).json({
    status: 'success',
    data: sequence
  });
});

/**
 * Trigger email sequence for a user or lead
 * @route POST /api/email-sequences/trigger
 * @access Private/Admin
 */
exports.triggerSequence = async (req, res, next) => {
  try {
    const { email, user_id, lead_id, sequence_type, metadata } = req.body;

    // Validate required fields
    if (!email || !sequence_type) {
      return next(new AppError('Email and sequence type are required', 400));
    }

    // Find the sequence
    const sequence = await EmailSequence.findOne({
      trigger: sequence_type,
      isActive: true
    });

    if (!sequence) {
      return next(new AppError(`No active sequence found for type: ${sequence_type}`, 404));
    }

    // Check if user/lead is already in this sequence
    const existingTracking = await EmailSequenceTracking.findOne({
      email,
      email_sequence: sequence._id,
      is_completed: false,
      unsubscribed: false
    });

    if (existingTracking) {
      return res.status(200).json({
        success: true,
        message: 'User is already in this sequence',
        data: existingTracking
      });
    }

    // Calculate next email date (now + delay of first email)
    const firstTemplate = sequence.emails.find(t => t.order === 1);
    const nextEmailDate = new Date();
    nextEmailDate.setDate(nextEmailDate.getDate() + (firstTemplate?.delayDays || 1));

    // Create sequence tracking
    const sequenceTracking = new EmailSequenceTracking({
      user: user_id,
      lead: lead_id,
      email,
      email_sequence: sequence._id,
      current_position: 0,
      next_email_date: nextEmailDate,
      metadata: metadata || {}
    });

    await sequenceTracking.save();

    res.status(201).json({
      success: true,
      message: 'Email sequence triggered successfully',
      data: sequenceTracking
    });
  } catch (error) {
    next(new AppError(`Error triggering email sequence: ${error.message}`, 500));
  }
};

/**
 * Process due emails in sequences
 * This would typically be called by a cron job
 * @route POST /api/email-sequences/process
 * @access Admin
 */
exports.processSequences = async (req, res, next) => {
  try {
    const now = new Date();

    // Find all sequence trackings that need processing
    const dueSequences = await EmailSequenceTracking.find({
      next_email_date: { $lte: now },
      is_completed: false,
      is_paused: false,
      unsubscribed: false
    }).populate('email_sequence');

    console.log(`Found ${dueSequences.length} sequences to process`);

    let processed = 0;
    let errors = 0;

    // Process each sequence
    for (const tracking of dueSequences) {
      try {
        const sequence = tracking.email_sequence;
        const nextPosition = tracking.current_position + 1;

        // Find the template for the next position
        const template = sequence.emails.find(t => t.order === nextPosition);

        if (!template) {
          // No more templates, mark sequence as completed
          tracking.is_completed = true;
          await tracking.save();
          continue;
        }

        // Process template variables using metadata
        let processedSubject = template.subject;
        let processedBody = template.body;

        // Replace variables in subject and body
        if (tracking.metadata) {
          // Get user or lead name if available
          let recipientName = '';
          if (tracking.user) {
            try {
              const user = await User.findById(tracking.user);
              if (user) {
                recipientName = user.name;
              }
            } catch (err) {
              console.error('Error getting user name:', err);
            }
          } else if (tracking.lead) {
            try {
              const lead = await Lead.findById(tracking.lead);
              if (lead) {
                recipientName = lead.name;
              }
            } catch (err) {
              console.error('Error getting lead name:', err);
            }
          }

          // Replace common variables
          const replacements = {
            '{{firstName}}': recipientName.split(' ')[0] || 'there',
            '{{fullName}}': recipientName || 'there',
            '{{email}}': tracking.email,
            '{{siteUrl}}': config.BASE_URL || 'http://localhost:5173',
            '{{productName}}': tracking.metadata.product_name || '',
            '{{orderId}}': tracking.metadata.order_id || '',
            '{{orderDate}}': new Date().toLocaleDateString()
          };

          // Apply replacements
          Object.keys(replacements).forEach(key => {
            processedSubject = processedSubject.replace(new RegExp(key, 'g'), replacements[key]);
            processedBody = processedBody.replace(new RegExp(key, 'g'), replacements[key]);
          });
        }

        // Send the email
        const emailSent = await sendSequenceEmail(
          tracking.email,
          processedSubject,
          processedBody,
          tracking._id,
          template._id,
          sequence._id
        );

        if (emailSent) {
          // Update tracking
          const emailDelivery = {
            email_template_id: template._id,
            position_in_sequence: template.order,
            sent_at: new Date(),
            status: 'sent',
            subject: processedSubject,
            is_ab_test: template.isABTest || false
          };

          tracking.emails_delivered.push(emailDelivery);
          tracking.current_position = nextPosition;

          // Calculate next email date
          const nextTemplate = sequence.emails.find(t => t.order === nextPosition + 1);

          if (nextTemplate) {
            const nextDate = new Date();
            nextDate.setDate(nextDate.getDate() + nextTemplate.delayDays);
            tracking.next_email_date = nextDate;
          } else {
            tracking.is_completed = true;
          }

          await tracking.save();
          processed++;
        } else {
          errors++;
        }
      } catch (error) {
        console.error(`Error processing sequence ${tracking._id}: ${error.message}`);
        errors++;
      }
    }

    res.status(200).json({
      success: true,
      message: `Processed ${processed} sequences with ${errors} errors`,
      data: {
        total: dueSequences.length,
        processed,
        errors
      }
    });
  } catch (error) {
    next(new AppError(`Error processing email sequences: ${error.message}`, 500));
  }
};

/**
 * Delete an email sequence (using new model format)
 * @route DELETE /api/email-sequences/v2/:id
 * @access Private (Admin)
 */
exports.deleteSequence = catchAsync(async (req, res, next) => {
  const sequence = await EmailSequence.findByIdAndDelete(req.params.id);

  if (!sequence) {
    return next(new AppError('No email sequence found with that ID', 404));
  }

  res.status(204).json({
    status: 'success',
    data: null
  });
});

/**
 * Add an email to a sequence
 * @route POST /api/email-sequences/v2/:id/emails
 * @access Private (Admin)
 */
exports.addEmail = catchAsync(async (req, res, next) => {
  const sequence = await EmailSequence.findById(req.params.id);

  if (!sequence) {
    return next(new AppError('No email sequence found with that ID', 404));
  }

  sequence.addEmail(req.body);
  await sequence.save();

  res.status(200).json({
    status: 'success',
    data: sequence
  });
});

/**
 * Update an email in a sequence
 * @route PATCH /api/email-sequences/v2/:id/emails/:emailId
 * @access Private (Admin)
 */
exports.updateEmail = catchAsync(async (req, res, next) => {
  const sequence = await EmailSequence.findById(req.params.id);

  if (!sequence) {
    return next(new AppError('No email sequence found with that ID', 404));
  }

  const email = sequence.emails.id(req.params.emailId);

  if (!email) {
    return next(new AppError('No email found with that ID in the sequence', 404));
  }

  sequence.updateEmail(req.params.emailId, req.body);
  await sequence.save();

  res.status(200).json({
    status: 'success',
    data: sequence
  });
});

/**
 * Remove an email from a sequence
 * @route DELETE /api/email-sequences/v2/:id/emails/:emailId
 * @access Private (Admin)
 */
exports.removeEmail = catchAsync(async (req, res, next) => {
  const sequence = await EmailSequence.findById(req.params.id);

  if (!sequence) {
    return next(new AppError('No email sequence found with that ID', 404));
  }

  const email = sequence.emails.id(req.params.emailId);

  if (!email) {
    return next(new AppError('No email found with that ID in the sequence', 404));
  }

  sequence.removeEmail(req.params.emailId);
  await sequence.save();

  res.status(200).json({
    status: 'success',
    data: sequence
  });
});

/**
 * Reorder an email in a sequence
 * @route PATCH /api/email-sequences/v2/:id/emails/:emailId/reorder
 * @access Private (Admin)
 */
exports.reorderEmail = catchAsync(async (req, res, next) => {
  const { newOrder } = req.body;

  if (!newOrder || typeof newOrder !== 'number') {
    return next(new AppError('New order is required and must be a number', 400));
  }

  const sequence = await EmailSequence.findById(req.params.id);

  if (!sequence) {
    return next(new AppError('No email sequence found with that ID', 404));
  }

  const email = sequence.emails.id(req.params.emailId);

  if (!email) {
    return next(new AppError('No email found with that ID in the sequence', 404));
  }

  if (newOrder < 1 || newOrder > sequence.emails.length) {
    return next(new AppError(`New order must be between 1 and ${sequence.emails.length}`, 400));
  }

  sequence.reorderEmail(req.params.emailId, newOrder);
  await sequence.save();

  res.status(200).json({
    status: 'success',
    data: sequence
  });
});
