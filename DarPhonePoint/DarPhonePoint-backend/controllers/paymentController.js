// Phone Point Dar - Local payment processing for Tanzania
const Order = require('../models/Order');
const User = require('../models/User');
const Product = require('../models/Product');
const AppError = require('../utils/AppError');
const logger = require('../utils/logger');
const { catchAsync } = require('../utils/errorHandlers');
const auditLogService = require('../services/auditLogService');
const tanzaniaPaymentService = require('../services/tanzaniaPaymentService');
const paymentMonitoringService = require('../services/paymentMonitoringService');
const { sendOrderConfirmationEmail } = require('../utils/email');

/**
 * Create a payment for an order
 * @route POST /api/payments
 * @access Private
 */
exports.createPayment = catchAsync(async (req, res, next) => {
  const { orderId, paymentMethod, phone } = req.body;

  // Validate order
  const order = await Order.findById(orderId).populate('items.product');
  if (!order) {
    return next(new AppError('Order not found', 404));
  }

  // Check if order already has a payment
  if (order.payment_status === 'completed') {
    return next(new AppError('Order has already been paid', 400));
  }

  try {
    // Initialize payment with Tanzania payment service
    const paymentResult = await tanzaniaPaymentService.initializePayment({
      method: paymentMethod,
      amount: order.total,
      currency: 'TZS',
      phone: phone,
      orderId: order._id,
      customerName: order.customer_name,
      customerEmail: order.customer_email
    });

    // Update order with payment information
    order.transaction_id = paymentResult.transaction_id;
    order.payment_method = paymentMethod;
    order.payment_status = paymentResult.status;
    await order.save();

    // Log payment creation
    await auditLogService.logPaymentCreated(
      req.user,
      order._id,
      order.total,
      paymentResult.transaction_id
    );

    // Record payment attempt for monitoring
    await paymentMonitoringService.recordPaymentAttempt({
      orderId: order._id,
      transactionId: paymentResult.transaction_id,
      method: paymentMethod,
      amount: order.total,
      status: 'pending',
      previousStatus: order.payment_status
    });

    res.status(200).json({
      success: true,
      data: {
        payment_id: paymentResult.transaction_id,
        order_id: order._id,
        payment_method: paymentMethod,
        instructions: paymentResult.payment_instructions,
        amount: paymentResult.amount,
        currency: paymentResult.currency,
        status: paymentResult.status,
        expires_at: paymentResult.payment_instructions?.expires_at,
        provider_reference: paymentResult.provider_reference
      }
    });
  } catch (error) {
    logger.error('Payment creation error:', error);
    return next(new AppError('Failed to create payment', 500));
  }
});

/**
 * Handle payment webhook from Tanzania payment providers
 * @route POST /api/payments/webhook
 * @access Public
 */
exports.handleWebhook = catchAsync(async (req, res, next) => {
  try {
    const signature = req.headers['x-payment-signature'];
    const webhookData = await tanzaniaPaymentService.processWebhook(req.body, signature);

    // Find order by payment ID
    const order = await Order.findOne({ transaction_id: webhookData.paymentId });
    if (!order) {
      logger.error(`Order not found for payment: ${webhookData.paymentId}`);
      return res.status(200).json({ received: true });
    }

    // Update order status
    const previousStatus = order.payment_status;
    order.payment_status = webhookData.status === 'completed' ? 'completed' : 'failed';

    if (webhookData.status === 'completed') {
      order.order_status = 'confirmed';
      order.confirmed_at = new Date();
    }

    await order.save();

    // Record payment attempt for monitoring
    await paymentMonitoringService.recordPaymentAttempt({
      orderId: order._id,
      transactionId: webhookData.paymentId,
      method: order.payment_method || 'unknown',
      amount: order.total,
      status: webhookData.status,
      previousStatus
    });

    // If payment successful, send confirmation email
    if (webhookData.status === 'completed' && previousStatus !== 'completed') {
      try {
        await sendOrderConfirmationEmail(
          order.customer_email,
          order.customer_name,
          order
        );
      } catch (emailError) {
        logger.error('Failed to send order confirmation email:', emailError);
      }

      // Log successful payment
      await auditLogService.logPaymentProcessed(
        order.user,
        order._id,
        order.total,
        'completed',
        webhookData.paymentId
      );
    }

    res.status(200).json({ received: true });
  } catch (error) {
    logger.error('Webhook processing error:', error);
    return res.status(200).json({ received: true }); // Always return 200 to acknowledge receipt
  }
});

/**
 * Verify payment status
 * @route GET /api/payments/:paymentId/verify
 * @access Private
 */
exports.verifyPayment = catchAsync(async (req, res, next) => {
  const { paymentId } = req.params;

  try {
    const paymentStatus = await tanzaniaPaymentService.verifyPayment(paymentId);

    // Find order by payment ID
    const order = await Order.findOne({ transaction_id: paymentId });
    if (!order) {
      return next(new AppError('Order not found', 404));
    }

    // Update order status if needed
    if (order.payment_status !== paymentStatus.status) {
      order.payment_status = paymentStatus.status === 'completed' ? 'completed' : 'failed';

      if (paymentStatus.status === 'completed') {
        order.order_status = 'confirmed';
        order.confirmed_at = new Date();
      }

      await order.save();
    }

    res.status(200).json({
      success: true,
      data: {
        status: paymentStatus.status,
        order_id: order._id,
        verified_at: paymentStatus.verified_at
      }
    });
  } catch (error) {
    logger.error('Payment verification error:', error);
    return next(new AppError('Failed to verify payment', 500));
  }
});

/**
 * Get payment monitoring metrics
 * @route GET /api/payments/monitoring/metrics
 * @access Private (Admin only)
 */
exports.getPaymentMetrics = catchAsync(async (req, res, next) => {
  // Check if user is admin
  if (req.user.role !== 'admin') {
    return next(new AppError('Access denied. Admin role required.', 403));
  }

  const metrics = paymentMonitoringService.getMetrics();
  const healthStatus = paymentMonitoringService.getHealthStatus();

  res.status(200).json({
    success: true,
    data: {
      metrics,
      health: healthStatus,
      timestamp: new Date().toISOString()
    }
  });
});

/**
 * Get payment health status
 * @route GET /api/payments/monitoring/health
 * @access Private (Admin only)
 */
exports.getPaymentHealth = catchAsync(async (req, res, next) => {
  // Check if user is admin
  if (req.user.role !== 'admin') {
    return next(new AppError('Access denied. Admin role required.', 403));
  }

  const healthStatus = paymentMonitoringService.getHealthStatus();

  res.status(200).json({
    success: true,
    data: healthStatus
  });
});

/**
 * Update user account type based on product purchase
 */
exports.updateUserAccountType = async (user, product) => {
  try {
    // Define account type mapping based on product type and price
    let newAccountType = user.user_type; // Keep current type as default

    if (product.product_type === 'premium' || product.price >= 40) {
      newAccountType = 'premium';
    } else if (product.product_type === 'basic' || product.price >= 20) {
      // Only upgrade to basic if user is currently free
      if (user.user_type === 'free') {
        newAccountType = 'basic';
      }
    }

    // Update user account type if it's an upgrade
    const typeHierarchy = { 'free': 0, 'basic': 1, 'premium': 2 };
    if (typeHierarchy[newAccountType] > typeHierarchy[user.user_type]) {
      user.user_type = newAccountType;
      logger.info(`User ${user._id} account type upgraded to ${newAccountType} after purchasing ${product.name}`);
    }

    return newAccountType;
  } catch (error) {
    logger.error('Error updating user account type:', error);
    // Don't throw error - account type update shouldn't fail the payment
  }
};

/**
 * Cancel payment
 * @route POST /api/payments/:paymentId/cancel
 * @access Private
 */
exports.cancelPayment = catchAsync(async (req, res, next) => {
  const { paymentId } = req.params;

  try {
    await paymentService.cancelPayment(paymentId);

    // Find and update order
    const order = await Order.findOne({ transaction_id: paymentId });
    if (order) {
      order.payment_status = 'cancelled';
      await order.save();

      // Log payment cancellation
      await auditLogService.logPaymentCancelled(req.user, order._id, order.amount, paymentId);
    }

    res.status(200).json({
      success: true,
      message: 'Payment cancelled successfully'
    });
  } catch (error) {
    logger.error('Payment cancellation error:', error);
    return next(new AppError('Failed to cancel payment', 500));
  }
});