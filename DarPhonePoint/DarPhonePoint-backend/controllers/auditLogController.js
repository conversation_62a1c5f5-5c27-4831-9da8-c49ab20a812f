const mongoose = require('mongoose');
const AuditLog = require('../models/AuditLog');
const logger = require('../utils/logger');
const ApiResponse = require('../utils/ApiResponse');
const { sanitizeState } = require('../services/auditLogService');

/**
 * @desc    Get all audit logs with pagination and filtering
 * @route   GET /api/audit-logs
 * @access  Private/Admin
 */
const getAuditLogs = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 50;
    const skip = (page - 1) * limit;

    // Build filter object
    const filter = {};

    // User filter
    if (req.query.user) {
      filter.user = req.query.user;
    }

    // Action filter
    if (req.query.action) {
      if (Array.isArray(req.query.action)) {
        filter.action = { $in: req.query.action };
      } else {
        filter.action = req.query.action;
      }
    }

    // Resource type filter
    if (req.query.resource_type) {
      if (Array.isArray(req.query.resource_type)) {
        filter.resource_type = { $in: req.query.resource_type };
      } else {
        filter.resource_type = req.query.resource_type;
      }
    }

    // Resource ID filter
    if (req.query.resource_id) {
      filter.resource_id = req.query.resource_id;
    }

    // Description search
    if (req.query.search) {
      filter.description = { $regex: req.query.search, $options: 'i' };
    }

    // Date range filter
    if (req.query.start_date && req.query.end_date) {
      filter.created_at = {
        $gte: new Date(req.query.start_date),
        $lte: new Date(req.query.end_date + 'T23:59:59.999Z')
      };
    } else if (req.query.start_date) {
      filter.created_at = { $gte: new Date(req.query.start_date) };
    } else if (req.query.end_date) {
      filter.created_at = { $lte: new Date(req.query.end_date + 'T23:59:59.999Z') };
    }

    // IP address filter
    if (req.query.ip_address) {
      filter.ip_address = req.query.ip_address;
    }

    // Metadata field search
    if (req.query.metadata_field && req.query.metadata_value) {
      filter[`metadata.${req.query.metadata_field}`] = req.query.metadata_value;
    }

    // Get total count
    const total = await AuditLog.countDocuments(filter);

    // Get logs with pagination
    const logs = await AuditLog.find(filter)
      .populate('user', 'name email role')
      .sort({ created_at: -1 })
      .skip(skip)
      .limit(limit);

    // Calculate total pages
    const pages = Math.ceil(total / limit);

    return res.apiPaginated(logs, page, limit, total, 'Audit logs retrieved successfully');
  } catch (error) {
    logger.error('Error getting audit logs', { error: error.message });
    return res.apiError('Error getting audit logs', 500, [error.message]);
  }
};

/**
 * @desc    Get recent audit logs
 * @route   GET /api/audit-logs/recent
 * @access  Private/Admin
 */
const getRecentLogs = async (req, res) => {
  try {
    const limit = parseInt(req.query.limit) || 100;
    const actionTypes = req.query.actions ? req.query.actions.split(',') : null;
    const resourceTypes = req.query.resource_types ? req.query.resource_types.split(',') : null;

    // Build filter
    const filter = {};

    if (actionTypes) {
      filter.action = { $in: actionTypes };
    }

    if (resourceTypes) {
      filter.resource_type = { $in: resourceTypes };
    }

    const logs = await AuditLog.find(filter)
      .populate('user', 'name email role')
      .sort({ created_at: -1 })
      .limit(limit);

    res.json({
      success: true,
      data: logs
    });
  } catch (error) {
    logger.error('Error getting recent audit logs', { error: error.message });
    res.status(500).json({
      success: false,
      message: 'Error getting recent audit logs',
      error: error.message
    });
  }
};

/**
 * @desc    Get audit logs for a specific resource
 * @route   GET /api/audit-logs/resource/:type/:id
 * @access  Private/Admin
 */
const getResourceLogs = async (req, res) => {
  try {
    const { type, id } = req.params;
    const limit = parseInt(req.query.limit) || 100;
    const page = parseInt(req.query.page) || 1;
    const skip = (page - 1) * limit;
    const actionTypes = req.query.actions ? req.query.actions.split(',') : null;

    // Build filter
    const filter = {
      resource_type: type,
      resource_id: id
    };

    if (actionTypes) {
      filter.action = { $in: actionTypes };
    }

    // Get total count
    const total = await AuditLog.countDocuments(filter);

    // Get logs with pagination
    const logs = await AuditLog.find(filter)
      .populate('user', 'name email role')
      .sort({ created_at: -1 })
      .skip(skip)
      .limit(limit);

    // Calculate total pages
    const pages = Math.ceil(total / limit);

    return res.apiPaginated(logs, page, limit, total, 'Resource audit logs retrieved successfully');
  } catch (error) {
    logger.error('Error getting resource audit logs', { error: error.message });
    return res.apiError('Error getting resource audit logs', 500, [error.message]);
  }
};

/**
 * @desc    Get audit logs for a specific user
 * @route   GET /api/audit-logs/user/:id
 * @access  Private/Admin
 */
const getUserLogs = async (req, res) => {
  try {
    const { id } = req.params;
    const limit = parseInt(req.query.limit) || 100;
    const page = parseInt(req.query.page) || 1;
    const skip = (page - 1) * limit;
    const actionTypes = req.query.actions ? req.query.actions.split(',') : null;
    const resourceTypes = req.query.resource_types ? req.query.resource_types.split(',') : null;

    // Build filter
    const filter = { user: id };

    if (actionTypes) {
      filter.action = { $in: actionTypes };
    }

    if (resourceTypes) {
      filter.resource_type = { $in: resourceTypes };
    }

    // Get total count
    const total = await AuditLog.countDocuments(filter);

    // Get logs with pagination
    const logs = await AuditLog.find(filter)
      .populate('user', 'name email role')
      .sort({ created_at: -1 })
      .skip(skip)
      .limit(limit);

    // Calculate total pages
    const pages = Math.ceil(total / limit);

    res.json({
      success: true,
      data: logs,
      page,
      pages,
      total,
      limit
    });
  } catch (error) {
    logger.error('Error getting user audit logs', { error: error.message });
    res.status(500).json({
      success: false,
      message: 'Error getting user audit logs',
      error: error.message
    });
  }
};

/**
 * @desc    Create an audit log
 * @route   POST /api/audit-logs
 * @access  Private
 */
const createAuditLog = async (req, res) => {
  try {
    const {
      user,
      action,
      resource_type,
      resource_id,
      description,
      previous_state,
      new_state,
      metadata
    } = req.body;

    // Validate required fields
    if (!action || !resource_type || !description) {
      return res.status(400).json({
        success: false,
        message: 'Action, resource type, and description are required'
      });
    }

    // Create log
    const log = await AuditLog.create({
      user,
      action,
      resource_type,
      resource_id,
      description,
      previous_state: previous_state ? sanitizeState(previous_state) : null,
      new_state: new_state ? sanitizeState(new_state) : null,
      metadata: metadata || {},
      ip_address: req.ip,
      user_agent: req.headers['user-agent']
    });

    res.status(201).json({
      success: true,
      data: log
    });
  } catch (error) {
    logger.error('Error creating audit log', { error: error.message });
    res.status(500).json({
      success: false,
      message: 'Error creating audit log',
      error: error.message
    });
  }
};

/**
 * @desc    Get audit log statistics
 * @route   GET /api/audit-logs/stats
 * @access  Private/Admin
 */
const getAuditLogStats = async (req, res) => {
  try {
    const startDate = req.query.start_date ? new Date(req.query.start_date) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    const endDate = req.query.end_date ? new Date(req.query.end_date + 'T23:59:59.999Z') : new Date();

    // Get total count
    const totalCount = await AuditLog.countDocuments({
      created_at: { $gte: startDate, $lte: endDate }
    });

    // Get counts by action
    const actionCounts = await AuditLog.aggregate([
      {
        $match: {
          created_at: { $gte: startDate, $lte: endDate }
        }
      },
      {
        $group: {
          _id: '$action',
          count: { $sum: 1 }
        }
      },
      {
        $sort: { count: -1 }
      }
    ]);

    // Get counts by resource type
    const resourceTypeCounts = await AuditLog.aggregate([
      {
        $match: {
          created_at: { $gte: startDate, $lte: endDate }
        }
      },
      {
        $group: {
          _id: '$resource_type',
          count: { $sum: 1 }
        }
      },
      {
        $sort: { count: -1 }
      }
    ]);

    // Get counts by user (top 10)
    const userCounts = await AuditLog.aggregate([
      {
        $match: {
          created_at: { $gte: startDate, $lte: endDate },
          user: { $ne: null }
        }
      },
      {
        $group: {
          _id: '$user',
          count: { $sum: 1 }
        }
      },
      {
        $sort: { count: -1 }
      },
      {
        $limit: 10
      }
    ]);

    // Get user details for user counts
    const userIds = userCounts.map(item => item._id);
    const users = await mongoose.model('User').find(
      { _id: { $in: userIds } },
      { name: 1, email: 1 }
    );

    // Map user details to counts
    const userCountsWithDetails = userCounts.map(item => {
      const user = users.find(u => u._id.toString() === item._id.toString());
      return {
        _id: item._id,
        count: item.count,
        name: user ? user.name : 'Unknown',
        email: user ? user.email : 'Unknown'
      };
    });

    // Get daily activity
    const dailyActivity = await AuditLog.aggregate([
      {
        $match: {
          created_at: { $gte: startDate, $lte: endDate }
        }
      },
      {
        $group: {
          _id: {
            year: { $year: '$created_at' },
            month: { $month: '$created_at' },
            day: { $dayOfMonth: '$created_at' }
          },
          count: { $sum: 1 }
        }
      },
      {
        $sort: { '_id.year': 1, '_id.month': 1, '_id.day': 1 }
      }
    ]);

    // Format daily activity for chart
    const formattedDailyActivity = dailyActivity.map(item => {
      const date = new Date(item._id.year, item._id.month - 1, item._id.day);
      return {
        date: date.toISOString().split('T')[0],
        count: item.count
      };
    });

    const statsData = {
      totalCount,
      actionCounts,
      resourceTypeCounts,
      userCounts: userCountsWithDetails,
      dailyActivity: formattedDailyActivity,
      dateRange: {
        startDate: startDate.toISOString().split('T')[0],
        endDate: endDate.toISOString().split('T')[0]
      }
    };

    return res.apiSuccess(statsData, 'Audit log statistics retrieved successfully');
  } catch (error) {
    logger.error('Error getting audit log stats', { error: error.message });
    return res.apiError('Error getting audit log stats', 500, [error.message]);
  }
};

module.exports = {
  getAuditLogs,
  getRecentLogs,
  getResourceLogs,
  getUserLogs,
  createAuditLog,
  getAuditLogStats
};
