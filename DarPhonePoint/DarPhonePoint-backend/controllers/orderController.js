const Order = require('../models/Order');
const User = require('../models/User');
const Product = require('../models/Product');
const Cart = require('../models/Cart');
const Inventory = require('../models/Inventory');
const Shipping = require('../models/Shipping');
const Analytics = require('../models/Analytics');
const paymentService = require('../services/paymentService');
const paymentCacheService = require('../services/paymentCacheService');
const auditLogService = require('../services/auditLogService');
const ErrorResponse = require('../utils/errorResponse');
const asyncHandler = require('../middleware/async');
const { catchAsync } = require('../utils/errorHandlers');
const AppError = require('../utils/AppError');

// @desc    Create new order from cart
// @route   POST /api/orders
// @access  Private
exports.createOrder = asyncHandler(async (req, res, next) => {
  const {
    customer_email,
    customer_name,
    customer_phone,
    shipping_address,
    billing_address,
    payment_method = 'stripe',
    payment_details,
    shipping_method = 'standard',
    delivery_instructions,
    use_cart = true,
    items, // For direct checkout without cart
    is_guest = false,
    guest_email,
    guest_name,
    cart_items // For guest checkout with cart items
  } = req.body;

  let orderItems = [];
  let cart = null;
  let userId = null;

  // Determine if this is a guest order
  const isGuestOrder = is_guest || !req.user;

  if (use_cart && !isGuestOrder) {
    // Get items from authenticated user's cart
    cart = await Cart.findOne({ user: req.user.id }).populate('items.product');

    if (!cart || cart.items.length === 0) {
      return next(new ErrorResponse('Cart is empty', 400));
    }

    orderItems = cart.items;
    userId = req.user.id;
  } else if (use_cart && isGuestOrder && cart_items) {
    // Handle guest cart items
    for (const item of cart_items) {
      const product = await Product.findById(item.productId);
      if (!product) {
        return next(new ErrorResponse(`Product ${item.productId} not found`, 404));
      }

      orderItems.push({
        product: product,
        variant_sku: item.variantSku,
        quantity: item.quantity,
        price: item.price || product.price,
        name: product.name,
        imei: item.imei,
        condition: item.condition,
        warranty_months: item.warrantyMonths
      });
    }
  } else {
    // Use provided items for direct checkout
    if (!items || items.length === 0) {
      return next(new ErrorResponse('No items provided', 400));
    }

    // Validate and populate items
    for (const item of items) {
      const product = await Product.findById(item.productId);
      if (!product) {
        return next(new ErrorResponse(`Product ${item.productId} not found`, 404));
      }

      orderItems.push({
        product: product,
        variant_sku: item.variantSku,
        quantity: item.quantity,
        price: item.price || product.price,
        name: product.name,
        sku: item.variantSku || product.sku
      });
    }
  }

  // Validate inventory and reserve stock
  const inventoryReservations = [];
  let totalAmount = 0;
  let totalWeight = 0;

  for (const item of orderItems) {
    const product = item.product;

    // Check if product is active
    if (!product.is_active) {
      return next(new ErrorResponse(`Product ${product.name} is no longer available`, 400));
    }

    // Calculate item total
    const itemTotal = item.price * item.quantity;
    totalAmount += itemTotal;
    totalWeight += (product.weight || 200) * item.quantity; // Default 200g if not specified

    // Check and reserve inventory if tracking is enabled
    if (product.track_inventory) {
      const inventory = await Inventory.findOne({
        product: product._id,
        variant_sku: item.variant_sku || null,
        location: 'main_warehouse'
      });

      if (!inventory || inventory.quantity_available < item.quantity) {
        return next(new ErrorResponse(
          `Insufficient stock for ${product.name}. Available: ${inventory?.quantity_available || 0}, Requested: ${item.quantity}`,
          400
        ));
      }

      // Reserve inventory
      if (inventory.reserveQuantity(item.quantity)) {
        await inventory.save();
        inventoryReservations.push({
          inventory_id: inventory._id,
          quantity: item.quantity
        });
      } else {
        // Release any previously reserved inventory
        for (const reservation of inventoryReservations) {
          const prevInventory = await Inventory.findById(reservation.inventory_id);
          if (prevInventory) {
            prevInventory.releaseReservedQuantity(reservation.quantity);
            await prevInventory.save();
          }
        }
        return next(new ErrorResponse(`Failed to reserve inventory for ${product.name}`, 400));
      }
    }
  }

  // Calculate shipping cost
  const shippingCost = calculateShippingCost(orderItems, shipping_address, shipping_method);

  // Calculate tax (18% VAT in Tanzania)
  const taxRate = 0.18;
  const taxAmount = Math.round(totalAmount * taxRate);

  // Calculate final total
  const finalTotal = totalAmount + shippingCost + taxAmount;

  // Generate order number
  const orderNumber = `PPD-${Date.now()}-${Math.random().toString(36).substr(2, 4).toUpperCase()}`;

  // Create order
  const orderData = {
    order_number: orderNumber,
    user: isGuestOrder ? null : req.user.id,
    customer_name: customer_name || (isGuestOrder ? guest_name : req.user.name),
    customer_email: customer_email || (isGuestOrder ? guest_email : req.user.email),
    customer_phone: customer_phone || (isGuestOrder ? shipping_address.phone : req.user.phone),
    is_guest: isGuestOrder,
    items: orderItems.map(item => ({
      product: item.product._id,
      variant_sku: item.variant_sku,
      quantity: item.quantity,
      unit_price: item.price,
      total_price: item.price * item.quantity,
      name: item.name,
      sku: item.variant_sku || item.product.sku || `${item.product._id}`,
      imei: item.imei,
      condition: item.condition,
      warranty_months: item.warranty_months
    })),
    subtotal: totalAmount,
    shipping_cost: shippingCost,
    tax_amount: taxAmount,
    total_amount: finalTotal,
    shipping_address,
    billing_address: billing_address || shipping_address,
    payment_method,
    shipping_method,
    delivery_instructions,
    order_status: 'pending',
    payment_status: 'pending',
    fulfillment_status: 'unfulfilled'
  };

  const order = await Order.create(orderData);
  console.log('Order created with ID:', order._id);

  try {
    // Create payment intent
    const payment = await paymentService.createPayment({
      amount: finalTotal,
      currency: 'TZS',
      description: `Phone Point Dar Order ${orderNumber}`,
      payment_method: payment_method,
      customer_phone: shipping_address.phone,
      customer_email: isGuestOrder ? guest_email : req.user.email,
      customer_name: isGuestOrder ? guest_name : req.user.name,
      order_id: order._id.toString(),
      order_number: orderNumber,
      user_id: isGuestOrder ? null : req.user.id
    });

    // Update order with payment ID
    order.transaction_id = payment.data.id;
    await order.save();

    // Clear cart if used
    if (use_cart && cart) {
      cart.items = [];
      await cart.save();
    }

    // Track analytics
    try {
      await Analytics.create({
        event_type: 'checkout_complete',
        user_id: isGuestOrder ? null : req.user._id,
        session_id: req.sessionID || `session_${Date.now()}`,
        order_id: order._id,
        device_type: req.headers['user-agent']?.includes('Mobile') ? 'mobile' : 'desktop',
        browser: req.headers['user-agent']?.split(' ')[0] || 'unknown',
        referrer: req.headers.referer || 'direct',
        ip_address: req.ip,
        metadata: {
          order_number: orderNumber,
          total_amount: finalTotal,
          item_count: orderItems.length,
          payment_method
        }
      });
    } catch (analyticsError) {
      console.error('Error tracking order analytics:', analyticsError);
    }

    res.status(201).json({
      success: true,
      data: {
        order_id: order._id,
        order_number: orderNumber,
        transaction_id: order.transaction_id,
        total_amount: finalTotal,
        payment_instructions: payment.data.instructions,
        payment_status: payment.data.status,
        payment_expires_at: payment.data.expires_at
      }
    });

  } catch (error) {
    // Release reserved inventory on payment failure
    for (const reservation of inventoryReservations) {
      const inventory = await Inventory.findById(reservation.inventory_id);
      if (inventory) {
        inventory.releaseReservedQuantity(reservation.quantity);
        await inventory.save();
      }
    }

    // Delete the order if payment creation failed
    await Order.findByIdAndDelete(order._id);

    console.error('Payment creation failed:', error);
    return next(new ErrorResponse('Failed to create payment. Please try again.', 500));
  }
});

// Helper function to calculate shipping cost
const calculateShippingCost = (items, shippingAddress, shippingMethod) => {
  const totalWeight = items.reduce((weight, item) => {
    const itemWeight = item.product.weight || 200; // Default 200g
    return weight + (itemWeight * item.quantity);
  }, 0);

  const subtotal = items.reduce((total, item) => total + (item.price * item.quantity), 0);

  const isDarEsSalaam = shippingAddress?.city?.toLowerCase().includes('dar');

  // Free shipping for orders over 500,000 TZS within Dar es Salaam
  if (subtotal >= 500000 && isDarEsSalaam) {
    return 0;
  }

  let baseCost = 5000; // 5,000 TZS base cost
  const weightCost = Math.ceil(totalWeight / 1000) * 2000; // 2,000 TZS per kg

  // Adjust for shipping method
  switch (shippingMethod) {
    case 'express':
      baseCost = 15000;
      break;
    case 'same_day':
      baseCost = 25000;
      break;
    case 'pickup':
      return 0;
    default:
      baseCost = isDarEsSalaam ? 5000 : 8000;
  }

  return baseCost + weightCost;
};

// @desc    Update order status (admin only)
// @route   PUT /api/orders/:id/status
// @access  Private (Admin)
exports.updateOrderStatus = asyncHandler(async (req, res, next) => {
  const { order_status, fulfillment_status, tracking_number, notes } = req.body;

  const order = await Order.findById(req.params.id);
  if (!order) {
    return next(new ErrorResponse('Order not found', 404));
  }

  const previousStatus = {
    order_status: order.order_status,
    fulfillment_status: order.fulfillment_status
  };

  // Update order status
  if (order_status) {
    order.order_status = order_status;
  }

  // Update fulfillment status and handle inventory
  if (fulfillment_status) {
    order.fulfillment_status = fulfillment_status;

    // If order is being fulfilled, commit inventory reservations
    if (fulfillment_status === 'processing' && previousStatus.fulfillment_status === 'pending') {
      for (const item of order.items) {
        const inventory = await Inventory.findOne({
          product: item.product,
          variant_sku: item.variant_sku || null,
          location: 'main_warehouse'
        });

        if (inventory) {
          inventory.commitReservedQuantity(item.quantity);
          await inventory.save();
        }
      }
    }

    // If order is cancelled, release inventory reservations
    if (fulfillment_status === 'cancelled') {
      for (const item of order.items) {
        const inventory = await Inventory.findOne({
          product: item.product,
          variant_sku: item.variant_sku || null,
          location: 'main_warehouse'
        });

        if (inventory) {
          inventory.releaseReservedQuantity(item.quantity);
          await inventory.save();
        }
      }
    }
  }

  // Add tracking number if provided
  if (tracking_number) {
    order.tracking_number = tracking_number;
  }

  // Add status update to history
  order.status_history.push({
    status: fulfillment_status || order_status,
    timestamp: new Date(),
    notes: notes || `Status updated to ${fulfillment_status || order_status}`,
    updated_by: req.user.id
  });

  await order.save();

  // Log the status update
  await auditLogService.logUpdate(
    req.user,
    'order',
    order._id.toString(),
    `Order ${order.order_number}`,
    previousStatus,
    { order_status: order.order_status, fulfillment_status: order.fulfillment_status },
    req,
    {
      order_number: order.order_number,
      status_change: true,
      tracking_number
    }
  );

  res.status(200).json({
    success: true,
    data: order
  });
});

// @desc    Add tracking information
// @route   PUT /api/orders/:id/tracking
// @access  Private (Admin)
exports.addTrackingInfo = asyncHandler(async (req, res, next) => {
  const { tracking_number, carrier, tracking_url, estimated_delivery } = req.body;

  const order = await Order.findById(req.params.id);
  if (!order) {
    return next(new ErrorResponse('Order not found', 404));
  }

  // Update tracking information
  order.tracking_number = tracking_number;
  order.carrier = carrier;
  order.tracking_url = tracking_url;
  order.estimated_delivery = estimated_delivery;

  // Update fulfillment status to shipped if not already
  if (order.fulfillment_status !== 'shipped' && order.fulfillment_status !== 'delivered') {
    order.fulfillment_status = 'shipped';
  }

  // Add to status history
  order.status_history.push({
    status: 'shipped',
    timestamp: new Date(),
    notes: `Package shipped with tracking number: ${tracking_number}`,
    updated_by: req.user.id
  });

  await order.save();

  res.status(200).json({
    success: true,
    data: order
  });
});

// @desc    Mark order as delivered
// @route   PUT /api/orders/:id/delivered
// @access  Private (Admin)
exports.markAsDelivered = asyncHandler(async (req, res, next) => {
  const { delivery_notes, delivered_to } = req.body;

  const order = await Order.findById(req.params.id);
  if (!order) {
    return next(new ErrorResponse('Order not found', 404));
  }

  order.fulfillment_status = 'delivered';
  order.delivered_at = new Date();
  order.delivered_to = delivered_to;

  // Add to status history
  order.status_history.push({
    status: 'delivered',
    timestamp: new Date(),
    notes: delivery_notes || 'Order delivered successfully',
    updated_by: req.user.id
  });

  await order.save();

  res.status(200).json({
    success: true,
    data: order
  });
});

// @desc    Get order by ID
// @route   GET /api/orders/:id
// @access  Public (with validation)
exports.getOrder = asyncHandler(async (req, res, next) => {
  const order = await Order.findById(req.params.id)
    .populate('items.product', 'name brand model images')
    .populate('user', 'name email');

  if (!order) {
    return next(new ErrorResponse('Order not found', 404));
  }

  // For authenticated users, check authorization
  if (req.user) {
    if (order.user && order.user._id.toString() !== req.user.id && req.user.role !== 'admin') {
      return next(new ErrorResponse('Not authorized to access this order', 403));
    }
  } else {
    // For guest orders (no authentication), allow access
    // In production, you might want to add additional validation like email verification
    if (order.user) {
      return next(new ErrorResponse('This order requires authentication', 401));
    }
  }

  res.status(200).json({
    success: true,
    data: order
  });
});

// @desc    Get user orders
// @route   GET /api/orders
// @access  Private
exports.getUserOrders = asyncHandler(async (req, res) => {
  const { page = 1, limit = 10, status } = req.query;

  const query = { user: req.user.id };

  // Filter by status if provided
  if (status) {
    query.order_status = status;
  }

  const orders = await Order.find(query)
    .populate('items.product', 'name brand model images price')
    .sort('-created_at')
    .limit(limit * 1)
    .skip((page - 1) * limit);

  const total = await Order.countDocuments(query);

  res.status(200).json({
    success: true,
    count: orders.length,
    total,
    page: parseInt(page),
    pages: Math.ceil(total / limit),
    data: orders
  });
});

// @desc    Get all orders (admin only)
// @route   GET /api/orders/admin
// @access  Private (Admin)
exports.getAllOrders = catchAsync(async (req, res) => {
  const {
    page = 1,
    limit = 20,
    order_status,
    fulfillment_status,
    payment_status,
    startDate,
    endDate,
    search,
    sort = '-created_at'
  } = req.query;

  // Build query
  const query = {};

  // Filter by order status
  if (order_status) {
    query.order_status = order_status;
  }

  // Filter by fulfillment status
  if (fulfillment_status) {
    query.fulfillment_status = fulfillment_status;
  }

  // Filter by payment status
  if (payment_status) {
    query.payment_status = payment_status;
  }

  // Filter by date range
  if (startDate || endDate) {
    query.created_at = {};
    if (startDate) query.created_at.$gte = new Date(startDate);
    if (endDate) query.created_at.$lte = new Date(endDate);
  }

  // Search by order number, customer name, or email
  if (search) {
    query.$or = [
      { order_number: new RegExp(search, 'i') },
      { customer_name: new RegExp(search, 'i') },
      { customer_email: new RegExp(search, 'i') }
    ];
  }

  const orders = await Order.find(query)
    .populate('user', 'name email')
    .populate('items.product', 'name brand model sku')
    .sort(sort)
    .limit(limit * 1)
    .skip((page - 1) * limit);

  const total = await Order.countDocuments(query);

  // Calculate summary statistics
  const stats = await Order.aggregate([
    { $match: query },
    {
      $group: {
        _id: null,
        totalRevenue: { $sum: '$total_amount' },
        averageOrderValue: { $avg: '$total_amount' },
        totalOrders: { $sum: 1 }
      }
    }
  ]);

  res.status(200).json({
    success: true,
    count: orders.length,
    total,
    page: parseInt(page),
    pages: Math.ceil(total / limit),
    stats: stats[0] || { totalRevenue: 0, averageOrderValue: 0, totalOrders: 0 },
    data: orders
  });
});

// @desc    Get order analytics (admin only)
// @route   GET /api/orders/analytics
// @access  Private (Admin)
exports.getOrderAnalytics = catchAsync(async (req, res) => {
  const { period = '30d' } = req.query;

  let startDate;
  switch (period) {
    case '7d':
      startDate = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
      break;
    case '30d':
      startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
      break;
    case '90d':
      startDate = new Date(Date.now() - 90 * 24 * 60 * 60 * 1000);
      break;
    default:
      startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
  }

  const analytics = await Order.aggregate([
    {
      $match: {
        created_at: { $gte: startDate },
        payment_status: 'completed'
      }
    },
    {
      $group: {
        _id: {
          year: { $year: '$created_at' },
          month: { $month: '$created_at' },
          day: { $dayOfMonth: '$created_at' }
        },
        totalRevenue: { $sum: '$total_amount' },
        orderCount: { $sum: 1 },
        averageOrderValue: { $avg: '$total_amount' }
      }
    },
    {
      $sort: { '_id.year': 1, '_id.month': 1, '_id.day': 1 }
    }
  ]);

  // Get status distribution
  const statusDistribution = await Order.aggregate([
    {
      $match: { created_at: { $gte: startDate } }
    },
    {
      $group: {
        _id: '$fulfillment_status',
        count: { $sum: 1 }
      }
    }
  ]);

  res.status(200).json({
    success: true,
    data: {
      analytics,
      statusDistribution,
      period
    }
  });
});

// @desc    Cancel order
// @route   PUT /api/orders/:id/cancel
// @access  Private (User who placed order or Admin)
exports.cancelOrder = asyncHandler(async (req, res, next) => {
  const order = await Order.findById(req.params.id);

  if (!order) {
    return next(new ErrorResponse('Order not found', 404));
  }

  // Check authorization
  if (req.user) {
    // Authenticated user - check if they own the order or are admin
    if (order.user && order.user.toString() !== req.user.id && req.user.role !== 'admin') {
      return next(new ErrorResponse('Not authorized to cancel this order', 403));
    }
  } else {
    // Guest user - only allow cancellation within 30 minutes and if order is pending
    if (order.user) {
      return next(new ErrorResponse('This order requires authentication to cancel', 401));
    }

    const thirtyMinutesAgo = new Date(Date.now() - 30 * 60 * 1000);
    if (order.created_at < thirtyMinutesAgo) {
      return next(new ErrorResponse('Guest orders can only be cancelled within 30 minutes', 400));
    }
  }

  // Check if order can be cancelled
  if (order.order_status === 'cancelled') {
    return next(new ErrorResponse('Order is already cancelled', 400));
  }

  if (['shipped', 'delivered'].includes(order.order_status)) {
    return next(new ErrorResponse('Cannot cancel order that has been shipped or delivered', 400));
  }

  // Update order status
  order.order_status = 'cancelled';
  order.payment_status = 'cancelled';
  order.cancelled_at = new Date();
  order.cancellation_reason = req.body.reason || 'Cancelled by customer';

  // Restore inventory for IMEI-tracked items
  for (const item of order.items) {
    if (item.imei) {
      const Phone = require('../models/Phone');
      await Phone.findOneAndUpdate(
        { imei: item.imei },
        {
          status: 'available',
          reserved_until: null,
          reserved_by: null
        }
      );
    }
  }

  await order.save();

  res.status(200).json({
    success: true,
    data: order,
    message: 'Order cancelled successfully'
  });
});

// @desc    Update order tracking
// @route   PUT /api/orders/admin/:id/tracking
// @access  Private (Admin)
exports.updateOrderTracking = asyncHandler(async (req, res, next) => {
  const order = await Order.findById(req.params.id);

  if (!order) {
    return next(new ErrorResponse('Order not found', 404));
  }

  const { tracking } = req.body;

  // Update tracking information
  order.tracking = {
    ...order.tracking,
    ...tracking,
    updated_at: new Date()
  };

  // Update order status based on tracking status
  if (tracking.current_status) {
    const statusMapping = {
      'pending': 'pending',
      'processing': 'processing',
      'ready_for_pickup': 'processing',
      'picked_up': 'shipped',
      'in_transit': 'shipped',
      'out_for_delivery': 'shipped',
      'delivered': 'delivered',
      'failed_delivery': 'shipped',
      'returned': 'cancelled'
    };

    if (statusMapping[tracking.current_status]) {
      order.order_status = statusMapping[tracking.current_status];
    }

    if (tracking.current_status === 'delivered') {
      order.delivered_at = new Date();
    }
  }

  await order.save();

  res.status(200).json({
    success: true,
    data: order,
    message: 'Order tracking updated successfully'
  });
});

// Functions are already exported above using exports.functionName
// No need for additional module.exports