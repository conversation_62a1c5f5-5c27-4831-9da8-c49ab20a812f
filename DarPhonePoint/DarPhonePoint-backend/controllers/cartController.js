/**
 * Cart Controller
 * Handles cart operations including abandoned cart recovery
 */

const Cart = require('../models/Cart');
const User = require('../models/User');
const Product = require('../models/Product');
const Inventory = require('../models/Inventory');
const { catchAsync } = require('../utils/errorHandlers');
const AppError = require('../utils/AppError');
const emailSequenceController = require('./emailSequenceController');

// Shipping calculation helper
const calculateShipping = (items, shippingAddress) => {
  // Basic shipping calculation - can be enhanced with real shipping APIs
  const totalWeight = items.reduce((weight, item) => {
    // Handle case where product might be null (deleted product)
    const itemWeight = item.product?.weight || 200; // Default 200g if not specified
    return weight + (itemWeight * item.quantity);
  }, 0);

  const baseShippingCost = 5000; // 5,000 TZS base cost
  const weightCost = Math.ceil(totalWeight / 1000) * 2000; // 2,000 TZS per kg

  // Free shipping for orders over 500,000 TZS within Dar es Salaam
  const subtotal = items.reduce((total, item) => {
    // Handle case where item might not have price (deleted product)
    const itemPrice = item.price || 0;
    return total + (itemPrice * item.quantity);
  }, 0);
  const isDarEsSalaam = shippingAddress?.city?.toLowerCase().includes('dar');

  if (subtotal >= 500000 && isDarEsSalaam) {
    return 0;
  }

  return baseShippingCost + weightCost;
};

// Helper function to get cart with calculated totals
const getCartWithTotals = async (cartId, userId) => {
  const cart = await Cart.findById(cartId).populate('items.product');

  if (!cart) {
    return {
      items: [],
      subtotal: 0,
      shipping_cost: 0,
      tax_amount: 0,
      total: 0,
      item_count: 0
    };
  }

  // Clean up cart items with null products (deleted products)
  const originalItemCount = cart.items.length;
  cart.items = cart.items.filter(item => item.product !== null);

  // Save cart if items were removed
  if (cart.items.length !== originalItemCount) {
    await cart.save();
  }

  // Calculate subtotal
  const subtotal = cart.items.reduce((sum, item) => {
    // Handle case where item might not have price (deleted product)
    const itemPrice = item.price || 0;
    return sum + (itemPrice * item.quantity);
  }, 0);

  // Get user's default shipping address for shipping calculation
  const user = await User.findById(userId);
  const shippingAddress = user?.getDefaultShippingAddress();

  // Calculate shipping
  const shipping_cost = calculateShipping(cart.items, shippingAddress);

  // Calculate tax (18% VAT in Tanzania)
  const tax_rate = 0.18;
  const tax_amount = Math.round(subtotal * tax_rate);

  // Calculate total
  const total = subtotal + shipping_cost + tax_amount;

  // Count total items
  const item_count = cart.items.reduce((count, item) => count + item.quantity, 0);

  return {
    ...cart.toObject(),
    subtotal,
    shipping_cost,
    tax_amount,
    total,
    item_count,
    shipping_address: shippingAddress
  };
};

/**
 * @desc    Add item to cart
 * @route   POST /api/cart
 * @access  Private
 */
exports.addToCart = catchAsync(async (req, res, next) => {
  const {
    productId,
    variantSku,
    quantity = 1,
    imei,
    warrantyOption = 'standard',
    tradeInDeviceId,
    accessories = [],
    deviceCondition = 'new'
  } = req.body;
  const userId = req.user.id;

  // Validate product exists
  const product = await Product.findById(productId);
  if (!product) {
    return next(new AppError('Product not found', 404));
  }

  // Check if product is active
  if (!product.is_active) {
    return next(new AppError('Product is not available', 400));
  }

  // Validate variant if specified
  let selectedVariant = null;
  let itemPrice = product.price;
  let itemSku = product.sku;

  if (variantSku && product.variants && product.variants.length > 0) {
    selectedVariant = product.variants.find(v => v.sku === variantSku && v.is_active);
    if (!selectedVariant) {
      return next(new AppError('Product variant not found or not available', 404));
    }
    itemPrice = selectedVariant.price;
    itemSku = selectedVariant.sku;
  }

  // Check inventory availability
  if (product.track_inventory) {
    const inventory = await Inventory.findOne({
      product: productId,
      variant_sku: variantSku || null,
      location: 'main_warehouse'
    });

    if (!inventory || inventory.quantity_available < quantity) {
      return next(new AppError('Insufficient stock available', 400));
    }
  }

  // Validate IMEI if provided (for phones)
  if (imei) {
    // Check if IMEI is already in use
    const existingCartItem = await Cart.findOne({
      'items.imei': imei
    });

    if (existingCartItem) {
      return next(new AppError('This IMEI is already in another cart', 400));
    }

    // Validate IMEI format (basic validation)
    if (!/^\d{15}$/.test(imei)) {
      return next(new AppError('Invalid IMEI format. IMEI must be 15 digits', 400));
    }
  }

  // Calculate warranty price
  let warrantyPrice = 0;
  let warrantyDuration = 12; // Default 12 months

  if (warrantyOption && warrantyOption !== 'none') {
    const warrantyPricing = {
      'standard': { price: 0, duration: 12 },
      'extended': { price: itemPrice * 0.1, duration: 24 },
      'premium': { price: itemPrice * 0.15, duration: 36 }
    };

    if (warrantyPricing[warrantyOption]) {
      warrantyPrice = warrantyPricing[warrantyOption].price;
      warrantyDuration = warrantyPricing[warrantyOption].duration;
    }
  }

  // Handle trade-in value
  let tradeInValue = 0;
  if (tradeInDeviceId) {
    const TradeIn = require('../models/TradeIn');
    const tradeInDevice = await TradeIn.findById(tradeInDeviceId);

    if (!tradeInDevice || tradeInDevice.customer.toString() !== userId) {
      return next(new AppError('Trade-in device not found or not owned by user', 404));
    }

    if (tradeInDevice.status !== 'evaluated') {
      return next(new AppError('Trade-in device must be evaluated first', 400));
    }

    tradeInValue = tradeInDevice.final_value || 0;
  }

  // Find or create cart
  let cart = await Cart.findOne({ user: userId });

  if (!cart) {
    cart = new Cart({
      user: userId,
      items: [],
      created_at: new Date()
    });
  }

  // For phones with IMEI, each item is unique (no quantity updates)
  // For other products, check if product+variant already in cart
  let itemIndex = -1;

  if (!imei) {
    itemIndex = cart.items.findIndex(item =>
      item.product.toString() === productId &&
      (item.variant_sku || null) === (variantSku || null) &&
      !item.imei // Only match items without IMEI
    );
  }

  if (itemIndex > -1 && !imei) {
    // Update quantity if product+variant exists (non-IMEI items only)
    const newQuantity = cart.items[itemIndex].quantity + quantity;

    // Check total quantity against inventory
    if (product.track_inventory) {
      const inventory = await Inventory.findOne({
        product: productId,
        variant_sku: variantSku || null,
        location: 'main_warehouse'
      });

      if (!inventory || inventory.quantity_available < newQuantity) {
        return next(new AppError('Insufficient stock for requested quantity', 400));
      }
    }

    cart.items[itemIndex].quantity = newQuantity;
    cart.items[itemIndex].price = itemPrice; // Update price in case it changed
  } else {
    // Add new item with phone-specific features
    const newItem = {
      product: productId,
      variant_sku: variantSku,
      quantity,
      price: itemPrice,
      name: product.name,
      sku: itemSku,
      variant_details: selectedVariant ? {
        color: selectedVariant.color,
        storage: selectedVariant.storage,
        memory: selectedVariant.memory
      } : {},
      // Phone-specific fields
      imei: imei || undefined,
      warranty_option: warrantyOption,
      warranty_price: warrantyPrice,
      warranty_duration: warrantyDuration,
      trade_in_device: tradeInDeviceId || undefined,
      trade_in_value: tradeInValue,
      accessories: accessories || [],
      device_condition: deviceCondition
    };

    cart.items.push(newItem);
  }

  cart.updated_at = new Date();
  await cart.save();

  // Get populated cart with totals
  const cartData = await getCartWithTotals(cart._id, userId);

  res.status(200).json({
    success: true,
    data: cartData
  });
});

/**
 * @desc    Get user cart
 * @route   GET /api/cart
 * @access  Private
 */
exports.getCart = catchAsync(async (req, res, next) => {
  const userId = req.user.id;

  const cart = await Cart.findOne({ user: userId });

  if (!cart) {
    return res.status(200).json({
      success: true,
      data: {
        items: [],
        subtotal: 0,
        shipping_cost: 0,
        tax_amount: 0,
        total: 0,
        item_count: 0
      }
    });
  }

  // Get cart with calculated totals
  const cartData = await getCartWithTotals(cart._id, userId);

  res.status(200).json({
    success: true,
    data: cartData
  });
});

/**
 * @desc    Update cart item
 * @route   PATCH /api/cart/:itemId
 * @access  Private
 */
exports.updateCartItem = catchAsync(async (req, res, next) => {
  const { itemId } = req.params;
  const { quantity } = req.body;
  const userId = req.user.id;

  if (quantity < 0) {
    return next(new AppError('Quantity cannot be negative', 400));
  }

  const cart = await Cart.findOne({ user: userId });

  if (!cart) {
    return next(new AppError('Cart not found', 404));
  }

  const itemIndex = cart.items.findIndex(item => item._id.toString() === itemId);

  if (itemIndex === -1) {
    return next(new AppError('Item not found in cart', 404));
  }

  const cartItem = cart.items[itemIndex];

  if (quantity === 0) {
    // Remove item if quantity is 0
    cart.items.splice(itemIndex, 1);
  } else {
    // Check inventory availability for the new quantity
    const product = await Product.findById(cartItem.product);

    if (product && product.track_inventory) {
      const inventory = await Inventory.findOne({
        product: cartItem.product,
        variant_sku: cartItem.variant_sku || null,
        location: 'main_warehouse'
      });

      if (!inventory || inventory.quantity_available < quantity) {
        return next(new AppError(`Only ${inventory?.quantity_available || 0} items available in stock`, 400));
      }
    }

    // Update quantity
    cartItem.quantity = quantity;
  }

  cart.updated_at = new Date();
  await cart.save();

  // Get cart with calculated totals
  const cartData = await getCartWithTotals(cart._id, userId);

  res.status(200).json({
    success: true,
    data: cartData
  });
});

/**
 * @desc    Remove item from cart
 * @route   DELETE /api/cart/:itemId
 * @access  Private
 */
exports.removeCartItem = catchAsync(async (req, res, next) => {
  const { itemId } = req.params;
  const userId = req.user.id;

  const cart = await Cart.findOne({ user: userId });

  if (!cart) {
    return next(new AppError('Cart not found', 404));
  }

  const itemIndex = cart.items.findIndex(item => item._id.toString() === itemId);

  if (itemIndex === -1) {
    return next(new AppError('Item not found in cart', 404));
  }

  cart.items.splice(itemIndex, 1);
  cart.updated_at = new Date();
  await cart.save();

  // Get cart with calculated totals
  const cartData = await getCartWithTotals(cart._id, userId);

  res.status(200).json({
    success: true,
    data: cartData
  });
});

/**
 * @desc    Clear entire cart
 * @route   DELETE /api/cart
 * @access  Private
 */
exports.clearCart = catchAsync(async (req, res, next) => {
  const userId = req.user.id;

  const cart = await Cart.findOne({ user: userId });

  if (!cart) {
    return res.status(200).json({
      success: true,
      message: 'Cart is already empty'
    });
  }

  cart.items = [];
  cart.updated_at = new Date();
  await cart.save();

  res.status(200).json({
    success: true,
    message: 'Cart cleared successfully',
    data: {
      items: [],
      subtotal: 0,
      shipping_cost: 0,
      tax_amount: 0,
      total: 0,
      item_count: 0
    }
  });
});

/**
 * @desc    Calculate shipping for cart
 * @route   POST /api/cart/calculate-shipping
 * @access  Private
 */
exports.calculateCartShipping = catchAsync(async (req, res, next) => {
  const userId = req.user.id;
  const { shipping_address } = req.body;

  const cart = await Cart.findOne({ user: userId }).populate('items.product');

  if (!cart || cart.items.length === 0) {
    return next(new AppError('Cart is empty', 400));
  }

  // Calculate shipping with provided address
  const shipping_cost = calculateShipping(cart.items, shipping_address);

  // Calculate other totals
  const subtotal = cart.items.reduce((sum, item) => {
    // Handle case where item might not have price (deleted product)
    const itemPrice = item.price || 0;
    return sum + (itemPrice * item.quantity);
  }, 0);

  const tax_rate = 0.18;
  const tax_amount = Math.round(subtotal * tax_rate);
  const total = subtotal + shipping_cost + tax_amount;

  res.status(200).json({
    success: true,
    data: {
      subtotal,
      shipping_cost,
      tax_amount,
      total,
      shipping_address
    }
  });
});

/**
 * @desc    Validate cart before checkout
 * @route   POST /api/cart/validate
 * @access  Private
 */
exports.validateCart = catchAsync(async (req, res, next) => {
  const userId = req.user.id;

  const cart = await Cart.findOne({ user: userId }).populate('items.product');

  if (!cart || cart.items.length === 0) {
    return next(new AppError('Cart is empty', 400));
  }

  const errors = [];
  const validItems = [];

  // Check each item for availability and stock
  for (const item of cart.items) {
    const product = item.product;

    // Check if product exists (might be null if deleted)
    if (!product) {
      errors.push(`Product no longer exists`);
      continue;
    }

    // Check if product is still active
    if (!product.is_active) {
      errors.push(`${product.name} is no longer available`);
      continue;
    }

    // Check inventory if tracking is enabled
    if (product.track_inventory) {
      const inventory = await Inventory.findOne({
        product: product._id,
        variant_sku: item.variant_sku || null,
        location: 'main_warehouse'
      });

      if (!inventory || inventory.quantity_available < item.quantity) {
        errors.push(`${product.name} - Only ${inventory?.quantity_available || 0} items available (requested: ${item.quantity})`);
        continue;
      }
    }

    // Check if variant is still available (if applicable)
    if (item.variant_sku && product.variants && product.variants.length > 0) {
      const variant = product.variants.find(v => v.sku === item.variant_sku);
      if (!variant || !variant.is_active) {
        errors.push(`${product.name} variant is no longer available`);
        continue;
      }
    }

    validItems.push(item);
  }

  res.status(200).json({
    success: true,
    data: {
      is_valid: errors.length === 0,
      errors,
      valid_items_count: validItems.length,
      total_items_count: cart.items.length
    }
  });
});

/**
 * @desc    Clear cart
 * @route   DELETE /api/cart
 * @access  Private
 */
exports.clearCart = catchAsync(async (req, res, next) => {
  const userId = req.user.id;

  const cart = await Cart.findOne({ user: userId });

  if (!cart) {
    return next(new AppError('Cart not found', 404));
  }

  cart.items = [];
  cart.updated_at = new Date();
  await cart.save();

  res.status(200).json({
    success: true,
    data: {
      items: [],
      total: 0
    }
  });
});

/**
 * @desc    Process abandoned carts
 * @route   POST /api/cart/process-abandoned
 * @access  Admin
 */
exports.processAbandonedCarts = catchAsync(async (req, res, next) => {
  // Find carts that haven't been updated in 24 hours
  const abandonedTime = new Date();
  abandonedTime.setHours(abandonedTime.getHours() - 24);

  const abandonedCarts = await Cart.find({
    updated_at: { $lt: abandonedTime },
    items: { $exists: true, $ne: [] },
    abandoned_cart_processed: { $ne: true }
  }).populate('user');

  let processed = 0;
  let errors = 0;

  for (const cart of abandonedCarts) {
    try {
      if (!cart.user || !cart.user.email) continue;

      // Get the first product in the cart for the email
      const firstProduct = await Product.findById(cart.items[0].product);
      if (!firstProduct) continue;

      // Trigger abandoned cart email sequence
      await emailSequenceController.triggerSequence({
        body: {
          email: cart.user.email,
          user_id: cart.user._id,
          sequence_type: 'cart_abandoned',
          metadata: {
            cart_id: cart._id,
            product_id: firstProduct._id,
            product_name: firstProduct.name
          }
        }
      }, {
        status: (code) => ({
          json: (data) => {
            // Abandoned cart sequence triggered successfully
          }
        })
      }, (err) => {
        errors++;
      });

      // Mark cart as processed
      cart.abandoned_cart_processed = true;
      await cart.save();
      processed++;
    } catch (error) {
      errors++;
    }
  }

  res.status(200).json({
    success: true,
    message: `Processed ${processed} abandoned carts with ${errors} errors`,
    data: { total: abandonedCarts.length, processed, errors }
  });
});

/**
 * @desc    Mark cart as abandoned
 * @route   POST /api/cart/:id/mark-abandoned
 * @access  Admin
 */
exports.markCartAbandoned = catchAsync(async (req, res, next) => {
  const { id } = req.params;

  const cart = await Cart.findById(id);
  if (!cart) {
    return next(new AppError('Cart not found', 404));
  }

  cart.abandoned_cart_processed = false;
  await cart.save();

  res.status(200).json({
    success: true,
    message: 'Cart marked as abandoned'
  });
});
