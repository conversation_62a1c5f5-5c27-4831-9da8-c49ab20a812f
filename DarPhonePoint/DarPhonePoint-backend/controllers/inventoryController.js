const Inventory = require('../models/Inventory');
const Product = require('../models/Product');
const catchAsync = require('../utils/catchAsync');
const AppError = require('../utils/AppError');

/**
 * @desc    Get inventory for a specific product
 * @route   GET /api/inventory/product/:productId
 * @access  Private (Admin)
 */
exports.getProductInventory = catchAsync(async (req, res, next) => {
  const { productId } = req.params;
  const { location } = req.query;

  const query = { product: productId };
  if (location) query.location = location;

  const inventory = await Inventory.find(query)
    .populate('product', 'name sku brand model')
    .sort({ location: 1, variant_sku: 1 });

  res.status(200).json({
    success: true,
    count: inventory.length,
    data: inventory
  });
});

/**
 * @desc    Get all inventory items
 * @route   GET /api/inventory
 * @access  Private (Admin)
 */
exports.getAllInventory = catchAsync(async (req, res, next) => {
  const { location, low_stock, needs_reorder, page = 1, limit = 20 } = req.query;

  // Build query
  const query = {};
  if (location) query.location = location;

  // Create aggregation pipeline
  const pipeline = [
    { $match: query },
    {
      $lookup: {
        from: 'products',
        localField: 'product',
        foreignField: '_id',
        as: 'product'
      }
    },
    { $unwind: '$product' },
    {
      $addFields: {
        needs_reorder: { $lte: ['$quantity_available', '$reorder_point'] },
        is_low_stock: { 
          $and: [
            { $lte: ['$quantity_available', { $multiply: ['$reorder_point', 1.5] }] },
            { $gt: ['$quantity_available', 0] }
          ]
        }
      }
    }
  ];

  // Add filters based on query parameters
  if (low_stock === 'true') {
    pipeline.push({ $match: { is_low_stock: true } });
  }
  if (needs_reorder === 'true') {
    pipeline.push({ $match: { needs_reorder: true } });
  }

  // Add pagination
  const skip = (page - 1) * limit;
  pipeline.push(
    { $sort: { 'product.name': 1, variant_sku: 1 } },
    { $skip: skip },
    { $limit: parseInt(limit) }
  );

  const inventory = await Inventory.aggregate(pipeline);

  // Get total count for pagination
  const countPipeline = [...pipeline.slice(0, -3)]; // Remove sort, skip, limit
  countPipeline.push({ $count: 'total' });
  const countResult = await Inventory.aggregate(countPipeline);
  const total = countResult[0]?.total || 0;

  res.status(200).json({
    success: true,
    count: inventory.length,
    total,
    page: parseInt(page),
    pages: Math.ceil(total / limit),
    data: inventory
  });
});

/**
 * @desc    Create or update inventory record
 * @route   POST /api/inventory
 * @access  Private (Admin)
 */
exports.createOrUpdateInventory = catchAsync(async (req, res, next) => {
  const {
    product,
    variant_sku,
    location = 'main_warehouse',
    quantity_on_hand,
    reorder_point,
    reorder_quantity,
    max_stock_level,
    average_cost,
    last_cost
  } = req.body;

  // Check if product exists
  const productExists = await Product.findById(product);
  if (!productExists) {
    return next(new AppError('Product not found', 404));
  }

  // Find existing inventory record
  const existingInventory = await Inventory.findOne({
    product,
    variant_sku: variant_sku || null,
    location
  });

  let inventory;

  if (existingInventory) {
    // Update existing record
    existingInventory.quantity_on_hand = quantity_on_hand;
    existingInventory.reorder_point = reorder_point;
    existingInventory.reorder_quantity = reorder_quantity;
    existingInventory.max_stock_level = max_stock_level;
    existingInventory.average_cost = average_cost;
    existingInventory.last_cost = last_cost;
    
    inventory = await existingInventory.save();
  } else {
    // Create new record
    inventory = await Inventory.create({
      product,
      variant_sku,
      location,
      quantity_on_hand,
      reorder_point,
      reorder_quantity,
      max_stock_level,
      average_cost,
      last_cost
    });
  }

  await inventory.populate('product', 'name sku brand model');

  res.status(201).json({
    success: true,
    data: inventory
  });
});

/**
 * @desc    Add inventory movement (stock adjustment)
 * @route   POST /api/inventory/:id/movement
 * @access  Private (Admin)
 */
exports.addInventoryMovement = catchAsync(async (req, res, next) => {
  const { id } = req.params;
  const { type, quantity, reason, reference_id, reference_type, notes } = req.body;

  const inventory = await Inventory.findById(id);
  if (!inventory) {
    return next(new AppError('Inventory record not found', 404));
  }

  // Add movement
  inventory.addMovement(type, quantity, reason, req.user.id, {
    reference_id,
    reference_type,
    notes
  });

  await inventory.save();
  await inventory.populate('product', 'name sku brand model');

  res.status(200).json({
    success: true,
    data: inventory
  });
});

/**
 * @desc    Reserve inventory for order
 * @route   POST /api/inventory/reserve
 * @access  Private
 */
exports.reserveInventory = catchAsync(async (req, res, next) => {
  const { items } = req.body; // Array of { product, variant_sku, quantity, location }

  const reservations = [];
  const errors = [];

  for (const item of items) {
    const inventory = await Inventory.findOne({
      product: item.product,
      variant_sku: item.variant_sku || null,
      location: item.location || 'main_warehouse'
    });

    if (!inventory) {
      errors.push(`Inventory not found for product ${item.product}`);
      continue;
    }

    if (inventory.reserveQuantity(item.quantity)) {
      await inventory.save();
      reservations.push({
        inventory_id: inventory._id,
        quantity_reserved: item.quantity
      });
    } else {
      errors.push(`Insufficient stock for product ${item.product}. Available: ${inventory.quantity_available}, Requested: ${item.quantity}`);
    }
  }

  if (errors.length > 0) {
    return next(new AppError(`Reservation failed: ${errors.join(', ')}`, 400));
  }

  res.status(200).json({
    success: true,
    message: 'Inventory reserved successfully',
    data: reservations
  });
});

/**
 * @desc    Release reserved inventory
 * @route   POST /api/inventory/release
 * @access  Private
 */
exports.releaseInventory = catchAsync(async (req, res, next) => {
  const { reservations } = req.body; // Array of { inventory_id, quantity }

  for (const reservation of reservations) {
    const inventory = await Inventory.findById(reservation.inventory_id);
    if (inventory) {
      inventory.releaseReservedQuantity(reservation.quantity);
      await inventory.save();
    }
  }

  res.status(200).json({
    success: true,
    message: 'Inventory released successfully'
  });
});

/**
 * @desc    Get low stock alerts
 * @route   GET /api/inventory/alerts/low-stock
 * @access  Private (Admin)
 */
exports.getLowStockAlerts = catchAsync(async (req, res, next) => {
  const lowStockItems = await Inventory.aggregate([
    {
      $match: {
        $expr: { $lte: ['$quantity_available', '$reorder_point'] }
      }
    },
    {
      $lookup: {
        from: 'products',
        localField: 'product',
        foreignField: '_id',
        as: 'product'
      }
    },
    { $unwind: '$product' },
    {
      $project: {
        product: 1,
        variant_sku: 1,
        location: 1,
        quantity_available: 1,
        reorder_point: 1,
        reorder_quantity: 1,
        shortage: { $subtract: ['$reorder_point', '$quantity_available'] }
      }
    },
    { $sort: { shortage: -1 } }
  ]);

  res.status(200).json({
    success: true,
    count: lowStockItems.length,
    data: lowStockItems
  });
});

/**
 * @desc    Get inventory summary by location
 * @route   GET /api/inventory/summary
 * @access  Private (Admin)
 */
exports.getInventorySummary = catchAsync(async (req, res, next) => {
  const summary = await Inventory.aggregate([
    {
      $group: {
        _id: '$location',
        total_items: { $sum: 1 },
        total_quantity: { $sum: '$quantity_on_hand' },
        total_available: { $sum: '$quantity_available' },
        total_reserved: { $sum: '$quantity_reserved' },
        low_stock_items: {
          $sum: {
            $cond: [{ $lte: ['$quantity_available', '$reorder_point'] }, 1, 0]
          }
        },
        total_value: { $sum: { $multiply: ['$quantity_on_hand', '$average_cost'] } }
      }
    },
    { $sort: { _id: 1 } }
  ]);

  res.status(200).json({
    success: true,
    data: summary
  });
});
