const Order = require('../models/Order');
const User = require('../models/User');
const Product = require('../models/Product');
const Analytics = require('../models/Analytics');
const paymentService = require('../services/paymentService');
const MonitoringService = require('../services/monitoringService');
const ErrorResponse = require('../utils/errorResponse');
const asyncHandler = require('../middleware/async');
const { sendOrderConfirmationEmail } = require('../utils/email');
const emailSequenceController = require('./emailSequenceController');
const webhookBatchProcessor = require('../services/webhookBatchProcessor');
const logger = require('../utils/logger');

/**
 * Retry webhook processing with exponential backoff
 * @param {Function} operation - Operation to retry
 * @param {number} maxRetries - Maximum number of retries
 * @returns {Promise} Operation result
 */
async function retryWebhookProcessing(operation, maxRetries = 3) {
  let lastError;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error;

      // Don't retry certain errors
      if (error.message.includes('Invalid webhook signature') ||
        error.message.includes('Order not found')) {
        throw error;
      }

      if (attempt === maxRetries) {
        break;
      }

      // Exponential backoff: 100ms, 200ms, 400ms
      const delay = 100 * Math.pow(2, attempt - 1);
      logger.warn(`Webhook processing attempt ${attempt} failed, retrying in ${delay}ms:`, {
        error: error.message,
        attempt,
        maxRetries
      });

      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }

  throw lastError;
}

/**
 * Wrap operation with timeout protection
 * @param {Function} operation - Operation to execute
 * @param {number} timeoutMs - Timeout in milliseconds
 * @returns {Promise} Operation result
 */
async function withTimeout(operation, timeoutMs = 30000) {
  return Promise.race([
    operation(),
    new Promise((_, reject) =>
      setTimeout(() => reject(new Error(`Operation timed out after ${timeoutMs}ms`)), timeoutMs)
    )
  ]);
}

exports.stripeWebhook = async (req, res) => {
  const startTime = Date.now();
  let webhookData = null;

  try {
    const signature = req.headers['whop-signature'] || req.headers['x-whop-signature'];
    const payload = req.body;

    // Process Whop webhook with retry logic
    webhookData = await retryWebhookProcessing(async () => {
      return await paymentService.processWebhook(payload, signature);
    });

    // Find ALL orders by payment ID
    const orders = await Order.find({ transaction_id: webhookData.paymentId });
    if (!orders || orders.length === 0) {
      logger.warn(`No orders found for Whop payment: ${webhookData.paymentId}`, {
        paymentId: webhookData.paymentId,
        webhookType: webhookData.type || 'unknown'
      });
      return res.status(200).json({ received: true, warning: 'No orders found' });
    }

    logger.info(`Processing webhook for ${orders.length} orders`, {
      paymentId: webhookData.paymentId,
      orderCount: orders.length
    });

    // Update all orders status based on webhook with retry and timeout protection
    const processedOrders = [];
    await withTimeout(async () => {
      await retryWebhookProcessing(async () => {
        for (const order of orders) {
          await updateOrderFromWebhook(order, webhookData);
          processedOrders.push(order._id);
        }
      });
    }, 25000); // 25 second timeout

    // Log successful webhook processing
    const processingTime = Date.now() - startTime;
    logger.info('Stripe webhook processed successfully:', {
      type: 'stripe',
      paymentId: webhookData.paymentId,
      status: webhookData.status,
      processingTimeMs: processingTime,
      totalOrders: orders.length,
      processedOrders: processedOrders
    });

    MonitoringService.incrementWebhookReceived();
    res.status(200).json({
      received: true,
      processingTimeMs: processingTime,
      totalOrders: orders.length,
      processedOrders: processedOrders
    });
  } catch (error) {
    const processingTime = Date.now() - startTime;
    logger.error('Stripe webhook handling error:', {
      error: error.message,
      stack: error.stack,
      paymentId: webhookData?.paymentId,
      processingTimeMs: processingTime
    });

    MonitoringService.logError(error, {
      context: 'webhook-controller',
      type: 'stripe',
      paymentId: webhookData?.paymentId
    });

    // Return 200 to prevent Stripe retries for certain errors
    if (error.message.includes('Invalid webhook signature')) {
      return res.status(200).json({ received: true, error: 'Invalid signature' });
    }

    res.status(500).json({ success: false, error: 'Webhook processing failed' });
  }
};

/**
 * Update order based on webhook data with idempotency protection
 * @param {Object} order - Order document
 * @param {Object} webhookData - Processed webhook data
 */
async function updateOrderFromWebhook(order, webhookData) {
  // Map webhook status to order status
  let orderStatus;
  switch (webhookData.status) {
    case 'completed':
    case 'succeeded':
      orderStatus = 'completed';
      break;
    case 'failed':
      orderStatus = 'failed';
      break;
    case 'canceled':
    case 'cancelled':
      orderStatus = 'cancelled';
      break;
    default:
      orderStatus = 'pending';
  }

  // Check if order is already in the target status (idempotency)
  if (order.payment_status === orderStatus) {
    logger.info('Order already in target status, skipping update:', {
      orderId: order._id,
      currentStatus: order.payment_status,
      targetStatus: orderStatus,
      paymentId: webhookData.paymentId
    });
    return;
  }

  // Store previous status for logging
  const previousStatus = order.payment_status;

  // Update order
  order.payment_status = orderStatus;
  order.webhook_processed_at = new Date();
  await order.save();

  logger.info('Order status updated:', {
    orderId: order._id,
    previousStatus,
    newStatus: orderStatus,
    paymentId: webhookData.paymentId
  });

  // If payment successful, update user's purchased products
  if (orderStatus === 'completed') {
    const user = await User.findById(order.user);
    if (user && !user.purchased_products.includes(order.product)) {
      user.purchased_products.push(order.product);
      await user.save();

      logger.info('Product access granted to user:', {
        userId: user._id,
        productId: order.product,
        orderId: order._id
      });

      // Track purchase analytics event
      try {
        const product = await Product.findById(order.product);
        await Analytics.create({
          event_type: 'purchase',
          user_id: user._id,
          session_id: `webhook_${Date.now()}`,
          product_id: order.product,
          order_id: order._id,
          device_type: 'unknown',
          browser: 'webhook',
          referrer: 'stripe_webhook',
          ip_address: '127.0.0.1',
          metadata: {
            product_name: product?.name || 'Unknown Product',
            amount: order.amount,
            payment_method: 'stripe',
            order_id: order._id.toString(),
            webhook_processed: true
          }
        });
        logger.info('Purchase analytics event tracked:', {
          orderId: order._id,
          userId: user._id,
          productId: order.product
        });
      } catch (analyticsError) {
        logger.error('Error tracking purchase analytics:', {
          error: analyticsError.message,
          orderId: order._id,
          userId: user._id
        });
      }

      // Send order confirmation email
      try {
        const product = await Product.findById(order.product);
        await sendOrderConfirmationEmail(user.email, user.name, product);
        logger.info('Order confirmation email sent:', {
          email: user.email,
          productName: product.name
        });
      } catch (emailError) {
        logger.error('Order confirmation email error:', {
          error: emailError.message,
          userId: user._id,
          orderId: order._id
        });
      }

      // Trigger purchase email sequence
      try {
        await emailSequenceController.triggerSequence({
          body: {
            email: user.email,
            user_id: user._id,
            sequence_type: 'purchase',
            product_id: order.product
          }
        }, {
          status: (code) => ({ json: (data) => logger.info('Purchase sequence triggered') })
        }, (err) => {
          if (err) logger.error('Purchase sequence trigger error:', err);
        });
      } catch (seqError) {
        logger.error('Purchase sequence error:', {
          error: seqError.message,
          userId: user._id,
          orderId: order._id
        });
      }
    } else if (user && user.purchased_products.includes(order.product)) {
      logger.info('User already has product access:', {
        userId: user._id,
        productId: order.product,
        orderId: order._id
      });
    }
  }
}

/**
 * Get webhook processing statistics
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getWebhookStats = async (req, res) => {
  try {
    const stats = await webhookBatchProcessor.getQueueStats();
    res.status(200).json({
      success: true,
      data: stats
    });
  } catch (error) {
    logger.error('Webhook stats error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
};
