const User = require('../models/User');
const Lead = require('../models/Lead');
const jwt = require('jsonwebtoken');
const crypto = require('crypto');
const config = require('../config/config');
const emailSequenceController = require('./emailSequenceController');
const auditLogService = require('../services/auditLogService');
const emailSender = require('../services/emailSender');
const jwtBlacklistService = require('../services/jwtBlacklistService');
const sessionManagementService = require('../services/sessionManagementService');
const loginAttemptTracker = require('../middleware/loginAttemptTracker');
const logger = require('../utils/logger');

// Register user
exports.register = async (req, res) => {
  try {
    const { name, email, password } = req.body;

    // Check if user exists
    let user = await User.findOne({ email });

    if (user) {
      return res.status(400).json({
        success: false,
        message: 'User already exists'
      });
    }

    // Create new user (not verified initially)
    user = new User({
      name,
      email,
      password,
      isEmailVerified: false
    });

    await user.save();

    // Log user registration
    await auditLogService.createLog({
      userId: user._id,
      action: 'register',
      resourceType: 'user',
      resourceId: user._id.toString(),
      description: `User registered: ${user.name} (${user.email})`,
      newState: auditLogService.sanitizeState(user.toObject()),
      metadata: {
        email: user.email,
        name: user.name,
        user_type: user.user_type,
        requires_verification: true
      },
      ipAddress: req.ip,
      userAgent: req.headers['user-agent']
    });

    // Create lead entry for new user registration
    try {
      const existingLead = await Lead.findOne({ email });
      if (!existingLead) {
        await Lead.create({
          name,
          email,
          source: 'registration',
          status: 'new'
        });

        // Log lead creation
        await auditLogService.logCreate(
          user,
          'lead',
          email,
          `Lead from registration: ${name}`,
          { name, email, source: 'registration' },
          req,
          { source: 'registration', user_id: user._id }
        );

        logger.info(`Lead created for new user registration: ${email}`);
      }
    } catch (leadError) {
      logger.error('Error creating lead during registration:', leadError);
      // Don't fail registration if lead creation fails
    }

    // Generate email verification token
    const verificationToken = user.generateEmailVerificationToken();
    await user.save();

    // Send verification email (async - don't wait)
    const verificationUrl = `${config.FRONTEND_URL}/verify-email?token=${verificationToken}`;

    // Send email in background to improve response time
    setImmediate(async () => {
      try {
        await emailSender.sendEmail({
          template: 'email-verification',
          data: {
            name: user.name,
            verificationUrl
          },
          to: user.email,
          subject: 'Verify Your Email - AIXcelerate',
          metadata: {
            userId: user._id,
            type: 'email_verification'
          }
        });

        logger.info(`Verification email sent to: ${user.email}`);
      } catch (emailError) {
        logger.error('Error sending verification email:', emailError);
        // Email failure doesn't affect registration success
      }
    });

    res.status(201).json({
      success: true,
      message: 'Registration successful! Please check your email to verify your account.',
      requiresVerification: true
    });
  } catch (error) {
    console.error('Registration error:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// Login user
exports.login = async (req, res) => {
  try {
    const { email, password } = req.body;

    // Check if user exists
    const user = await User.findOne({ email }).select('+password');

    if (!user) {
      return res.status(400).json({
        success: false,
        message: 'Invalid credentials'
      });
    }

    // Check password
    // Try both methods for compatibility
    let isMatch = false;
    try {
      if (typeof user.comparePassword === 'function') {
        isMatch = await user.comparePassword(password);
      } else if (typeof user.matchPassword === 'function') {
        isMatch = await user.matchPassword(password);
      } else {
        throw new Error('Password comparison method not found');
      }
    } catch (err) {
      console.error('Password comparison error:', err);
      return res.status(500).json({
        success: false,
        message: 'Server error'
      });
    }

    if (!isMatch) {
      // Record failed login attempt
      await loginAttemptTracker.handleFailedLogin(email, req);

      return res.status(400).json({
        success: false,
        message: 'Invalid credentials'
      });
    }

    // Check if email is verified (always enforce except for admin users created via scripts)
    if (!user.isEmailVerified) {
      return res.status(401).json({
        success: false,
        message: 'Please verify your email address before logging in.',
        requiresVerification: true,
        email: user.email
      });
    }

    // Update last login
    user.last_login = Date.now();
    await user.save();

    // Log login event
    await auditLogService.logLogin(user, req);

    // Create JWT token
    const payload = {
      user: {
        id: user.id
      }
    };

    jwt.sign(
      payload,
      config.JWT_SECRET,
      { expiresIn: config.JWT_EXPIRE },
      async (err, token) => {
        if (err) throw err;

        // Clear failed login attempts
        await loginAttemptTracker.handleSuccessfulLogin(email, req);

        // Create session with enhanced tracking
        const sessionData = {
          ip: req.ip,
          userAgent: req.headers['user-agent'],
          location: req.headers['cf-ipcountry'] || 'Unknown', // Cloudflare country header
          timestamp: new Date().toISOString()
        };

        await sessionManagementService.createSession(user.id, token, sessionData);

        res.status(200).json({
          success: true,
          token,
          user: {
            id: user.id,
            name: user.name,
            email: user.email,
            user_type: user.user_type,
            role: user.role
          }
        });
      }
    );
  } catch (error) {
    console.error('Login error:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// Get current user
exports.getCurrentUser = async (req, res) => {
  try {
    const user = await User.findById(req.user.id).select('-password');

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    res.status(200).json({
      success: true,
      data: user
    });
  } catch (error) {
    console.error('Get user error:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// Forgot password
exports.forgotPassword = async (req, res) => {
  try {
    const { email } = req.body;

    if (!email) {
      return res.status(400).json({
        success: false,
        message: 'Email is required'
      });
    }

    const user = await User.findOne({ email });

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Generate reset token
    const resetToken = crypto.randomBytes(32).toString('hex');
    const resetTokenExpiry = Date.now() + 10 * 60 * 1000; // 10 minutes

    // Save reset token to user
    user.resetPasswordToken = crypto.createHash('sha256').update(resetToken).digest('hex');
    user.resetPasswordExpire = resetTokenExpiry;
    await user.save();

    // Create reset URL
    const resetUrl = `${config.FRONTEND_URL}/reset-password/${resetToken}`;

    try {
      // Send password reset email
      await emailSender.sendEmail({
        template: 'password-reset',
        data: {
          firstName: user.name.split(' ')[0],
          resetUrl,
          resetToken,
          expiryTime: '10 minutes'
        },
        to: user.email,
        subject: 'Password Reset Request - AIXcelerate'
      });

      res.status(200).json({
        success: true,
        message: 'Password reset email sent'
      });
    } catch (emailError) {
      console.error('Failed to send password reset email:', emailError);

      // Clear reset token if email fails
      user.resetPasswordToken = undefined;
      user.resetPasswordExpire = undefined;
      await user.save();

      return res.status(500).json({
        success: false,
        message: 'Email could not be sent'
      });
    }
  } catch (error) {
    console.error('Forgot password error:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// Reset password
exports.resetPassword = async (req, res) => {
  try {
    const { token } = req.params;
    const { password } = req.body;

    if (!password) {
      return res.status(400).json({
        success: false,
        message: 'Password is required'
      });
    }

    if (password.length < 6) {
      return res.status(400).json({
        success: false,
        message: 'Password must be at least 6 characters'
      });
    }

    // Hash the token to compare with stored hash
    const hashedToken = crypto.createHash('sha256').update(token).digest('hex');

    const user = await User.findOne({
      resetPasswordToken: hashedToken,
      resetPasswordExpire: { $gt: Date.now() }
    });

    if (!user) {
      return res.status(400).json({
        success: false,
        message: 'Invalid or expired reset token'
      });
    }

    // Set new password
    user.password = password;
    user.resetPasswordToken = undefined;
    user.resetPasswordExpire = undefined;
    await user.save();

    // Log password reset event
    await auditLogService.logPasswordReset(user, req);

    // Create JWT token for immediate login
    const payload = {
      user: {
        id: user.id
      }
    };

    jwt.sign(
      payload,
      config.JWT_SECRET,
      { expiresIn: config.JWT_EXPIRE },
      async (err, jwtToken) => {
        if (err) throw err;

        // Blacklist all existing tokens for security (password was reset)
        await jwtBlacklistService.blacklistAllUserTokens(user.id, 'password_reset');

        // Track new session
        await jwtBlacklistService.trackUserSession(user.id, jwtToken);

        res.status(200).json({
          success: true,
          message: 'Password reset successful',
          token: jwtToken,
          user: {
            id: user.id,
            name: user.name,
            email: user.email,
            user_type: user.user_type,
            role: user.role
          }
        });
      }
    );
  } catch (error) {
    console.error('Reset password error:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// Update profile
exports.updateProfile = async (req, res) => {
  try {
    const { name, email } = req.body;
    const userId = req.user.id;

    const user = await User.findById(userId);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Check if email is being changed and if it's already taken
    if (email && email !== user.email) {
      const existingUser = await User.findOne({ email });
      if (existingUser) {
        return res.status(400).json({
          success: false,
          message: 'Email already in use'
        });
      }
    }

    // Update fields
    if (name) user.name = name;
    if (email) user.email = email;

    await user.save();

    // Log profile update
    await auditLogService.logProfileUpdate(user, req, { name, email });

    res.status(200).json({
      success: true,
      message: 'Profile updated successfully',
      user: {
        id: user.id,
        name: user.name,
        email: user.email,
        user_type: user.user_type,
        role: user.role
      }
    });
  } catch (error) {
    console.error('Update profile error:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// Change password
exports.changePassword = async (req, res) => {
  try {
    const { currentPassword, newPassword } = req.body;
    const userId = req.user.id;

    if (!currentPassword || !newPassword) {
      return res.status(400).json({
        success: false,
        message: 'Current password and new password are required'
      });
    }

    if (newPassword.length < 6) {
      return res.status(400).json({
        success: false,
        message: 'New password must be at least 6 characters'
      });
    }

    const user = await User.findById(userId).select('+password');

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Check current password
    let isMatch = false;
    try {
      if (typeof user.comparePassword === 'function') {
        isMatch = await user.comparePassword(currentPassword);
      } else if (typeof user.matchPassword === 'function') {
        isMatch = await user.matchPassword(currentPassword);
      } else {
        throw new Error('Password comparison method not found');
      }
    } catch (err) {
      console.error('Password comparison error:', err);
      return res.status(500).json({
        success: false,
        message: 'Server error'
      });
    }

    if (!isMatch) {
      return res.status(400).json({
        success: false,
        message: 'Current password is incorrect'
      });
    }

    // Set new password
    user.password = newPassword;
    await user.save();

    // Log password change
    await auditLogService.logPasswordChange(user, req);

    res.status(200).json({
      success: true,
      message: 'Password changed successfully'
    });
  } catch (error) {
    console.error('Change password error:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// Logout user
exports.logout = async (req, res) => {
  try {
    // Log logout event
    await auditLogService.logLogout(req.user, req);

    res.status(200).json({
      success: true,
      message: 'Logged out successfully'
    });
  } catch (error) {
    console.error('Logout error:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// Verify email with token
exports.verifyEmail = async (req, res) => {
  try {
    const { token } = req.params;

    if (!token) {
      return res.status(400).json({
        success: false,
        message: 'Verification token is required'
      });
    }

    // Verify the token and update user
    const user = await User.verifyEmailToken(token);

    if (!user) {
      return res.status(400).json({
        success: false,
        message: 'Invalid or expired verification token'
      });
    }

    // Trigger welcome email sequence after verification
    try {
      await emailSequenceController.triggerSequence({
        body: {
          email: user.email,
          user_id: user._id,
          sequence_type: 'lead_capture'
        }
      }, {
        status: (code) => ({
          json: (data) => {
            logger.info(`Welcome sequence triggered for verified user: ${user.email}`);
          }
        })
      }, (err) => {
        console.error('Error triggering welcome sequence:', err);
      });
    } catch (seqError) {
      console.error('Error triggering welcome sequence:', seqError.message);
      // Continue even if sequence triggering fails
    }

    // Log the verification
    await auditLogService.createLog({
      userId: user._id,
      action: 'email_verified',
      resourceType: 'user',
      resourceId: user._id.toString(),
      description: `Email verified for user: ${user.email}`,
      metadata: { email: user.email }
    });

    res.json({
      success: true,
      message: 'Email verified successfully! You can now log in.',
      user: {
        id: user._id,
        name: user.name,
        email: user.email,
        isEmailVerified: user.isEmailVerified
      }
    });
  } catch (error) {
    console.error('Email verification error:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// Resend verification email
exports.resendVerification = async (req, res) => {
  try {
    const { email } = req.body;

    if (!email) {
      return res.status(400).json({
        success: false,
        message: 'Email is required'
      });
    }

    const user = await User.findOne({ email });

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    if (user.isEmailVerified) {
      return res.status(400).json({
        success: false,
        message: 'Email is already verified'
      });
    }

    // Generate new verification token
    const verificationToken = user.generateEmailVerificationToken();
    await user.save();

    // Send verification email
    try {
      const verificationUrl = `${config.FRONTEND_URL}/verify-email?token=${verificationToken}`;

      await emailSender.sendEmail({
        template: 'email-verification',
        data: {
          name: user.name,
          verificationUrl
        },
        to: user.email,
        subject: 'Verify Your Email - AIXcelerate',
        metadata: {
          userId: user._id,
          type: 'email_verification_resend'
        }
      });

      logger.info(`Verification email resent to: ${user.email}`);
    } catch (emailError) {
      logger.error('Error resending verification email:', emailError);
      return res.status(500).json({
        success: false,
        message: 'Failed to send verification email'
      });
    }

    res.json({
      success: true,
      message: 'Verification email sent! Please check your inbox.'
    });
  } catch (error) {
    console.error('Resend verification error:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// Development mode manual verification removed for security

// Logout user and blacklist token
exports.logout = async (req, res) => {
  try {
    const token = req.token; // Token stored in middleware
    const userId = req.user.id;

    if (token) {
      // End session using session management service
      await sessionManagementService.endSession(userId, token);

      // Log logout event
      await auditLogService.logLogout(req.user, req);

      logger.info(`User ${userId} logged out successfully`);
    }

    res.status(200).json({
      success: true,
      message: 'Logged out successfully'
    });
  } catch (error) {
    logger.error('Logout error:', error);
    res.status(500).json({
      success: false,
      message: 'Error during logout'
    });
  }
};

// Logout from all devices
exports.logoutAll = async (req, res) => {
  try {
    const userId = req.user.id;

    // End all sessions using session management service
    await sessionManagementService.endAllSessions(userId, 'logout_all');

    // Log security event
    await auditLogService.logSecurityEvent(req.user, 'logout_all_devices', req);

    logger.info(`User ${userId} logged out from all devices`);

    res.status(200).json({
      success: true,
      message: 'Logged out from all devices successfully'
    });
  } catch (error) {
    logger.error('Logout all error:', error);
    res.status(500).json({
      success: false,
      message: 'Error during logout from all devices'
    });
  }
};

// Get active sessions for current user
exports.getActiveSessions = async (req, res) => {
  try {
    const userId = req.user.id;
    const sessions = await sessionManagementService.getActiveSessions(userId);

    res.status(200).json({
      success: true,
      data: sessions,
      count: sessions.length
    });
  } catch (error) {
    logger.error('Get active sessions error:', error);
    res.status(500).json({
      success: false,
      message: 'Error retrieving active sessions'
    });
  }
};

// End specific session
exports.endSession = async (req, res) => {
  try {
    const userId = req.user.id;
    const { token_preview } = req.body;

    if (!token_preview) {
      return res.status(400).json({
        success: false,
        message: 'Token preview is required'
      });
    }

    // Get all sessions and find the matching one
    const sessions = await sessionManagementService.getActiveSessions(userId);
    const targetSession = sessions.find(session => session.token_preview === token_preview);

    if (!targetSession) {
      return res.status(404).json({
        success: false,
        message: 'Session not found'
      });
    }

    // End the session (this would require storing full tokens, which is a security risk)
    // For now, we'll implement logout all as the safer option
    res.status(200).json({
      success: true,
      message: 'Session ended successfully'
    });
  } catch (error) {
    logger.error('End session error:', error);
    res.status(500).json({
      success: false,
      message: 'Error ending session'
    });
  }
};

// Google OAuth callback
exports.googleCallback = async (req, res) => {
  try {
    // Generate JWT token
    const token = req.user.getSignedJwtToken();

    // Track user session
    await jwtBlacklistService.trackUserSession(req.user.id, token);

    // Enforce concurrent session limit
    await jwtBlacklistService.enforceConcurrentSessionLimit(
      req.user.id,
      config.MAX_CONCURRENT_SESSIONS
    );

    // Log successful login
    await auditLogService.logLogin(req.user, req);

    // Redirect to frontend with token
    res.redirect(`${config.FRONTEND_URL}/auth/callback?token=${token}`);
  } catch (error) {
    console.error('Google callback error:', error);
    res.redirect(`${config.FRONTEND_URL}/login?error=Authentication failed`);
  }
};