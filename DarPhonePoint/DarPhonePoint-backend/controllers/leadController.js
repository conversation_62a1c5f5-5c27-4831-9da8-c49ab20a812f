const Lead = require('../models/Lead');
const Product = require('../models/Product');
const ApiResponse = require('../utils/ApiResponse');
const auditLogService = require('../services/auditLogService');
const { sendWelcomeEmail, deliverLeadMagnet, deliverFreeProductAttachment } = require('../utils/email');
const config = require('../config/config');
const emailSequenceController = require('./emailSequenceController');

// Capture new lead
exports.captureLead = async (req, res) => {
  try {
    const { email, name, source } = req.body;

    // Validate email
    if (!email || !email.includes('@')) {
      return res.status(400).json({
        success: false,
        message: 'Valid email is required'
      });
    }

    // Check if lead already exists
    let lead = await Lead.findOne({ email });

    if (lead) {
      // If lead exists but hasn't received welcome email, send it
      if (!lead.welcomeEmailSent) {
        await sendWelcomeEmail(email, name || lead.name);
        await deliverLeadMagnet(email, name || lead.name);

        lead.welcomeEmailSent = true;
        lead.leadMagnetDelivered = true;
        await lead.save();
      }

      return res.status(200).json({
        success: true,
        message: 'Lead already exists, welcome email resent if needed'
      });
    }

    // Create new lead
    lead = new Lead({
      email,
      name: name || email.split('@')[0],
      source: source || 'landing_page'
    });

    await lead.save();

    // Log lead capture
    await auditLogService.logLeadCapture(
      email,
      lead._id.toString(),
      source || 'website',
      lead.toObject(),
      req,
      {
        lead_magnet: '50-essential-ai-prompts'
      }
    );

    // Send welcome email with lead magnet
    await sendWelcomeEmail(email, lead.name);
    await deliverLeadMagnet(email, lead.name);

    // Trigger welcome email sequence
    try {
      // Call the email sequence trigger endpoint directly
      await emailSequenceController.triggerSequence({
        body: {
          email: lead.email,
          lead_id: lead._id,
          sequence_type: 'lead_capture'
        }
      }, {
        status: (code) => ({
          json: (data) => {
            console.log(`Welcome sequence triggered for lead: ${lead.email}`);
          }
        })
      }, (err) => {
        console.error('Error triggering welcome sequence:', err);
      });
    } catch (seqError) {
      console.error('Error triggering welcome sequence:', seqError.message);
      // Continue with lead capture even if sequence triggering fails
    }

    // Update lead record
    lead.welcomeEmailSent = true;
    lead.leadMagnetDelivered = true;
    await lead.save();

    return res.apiSuccess(null, 'Lead captured successfully', {}, 201);
  } catch (error) {
    console.error('Lead capture error:', error);
    return res.apiError('Failed to capture lead', 500);
  }
};

// Get all leads (admin only)
exports.getLeads = async (req, res) => {
  try {
    const leads = await Lead.find().sort({ created_at: -1 });

    return res.apiSuccess(leads, 'Leads retrieved successfully', { count: leads.length });
  } catch (error) {
    console.error('Get leads error:', error);
    return res.apiError('Failed to retrieve leads', 500);
  }
};

// Request free product delivery
exports.requestFreeProduct = async (req, res) => {
  try {
    const { email, name, productId, source } = req.body;

    // Validate email
    if (!email || !email.includes('@')) {
      return res.status(400).json({
        success: false,
        message: 'Valid email is required'
      });
    }

    // Validate product ID
    if (!productId) {
      return res.status(400).json({
        success: false,
        message: 'Product ID is required'
      });
    }

    // Get product details
    const product = await Product.findById(productId);
    if (!product) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }

    // Check if product is free
    if (product.product_type !== 'free' && product.product_type !== 'lead_magnet' && product.price > 0) {
      return res.status(400).json({
        success: false,
        message: 'This product is not available for free delivery'
      });
    }

    // Check if lead already exists
    let lead = await Lead.findOne({ email });

    if (!lead) {
      // Create new lead
      lead = new Lead({
        email,
        name: name || email.split('@')[0],
        source: source || 'free_product_request'
      });
      await lead.save();
    }

    // Log the free product request
    await auditLogService.logLeadCapture(
      email,
      lead._id.toString(),
      source || 'free_product_request',
      { ...lead.toObject(), requested_product: product.name },
      req,
      {
        product_id: product._id,
        product_name: product.name,
        product_type: product.product_type,
        delivery_method: 'email_attachment'
      }
    );

    // Deliver the free product as email attachment
    const deliverySuccess = await deliverFreeProductAttachment(email, lead.name, product);

    if (!deliverySuccess) {
      return res.status(500).json({
        success: false,
        message: 'Failed to deliver the free product. Please try again.'
      });
    }

    return res.status(200).json({
      success: true,
      message: `Free ${product.name} has been sent to your email as an attachment!`
    });
  } catch (error) {
    console.error('Request free product error:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};