const Settings = require('../models/Settings');
const AppError = require('../utils/AppError');
const { catchAsync } = require('../utils/errorHandlers');
const logger = require('../utils/logger');

/**
 * Get all settings or settings by category
 * @route GET /api/settings
 * @access Admin
 */
exports.getSettings = catchAsync(async (req, res, next) => {
  const { category } = req.query;

  let settings;

  if (category) {
    // Get settings for specific category
    settings = await Settings.getByCategory(category);
  } else {
    // Get all settings grouped by category
    settings = await Settings.getAllGrouped();
  }

  res.status(200).json({
    success: true,
    data: settings
  });
});

/**
 * Get public settings (accessible to all users)
 * @route GET /api/settings/public
 * @access Public
 */
exports.getPublicSettings = catchAsync(async (req, res, next) => {
  const settings = await Settings.getPublicSettings();

  res.status(200).json({
    success: true,
    data: settings
  });
});

/**
 * Update settings
 * @route PATCH /api/settings
 * @access Admin
 */
exports.updateSettings = catchAsync(async (req, res, next) => {
  const { category } = req.query;
  const settingsData = req.body;

  if (!category) {
    return next(new AppError('Category is required', 400));
  }

  if (!settingsData || typeof settingsData !== 'object') {
    return next(new AppError('Settings data is required', 400));
  }

  const updatedSettings = {};

  // Update each setting in the category
  for (const [key, value] of Object.entries(settingsData)) {
    try {
      // Determine the type of the value
      let type = 'string';
      if (typeof value === 'number') type = 'number';
      else if (typeof value === 'boolean') type = 'boolean';
      else if (Array.isArray(value)) type = 'array';
      else if (typeof value === 'object' && value !== null) type = 'object';

      // Determine if the setting should be encrypted (passwords, keys, etc.)
      const isEncrypted = key.toLowerCase().includes('password') ||
        key.toLowerCase().includes('secret') ||
        key.toLowerCase().includes('key');

      const setting = await Settings.setSetting(category, key, value, {
        type,
        isEncrypted,
        modifiedBy: req.user.id
      });

      updatedSettings[key] = setting.value;
    } catch (error) {
      logger.error(`Error updating setting ${category}.${key}:`, error);
      return next(new AppError(`Failed to update setting ${key}`, 500));
    }
  }

  logger.info(`Settings updated for category ${category} by user ${req.user.id}`);

  res.status(200).json({
    success: true,
    message: 'Settings updated successfully',
    data: updatedSettings
  });
});

/**
 * Update a single setting
 * @route PUT /api/settings/:category/:key
 * @access Admin
 */
exports.updateSetting = catchAsync(async (req, res, next) => {
  const { category, key } = req.params;
  const { value, type, description, isPublic, isEncrypted } = req.body;

  if (value === undefined) {
    return next(new AppError('Value is required', 400));
  }

  const setting = await Settings.setSetting(category, key, value, {
    type: type || 'string',
    description: description || '',
    isPublic: isPublic || false,
    isEncrypted: isEncrypted || false,
    modifiedBy: req.user.id
  });

  logger.info(`Setting ${category}.${key} updated by user ${req.user.id}`);

  res.status(200).json({
    success: true,
    message: 'Setting updated successfully',
    data: setting
  });
});

/**
 * Delete a setting
 * @route DELETE /api/settings/:category/:key
 * @access Admin
 */
exports.deleteSetting = catchAsync(async (req, res, next) => {
  const { category, key } = req.params;

  const setting = await Settings.findOneAndDelete({ category, key });

  if (!setting) {
    return next(new AppError('Setting not found', 404));
  }

  logger.info(`Setting ${category}.${key} deleted by user ${req.user.id}`);

  res.status(200).json({
    success: true,
    message: 'Setting deleted successfully'
  });
});

/**
 * Reset settings to default values
 * @route POST /api/settings/reset
 * @access Admin
 */
exports.resetSettings = catchAsync(async (req, res, next) => {
  const { category } = req.body;

  if (category) {
    // Reset specific category
    await Settings.deleteMany({ category });
    await initializeDefaultSettings(category);
  } else {
    // Reset all settings
    await Settings.deleteMany({});
    await initializeAllDefaultSettings();
  }

  logger.info(`Settings reset ${category ? `for category ${category}` : 'for all categories'} by user ${req.user.id}`);

  res.status(200).json({
    success: true,
    message: 'Settings reset to default values'
  });
});

/**
 * Initialize default settings for a category
 */
const initializeDefaultSettings = async (category) => {
  const defaults = getDefaultSettings();

  if (defaults[category]) {
    for (const [key, config] of Object.entries(defaults[category])) {
      await Settings.setSetting(category, key, config.value, {
        type: config.type || 'string',
        description: config.description || '',
        isPublic: config.isPublic || false,
        isEncrypted: config.isEncrypted || false
      });
    }
  }
};

/**
 * Initialize all default settings
 */
const initializeAllDefaultSettings = async () => {
  const defaults = getDefaultSettings();

  for (const category of Object.keys(defaults)) {
    await initializeDefaultSettings(category);
  }
};

/**
 * Get default settings configuration based on environment variables
 */
const getDefaultSettings = () => ({
  general: {
    siteName: { value: 'AIXcelerate', type: 'string', description: 'Site name', isPublic: true },
    siteDescription: { value: 'AI Productivity Tools for Business', type: 'string', description: 'Site description', isPublic: true },
    contactEmail: { value: process.env.EMAIL_FROM || '<EMAIL>', type: 'string', description: 'Contact email', isPublic: true },
    supportPhone: { value: '', type: 'string', description: 'Support phone number', isPublic: true },
    logoUrl: { value: '', type: 'string', description: 'Logo URL', isPublic: true },
    faviconUrl: { value: '', type: 'string', description: 'Favicon URL', isPublic: true },
    frontendUrl: { value: process.env.FRONTEND_URL || 'http://localhost:5173', type: 'string', description: 'Frontend URL', isPublic: true },
    baseUrl: { value: process.env.BASE_URL || 'http://localhost:5001', type: 'string', description: 'Backend base URL', isPublic: true }
  },
  email: {
    smtpHost: { value: process.env.EMAIL_HOST || 'smtp.gmail.com', type: 'string', description: 'SMTP host' },
    smtpPort: { value: parseInt(process.env.EMAIL_PORT) || 587, type: 'number', description: 'SMTP port' },
    smtpSecure: { value: process.env.EMAIL_SECURE === 'true', type: 'boolean', description: 'Use secure connection (TLS)' },
    smtpUsername: { value: process.env.EMAIL_USER || '', type: 'string', description: 'SMTP username' },
    smtpPassword: { value: process.env.EMAIL_PASSWORD || '', type: 'string', description: 'SMTP password', isEncrypted: true },
    fromEmail: { value: process.env.EMAIL_FROM || '<EMAIL>', type: 'string', description: 'From email address' },
    fromName: { value: 'AIXcelerate', type: 'string', description: 'From name' },
    enableEmailVerification: { value: true, type: 'boolean', description: 'Enable email verification' }
  },
  payment: {
    stripePublicKey: { value: process.env.STRIPE_PUBLISHABLE_KEY || '', type: 'string', description: 'Stripe public key', isPublic: true },
    stripeSecretKey: { value: process.env.STRIPE_SECRET_KEY || '', type: 'string', description: 'Stripe secret key', isEncrypted: true },
    stripeWebhookSecret: { value: process.env.STRIPE_WEBHOOK_SECRET || '', type: 'string', description: 'Stripe webhook secret', isEncrypted: true },
    currency: { value: 'USD', type: 'string', description: 'Default currency', isPublic: true },
    enableTestMode: { value: process.env.NODE_ENV !== 'production', type: 'boolean', description: 'Enable test mode' }
  },
  storage: {
    storageType: { value: 'local', type: 'string', description: 'Storage type (local, s3)' },
    uploadPath: { value: process.env.UPLOAD_PATH || './uploads', type: 'string', description: 'Local upload path' },
    maxFileSize: { value: parseInt(process.env.MAX_FILE_SIZE) || 10485760, type: 'number', description: 'Maximum file size in bytes' },
    allowedFileTypes: { value: process.env.ALLOWED_FILE_TYPES || 'pdf,doc,docx,txt,png,jpg,jpeg', type: 'string', description: 'Allowed file types (comma-separated)' },
    s3AccessKey: { value: '', type: 'string', description: 'S3 access key', isEncrypted: true },
    s3SecretKey: { value: '', type: 'string', description: 'S3 secret key', isEncrypted: true },
    s3Bucket: { value: '', type: 'string', description: 'S3 bucket name' },
    s3Region: { value: 'us-east-1', type: 'string', description: 'S3 region' }
  },
  security: {
    jwtSecret: { value: process.env.JWT_SECRET || '', type: 'string', description: 'JWT secret key', isEncrypted: true },
    jwtExpire: { value: process.env.JWT_EXPIRE || '24h', type: 'string', description: 'JWT expiration time' },
    bcryptRounds: { value: parseInt(process.env.BCRYPT_ROUNDS) || 12, type: 'number', description: 'Bcrypt hash rounds' },
    sessionSecret: { value: process.env.SESSION_SECRET || '', type: 'string', description: 'Session secret key', isEncrypted: true },
    corsOrigin: { value: process.env.CORS_ORIGIN || 'http://localhost:5173', type: 'string', description: 'CORS allowed origins' },
    trustProxy: { value: process.env.TRUST_PROXY === 'true', type: 'boolean', description: 'Trust proxy headers' },
    secureCookies: { value: process.env.SECURE_COOKIES === 'true', type: 'boolean', description: 'Use secure cookies' },
    rateLimitWindowMs: { value: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 900000, type: 'number', description: 'Rate limit window in milliseconds' },
    rateLimitMax: { value: parseInt(process.env.RATE_LIMIT_MAX) || 100, type: 'number', description: 'Maximum requests per window' }
  }
});

/**
 * Initialize default settings on first run
 * @route POST /api/settings/initialize
 * @access Admin
 */
exports.initializeSettings = catchAsync(async (req, res, next) => {
  const existingSettings = await Settings.countDocuments();

  if (existingSettings > 0) {
    return next(new AppError('Settings already initialized', 400));
  }

  await initializeAllDefaultSettings();

  logger.info(`Default settings initialized by user ${req.user.id}`);

  res.status(201).json({
    success: true,
    message: 'Default settings initialized successfully'
  });
});

module.exports = {
  getSettings: exports.getSettings,
  getPublicSettings: exports.getPublicSettings,
  updateSettings: exports.updateSettings,
  updateSetting: exports.updateSetting,
  deleteSetting: exports.deleteSetting,
  resetSettings: exports.resetSettings,
  initializeSettings: exports.initializeSettings
};
