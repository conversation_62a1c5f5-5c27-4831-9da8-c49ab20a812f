const monitoringService = require('../services/monitoringService');
const logger = require('../services/errorLoggingService').logger;
const ApiResponse = require('../utils/ApiResponse');

// Get current metrics
exports.getMetrics = async (req, res) => {
  try {
    const metrics = monitoringService.getMetrics();
    return res.apiSuccess(metrics, 'Metrics retrieved successfully');
  } catch (error) {
    logger.error('Error getting metrics:', error);
    return res.apiError('Error retrieving metrics', 500);
  }
};

// Get performance report
exports.getPerformanceReport = async (req, res) => {
  try {
    const report = monitoringService.getPerformanceReport();
    res.json({
      success: true,
      data: report
    });
  } catch (error) {
    logger.error('Error getting performance report:', error);
    res.status(500).json({
      success: false,
      error: 'Error retrieving performance report'
    });
  }
};

// Reset metrics
exports.resetMetrics = async (req, res) => {
  try {
    monitoringService.resetMetrics();
    res.json({
      success: true,
      message: 'Metrics reset successfully'
    });
  } catch (error) {
    logger.error('Error resetting metrics:', error);
    res.status(500).json({
      success: false,
      error: 'Error resetting metrics'
    });
  }
}; 