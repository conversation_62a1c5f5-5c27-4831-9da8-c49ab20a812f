/**
 * Unsubscribe Controller
 * Handles unsubscribe requests from email sequences
 */

const EmailSequenceTracking = require('../models/EmailSequenceTracking');
const User = require('../models/User');
const Lead = require('../models/Lead');
const AppError = require('../utils/AppError');
const logger = require('../utils/logger');
const crypto = require('crypto');

/**
 * Generate unsubscribe token
 * @param {String} email - User email
 * @param {String} sequenceId - Email sequence ID
 * @returns {String} Unsubscribe token
 */
exports.generateUnsubscribeToken = (email, sequenceId) => {
  const data = `${email}:${sequenceId}:${process.env.UNSUBSCRIBE_SECRET || 'aixcelerate-secret'}`;
  return crypto.createHash('sha256').update(data).digest('hex');
};

/**
 * Verify unsubscribe token
 * @param {String} token - Unsubscribe token
 * @param {String} email - User email
 * @param {String} sequenceId - Email sequence ID
 * @returns {Boolean} Is token valid
 */
exports.verifyUnsubscribeToken = (token, email, sequenceId) => {
  const expectedToken = this.generateUnsubscribeToken(email, sequenceId);
  return token === expectedToken;
};

/**
 * Unsubscribe from email sequence
 * @route GET /api/unsubscribe/:token
 * @access Public
 */
exports.unsubscribe = async (req, res, next) => {
  try {
    const { token } = req.params;
    const { email, sequence_id } = req.query;

    if (!token || !email || !sequence_id) {
      return next(new AppError('Invalid unsubscribe link', 400));
    }

    // Verify token
    if (!this.verifyUnsubscribeToken(token, email, sequence_id)) {
      return next(new AppError('Invalid unsubscribe token', 400));
    }

    // Find tracking record
    const tracking = await EmailSequenceTracking.findOne({
      email,
      email_sequence: sequence_id,
      unsubscribed: false
    });

    if (!tracking) {
      return res.status(200).json({
        success: true,
        message: 'You are already unsubscribed from this email sequence.'
      });
    }

    // Update tracking record
    tracking.unsubscribed = true;
    tracking.unsubscribed_at = new Date();
    await tracking.save();

    // Render unsubscribe page
    res.status(200).json({
      success: true,
      message: 'You have been unsubscribed from this email sequence.',
      data: {
        email,
        sequence_id
      }
    });
  } catch (error) {
    logger.error('Error unsubscribing:', error);
    next(new AppError(`Error unsubscribing: ${error.message}`, 500));
  }
};

/**
 * Submit unsubscribe feedback
 * @route POST /api/unsubscribe/feedback
 * @access Public
 */
exports.submitFeedback = async (req, res, next) => {
  try {
    const { email, sequence_id, reason, feedback } = req.body;

    if (!email || !sequence_id) {
      return next(new AppError('Email and sequence ID are required', 400));
    }

    // Find tracking record
    const tracking = await EmailSequenceTracking.findOne({
      email,
      email_sequence: sequence_id,
      unsubscribed: true
    });

    if (!tracking) {
      return next(new AppError('Unsubscribe record not found', 404));
    }

    // Update tracking record with feedback
    tracking.unsubscribe_reason = reason || 'other';
    tracking.unsubscribe_feedback = feedback;
    await tracking.save();

    res.status(200).json({
      success: true,
      message: 'Thank you for your feedback.'
    });
  } catch (error) {
    logger.error('Error submitting unsubscribe feedback:', error);
    next(new AppError(`Error submitting feedback: ${error.message}`, 500));
  }
};

/**
 * Unsubscribe from all email sequences
 * @route POST /api/unsubscribe/all
 * @access Public
 */
exports.unsubscribeAll = async (req, res, next) => {
  try {
    const { email, token } = req.body;

    if (!email || !token) {
      return next(new AppError('Email and token are required', 400));
    }

    // Verify token
    const globalToken = this.generateUnsubscribeToken(email, 'all');
    if (token !== globalToken) {
      return next(new AppError('Invalid token', 400));
    }

    // Update all tracking records
    const result = await EmailSequenceTracking.updateMany(
      { email, unsubscribed: false },
      { 
        unsubscribed: true,
        unsubscribed_at: new Date(),
        unsubscribe_reason: 'not_interested'
      }
    );

    // Update user preferences if exists
    const user = await User.findOne({ email });
    if (user) {
      user.email_preferences = user.email_preferences || {};
      user.email_preferences.marketing_emails = false;
      user.email_preferences.promotional_emails = false;
      await user.save();
    }

    // Update lead preferences if exists
    const lead = await Lead.findOne({ email });
    if (lead) {
      lead.unsubscribed = true;
      lead.unsubscribed_at = new Date();
      await lead.save();
    }

    res.status(200).json({
      success: true,
      message: 'You have been unsubscribed from all email sequences.',
      count: result.nModified
    });
  } catch (error) {
    logger.error('Error unsubscribing from all sequences:', error);
    next(new AppError(`Error unsubscribing: ${error.message}`, 500));
  }
};
