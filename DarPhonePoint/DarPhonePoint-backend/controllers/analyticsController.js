const Analytics = require('../models/Analytics');
const AppError = require('../utils/AppError');
const ApiResponse = require('../utils/ApiResponse');
const mongoose = require('mongoose');
const Order = require('../models/Order');
const User = require('../models/User');
const Lead = require('../models/Lead');
const Product = require('../models/Product');
const EmailSequence = require('../models/EmailSequence');
const EmailSequenceTracking = require('../models/EmailSequenceTracking');

/**
 * Track an analytics event
 * @route POST /api/analytics/track
 * @access Public
 */
exports.trackEvent = async (req, res, next) => {
  try {
    const {
      event_type,
      session_id,
      page_url,
      product_id,
      lead_id,
      order_id,
      device_type,
      browser,
      referrer,
      metadata
    } = req.body;

    // Validate required fields
    if (!event_type || !session_id) {
      return next(new AppError('Event type and session ID are required', 400));
    }

    // Create analytics event
    const analytics = new Analytics({
      event_type,
      session_id,
      page_url,
      product_id,
      lead_id,
      order_id,
      device_type,
      browser,
      referrer,
      ip_address: req.ip,
      metadata: metadata || {},
      user_id: req.user ? req.user.id : null
    });

    await analytics.save();

    res.status(201).json({
      success: true,
      message: 'Event tracked successfully'
    });
  } catch (error) {
    next(new AppError(`Error tracking event: ${error.message}`, 500));
  }
};

/**
 * Get analytics dashboard data
 * @route GET /api/analytics/dashboard
 * @access Admin
 */
exports.getDashboardData = async (req, res, next) => {
  try {
    // Get date range from query params or default to last 30 days
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - (req.query.days || 30));

    // Get event counts by type
    const eventCounts = await Analytics.aggregate([
      {
        $match: {
          created_at: { $gte: startDate, $lte: endDate }
        }
      },
      {
        $group: {
          _id: '$event_type',
          count: { $sum: 1 }
        }
      },
      {
        $sort: {
          count: -1
        }
      }
    ]);

    // Get daily event counts
    const dailyEvents = await Analytics.aggregate([
      {
        $match: {
          created_at: { $gte: startDate, $lte: endDate }
        }
      },
      {
        $group: {
          _id: {
            date: { $dateToString: { format: '%Y-%m-%d', date: '$created_at' } },
            event_type: '$event_type'
          },
          count: { $sum: 1 }
        }
      },
      {
        $sort: {
          '_id.date': 1
        }
      }
    ]);

    // Get conversion rates
    const pageViews = await Analytics.countDocuments({
      event_type: 'page_view',
      created_at: { $gte: startDate, $lte: endDate }
    });

    const leadCaptures = await Analytics.countDocuments({
      event_type: 'lead_capture',
      created_at: { $gte: startDate, $lte: endDate }
    });

    const purchases = await Analytics.countDocuments({
      event_type: 'purchase',
      created_at: { $gte: startDate, $lte: endDate }
    });

    // Calculate conversion rates
    const leadConversionRate = pageViews > 0 ? (leadCaptures / pageViews) * 100 : 0;
    const purchaseConversionRate = leadCaptures > 0 ? (purchases / leadCaptures) * 100 : 0;

    res.status(200).json({
      success: true,
      data: {
        eventCounts,
        dailyEvents,
        conversionRates: {
          leadConversionRate: leadConversionRate.toFixed(2),
          purchaseConversionRate: purchaseConversionRate.toFixed(2)
        },
        totals: {
          pageViews,
          leadCaptures,
          purchases
        }
      }
    });
  } catch (error) {
    next(new AppError(`Error getting dashboard data: ${error.message}`, 500));
  }
};

/**
 * Get dashboard analytics stats
 * @route GET /api/analytics/dashboard-stats
 * @access Admin
 */
exports.getDashboardStats = async (req, res, next) => {
  try {
    const { timeRange = '30d' } = req.query;

    // Calculate date range
    const now = new Date();
    let startDate;

    switch (timeRange) {
      case '7d':
        // For 7d, use the current week (Sunday to Saturday)
        const currentDay = now.getDay(); // 0 = Sunday, 1 = Monday, etc.
        startDate = new Date(now);
        startDate.setDate(now.getDate() - currentDay);
        startDate.setHours(0, 0, 0, 0);
        break;
      case '90d':
        // For 90d, use the last 3 complete months
        startDate = new Date(now);
        startDate.setMonth(now.getMonth() - 2); // Go back 2 months to get 3 months total
        startDate.setDate(1); // Start from the 1st of that month
        startDate.setHours(0, 0, 0, 0);
        break;
      case '30d':
      default:
        // For 30d, use the last 4 complete weeks (28 days)
        startDate = new Date(now.getTime() - 27 * 24 * 60 * 60 * 1000); // 28 days ago
        break;
    }

    // Get total revenue
    const totalRevenue = await Order.aggregate([
      {
        $match: {
          payment_status: 'completed',
          created_at: { $gte: startDate }
        }
      },
      {
        $group: {
          _id: null,
          total: { $sum: '$amount' }
        }
      }
    ]);

    // Get total orders
    const totalOrders = await Order.countDocuments({
      created_at: { $gte: startDate }
    });

    // Get total users
    const totalUsers = await User.countDocuments({
      created_at: { $gte: startDate }
    });

    // Get total leads
    const totalLeads = await Lead.countDocuments({
      created_at: { $gte: startDate }
    });

    // Calculate conversion rate
    const conversionRate = totalLeads > 0
      ? ((totalOrders / totalLeads) * 100).toFixed(2)
      : 0;

    // Get revenue data over time - simplified approach
    const revenueData = await getSimpleTimeSeriesData(Order, 'amount', startDate, now, timeRange);

    // Get orders data over time - simplified approach
    const ordersData = await getSimpleTimeSeriesData(Order, 'count', startDate, now, timeRange);

    // Get users data over time - simplified approach
    const usersData = await getSimpleTimeSeriesData(User, 'count', startDate, now, timeRange);

    // Get leads data over time - simplified approach
    const leadsData = await getSimpleTimeSeriesData(Lead, 'count', startDate, now, timeRange);

    // Generate date labels
    const dateLabels = generateDateLabels(timeRange, now);

    // Get product performance
    const products = await Product.find();
    const productPerformance = [];

    for (const product of products) {
      // Convert product._id to ObjectId for proper comparison
      const productId = new mongoose.Types.ObjectId(product._id);

      const sales = await Order.countDocuments({
        product: productId,
        created_at: { $gte: startDate }
      });

      const revenue = await Order.aggregate([
        {
          $match: {
            product: productId,
            payment_status: 'completed',
            created_at: { $gte: startDate }
          }
        },
        {
          $group: {
            _id: null,
            total: { $sum: '$amount' }
          }
        }
      ]);

      // Calculate conversion rate
      const views = await Analytics.countDocuments({
        event_type: 'product_view',
        'metadata.product_id': product._id.toString(),
        created_at: { $gte: startDate }
      });

      // If there are sales but no recorded views, assume at least one view per sale
      const effectiveViews = views > 0 ? views : (sales > 0 ? sales : 1);
      const productConversionRate = ((sales / effectiveViews) * 100).toFixed(2);

      // Format the product data
      const productRevenue = revenue.length > 0 ? revenue[0].total : 0;

      productPerformance.push({
        id: product._id,
        name: product.name,
        sales,
        revenue: productRevenue,
        price: product.price,
        conversionRate: productConversionRate
      });
    }

    // Get traffic sources
    const trafficSources = await Analytics.aggregate([
      {
        $match: {
          event_type: 'page_view',
          created_at: { $gte: startDate }
        }
      },
      {
        $group: {
          _id: '$referrer',
          count: { $sum: 1 }
        }
      },
      {
        $sort: { count: -1 }
      },
      {
        $limit: 4
      }
    ]);

    // Default traffic sources if none found
    const defaultTrafficSources = [
      { _id: 'Direct', count: 40 },
      { _id: 'Organic Search', count: 30 },
      { _id: 'Social Media', count: 20 },
      { _id: 'Referral', count: 10 }
    ];

    const finalTrafficSources = trafficSources.length > 0 ? trafficSources : defaultTrafficSources;

    // Get device types
    const deviceTypes = await Analytics.aggregate([
      {
        $match: {
          event_type: 'page_view',
          created_at: { $gte: startDate }
        }
      },
      {
        $group: {
          _id: '$device_type',
          count: { $sum: 1 }
        }
      },
      {
        $sort: { count: -1 }
      }
    ]);

    // Default device types if none found
    const defaultDeviceTypes = [
      { _id: 'Mobile', count: 60 },
      { _id: 'Desktop', count: 30 },
      { _id: 'Tablet', count: 10 }
    ];

    const finalDeviceTypes = deviceTypes.length > 0 ? deviceTypes : defaultDeviceTypes;

    // Set no-cache headers to prevent response caching
    res.set({
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      'Pragma': 'no-cache',
      'Expires': '0'
    });

    const analyticsData = {
      summary: {
        totalRevenue: totalRevenue.length > 0 ? totalRevenue[0].total : 0,
        totalOrders,
        totalUsers,
        totalLeads,
        conversionRate
      },
      revenueData,
      ordersData,
      usersData,
      leadsData,
      productPerformance,
      trafficSources: {
        data: finalTrafficSources.map(s => s.count),
        labels: finalTrafficSources.map(s => s._id || 'Direct')
      },
      deviceTypes: {
        data: finalDeviceTypes.map(d => d.count),
        labels: finalDeviceTypes.map(d => d._id)
      },
      dateLabels
    };

    return res.apiSuccess(analyticsData, 'Analytics dashboard data retrieved successfully');
  } catch (error) {
    next(new AppError(`Error getting dashboard stats: ${error.message}`, 500));
  }
};

/**
 * Simplified time series data generation that actually works
 * @param {Model} Model - Mongoose model
 * @param {String} type - Type of data to get (count or field name)
 * @param {Date} startDate - Start date
 * @param {Date} endDate - End date
 * @param {String} timeRange - Time range (7d, 30d, 90d)
 * @returns {Array} Array of data points
 */
const getSimpleTimeSeriesData = async (Model, type, startDate, endDate, timeRange) => {
  try {
    // Get all data within the date range
    const matchQuery = { created_at: { $gte: startDate, $lte: endDate } };

    // For orders, always include payment_status filter
    if (Model.modelName === 'Order') {
      matchQuery.payment_status = 'completed';
    }

    const allData = await Model.find(matchQuery).sort({ created_at: 1 });

    // Determine number of periods and handle different time ranges
    let numPeriods;
    switch (timeRange) {
      case '7d':
        numPeriods = 7;
        break;
      case '90d':
        numPeriods = 3;
        break;
      case '30d':
      default:
        numPeriods = 4;
        break;
    }

    // Initialize result array
    const result = Array(numPeriods).fill(0);

    // Distribute data into periods based on time range
    allData.forEach((item) => {
      const itemDate = new Date(item.created_at);
      let periodIndex = 0;

      switch (timeRange) {
        case '7d':
          // For 7d, distribute by day of week (Sunday = 0, Monday = 1, etc.)
          const currentDay = new Date().getDay();
          const startOfWeek = new Date();
          startOfWeek.setDate(new Date().getDate() - currentDay);
          startOfWeek.setHours(0, 0, 0, 0);

          const daysDiff = Math.floor((itemDate.getTime() - startOfWeek.getTime()) / (24 * 60 * 60 * 1000));
          periodIndex = Math.max(0, Math.min(6, daysDiff));
          break;

        case '90d':
          // For 90d, distribute by month (0 = oldest month, 2 = current month)
          const currentMonth = new Date().getMonth();
          const currentYear = new Date().getFullYear();
          const itemMonth = itemDate.getMonth();
          const itemYear = itemDate.getFullYear();

          // Calculate month difference
          const monthDiff = (itemYear - currentYear) * 12 + (itemMonth - currentMonth);
          periodIndex = Math.max(0, Math.min(2, monthDiff + 2)); // +2 because we go back 2 months
          break;

        case '30d':
        default:
          // For 30d, distribute by week (0 = oldest week, 3 = current week)
          const weeksDiff = Math.floor((itemDate.getTime() - startDate.getTime()) / (7 * 24 * 60 * 60 * 1000));
          periodIndex = Math.max(0, Math.min(3, weeksDiff));
          break;
      }

      const value = type === 'count' ? 1 : (item[type] || 0);
      result[periodIndex] += value;
    });

    return result;

  } catch (error) {
    console.error('Error getting simple time series data:', error);
    const numPoints = timeRange === '7d' ? 7 : timeRange === '30d' ? 4 : 3;
    return Array(numPoints).fill(0);
  }
};

/**
 * Get time series data for a model (LEGACY - keeping for reference)
 * @param {Model} Model - Mongoose model
 * @param {String} type - Type of data to get (count or field name)
 * @param {Date} startDate - Start date
 * @param {Date} endDate - End date
 * @param {String} timeRange - Time range (7d, 30d, 90d)
 * @returns {Array} Array of data points
 */
const getTimeSeriesData = async (Model, type, startDate, endDate, timeRange) => {
  try {
    console.log(`Getting time series data for ${Model.modelName}, type: ${type}, range: ${timeRange}`);
    console.log(`Date range: ${startDate.toISOString()} to ${endDate.toISOString()}`);

    // For 30d, use daily aggregation instead of weekly for better granularity
    let groupBy, numPoints;

    switch (timeRange) {
      case '7d':
        groupBy = { $dateToString: { format: '%Y-%m-%d', date: '$created_at' } };
        numPoints = 7;
        break;
      case '90d':
        groupBy = { $dateToString: { format: '%Y-%m', date: '$created_at' } };
        numPoints = 3;
        break;
      case '30d':
      default:
        // Use daily aggregation for 30d, then group into weeks for display
        groupBy = { $dateToString: { format: '%Y-%m-%d', date: '$created_at' } };
        numPoints = 4; // 4 weeks
        break;
    }

    const pipeline = [
      {
        $match: {
          created_at: { $gte: startDate, $lte: endDate }
        }
      },
      {
        $group: {
          _id: groupBy,
          value: type === 'count' ? { $sum: 1 } : { $sum: `$${type}` }
        }
      },
      {
        $sort: { _id: 1 }
      }
    ];

    // If it's an Order model and we're getting amount, only include completed orders
    if (Model.modelName === 'Order' && type === 'amount') {
      pipeline[0].$match.payment_status = 'completed';
    }

    const results = await Model.aggregate(pipeline);
    console.log(`Raw aggregation results for ${Model.modelName}:`, results);

    // For 30d, group daily results into weekly buckets
    if (timeRange === '30d') {
      const weeklyData = [0, 0, 0, 0]; // 4 weeks

      results.forEach(result => {
        const date = new Date(result._id);
        const daysDiff = Math.floor((date - startDate) / (1000 * 60 * 60 * 24));
        const weekIndex = Math.floor(daysDiff / 7);

        if (weekIndex >= 0 && weekIndex < 4) {
          weeklyData[weekIndex] += result.value;
        }
      });

      console.log(`Weekly aggregated data for ${Model.modelName}:`, weeklyData);
      return weeklyData;
    }

    // For 7d and 90d, fill in missing data points
    const data = [];

    if (timeRange === '7d') {
      // Fill in daily data for last 7 days (ending today)
      for (let i = 6; i >= 0; i--) {
        const date = new Date(endDate.getTime() - i * 24 * 60 * 60 * 1000);
        const dateString = date.toISOString().split('T')[0];
        const result = results.find(r => r._id === dateString);
        data.push(result ? result.value : 0);
      }
    } else if (timeRange === '90d') {
      // Fill in monthly data for last 3 months
      for (let i = 0; i < 3; i++) {
        const date = new Date(startDate.getFullYear(), startDate.getMonth() + i, 1);
        const monthString = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
        const result = results.find(r => r._id === monthString);
        data.push(result ? result.value : 0);
      }
    }

    console.log(`Final processed data for ${Model.modelName}:`, data);
    return data;

  } catch (error) {
    console.error('Error getting time series data:', error);
    // Return array of zeros as fallback
    const numPoints = timeRange === '7d' ? 7 : timeRange === '30d' ? 4 : 3;
    return Array(numPoints).fill(0);
  }
};

/**
 * Get date string for a specific interval
 * @param {Date} startDate - Start date
 * @param {Number} index - Index of the interval
 * @param {String} interval - Interval type (day, week, month)
 * @param {String} format - Date format
 * @returns {String} Date string
 */
const getDateString = (startDate, index, interval, format) => {
  const date = new Date(startDate);

  switch (interval) {
    case 'day':
      date.setDate(date.getDate() + index);
      break;
    case 'week':
      date.setDate(date.getDate() + index * 7);
      break;
    case 'month':
      date.setMonth(date.getMonth() + index);
      break;
  }

  // Format the date
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const week = getWeekNumber(date);

  if (format === '%Y-%m-%d') {
    return `${year}-${month}-${day}`;
  } else if (format === '%Y-%U') {
    return `${year}-${week}`;
  } else if (format === '%Y-%m') {
    return `${year}-${month}`;
  }

  return '';
};

/**
 * Get week number for a date
 * @param {Date} date - Date
 * @returns {String} Week number
 */
const getWeekNumber = (date) => {
  const d = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate()));
  const dayNum = d.getUTCDay() || 7;
  d.setUTCDate(d.getUTCDate() + 4 - dayNum);
  const yearStart = new Date(Date.UTC(d.getUTCFullYear(), 0, 1));
  return String(Math.ceil((((d - yearStart) / 86400000) + 1) / 7)).padStart(2, '0');
};

/**
 * Generate date labels based on time range
 * @param {String} timeRange - Time range (7d, 30d, 90d)
 * @param {Date} endDate - End date (for 7d, this should be today)
 * @returns {Array} Array of date labels
 */
const generateDateLabels = (timeRange, endDate = new Date()) => {
  switch (timeRange) {
    case '7d':
      // Generate dates for the current week (Sunday to Saturday)
      const labels = [];
      const dayNames = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

      // Find the start of the current week (Sunday)
      const currentDay = endDate.getDay(); // 0 = Sunday, 1 = Monday, etc.
      const startOfWeek = new Date(endDate);
      startOfWeek.setDate(endDate.getDate() - currentDay);
      startOfWeek.setHours(0, 0, 0, 0);

      // Generate labels for the 7 days of the current week
      for (let i = 0; i < 7; i++) {
        const date = new Date(startOfWeek.getTime() + i * 24 * 60 * 60 * 1000);
        const dayName = dayNames[date.getDay()];
        const monthDay = `${date.getMonth() + 1}/${date.getDate()}`;
        labels.push(`${dayName} ${monthDay}`);
      }
      return labels;
    case '90d':
      // Generate labels for the last 3 months
      const monthLabels = [];
      const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

      for (let i = 2; i >= 0; i--) {
        const date = new Date(endDate);
        date.setMonth(date.getMonth() - i);
        const monthName = monthNames[date.getMonth()];
        const year = date.getFullYear();
        monthLabels.push(`${monthName} ${year}`);
      }
      return monthLabels;
    case '30d':
    default:
      // Generate labels for the last 4 weeks
      const weekLabels = [];

      for (let i = 3; i >= 0; i--) {
        const weekStart = new Date(endDate);
        weekStart.setDate(endDate.getDate() - (i * 7) - 6);
        const weekEnd = new Date(endDate);
        weekEnd.setDate(endDate.getDate() - (i * 7));

        const startMonth = weekStart.getMonth() + 1;
        const startDay = weekStart.getDate();
        const endMonth = weekEnd.getMonth() + 1;
        const endDay = weekEnd.getDate();

        if (startMonth === endMonth) {
          weekLabels.push(`${startMonth}/${startDay}-${endDay}`);
        } else {
          weekLabels.push(`${startMonth}/${startDay}-${endMonth}/${endDay}`);
        }
      }
      return weekLabels;
  }
};

/**
 * Get user journey data
 * @route GET /api/analytics/user-journey/:sessionId
 * @access Admin
 */
exports.getUserJourney = async (req, res, next) => {
  try {
    const { sessionId } = req.params;

    if (!sessionId) {
      return next(new AppError('Session ID is required', 400));
    }

    const events = await Analytics.find({ session_id: sessionId })
      .sort({ created_at: 1 })
      .populate('product_id', 'name slug')
      .populate('lead_id', 'email')
      .populate('order_id');

    if (!events.length) {
      return next(new AppError('No events found for this session', 404));
    }

    res.status(200).json({
      success: true,
      count: events.length,
      data: events
    });
  } catch (error) {
    next(new AppError(`Error getting user journey: ${error.message}`, 500));
  }
};

/**
 * Get weekly activity heatmap data
 * @route GET /api/analytics/heatmap
 * @access Admin
 */
exports.getWeeklyActivityHeatmap = async (req, res, next) => {
  try {
    const { days = 30 } = req.query;
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - parseInt(days));

    // Get all analytics events within the date range
    const events = await Analytics.find({
      created_at: { $gte: startDate, $lte: endDate },
      event_type: { $in: ['page_view', 'login', 'download', 'purchase', 'checkout_complete'] }
    }).select('created_at event_type user_id');

    // Initialize heatmap data structure
    // 7 days (Mon-Sun) x 24 hours = 7x24 matrix
    const heatmapData = Array(24).fill(null).map(() => Array(7).fill(0));

    // Process each event
    events.forEach(event => {
      const eventDate = new Date(event.created_at);

      // Get day of week (0 = Sunday, 1 = Monday, etc.)
      // Convert to Monday = 0, Sunday = 6
      let dayOfWeek = eventDate.getDay();
      dayOfWeek = dayOfWeek === 0 ? 6 : dayOfWeek - 1;

      // Get hour (0-23)
      const hour = eventDate.getHours();

      // Increment the count for this day/hour combination
      if (hour >= 0 && hour < 24 && dayOfWeek >= 0 && dayOfWeek < 7) {
        heatmapData[hour][dayOfWeek]++;
      }
    });



    res.status(200).json({
      success: true,
      data: {
        heatmapData,
        totalEvents: events.length,
        dateRange: {
          start: startDate.toISOString(),
          end: endDate.toISOString()
        },
        labels: {
          days: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
          hours: [
            '12AM', '1AM', '2AM', '3AM', '4AM', '5AM', '6AM', '7AM', '8AM', '9AM', '10AM', '11AM',
            '12PM', '1PM', '2PM', '3PM', '4PM', '5PM', '6PM', '7PM', '8PM', '9PM', '10PM', '11PM'
          ]
        }
      }
    });
  } catch (error) {
    console.error('Error getting weekly activity heatmap:', error);
    next(new AppError(`Error getting weekly activity heatmap: ${error.message}`, 500));
  }
};

/**
 * Get email sequence stats
 * @route GET /api/analytics/email-sequences/:id
 * @access Admin
 */
exports.getEmailSequenceStats = async (req, res, next) => {
  try {
    const { id } = req.params;

    if (!id) {
      return next(new AppError('Email sequence ID is required', 400));
    }

    // Find the sequence
    const sequence = await EmailSequence.findById(id);
    if (!sequence) {
      return next(new AppError('Email sequence not found', 404));
    }

    // Get tracking data
    const trackingData = await EmailSequenceTracking.find({ email_sequence: id });

    // Calculate stats
    const totalUsers = trackingData.length;
    let totalSent = 0;
    let totalOpened = 0;
    let totalClicked = 0;

    // Count emails delivered
    trackingData.forEach(tracking => {
      if (tracking.emails_delivered && tracking.emails_delivered.length) {
        totalSent += tracking.emails_delivered.length;

        // Count opened and clicked emails
        tracking.emails_delivered.forEach(email => {
          if (email.opened_at) totalOpened++;
          if (email.clicked_at) totalClicked++;
        });
      }
    });

    // Get email-specific stats
    const emailStats = [];

    for (const email of sequence.emails) {
      let sent = 0;
      let opened = 0;
      let clicked = 0;

      // Count stats for this specific email
      trackingData.forEach(tracking => {
        if (tracking.emails_delivered && tracking.emails_delivered.length) {
          const delivered = tracking.emails_delivered.find(
            e => e.position_in_sequence === email.order
          );

          if (delivered) {
            sent++;
            if (delivered.opened_at) opened++;
            if (delivered.clicked_at) clicked++;
          }
        }
      });

      // Check if this is an A/B test
      const variantStats = [];

      if (email.isABTest && email.variants && email.variants.length > 0) {
        // Calculate stats for each variant
        for (const variant of email.variants) {
          let variantSent = 0;
          let variantOpened = 0;
          let variantClicked = 0;

          // Count stats for this variant
          trackingData.forEach(tracking => {
            if (tracking.emails_delivered && tracking.emails_delivered.length) {
              const delivered = tracking.emails_delivered.find(
                e => e.position_in_sequence === email.order &&
                  e.variant_id && e.variant_id.toString() === variant._id.toString()
              );

              if (delivered) {
                variantSent++;
                if (delivered.opened_at) variantOpened++;
                if (delivered.clicked_at) variantClicked++;
              }
            }
          });

          variantStats.push({
            id: variant._id,
            subject: variant.subject,
            sent: variantSent,
            opened: variantOpened,
            clicked: variantClicked,
            openRate: variantSent > 0 ? (variantOpened / variantSent * 100).toFixed(2) : 0,
            clickRate: variantOpened > 0 ? (variantClicked / variantOpened * 100).toFixed(2) : 0,
            weight: variant.weight
          });
        }
      }

      emailStats.push({
        id: email._id,
        subject: email.subject,
        order: email.order,
        sent,
        opened,
        clicked,
        openRate: sent > 0 ? (opened / sent * 100).toFixed(2) : 0,
        clickRate: opened > 0 ? (clicked / opened * 100).toFixed(2) : 0,
        isABTest: email.isABTest || false,
        variants: variantStats
      });
    }

    // Get daily stats for the last 30 days
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - 30);

    const dailyStats = await Analytics.aggregate([
      {
        $match: {
          event_type: { $in: ['email_sent', 'email_open', 'email_click'] },
          'metadata.email_sequence_id': id,
          created_at: { $gte: startDate }
        }
      },
      {
        $group: {
          _id: {
            date: { $dateToString: { format: '%Y-%m-%d', date: '$created_at' } },
            event_type: '$event_type'
          },
          count: { $sum: 1 }
        }
      },
      {
        $sort: {
          '_id.date': 1
        }
      }
    ]);

    // Format daily stats
    const formattedDailyStats = {};

    dailyStats.forEach(stat => {
      const date = stat._id.date;
      const eventType = stat._id.event_type;
      const count = stat.count;

      if (!formattedDailyStats[date]) {
        formattedDailyStats[date] = {
          email_sent: 0,
          email_open: 0,
          email_click: 0
        };
      }

      formattedDailyStats[date][eventType] = count;
    });

    res.status(200).json({
      success: true,
      data: {
        id: sequence._id,
        name: sequence.name,
        trigger: sequence.trigger,
        isActive: sequence.isActive,
        users: totalUsers,
        sent: totalSent,
        opened: totalOpened,
        clicked: totalClicked,
        openRate: totalSent > 0 ? (totalOpened / totalSent * 100).toFixed(2) : 0,
        clickRate: totalOpened > 0 ? (totalClicked / totalOpened * 100).toFixed(2) : 0,
        emailStats,
        dailyStats: formattedDailyStats
      }
    });
  } catch (error) {
    next(new AppError(`Error getting email sequence stats: ${error.message}`, 500));
  }
};

/**
 * Get all email sequences stats
 * @route GET /api/analytics/email-sequences
 * @access Admin
 */
/**
 * Get A/B test results for an email
 * @route GET /api/analytics/ab-test/:emailId
 * @access Admin
 */
exports.getABTestResults = async (req, res, next) => {
  try {
    const { emailId } = req.params;

    if (!emailId) {
      return next(new AppError('Email ID is required', 400));
    }

    // Find the email sequence containing this email
    const sequence = await EmailSequence.findOne({ 'emails._id': emailId });
    if (!sequence) {
      return next(new AppError('Email not found in any sequence', 404));
    }

    // Find the specific email
    const email = sequence.emails.find(e => e._id.toString() === emailId);
    if (!email) {
      return next(new AppError('Email not found', 404));
    }

    // If not an A/B test, return error
    if (!email.isABTest || !email.variants || email.variants.length === 0) {
      return next(new AppError('This email is not configured for A/B testing', 400));
    }

    // Get tracking data
    const trackingData = await EmailSequenceTracking.find({ email_sequence: sequence._id });

    // Calculate stats for each variant
    const variantStats = [];

    for (const variant of email.variants) {
      let sent = 0;
      let opened = 0;
      let clicked = 0;

      // Count stats for this variant
      trackingData.forEach(tracking => {
        if (tracking.emails_delivered && tracking.emails_delivered.length) {
          const delivered = tracking.emails_delivered.find(
            e => e.position_in_sequence === email.order &&
              e.variant_id && e.variant_id.toString() === variant._id.toString()
          );

          if (delivered) {
            sent++;
            if (delivered.opened_at) opened++;
            if (delivered.clicked_at) clicked++;
          }
        }
      });

      variantStats.push({
        id: variant._id,
        subject: variant.subject,
        sent,
        opened,
        clicked,
        openRate: sent > 0 ? (opened / sent * 100).toFixed(2) : 0,
        clickRate: opened > 0 ? (clicked / opened * 100).toFixed(2) : 0,
        weight: variant.weight
      });
    }

    // Determine the winner based on open rate and click rate
    let winner = null;

    if (variantStats.length > 0) {
      // Sort by click rate first, then open rate
      const sortedVariants = [...variantStats].sort((a, b) => {
        if (parseFloat(b.clickRate) !== parseFloat(a.clickRate)) {
          return parseFloat(b.clickRate) - parseFloat(a.clickRate);
        }
        return parseFloat(b.openRate) - parseFloat(a.openRate);
      });

      winner = sortedVariants[0];
    }

    res.status(200).json({
      success: true,
      data: {
        emailId,
        variants: variantStats,
        winner
      }
    });
  } catch (error) {
    next(new AppError(`Error getting A/B test results: ${error.message}`, 500));
  }
};

/**
 * Get unsubscribe analytics
 * @route GET /api/analytics/unsubscribes
 * @access Admin
 */
exports.getUnsubscribeAnalytics = async (req, res, next) => {
  try {
    // Get all unsubscribes
    const unsubscribes = await EmailSequenceTracking.find({ unsubscribed: true });

    // Calculate stats
    const total = unsubscribes.length;
    const reasons = {
      not_interested: 0,
      too_many_emails: 0,
      content_not_relevant: 0,
      other: 0
    };

    // Count by reason
    unsubscribes.forEach(unsubscribe => {
      if (unsubscribe.unsubscribe_reason) {
        reasons[unsubscribe.unsubscribe_reason]++;
      } else {
        reasons.other++;
      }
    });

    // Get unsubscribes by sequence
    const sequenceUnsubscribes = await EmailSequenceTracking.aggregate([
      { $match: { unsubscribed: true } },
      {
        $group: {
          _id: '$email_sequence',
          count: { $sum: 1 }
        }
      },
      { $sort: { count: -1 } }
    ]);

    // Get sequence details
    const bySequence = [];

    for (const item of sequenceUnsubscribes) {
      const sequence = await EmailSequence.findById(item._id);
      if (sequence) {
        bySequence.push({
          id: sequence._id,
          name: sequence.name,
          count: item.count
        });
      }
    }

    res.status(200).json({
      success: true,
      data: {
        total,
        reasons,
        bySequence
      }
    });
  } catch (error) {
    next(new AppError(`Error getting unsubscribe analytics: ${error.message}`, 500));
  }
};

/**
 * Get unsubscribe analytics for a specific sequence
 * @route GET /api/analytics/unsubscribes/:sequenceId
 * @access Admin
 */
exports.getSequenceUnsubscribeAnalytics = async (req, res, next) => {
  try {
    const { sequenceId } = req.params;

    if (!sequenceId) {
      return next(new AppError('Sequence ID is required', 400));
    }

    // Get sequence
    const sequence = await EmailSequence.findById(sequenceId);
    if (!sequence) {
      return next(new AppError('Email sequence not found', 404));
    }

    // Get unsubscribes for this sequence
    const unsubscribes = await EmailSequenceTracking.find({
      email_sequence: sequenceId,
      unsubscribed: true
    });

    // Calculate stats
    const total = unsubscribes.length;
    const reasons = {
      not_interested: 0,
      too_many_emails: 0,
      content_not_relevant: 0,
      other: 0
    };

    // Count by reason
    unsubscribes.forEach(unsubscribe => {
      if (unsubscribe.unsubscribe_reason) {
        reasons[unsubscribe.unsubscribe_reason]++;
      } else {
        reasons.other++;
      }
    });

    // Get unsubscribe rate
    const totalTracking = await EmailSequenceTracking.countDocuments({ email_sequence: sequenceId });
    const unsubscribeRate = totalTracking > 0 ? (total / totalTracking * 100).toFixed(2) : 0;

    res.status(200).json({
      success: true,
      data: {
        sequenceId,
        sequenceName: sequence.name,
        total,
        reasons,
        unsubscribeRate
      }
    });
  } catch (error) {
    next(new AppError(`Error getting sequence unsubscribe analytics: ${error.message}`, 500));
  }
};

exports.getAllEmailSequencesStats = async (req, res, next) => {
  try {
    // Get all sequences
    const sequences = await EmailSequence.find();

    // Get stats for each sequence
    const stats = {};

    for (const sequence of sequences) {
      // Get tracking data
      const trackingData = await EmailSequenceTracking.find({ email_sequence: sequence._id });

      // Calculate stats
      const totalUsers = trackingData.length;
      let totalSent = 0;
      let totalOpened = 0;
      let totalClicked = 0;

      // Count emails delivered
      trackingData.forEach(tracking => {
        if (tracking.emails_delivered && tracking.emails_delivered.length) {
          totalSent += tracking.emails_delivered.length;

          // Count opened and clicked emails
          tracking.emails_delivered.forEach(email => {
            if (email.opened_at) totalOpened++;
            if (email.clicked_at) totalClicked++;
          });
        }
      });

      stats[sequence._id] = {
        id: sequence._id,
        name: sequence.name,
        trigger: sequence.trigger,
        isActive: sequence.isActive,
        users: totalUsers,
        sent: totalSent,
        opened: totalOpened,
        clicked: totalClicked,
        openRate: totalSent > 0 ? (totalOpened / totalSent * 100).toFixed(2) : 0,
        clickRate: totalOpened > 0 ? (totalClicked / totalOpened * 100).toFixed(2) : 0
      };
    }

    res.status(200).json({
      success: true,
      data: stats
    });
  } catch (error) {
    next(new AppError(`Error getting all email sequences stats: ${error.message}`, 500));
  }
};


