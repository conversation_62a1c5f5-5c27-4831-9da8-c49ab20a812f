const Shipping = require('../models/Shipping');
const Order = require('../models/Order');
const catchAsync = require('../utils/catchAsync');
const AppError = require('../utils/appError');

/**
 * @desc    Create shipping record for order
 * @route   POST /api/shipping
 * @access  Private (Admin)
 */
exports.createShipping = catchAsync(async (req, res, next) => {
  const {
    order,
    shipping_method,
    carrier,
    service_type,
    shipping_address,
    billing_address,
    package_weight,
    package_dimensions,
    declared_value,
    shipping_cost,
    insurance_cost,
    delivery_instructions,
    signature_required
  } = req.body;

  // Check if order exists and doesn't already have shipping
  const orderExists = await Order.findById(order);
  if (!orderExists) {
    return next(new AppError('Order not found', 404));
  }

  const existingShipping = await Shipping.findOne({ order });
  if (existingShipping) {
    return next(new AppError('Shipping record already exists for this order', 400));
  }

  // Calculate tax amount (18% VAT in Tanzania)
  const tax_amount = Math.round(shipping_cost * 0.18);

  const shipping = await Shipping.create({
    order,
    shipping_method,
    carrier,
    service_type,
    shipping_address,
    billing_address,
    package_weight,
    package_dimensions,
    declared_value,
    shipping_cost,
    insurance_cost,
    tax_amount,
    delivery_instructions,
    signature_required,
    warehouse_location: 'main_warehouse'
  });

  await shipping.populate('order', 'order_number customer_name customer_email');

  res.status(201).json({
    success: true,
    data: shipping
  });
});

/**
 * @desc    Get shipping record by order ID
 * @route   GET /api/shipping/order/:orderId
 * @access  Private
 */
exports.getShippingByOrder = catchAsync(async (req, res, next) => {
  const { orderId } = req.params;

  const shipping = await Shipping.findOne({ order: orderId })
    .populate('order', 'order_number customer_name customer_email total_amount');

  if (!shipping) {
    return next(new AppError('Shipping record not found', 404));
  }

  res.status(200).json({
    success: true,
    data: shipping
  });
});

/**
 * @desc    Get shipping record by tracking number
 * @route   GET /api/shipping/track/:trackingNumber
 * @access  Public
 */
exports.trackShipment = catchAsync(async (req, res, next) => {
  const { trackingNumber } = req.params;

  const shipping = await Shipping.findOne({ tracking_number: trackingNumber })
    .populate('order', 'order_number customer_name total_amount')
    .select('-billing_address'); // Don't expose billing address in public tracking

  if (!shipping) {
    return next(new AppError('Tracking number not found', 404));
  }

  res.status(200).json({
    success: true,
    data: {
      tracking_number: shipping.tracking_number,
      status: shipping.status,
      shipping_method: shipping.shipping_method,
      carrier: shipping.carrier,
      estimated_delivery_date: shipping.estimated_delivery_date,
      actual_delivery_date: shipping.actual_delivery_date,
      tracking_events: shipping.tracking_events,
      shipping_address: shipping.shipping_address,
      order: shipping.order
    }
  });
});

/**
 * @desc    Update shipping status and add tracking event
 * @route   PATCH /api/shipping/:id/status
 * @access  Private (Admin)
 */
exports.updateShippingStatus = catchAsync(async (req, res, next) => {
  const { id } = req.params;
  const { status, description, location, carrier_status, notes } = req.body;

  const shipping = await Shipping.findById(id);
  if (!shipping) {
    return next(new AppError('Shipping record not found', 404));
  }

  // Add tracking event
  shipping.addTrackingEvent(status, description, {
    location,
    carrier_status,
    notes
  });

  await shipping.save();
  await shipping.populate('order', 'order_number customer_name customer_email');

  res.status(200).json({
    success: true,
    data: shipping
  });
});

/**
 * @desc    Generate shipping label
 * @route   POST /api/shipping/:id/label
 * @access  Private (Admin)
 */
exports.generateShippingLabel = catchAsync(async (req, res, next) => {
  const { id } = req.params;
  const { tracking_number, label_url, estimated_delivery_date } = req.body;

  const shipping = await Shipping.findById(id);
  if (!shipping) {
    return next(new AppError('Shipping record not found', 404));
  }

  // Update shipping with label information
  shipping.tracking_number = tracking_number;
  shipping.label_url = label_url;
  shipping.estimated_delivery_date = estimated_delivery_date;
  shipping.status = 'label_created';

  // Add tracking event
  shipping.addTrackingEvent('label_created', 'Shipping label created', {
    notes: 'Label generated and ready for pickup'
  });

  await shipping.save();

  res.status(200).json({
    success: true,
    data: shipping
  });
});

/**
 * @desc    Mark package as packed
 * @route   PATCH /api/shipping/:id/packed
 * @access  Private (Admin)
 */
exports.markAsPacked = catchAsync(async (req, res, next) => {
  const { id } = req.params;
  const { notes } = req.body;

  const shipping = await Shipping.findById(id);
  if (!shipping) {
    return next(new AppError('Shipping record not found', 404));
  }

  shipping.packed_by = req.user.id;
  shipping.packed_at = new Date();

  // Add tracking event if not already packed
  if (shipping.status === 'pending' || shipping.status === 'label_created') {
    shipping.addTrackingEvent('picked_up', 'Package packed and ready for pickup', {
      notes: notes || 'Package packed and ready for carrier pickup'
    });
  }

  await shipping.save();

  res.status(200).json({
    success: true,
    data: shipping
  });
});

/**
 * @desc    Get all shipments with filters
 * @route   GET /api/shipping
 * @access  Private (Admin)
 */
exports.getAllShipments = catchAsync(async (req, res, next) => {
  const {
    status,
    carrier,
    shipping_method,
    overdue,
    page = 1,
    limit = 20,
    sort = '-created_at'
  } = req.query;

  // Build query
  const query = {};
  if (status) query.status = status;
  if (carrier) query.carrier = carrier;
  if (shipping_method) query.shipping_method = shipping_method;

  // Handle overdue filter
  if (overdue === 'true') {
    query.estimated_delivery_date = { $lt: new Date() };
    query.status = { $nin: ['delivered', 'returned', 'cancelled'] };
  }

  // Execute query with pagination
  const skip = (page - 1) * limit;
  const shipments = await Shipping.find(query)
    .populate('order', 'order_number customer_name customer_email total_amount')
    .sort(sort)
    .skip(skip)
    .limit(parseInt(limit));

  // Get total count
  const total = await Shipping.countDocuments(query);

  res.status(200).json({
    success: true,
    count: shipments.length,
    total,
    page: parseInt(page),
    pages: Math.ceil(total / limit),
    data: shipments
  });
});

/**
 * @desc    Calculate shipping rates
 * @route   POST /api/shipping/calculate-rates
 * @access  Public
 */
exports.calculateShippingRates = catchAsync(async (req, res, next) => {
  const { items, shipping_address, delivery_speed } = req.body;

  if (!items || !Array.isArray(items) || items.length === 0) {
    return next(new AppError('Items are required for shipping calculation', 400));
  }

  if (!shipping_address || !shipping_address.city || !shipping_address.country) {
    return next(new AppError('Valid shipping address is required', 400));
  }

  // Calculate total weight and dimensions
  const totalWeight = items.reduce((weight, item) => {
    const itemWeight = item.weight || 200; // Default 200g
    return weight + (itemWeight * item.quantity);
  }, 0);

  const totalValue = items.reduce((value, item) => {
    return value + (item.price * item.quantity);
  }, 0);

  // Determine if delivery is within Dar es Salaam
  const isDarEsSalaam = shipping_address.city.toLowerCase().includes('dar');
  const isWithinTanzania = shipping_address.country.toLowerCase().includes('tanzania');

  const rates = [];

  if (isWithinTanzania) {
    // Standard delivery (3-5 business days)
    const standardCost = isDarEsSalaam ? 
      (totalValue >= 500000 ? 0 : 5000 + Math.ceil(totalWeight / 1000) * 2000) :
      8000 + Math.ceil(totalWeight / 1000) * 3000;

    rates.push({
      method: 'standard',
      name: 'Standard Delivery',
      description: isDarEsSalaam ? '2-3 business days' : '3-5 business days',
      cost: standardCost,
      estimated_days: isDarEsSalaam ? 3 : 5,
      free_shipping_eligible: totalValue >= 500000 && isDarEsSalaam
    });

    // Express delivery (1-2 business days)
    if (isDarEsSalaam) {
      rates.push({
        method: 'express',
        name: 'Express Delivery',
        description: '1-2 business days',
        cost: 15000 + Math.ceil(totalWeight / 1000) * 5000,
        estimated_days: 2,
        free_shipping_eligible: false
      });

      // Same day delivery (if ordered before 2 PM)
      rates.push({
        method: 'same_day',
        name: 'Same Day Delivery',
        description: 'Same day (order before 2 PM)',
        cost: 25000,
        estimated_days: 0,
        free_shipping_eligible: false,
        cutoff_time: '14:00'
      });

      // Pickup option
      rates.push({
        method: 'pickup',
        name: 'Store Pickup',
        description: 'Pick up from our store',
        cost: 0,
        estimated_days: 1,
        free_shipping_eligible: true,
        pickup_address: {
          name: 'Phone Point Dar',
          address: 'Msimbazi Street, Kariakoo',
          city: 'Dar es Salaam',
          phone: '+255 123 456 789'
        }
      });
    }
  }

  res.status(200).json({
    success: true,
    data: {
      rates,
      total_weight: totalWeight,
      total_value: totalValue,
      destination: shipping_address
    }
  });
});
