/**
 * Trade-In Controller
 * Handles device trade-in functionality for Phone Point Dar
 */

const TradeIn = require('../models/TradeIn');
const User = require('../models/User');
const AppError = require('../utils/AppError');
const { catchAsync } = require('../utils/errorHandlers');
const logger = require('../utils/logger');
const auditLogService = require('../services/auditLogService');

/**
 * @desc    Create a new trade-in request
 * @route   POST /api/trade-in
 * @access  Private
 */
exports.createTradeIn = catchAsync(async (req, res, next) => {
  const {
    deviceBrand,
    deviceModel,
    deviceStorage,
    deviceColor,
    imei,
    deviceCondition,
    screenCondition,
    batteryHealth,
    functionalIssues = [],
    accessoriesIncluded = [],
    deviceImages = []
  } = req.body;

  const userId = req.user.id;
  const user = await User.findById(userId);

  // Validate required fields
  if (!deviceBrand || !deviceModel || !imei || !deviceCondition || !screenCondition) {
    return next(new AppError('Missing required fields', 400));
  }

  // Validate IMEI format
  if (!/^\d{15}$/.test(imei)) {
    return next(new AppError('Invalid IMEI format. IMEI must be 15 digits', 400));
  }

  // Check if IMEI already exists
  const existingTradeIn = await TradeIn.findOne({ imei });
  if (existingTradeIn) {
    return next(new AppError('A trade-in request already exists for this IMEI', 400));
  }

  try {
    // Create trade-in request
    const tradeIn = new TradeIn({
      customer: userId,
      customer_email: user.email,
      customer_phone: user.phone || req.body.customerPhone,
      device_brand: deviceBrand,
      device_model: deviceModel,
      device_storage: deviceStorage,
      device_color: deviceColor,
      imei,
      device_condition: deviceCondition,
      screen_condition: screenCondition,
      battery_health: batteryHealth,
      functional_issues: functionalIssues,
      accessories_included: accessoriesIncluded,
      device_images: deviceImages,
      status: 'pending'
    });

    // Calculate estimated value
    tradeIn.estimated_value = tradeIn.calculateValue();
    
    await tradeIn.save();

    // Log trade-in creation
    await auditLogService.logTradeInCreated(user, tradeIn._id, tradeIn.estimated_value);

    res.status(201).json({
      success: true,
      data: tradeIn,
      message: 'Trade-in request created successfully. We will evaluate your device and get back to you within 24 hours.'
    });
  } catch (error) {
    logger.error('Trade-in creation error:', error);
    return next(new AppError('Failed to create trade-in request', 500));
  }
});

/**
 * @desc    Get user's trade-in requests
 * @route   GET /api/trade-in
 * @access  Private
 */
exports.getUserTradeIns = catchAsync(async (req, res, next) => {
  const userId = req.user.id;
  const page = parseInt(req.query.page) || 1;
  const limit = parseInt(req.query.limit) || 10;
  const skip = (page - 1) * limit;

  const tradeIns = await TradeIn.find({ customer: userId })
    .sort({ createdAt: -1 })
    .skip(skip)
    .limit(limit)
    .populate('evaluated_by', 'firstName lastName')
    .populate('related_order', 'order_number');

  const total = await TradeIn.countDocuments({ customer: userId });

  res.status(200).json({
    success: true,
    data: tradeIns,
    pagination: {
      current_page: page,
      total_pages: Math.ceil(total / limit),
      total_items: total,
      items_per_page: limit
    }
  });
});

/**
 * @desc    Get trade-in by ID
 * @route   GET /api/trade-in/:id
 * @access  Private
 */
exports.getTradeInById = catchAsync(async (req, res, next) => {
  const { id } = req.params;
  const userId = req.user.id;

  const tradeIn = await TradeIn.findById(id)
    .populate('customer', 'firstName lastName email phone')
    .populate('evaluated_by', 'firstName lastName')
    .populate('related_order', 'order_number total');

  if (!tradeIn) {
    return next(new AppError('Trade-in request not found', 404));
  }

  // Check if user owns this trade-in or is admin
  if (tradeIn.customer._id.toString() !== userId && req.user.role !== 'admin') {
    return next(new AppError('Not authorized to view this trade-in', 403));
  }

  res.status(200).json({
    success: true,
    data: tradeIn
  });
});

/**
 * @desc    Update trade-in status (Admin only)
 * @route   PATCH /api/trade-in/:id/status
 * @access  Private (Admin)
 */
exports.updateTradeInStatus = catchAsync(async (req, res, next) => {
  const { id } = req.params;
  const { status, finalValue, valuationNotes } = req.body;
  const userId = req.user.id;

  if (req.user.role !== 'admin') {
    return next(new AppError('Not authorized to update trade-in status', 403));
  }

  const tradeIn = await TradeIn.findById(id);
  if (!tradeIn) {
    return next(new AppError('Trade-in request not found', 404));
  }

  // Update status
  tradeIn.updateStatus(status, userId);
  
  if (finalValue !== undefined) {
    tradeIn.final_value = finalValue;
  }
  
  if (valuationNotes) {
    tradeIn.valuation_notes = valuationNotes;
  }

  await tradeIn.save();

  // Log status update
  await auditLogService.logTradeInStatusUpdated(req.user, tradeIn._id, status, finalValue);

  res.status(200).json({
    success: true,
    data: tradeIn,
    message: `Trade-in status updated to ${status}`
  });
});

/**
 * @desc    Get all trade-in requests (Admin only)
 * @route   GET /api/trade-in/admin/all
 * @access  Private (Admin)
 */
exports.getAllTradeIns = catchAsync(async (req, res, next) => {
  if (req.user.role !== 'admin') {
    return next(new AppError('Not authorized to view all trade-ins', 403));
  }

  const page = parseInt(req.query.page) || 1;
  const limit = parseInt(req.query.limit) || 20;
  const skip = (page - 1) * limit;
  const status = req.query.status;
  const search = req.query.search;

  // Build query
  let query = {};
  
  if (status) {
    query.status = status;
  }
  
  if (search) {
    query.$or = [
      { device_brand: { $regex: search, $options: 'i' } },
      { device_model: { $regex: search, $options: 'i' } },
      { imei: { $regex: search, $options: 'i' } },
      { customer_email: { $regex: search, $options: 'i' } }
    ];
  }

  const tradeIns = await TradeIn.find(query)
    .sort({ createdAt: -1 })
    .skip(skip)
    .limit(limit)
    .populate('customer', 'firstName lastName email phone')
    .populate('evaluated_by', 'firstName lastName');

  const total = await TradeIn.countDocuments(query);

  res.status(200).json({
    success: true,
    data: tradeIns,
    pagination: {
      current_page: page,
      total_pages: Math.ceil(total / limit),
      total_items: total,
      items_per_page: limit
    }
  });
});

/**
 * @desc    Get trade-in value estimate
 * @route   POST /api/trade-in/estimate
 * @access  Public
 */
exports.getTradeInEstimate = catchAsync(async (req, res, next) => {
  const {
    deviceBrand,
    deviceModel,
    deviceCondition,
    screenCondition,
    batteryHealth,
    functionalIssues = [],
    accessoriesIncluded = []
  } = req.body;

  // Validate required fields
  if (!deviceBrand || !deviceModel || !deviceCondition || !screenCondition) {
    return next(new AppError('Missing required fields for estimation', 400));
  }

  // Create temporary trade-in object for calculation
  const tempTradeIn = new TradeIn({
    customer: null, // Not needed for estimation
    customer_email: '<EMAIL>',
    customer_phone: '**********',
    device_brand: deviceBrand,
    device_model: deviceModel,
    imei: '**********00000', // Temporary IMEI for calculation
    device_condition: deviceCondition,
    screen_condition: screenCondition,
    battery_health: batteryHealth,
    functional_issues: functionalIssues,
    accessories_included: accessoriesIncluded
  });

  const estimatedValue = tempTradeIn.calculateValue();

  res.status(200).json({
    success: true,
    data: {
      estimated_value: estimatedValue,
      device_info: {
        brand: deviceBrand,
        model: deviceModel,
        condition: deviceCondition,
        screen_condition: screenCondition
      },
      note: 'This is an estimated value. Final value will be determined after physical inspection.'
    }
  });
});

/**
 * @desc    Cancel trade-in request
 * @route   DELETE /api/trade-in/:id
 * @access  Private
 */
exports.cancelTradeIn = catchAsync(async (req, res, next) => {
  const { id } = req.params;
  const userId = req.user.id;

  const tradeIn = await TradeIn.findById(id);
  if (!tradeIn) {
    return next(new AppError('Trade-in request not found', 404));
  }

  // Check ownership
  if (tradeIn.customer.toString() !== userId) {
    return next(new AppError('Not authorized to cancel this trade-in', 403));
  }

  // Can only cancel pending or evaluated trade-ins
  if (!['pending', 'evaluated'].includes(tradeIn.status)) {
    return next(new AppError('Cannot cancel trade-in in current status', 400));
  }

  tradeIn.status = 'cancelled';
  await tradeIn.save();

  // Log cancellation
  await auditLogService.logTradeInCancelled(req.user, tradeIn._id);

  res.status(200).json({
    success: true,
    message: 'Trade-in request cancelled successfully'
  });
});
