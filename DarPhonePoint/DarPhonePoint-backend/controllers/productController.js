const Product = require('../models/Product');
const auditLogService = require('../services/auditLogService');
const logger = require('../utils/logger');
const ApiResponse = require('../utils/ApiResponse');
const queryOptimizationService = require('../services/queryOptimizationService');
const cacheService = require('../services/cacheService');

// Get all products with advanced filtering and search (OPTIMIZED)
exports.getProducts = async (req, res) => {
  try {
    // Get products with filters applied
    const {
      search,
      category,
      brand,
      minPrice,
      maxPrice,
      sort = 'name',
      page = 1,
      limit = 20,
      includeInventory = 'true',
      includeVariants = 'true'
    } = req.query;

    // Build filters object
    const filters = { is_active: true };

    // Apply filters (only if values are not empty or undefined)
    if (search && search !== 'undefined' && search.trim() !== '') {
      filters.$or = [
        { name: { $regex: search, $options: 'i' } },
        { brand: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } }
      ];
    }

    if (category && category !== 'undefined' && category.trim() !== '') {
      filters.category = category;
    }

    if (brand && brand !== 'undefined' && brand.trim() !== '') {
      filters.brand = brand;
    }

    // Price range filter (only if values are not empty or undefined)
    const hasMinPrice = minPrice && minPrice !== 'undefined' && minPrice.trim() !== '' && !isNaN(parseFloat(minPrice));
    const hasMaxPrice = maxPrice && maxPrice !== 'undefined' && maxPrice.trim() !== '' && !isNaN(parseFloat(maxPrice));

    if (hasMinPrice || hasMaxPrice) {
      filters.price = {};
      if (hasMinPrice) filters.price.$gte = parseFloat(minPrice);
      if (hasMaxPrice) filters.price.$lte = parseFloat(maxPrice);
    }

    // Build sort object
    let sortObj = {};
    switch (sort) {
      case 'price':
        sortObj = { price: 1 };
        break;
      case '-price':
        sortObj = { price: -1 };
        break;
      case 'name':
        sortObj = { name: 1 };
        break;
      case '-name':
        sortObj = { name: -1 };
        break;
      case 'brand':
        sortObj = { brand: 1, name: 1 };
        break;
      case '-created_at':
        sortObj = { created_at: -1 };
        break;
      default:
        sortObj = { name: 1 };
    }

    // Build options for optimized query
    const options = {
      page: parseInt(page),
      limit: parseInt(limit),
      sort: sortObj,
      includeInventory: includeInventory === 'true',
      includeVariants: includeVariants === 'true'
    };

    // Use optimized query service
    const result = await queryOptimizationService.getProductsOptimized(filters, options);

    // Use standardized paginated response with optimized data
    return res.status(200).json(
      ApiResponse.success(result.data, 'Products retrieved successfully', {
        pagination: result.pagination
      })
    );

  } catch (error) {
    console.error('Get products error:', error);
    logger.error('Product fetch error:', { error: error.message, query });
    return res.status(500).json(
      ApiResponse.serverError('Failed to retrieve products')
    );
  }
};

/* ORIGINAL COMPLEX FUNCTION - COMMENTED OUT FOR DEBUGGING
exports.getProductsOriginal = async (req, res) => {
  try {
    const {
      // Search parameters
      search,
      category,
      brand,
      minPrice,
      maxPrice,

      // Feature filters
      storage,
      color,
      memory,

      // Sorting and pagination
      sort = 'name',
      page = 1,
      limit = 20,

      // Additional filters
      featured,
      inStock,
      onSale
    } = req.query;

    // Build the base query
    const query = { is_active: true };

    // Text search across multiple fields
    if (search) {
      query.$text = { $search: search };
    }

    // Category filter
    if (category) {
      query.category = category;
    }

    // Brand filter
    if (brand) {
      query.brand = new RegExp(brand, 'i'); // Case insensitive
    }

    // Price range filter
    if (minPrice || maxPrice) {
      query.price = {};
      if (minPrice) query.price.$gte = parseFloat(minPrice);
      if (maxPrice) query.price.$lte = parseFloat(maxPrice);
    }

    // Featured products filter
    if (featured === 'true') {
      query.is_featured = true;
    }

    // In stock filter
    if (inStock === 'true') {
      query.$or = [
        { track_inventory: false },
        {
          track_inventory: true,
          $or: [
            { stock_quantity: { $gt: 0 } },
            {
              variants: {
                $elemMatch: {
                  is_active: true,
                  stock_quantity: { $gt: 0 }
                }
              }
            }
          ]
        }
      ];
    }

    // On sale filter (products with compare_at_price)
    if (onSale === 'true') {
      query.$or = [
        {
          compare_at_price: { $exists: true, $gt: 0 },
          $expr: { $gt: ['$compare_at_price', '$price'] }
        },
        {
          variants: {
            $elemMatch: {
              is_active: true,
              compare_at_price: { $exists: true, $gt: 0 },
              $expr: { $gt: ['$compare_at_price', '$price'] }
            }
          }
        }
      ];
    }

    // Variant-specific filters
    const variantFilters = [];
    if (storage) {
      variantFilters.push({ 'variants.storage': new RegExp(storage, 'i') });
    }
    if (color) {
      variantFilters.push({ 'variants.color': new RegExp(color, 'i') });
    }
    if (memory) {
      variantFilters.push({ 'variants.memory': new RegExp(memory, 'i') });
    }

    if (variantFilters.length > 0) {
      query.$and = variantFilters.map(filter => ({
        variants: { $elemMatch: { ...filter, is_active: true } }
      }));
    }

    // Build sort object
    let sortObj = {};
    switch (sort) {
      case 'price':
        sortObj = { price: 1 };
        break;
      case '-price':
        sortObj = { price: -1 };
        break;
      case 'name':
        sortObj = { name: 1 };
        break;
      case '-name':
        sortObj = { name: -1 };
        break;
      case 'brand':
        sortObj = { brand: 1, name: 1 };
        break;
      case '-created_at':
        sortObj = { created_at: -1 };
        break;
      case 'featured':
        sortObj = { is_featured: -1, name: 1 };
        break;
      default:
        sortObj = { name: 1 };
    }

    // Add text search score sorting if search is used
    if (search) {
      sortObj = { score: { $meta: 'textScore' }, ...sortObj };
    }

    // Calculate pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);

    // Execute query with aggregation for better performance
    const aggregationPipeline = [
      { $match: query },
      {
        $addFields: {
          // Add computed fields for sorting and filtering
          in_stock: {
            $cond: {
              if: { $eq: ['$track_inventory', false] },
              then: true,
              else: {
                $or: [
                  { $gt: ['$stock_quantity', 0] },
                  {
                    $gt: [
                      {
                        $size: {
                          $filter: {
                            input: '$variants',
                            cond: {
                              $and: [
                                { $eq: ['$$this.is_active', true] },
                                { $gt: ['$$this.stock_quantity', 0] }
                              ]
                            }
                          }
                        }
                      },
                      0
                    ]
                  }
                ]
              }
            }
          },
          price_range: {
            $cond: {
              if: { $gt: [{ $size: '$variants' }, 0] },
              then: {
                $let: {
                  vars: {
                    activePrices: {
                      $map: {
                        input: {
                          $filter: {
                            input: '$variants',
                            cond: { $eq: ['$$this.is_active', true] }
                          }
                        },
                        as: 'variant',
                        in: '$$variant.price'
                      }
                    }
                  },
                  in: {
                    min: { $min: '$$activePrices' },
                    max: { $max: '$$activePrices' }
                  }
                }
              },
              else: { min: '$price', max: '$price' }
            }
          },
          primary_image: {
            $arrayElemAt: [
              {
                $filter: {
                  input: '$images',
                  cond: { $eq: ['$$this.is_primary', true] }
                }
              },
              0
            ]
          }
        }
      },
      { $sort: sortObj },
      { $skip: skip },
      { $limit: parseInt(limit) }
    ];

    // Add text search score if searching
    if (search) {
      aggregationPipeline[0].$match.score = { $meta: 'textScore' };
    }

    const products = await Product.aggregate(aggregationPipeline);

    // Get total count for pagination
    const totalQuery = [
      { $match: query },
      { $count: 'total' }
    ];
    const totalResult = await Product.aggregate(totalQuery);
    const total = totalResult[0]?.total || 0;

    return res.status(200).json({
      success: true,
      count: products.length,
      total,
      page: parseInt(page),
      pages: Math.ceil(total / parseInt(limit)),
      data: products
    });
  } catch (error) {
    console.error('Get products error:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};
*/

// Get single product (by ID or slug)
exports.getProduct = async (req, res) => {
  try {
    const { id } = req.params;
    let product;

    // Check if the parameter is a valid ObjectId (24 character hex string)
    if (id.match(/^[0-9a-fA-F]{24}$/)) {
      // It's an ObjectId, search by _id
      product = await Product.findById(id);
    } else {
      // It's likely a slug, search by slug
      product = await Product.findOne({ slug: id, is_active: true });
    }

    if (!product) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }

    return res.status(200).json({
      success: true,
      data: product
    });
  } catch (error) {
    console.error('Get product error:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// Create product (admin only)
exports.createProduct = async (req, res) => {
  try {
    const { name, description, price, product_type, file_path, features, slug: providedSlug, whop_checkout_link } = req.body;

    // Create base slug from provided slug or name
    let baseSlug = providedSlug || name.toLowerCase().replace(/[^\w ]+/g, '').replace(/ +/g, '-');
    let slug = baseSlug;
    let counter = 1;

    // Check for existing slugs and generate unique one if needed
    while (await Product.findOne({ slug })) {
      slug = `${baseSlug}-${counter}`;
      counter++;
    }

    const product = new Product({
      name,
      slug,
      description,
      price,
      product_type,
      file_path,
      features: features || [],
      whop_checkout_link
    });

    await product.save();

    // Log the product creation
    await auditLogService.logCreate(
      req.user,
      'product',
      product._id.toString(),
      product.name,
      product.toObject(),
      req,
      { product_type: product.product_type, price: product.price }
    );

    logger.info(`Product created: ${product.name} (${product._id}) by user ${req.user.name}`);

    return res.status(201).json({
      success: true,
      data: product
    });
  } catch (error) {
    logger.error('Create product error:', { error: error.message, user: req.user?.id });
    return res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// Update product (admin only)
exports.updateProduct = async (req, res) => {
  try {
    let product = await Product.findById(req.params.id);

    if (!product) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }

    // Store the previous state for audit logging
    const previousState = product.toObject();

    product = await Product.findByIdAndUpdate(
      req.params.id,
      { ...req.body, updated_at: Date.now(), whop_checkout_link: req.body.whop_checkout_link },
      { new: true, runValidators: true }
    );

    // Log the product update
    await auditLogService.logUpdate(
      req.user,
      'product',
      product._id.toString(),
      product.name,
      previousState,
      product.toObject(),
      req,
      {
        product_type: product.product_type,
        price: product.price,
        price_change: product.price !== previousState.price,
        status_change: product.is_active !== previousState.is_active
      }
    );

    logger.info(`Product updated: ${product.name} (${product._id}) by user ${req.user.name}`);

    return res.status(200).json({
      success: true,
      data: product
    });
  } catch (error) {
    logger.error('Update product error:', { error: error.message, user: req.user?.id, productId: req.params.id });
    return res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// Delete product (admin only)
exports.deleteProduct = async (req, res) => {
  try {
    const product = await Product.findById(req.params.id);

    if (!product) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }

    // Store the previous state for audit logging
    const previousState = product.toObject();

    // Soft delete - just mark as inactive
    product.is_active = false;
    product.updated_at = Date.now();
    await product.save();

    // Log the product deletion (soft delete)
    await auditLogService.logDelete(
      req.user,
      'product',
      product._id.toString(),
      product.name,
      previousState,
      req,
      {
        product_type: product.product_type,
        price: product.price,
        soft_delete: true
      }
    );

    logger.info(`Product deleted (soft): ${product.name} (${product._id}) by user ${req.user.name}`);

    return res.status(200).json({
      success: true,
      message: 'Product deleted successfully'
    });
  } catch (error) {
    logger.error('Delete product error:', { error: error.message, user: req.user?.id, productId: req.params.id });
    return res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// Get product by slug
exports.getProductBySlug = async (req, res) => {
  try {
    const product = await Product.findOne({
      slug: req.params.slug,
      is_active: true
    });

    if (!product) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }

    return res.status(200).json({
      success: true,
      data: product
    });
  } catch (error) {
    console.error('Get product by slug error:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// Get search suggestions
exports.getSearchSuggestions = async (req, res) => {
  try {
    const { q } = req.query;

    if (!q || q.length < 2) {
      return res.status(200).json({
        success: true,
        data: {
          suggestions: [],
          brands: [],
          categories: []
        }
      });
    }

    const searchRegex = new RegExp(q, 'i');

    // Get product name suggestions
    const productSuggestions = await Product.aggregate([
      {
        $match: {
          is_active: true,
          $or: [
            { name: searchRegex },
            { brand: searchRegex },
            { model: searchRegex }
          ]
        }
      },
      {
        $project: {
          suggestion: '$name',
          type: 'product',
          brand: 1,
          category: 1,
          price: 1
        }
      },
      { $limit: 5 }
    ]);

    // Get brand suggestions
    const brandSuggestions = await Product.aggregate([
      {
        $match: {
          is_active: true,
          brand: searchRegex
        }
      },
      {
        $group: {
          _id: '$brand',
          count: { $sum: 1 }
        }
      },
      {
        $project: {
          suggestion: '$_id',
          type: 'brand',
          count: 1
        }
      },
      { $sort: { count: -1 } },
      { $limit: 3 }
    ]);

    // Get category suggestions
    const categorySuggestions = await Product.aggregate([
      {
        $match: {
          is_active: true,
          category: searchRegex
        }
      },
      {
        $group: {
          _id: '$category',
          count: { $sum: 1 }
        }
      },
      {
        $project: {
          suggestion: '$_id',
          type: 'category',
          count: 1
        }
      },
      { $sort: { count: -1 } },
      { $limit: 3 }
    ]);

    return res.status(200).json({
      success: true,
      data: {
        suggestions: productSuggestions,
        brands: brandSuggestions,
        categories: categorySuggestions
      }
    });
  } catch (error) {
    console.error('Get search suggestions error:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// Get product recommendations
exports.getProductRecommendations = async (req, res) => {
  try {
    const { productId, type = 'related' } = req.query;
    let recommendations = [];

    if (type === 'related' && productId) {
      // Get the current product to base recommendations on
      const currentProduct = await Product.findById(productId);

      if (currentProduct) {
        // Find related products by brand and category
        recommendations = await Product.find({
          _id: { $ne: productId },
          is_active: true,
          $or: [
            { brand: currentProduct.brand, category: currentProduct.category },
            { brand: currentProduct.brand },
            { category: currentProduct.category }
          ]
        })
        .sort({ is_featured: -1, created_at: -1 })
        .limit(8);
      }
    } else if (type === 'popular') {
      // Get popular/featured products
      recommendations = await Product.find({
        is_active: true,
        is_featured: true
      })
      .sort({ created_at: -1 })
      .limit(8);
    } else if (type === 'new') {
      // Get newest products
      recommendations = await Product.find({
        is_active: true
      })
      .sort({ created_at: -1 })
      .limit(8);
    } else if (type === 'sale') {
      // Get products on sale
      recommendations = await Product.find({
        is_active: true,
        $or: [
          {
            compare_at_price: { $exists: true, $gt: 0 },
            $expr: { $gt: ['$compare_at_price', '$price'] }
          },
          {
            variants: {
              $elemMatch: {
                is_active: true,
                compare_at_price: { $exists: true, $gt: 0 },
                $expr: { $gt: ['$compare_at_price', '$price'] }
              }
            }
          }
        ]
      })
      .sort({ created_at: -1 })
      .limit(8);
    }

    return res.status(200).json({
      success: true,
      count: recommendations.length,
      data: recommendations
    });
  } catch (error) {
    console.error('Get product recommendations error:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// Get filter options for the frontend
exports.getFilterOptions = async (req, res) => {
  try {
    // Get all unique brands
    const brands = await Product.aggregate([
      { $match: { is_active: true } },
      { $group: { _id: '$brand', count: { $sum: 1 } } },
      { $sort: { _id: 1 } },
      { $project: { name: '$_id', count: 1, _id: 0 } }
    ]);

    // Get all unique categories
    const categories = await Product.aggregate([
      { $match: { is_active: true } },
      { $group: { _id: '$category', count: { $sum: 1 } } },
      { $sort: { _id: 1 } },
      { $project: { name: '$_id', count: 1, _id: 0 } }
    ]);

    // Get price range
    const priceRange = await Product.aggregate([
      { $match: { is_active: true } },
      {
        $group: {
          _id: null,
          minPrice: { $min: '$price' },
          maxPrice: { $max: '$price' }
        }
      }
    ]);

    // Get unique storage options from variants
    const storageOptions = await Product.aggregate([
      { $match: { is_active: true } },
      { $unwind: '$variants' },
      { $match: { 'variants.is_active': true, 'variants.storage': { $exists: true } } },
      { $group: { _id: '$variants.storage', count: { $sum: 1 } } },
      { $sort: { _id: 1 } },
      { $project: { name: '$_id', count: 1, _id: 0 } }
    ]);

    // Get unique color options from variants
    const colorOptions = await Product.aggregate([
      { $match: { is_active: true } },
      { $unwind: '$variants' },
      { $match: { 'variants.is_active': true, 'variants.color': { $exists: true } } },
      { $group: { _id: '$variants.color', count: { $sum: 1 } } },
      { $sort: { _id: 1 } },
      { $project: { name: '$_id', count: 1, _id: 0 } }
    ]);

    return res.status(200).json({
      success: true,
      data: {
        brands,
        categories,
        priceRange: priceRange[0] || { minPrice: 0, maxPrice: 0 },
        storageOptions,
        colorOptions
      }
    });
  } catch (error) {
    console.error('Get filter options error:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// Functions are already exported above using exports.functionName
// No need for additional module.exports
