const emailService = require('../services/emailService');
const emailQueueService = require('../services/emailQueueService');
const EmailTracking = require('../models/EmailTracking');
const AppError = require('../utils/AppError');
const logger = require('../utils/logger');
const { catchAsync } = require('../utils/errorHandlers');

/**
 * Send email
 * @route POST /api/email/send
 * @access Private
 */
exports.sendEmail = catchAsync(async (req, res, next) => {
  const { template, data, to, subject } = req.body;

  if (!template || !to || !subject) {
    return next(new AppError('Missing required fields', 400));
  }

  try {
    // Queue email for sending
    const job = await emailService.queueEmail({
      template,
      data,
      to,
      subject
    });

    return res.apiSuccess({
      jobId: job.id,
      message: 'Email queued for sending'
    }, 'Email queued successfully');
  } catch (error) {
    logger.error('Failed to queue email', {
      error: error.message,
      template,
      to,
      subject
    });
    return next(new AppError('Failed to send email', 500));
  }
});

/**
 * Get email statistics
 * @route GET /api/email/stats
 * @access Private
 */
exports.getEmailStats = catchAsync(async (req, res, next) => {
  try {
    const stats = await emailService.getStats();
    res.status(200).json({
      success: true,
      data: stats
    });
  } catch (error) {
    logger.error('Failed to get email statistics', {
      error: error.message
    });
    return next(new AppError('Failed to get email statistics', 500));
  }
});

/**
 * Track email open
 * @route GET /api/email/track/:trackingId
 * @access Public
 */
exports.trackEmail = catchAsync(async (req, res, next) => {
  const { trackingId } = req.params;

  try {
    const tracking = await EmailTracking.findById(trackingId);
    if (!tracking) {
      return next(new AppError('Invalid tracking ID', 404));
    }

    await tracking.addEvent('opened', {
      userAgent: req.headers['user-agent'],
      ip: req.ip
    });

    // Return a 1x1 transparent pixel
    res.writeHead(200, {
      'Content-Type': 'image/gif',
      'Content-Length': '43',
      'Cache-Control': 'private, no-cache, no-store, must-revalidate'
    });
    res.end(Buffer.from('R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7', 'base64'));
  } catch (error) {
    logger.error('Failed to track email open', {
      error: error.message,
      trackingId
    });
    // Still return the pixel even if tracking fails
    res.writeHead(200, {
      'Content-Type': 'image/gif',
      'Content-Length': '43'
    });
    res.end(Buffer.from('R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7', 'base64'));
  }
});

/**
 * Track email click
 * @route GET /api/email/click/:trackingId
 * @access Public
 */
exports.trackEmailClick = catchAsync(async (req, res, next) => {
  const { trackingId } = req.params;
  const { url } = req.query;

  try {
    const tracking = await EmailTracking.findById(trackingId);
    if (!tracking) {
      return next(new AppError('Invalid tracking ID', 404));
    }

    await tracking.addEvent('clicked', {
      url,
      userAgent: req.headers['user-agent'],
      ip: req.ip
    });

    // Redirect to the actual URL
    res.redirect(url);
  } catch (error) {
    logger.error('Failed to track email click', {
      error: error.message,
      trackingId,
      url
    });
    // Still redirect even if tracking fails
    res.redirect(url);
  }
});

/**
 * Get email tracking details
 * @route GET /api/email/tracking/:trackingId
 * @access Private
 */
exports.getTrackingDetails = catchAsync(async (req, res, next) => {
  const { trackingId } = req.params;

  try {
    const tracking = await EmailTracking.findById(trackingId);
    if (!tracking) {
      return next(new AppError('Invalid tracking ID', 404));
    }

    res.status(200).json({
      success: true,
      data: tracking
    });
  } catch (error) {
    logger.error('Failed to get tracking details', {
      error: error.message,
      trackingId
    });
    return next(new AppError('Failed to get tracking details', 500));
  }
});

/**
 * Get available email templates
 * @route GET /api/email/templates
 * @access Private
 */
exports.getTemplates = catchAsync(async (req, res, next) => {
  try {
    const emailTemplateService = require('../services/emailTemplateService');
    const templates = emailTemplateService.getAvailableTemplates();

    res.status(200).json({
      success: true,
      data: templates
    });
  } catch (error) {
    logger.error('Failed to get email templates', {
      error: error.message
    });
    return next(new AppError('Failed to get email templates', 500));
  }
});

/**
 * Send test email
 * @route POST /api/email/test
 * @access Private
 */
exports.sendTestEmail = catchAsync(async (req, res, next) => {
  const { to } = req.body;

  if (!to) {
    return next(new AppError('Recipient email is required', 400));
  }

  try {
    // Send a test email using the welcome template
    const job = await emailService.queueEmail({
      template: 'welcome',
      data: {
        firstName: 'Test User',
        leadMagnetUrl: `${process.env.FRONTEND_URL}/downloads/test`,
        dashboardUrl: `${process.env.FRONTEND_URL}/dashboard`,
        unsubscribeUrl: `${process.env.FRONTEND_URL}/unsubscribe`,
        currentYear: new Date().getFullYear()
      },
      to,
      subject: 'Test Email from AIXcelerate'
    });

    res.status(200).json({
      success: true,
      data: {
        jobId: job.id,
        message: 'Test email queued for sending'
      }
    });
  } catch (error) {
    logger.error('Failed to send test email', {
      error: error.message,
      to
    });
    return next(new AppError('Failed to send test email', 500));
  }
});