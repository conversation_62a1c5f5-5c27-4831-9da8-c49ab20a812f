/**
 * Authentication Controller Tests
 * Tests for user registration, login, logout, and password reset functionality
 */

const request = require('supertest');
const express = require('express');
const authController = require('../../controllers/authController');
const { createTestUser, generateTestToken } = require('../setup/testSetup');
const User = require('../../models/User');

// Create test app
const app = express();
app.use(express.json());

// Mock middleware
const mockProtect = (req, res, next) => {
  req.user = { id: 'test-user-id' };
  req.token = 'test-token';
  next();
};

// Setup routes
app.post('/register', authController.register);
app.post('/login', authController.login);
app.post('/logout', mockProtect, authController.logout);
app.post('/logout-all', mockProtect, authController.logoutAll);
app.post('/forgot-password', authController.forgotPassword);
app.post('/reset-password/:token', authController.resetPassword);

describe('Auth Controller', () => {
  describe('POST /register', () => {
    it('should register a new user successfully', async () => {
      const userData = {
        name: 'John Doe',
        email: '<EMAIL>',
        password: 'Password123!',
        phone: '+255123456789'
      };

      const response = await request(app)
        .post('/register')
        .send(userData);

      const body = global.testUtils.expectValidResponse(response, 201);
      expect(body.message).toContain('Registration successful');
      expect(body.requiresVerification).toBe(true);

      // Verify user was created in database
      const user = await User.findOne({ email: userData.email });
      expect(user).toBeTruthy();
      expect(user.name).toBe(userData.name);
      expect(user.isEmailVerified).toBe(false);
    });

    it('should reject registration with invalid email', async () => {
      const userData = {
        name: 'John Doe',
        email: 'invalid-email',
        password: 'Password123!',
        phone: '+255123456789'
      };

      const response = await request(app)
        .post('/register')
        .send(userData);

      global.testUtils.expectErrorResponse(response, 400);
    });

    it('should reject registration with weak password', async () => {
      const userData = {
        name: 'John Doe',
        email: '<EMAIL>',
        password: '123',
        phone: '+255123456789'
      };

      const response = await request(app)
        .post('/register')
        .send(userData);

      global.testUtils.expectErrorResponse(response, 400);
    });

    it('should reject duplicate email registration', async () => {
      const userData = {
        name: 'John Doe',
        email: '<EMAIL>',
        password: 'Password123!',
        phone: '+255123456789'
      };

      // First registration
      await request(app)
        .post('/register')
        .send(userData);

      // Second registration with same email
      const response = await request(app)
        .post('/register')
        .send(userData);

      global.testUtils.expectErrorResponse(response, 400);
    });
  });

  describe('POST /login', () => {
    let testUser;

    beforeEach(async () => {
      testUser = await createTestUser({
        email: '<EMAIL>',
        password: 'Password123!',
        isEmailVerified: true
      });
    });

    it('should login user with valid credentials', async () => {
      const loginData = {
        email: '<EMAIL>',
        password: 'Password123!'
      };

      const response = await request(app)
        .post('/login')
        .send(loginData);

      const body = global.testUtils.expectValidResponse(response, 200);
      expect(body.token).toBeTruthy();
      expect(body.user).toHaveProperty('id');
      expect(body.user).toHaveProperty('email', loginData.email);
      expect(body.user).not.toHaveProperty('password');
    });

    it('should reject login with invalid email', async () => {
      const loginData = {
        email: '<EMAIL>',
        password: 'Password123!'
      };

      const response = await request(app)
        .post('/login')
        .send(loginData);

      global.testUtils.expectErrorResponse(response, 400);
    });

    it('should reject login with invalid password', async () => {
      const loginData = {
        email: '<EMAIL>',
        password: 'WrongPassword'
      };

      const response = await request(app)
        .post('/login')
        .send(loginData);

      global.testUtils.expectErrorResponse(response, 400);
    });

    it('should reject login for unverified email', async () => {
      // Create unverified user
      await createTestUser({
        email: '<EMAIL>',
        password: 'Password123!',
        isEmailVerified: false
      });

      const loginData = {
        email: '<EMAIL>',
        password: 'Password123!'
      };

      const response = await request(app)
        .post('/login')
        .send(loginData);

      const body = global.testUtils.expectErrorResponse(response, 401);
      expect(body.requiresVerification).toBe(true);
    });
  });

  describe('POST /logout', () => {
    it('should logout user successfully', async () => {
      const response = await request(app)
        .post('/logout');

      const body = global.testUtils.expectValidResponse(response, 200);
      expect(body.message).toContain('Logged out successfully');
    });
  });

  describe('POST /logout-all', () => {
    it('should logout user from all devices successfully', async () => {
      const response = await request(app)
        .post('/logout-all');

      const body = global.testUtils.expectValidResponse(response, 200);
      expect(body.message).toContain('Logged out from all devices successfully');
    });
  });

  describe('POST /forgot-password', () => {
    let testUser;

    beforeEach(async () => {
      testUser = await createTestUser({
        email: '<EMAIL>',
        password: 'Password123!'
      });
    });

    it('should send password reset email for valid email', async () => {
      const response = await request(app)
        .post('/forgot-password')
        .send({ email: '<EMAIL>' });

      const body = global.testUtils.expectValidResponse(response, 200);
      expect(body.message).toContain('Password reset email sent');

      // Verify reset token was set
      const user = await User.findById(testUser._id).select('+resetPasswordToken +resetPasswordExpire');
      expect(user.resetPasswordToken).toBeTruthy();
      expect(user.resetPasswordExpire).toBeTruthy();
    });

    it('should handle non-existent email gracefully', async () => {
      const response = await request(app)
        .post('/forgot-password')
        .send({ email: '<EMAIL>' });

      // Should still return success for security reasons
      const body = global.testUtils.expectValidResponse(response, 200);
      expect(body.message).toContain('Password reset email sent');
    });

    it('should reject invalid email format', async () => {
      const response = await request(app)
        .post('/forgot-password')
        .send({ email: 'invalid-email' });

      global.testUtils.expectErrorResponse(response, 400);
    });
  });

  describe('POST /reset-password/:token', () => {
    let testUser;
    let resetToken;

    beforeEach(async () => {
      testUser = await createTestUser({
        email: '<EMAIL>',
        password: 'Password123!'
      });

      // Generate reset token
      const crypto = require('crypto');
      resetToken = crypto.randomBytes(32).toString('hex');
      const hashedToken = crypto.createHash('sha256').update(resetToken).digest('hex');

      testUser.resetPasswordToken = hashedToken;
      testUser.resetPasswordExpire = Date.now() + 10 * 60 * 1000; // 10 minutes
      await testUser.save();
    });

    it('should reset password with valid token', async () => {
      const newPassword = 'NewPassword123!';

      const response = await request(app)
        .post(`/reset-password/${resetToken}`)
        .send({ password: newPassword });

      const body = global.testUtils.expectValidResponse(response, 200);
      expect(body.message).toContain('Password reset successful');
      expect(body.token).toBeTruthy();

      // Verify password was changed
      const updatedUser = await User.findById(testUser._id);
      const isValidPassword = await updatedUser.matchPassword(newPassword);
      expect(isValidPassword).toBe(true);

      // Verify reset token was cleared
      expect(updatedUser.resetPasswordToken).toBeUndefined();
      expect(updatedUser.resetPasswordExpire).toBeUndefined();
    });

    it('should reject invalid reset token', async () => {
      const response = await request(app)
        .post('/reset-password/invalid-token')
        .send({ password: 'NewPassword123!' });

      global.testUtils.expectErrorResponse(response, 400);
    });

    it('should reject expired reset token', async () => {
      // Set token as expired
      testUser.resetPasswordExpire = Date.now() - 1000; // 1 second ago
      await testUser.save();

      const response = await request(app)
        .post(`/reset-password/${resetToken}`)
        .send({ password: 'NewPassword123!' });

      global.testUtils.expectErrorResponse(response, 400);
    });

    it('should reject weak password', async () => {
      const response = await request(app)
        .post(`/reset-password/${resetToken}`)
        .send({ password: '123' });

      global.testUtils.expectErrorResponse(response, 400);
    });
  });
});
