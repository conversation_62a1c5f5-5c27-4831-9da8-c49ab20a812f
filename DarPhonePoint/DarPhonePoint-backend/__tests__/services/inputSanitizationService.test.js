/**
 * Input Sanitization Service Tests
 * Tests for input sanitization and XSS protection
 */

const inputSanitizationService = require('../../services/inputSanitizationService');

describe('Input Sanitization Service', () => {
  describe('sanitizeHtml', () => {
    it('should remove malicious script tags', () => {
      const maliciousInput = '<script>alert("XSS")</script><p>Safe content</p>';
      const sanitized = inputSanitizationService.sanitizeHtml(maliciousInput, true);
      
      expect(sanitized).not.toContain('<script>');
      expect(sanitized).not.toContain('alert("XSS")');
      expect(sanitized).toContain('<p>Safe content</p>');
    });

    it('should remove event handlers', () => {
      const maliciousInput = '<div onclick="alert(\'XSS\')">Click me</div>';
      const sanitized = inputSanitizationService.sanitizeHtml(maliciousInput, true);
      
      expect(sanitized).not.toContain('onclick');
      expect(sanitized).not.toContain('alert');
      expect(sanitized).toContain('Click me');
    });

    it('should allow basic formatting when enabled', () => {
      const input = '<p><strong>Bold</strong> and <em>italic</em> text</p>';
      const sanitized = inputSanitizationService.sanitizeHtml(input, true);
      
      expect(sanitized).toContain('<p>');
      expect(sanitized).toContain('<strong>');
      expect(sanitized).toContain('<em>');
    });

    it('should strip all HTML when basic formatting is disabled', () => {
      const input = '<p><strong>Bold</strong> text</p>';
      const sanitized = inputSanitizationService.sanitizeHtml(input, false);
      
      expect(sanitized).not.toContain('<p>');
      expect(sanitized).not.toContain('<strong>');
      expect(sanitized).toBe('Bold text');
    });

    it('should handle empty and null inputs', () => {
      expect(inputSanitizationService.sanitizeHtml('')).toBe('');
      expect(inputSanitizationService.sanitizeHtml(null)).toBe('');
      expect(inputSanitizationService.sanitizeHtml(undefined)).toBe('');
    });
  });

  describe('sanitizeText', () => {
    it('should remove control characters', () => {
      const input = 'Normal text\x00\x01\x02with control chars';
      const sanitized = inputSanitizationService.sanitizeText(input);
      
      expect(sanitized).toBe('Normal textwith control chars');
    });

    it('should trim whitespace when enabled', () => {
      const input = '  spaced text  ';
      const sanitized = inputSanitizationService.sanitizeText(input, { trim: true });
      
      expect(sanitized).toBe('spaced text');
    });

    it('should respect max length', () => {
      const input = 'This is a very long text that should be truncated';
      const sanitized = inputSanitizationService.sanitizeText(input, { maxLength: 10 });
      
      expect(sanitized.length).toBe(10);
      expect(sanitized).toBe('This is a ');
    });

    it('should handle newlines based on options', () => {
      const input = 'Line 1\nLine 2\rLine 3';
      
      const withNewlines = inputSanitizationService.sanitizeText(input, { allowNewlines: true });
      expect(withNewlines).toContain('\n');
      
      const withoutNewlines = inputSanitizationService.sanitizeText(input, { allowNewlines: false });
      expect(withoutNewlines).not.toContain('\n');
      expect(withoutNewlines).not.toContain('\r');
    });

    it('should escape special characters when not allowed', () => {
      const input = 'Text with <script> and "quotes"';
      const sanitized = inputSanitizationService.sanitizeText(input, { allowSpecialChars: false });
      
      expect(sanitized).not.toContain('<');
      expect(sanitized).not.toContain('>');
      expect(sanitized).not.toContain('"');
    });
  });

  describe('sanitizeEmail', () => {
    it('should normalize valid email addresses', () => {
      const input = '  <EMAIL>  ';
      const sanitized = inputSanitizationService.sanitizeEmail(input);
      
      expect(sanitized).toBe('<EMAIL>');
    });

    it('should reject invalid email formats', () => {
      const invalidEmails = [
        'invalid-email',
        '@example.com',
        'test@',
        '<EMAIL>',
        'test@example',
        ''
      ];

      invalidEmails.forEach(email => {
        const sanitized = inputSanitizationService.sanitizeEmail(email);
        expect(sanitized).toBe('');
      });
    });

    it('should remove dangerous characters', () => {
      const input = 'test<script>@example.com';
      const sanitized = inputSanitizationService.sanitizeEmail(input);
      
      expect(sanitized).toBe('');
    });

    it('should handle null and undefined inputs', () => {
      expect(inputSanitizationService.sanitizeEmail(null)).toBe('');
      expect(inputSanitizationService.sanitizeEmail(undefined)).toBe('');
    });
  });

  describe('sanitizeUrl', () => {
    it('should accept valid HTTP URLs', () => {
      const validUrls = [
        'http://example.com',
        'https://example.com',
        'https://subdomain.example.com/path?query=value'
      ];

      validUrls.forEach(url => {
        const sanitized = inputSanitizationService.sanitizeUrl(url);
        expect(sanitized).toBe(url);
      });
    });

    it('should reject invalid URLs', () => {
      const invalidUrls = [
        'javascript:alert("XSS")',
        'data:text/html,<script>alert("XSS")</script>',
        'ftp://example.com',
        'not-a-url',
        'http://',
        ''
      ];

      invalidUrls.forEach(url => {
        const sanitized = inputSanitizationService.sanitizeUrl(url);
        expect(sanitized).toBe('');
      });
    });

    it('should remove control characters', () => {
      const input = 'https://example.com\x00\x01';
      const sanitized = inputSanitizationService.sanitizeUrl(input);
      
      expect(sanitized).toBe('https://example.com');
    });
  });

  describe('sanitizePhone', () => {
    it('should accept valid phone numbers', () => {
      const validPhones = [
        '+255123456789',
        '0123456789',
        '+****************',
        '255 123 456 789'
      ];

      validPhones.forEach(phone => {
        const sanitized = inputSanitizationService.sanitizePhone(phone);
        expect(sanitized).toBeTruthy();
        expect(sanitized.length).toBeGreaterThan(0);
      });
    });

    it('should reject invalid phone numbers', () => {
      const invalidPhones = [
        'abc123',
        '123',
        '+' + '1'.repeat(25), // Too long
        '<script>alert("XSS")</script>',
        ''
      ];

      invalidPhones.forEach(phone => {
        const sanitized = inputSanitizationService.sanitizePhone(phone);
        expect(sanitized).toBe('');
      });
    });

    it('should remove non-phone characters', () => {
      const input = '+255abc123def456ghi789';
      const sanitized = inputSanitizationService.sanitizePhone(input);
      
      expect(sanitized).not.toContain('abc');
      expect(sanitized).not.toContain('def');
      expect(sanitized).not.toContain('ghi');
    });
  });

  describe('sanitizeNumber', () => {
    it('should parse valid numbers', () => {
      expect(inputSanitizationService.sanitizeNumber('123')).toBe(123);
      expect(inputSanitizationService.sanitizeNumber('123.45')).toBe(123.45);
      expect(inputSanitizationService.sanitizeNumber(456)).toBe(456);
    });

    it('should handle negative numbers based on options', () => {
      expect(inputSanitizationService.sanitizeNumber('-123', { allowNegative: true })).toBe(-123);
      expect(inputSanitizationService.sanitizeNumber('-123', { allowNegative: false })).toBe(null);
    });

    it('should handle float numbers based on options', () => {
      expect(inputSanitizationService.sanitizeNumber('123.45', { allowFloat: true })).toBe(123.45);
      expect(inputSanitizationService.sanitizeNumber('123.45', { allowFloat: false })).toBe(123);
    });

    it('should enforce min/max bounds', () => {
      expect(inputSanitizationService.sanitizeNumber('50', { min: 0, max: 100 })).toBe(50);
      expect(inputSanitizationService.sanitizeNumber('-10', { min: 0, max: 100 })).toBe(null);
      expect(inputSanitizationService.sanitizeNumber('150', { min: 0, max: 100 })).toBe(null);
    });

    it('should reject invalid inputs', () => {
      const invalidInputs = ['abc', '', null, undefined, NaN, Infinity];
      
      invalidInputs.forEach(input => {
        const sanitized = inputSanitizationService.sanitizeNumber(input);
        expect(sanitized).toBe(null);
      });
    });
  });

  describe('sanitizeObject', () => {
    it('should sanitize object fields based on schema', () => {
      const input = {
        name: '  John Doe  ',
        email: '  <EMAIL>  ',
        age: '25',
        bio: '<script>alert("XSS")</script><p>Safe bio</p>'
      };

      const schema = {
        name: { type: 'text', options: { trim: true } },
        email: { type: 'email' },
        age: { type: 'number', options: { min: 0, max: 120 } },
        bio: { type: 'html', allowFormatting: true }
      };

      const sanitized = inputSanitizationService.sanitizeObject(input, schema);

      expect(sanitized.name).toBe('John Doe');
      expect(sanitized.email).toBe('<EMAIL>');
      expect(sanitized.age).toBe(25);
      expect(sanitized.bio).not.toContain('<script>');
      expect(sanitized.bio).toContain('<p>Safe bio</p>');
    });

    it('should handle missing schema gracefully', () => {
      const input = { name: 'John', age: '25' };
      const sanitized = inputSanitizationService.sanitizeObject(input);

      expect(sanitized.name).toBe('John');
      expect(sanitized.age).toBe('25');
    });

    it('should handle null and undefined inputs', () => {
      expect(inputSanitizationService.sanitizeObject(null)).toEqual({});
      expect(inputSanitizationService.sanitizeObject(undefined)).toEqual({});
    });
  });

  describe('detectXSS', () => {
    it('should detect script tags', () => {
      const maliciousInputs = [
        '<script>alert("XSS")</script>',
        '<SCRIPT>alert("XSS")</SCRIPT>',
        '<script src="malicious.js"></script>'
      ];

      maliciousInputs.forEach(input => {
        expect(inputSanitizationService.detectXSS(input)).toBe(true);
      });
    });

    it('should detect event handlers', () => {
      const maliciousInputs = [
        '<div onclick="alert(\'XSS\')">',
        '<img onload="alert(\'XSS\')" src="x">',
        '<body onload="malicious()">',
        'onmouseover="alert(\'XSS\')"'
      ];

      maliciousInputs.forEach(input => {
        expect(inputSanitizationService.detectXSS(input)).toBe(true);
      });
    });

    it('should detect javascript: URLs', () => {
      const maliciousInputs = [
        'javascript:alert("XSS")',
        'JAVASCRIPT:alert("XSS")',
        'javascript:void(0)'
      ];

      maliciousInputs.forEach(input => {
        expect(inputSanitizationService.detectXSS(input)).toBe(true);
      });
    });

    it('should detect other malicious patterns', () => {
      const maliciousInputs = [
        '<iframe src="malicious.html"></iframe>',
        '<object data="malicious.swf"></object>',
        '<embed src="malicious.swf">',
        'expression(alert("XSS"))',
        'vbscript:msgbox("XSS")',
        'data:text/html,<script>alert("XSS")</script>'
      ];

      maliciousInputs.forEach(input => {
        expect(inputSanitizationService.detectXSS(input)).toBe(true);
      });
    });

    it('should not flag safe content', () => {
      const safeInputs = [
        'Normal text content',
        '<p>Safe HTML paragraph</p>',
        '<strong>Bold text</strong>',
        'Email: <EMAIL>',
        'URL: https://example.com'
      ];

      safeInputs.forEach(input => {
        expect(inputSanitizationService.detectXSS(input)).toBe(false);
      });
    });

    it('should handle null and undefined inputs', () => {
      expect(inputSanitizationService.detectXSS(null)).toBe(false);
      expect(inputSanitizationService.detectXSS(undefined)).toBe(false);
      expect(inputSanitizationService.detectXSS('')).toBe(false);
    });
  });
});
