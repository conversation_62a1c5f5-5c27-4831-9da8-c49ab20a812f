/**
 * JWT Blacklist Service Tests
 * Tests for JWT token blacklisting and session management
 */

const jwtBlacklistService = require('../../services/jwtBlacklistService');
const jwt = require('jsonwebtoken');
const config = require('../../config/config');

describe('JWT Blacklist Service', () => {
  const testUserId = 'test-user-id-123';
  let testToken;

  beforeEach(() => {
    // Generate a test JWT token
    testToken = jwt.sign(
      { user: { id: testUserId } },
      config.JWT_SECRET,
      { expiresIn: '1h' }
    );
  });

  describe('blacklistToken', () => {
    it('should blacklist a valid token', async () => {
      const result = await jwtBlacklistService.blacklistToken(testToken, 'logout');
      expect(result).toBe(true);

      // Verify token is blacklisted
      const isBlacklisted = await jwtBlacklistService.isTokenBlacklisted(testToken);
      expect(isBlacklisted).toBe(true);
    });

    it('should handle invalid token gracefully', async () => {
      const result = await jwtBlacklistService.blacklistToken('invalid-token', 'logout');
      expect(result).toBe(false);
    });

    it('should handle expired token', async () => {
      // Create expired token
      const expiredToken = jwt.sign(
        { user: { id: testUserId } },
        config.JWT_SECRET,
        { expiresIn: '-1h' } // Already expired
      );

      const result = await jwtBlacklistService.blacklistToken(expiredToken, 'logout');
      expect(result).toBe(true); // Should still return true but not actually blacklist
    });

    it('should blacklist token with different reasons', async () => {
      const reasons = ['logout', 'security_event', 'password_reset'];

      for (const reason of reasons) {
        const token = jwt.sign(
          { user: { id: `${testUserId}-${reason}` } },
          config.JWT_SECRET,
          { expiresIn: '1h' }
        );

        const result = await jwtBlacklistService.blacklistToken(token, reason);
        expect(result).toBe(true);

        const isBlacklisted = await jwtBlacklistService.isTokenBlacklisted(token);
        expect(isBlacklisted).toBe(true);
      }
    });
  });

  describe('isTokenBlacklisted', () => {
    it('should return false for non-blacklisted token', async () => {
      const isBlacklisted = await jwtBlacklistService.isTokenBlacklisted(testToken);
      expect(isBlacklisted).toBe(false);
    });

    it('should return true for blacklisted token', async () => {
      await jwtBlacklistService.blacklistToken(testToken, 'logout');
      
      const isBlacklisted = await jwtBlacklistService.isTokenBlacklisted(testToken);
      expect(isBlacklisted).toBe(true);
    });

    it('should handle invalid token gracefully', async () => {
      const isBlacklisted = await jwtBlacklistService.isTokenBlacklisted('invalid-token');
      expect(isBlacklisted).toBe(true); // Fail secure
    });
  });

  describe('trackUserSession', () => {
    it('should track user session successfully', async () => {
      const result = await jwtBlacklistService.trackUserSession(testUserId, testToken);
      expect(result).toBe(true);

      // Verify session is tracked
      const sessionCount = await jwtBlacklistService.getUserSessionCount(testUserId);
      expect(sessionCount).toBe(1);
    });

    it('should track multiple sessions for same user', async () => {
      const token1 = jwt.sign({ user: { id: testUserId } }, config.JWT_SECRET, { expiresIn: '1h' });
      const token2 = jwt.sign({ user: { id: testUserId } }, config.JWT_SECRET, { expiresIn: '1h' });

      await jwtBlacklistService.trackUserSession(testUserId, token1);
      await jwtBlacklistService.trackUserSession(testUserId, token2);

      const sessionCount = await jwtBlacklistService.getUserSessionCount(testUserId);
      expect(sessionCount).toBe(2);
    });
  });

  describe('removeUserSession', () => {
    beforeEach(async () => {
      await jwtBlacklistService.trackUserSession(testUserId, testToken);
    });

    it('should remove user session successfully', async () => {
      const result = await jwtBlacklistService.removeUserSession(testUserId, testToken);
      expect(result).toBe(true);

      const sessionCount = await jwtBlacklistService.getUserSessionCount(testUserId);
      expect(sessionCount).toBe(0);
    });

    it('should handle removing non-existent session', async () => {
      const nonExistentToken = jwt.sign({ user: { id: testUserId } }, config.JWT_SECRET, { expiresIn: '1h' });
      
      const result = await jwtBlacklistService.removeUserSession(testUserId, nonExistentToken);
      expect(result).toBe(true); // Should not fail
    });
  });

  describe('blacklistAllUserTokens', () => {
    beforeEach(async () => {
      // Track multiple sessions
      const token1 = jwt.sign({ user: { id: testUserId } }, config.JWT_SECRET, { expiresIn: '1h' });
      const token2 = jwt.sign({ user: { id: testUserId } }, config.JWT_SECRET, { expiresIn: '1h' });
      
      await jwtBlacklistService.trackUserSession(testUserId, token1);
      await jwtBlacklistService.trackUserSession(testUserId, token2);
    });

    it('should blacklist all user tokens', async () => {
      const result = await jwtBlacklistService.blacklistAllUserTokens(testUserId, 'security_event');
      expect(result).toBe(true);

      // Verify all sessions are cleared
      const sessionCount = await jwtBlacklistService.getUserSessionCount(testUserId);
      expect(sessionCount).toBe(0);
    });

    it('should handle user with no active sessions', async () => {
      const result = await jwtBlacklistService.blacklistAllUserTokens('non-existent-user', 'security_event');
      expect(result).toBe(true); // Should not fail
    });
  });

  describe('getUserSessionCount', () => {
    it('should return 0 for user with no sessions', async () => {
      const sessionCount = await jwtBlacklistService.getUserSessionCount('non-existent-user');
      expect(sessionCount).toBe(0);
    });

    it('should return correct session count', async () => {
      const tokens = [];
      for (let i = 0; i < 3; i++) {
        const token = jwt.sign({ user: { id: testUserId } }, config.JWT_SECRET, { expiresIn: '1h' });
        tokens.push(token);
        await jwtBlacklistService.trackUserSession(testUserId, token);
      }

      const sessionCount = await jwtBlacklistService.getUserSessionCount(testUserId);
      expect(sessionCount).toBe(3);
    });
  });

  describe('enforceConcurrentSessionLimit', () => {
    it('should allow sessions within limit', async () => {
      const maxSessions = 3;
      
      // Create sessions within limit
      for (let i = 0; i < maxSessions; i++) {
        const token = jwt.sign({ user: { id: testUserId } }, config.JWT_SECRET, { expiresIn: '1h' });
        await jwtBlacklistService.trackUserSession(testUserId, token);
      }

      const result = await jwtBlacklistService.enforceConcurrentSessionLimit(testUserId, maxSessions);
      expect(result).toBe(true);

      const sessionCount = await jwtBlacklistService.getUserSessionCount(testUserId);
      expect(sessionCount).toBe(maxSessions);
    });

    it('should enforce session limit by blacklisting excess sessions', async () => {
      const maxSessions = 2;
      const totalSessions = 4;
      
      // Create more sessions than allowed
      for (let i = 0; i < totalSessions; i++) {
        const token = jwt.sign({ user: { id: testUserId } }, config.JWT_SECRET, { expiresIn: '1h' });
        await jwtBlacklistService.trackUserSession(testUserId, token);
      }

      const result = await jwtBlacklistService.enforceConcurrentSessionLimit(testUserId, maxSessions);
      expect(result).toBe(true);

      // Should have reduced sessions to within limit
      const sessionCount = await jwtBlacklistService.getUserSessionCount(testUserId);
      expect(sessionCount).toBeLessThanOrEqual(maxSessions);
    });

    it('should handle user with no sessions', async () => {
      const result = await jwtBlacklistService.enforceConcurrentSessionLimit('non-existent-user', 5);
      expect(result).toBe(true);
    });
  });

  describe('Error handling', () => {
    it('should handle Redis connection errors gracefully', async () => {
      // Mock Redis error
      const originalGet = global.testRedisClient.get;
      global.testRedisClient.get = jest.fn().mockRejectedValue(new Error('Redis connection error'));

      const isBlacklisted = await jwtBlacklistService.isTokenBlacklisted(testToken);
      expect(isBlacklisted).toBe(true); // Should fail secure

      // Restore original method
      global.testRedisClient.get = originalGet;
    });

    it('should handle malformed JWT tokens', async () => {
      const malformedTokens = [
        'not.a.jwt',
        'invalid-token',
        '',
        null,
        undefined
      ];

      for (const token of malformedTokens) {
        const result = await jwtBlacklistService.blacklistToken(token, 'test');
        expect(result).toBe(false);
      }
    });
  });
});
