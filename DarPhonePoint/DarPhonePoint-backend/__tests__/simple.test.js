/**
 * Simple Test
 * Basic test to verify Jest setup is working
 */

describe('Simple Test Suite', () => {
  it('should pass a basic test', () => {
    expect(1 + 1).toBe(2);
  });

  it('should test string operations', () => {
    const str = 'Hello World';
    expect(str.toLowerCase()).toBe('hello world');
    expect(str.length).toBe(11);
  });

  it('should test array operations', () => {
    const arr = [1, 2, 3, 4, 5];
    expect(arr.length).toBe(5);
    expect(arr.includes(3)).toBe(true);
    expect(arr.filter(x => x > 3)).toEqual([4, 5]);
  });

  it('should test object operations', () => {
    const obj = { name: '<PERSON>', age: 30 };
    expect(obj.name).toBe('John');
    expect(obj).toHaveProperty('age');
    expect(Object.keys(obj)).toEqual(['name', 'age']);
  });

  it('should test async operations', async () => {
    const promise = Promise.resolve('success');
    const result = await promise;
    expect(result).toBe('success');
  });
});
