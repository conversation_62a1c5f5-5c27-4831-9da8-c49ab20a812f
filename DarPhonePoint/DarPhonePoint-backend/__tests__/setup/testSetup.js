/**
 * Test Setup Configuration
 * Sets up the testing environment with database, Redis, and other dependencies
 */

const { MongoMemoryServer } = require('mongodb-memory-server');
const mongoose = require('mongoose');
const redis = require('redis');
const logger = require('../../utils/logger');

// Global test variables
let mongoServer;
let redisClient;

/**
 * Setup test environment before all tests
 */
const setupTestEnvironment = async () => {
  try {
    // Set test environment
    process.env.NODE_ENV = 'test';
    process.env.JWT_SECRET = 'test_jwt_secret_key_for_testing_only';
    process.env.JWT_REFRESH_SECRET = 'test_jwt_refresh_secret_key_for_testing_only';
    
    // Start MongoDB Memory Server
    mongoServer = await MongoMemoryServer.create({
      binary: {
        version: '6.0.0',
        downloadDir: './node_modules/.cache/mongodb-memory-server/mongodb-binaries'
      }
    });
    
    const mongoUri = mongoServer.getUri();
    process.env.MONGODB_URI = mongoUri;
    
    // Connect to test database
    await mongoose.connect(mongoUri, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      maxPoolSize: 10,
      serverSelectionTimeoutMS: 5000,
      socketTimeoutMS: 45000,
    });
    
    // Setup Redis client for testing (use fakeredis or mock)
    try {
      redisClient = redis.createClient({
        socket: {
          host: 'localhost',
          port: 6379
        },
        lazyConnect: true
      });
      
      // Try to connect, but don't fail if Redis is not available
      await redisClient.connect().catch(() => {
        console.log('Redis not available for testing, using mock');
        redisClient = createMockRedisClient();
      });
    } catch (error) {
      console.log('Using mock Redis client for testing');
      redisClient = createMockRedisClient();
    }
    
    // Make Redis client available globally for tests
    global.testRedisClient = redisClient;
    
    logger.info('Test environment setup completed');
    
  } catch (error) {
    logger.error('Test environment setup failed:', error);
    throw error;
  }
};

/**
 * Cleanup test environment after all tests
 */
const teardownTestEnvironment = async () => {
  try {
    // Close database connection
    if (mongoose.connection.readyState !== 0) {
      await mongoose.connection.close();
    }
    
    // Stop MongoDB Memory Server
    if (mongoServer) {
      await mongoServer.stop();
    }
    
    // Close Redis connection
    if (redisClient && typeof redisClient.quit === 'function') {
      await redisClient.quit();
    }
    
    logger.info('Test environment cleanup completed');
    
  } catch (error) {
    logger.error('Test environment cleanup failed:', error);
  }
};

/**
 * Clean database between tests
 */
const cleanDatabase = async () => {
  try {
    const collections = mongoose.connection.collections;
    
    for (const key in collections) {
      const collection = collections[key];
      await collection.deleteMany({});
    }
    
    // Clear Redis if available
    if (redisClient && typeof redisClient.flushAll === 'function') {
      await redisClient.flushAll();
    }
    
  } catch (error) {
    logger.error('Database cleanup failed:', error);
    throw error;
  }
};

/**
 * Create mock Redis client for testing when Redis is not available
 */
const createMockRedisClient = () => {
  const mockData = new Map();
  
  return {
    get: jest.fn(async (key) => mockData.get(key) || null),
    set: jest.fn(async (key, value) => mockData.set(key, value)),
    setex: jest.fn(async (key, ttl, value) => mockData.set(key, value)),
    del: jest.fn(async (key) => mockData.delete(key)),
    exists: jest.fn(async (key) => mockData.has(key) ? 1 : 0),
    expire: jest.fn(async () => true),
    flushAll: jest.fn(async () => mockData.clear()),
    sadd: jest.fn(async (key, value) => {
      const set = mockData.get(key) || new Set();
      set.add(value);
      mockData.set(key, set);
      return 1;
    }),
    srem: jest.fn(async (key, value) => {
      const set = mockData.get(key);
      if (set) {
        set.delete(value);
        return 1;
      }
      return 0;
    }),
    smembers: jest.fn(async (key) => {
      const set = mockData.get(key);
      return set ? Array.from(set) : [];
    }),
    scard: jest.fn(async (key) => {
      const set = mockData.get(key);
      return set ? set.size : 0;
    }),
    lpush: jest.fn(async (key, value) => {
      const list = mockData.get(key) || [];
      list.unshift(value);
      mockData.set(key, list);
      return list.length;
    }),
    lrange: jest.fn(async (key, start, stop) => {
      const list = mockData.get(key) || [];
      return list.slice(start, stop === -1 ? undefined : stop + 1);
    }),
    ltrim: jest.fn(async (key, start, stop) => {
      const list = mockData.get(key) || [];
      const trimmed = list.slice(start, stop + 1);
      mockData.set(key, trimmed);
      return 'OK';
    }),
    quit: jest.fn(async () => true),
    connect: jest.fn(async () => true)
  };
};

/**
 * Create test user helper
 */
const createTestUser = async (userData = {}) => {
  const User = require('../../models/User');
  
  const defaultUser = {
    name: 'Test User',
    email: '<EMAIL>',
    password: 'TestPassword123!',
    role: 'user',
    isEmailVerified: true,
    ...userData
  };
  
  const user = new User(defaultUser);
  await user.save();
  
  return user;
};

/**
 * Create test admin user helper
 */
const createTestAdmin = async (userData = {}) => {
  return createTestUser({
    name: 'Test Admin',
    email: '<EMAIL>',
    role: 'admin',
    ...userData
  });
};

/**
 * Create test product helper
 */
const createTestProduct = async (productData = {}) => {
  const Product = require('../../models/Product');
  
  const defaultProduct = {
    name: 'Test Phone',
    description: 'Test phone description',
    price: 299.99,
    category: 'Smartphones',
    brand: 'TestBrand',
    model: 'TestModel',
    specifications: {
      display: '6.1 inch',
      storage: '128GB',
      ram: '6GB',
      camera: '12MP',
      battery: '3000mAh'
    },
    images: ['test-image.jpg'],
    stock: 10,
    isActive: true,
    ...productData
  };
  
  const product = new Product(defaultProduct);
  await product.save();
  
  return product;
};

/**
 * Generate JWT token for testing
 */
const generateTestToken = (userId) => {
  const jwt = require('jsonwebtoken');
  const config = require('../../config/config');
  
  return jwt.sign(
    { user: { id: userId } },
    config.JWT_SECRET,
    { expiresIn: '1h' }
  );
};

module.exports = {
  setupTestEnvironment,
  teardownTestEnvironment,
  cleanDatabase,
  createTestUser,
  createTestAdmin,
  createTestProduct,
  generateTestToken,
  createMockRedisClient
};
