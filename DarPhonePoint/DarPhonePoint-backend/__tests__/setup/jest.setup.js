/**
 * Jest Setup File
 * Configures Jest environment and global test utilities
 */

const { setupTestEnvironment, teardownTestEnvironment, cleanDatabase } = require('./testSetup');

// Increase timeout for database operations
jest.setTimeout(30000);

// Setup test environment before all tests
beforeAll(async () => {
  await setupTestEnvironment();
});

// Cleanup test environment after all tests
afterAll(async () => {
  await teardownTestEnvironment();
});

// Clean database before each test
beforeEach(async () => {
  await cleanDatabase();
});

// Mock console methods to reduce noise in tests
global.console = {
  ...console,
  // Uncomment to silence console.log in tests
  // log: jest.fn(),
  // info: jest.fn(),
  // warn: jest.fn(),
  // error: jest.fn(),
};

// Mock external services
jest.mock('nodemailer', () => ({
  createTransport: jest.fn(() => ({
    sendMail: jest.fn(() => Promise.resolve({ messageId: 'test-message-id' })),
    verify: jest.fn(() => Promise.resolve(true))
  }))
}));

// Mock Redis client if not available
jest.mock('redis', () => {
  const { createMockRedisClient } = require('./testSetup');
  return {
    createClient: jest.fn(() => createMockRedisClient())
  };
});

// Mock file upload services (conditional)
try {
  jest.mock('multer', () => {
    return jest.fn(() => ({
      single: jest.fn(() => (req, res, next) => {
        req.file = {
          filename: 'test-file.jpg',
          path: '/test/path/test-file.jpg',
          mimetype: 'image/jpeg',
          size: 1024
        };
        next();
      }),
      array: jest.fn(() => (req, res, next) => {
        req.files = [{
          filename: 'test-file.jpg',
          path: '/test/path/test-file.jpg',
          mimetype: 'image/jpeg',
          size: 1024
        }];
        next();
      })
    }));
  });
} catch (error) {
  // Multer not available, skip mock
}

// Mock AWS S3 (conditional)
try {
  jest.mock('aws-sdk', () => ({
    S3: jest.fn(() => ({
      upload: jest.fn(() => ({
        promise: jest.fn(() => Promise.resolve({
          Location: 'https://test-bucket.s3.amazonaws.com/test-file.jpg',
          Key: 'test-file.jpg'
        }))
      })),
      deleteObject: jest.fn(() => ({
        promise: jest.fn(() => Promise.resolve())
      }))
    }))
  }));
} catch (error) {
  // AWS SDK not available, skip mock
}

// Mock payment services (conditional)
try {
  jest.mock('stripe', () => {
    return jest.fn(() => ({
      paymentIntents: {
        create: jest.fn(() => Promise.resolve({
          id: 'pi_test_payment_intent',
          client_secret: 'pi_test_client_secret',
          status: 'requires_payment_method'
        })),
        retrieve: jest.fn(() => Promise.resolve({
          id: 'pi_test_payment_intent',
          status: 'succeeded',
          amount: 29999
        }))
      },
      webhooks: {
        constructEvent: jest.fn(() => ({
          type: 'payment_intent.succeeded',
          data: {
            object: {
              id: 'pi_test_payment_intent',
              status: 'succeeded'
            }
          }
        }))
      }
    }));
  });
} catch (error) {
  // Stripe not available, skip mock
}

// Mock Tanzania payment services
jest.mock('../../services/tanzaniaPaymentService', () => ({
  initiateMpesaPayment: jest.fn(() => Promise.resolve({
    success: true,
    transactionId: 'test_mpesa_transaction',
    checkoutRequestId: 'test_checkout_request'
  })),
  checkMpesaPaymentStatus: jest.fn(() => Promise.resolve({
    success: true,
    status: 'completed',
    transactionId: 'test_mpesa_transaction'
  })),
  initiateTigoPesaPayment: jest.fn(() => Promise.resolve({
    success: true,
    transactionId: 'test_tigo_transaction'
  })),
  initiateAirtelMoneyPayment: jest.fn(() => Promise.resolve({
    success: true,
    transactionId: 'test_airtel_transaction'
  }))
}));

// Global test utilities
global.testUtils = {
  // Helper to create authenticated request
  createAuthenticatedRequest: (app, token) => {
    const request = require('supertest')(app);
    return {
      get: (url) => request.get(url).set('Authorization', `Bearer ${token}`),
      post: (url) => request.post(url).set('Authorization', `Bearer ${token}`),
      put: (url) => request.put(url).set('Authorization', `Bearer ${token}`),
      delete: (url) => request.delete(url).set('Authorization', `Bearer ${token}`)
    };
  },
  
  // Helper to wait for async operations
  wait: (ms) => new Promise(resolve => setTimeout(resolve, ms)),
  
  // Helper to generate test data
  generateTestEmail: () => `test${Date.now()}@example.com`,
  generateTestPhone: () => `+255${Math.floor(Math.random() * 1000000000)}`,
  
  // Helper to validate response structure
  expectValidResponse: (response, expectedStatus = 200) => {
    expect(response.status).toBe(expectedStatus);
    expect(response.body).toHaveProperty('success');
    return response.body;
  },
  
  // Helper to validate error response
  expectErrorResponse: (response, expectedStatus = 400) => {
    expect(response.status).toBe(expectedStatus);
    expect(response.body).toHaveProperty('success', false);
    expect(response.body).toHaveProperty('message');
    return response.body;
  }
};

// Suppress specific warnings in tests
const originalConsoleWarn = console.warn;
console.warn = (...args) => {
  const message = args[0];
  
  // Suppress specific warnings that are expected in test environment
  if (
    typeof message === 'string' && (
      message.includes('DeprecationWarning') ||
      message.includes('ExperimentalWarning') ||
      message.includes('MongoDB Memory Server')
    )
  ) {
    return;
  }
  
  originalConsoleWarn.apply(console, args);
};
