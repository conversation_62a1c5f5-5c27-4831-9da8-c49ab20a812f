{"server": {"port": 3000, "host": "0.0.0.0", "trustProxy": true, "rateLimit": {"windowMs": 900000, "max": 100}}, "database": {"uri": "mongodb://localhost:27017/aixcelerate", "options": {"useNewUrlParser": true, "useUnifiedTopology": true, "maxPoolSize": 10, "minPoolSize": 5, "serverSelectionTimeoutMS": 5000, "socketTimeoutMS": 45000}}, "jwt": {"secret": "YOUR_JWT_SECRET_KEY", "expiresIn": "24h", "refreshExpiresIn": "7d"}, "email": {"from": "<EMAIL>", "smtp": {"host": "smtp.example.com", "port": 587, "secure": true, "auth": {"user": "YOUR_SMTP_USER", "pass": "YOUR_SMTP_PASSWORD"}}}, "storage": {"type": "s3", "s3": {"accessKeyId": "YOUR_AWS_ACCESS_KEY", "secretAccessKey": "YOUR_AWS_SECRET_KEY", "region": "us-east-1", "bucket": "aixcelerate-assets"}}, "logging": {"level": "info", "maxSize": "5m", "maxFiles": "5d"}, "security": {"bcryptRounds": 12, "cors": {"origin": ["https://aixcelerate.com"], "methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"], "allowedHeaders": ["Content-Type", "Authorization"], "credentials": true}, "helmet": {"contentSecurityPolicy": {"directives": {"defaultSrc": ["'self'"], "scriptSrc": ["'self'", "'unsafe-inline'"], "styleSrc": ["'self'", "'unsafe-inline'"], "imgSrc": ["'self'", "data:", "https:"], "connectSrc": ["'self'"]}}}}, "cache": {"redis": {"host": "localhost", "port": 6379, "password": "YOUR_REDIS_PASSWORD"}}, "monitoring": {"sentry": {"dsn": "YOUR_SENTRY_DSN"}}}