/**
 * Brand categories configuration for Phone Point Dar
 * Maps brands to their applicable product categories
 */

const brandCategories = {
  // Mobile & Tablet Brands
  mobile: {
    'Apple': ['smartphone', 'tablet', 'smartwatch'],
    'Samsung': ['smartphone', 'tablet', 'smartwatch', 'earbuds', 'monitor'],
    'Huawei': ['smartphone', 'tablet', 'smartwatch', 'earbuds', 'laptop'],
    'Xiaomi': ['smartphone', 'tablet', 'smartwatch', 'earbuds', 'laptop', 'router', 'power_bank'],
    'Oppo': ['smartphone', 'earbuds'],
    'Vivo': ['smartphone', 'earbuds'],
    'Realme': ['smartphone', 'earbuds', 'smartwatch'],
    'OnePlus': ['smartphone', 'earbuds'],
    'Google': ['smartphone', 'tablet', 'smartwatch', 'earbuds', 'smart_speaker'],
    'Sony': ['smartphone', 'tablet', 'camera', 'headphones', 'earbuds', 'gaming_console'],
    'Nokia': ['smartphone', 'feature_phone'],
    'Tecno': ['smartphone', 'feature_phone'],
    'Infinix': ['smartphone', 'laptop'],
    'Itel': ['smartphone', 'feature_phone'],
    'Redmi': ['smartphone', 'earbuds', 'power_bank'],
    'Honor': ['smartphone', 'laptop'],
    'Motorola': ['smartphone'],
    'Nothing': ['smartphone', 'earbuds']
  },

  // Computer Brands
  computer: {
    'Dell': ['laptop', 'desktop', 'workstation', 'monitor', 'server'],
    'HP': ['laptop', 'desktop', 'workstation', 'printer', 'scanner', 'monitor'],
    'Lenovo': ['laptop', 'desktop', 'workstation', 'tablet', 'monitor'],
    'ASUS': ['laptop', 'desktop', 'gaming_laptop', 'monitor', 'router', 'motherboard'],
    'Acer': ['laptop', 'desktop', 'gaming_laptop', 'monitor', 'projector'],
    'MSI': ['laptop', 'desktop', 'gaming_laptop', 'monitor', 'motherboard'],
    'Razer': ['gaming_laptop', 'keyboard', 'mouse', 'headphones', 'mousepad'],
    'Alienware': ['gaming_laptop', 'desktop'],
    'ThinkPad': ['laptop', 'workstation'],
    'Surface': ['laptop', 'tablet'],
    'MacBook': ['laptop'],
    'iMac': ['desktop']
  },

  // Gaming Brands
  gaming: {
    'PlayStation': ['gaming_console', 'gaming_accessories', 'controller'],
    'Xbox': ['gaming_console', 'gaming_accessories', 'controller'],
    'Nintendo': ['gaming_console', 'gaming_accessories'],
    'Steam': ['gaming_console'],
    'ROG': ['gaming_laptop', 'desktop', 'monitor', 'keyboard', 'mouse'],
    'Predator': ['gaming_laptop', 'desktop', 'monitor']
  },

  // Audio Brands
  audio: {
    'Bose': ['headphones', 'earbuds', 'speaker'],
    'JBL': ['headphones', 'earbuds', 'speaker'],
    'Beats': ['headphones', 'earbuds'],
    'Sennheiser': ['headphones', 'earbuds', 'microphone'],
    'Audio-Technica': ['headphones', 'microphone'],
    'Skullcandy': ['headphones', 'earbuds'],
    'Anker': ['earbuds', 'speaker', 'charger', 'power_bank', 'cable'],
    'Jabra': ['earbuds', 'headphones', 'microphone'],
    'Plantronics': ['headphones', 'microphone'],
    'HyperX': ['headphones', 'microphone', 'keyboard', 'mouse']
  },

  // Camera Brands
  camera: {
    'Canon': ['camera', 'lens', 'printer'],
    'Nikon': ['camera', 'lens'],
    'Sony': ['camera', 'lens', 'camcorder'],
    'Fujifilm': ['camera', 'lens'],
    'Olympus': ['camera', 'lens'],
    'Panasonic': ['camera', 'lens', 'camcorder'],
    'GoPro': ['camera', 'camera_accessories'],
    'DJI': ['drone', 'camera', 'camera_accessories'],
    'Insta360': ['camera', 'camera_accessories']
  },

  // Networking Brands
  networking: {
    'TP-Link': ['router', 'modem', 'wifi_extender', 'network_switch', 'smart_plug'],
    'Netgear': ['router', 'modem', 'wifi_extender', 'network_switch'],
    'Linksys': ['router', 'wifi_extender', 'network_switch'],
    'D-Link': ['router', 'modem', 'wifi_extender', 'network_switch'],
    'Ubiquiti': ['router', 'wifi_extender', 'network_switch'],
    'Cisco': ['router', 'network_switch', 'server'],
    'ASUS': ['router', 'wifi_extender'],
    'Tenda': ['router', 'wifi_extender'],
    'Mercusys': ['router', 'wifi_extender']
  },

  // Storage Brands
  storage: {
    'SanDisk': ['usb_drive', 'memory_card', 'ssd', 'external_drive'],
    'Western Digital': ['hdd', 'ssd', 'external_drive'],
    'Seagate': ['hdd', 'ssd', 'external_drive'],
    'Kingston': ['usb_drive', 'memory_card', 'ssd', 'ram'],
    'Crucial': ['ssd', 'ram'],
    'Samsung': ['ssd', 'memory_card', 'usb_drive'],
    'Transcend': ['usb_drive', 'memory_card', 'ssd', 'external_drive'],
    'Lexar': ['memory_card', 'usb_drive', 'ssd']
  },

  // Accessory Brands
  accessories: {
    'Logitech': ['keyboard', 'mouse', 'webcam', 'speaker', 'mousepad'],
    'Corsair': ['keyboard', 'mouse', 'headphones', 'mousepad', 'ram'],
    'Cooler Master': ['keyboard', 'mouse', 'cooling', 'case'],
    'Thermaltake': ['cooling', 'case', 'power_supply'],
    'NZXT': ['case', 'cooling'],
    'Belkin': ['charger', 'cable', 'adapter', 'surge_protector'],
    'Anker': ['charger', 'cable', 'power_bank', 'adapter'],
    'Baseus': ['charger', 'cable', 'power_bank', 'adapter', 'mount'],
    'Ugreen': ['charger', 'cable', 'adapter', 'hub']
  },

  // Generic/Other
  generic: {
    'Generic': ['case', 'screen_protector', 'cable', 'adapter', 'mount', 'stand', 'cleaning_kit'],
    'Other': ['other', 'repair_tool', 'cleaning_kit']
  }
};

/**
 * Get brands for a specific category
 * @param {string} category - Product category
 * @returns {array} Array of applicable brands
 */
const getBrandsForCategory = (category) => {
  const brands = new Set();
  
  Object.values(brandCategories).forEach(brandGroup => {
    Object.entries(brandGroup).forEach(([brand, categories]) => {
      if (categories.includes(category)) {
        brands.add(brand);
      }
    });
  });
  
  return Array.from(brands).sort();
};

/**
 * Get categories for a specific brand
 * @param {string} brand - Brand name
 * @returns {array} Array of applicable categories
 */
const getCategoriesForBrand = (brand) => {
  const categories = new Set();
  
  Object.values(brandCategories).forEach(brandGroup => {
    if (brandGroup[brand]) {
      brandGroup[brand].forEach(category => categories.add(category));
    }
  });
  
  return Array.from(categories).sort();
};

/**
 * Check if a brand is valid for a category
 * @param {string} brand - Brand name
 * @param {string} category - Product category
 * @returns {boolean} True if valid combination
 */
const isBrandValidForCategory = (brand, category) => {
  const validBrands = getBrandsForCategory(category);
  return validBrands.includes(brand);
};

/**
 * Get all brands
 * @returns {array} Array of all brands
 */
const getAllBrands = () => {
  const brands = new Set();
  
  Object.values(brandCategories).forEach(brandGroup => {
    Object.keys(brandGroup).forEach(brand => brands.add(brand));
  });
  
  return Array.from(brands).sort();
};

/**
 * Get brand suggestions based on category
 * @param {string} category - Product category
 * @returns {object} Organized brand suggestions
 */
const getBrandSuggestions = (category) => {
  const allBrands = getBrandsForCategory(category);
  const suggestions = {
    popular: [],
    budget: [],
    premium: [],
    other: []
  };

  // Categorize brands based on category and brand reputation
  allBrands.forEach(brand => {
    switch (category) {
      case 'smartphone':
        if (['Apple', 'Samsung', 'Google'].includes(brand)) {
          suggestions.premium.push(brand);
        } else if (['Xiaomi', 'Realme', 'Tecno', 'Infinix'].includes(brand)) {
          suggestions.budget.push(brand);
        } else if (['OnePlus', 'Huawei', 'Sony'].includes(brand)) {
          suggestions.popular.push(brand);
        } else {
          suggestions.other.push(brand);
        }
        break;
      
      case 'laptop':
        if (['Apple', 'Dell', 'HP', 'Lenovo'].includes(brand)) {
          suggestions.popular.push(brand);
        } else if (['MSI', 'ASUS', 'Razer'].includes(brand)) {
          suggestions.premium.push(brand);
        } else if (['Acer', 'Infinix'].includes(brand)) {
          suggestions.budget.push(brand);
        } else {
          suggestions.other.push(brand);
        }
        break;
      
      default:
        suggestions.popular.push(brand);
    }
  });

  return suggestions;
};

module.exports = {
  brandCategories,
  getBrandsForCategory,
  getCategoriesForBrand,
  isBrandValidForCategory,
  getAllBrands,
  getBrandSuggestions
};
