const mongoose = require('mongoose');
const env = require('./env');
const logger = require('../utils/logger');

const connectDB = async () => {
  try {
    // Optimized connection options for Phone Point Dar e-commerce
    const options = {
      // Connection Pool Configuration
      maxPoolSize: 20,           // Increased for e-commerce traffic
      minPoolSize: 5,            // Maintain minimum connections
      maxIdleTimeMS: 30000,      // Close idle connections after 30s
      waitQueueTimeoutMS: 5000,  // Max wait time for connection from pool

      // Connection Timeouts
      serverSelectionTimeoutMS: 5000,   // Server selection timeout
      socketTimeoutMS: 45000,           // Socket timeout
      connectTimeoutMS: 10000,          // Connection timeout
      heartbeatFrequencyMS: 10000,      // Heartbeat frequency

      // Buffering Configuration
      bufferCommands: true,             // Enable command buffering for startup

      // Read/Write Preferences for Phone Point Dar
      readPreference: 'primary',        // Always read from primary for consistency
      writeConcern: {
        w: 'majority',                  // Wait for majority acknowledgment
        j: true,                        // Wait for journal acknowledgment
        wtimeout: 5000                  // Write timeout
      },

      // Additional optimizations
      retryWrites: true,                // Retry failed writes
      retryReads: true,                 // Retry failed reads
      compressors: ['zlib'],            // Enable compression
      zlibCompressionLevel: 6,          // Compression level

      // Monitoring
      monitorCommands: env.NODE_ENV === 'development'
    };

    // Construct MongoDB URI with authentication if credentials are provided
    let mongoUri = env.MONGODB_URI;
    if (env.MONGODB_USER && env.MONGODB_PASSWORD) {
      // If the URI already contains authentication, don't modify it
      if (!mongoUri.includes('@')) {
        const uriParts = mongoUri.split('://');
        if (uriParts.length === 2) {
          mongoUri = `${uriParts[0]}://${encodeURIComponent(env.MONGODB_USER)}:${encodeURIComponent(env.MONGODB_PASSWORD)}@${uriParts[1]}`;
        }
      }
    }

    await mongoose.connect(mongoUri, options);
    logger.info('MongoDB connected successfully', {
      host: mongoose.connection.host,
      port: mongoose.connection.port,
      database: mongoose.connection.name,
      poolSize: options.maxPoolSize
    });

    // Enhanced connection event monitoring
    mongoose.connection.on('error', (err) => {
      logger.error('MongoDB connection error:', err);
    });

    mongoose.connection.on('disconnected', () => {
      logger.warn('MongoDB disconnected - attempting to reconnect...');
    });

    mongoose.connection.on('reconnected', () => {
      logger.info('MongoDB reconnected successfully');
    });

    mongoose.connection.on('close', () => {
      logger.info('MongoDB connection closed');
    });

    // Monitor connection pool events
    mongoose.connection.on('connectionPoolCreated', () => {
      logger.info('MongoDB connection pool created');
    });

    mongoose.connection.on('connectionPoolClosed', () => {
      logger.info('MongoDB connection pool closed');
    });

    mongoose.connection.on('connectionCreated', () => {
      logger.debug('New MongoDB connection created');
    });

    mongoose.connection.on('connectionClosed', () => {
      logger.debug('MongoDB connection closed');
    });

    // Log slow operations in development
    if (env.NODE_ENV === 'development') {
      mongoose.set('debug', (collectionName, method, query, doc) => {
        logger.debug(`MongoDB ${method} on ${collectionName}:`, {
          query: JSON.stringify(query),
          doc: doc ? JSON.stringify(doc) : undefined
        });
      });
    }

    process.on('SIGINT', async () => {
      await mongoose.connection.close();
      logger.info('MongoDB connection closed through app termination');
      process.exit(0);
    });

  } catch (error) {
    logger.error('MongoDB connection error:', error);
    process.exit(1);
  }
};

module.exports = connectDB;