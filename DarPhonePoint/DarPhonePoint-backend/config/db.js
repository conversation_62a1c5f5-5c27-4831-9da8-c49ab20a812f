const mongoose = require('mongoose');
const env = require('./env');
const logger = require('../utils/logger');

const connectDB = async () => {
  try {
    // Optimized connection options for Phone Point Dar
    const isProduction = env.NODE_ENV === 'production';
    const options = {
      // Connection Pool Configuration (optimized for environment)
      maxPoolSize: isProduction ? 20 : 10,     // Fewer connections in development
      minPoolSize: isProduction ? 5 : 2,       // Minimal connections in development
      maxIdleTimeMS: 30000,                    // Close idle connections after 30s
      waitQueueTimeoutMS: 5000,                // Max wait time for connection from pool

      // Connection Timeouts (faster in development)
      serverSelectionTimeoutMS: isProduction ? 5000 : 3000,
      socketTimeoutMS: isProduction ? 45000 : 30000,
      connectTimeoutMS: isProduction ? 10000 : 5000,
      heartbeatFrequencyMS: isProduction ? 10000 : 15000,

      // Buffering Configuration
      bufferCommands: true,                    // Enable command buffering for startup

      // Read/Write Preferences
      readPreference: 'primary',               // Always read from primary for consistency
      writeConcern: isProduction ? {
        w: 'majority',                         // Wait for majority acknowledgment in production
        j: true,                               // Wait for journal acknowledgment
        wtimeout: 5000                         // Write timeout
      } : {
        w: 1,                                  // Faster writes in development
        j: false,                              // Skip journal in development
        wtimeout: 3000
      },

      // Additional optimizations
      retryWrites: true,                       // Retry failed writes
      retryReads: true,                        // Retry failed reads
      compressors: isProduction ? ['zlib'] : [], // Compression only in production
      zlibCompressionLevel: 6,                 // Compression level

      // Monitoring (reduced in development)
      monitorCommands: env.NODE_ENV === 'development'
    };

    // Construct MongoDB URI with authentication if credentials are provided
    let mongoUri = env.MONGODB_URI;
    if (env.MONGODB_USER && env.MONGODB_PASSWORD) {
      // If the URI already contains authentication, don't modify it
      if (!mongoUri.includes('@')) {
        const uriParts = mongoUri.split('://');
        if (uriParts.length === 2) {
          mongoUri = `${uriParts[0]}://${encodeURIComponent(env.MONGODB_USER)}:${encodeURIComponent(env.MONGODB_PASSWORD)}@${uriParts[1]}`;
        }
      }
    }

    await mongoose.connect(mongoUri, options);
    logger.info('MongoDB connected successfully', {
      host: mongoose.connection.host,
      port: mongoose.connection.port,
      database: mongoose.connection.name,
      poolSize: options.maxPoolSize
    });

    // Enhanced connection event monitoring
    mongoose.connection.on('error', (err) => {
      logger.error('MongoDB connection error:', err);
    });

    mongoose.connection.on('disconnected', () => {
      logger.warn('MongoDB disconnected - attempting to reconnect...');
    });

    mongoose.connection.on('reconnected', () => {
      logger.info('MongoDB reconnected successfully');
    });

    mongoose.connection.on('close', () => {
      logger.info('MongoDB connection closed');
    });

    // Monitor connection pool events (reduced logging)
    if (env.NODE_ENV === 'production') {
      mongoose.connection.on('connectionPoolCreated', () => {
        logger.info('MongoDB connection pool created');
      });

      mongoose.connection.on('connectionPoolClosed', () => {
        logger.info('MongoDB connection pool closed');
      });
    }

    // Minimal connection logging in development
    if (env.NODE_ENV === 'development') {
      mongoose.connection.on('connectionCreated', () => {
        logger.debug('New MongoDB connection created');
      });

      mongoose.connection.on('connectionClosed', () => {
        logger.debug('MongoDB connection closed');
      });

      // Simplified debug logging (only for slow queries)
      mongoose.set('debug', function(collectionName, method, query, doc, options) {
        // Only log queries that might be slow
        if (method === 'find' || method === 'aggregate' || method === 'findOne') {
          logger.debug(`MongoDB ${method} on ${collectionName}`);
        }
      });
    }

    process.on('SIGINT', async () => {
      await mongoose.connection.close();
      logger.info('MongoDB connection closed through app termination');
      process.exit(0);
    });

  } catch (error) {
    logger.error('MongoDB connection error:', error);
    process.exit(1);
  }
};

module.exports = connectDB;