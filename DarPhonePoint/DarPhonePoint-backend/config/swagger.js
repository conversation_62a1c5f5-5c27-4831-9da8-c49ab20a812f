const swaggerJsdoc = require('swagger-jsdoc');

const options = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: 'AIXcelerate API Documentation',
      version: '1.0.0',
      description: 'API documentation for AIXcelerate platform',
    },
    servers: [
      {
        url: process.env.API_URL || 'http://localhost:5001',
        description: 'Development server',
      },
    ],
    components: {
      securitySchemes: {
        bearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
        },
      },
      schemas: {
        Order: {
          type: 'object',
          properties: {
            _id: { type: 'string' },
            user: { type: 'string' },
            product: { type: 'string' },
            amount: { type: 'number' },
            payment_status: { type: 'string', enum: ['pending', 'completed', 'failed', 'cancelled'] },
            transaction_id: { type: 'string' },
            payment_method: { type: 'string' },
            customer_email: { type: 'string' },
            created_at: { type: 'string', format: 'date-time' },
            updated_at: { type: 'string', format: 'date-time' }
          }
        },
        PaymentResponse: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                payment_url: { type: 'string' },
                order_id: { type: 'string' },
                payment_id: { type: 'string' }
              }
            }
          }
        }
      }
    },
    security: [{
      bearerAuth: [],
    }],
  },
  apis: ['./routes/*.js', './models/*.js'],
};

const swaggerDocs = swaggerJsdoc(options);

// Add monitoring API documentation
swaggerDocs.paths['/api/monitoring/metrics'] = {
  get: {
    tags: ['Monitoring'],
    summary: 'Get current system metrics',
    description: 'Retrieve current system metrics including requests, performance, database, and cache statistics',
    security: [{ bearerAuth: [] }],
    responses: {
      200: {
        description: 'Successfully retrieved metrics',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                success: { type: 'boolean' },
                data: {
                  type: 'object',
                  properties: {
                    requests: {
                      type: 'object',
                      properties: {
                        total: { type: 'number' },
                        success: { type: 'number' },
                        error: { type: 'number' },
                        byEndpoint: { type: 'object' }
                      }
                    },
                    performance: {
                      type: 'object',
                      properties: {
                        responseTime: { type: 'array' },
                        memoryUsage: { type: 'array' },
                        cpuUsage: { type: 'array' }
                      }
                    },
                    database: {
                      type: 'object',
                      properties: {
                        operations: { type: 'number' },
                        slowQueries: { type: 'array' }
                      }
                    },
                    cache: {
                      type: 'object',
                      properties: {
                        hits: { type: 'number' },
                        misses: { type: 'number' },
                        hitRate: { type: 'number' }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      },
      401: {
        description: 'Unauthorized - Authentication required'
      },
      403: {
        description: 'Forbidden - Admin access required'
      },
      500: {
        description: 'Server error'
      }
    }
  }
};

swaggerDocs.paths['/api/monitoring/performance'] = {
  get: {
    tags: ['Monitoring'],
    summary: 'Get performance report',
    description: 'Retrieve a comprehensive performance report including request rates, response times, and system metrics',
    security: [{ bearerAuth: [] }],
    responses: {
      200: {
        description: 'Successfully retrieved performance report',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                success: { type: 'boolean' },
                data: {
                  type: 'object',
                  properties: {
                    requests: {
                      type: 'object',
                      properties: {
                        total: { type: 'number' },
                        successRate: { type: 'number' },
                        errorRate: { type: 'number' }
                      }
                    },
                    performance: {
                      type: 'object',
                      properties: {
                        avgResponseTime: { type: 'number' },
                        memoryUsage: { type: 'object' },
                        cpuUsage: { type: 'object' }
                      }
                    },
                    database: {
                      type: 'object',
                      properties: {
                        operations: { type: 'number' },
                        slowQueries: { type: 'number' }
                      }
                    },
                    cache: {
                      type: 'object',
                      properties: {
                        hitRate: { type: 'number' }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      },
      401: {
        description: 'Unauthorized - Authentication required'
      },
      403: {
        description: 'Forbidden - Admin access required'
      },
      500: {
        description: 'Server error'
      }
    }
  }
};

swaggerDocs.paths['/api/monitoring/reset'] = {
  post: {
    tags: ['Monitoring'],
    summary: 'Reset monitoring metrics',
    description: 'Reset all monitoring metrics to their initial values',
    security: [{ bearerAuth: [] }],
    responses: {
      200: {
        description: 'Successfully reset metrics',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                success: { type: 'boolean' },
                message: { type: 'string' }
              }
            }
          }
        }
      },
      401: {
        description: 'Unauthorized - Authentication required'
      },
      403: {
        description: 'Forbidden - Admin access required'
      },
      500: {
        description: 'Server error'
      }
    }
  }
};

module.exports = {
  swaggerUi: require('swagger-ui-express'),
  swaggerDocs
};
