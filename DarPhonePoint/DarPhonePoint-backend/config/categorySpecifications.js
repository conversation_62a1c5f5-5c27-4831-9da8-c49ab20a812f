/**
 * Category-specific specification templates for Phone Point Dar
 * Defines required and optional specifications for each product category
 */

const categorySpecifications = {
  // Mobile & Tablets
  smartphone: {
    required: ['display_size', 'processor', 'ram', 'storage', 'operating_system', 'main_camera', 'battery'],
    optional: ['front_camera', 'network', 'charging_speed', 'weight', 'dimensions', 'water_resistance'],
    specifications: [
      { name: 'Display Size', category: 'display', unit: 'inches', is_key_spec: true },
      { name: 'Display Type', category: 'display', unit: '', is_key_spec: true },
      { name: 'Resolution', category: 'display', unit: 'pixels', is_key_spec: false },
      { name: 'Processor', category: 'performance', unit: '', is_key_spec: true },
      { name: 'RAM', category: 'performance', unit: 'GB', is_key_spec: true },
      { name: 'Storage', category: 'storage', unit: 'GB', is_key_spec: true },
      { name: 'Operating System', category: 'software', unit: '', is_key_spec: true },
      { name: 'Main Camera', category: 'camera', unit: 'MP', is_key_spec: true },
      { name: 'Front Camera', category: 'camera', unit: 'MP', is_key_spec: false },
      { name: 'Battery', category: 'battery', unit: 'mAh', is_key_spec: true },
      { name: 'Charging Speed', category: 'battery', unit: 'W', is_key_spec: false },
      { name: 'Network', category: 'connectivity', unit: '', is_key_spec: false },
      { name: 'Weight', category: 'dimensions', unit: 'g', is_key_spec: false },
      { name: 'Water Resistance', category: 'build', unit: '', is_key_spec: false }
    ]
  },

  tablet: {
    required: ['display_size', 'processor', 'ram', 'storage', 'operating_system', 'battery'],
    optional: ['main_camera', 'front_camera', 'cellular', 'weight', 'dimensions'],
    specifications: [
      { name: 'Display Size', category: 'display', unit: 'inches', is_key_spec: true },
      { name: 'Display Type', category: 'display', unit: '', is_key_spec: true },
      { name: 'Resolution', category: 'display', unit: 'pixels', is_key_spec: true },
      { name: 'Processor', category: 'performance', unit: '', is_key_spec: true },
      { name: 'RAM', category: 'performance', unit: 'GB', is_key_spec: true },
      { name: 'Storage', category: 'storage', unit: 'GB', is_key_spec: true },
      { name: 'Operating System', category: 'software', unit: '', is_key_spec: true },
      { name: 'Main Camera', category: 'camera', unit: 'MP', is_key_spec: false },
      { name: 'Front Camera', category: 'camera', unit: 'MP', is_key_spec: false },
      { name: 'Battery', category: 'battery', unit: 'mAh', is_key_spec: true },
      { name: 'Cellular', category: 'connectivity', unit: '', is_key_spec: false },
      { name: 'Weight', category: 'dimensions', unit: 'g', is_key_spec: false }
    ]
  },

  smartwatch: {
    required: ['display_size', 'operating_system', 'battery_life', 'water_resistance'],
    optional: ['gps', 'cellular', 'health_sensors', 'storage', 'weight'],
    specifications: [
      { name: 'Display Size', category: 'display', unit: 'mm', is_key_spec: true },
      { name: 'Display Type', category: 'display', unit: '', is_key_spec: true },
      { name: 'Operating System', category: 'software', unit: '', is_key_spec: true },
      { name: 'Processor', category: 'performance', unit: '', is_key_spec: false },
      { name: 'Storage', category: 'storage', unit: 'GB', is_key_spec: false },
      { name: 'Battery Life', category: 'battery', unit: 'hours', is_key_spec: true },
      { name: 'Water Resistance', category: 'build', unit: '', is_key_spec: true },
      { name: 'GPS', category: 'connectivity', unit: '', is_key_spec: false },
      { name: 'Cellular', category: 'connectivity', unit: '', is_key_spec: false },
      { name: 'Health Sensors', category: 'sensors', unit: '', is_key_spec: true },
      { name: 'Weight', category: 'dimensions', unit: 'g', is_key_spec: false }
    ]
  },

  // Computers
  laptop: {
    required: ['processor', 'ram', 'storage', 'display_size', 'graphics', 'operating_system'],
    optional: ['battery_life', 'weight', 'ports', 'keyboard_backlight', 'webcam'],
    specifications: [
      { name: 'Processor', category: 'processor', unit: '', is_key_spec: true },
      { name: 'RAM', category: 'memory', unit: 'GB', is_key_spec: true },
      { name: 'Storage Type', category: 'storage', unit: '', is_key_spec: true },
      { name: 'Storage Capacity', category: 'storage', unit: 'GB', is_key_spec: true },
      { name: 'Display Size', category: 'display', unit: 'inches', is_key_spec: true },
      { name: 'Display Resolution', category: 'display', unit: '', is_key_spec: true },
      { name: 'Graphics Card', category: 'graphics', unit: '', is_key_spec: true },
      { name: 'Operating System', category: 'software', unit: '', is_key_spec: true },
      { name: 'Battery Life', category: 'battery', unit: 'hours', is_key_spec: true },
      { name: 'Weight', category: 'dimensions', unit: 'kg', is_key_spec: true },
      { name: 'Ports', category: 'ports', unit: '', is_key_spec: false },
      { name: 'Keyboard Backlight', category: 'build', unit: '', is_key_spec: false },
      { name: 'Webcam', category: 'camera', unit: 'MP', is_key_spec: false }
    ]
  },

  desktop: {
    required: ['processor', 'ram', 'storage', 'graphics', 'motherboard'],
    optional: ['cooling', 'power_supply', 'case_type', 'ports'],
    specifications: [
      { name: 'Processor', category: 'processor', unit: '', is_key_spec: true },
      { name: 'RAM', category: 'memory', unit: 'GB', is_key_spec: true },
      { name: 'Storage Type', category: 'storage', unit: '', is_key_spec: true },
      { name: 'Storage Capacity', category: 'storage', unit: 'GB', is_key_spec: true },
      { name: 'Graphics Card', category: 'graphics', unit: '', is_key_spec: true },
      { name: 'Motherboard', category: 'motherboard', unit: '', is_key_spec: true },
      { name: 'Power Supply', category: 'power_output', unit: 'W', is_key_spec: true },
      { name: 'Cooling System', category: 'cooling', unit: '', is_key_spec: false },
      { name: 'Case Type', category: 'build', unit: '', is_key_spec: false },
      { name: 'Ports', category: 'ports', unit: '', is_key_spec: false }
    ]
  },

  // Gaming
  gaming_console: {
    required: ['platform', 'storage', 'resolution', 'processor'],
    optional: ['backwards_compatibility', 'online_service', 'controller_included'],
    specifications: [
      { name: 'Platform', category: 'gaming_performance', unit: '', is_key_spec: true },
      { name: 'Processor', category: 'performance', unit: '', is_key_spec: true },
      { name: 'Storage', category: 'storage', unit: 'GB', is_key_spec: true },
      { name: 'Max Resolution', category: 'display', unit: '', is_key_spec: true },
      { name: 'Max Frame Rate', category: 'gaming_performance', unit: 'fps', is_key_spec: true },
      { name: 'Backwards Compatibility', category: 'compatibility', unit: '', is_key_spec: false },
      { name: 'Online Service', category: 'online_features', unit: '', is_key_spec: false },
      { name: 'Controller Included', category: 'accessories', unit: '', is_key_spec: false }
    ]
  },

  // Audio/Visual
  camera: {
    required: ['sensor_type', 'megapixels', 'lens_mount', 'video_recording'],
    optional: ['image_stabilization', 'battery_life', 'weather_sealing'],
    specifications: [
      { name: 'Sensor Type', category: 'camera', unit: '', is_key_spec: true },
      { name: 'Megapixels', category: 'camera', unit: 'MP', is_key_spec: true },
      { name: 'Lens Mount', category: 'lens', unit: '', is_key_spec: true },
      { name: 'Video Recording', category: 'video', unit: '', is_key_spec: true },
      { name: 'Image Stabilization', category: 'stabilization', unit: '', is_key_spec: true },
      { name: 'Battery Life', category: 'battery', unit: 'shots', is_key_spec: false },
      { name: 'Weather Sealing', category: 'build', unit: '', is_key_spec: false }
    ]
  },

  monitor: {
    required: ['display_size', 'resolution', 'refresh_rate', 'panel_type'],
    optional: ['response_time', 'color_accuracy', 'ports', 'adjustable_stand'],
    specifications: [
      { name: 'Display Size', category: 'display', unit: 'inches', is_key_spec: true },
      { name: 'Resolution', category: 'display', unit: '', is_key_spec: true },
      { name: 'Refresh Rate', category: 'display', unit: 'Hz', is_key_spec: true },
      { name: 'Panel Type', category: 'display', unit: '', is_key_spec: true },
      { name: 'Response Time', category: 'display', unit: 'ms', is_key_spec: true },
      { name: 'Color Accuracy', category: 'display', unit: '%', is_key_spec: false },
      { name: 'Ports', category: 'ports', unit: '', is_key_spec: false },
      { name: 'Adjustable Stand', category: 'build', unit: '', is_key_spec: false }
    ]
  },

  // Networking
  router: {
    required: ['wifi_standard', 'max_speed', 'frequency_bands', 'ethernet_ports'],
    optional: ['range', 'antenna_count', 'security_protocols', 'guest_network'],
    specifications: [
      { name: 'WiFi Standard', category: 'wireless', unit: '', is_key_spec: true },
      { name: 'Max Speed', category: 'speed', unit: 'Mbps', is_key_spec: true },
      { name: 'Frequency Bands', category: 'wireless', unit: '', is_key_spec: true },
      { name: 'Ethernet Ports', category: 'ethernet', unit: '', is_key_spec: true },
      { name: 'Range', category: 'range', unit: 'm', is_key_spec: true },
      { name: 'Antenna Count', category: 'wireless', unit: '', is_key_spec: false },
      { name: 'Security Protocols', category: 'security', unit: '', is_key_spec: false },
      { name: 'Guest Network', category: 'special_features', unit: '', is_key_spec: false }
    ]
  },

  // Storage
  external_drive: {
    required: ['capacity', 'interface', 'drive_type'],
    optional: ['transfer_speed', 'encryption', 'shock_resistance', 'warranty'],
    specifications: [
      { name: 'Capacity', category: 'storage', unit: 'GB', is_key_spec: true },
      { name: 'Interface', category: 'connectivity', unit: '', is_key_spec: true },
      { name: 'Drive Type', category: 'storage', unit: '', is_key_spec: true },
      { name: 'Transfer Speed', category: 'performance', unit: 'MB/s', is_key_spec: true },
      { name: 'Encryption', category: 'security', unit: '', is_key_spec: false },
      { name: 'Shock Resistance', category: 'build', unit: '', is_key_spec: false },
      { name: 'Warranty', category: 'warranty', unit: 'years', is_key_spec: false }
    ]
  },

  // Default for other categories
  other: {
    required: [],
    optional: ['dimensions', 'weight', 'material', 'color', 'warranty'],
    specifications: [
      { name: 'Dimensions', category: 'dimensions', unit: '', is_key_spec: false },
      { name: 'Weight', category: 'dimensions', unit: 'g', is_key_spec: false },
      { name: 'Material', category: 'build', unit: '', is_key_spec: false },
      { name: 'Color', category: 'build', unit: '', is_key_spec: false },
      { name: 'Warranty', category: 'warranty', unit: '', is_key_spec: false }
    ]
  }
};

/**
 * Get specification template for a category
 * @param {string} category - Product category
 * @returns {object} Specification template
 */
const getSpecificationTemplate = (category) => {
  return categorySpecifications[category] || categorySpecifications.other;
};

/**
 * Get all supported categories
 * @returns {array} Array of category names
 */
const getSupportedCategories = () => {
  return Object.keys(categorySpecifications);
};

/**
 * Validate specifications against category template
 * @param {string} category - Product category
 * @param {array} specifications - Product specifications
 * @returns {object} Validation result
 */
const validateSpecifications = (category, specifications) => {
  const template = getSpecificationTemplate(category);
  const specNames = specifications.map(spec => spec.name.toLowerCase().replace(/\s+/g, '_'));
  
  const missingRequired = template.required.filter(req => 
    !specNames.includes(req.toLowerCase())
  );
  
  return {
    isValid: missingRequired.length === 0,
    missingRequired,
    template
  };
};

module.exports = {
  categorySpecifications,
  getSpecificationTemplate,
  getSupportedCategories,
  validateSpecifications
};
