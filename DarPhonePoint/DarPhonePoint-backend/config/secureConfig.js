const secretsManager = require('../utils/secretsManager');
const logger = require('../utils/logger');

/**
 * Secure Configuration Manager
 * Handles loading and validation of sensitive configuration
 */
class SecureConfig {
  constructor() {
    this.config = {};
    this.loadConfiguration();
    this.validateConfiguration();
  }

  /**
   * Load configuration from environment variables
   */
  loadConfiguration() {
    // Database Configuration
    this.config.database = {
      uri: this.getSecureEnv('MONGODB_URI', 'mongodb://localhost:27017/aixcelerate'),
      options: {
        maxPoolSize: parseInt(this.getEnv('DB_MAX_POOL_SIZE', '10')),
        minPoolSize: parseInt(this.getEnv('DB_MIN_POOL_SIZE', '5')),
        maxIdleTimeMS: parseInt(this.getEnv('DB_MAX_IDLE_TIME', '30000')),
        serverSelectionTimeoutMS: parseInt(this.getEnv('DB_SERVER_SELECTION_TIMEOUT', '5000'))
      }
    };

    // JWT Configuration
    this.config.jwt = {
      secret: this.getSecureEnv('JWT_SECRET', this.generateSecureDefault('jwt')),
      expiresIn: this.getEnv('JWT_EXPIRE', '7d'),
      issuer: this.getEnv('JWT_ISSUER', 'aixcelerate-api'),
      audience: this.getEnv('JWT_AUDIENCE', 'aixcelerate-users')
    };

    // Session Configuration
    this.config.session = {
      secret: this.getSecureEnv('SESSION_SECRET', this.generateSecureDefault('session')),
      maxAge: parseInt(this.getEnv('SESSION_MAX_AGE', '86400000')), // 24 hours
      secure: process.env.NODE_ENV === 'production',
      httpOnly: true,
      sameSite: 'strict'
    };

    // Redis Configuration
    this.config.redis = {
      host: this.getEnv('REDIS_HOST', 'localhost'),
      port: parseInt(this.getEnv('REDIS_PORT', '6379')),
      password: this.getSecureEnv('REDIS_PASSWORD', ''),
      db: parseInt(this.getEnv('REDIS_DB', '0')),
      keyPrefix: this.getEnv('REDIS_KEY_PREFIX', 'aixcelerate:'),
      retryDelayOnFailover: parseInt(this.getEnv('REDIS_RETRY_DELAY', '100')),
      maxRetriesPerRequest: parseInt(this.getEnv('REDIS_MAX_RETRIES', '3'))
    };

    // Email Configuration
    this.config.email = {
      host: this.getEnv('EMAIL_HOST', 'smtp.gmail.com'),
      port: parseInt(this.getEnv('EMAIL_PORT', '587')),
      secure: this.getEnv('EMAIL_SECURE', 'false') === 'true',
      user: this.getEnv('EMAIL_USER', ''),
      password: this.getSecureEnv('EMAIL_PASSWORD', ''),
      from: this.getEnv('EMAIL_FROM', '<EMAIL>')
    };

    // Whop Configuration
    this.config.whop = {
      apiKey: this.getSecureEnv('WHOP_API_KEY', ''),
      appId: this.getEnv('WHOP_APP_ID', ''),
      agentUserId: this.getEnv('WHOP_AGENT_USER_ID', ''),
      webhookSecret: this.getSecureEnv('WHOP_WEBHOOK_SECRET', ''),
      baseUrl: this.getEnv('WHOP_BASE_URL', 'https://api.whop.com/v1')
    };

    // Security Configuration
    this.config.security = {
      bcryptRounds: parseInt(this.getEnv('BCRYPT_ROUNDS', '12')),
      corsOrigins: this.getEnv('CORS_ORIGIN', 'http://localhost:5173').split(','),
      trustProxy: this.getEnv('TRUST_PROXY', 'false') === 'true',
      rateLimitWindow: parseInt(this.getEnv('RATE_LIMIT_WINDOW', '900000')), // 15 minutes
      rateLimitMax: parseInt(this.getEnv('RATE_LIMIT_MAX', '100')),
      encryptionKey: this.getSecureEnv('ENCRYPTION_KEY', this.generateSecureDefault('encryption'))
    };

    // Application Configuration
    this.config.app = {
      name: this.getEnv('APP_NAME', 'AIXcelerate API'),
      version: this.getEnv('APP_VERSION', '1.0.0'),
      environment: this.getEnv('NODE_ENV', 'development'),
      port: parseInt(this.getEnv('PORT', '5000')),
      baseUrl: this.getEnv('BASE_URL', 'http://localhost:5000'),
      frontendUrl: this.getEnv('FRONTEND_URL', 'http://localhost:5173'),
      logLevel: this.getEnv('LOG_LEVEL', 'info'),
      enableSwagger: this.getEnv('ENABLE_SWAGGER', 'true') === 'true'
    };

    // File Upload Configuration
    this.config.upload = {
      maxFileSize: parseInt(this.getEnv('MAX_FILE_SIZE', '10485760')), // 10MB
      allowedTypes: this.getEnv('ALLOWED_FILE_TYPES', 'pdf,zip,jpg,png').split(','),
      uploadPath: this.getEnv('UPLOAD_PATH', './public/uploads'),
      tempPath: this.getEnv('TEMP_PATH', './temp')
    };

    logger.info('Secure configuration loaded successfully');
  }

  /**
   * Get environment variable with fallback
   */
  getEnv(key, defaultValue = '') {
    return process.env[key] || defaultValue;
  }

  /**
   * Get secure environment variable (sensitive data)
   */
  getSecureEnv(key, defaultValue = '') {
    const value = process.env[key] || defaultValue;
    
    // Log warning if using default for sensitive data in production
    if (value === defaultValue && defaultValue && process.env.NODE_ENV === 'production') {
      logger.warn(`Using default value for sensitive configuration: ${key}`);
    }
    
    return value;
  }

  /**
   * Generate secure default for development
   */
  generateSecureDefault(type) {
    if (process.env.NODE_ENV === 'production') {
      throw new Error(`Secure default cannot be generated in production for ${type}`);
    }
    
    const defaults = {
      jwt: 'your-super-secret-jwt-key-for-development-only',
      session: 'your-session-secret-for-development-only',
      encryption: 'your-encryption-key-for-development-only'
    };
    
    return defaults[type] || secretsManager.generateToken(32);
  }

  /**
   * Validate configuration
   */
  validateConfiguration() {
    const validation = secretsManager.validateEnvironmentSecurity();
    
    if (!validation.isSecure) {
      logger.error('Security validation failed:', validation.issues);
      
      if (process.env.NODE_ENV === 'production') {
        throw new Error('Production deployment blocked due to security issues');
      } else {
        logger.warn('Development environment has security issues that must be fixed before production');
      }
    }
    
    if (validation.warnings.length > 0) {
      validation.warnings.forEach(warning => logger.warn(warning));
    }

    // Validate required configurations
    this.validateRequiredConfig();
    
    logger.info('Configuration validation completed');
  }

  /**
   * Validate required configuration values
   */
  validateRequiredConfig() {
    const required = [
      { path: 'database.uri', name: 'Database URI' },
      { path: 'jwt.secret', name: 'JWT Secret' },
      { path: 'app.port', name: 'Application Port' }
    ];

    required.forEach(({ path, name }) => {
      const value = this.getConfigValue(path);
      if (!value) {
        throw new Error(`Required configuration missing: ${name} (${path})`);
      }
    });
  }

  /**
   * Get configuration value by path
   */
  getConfigValue(path) {
    return path.split('.').reduce((obj, key) => obj && obj[key], this.config);
  }

  /**
   * Get all configuration (sanitized for logging)
   */
  getSanitizedConfig() {
    return secretsManager.sanitizeEnvForLogging(this.config);
  }

  /**
   * Get specific configuration section
   */
  get(section) {
    return this.config[section];
  }

  /**
   * Check if running in production
   */
  isProduction() {
    return this.config.app.environment === 'production';
  }

  /**
   * Check if running in development
   */
  isDevelopment() {
    return this.config.app.environment === 'development';
  }

  /**
   * Check if running in test
   */
  isTest() {
    return this.config.app.environment === 'test';
  }
}

// Export singleton instance
module.exports = new SecureConfig();
