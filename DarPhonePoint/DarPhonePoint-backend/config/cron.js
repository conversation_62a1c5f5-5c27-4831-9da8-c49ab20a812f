/**
 * Cron Job Configuration
 *
 * This file contains the configuration for all cron jobs in the application.
 * It uses node-cron to schedule tasks.
 */

const cron = require('node-cron');
const path = require('path');
const { exec } = require('child_process');
const logger = require('../utils/logger');

// Base directory for scripts
const scriptsDir = path.join(__dirname, '../scripts');

/**
 * Execute a script with node
 * @param {string} scriptName - Name of the script file
 * @returns {Promise<void>}
 */
const executeScript = (scriptName) => {
  return new Promise((resolve, reject) => {
    const scriptPath = path.join(scriptsDir, scriptName);
    logger.info(`Executing script: ${scriptPath}`);

    exec(`node "${scriptPath}"`, (error, stdout, stderr) => {
      if (error) {
        logger.error(`Error executing ${scriptName}: ${error.message}`);
        logger.error(stderr);
        reject(error);
        return;
      }

      logger.info(`${scriptName} output: ${stdout}`);
      resolve();
    });
  });
};

/**
 * Initialize all cron jobs
 */
const initCronJobs = () => {
  logger.info('Initializing cron jobs...');

  // Process pending payments every 5 minutes
  cron.schedule('*/5 * * * *', async () => {
    logger.info('Running pending payments processing job');
    try {
      await executeScript('processPendingPayments.js');
    } catch (error) {
      logger.error('Failed to process pending payments');
    }
  });

  // Process email sequences every hour
  cron.schedule('0 * * * *', async () => {
    logger.info('Running email sequence processing job');
    try {
      await executeScript('processEmailSequences.js');
    } catch (error) {
      logger.error('Failed to process email sequences');
    }
  });

  // Process inactive users once a day at 2 AM
  cron.schedule('0 2 * * *', async () => {
    logger.info('Running inactive user processing job');
    try {
      await executeScript('processInactiveUsers.js');
    } catch (error) {
      logger.error('Failed to process inactive users');
    }
  });

  // Clean up old analytics data once a week on Sunday at 4 AM
  cron.schedule('0 4 * * 0', async () => {
    logger.info('Running analytics cleanup job');
    try {
      await executeScript('cleanupAnalytics.js');
    } catch (error) {
      logger.error('Failed to clean up analytics');
    }
  });

  logger.info('All cron jobs initialized');
};

module.exports = {
  initCronJobs
};
