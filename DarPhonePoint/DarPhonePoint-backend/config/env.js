require('dotenv').config();

const env = {
  // Server
  NODE_ENV: process.env.NODE_ENV || 'development',
  PORT: parseInt(process.env.PORT, 10) || 5001,
  HOST: process.env.HOST || '0.0.0.0',

  // Database
  MONGODB_URI: process.env.MONGODB_URI,
  MONGODB_USER: process.env.MONGODB_USER,
  MONGODB_PASSWORD: process.env.MONGODB_PASSWORD,

  // JWT
  JWT_SECRET: process.env.JWT_SECRET,
  JWT_EXPIRES_IN: process.env.JWT_EXPIRES_IN || '24h',
  JWT_REFRESH_EXPIRES_IN: process.env.JWT_REFRESH_EXPIRES_IN || '7d',

  // Redis
  REDIS_HOST: process.env.REDIS_HOST || 'localhost',
  REDIS_PORT: parseInt(process.env.REDIS_PORT, 10) || 6379,
  REDIS_PASSWORD: process.env.REDIS_PASSWORD,

  // ClickPesa Payment (Tanzania)
  CLICKPESA_CLIENT_ID: process.env.CLICKPESA_CLIENT_ID,
  CLICKPESA_API_KEY: process.env.CLICKPESA_API_KEY,
  CLICKPESA_API_URL: process.env.CLICKPESA_API_URL || 'https://api.clickpesa.com',
  CLICKPESA_WEBHOOK_SECRET: process.env.CLICKPESA_WEBHOOK_SECRET,
  CLICKPESA_BILLPAY_NAMBA: process.env.CLICKPESA_BILLPAY_NAMBA,

  // Frontend
  FRONTEND_URL: process.env.FRONTEND_URL || 'http://localhost:5173',
  BASE_URL: process.env.BASE_URL || 'http://localhost:5001',
  API_URL: process.env.API_URL || 'http://localhost:5001',
  DOMAIN: process.env.DOMAIN || 'localhost',

  // Email
  EMAIL_HOST: process.env.EMAIL_HOST,
  EMAIL_PORT: parseInt(process.env.EMAIL_PORT, 10) || 587,
  EMAIL_SECURE: process.env.EMAIL_SECURE === 'true',
  EMAIL_USER: process.env.EMAIL_USER,
  EMAIL_PASSWORD: process.env.EMAIL_PASSWORD,
  EMAIL_FROM: process.env.EMAIL_FROM,

  // Security
  BCRYPT_ROUNDS: parseInt(process.env.BCRYPT_ROUNDS, 10) || 12,
  RATE_LIMIT_WINDOW_MS: parseInt(process.env.RATE_LIMIT_WINDOW_MS, 10) || 900000,
  RATE_LIMIT_MAX: parseInt(process.env.RATE_LIMIT_MAX, 10) || 100,
  SESSION_SECRET: process.env.SESSION_SECRET,
  CORS_ORIGIN: process.env.CORS_ORIGIN?.split(',') || ['http://localhost:5173'],
  TRUST_PROXY: process.env.TRUST_PROXY === 'true',
  SECURE_COOKIES: process.env.SECURE_COOKIES === 'true',
  HELMET_CSP_NONCE: process.env.HELMET_CSP_NONCE === 'true',

  // Logging
  LOG_LEVEL: process.env.LOG_LEVEL || 'info',
  REQUEST_LOGGING: process.env.REQUEST_LOGGING === 'true',
  PERFORMANCE_LOGGING: process.env.PERFORMANCE_LOGGING === 'true',
};

// Validate required environment variables for Phone Point Dar
const requiredEnvVars = [
  'MONGODB_URI',
  'JWT_SECRET',
  'EMAIL_HOST',
  'EMAIL_USER',
  'EMAIL_PASSWORD'
  // ClickPesa credentials are optional for development
  // 'CLICKPESA_CLIENT_ID',
  // 'CLICKPESA_API_KEY'
];

const missingEnvVars = requiredEnvVars.filter(envVar => !env[envVar]);

if (missingEnvVars.length > 0) {
  throw new Error(`Missing required environment variables: ${missingEnvVars.join(', ')}`);
}

module.exports = env;