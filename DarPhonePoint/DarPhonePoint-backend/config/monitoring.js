const Sentry = require('@sentry/node');
const { ProfilingIntegration } = require('@sentry/profiling-node');
const env = require('./env');
const logger = require('../utils/logger');

const initializeMonitoring = () => {
  // Initialize Sentry
  if (env.SENTRY_DSN) {
    Sentry.init({
      dsn: env.SENTRY_DSN,
      environment: env.NODE_ENV,
      integrations: [
        new ProfilingIntegration(),
      ],
      // Performance Monitoring
      tracesSampleRate: 1.0,
      // Set sampling rate for profiling
      profilesSampleRate: 1.0,
    });

    logger.info('Sentry monitoring initialized');
  }

  // Global error handler
  process.on('uncaughtException', (error) => {
    logger.error('Uncaught Exception:', error);
    Sentry.captureException(error);
    process.exit(1);
  });

  process.on('unhandledRejection', (reason, promise) => {
    logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
    Sentry.captureException(reason);
  });

  // Memory monitoring
  const memoryUsage = () => {
    const used = process.memoryUsage();
    logger.info('Memory Usage:', {
      rss: `${Math.round(used.rss / 1024 / 1024)}MB`,
      heapTotal: `${Math.round(used.heapTotal / 1024 / 1024)}MB`,
      heapUsed: `${Math.round(used.heapUsed / 1024 / 1024)}MB`,
      external: `${Math.round(used.external / 1024 / 1024)}MB`,
    });
  };

  // Monitor memory usage every 5 minutes
  setInterval(memoryUsage, 5 * 60 * 1000);

  // Initial memory usage
  memoryUsage();
};

module.exports = initializeMonitoring; 