/**
 * Load Testing Script for Phone Point Dar
 * Tests application performance under various load conditions
 */

import http from 'k6/http';
import { check, sleep } from 'k6';
import { Rate, Trend } from 'k6/metrics';

// Custom metrics
const errorRate = new Rate('errors');
const responseTime = new Trend('response_time');

// Test configuration
export const options = {
  stages: [
    // Ramp up
    { duration: '2m', target: 10 },   // Ramp up to 10 users over 2 minutes
    { duration: '5m', target: 10 },   // Stay at 10 users for 5 minutes
    { duration: '2m', target: 20 },   // Ramp up to 20 users over 2 minutes
    { duration: '5m', target: 20 },   // Stay at 20 users for 5 minutes
    { duration: '2m', target: 50 },   // Ramp up to 50 users over 2 minutes
    { duration: '5m', target: 50 },   // Stay at 50 users for 5 minutes
    { duration: '2m', target: 0 },    // Ramp down to 0 users
  ],
  thresholds: {
    http_req_duration: ['p(95)<2000'], // 95% of requests must complete below 2s
    http_req_failed: ['rate<0.05'],    // Error rate must be below 5%
    errors: ['rate<0.05'],             // Custom error rate below 5%
  },
};

// Base URL configuration
const BASE_URL = __ENV.API_BASE_URL || 'http://localhost:5000';

// Test data
const testUsers = [
  { email: '<EMAIL>', password: 'testpassword123' },
  { email: '<EMAIL>', password: 'testpassword123' },
  { email: '<EMAIL>', password: 'testpassword123' },
];

let authToken = '';

// Setup function - runs once before the test
export function setup() {
  console.log('Setting up load test for Phone Point Dar...');
  
  // Create test users if they don't exist
  testUsers.forEach(user => {
    const registerResponse = http.post(`${BASE_URL}/api/auth/register`, JSON.stringify({
      name: `Test User ${user.email}`,
      email: user.email,
      password: user.password,
      phone: '+255123456789'
    }), {
      headers: { 'Content-Type': 'application/json' },
    });
    
    // Ignore if user already exists
    if (registerResponse.status !== 201 && registerResponse.status !== 400) {
      console.error(`Failed to create test user: ${user.email}`);
    }
  });
  
  return { testUsers };
}

// Main test function
export default function(data) {
  const user = data.testUsers[Math.floor(Math.random() * data.testUsers.length)];
  
  // Test scenarios with different weights
  const scenario = Math.random();
  
  if (scenario < 0.3) {
    // 30% - Browse products (most common)
    browseProducts();
  } else if (scenario < 0.5) {
    // 20% - Search products
    searchProducts();
  } else if (scenario < 0.7) {
    // 20% - User authentication flow
    authenticateUser(user);
  } else if (scenario < 0.9) {
    // 20% - Complete order flow
    completeOrderFlow(user);
  } else {
    // 10% - Admin operations
    adminOperations(user);
  }
  
  sleep(1); // Think time between requests
}

// Test scenario: Browse products
function browseProducts() {
  const responses = http.batch([
    ['GET', `${BASE_URL}/api/products`],
    ['GET', `${BASE_URL}/api/products/categories`],
    ['GET', `${BASE_URL}/api/products/brands`],
    ['GET', `${BASE_URL}/api/products/featured`],
  ]);
  
  responses.forEach(response => {
    const success = check(response, {
      'browse products - status is 200': (r) => r.status === 200,
      'browse products - response time < 1s': (r) => r.timings.duration < 1000,
    });
    
    errorRate.add(!success);
    responseTime.add(response.timings.duration);
  });
}

// Test scenario: Search products
function searchProducts() {
  const searchTerms = ['iPhone', 'Samsung', 'Xiaomi', 'smartphone', 'phone'];
  const searchTerm = searchTerms[Math.floor(Math.random() * searchTerms.length)];
  
  const response = http.get(`${BASE_URL}/api/products?search=${searchTerm}&limit=20`);
  
  const success = check(response, {
    'search products - status is 200': (r) => r.status === 200,
    'search products - response time < 1.5s': (r) => r.timings.duration < 1500,
    'search products - has results': (r) => {
      try {
        const data = JSON.parse(r.body);
        return data.data && data.data.products && Array.isArray(data.data.products);
      } catch (e) {
        return false;
      }
    },
  });
  
  errorRate.add(!success);
  responseTime.add(response.timings.duration);
}

// Test scenario: User authentication
function authenticateUser(user) {
  // Login
  const loginResponse = http.post(`${BASE_URL}/api/auth/login`, JSON.stringify({
    email: user.email,
    password: user.password,
  }), {
    headers: { 'Content-Type': 'application/json' },
  });
  
  const loginSuccess = check(loginResponse, {
    'login - status is 200': (r) => r.status === 200,
    'login - response time < 2s': (r) => r.timings.duration < 2000,
    'login - has token': (r) => {
      try {
        const data = JSON.parse(r.body);
        return data.data && data.data.token;
      } catch (e) {
        return false;
      }
    },
  });
  
  if (loginSuccess && loginResponse.status === 200) {
    try {
      const loginData = JSON.parse(loginResponse.body);
      const token = loginData.data.token;
      
      // Get user profile
      const profileResponse = http.get(`${BASE_URL}/api/auth/me`, {
        headers: { 'Authorization': `Bearer ${token}` },
      });
      
      check(profileResponse, {
        'profile - status is 200': (r) => r.status === 200,
        'profile - response time < 1s': (r) => r.timings.duration < 1000,
      });
      
      responseTime.add(profileResponse.timings.duration);
    } catch (e) {
      errorRate.add(true);
    }
  }
  
  errorRate.add(!loginSuccess);
  responseTime.add(loginResponse.timings.duration);
}

// Test scenario: Complete order flow
function completeOrderFlow(user) {
  // Login first
  const loginResponse = http.post(`${BASE_URL}/api/auth/login`, JSON.stringify({
    email: user.email,
    password: user.password,
  }), {
    headers: { 'Content-Type': 'application/json' },
  });
  
  if (loginResponse.status !== 200) {
    errorRate.add(true);
    return;
  }
  
  try {
    const loginData = JSON.parse(loginResponse.body);
    const token = loginData.data.token;
    
    // Get products
    const productsResponse = http.get(`${BASE_URL}/api/products?limit=5`);
    if (productsResponse.status !== 200) {
      errorRate.add(true);
      return;
    }
    
    const productsData = JSON.parse(productsResponse.body);
    if (!productsData.data.products || productsData.data.products.length === 0) {
      errorRate.add(true);
      return;
    }
    
    const product = productsData.data.products[0];
    
    // Add to cart
    const cartResponse = http.post(`${BASE_URL}/api/cart/add`, JSON.stringify({
      productId: product._id,
      quantity: 1,
    }), {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
    });
    
    const cartSuccess = check(cartResponse, {
      'add to cart - status is 200': (r) => r.status === 200,
      'add to cart - response time < 1.5s': (r) => r.timings.duration < 1500,
    });
    
    if (cartSuccess) {
      // Create order
      const orderResponse = http.post(`${BASE_URL}/api/orders`, JSON.stringify({
        items: [{
          product: product._id,
          quantity: 1,
          price: product.price,
        }],
        payment_method: 'mpesa',
        shipping_address: {
          street: 'Test Street 123',
          city: 'Dar es Salaam',
          region: 'Dar es Salaam',
          postal_code: '12345',
          country: 'Tanzania',
        },
      }), {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
      });
      
      const orderSuccess = check(orderResponse, {
        'create order - status is 201': (r) => r.status === 201,
        'create order - response time < 2s': (r) => r.timings.duration < 2000,
      });
      
      errorRate.add(!orderSuccess);
      responseTime.add(orderResponse.timings.duration);
    }
    
    errorRate.add(!cartSuccess);
    responseTime.add(cartResponse.timings.duration);
    
  } catch (e) {
    errorRate.add(true);
  }
}

// Test scenario: Admin operations
function adminOperations(user) {
  // Try to access admin endpoints (should fail for regular users)
  const adminResponse = http.get(`${BASE_URL}/api/admin/dashboard`);
  
  check(adminResponse, {
    'admin access - unauthorized': (r) => r.status === 401 || r.status === 403,
    'admin access - response time < 1s': (r) => r.timings.duration < 1000,
  });
  
  responseTime.add(adminResponse.timings.duration);
}

// Teardown function - runs once after the test
export function teardown(data) {
  console.log('Load test completed for Phone Point Dar');
  console.log('Check the results for performance metrics and any issues');
}

// Handle test results
export function handleSummary(data) {
  return {
    'load-test-results.json': JSON.stringify(data, null, 2),
    stdout: `
📊 Phone Point Dar Load Test Results
=====================================

🎯 Test Summary:
- Total Requests: ${data.metrics.http_reqs.count}
- Failed Requests: ${data.metrics.http_req_failed.count} (${(data.metrics.http_req_failed.rate * 100).toFixed(2)}%)
- Average Response Time: ${data.metrics.http_req_duration.avg.toFixed(2)}ms
- 95th Percentile: ${data.metrics.http_req_duration['p(95)'].toFixed(2)}ms

🚀 Performance Metrics:
- Requests per second: ${data.metrics.http_reqs.rate.toFixed(2)}
- Data received: ${(data.metrics.data_received.count / 1024 / 1024).toFixed(2)} MB
- Data sent: ${(data.metrics.data_sent.count / 1024 / 1024).toFixed(2)} MB

✅ Thresholds:
${Object.entries(data.thresholds).map(([key, value]) => 
  `- ${key}: ${value.ok ? '✅ PASSED' : '❌ FAILED'}`
).join('\n')}

${data.metrics.http_req_failed.rate > 0.05 ? 
  '⚠️  WARNING: Error rate is above 5% threshold!' : 
  '✅ All error rates within acceptable limits'}

${data.metrics.http_req_duration['p(95)'] > 2000 ? 
  '⚠️  WARNING: 95th percentile response time is above 2s threshold!' : 
  '✅ Response times within acceptable limits'}
    `,
  };
}
