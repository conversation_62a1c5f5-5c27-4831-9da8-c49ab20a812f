/**
 * Test Setup Configuration for Phone Point Dar
 * Sets up test environment, database, and common utilities
 */

const mongoose = require('mongoose');
const { MongoMemoryServer } = require('mongodb-memory-server');
const request = require('supertest');
const app = require('../server');

// Global test variables
let mongoServer;
let testDb;

/**
 * Setup test database before all tests
 */
const setupTestDatabase = async () => {
  try {
    // Create in-memory MongoDB instance
    mongoServer = await MongoMemoryServer.create();
    const mongoUri = mongoServer.getUri();
    
    // Connect to test database
    await mongoose.connect(mongoUri, {
      useNewUrlParser: true,
      useUnifiedTopology: true
    });
    
    testDb = mongoose.connection.db;
    console.log('✅ Test database connected');
  } catch (error) {
    console.error('❌ Test database setup failed:', error);
    throw error;
  }
};

/**
 * Cleanup test database after all tests
 */
const cleanupTestDatabase = async () => {
  try {
    if (mongoose.connection.readyState !== 0) {
      await mongoose.disconnect();
    }
    
    if (mongoServer) {
      await mongoServer.stop();
    }
    
    console.log('✅ Test database cleaned up');
  } catch (error) {
    console.error('❌ Test database cleanup failed:', error);
  }
};

/**
 * Clear all collections before each test
 */
const clearDatabase = async () => {
  try {
    const collections = await testDb.listCollections().toArray();
    
    for (const collection of collections) {
      await testDb.collection(collection.name).deleteMany({});
    }
  } catch (error) {
    console.error('❌ Database clear failed:', error);
    throw error;
  }
};

/**
 * Create test user with specified role
 */
const createTestUser = async (userData = {}) => {
  const User = require('../models/User');
  const bcrypt = require('bcryptjs');
  
  const defaultUser = {
    name: 'Test User',
    email: '<EMAIL>',
    password: await bcrypt.hash('testpassword123', 12),
    role: 'customer',
    status: 'active',
    isEmailVerified: true,
    ...userData
  };
  
  const user = new User(defaultUser);
  await user.save();
  return user;
};

/**
 * Create test admin user
 */
const createTestAdmin = async () => {
  return createTestUser({
    name: 'Test Admin',
    email: '<EMAIL>',
    role: 'admin'
  });
};

/**
 * Create test product
 */
const createTestProduct = async (productData = {}) => {
  const Product = require('../models/Product');
  
  const defaultProduct = {
    name: 'Test iPhone 15',
    description: 'Test iPhone 15 description',
    price: 1500000, // TZS
    category: 'smartphones',
    brand: 'Apple',
    model: 'iPhone 15',
    specifications: {
      storage: '128GB',
      ram: '6GB',
      color: 'Blue',
      screen_size: '6.1 inch',
      camera: '48MP',
      battery: '3349mAh',
      operating_system: 'iOS 17'
    },
    is_active: true,
    is_featured: false,
    track_inventory: true,
    warranty_period: 12,
    ...productData
  };
  
  const product = new Product(defaultProduct);
  await product.save();
  return product;
};

/**
 * Create test order
 */
const createTestOrder = async (user, products = [], orderData = {}) => {
  const Order = require('../models/Order');
  
  // If no products provided, create a default one
  if (products.length === 0) {
    const product = await createTestProduct();
    products = [product];
  }
  
  const items = products.map(product => ({
    product: product._id,
    quantity: 1,
    price: product.price,
    name: product.name
  }));
  
  const total = items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
  
  const defaultOrder = {
    user: user._id,
    order_number: `ORD-${Date.now()}`,
    items,
    total,
    order_status: 'pending',
    payment_status: 'pending',
    payment_method: 'mpesa',
    shipping_address: {
      street: 'Test Street',
      city: 'Dar es Salaam',
      region: 'Dar es Salaam',
      postal_code: '12345',
      country: 'Tanzania'
    },
    ...orderData
  };
  
  const order = new Order(defaultOrder);
  await order.save();
  return order;
};

/**
 * Create test inventory
 */
const createTestInventory = async (product, inventoryData = {}) => {
  const Inventory = require('../models/Inventory');
  
  const defaultInventory = {
    product: product._id,
    quantity_available: 10,
    quantity_reserved: 0,
    reorder_level: 5,
    location: 'Main Warehouse',
    ...inventoryData
  };
  
  const inventory = new Inventory(defaultInventory);
  await inventory.save();
  return inventory;
};

/**
 * Create test serial number (IMEI)
 */
const createTestSerialNumber = async (product, customer = null, serialData = {}) => {
  const SerialNumber = require('../models/SerialNumber');
  
  const defaultSerial = {
    imei: `35${Math.floor(Math.random() * 1000000000000000)}`, // Generate random IMEI
    serial_number: `SN${Math.floor(Math.random() * 1000000000)}`,
    product: product._id,
    customer: customer?._id,
    status: 'available',
    condition: 'new',
    warrantyStatus: 'active',
    warrantyStartDate: new Date(),
    warrantyEndDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year
    ...serialData
  };
  
  const serialNumber = new SerialNumber(defaultSerial);
  await serialNumber.save();
  return serialNumber;
};

/**
 * Login user and get JWT token
 */
const loginUser = async (email = '<EMAIL>', password = 'testpassword123') => {
  const response = await request(app)
    .post('/api/auth/login')
    .send({ email, password })
    .expect(200);
    
  return response.body.token;
};

/**
 * Login admin and get JWT token
 */
const loginAdmin = async () => {
  return loginUser('<EMAIL>', 'testpassword123');
};

/**
 * Make authenticated request
 */
const authenticatedRequest = (token) => {
  return request(app).set('Authorization', `Bearer ${token}`);
};

/**
 * Test data generators
 */
const generateTestData = {
  user: (overrides = {}) => ({
    name: 'Test User',
    email: `test${Date.now()}@phonepointdar.com`,
    password: 'testpassword123',
    phone: '+255123456789',
    ...overrides
  }),
  
  product: (overrides = {}) => ({
    name: `Test Product ${Date.now()}`,
    description: 'Test product description',
    price: Math.floor(Math.random() * 1000000) + 100000,
    category: 'smartphones',
    brand: 'TestBrand',
    model: 'TestModel',
    ...overrides
  }),
  
  order: (userId, productIds = [], overrides = {}) => ({
    user: userId,
    items: productIds.map(id => ({
      product: id,
      quantity: 1,
      price: 500000
    })),
    total: productIds.length * 500000,
    order_status: 'pending',
    payment_status: 'pending',
    ...overrides
  })
};

module.exports = {
  setupTestDatabase,
  cleanupTestDatabase,
  clearDatabase,
  createTestUser,
  createTestAdmin,
  createTestProduct,
  createTestOrder,
  createTestInventory,
  createTestSerialNumber,
  loginUser,
  loginAdmin,
  authenticatedRequest,
  generateTestData,
  app
};
