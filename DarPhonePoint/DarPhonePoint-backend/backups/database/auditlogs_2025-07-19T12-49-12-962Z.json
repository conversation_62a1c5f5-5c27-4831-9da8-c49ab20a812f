[{"_id": "687a0095401003287d89c7c6", "user": "687a0083a5c95f28b2cf75b3", "action": "login", "resource_type": "auth", "resource_id": "687a0083a5c95f28b2cf75b3", "description": "User Phone Point Dar Admin (<EMAIL>) logged in", "previous_state": null, "new_state": null, "metadata": {"user_role": "admin", "user_type": "premium"}, "ip_address": "::1", "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "created_at": "2025-07-18T08:06:45.132Z", "__v": 0}, {"_id": "687a01f5401003287d89c823", "user": "687a0083a5c95f28b2cf75b3", "action": "login", "resource_type": "auth", "resource_id": "687a0083a5c95f28b2cf75b3", "description": "User Phone Point Dar Admin (<EMAIL>) logged in", "previous_state": null, "new_state": null, "metadata": {"user_role": "admin", "user_type": "premium"}, "ip_address": "::1", "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "created_at": "2025-07-18T08:12:37.118Z", "__v": 0}, {"_id": "687a81b7b19b893805709555", "user": "687a0083a5c95f28b2cf75b3", "action": "login", "resource_type": "auth", "resource_id": "687a0083a5c95f28b2cf75b3", "description": "User Phone Point Dar Admin (<EMAIL>) logged in", "previous_state": null, "new_state": null, "metadata": {"user_role": "admin", "user_type": "premium"}, "ip_address": "::1", "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3.1 Safari/605.1.15", "created_at": "2025-07-18T17:17:43.996Z", "__v": 0}, {"_id": "687a81eab19b89380570957b", "user": "687a0083a5c95f28b2cf75b3", "action": "login", "resource_type": "auth", "resource_id": "687a0083a5c95f28b2cf75b3", "description": "User Phone Point Dar Admin (<EMAIL>) logged in", "previous_state": null, "new_state": null, "metadata": {"user_role": "admin", "user_type": "premium"}, "ip_address": "::1", "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "created_at": "2025-07-18T17:18:34.634Z", "__v": 0}, {"_id": "687a84e1b19b893805709598", "user": "687a0083a5c95f28b2cf75b3", "action": "login", "resource_type": "auth", "resource_id": "687a0083a5c95f28b2cf75b3", "description": "User Phone Point Dar Admin (<EMAIL>) logged in", "previous_state": null, "new_state": null, "metadata": {"user_role": "admin", "user_type": "premium"}, "ip_address": "::1", "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "created_at": "2025-07-18T17:31:13.518Z", "__v": 0}, {"_id": "687a860cb19b8938057095b0", "user": "687a0083a5c95f28b2cf75b3", "action": "login", "resource_type": "auth", "resource_id": "687a0083a5c95f28b2cf75b3", "description": "User Phone Point Dar Admin (<EMAIL>) logged in", "previous_state": null, "new_state": null, "metadata": {"user_role": "admin", "user_type": "premium"}, "ip_address": "::1", "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "created_at": "2025-07-18T17:36:12.689Z", "__v": 0}, {"_id": "687a88c28232ab2cbde895ca", "user": "687a0083a5c95f28b2cf75b3", "action": "login", "resource_type": "auth", "resource_id": "687a0083a5c95f28b2cf75b3", "description": "User Phone Point Dar Admin (<EMAIL>) logged in", "previous_state": null, "new_state": null, "metadata": {"user_role": "admin", "user_type": "premium"}, "ip_address": "::1", "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "created_at": "2025-07-18T17:47:46.406Z", "__v": 0}, {"_id": "687a89b68232ab2cbde895fc", "user": "687a0083a5c95f28b2cf75b3", "action": "login", "resource_type": "auth", "resource_id": "687a0083a5c95f28b2cf75b3", "description": "User Phone Point Dar Admin (<EMAIL>) logged in", "previous_state": null, "new_state": null, "metadata": {"user_role": "admin", "user_type": "premium"}, "ip_address": "::1", "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "created_at": "2025-07-18T17:51:50.775Z", "__v": 0}, {"_id": "687a8aae8232ab2cbde89614", "user": "687a0083a5c95f28b2cf75b3", "action": "login", "resource_type": "auth", "resource_id": "687a0083a5c95f28b2cf75b3", "description": "User Phone Point Dar Admin (<EMAIL>) logged in", "previous_state": null, "new_state": null, "metadata": {"user_role": "admin", "user_type": "premium"}, "ip_address": "::1", "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "created_at": "2025-07-18T17:55:58.019Z", "__v": 0}, {"_id": "687a8b8f8232ab2cbde89630", "user": "687a0083a5c95f28b2cf75b3", "action": "login", "resource_type": "auth", "resource_id": "687a0083a5c95f28b2cf75b3", "description": "User Phone Point Dar Admin (<EMAIL>) logged in", "previous_state": null, "new_state": null, "metadata": {"user_role": "admin", "user_type": "premium"}, "ip_address": "::1", "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "created_at": "2025-07-18T17:59:43.913Z", "__v": 0}, {"_id": "687a8c668232ab2cbde89667", "user": "687a0083a5c95f28b2cf75b3", "action": "login", "resource_type": "auth", "resource_id": "687a0083a5c95f28b2cf75b3", "description": "User Phone Point Dar Admin (<EMAIL>) logged in", "previous_state": null, "new_state": null, "metadata": {"user_role": "admin", "user_type": "premium"}, "ip_address": "::1", "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "created_at": "2025-07-18T18:03:18.238Z", "__v": 0}, {"_id": "687a8ddb8232ab2cbde89695", "user": "687a0083a5c95f28b2cf75b3", "action": "login", "resource_type": "auth", "resource_id": "687a0083a5c95f28b2cf75b3", "description": "User Phone Point Dar Admin (<EMAIL>) logged in", "previous_state": null, "new_state": null, "metadata": {"user_role": "admin", "user_type": "premium"}, "ip_address": "::1", "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "created_at": "2025-07-18T18:09:31.120Z", "__v": 0}, {"_id": "687a8f3d8232ab2cbde89731", "user": "687a0083a5c95f28b2cf75b3", "action": "login", "resource_type": "auth", "resource_id": "687a0083a5c95f28b2cf75b3", "description": "User Phone Point Dar Admin (<EMAIL>) logged in", "previous_state": null, "new_state": null, "metadata": {"user_role": "admin", "user_type": "premium"}, "ip_address": "::1", "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "created_at": "2025-07-18T18:15:25.578Z", "__v": 0}, {"_id": "687a91de8232ab2cbde8978e", "user": "687a0083a5c95f28b2cf75b3", "action": "login", "resource_type": "auth", "resource_id": "687a0083a5c95f28b2cf75b3", "description": "User Phone Point Dar Admin (<EMAIL>) logged in", "previous_state": null, "new_state": null, "metadata": {"user_role": "admin", "user_type": "premium"}, "ip_address": "::1", "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "created_at": "2025-07-18T18:26:38.217Z", "__v": 0}, {"_id": "687a92558232ab2cbde898f0", "user": "687a0083a5c95f28b2cf75b3", "action": "login", "resource_type": "auth", "resource_id": "687a0083a5c95f28b2cf75b3", "description": "User Phone Point Dar Admin (<EMAIL>) logged in", "previous_state": null, "new_state": null, "metadata": {"user_role": "admin", "user_type": "premium"}, "ip_address": "::1", "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "created_at": "2025-07-18T18:28:37.760Z", "__v": 0}, {"_id": "687a94408232ab2cbde89b27", "user": "687a0083a5c95f28b2cf75b3", "action": "login", "resource_type": "auth", "resource_id": "687a0083a5c95f28b2cf75b3", "description": "User Phone Point Dar Admin (<EMAIL>) logged in", "previous_state": null, "new_state": null, "metadata": {"user_role": "admin", "user_type": "premium"}, "ip_address": "::1", "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "created_at": "2025-07-18T18:36:48.413Z", "__v": 0}, {"_id": "687a951b8232ab2cbde89b72", "user": "687a0083a5c95f28b2cf75b3", "action": "login", "resource_type": "auth", "resource_id": "687a0083a5c95f28b2cf75b3", "description": "User Phone Point Dar Admin (<EMAIL>) logged in", "previous_state": null, "new_state": null, "metadata": {"user_role": "admin", "user_type": "premium"}, "ip_address": "::1", "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "created_at": "2025-07-18T18:40:27.868Z", "__v": 0}, {"_id": "687a95518232ab2cbde89ba2", "user": "687a0083a5c95f28b2cf75b3", "action": "login", "resource_type": "auth", "resource_id": "687a0083a5c95f28b2cf75b3", "description": "User Phone Point Dar Admin (<EMAIL>) logged in", "previous_state": null, "new_state": null, "metadata": {"user_role": "admin", "user_type": "premium"}, "ip_address": "::1", "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "created_at": "2025-07-18T18:41:21.637Z", "__v": 0}, {"_id": "687a95e48232ab2cbde89bf2", "user": "687a0083a5c95f28b2cf75b3", "action": "login", "resource_type": "auth", "resource_id": "687a0083a5c95f28b2cf75b3", "description": "User Phone Point Dar Admin (<EMAIL>) logged in", "previous_state": null, "new_state": null, "metadata": {"user_role": "admin", "user_type": "premium"}, "ip_address": "::1", "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "created_at": "2025-07-18T18:43:48.166Z", "__v": 0}, {"_id": "687b6200b8ab4a672af1a282", "user": "687a0083a5c95f28b2cf75b3", "action": "login", "resource_type": "auth", "resource_id": "687a0083a5c95f28b2cf75b3", "description": "User Phone Point Dar Admin (<EMAIL>) logged in", "previous_state": null, "new_state": null, "metadata": {"user_role": "admin", "user_type": "premium"}, "ip_address": "::1", "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3.1 Safari/605.1.15", "created_at": "2025-07-19T09:14:40.876Z", "__v": 0}, {"_id": "687b6252b8ab4a672af1a2fe", "user": "687a0083a5c95f28b2cf75b3", "action": "login", "resource_type": "auth", "resource_id": "687a0083a5c95f28b2cf75b3", "description": "User Phone Point Dar Admin (<EMAIL>) logged in", "previous_state": null, "new_state": null, "metadata": {"user_role": "admin", "user_type": "premium"}, "ip_address": "::1", "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3.1 Safari/605.1.15", "created_at": "2025-07-19T09:16:02.219Z", "__v": 0}, {"_id": "687b6276b8ab4a672af1a312", "user": "687a0083a5c95f28b2cf75b3", "action": "login", "resource_type": "auth", "resource_id": "687a0083a5c95f28b2cf75b3", "description": "User Phone Point Dar Admin (<EMAIL>) logged in", "previous_state": null, "new_state": null, "metadata": {"user_role": "admin", "user_type": "premium"}, "ip_address": "::1", "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3.1 Safari/605.1.15", "created_at": "2025-07-19T09:16:38.281Z", "__v": 0}, {"_id": "687b6322b8ab4a672af1a381", "user": "687a0083a5c95f28b2cf75b3", "action": "login", "resource_type": "auth", "resource_id": "687a0083a5c95f28b2cf75b3", "description": "User Phone Point Dar Admin (<EMAIL>) logged in", "previous_state": null, "new_state": null, "metadata": {"user_role": "admin", "user_type": "premium"}, "ip_address": "::1", "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3.1 Safari/605.1.15", "created_at": "2025-07-19T09:19:30.633Z", "__v": 0}, {"_id": "687b633eb8ab4a672af1a38d", "user": "687a0083a5c95f28b2cf75b3", "action": "login", "resource_type": "auth", "resource_id": "687a0083a5c95f28b2cf75b3", "description": "User Phone Point Dar Admin (<EMAIL>) logged in", "previous_state": null, "new_state": null, "metadata": {"user_role": "admin", "user_type": "premium"}, "ip_address": "::1", "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3.1 Safari/605.1.15", "created_at": "2025-07-19T09:19:58.038Z", "__v": 0}, {"_id": "687b6369b8ab4a672af1a397", "user": "687a0083a5c95f28b2cf75b3", "action": "login", "resource_type": "auth", "resource_id": "687a0083a5c95f28b2cf75b3", "description": "User Phone Point Dar Admin (<EMAIL>) logged in", "previous_state": null, "new_state": null, "metadata": {"user_role": "admin", "user_type": "premium"}, "ip_address": "::1", "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "created_at": "2025-07-19T09:20:41.218Z", "__v": 0}, {"_id": "687b6606b8ab4a672af1a3a7", "user": "687a0083a5c95f28b2cf75b3", "action": "login", "resource_type": "auth", "resource_id": "687a0083a5c95f28b2cf75b3", "description": "User Phone Point Dar Admin (<EMAIL>) logged in", "previous_state": null, "new_state": null, "metadata": {"user_role": "admin", "user_type": "premium"}, "ip_address": "::1", "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "created_at": "2025-07-19T09:31:50.573Z", "__v": 0}, {"_id": "687b6683b8ab4a672af1a3e7", "user": "687a0083a5c95f28b2cf75b3", "action": "login", "resource_type": "auth", "resource_id": "687a0083a5c95f28b2cf75b3", "description": "User Phone Point Dar Admin (<EMAIL>) logged in", "previous_state": null, "new_state": null, "metadata": {"user_role": "admin", "user_type": "premium"}, "ip_address": "::1", "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "created_at": "2025-07-19T09:33:55.267Z", "__v": 0}]