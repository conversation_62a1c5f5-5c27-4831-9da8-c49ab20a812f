const express = require('express');
const connectDB = require('./config/db');
const env = require('./config/env');
const cors = require('cors');
const helmet = require('helmet');
const path = require('path');
const morgan = require('morgan');
const errorHandler = require('./middleware/errorHandler');
const logger = require('./utils/logger');
const { initCronJobs } = require('./config/cron');
const { swaggerUi, swaggerDocs } = require('./config/swagger');
const warmupCache = require('./utils/cacheWarmup');
const performanceMiddleware = require('./middleware/performanceMiddleware');
const { addCorrelationId, logRequestPerformance, logErrorContext } = require('./middleware/requestLogger');
const { apiLimiter, authLimiter, paymentLimiter, adminLimiter, webhookLimiter } = require('./middleware/rateLimiter');
const {
  preventMongoInjection,
  enhancedXSSProtection,
  requestSizeLimiter,
  detectSuspiciousActivity
} = require('./middleware/inputValidation');
const https = require('https');
const http = require('http');
const fs = require('fs');
const mongoose = require('mongoose');
const passport = require('./config/passport');
const session = require('express-session');
const config = require('./config/config');

// Production optimization services
const advancedCacheService = require('./services/advancedCacheService');
const enhancedMonitoringService = require('./services/enhancedMonitoringService');
const databaseOptimizationService = require('./services/databaseOptimizationService');
const cdnService = require('./services/cdnService');
const disasterRecoveryService = require('./services/disasterRecoveryService');
const backupSchedulerService = require('./services/backupSchedulerService');

// Initialize express app
const app = express();

// Connect to database
connectDB();

// Initialize production optimization services
if (env.NODE_ENV === 'production') {
  logger.info('Initializing production optimization services...');

  // Initialize enhanced monitoring
  enhancedMonitoringService.initialize().then(health => {
    logger.info('Enhanced monitoring service status:', health);
  }).catch(error => {
    logger.error('Enhanced monitoring service initialization error:', error);
  });

  // Initialize disaster recovery
  disasterRecoveryService.initialize().then(health => {
    logger.info('Disaster recovery service status:', health);
  }).catch(error => {
    logger.error('Disaster recovery service initialization error:', error);
  });

  // Initialize advanced caching
  advancedCacheService.healthCheck().then(health => {
    logger.info('Advanced cache service status:', health);
  }).catch(error => {
    logger.error('Advanced cache service initialization error:', error);
  });

  // Initialize enhanced monitoring
  enhancedMonitoringService.healthCheck().then(health => {
    logger.info('Enhanced monitoring service status:', health);
  }).catch(error => {
    logger.error('Enhanced monitoring service initialization error:', error);
  });

  // Initialize database optimization
  databaseOptimizationService.healthCheck().then(health => {
    logger.info('Database optimization service status:', health);
  }).catch(error => {
    logger.error('Database optimization service initialization error:', error);
  });

  // Initialize CDN service
  cdnService.healthCheck().then(health => {
    logger.info('CDN service status:', health);
  }).catch(error => {
    logger.error('CDN service initialization error:', error);
  });

  // Initialize backup scheduler
  backupSchedulerService.initialize().then(() => {
    logger.info('Backup scheduler initialized successfully');
  }).catch(error => {
    logger.error('Backup scheduler initialization error:', error);
  });
} else {
  logger.info('Development mode - skipping heavy production services');

  // Initialize backup scheduler in development too (for testing)
  backupSchedulerService.initialize().then(() => {
    logger.info('Backup scheduler initialized in development mode');
  }).catch(error => {
    logger.error('Backup scheduler initialization error:', error);
  });
}

// Initialize email templates
const emailTemplateService = require('./services/emailTemplateService');
emailTemplateService.loadTemplates().catch(error => {
  logger.error('Failed to load email templates:', error);
});

// Security middleware with enhanced configuration
const helmetConfig = {
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com", "https://js.stripe.com"],
      fontSrc: ["'self'", "https://fonts.gstatic.com"],
      imgSrc: ["'self'", "data:", "https:", "https://js.stripe.com"],
      scriptSrc: ["'self'", "https://js.stripe.com"],
      connectSrc: ["'self'", env.FRONTEND_URL, "https://api.stripe.com"],
      frameSrc: ["'self'", "https://js.stripe.com", "https://hooks.stripe.com", "http://localhost:5001", "https://localhost:5001"],
      frameAncestors: ["'self'", "http://localhost:5173", "https://localhost:5173"],
      objectSrc: ["'none'"],
      baseUri: ["'self'"],
      formAction: ["'self'"],
      upgradeInsecureRequests: env.NODE_ENV === 'production' ? [] : null
    }
  },
  crossOriginEmbedderPolicy: false, // Disable for API compatibility
  frameguard: false, // Disable X-Frame-Options to use CSP frame-ancestors instead
  hsts: env.NODE_ENV === 'production' ? {
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true
  } : false,
  noSniff: true,
  xssFilter: true,
  referrerPolicy: { policy: "strict-origin-when-cross-origin" }
};

app.use(helmet(helmetConfig));

// Enhanced security middleware
app.use(preventMongoInjection);
app.use(enhancedXSSProtection);
app.use(requestSizeLimiter);
app.use(detectSuspiciousActivity);

// CORS configuration with enhanced security
const corsOptions = {
  origin: function (origin, callback) {
    // Allow requests with no origin (like mobile apps or curl requests) only in development
    if (!origin && env.NODE_ENV !== 'production') {
      return callback(null, true);
    }

    // Get allowed origins from environment or use defaults
    const corsOrigins = env.CORS_ORIGIN && typeof env.CORS_ORIGIN === 'string' ? env.CORS_ORIGIN.split(',') : [];
    const allowedOrigins = [
      env.FRONTEND_URL,
      ...corsOrigins,
      // Development origins (only in non-production)
      ...(env.NODE_ENV !== 'production' ? [
        'http://localhost:5173', // Vite dev server
        'http://localhost:3000', // React dev server
        'http://127.0.0.1:5173',
        'http://127.0.0.1:3000'
      ] : [])
    ].filter(Boolean); // Remove any undefined values

    if (allowedOrigins.includes(origin)) {
      callback(null, true);
    } else {
      logger.warn(`CORS blocked origin: ${origin}`);
      callback(new Error('Not allowed by CORS'));
    }
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: [
    'Content-Type',
    'Authorization',
    'x-auth-token',
    'x-correlation-id',
    'x-requested-with'
  ],
  exposedHeaders: ['x-total-count', 'x-correlation-id'],
  maxAge: 86400 // 24 hours
};

app.use(cors(corsOptions));

// Special CORS handling for email tracking routes (allow all origins)
app.use('/api/email/track', cors({
  origin: true, // Allow all origins for email tracking
  credentials: false,
  methods: ['GET', 'OPTIONS'],
  allowedHeaders: ['Content-Type'],
  maxAge: 86400
}));

// Raw body middleware for Stripe webhooks (must be before express.json())
app.use('/api/stripe/webhook', express.raw({ type: 'application/json' }));

app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Add correlation ID to all requests
app.use(addCorrelationId);

// Add request logging
app.use(morgan('combined', { stream: logger.stream }));

// Add request performance logging
app.use(logRequestPerformance);

// Add performance monitoring middleware
app.use(performanceMiddleware);

// Apply general rate limiting to all API routes
app.use('/api', apiLimiter);

// Swagger API Documentation
app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(swaggerDocs, {
  explorer: true,
  customCss: '.swagger-ui .topbar { display: none }',
  customSiteTitle: 'AIXcelerate API Documentation'
}));

// Define routes with specific rate limiting
app.use('/api/auth', authLimiter, require('./routes/auth'));
app.use('/api/leads', require('./routes/leads'));
app.use('/api/products', require('./routes/products'));
app.use('/api/orders', require('./routes/orders')); // Payment limiter is applied per-route inside orders.js

app.use('/api/analytics', require('./routes/analytics'));
app.use('/api/email-sequences', require('./routes/emailSequences'));
app.use('/api/email', require('./routes/email'));
app.use('/api/users', require('./routes/users'));
app.use('/api/cart', require('./routes/cart'));
app.use('/api/inventory', require('./routes/inventory'));
app.use('/api/phone-inventory', require('./routes/phoneInventory'));
app.use('/api/shipping', require('./routes/shipping'));
app.use('/api/trade-in', require('./routes/tradeIn'));
app.use('/api/payments/clickpesa', require('./routes/clickpesaCallback'));
app.use('/api/warranty', require('./routes/warranty'));
app.use('/api/returns', require('./routes/returns'));
app.use('/api/bulk', require('./routes/bulkOperations'));
app.use('/api/whatsapp', require('./routes/whatsapp'));

app.use('/api/unsubscribe', require('./routes/unsubscribeRoutes'));
app.use('/api/audit-logs', require('./routes/auditLogRoutes'));
app.use('/api/health', require('./routes/health'));
app.use('/api/monitoring', require('./routes/monitoringRoutes'));
app.use('/api/admin', adminLimiter, require('./routes/admin'));

app.use('/api/webhooks', webhookLimiter, require('./routes/webhooks'));
app.use('/api/settings', require('./routes/settings'));
app.use('/api/search', require('./routes/search'));
app.use('/api/wishlist', require('./routes/wishlist'));


// Serve static files from the public directory
app.use('/public', express.static(path.join(__dirname, 'public')));
app.use('/uploads', express.static(path.join(__dirname, 'public/uploads'), {
  setHeaders: (res, path) => {
    if (path.endsWith('.pdf')) {
      res.setHeader('Content-Type', 'application/pdf');
      res.setHeader('Content-Disposition', 'inline');
      // Add aggressive cache control headers to prevent caching issues with updated PDFs
      res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate, max-age=0');
      res.setHeader('Pragma', 'no-cache');
      res.setHeader('Expires', '0');
      res.setHeader('Surrogate-Control', 'no-store');
      res.setHeader('Vary', '*');
      // Add ETag based on file modification time for better cache invalidation
      const fs = require('fs');
      try {
        const stats = fs.statSync(path);
        res.setHeader('ETag', `"${stats.mtime.getTime()}-${stats.size}"`);
        res.setHeader('Last-Modified', stats.mtime.toUTCString());
      } catch (error) {
        // If file stats fail, continue without ETag
      }
      // Remove X-Frame-Options to avoid conflicts with CSP frame-ancestors
    }
  }
}));
app.use('/products', express.static(path.join(__dirname, 'public/products')));



// Webhook routes are handled in routes/webhooks.js

// Error context logging middleware
app.use(logErrorContext);

// Error handling middleware
app.use(errorHandler);

// Serve static assets in production
if (process.env.NODE_ENV === 'production') {
  app.use(express.static('client/build'));
  app.get('*', (req, res) => {
    res.sendFile(path.resolve(__dirname, 'client', 'build', 'index.html'));
  });
}

// Initialize Passport
app.use(passport.initialize());

// Session middleware (required for Passport)
app.use(session({
  secret: config.JWT_SECRET,
  resave: false,
  saveUninitialized: false,
  cookie: {
    secure: config.NODE_ENV === 'production',
    maxAge: 24 * 60 * 60 * 1000 // 24 hours
  }
}));

// Create HTTP and HTTPS servers
let server;

if (process.env.NODE_ENV === 'production' && env.SSL_CERT_PATH && env.SSL_KEY_PATH) {
  const httpsOptions = {
    cert: fs.readFileSync(env.SSL_CERT_PATH),
    key: fs.readFileSync(env.SSL_KEY_PATH)
  };

  // Create HTTPS server
  server = https.createServer(httpsOptions, app);

  // Create HTTP server for redirect
  const httpApp = express();
  httpApp.use((req, res) => {
    res.redirect(`https://${env.DOMAIN}${req.url}`);
  });

  http.createServer(httpApp).listen(80, () => {
    logger.info('HTTP server running on port 80 (redirecting to HTTPS)');
  });
} else {
  server = http.createServer(app);
}

// Start server
server.listen(env.PORT, env.HOST, () => {
  logger.info(`Server running in ${env.NODE_ENV} mode on ${env.HOST}:${env.PORT}`);

  // Warm up cache
  warmupCache().catch(error => {
    logger.error('Cache warmup failed:', error);
  });

  // Initialize cron jobs
  if (env.NODE_ENV !== 'test') {
    initCronJobs();
    logger.info('Cron jobs initialized');
  }
});

// Add health check endpoint
app.get('/api/health', async (req, res) => {
  try {
    const monitoringHealth = await enhancedMonitoringService.healthCheck();
    const recoveryHealth = await disasterRecoveryService.healthCheck();
    const cacheHealth = await advancedCacheService.healthCheck();
    const dbHealth = await databaseOptimizationService.healthCheck();
    const cdnHealth = await cdnService.healthCheck();

    const health = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      services: {
        monitoring: monitoringHealth,
        recovery: recoveryHealth,
        cache: cacheHealth,
        database: dbHealth,
        cdn: cdnHealth
      }
    };

    res.status(200).json(health);
  } catch (error) {
    logger.error('Health check failed:', error);
    res.status(500).json({
      status: 'unhealthy',
      error: error.message
    });
  }
});

// Graceful shutdown
const gracefulShutdown = async (signal) => {
  logger.info(`Received ${signal}. Starting graceful shutdown...`);

  try {
    // Stop accepting new connections
    server.close(() => {
      logger.info('HTTP server closed');
    });

    // Perform final backup
    await disasterRecoveryService.performBackup();

    // Close database connection
    await mongoose.connection.close();
    logger.info('Database connection closed');

    // Exit process
    process.exit(0);
  } catch (error) {
    logger.error('Error during graceful shutdown:', error);
    process.exit(1);
  }
};

// Listen for termination signals
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

// Handle unhandled promise rejections
process.on('unhandledRejection', (err, promise) => {
  logger.error('Unhandled Promise Rejection:', {
    error: err.message,
    stack: err.stack
  });
  // Close server & exit process
  gracefulShutdown('UNHANDLED_REJECTION');
});

// Handle uncaught exceptions
process.on('uncaughtException', (err) => {
  logger.error('Uncaught Exception:', {
    error: err.message,
    stack: err.stack
  });
  // Close server & exit process
  gracefulShutdown('UNCAUGHT_EXCEPTION');
});

// Memory usage monitoring
const checkMemoryUsage = () => {
  const memUsage = process.memoryUsage();
  const memUsageMB = {
    rss: Math.round(memUsage.rss / 1024 / 1024),
    heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024),
    heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024),
    external: Math.round(memUsage.external / 1024 / 1024)
  };

  // Log if memory usage is high
  if (memUsageMB.heapUsed > 500) { // 500MB threshold
    logger.warn('High memory usage detected:', memUsageMB);
  }

  // Log memory stats every hour in production
  if (env.NODE_ENV === 'production') {
    logger.info('Memory usage:', memUsageMB);
  }
};

// Check memory usage every 5 minutes
setInterval(checkMemoryUsage, 5 * 60 * 1000);