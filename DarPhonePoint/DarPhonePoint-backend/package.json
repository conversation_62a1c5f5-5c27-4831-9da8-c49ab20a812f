{"name": "aixcelerate-backend", "version": "1.0.0", "description": "AIXcelerate Backend API", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server-dev.js", "dev:simple": "node server-dev.js", "validate-env": "node scripts/validateEnvironment.js", "prestart": "npm run validate-env", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint .", "lint:fix": "eslint . --fix", "test:perf:setup": "node tests/performance/setup.js", "test:perf": "npm run test:perf:setup && k6 run tests/performance/paymentFlow.test.js", "test:perf:webhook": "npm run test:perf:setup && k6 run tests/performance/webhookProcessing.test.js", "test:perf:all": "npm run test:perf:setup && k6 run tests/performance/*.test.js", "setup:db": "node scripts/setup-db.js", "setup:ssl": "node scripts/setup-ssl.js", "seed:admin": "node scripts/seedAdmin.js", "create:admin": "node scripts/createProductionAdmin.js", "optimize:db": "node scripts/optimizeDatabase.js", "test:ci": "jest --ci --coverage --watchAll=false", "test:clickpesa": "node scripts/test-clickpesa.js", "test:email": "node scripts/test-email.js", "test:webhook": "node scripts/test-webhook.js", "test:all-integrations": "npm run test:email && npm run test:clickpesa && npm run test:webhook", "check:production": "node scripts/production-readiness-check.js", "validate:all": "npm run check:production && npm run test:all-integrations"}, "keywords": ["aixcelerate", "backend", "api"], "author": "Your Name", "license": "MIT", "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^7.1.0", "@mui/material": "^7.1.0", "@sentry/node": "^7.91.0", "@sentry/profiling-node": "^1.2.4", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.3.0", "@whop-sdk/core": "^1.0.0", "axios": "^1.6.2", "bcrypt": "^6.0.0", "bcryptjs": "^2.4.3", "bull": "^4.12.2", "colors": "^1.4.0", "cors": "^2.8.5", "csv-parser": "^3.2.0", "dompurify": "^3.2.6", "dotenv": "^16.3.1", "express": "^4.18.2", "express-mongo-sanitize": "^2.2.0", "express-rate-limit": "^7.5.1", "express-session": "^1.18.1", "express-validator": "^7.2.1", "handlebars": "^4.7.8", "helmet": "^7.2.0", "hpp": "^0.2.3", "ioredis": "^5.3.2", "joi": "^17.11.0", "jsdom": "^26.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.0.3", "morgan": "^1.10.0", "multer": "^2.0.1", "node-cron": "^4.0.6", "node-fetch": "^3.3.2", "nodemailer": "^6.9.7", "passport": "^0.7.0", "passport-google-oauth20": "^2.0.0", "puppeteer": "^24.9.0", "rate-limit-redis": "^4.2.0", "recharts": "^2.15.3", "redis": "^4.6.11", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0", "winston": "^3.11.0", "xss": "^1.0.15"}, "devDependencies": {"@babel/core": "^7.23.5", "@babel/preset-env": "^7.23.5", "babel-jest": "^29.7.0", "eslint": "^8.55.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-import": "^2.29.1", "jest": "^29.7.0", "k6": "^0.0.0", "mongodb-memory-server": "^9.5.0", "nodemon": "^3.1.10", "supertest": "^6.3.4"}}