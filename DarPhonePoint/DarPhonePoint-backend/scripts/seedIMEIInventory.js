const mongoose = require('mongoose');
const Product = require('../models/Product');
const Inventory = require('../models/Inventory');

// Connect to database
mongoose.connect('mongodb://localhost:27017/aixcelerate-dev');

// Helper function to generate realistic IMEI numbers
function generateIMEI() {
  // Generate a 15-digit IMEI number
  // Format: TAC (8 digits) + SNR (6 digits) + Check digit (1 digit)
  const tac = Math.floor(Math.random() * 90000000) + 10000000; // 8 digits
  const snr = Math.floor(Math.random() * 900000) + 100000; // 6 digits
  const partial = `${tac}${snr}`;
  
  // Calculate check digit using <PERSON><PERSON> algorithm
  let sum = 0;
  for (let i = 0; i < partial.length; i++) {
    let digit = parseInt(partial[i]);
    if (i % 2 === 1) {
      digit *= 2;
      if (digit > 9) digit = Math.floor(digit / 10) + (digit % 10);
    }
    sum += digit;
  }
  const checkDigit = (10 - (sum % 10)) % 10;
  
  return `${partial}${checkDigit}`;
}

// IMEI inventory data for smartphones
const phoneIMEIData = {
  'IP15PM-256-NT': { count: 5, condition: 'new', warranty: 12 },
  'IP15PM-512-NT': { count: 5, condition: 'new', warranty: 12 },
  'IP15PM-1TB-NT': { count: 5, condition: 'new', warranty: 12 },
  'SGS24U-256-TG': { count: 7, condition: 'new', warranty: 12 },
  'SGS24U-512-TG': { count: 7, condition: 'new', warranty: 12 },
  'SGS24U-1TB-TG': { count: 6, condition: 'new', warranty: 12 },
  'RN13-128-MB': { count: 25, condition: 'new', warranty: 12 },
  'RN13-256-MB': { count: 25, condition: 'new', warranty: 12 },
  'GP8-128-HZ': { count: 6, condition: 'new', warranty: 12 },
  'GP8-256-HZ': { count: 6, condition: 'new', warranty: 12 }
};

async function seedIMEIInventory() {
  try {
    console.log('📱 Starting IMEI inventory seeding for smartphones...');
    
    // Get all smartphone products
    const smartphones = await Product.find({ 
      category: 'smartphone',
      track_inventory: true 
    });
    
    console.log(`Found ${smartphones.length} smartphones that need IMEI tracking`);
    
    for (const product of smartphones) {
      console.log(`\n📱 Processing: ${product.name}`);
      
      for (const variant of product.variants) {
        const imeiData = phoneIMEIData[variant.sku];
        
        if (!imeiData) {
          console.log(`   ⚠️  No IMEI data found for variant: ${variant.sku}`);
          continue;
        }
        
        console.log(`   📦 Adding ${imeiData.count} IMEI entries for ${variant.name}`);
        
        // Find or create inventory record for this variant
        let inventoryRecord = await Inventory.findOne({
          product: product._id,
          variant_sku: variant.sku
        });
        
        if (!inventoryRecord) {
          // Create new inventory record
          inventoryRecord = new Inventory({
            product: product._id,
            variant_sku: variant.sku,
            location: 'main_warehouse',
            quantity_on_hand: imeiData.count,
            quantity_available: imeiData.count,
            quantity_reserved: 0,
            reorder_point: Math.max(2, Math.floor(imeiData.count * 0.2)),
            reorder_quantity: imeiData.count,
            cost_per_unit: Math.round(product.price * 0.6),
            devices: []
          });
        } else {
          // Clear existing devices to avoid duplicates
          inventoryRecord.devices = [];
          inventoryRecord.quantity_on_hand = imeiData.count;
          inventoryRecord.quantity_available = imeiData.count;
          inventoryRecord.quantity_reserved = 0;
        }
        
        // Generate IMEI numbers for this variant
        const generatedIMEIs = new Set();
        for (let i = 0; i < imeiData.count; i++) {
          let imei;
          do {
            imei = generateIMEI();
          } while (generatedIMEIs.has(imei));
          
          generatedIMEIs.add(imei);
          
          inventoryRecord.devices.push({
            imei: imei,
            condition: imeiData.condition,
            warranty_months: imeiData.warranty,
            status: 'available',
            received_at: new Date(),
            purchase_price: Math.round(product.price * 0.6),
            supplier: 'Phone Point Dar Supplier',
            notes: `Initial inventory - ${variant.name}`
          });
        }
        
        await inventoryRecord.save();
        console.log(`   ✅ Added ${imeiData.count} IMEI entries for ${variant.name}`);
      }
    }
    
    // Summary
    console.log('\n📊 IMEI Inventory Summary:');
    const inventoryRecords = await Inventory.find({
      variant_sku: { $exists: true, $ne: null }
    }).populate('product', 'name');
    
    let totalDevices = 0;
    for (const record of inventoryRecords) {
      const deviceCount = record.devices.length;
      totalDevices += deviceCount;
      console.log(`   ${record.product.name} (${record.variant_sku}): ${deviceCount} devices`);
    }
    
    console.log(`\n📱 Total IMEI-tracked devices: ${totalDevices}`);
    console.log('\n🎉 IMEI inventory seeding completed successfully!');
    console.log('💡 Customers can now select specific phones by IMEI when adding to cart');
    
    process.exit(0);
    
  } catch (error) {
    console.error('❌ Error seeding IMEI inventory:', error);
    process.exit(1);
  }
}

seedIMEIInventory();
