#!/usr/bin/env node

/**
 * Email Configuration Setup Script
 * 
 * This script helps configure email settings for different environments
 */

const fs = require('fs');
const path = require('path');
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

const question = (query) => new Promise((resolve) => rl.question(query, resolve));

async function setupEmail() {
  console.log('\n🚀 AIXcelerate Email Configuration Setup\n');
  
  const environment = await question('Select environment (development/production): ');
  
  if (environment === 'development') {
    await setupDevelopmentEmail();
  } else if (environment === 'production') {
    await setupProductionEmail();
  } else {
    console.log('❌ Invalid environment. Please choose "development" or "production"');
    process.exit(1);
  }
  
  rl.close();
}

async function setupDevelopmentEmail() {
  console.log('\n📧 Development Email Setup');
  console.log('Choose your email service:');
  console.log('1. Gmail (recommended for testing)');
  console.log('2. Ethereal Email (mock service)');
  console.log('3. Custom SMTP');
  
  const choice = await question('Enter choice (1-3): ');
  
  let config = {};
  
  switch (choice) {
    case '1':
      config = await setupGmail();
      break;
    case '2':
      config = await setupEthereal();
      break;
    case '3':
      config = await setupCustomSMTP();
      break;
    default:
      console.log('❌ Invalid choice');
      return;
  }
  
  await updateEnvFile('.env.development', config);
  console.log('✅ Development email configuration updated!');
}

async function setupProductionEmail() {
  console.log('\n📧 Production Email Setup');
  console.log('Choose your email service:');
  console.log('1. SendGrid (recommended)');
  console.log('2. Mailgun');
  console.log('3. Amazon SES');
  console.log('4. Custom SMTP');
  
  const choice = await question('Enter choice (1-4): ');
  
  let config = {};
  
  switch (choice) {
    case '1':
      config = await setupSendGrid();
      break;
    case '2':
      config = await setupMailgun();
      break;
    case '3':
      config = await setupAmazonSES();
      break;
    case '4':
      config = await setupCustomSMTP();
      break;
    default:
      console.log('❌ Invalid choice');
      return;
  }
  
  await updateEnvFile('.env.production', config);
  console.log('✅ Production email configuration updated!');
}

async function setupGmail() {
  console.log('\n📧 Gmail Setup');
  console.log('Note: You need to enable 2FA and create an App Password');
  console.log('Visit: https://myaccount.google.com/apppasswords');
  
  const email = await question('Gmail address: ');
  const password = await question('App password: ');
  const fromEmail = await question('From email (default: <EMAIL>): ') || '<EMAIL>';
  
  return {
    EMAIL_HOST: 'smtp.gmail.com',
    EMAIL_PORT: '587',
    EMAIL_SECURE: 'true',
    EMAIL_USER: email,
    EMAIL_PASSWORD: password,
    EMAIL_FROM: fromEmail
  };
}

async function setupEthereal() {
  console.log('\n📧 Ethereal Email Setup (Mock Service)');
  console.log('Visit: https://ethereal.email/create to create test account');
  
  const user = await question('Ethereal username: ');
  const password = await question('Ethereal password: ');
  
  return {
    EMAIL_HOST: 'smtp.ethereal.email',
    EMAIL_PORT: '587',
    EMAIL_SECURE: 'false',
    EMAIL_USER: user,
    EMAIL_PASSWORD: password,
    EMAIL_FROM: '<EMAIL>'
  };
}

async function setupSendGrid() {
  console.log('\n📧 SendGrid Setup');
  console.log('Get your API key from: https://app.sendgrid.com/settings/api_keys');
  
  const apiKey = await question('SendGrid API Key: ');
  const fromEmail = await question('From email (default: <EMAIL>): ') || '<EMAIL>';
  
  return {
    EMAIL_HOST: 'smtp.sendgrid.net',
    EMAIL_PORT: '587',
    EMAIL_SECURE: 'true',
    EMAIL_USER: 'apikey',
    EMAIL_PASSWORD: apiKey,
    EMAIL_FROM: fromEmail
  };
}

async function setupMailgun() {
  console.log('\n📧 Mailgun Setup');
  
  const domain = await question('Mailgun domain: ');
  const apiKey = await question('Mailgun API key: ');
  const fromEmail = await question('From email: ');
  
  return {
    EMAIL_HOST: 'smtp.mailgun.org',
    EMAIL_PORT: '587',
    EMAIL_SECURE: 'true',
    EMAIL_USER: `postmaster@${domain}`,
    EMAIL_PASSWORD: apiKey,
    EMAIL_FROM: fromEmail
  };
}

async function setupAmazonSES() {
  console.log('\n📧 Amazon SES Setup');
  
  const region = await question('AWS Region (e.g., us-east-1): ');
  const accessKey = await question('AWS Access Key ID: ');
  const secretKey = await question('AWS Secret Access Key: ');
  const fromEmail = await question('From email: ');
  
  return {
    EMAIL_HOST: `email-smtp.${region}.amazonaws.com`,
    EMAIL_PORT: '587',
    EMAIL_SECURE: 'true',
    EMAIL_USER: accessKey,
    EMAIL_PASSWORD: secretKey,
    EMAIL_FROM: fromEmail
  };
}

async function setupCustomSMTP() {
  console.log('\n📧 Custom SMTP Setup');
  
  const host = await question('SMTP Host: ');
  const port = await question('SMTP Port (default: 587): ') || '587';
  const secure = await question('Use TLS/SSL? (y/n): ');
  const user = await question('Username: ');
  const password = await question('Password: ');
  const fromEmail = await question('From email: ');
  
  return {
    EMAIL_HOST: host,
    EMAIL_PORT: port,
    EMAIL_SECURE: secure.toLowerCase() === 'y' ? 'true' : 'false',
    EMAIL_USER: user,
    EMAIL_PASSWORD: password,
    EMAIL_FROM: fromEmail
  };
}

async function updateEnvFile(filename, config) {
  const envPath = path.join(__dirname, '..', filename);
  
  if (!fs.existsSync(envPath)) {
    console.log(`❌ ${filename} not found`);
    return;
  }
  
  let envContent = fs.readFileSync(envPath, 'utf8');
  
  // Update each configuration value
  Object.entries(config).forEach(([key, value]) => {
    const regex = new RegExp(`^${key}=.*$`, 'm');
    if (envContent.match(regex)) {
      envContent = envContent.replace(regex, `${key}=${value}`);
    } else {
      envContent += `\n${key}=${value}`;
    }
  });
  
  fs.writeFileSync(envPath, envContent);
}

// Run the setup
setupEmail().catch(console.error);
