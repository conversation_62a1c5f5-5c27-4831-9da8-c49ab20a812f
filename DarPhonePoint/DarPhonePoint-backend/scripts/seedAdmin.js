/**
 * Admin User Seeding Script for Phone Point Dar
 * Creates initial admin user for production deployment
 */

const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const User = require('../models/User');
const logger = require('../utils/logger');
const readline = require('readline');

// Create readline interface for user input
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Promisify readline question
const question = (query) => new Promise((resolve) => rl.question(query, resolve));

class AdminSeeder {
  constructor() {
    this.adminData = {
      name: '',
      email: '',
      password: '',
      phone: '',
      role: 'admin'
    };
  }

  /**
   * Connect to database
   */
  async connectDB() {
    try {
      const mongoUri = process.env.MONGODB_URI || 'mongodb://localhost:27017/phonepointdar';
      await mongoose.connect(mongoUri);
      logger.info('Connected to MongoDB for admin seeding');
    } catch (error) {
      logger.error('Database connection failed:', error);
      throw error;
    }
  }

  /**
   * Collect admin user information
   */
  async collectAdminInfo() {
    console.log('\n🔧 PHONE POINT DAR - ADMIN USER SETUP');
    console.log('=====================================\n');

    // Check if admin already exists
    const existingAdmin = await User.findOne({ role: 'admin' });
    if (existingAdmin) {
      console.log('⚠️  Admin user already exists:');
      console.log(`   Name: ${existingAdmin.name}`);
      console.log(`   Email: ${existingAdmin.email}`);
      console.log(`   Created: ${existingAdmin.createdAt.toLocaleDateString()}\n`);
      
      const overwrite = await question('Do you want to create another admin user? (y/N): ');
      if (overwrite.toLowerCase() !== 'y' && overwrite.toLowerCase() !== 'yes') {
        console.log('Admin seeding cancelled.');
        return false;
      }
    }

    // Collect admin information
    this.adminData.name = await question('Admin Full Name: ');
    if (!this.adminData.name.trim()) {
      throw new Error('Admin name is required');
    }

    this.adminData.email = await question('Admin Email: ');
    if (!this.adminData.email.trim() || !this.isValidEmail(this.adminData.email)) {
      throw new Error('Valid admin email is required');
    }

    // Check if email already exists
    const existingUser = await User.findOne({ email: this.adminData.email });
    if (existingUser) {
      throw new Error('User with this email already exists');
    }

    this.adminData.phone = await question('Admin Phone (optional): ');

    // Password collection with confirmation
    this.adminData.password = await this.collectPassword();

    return true;
  }

  /**
   * Collect and confirm password
   */
  async collectPassword() {
    const password = await question('Admin Password (min 8 characters): ');
    if (!password || password.length < 8) {
      throw new Error('Password must be at least 8 characters long');
    }

    const confirmPassword = await question('Confirm Password: ');
    if (password !== confirmPassword) {
      throw new Error('Passwords do not match');
    }

    return password;
  }

  /**
   * Validate email format
   */
  isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Create admin user
   */
  async createAdmin() {
    try {
      // Hash password
      const saltRounds = 12;
      const hashedPassword = await bcrypt.hash(this.adminData.password, saltRounds);

      // Create admin user
      const adminUser = new User({
        name: this.adminData.name.trim(),
        email: this.adminData.email.trim().toLowerCase(),
        password: hashedPassword,
        phone: this.adminData.phone.trim() || undefined,
        role: 'admin',
        isEmailVerified: true, // Admin emails are pre-verified
        preferences: {
          language: 'en',
          currency: 'TZS',
          notifications: {
            email: true,
            sms: false,
            push: true
          }
        },
        profile: {
          address: {
            country: 'Tanzania',
            city: 'Dar es Salaam'
          }
        }
      });

      await adminUser.save();

      console.log('\n✅ Admin user created successfully!');
      console.log('=====================================');
      console.log(`Name: ${adminUser.name}`);
      console.log(`Email: ${adminUser.email}`);
      console.log(`Phone: ${adminUser.phone || 'Not provided'}`);
      console.log(`Role: ${adminUser.role}`);
      console.log(`User ID: ${adminUser._id}`);
      console.log(`Created: ${adminUser.createdAt.toLocaleString()}\n`);

      return adminUser;
    } catch (error) {
      logger.error('Failed to create admin user:', error);
      throw error;
    }
  }

  /**
   * Display login instructions
   */
  displayLoginInstructions() {
    console.log('🔐 LOGIN INSTRUCTIONS');
    console.log('=====================');
    console.log('1. Navigate to your Phone Point Dar admin panel');
    console.log('2. Use the following credentials to log in:');
    console.log(`   Email: ${this.adminData.email}`);
    console.log('   Password: [The password you just created]');
    console.log('3. Change your password after first login for security');
    console.log('4. Set up two-factor authentication if available\n');
    
    console.log('📱 ADMIN PANEL ACCESS');
    console.log('=====================');
    console.log('Development: http://localhost:5173/admin');
    console.log('Production: https://your-domain.com/admin\n');
    
    console.log('🛡️  SECURITY RECOMMENDATIONS');
    console.log('============================');
    console.log('• Use a strong, unique password');
    console.log('• Enable two-factor authentication');
    console.log('• Regularly review admin access logs');
    console.log('• Keep admin credentials secure');
    console.log('• Consider creating separate admin accounts for different team members\n');
  }

  /**
   * Run the complete admin seeding process
   */
  async run() {
    try {
      await this.connectDB();
      
      const shouldProceed = await this.collectAdminInfo();
      if (!shouldProceed) {
        return;
      }

      await this.createAdmin();
      this.displayLoginInstructions();

    } catch (error) {
      console.error('\n❌ Admin seeding failed:', error.message);
      logger.error('Admin seeding error:', error);
      process.exit(1);
    } finally {
      rl.close();
      await mongoose.disconnect();
      logger.info('Database connection closed');
    }
  }
}

// Handle script execution
if (require.main === module) {
  const seeder = new AdminSeeder();
  
  // Handle graceful shutdown
  process.on('SIGINT', async () => {
    console.log('\n\nAdmin seeding interrupted by user');
    rl.close();
    await mongoose.disconnect();
    process.exit(0);
  });

  // Run the seeder
  seeder.run().then(() => {
    console.log('Admin seeding completed successfully!');
    process.exit(0);
  }).catch((error) => {
    console.error('Admin seeding failed:', error);
    process.exit(1);
  });
}

module.exports = AdminSeeder;
