#!/usr/bin/env node

/**
 * Production Readiness Check for Phone Point Dar
 * Validates all critical systems and configurations for production deployment
 */

require('dotenv').config();
const mongoose = require('mongoose');
const logger = require('../utils/logger');

class ProductionReadinessChecker {
  constructor() {
    this.checks = [];
    this.results = {
      passed: 0,
      failed: 0,
      warnings: 0,
      critical: 0,
      details: []
    };
  }

  /**
   * Add a check to the validation suite
   */
  addCheck(name, category, severity, checkFunction) {
    this.checks.push({
      name,
      category,
      severity, // 'critical', 'high', 'medium', 'low'
      check: checkFunction
    });
  }

  /**
   * Run all checks
   */
  async runAllChecks() {
    console.log('🔍 Phone Point Dar Production Readiness Check');
    console.log('=============================================');
    console.log(`Timestamp: ${new Date().toISOString()}\n`);

    for (const check of this.checks) {
      await this.runCheck(check);
    }

    this.printSummary();
    return this.results;
  }

  /**
   * Run individual check
   */
  async runCheck(check) {
    try {
      console.log(`\n🔍 ${check.name} (${check.severity.toUpperCase()})...`);
      
      const result = await check.check();
      
      if (result.passed) {
        console.log(`✅ ${check.name}: PASSED`);
        if (result.message) console.log(`   ${result.message}`);
        this.results.passed++;
      } else {
        const icon = check.severity === 'critical' ? '🚨' : '⚠️';
        console.log(`${icon} ${check.name}: FAILED`);
        console.log(`   ${result.message}`);
        
        if (check.severity === 'critical') {
          this.results.critical++;
        } else {
          this.results.warnings++;
        }
        this.results.failed++;
      }

      this.results.details.push({
        name: check.name,
        category: check.category,
        severity: check.severity,
        passed: result.passed,
        message: result.message,
        details: result.details || {}
      });

    } catch (error) {
      console.log(`💥 ${check.name}: ERROR`);
      console.log(`   ${error.message}`);
      
      this.results.failed++;
      if (check.severity === 'critical') {
        this.results.critical++;
      }

      this.results.details.push({
        name: check.name,
        category: check.category,
        severity: check.severity,
        passed: false,
        message: `Check failed with error: ${error.message}`,
        error: true
      });
    }
  }

  /**
   * Print summary
   */
  printSummary() {
    console.log('\n📊 Production Readiness Summary');
    console.log('===============================');
    console.log(`Total Checks: ${this.checks.length}`);
    console.log(`Passed: ${this.results.passed}`);
    console.log(`Failed: ${this.results.failed}`);
    console.log(`Critical Issues: ${this.results.critical}`);
    console.log(`Warnings: ${this.results.warnings}`);

    const readinessScore = (this.results.passed / this.checks.length) * 100;
    console.log(`\nReadiness Score: ${readinessScore.toFixed(1)}%`);

    if (this.results.critical > 0) {
      console.log('\n🚨 CRITICAL ISSUES DETECTED - NOT READY FOR PRODUCTION');
      console.log('Fix all critical issues before deploying to production.');
    } else if (this.results.failed > 0) {
      console.log('\n⚠️ WARNINGS DETECTED - REVIEW BEFORE PRODUCTION');
      console.log('Address warnings to ensure optimal production performance.');
    } else {
      console.log('\n🎉 ALL CHECKS PASSED - READY FOR PRODUCTION');
      console.log('Your Phone Point Dar system is ready for production deployment!');
    }
  }
}

// Initialize checker
const checker = new ProductionReadinessChecker();

// Environment Configuration Checks
checker.addCheck(
  'Environment Variables',
  'Configuration',
  'critical',
  async () => {
    const required = [
      'NODE_ENV',
      'MONGODB_URI',
      'JWT_SECRET',
      'EMAIL_HOST',
      'EMAIL_USER',
      'EMAIL_PASSWORD',
      'CLICKPESA_CLIENT_ID',
      'CLICKPESA_API_KEY'
    ];

    const missing = required.filter(env => !process.env[env]);
    
    if (missing.length > 0) {
      return {
        passed: false,
        message: `Missing required environment variables: ${missing.join(', ')}`
      };
    }

    return {
      passed: true,
      message: 'All required environment variables are configured'
    };
  }
);

// Database Connection Check
checker.addCheck(
  'Database Connection',
  'Infrastructure',
  'critical',
  async () => {
    try {
      if (mongoose.connection.readyState === 0) {
        await mongoose.connect(process.env.MONGODB_URI);
      }
      
      // Test database operation
      await mongoose.connection.db.admin().ping();
      
      return {
        passed: true,
        message: 'Database connection successful'
      };
    } catch (error) {
      return {
        passed: false,
        message: `Database connection failed: ${error.message}`
      };
    }
  }
);

// ClickPesa Configuration Check
checker.addCheck(
  'ClickPesa Configuration',
  'Payment',
  'critical',
  async () => {
    const config = {
      clientId: process.env.CLICKPESA_CLIENT_ID,
      apiKey: process.env.CLICKPESA_API_KEY,
      apiUrl: process.env.CLICKPESA_API_URL,
      billpayNamba: process.env.CLICKPESA_BILLPAY_NAMBA
    };

    const missing = Object.entries(config)
      .filter(([key, value]) => !value)
      .map(([key]) => key);

    if (missing.length > 0) {
      return {
        passed: false,
        message: `Missing ClickPesa configuration: ${missing.join(', ')}`
      };
    }

    return {
      passed: true,
      message: 'ClickPesa configuration complete',
      details: {
        clientId: config.clientId,
        apiUrl: config.apiUrl,
        billpayNamba: config.billpayNamba
      }
    };
  }
);

// Email Configuration Check
checker.addCheck(
  'Email Service Configuration',
  'Communication',
  'high',
  async () => {
    const emailConfig = {
      host: process.env.EMAIL_HOST,
      user: process.env.EMAIL_USER,
      password: process.env.EMAIL_PASSWORD,
      from: process.env.EMAIL_FROM
    };

    const missing = Object.entries(emailConfig)
      .filter(([key, value]) => !value)
      .map(([key]) => key);

    if (missing.length > 0) {
      return {
        passed: false,
        message: `Missing email configuration: ${missing.join(', ')}`
      };
    }

    return {
      passed: true,
      message: 'Email service configuration complete'
    };
  }
);

// Security Configuration Check
checker.addCheck(
  'Security Configuration',
  'Security',
  'critical',
  async () => {
    const issues = [];

    // Check JWT secret strength
    if (!process.env.JWT_SECRET || process.env.JWT_SECRET.length < 32) {
      issues.push('JWT secret is too short (minimum 32 characters)');
    }

    // Check if using default secrets
    if (process.env.JWT_SECRET && process.env.JWT_SECRET.includes('development')) {
      issues.push('Using development JWT secret in production');
    }

    // Check NODE_ENV
    if (process.env.NODE_ENV !== 'production') {
      issues.push('NODE_ENV is not set to production');
    }

    // Check webhook secret
    if (!process.env.CLICKPESA_WEBHOOK_SECRET) {
      issues.push('ClickPesa webhook secret not configured');
    }

    if (issues.length > 0) {
      return {
        passed: false,
        message: `Security issues: ${issues.join(', ')}`
      };
    }

    return {
      passed: true,
      message: 'Security configuration is properly set'
    };
  }
);

// File System Permissions Check
checker.addCheck(
  'File System Permissions',
  'Infrastructure',
  'medium',
  async () => {
    const fs = require('fs').promises;
    
    try {
      // Check upload directory
      const uploadPath = process.env.UPLOAD_PATH || './public/uploads';
      
      try {
        await fs.access(uploadPath);
      } catch {
        await fs.mkdir(uploadPath, { recursive: true });
      }

      // Test write permissions
      const testFile = `${uploadPath}/test-${Date.now()}.txt`;
      await fs.writeFile(testFile, 'test');
      await fs.unlink(testFile);

      return {
        passed: true,
        message: 'File system permissions are correct'
      };
    } catch (error) {
      return {
        passed: false,
        message: `File system permission error: ${error.message}`
      };
    }
  }
);

// Production URLs Check
checker.addCheck(
  'Production URLs',
  'Configuration',
  'high',
  async () => {
    const urls = {
      baseUrl: process.env.BASE_URL,
      frontendUrl: process.env.FRONTEND_URL,
      apiUrl: process.env.API_URL
    };

    const issues = [];

    Object.entries(urls).forEach(([key, url]) => {
      if (!url) {
        issues.push(`${key} not configured`);
      } else if (url.includes('localhost') || url.includes('127.0.0.1')) {
        issues.push(`${key} still uses localhost (${url})`);
      }
    });

    if (issues.length > 0) {
      return {
        passed: false,
        message: `URL configuration issues: ${issues.join(', ')}`
      };
    }

    return {
      passed: true,
      message: 'Production URLs are properly configured'
    };
  }
);

// Logging Configuration Check
checker.addCheck(
  'Logging Configuration',
  'Monitoring',
  'medium',
  async () => {
    const logLevel = process.env.LOG_LEVEL || 'info';
    const debugMode = process.env.DEBUG === 'true';

    if (debugMode && process.env.NODE_ENV === 'production') {
      return {
        passed: false,
        message: 'Debug mode is enabled in production environment'
      };
    }

    if (logLevel === 'debug' && process.env.NODE_ENV === 'production') {
      return {
        passed: false,
        message: 'Debug log level should not be used in production'
      };
    }

    return {
      passed: true,
      message: `Logging configured correctly (level: ${logLevel})`
    };
  }
);

// Dependencies Check
checker.addCheck(
  'Critical Dependencies',
  'Infrastructure',
  'high',
  async () => {
    try {
      // Check if critical modules can be loaded
      require('mongoose');
      require('express');
      require('nodemailer');
      require('axios');
      require('bcryptjs');
      require('jsonwebtoken');

      return {
        passed: true,
        message: 'All critical dependencies are available'
      };
    } catch (error) {
      return {
        passed: false,
        message: `Missing critical dependency: ${error.message}`
      };
    }
  }
);

// Run the checks
async function main() {
  try {
    const results = await checker.runAllChecks();
    
    // Exit with appropriate code
    if (results.critical > 0) {
      process.exit(2); // Critical issues
    } else if (results.failed > 0) {
      process.exit(1); // Warnings
    } else {
      process.exit(0); // All good
    }
  } catch (error) {
    console.error('💥 Production readiness check crashed:', error);
    process.exit(3);
  } finally {
    // Close database connection
    if (mongoose.connection.readyState !== 0) {
      await mongoose.connection.close();
    }
  }
}

// Run if executed directly
if (require.main === module) {
  main();
}

module.exports = ProductionReadinessChecker;
