const mongoose = require('mongoose');
const Product = require('../models/Product');
const fs = require('fs');
const path = require('path');

async function checkFilePathMismatch() {
  try {
    await mongoose.connect('mongodb://localhost:27017/aixcelerate-dev');
    console.log('Connected to MongoDB (aixcelerate-dev)');
    
    const products = await Product.find({ is_active: true }).select('name slug file_path');
    
    console.log('\n📋 DATABASE vs ACTUAL FILES MISMATCH:');
    console.log('=====================================');
    
    const mismatches = [];
    
    for (const product of products) {
      const dbPath = product.file_path;
      const expectedGeneratedPath = `/uploads/products/${product.slug}.pdf`;
      const actualDbFile = path.join(__dirname, '../public', dbPath);
      const actualGeneratedFile = path.join(__dirname, '../public', expectedGeneratedPath);
      
      console.log(`\n${product.name}:`);
      console.log(`  Database Path: ${dbPath}`);
      console.log(`  Generated Path: ${expectedGeneratedPath}`);
      console.log(`  DB File Exists: ${fs.existsSync(actualDbFile) ? '✅' : '❌'}`);
      console.log(`  Generated File Exists: ${fs.existsSync(actualGeneratedFile) ? '✅' : '❌'}`);
      console.log(`  Paths Match: ${dbPath === expectedGeneratedPath ? '✅' : '❌'}`);
      
      if (dbPath !== expectedGeneratedPath) {
        console.log(`  ⚠️  MISMATCH: Frontend will try to refresh wrong file!`);
        mismatches.push({
          product,
          dbPath,
          expectedGeneratedPath,
          dbFileExists: fs.existsSync(actualDbFile),
          generatedFileExists: fs.existsSync(actualGeneratedFile)
        });
      }
    }
    
    console.log('\n📊 SUMMARY:');
    console.log(`  Total products: ${products.length}`);
    console.log(`  Mismatched paths: ${mismatches.length}`);
    
    if (mismatches.length > 0) {
      console.log('\n🔧 RECOMMENDED FIXES:');
      mismatches.forEach((mismatch, i) => {
        console.log(`${i + 1}. ${mismatch.product.name}:`);
        if (mismatch.generatedFileExists && !mismatch.dbFileExists) {
          console.log(`   - Update database to use: ${mismatch.expectedGeneratedPath}`);
        } else if (mismatch.dbFileExists && !mismatch.generatedFileExists) {
          console.log(`   - Rename file: ${mismatch.dbPath} → ${mismatch.expectedGeneratedPath}`);
        } else if (mismatch.dbFileExists && mismatch.generatedFileExists) {
          console.log(`   - Choose: Keep DB file or Generated file (different content)`);
        } else {
          console.log(`   - Neither file exists - need to generate PDF`);
        }
      });
    }
    
    await mongoose.disconnect();
    return mismatches;
  } catch (error) {
    console.error('Error:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  checkFilePathMismatch();
}

module.exports = checkFilePathMismatch;
