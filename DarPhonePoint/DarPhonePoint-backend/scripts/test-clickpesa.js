#!/usr/bin/env node

/**
 * ClickPesa Integration Test Script
 * Tests the ClickPesa payment integration with real credentials
 */

require('dotenv').config();
const axios = require('axios');
const logger = require('../utils/logger');

// ClickPesa configuration
const CLICKPESA_CONFIG = {
  CLIENT_ID: process.env.CLICKPESA_CLIENT_ID,
  API_KEY: process.env.CLICKPESA_API_KEY,
  API_URL: process.env.CLICKPESA_API_URL || 'https://api.clickpesa.com',
  BILLPAY_NAMBA: process.env.CLICKPESA_BILLPAY_NAMBA || '1804'
};

// Create ClickPesa client
const createClickPesaClient = () => {
  return axios.create({
    baseURL: CLICKPESA_CONFIG.API_URL,
    timeout: 30000,
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${CLICKPESA_CONFIG.API_KEY}`,
      'X-Client-ID': CLICKPESA_CONFIG.CLIENT_ID
    }
  });
};

// Test ClickPesa connection
async function testClickPesaConnection() {
  console.log('\n🔍 Testing ClickPesa Connection...');
  console.log('=====================================');
  
  // Check configuration
  console.log('📋 Configuration:');
  console.log(`   Client ID: ${CLICKPESA_CONFIG.CLIENT_ID}`);
  console.log(`   API URL: ${CLICKPESA_CONFIG.API_URL}`);
  console.log(`   BillPay Namba: ${CLICKPESA_CONFIG.BILLPAY_NAMBA}`);
  console.log(`   API Key: ${CLICKPESA_CONFIG.API_KEY ? '✅ Configured' : '❌ Missing'}`);
  
  if (!CLICKPESA_CONFIG.CLIENT_ID || !CLICKPESA_CONFIG.API_KEY) {
    console.log('\n❌ Missing ClickPesa credentials!');
    console.log('Please ensure CLICKPESA_CLIENT_ID and CLICKPESA_API_KEY are set in your .env file');
    return false;
  }
  
  try {
    const client = createClickPesaClient();
    
    // Test API connectivity with a simple request
    console.log('\n🌐 Testing API connectivity...');
    
    // Try to get account balance or status (adjust endpoint based on ClickPesa API)
    const response = await client.get('/v1/account/balance');
    
    console.log('✅ ClickPesa API connection successful!');
    console.log('📊 Response:', response.data);
    
    return true;
  } catch (error) {
    console.log('\n❌ ClickPesa API connection failed!');
    
    if (error.response) {
      console.log('📄 Error Response:');
      console.log(`   Status: ${error.response.status}`);
      console.log(`   Message: ${error.response.data?.message || 'Unknown error'}`);
      console.log(`   Data:`, error.response.data);
    } else if (error.request) {
      console.log('📡 Network Error: No response received');
      console.log('   Check your internet connection and API URL');
    } else {
      console.log('⚙️ Configuration Error:', error.message);
    }
    
    return false;
  }
}

// Test M-Pesa payment initialization
async function testMpesaPayment() {
  console.log('\n💳 Testing M-Pesa Payment Initialization...');
  console.log('=============================================');
  
  try {
    const client = createClickPesaClient();
    
    // Test payment request
    const paymentData = {
      client_id: CLICKPESA_CONFIG.CLIENT_ID,
      request_id: `TEST-${Date.now()}`,
      msisdn: '255123456789', // Test phone number
      amount: 1000, // 1000 TZS test amount
      currency: 'TZS',
      reference: `PPD-TEST-${Date.now()}`,
      description: 'Phone Point Dar - Test Payment',
      callback_url: `${process.env.BASE_URL}/api/payments/clickpesa/callback`,
      redirect_url: `${process.env.FRONTEND_URL}/payment/success`,
      cancel_url: `${process.env.FRONTEND_URL}/payment/cancel`
    };
    
    console.log('📤 Sending test payment request...');
    console.log('   Amount: 1000 TZS');
    console.log('   Phone: 255123456789 (test)');
    
    const response = await client.post('/v1/payments/request', paymentData);
    
    if (response.data && response.data.success) {
      console.log('✅ M-Pesa payment initialization successful!');
      console.log('📊 Payment Response:');
      console.log(`   Request ID: ${response.data.request_id}`);
      console.log(`   Status: ${response.data.status}`);
      console.log(`   Checkout URL: ${response.data.checkout_url}`);
      
      return response.data;
    } else {
      console.log('❌ M-Pesa payment initialization failed');
      console.log('📄 Response:', response.data);
      return null;
    }
  } catch (error) {
    console.log('\n❌ M-Pesa payment test failed!');
    
    if (error.response) {
      console.log('📄 Error Response:');
      console.log(`   Status: ${error.response.status}`);
      console.log(`   Message: ${error.response.data?.message || 'Unknown error'}`);
      console.log(`   Data:`, error.response.data);
    } else {
      console.log('⚙️ Error:', error.message);
    }
    
    return null;
  }
}

// Main test function
async function runTests() {
  console.log('🚀 ClickPesa Integration Test Suite');
  console.log('===================================');
  console.log('Phone Point Dar - Payment Gateway Testing');
  console.log(`Timestamp: ${new Date().toISOString()}\n`);
  
  let allTestsPassed = true;
  
  // Test 1: Connection
  const connectionTest = await testClickPesaConnection();
  if (!connectionTest) {
    allTestsPassed = false;
  }
  
  // Test 2: M-Pesa Payment (only if connection works)
  if (connectionTest) {
    const paymentTest = await testMpesaPayment();
    if (!paymentTest) {
      allTestsPassed = false;
    }
  }
  
  // Summary
  console.log('\n📋 Test Summary');
  console.log('================');
  console.log(`Connection Test: ${connectionTest ? '✅ PASSED' : '❌ FAILED'}`);
  console.log(`Payment Test: ${connectionTest ? (allTestsPassed ? '✅ PASSED' : '❌ FAILED') : '⏭️ SKIPPED'}`);
  console.log(`Overall Status: ${allTestsPassed ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED'}`);
  
  if (allTestsPassed) {
    console.log('\n🎉 ClickPesa integration is working correctly!');
    console.log('You can now process real payments through Phone Point Dar.');
  } else {
    console.log('\n⚠️ ClickPesa integration needs attention.');
    console.log('Please check the error messages above and fix the issues.');
  }
  
  process.exit(allTestsPassed ? 0 : 1);
}

// Run tests if script is executed directly
if (require.main === module) {
  runTests().catch(error => {
    console.error('💥 Test suite crashed:', error);
    process.exit(1);
  });
}

module.exports = {
  testClickPesaConnection,
  testMpesaPayment,
  runTests
};
