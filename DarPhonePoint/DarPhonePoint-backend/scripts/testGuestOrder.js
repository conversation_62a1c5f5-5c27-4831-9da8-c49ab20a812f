const axios = require('axios');

const API_BASE = 'http://localhost:5001/api';

async function testGuestOrder() {
  try {
    console.log('🧪 Testing guest order creation...');
    
    // First, get a smartphone to test with
    console.log('📱 Fetching smartphones...');
    const productsResponse = await axios.get(`${API_BASE}/products`);
    const product = productsResponse.data.data.find(p => p.category === 'smartphone');
    
    if (!product) {
      throw new Error('No products found');
    }
    
    console.log(`Found product: ${product.name}`);
    console.log(`Product has variants: ${product.variants?.length || 0}`);
    console.log(`Track inventory: ${product.track_inventory}`);
    
    // Get a variant if available
    const variant = product.variants && product.variants.length > 0 ? product.variants[0] : null;
    
    // If it's a phone that tracks inventory, get available IMEI
    let selectedIMEI = null;
    if (product.track_inventory && variant) {
      console.log(`🔍 Checking available phones for variant: ${variant.sku}`);
      try {
        const phonesResponse = await axios.get(`${API_BASE}/phone-inventory/phones/${product._id}/${variant.sku}`);
        const availablePhones = phonesResponse.data.data || [];
        console.log(`Available phones: ${availablePhones.length}`);
        
        if (availablePhones.length > 0) {
          selectedIMEI = availablePhones[0].imei;
          console.log(`Selected IMEI: ${selectedIMEI}`);
        }
      } catch (error) {
        console.log(`⚠️  Could not fetch phones: ${error.message}`);
      }
    }
    
    // Create test order data
    const orderData = {
      customer_email: '<EMAIL>',
      customer_name: 'John Doe',
      customer_phone: '+255123456789',
      shipping_address: {
        first_name: 'John',
        last_name: 'Doe',
        address_line_1: '123 Test Street',
        city: 'Dar es Salaam',
        state: 'Dar es Salaam',
        postal_code: '12345',
        country: 'Tanzania',
        phone: '+255123456789'
      },
      payment_method: 'mobile_money',
      payment_details: {
        phone: '+255123456789',
        provider: 'M-Pesa'
      },
      is_guest: false,
      use_cart: true,
      cart_items: [
        {
          productId: product._id,
          variantSku: variant ? variant.sku : null,
          quantity: 1,
          price: variant ? variant.price : product.price,
          imei: selectedIMEI
        }
      ]
    };
    
    console.log('\n📦 Order data:');
    console.log(JSON.stringify(orderData, null, 2));
    
    // Test order creation
    console.log('\n🛒 Creating test order...');
    const orderResponse = await axios.post(`${API_BASE}/orders`, orderData);
    
    console.log('✅ Order created successfully!');
    console.log(`Order ID: ${orderResponse.data.data._id}`);
    console.log(`Order Number: ${orderResponse.data.data.order_number}`);
    console.log(`Total: TZS ${orderResponse.data.data.total_amount.toLocaleString()}`);
    
  } catch (error) {
    console.error('❌ Test failed:');
    if (error.response) {
      console.error(`Status: ${error.response.status}`);
      console.error(`Message: ${error.response.data.message || error.response.data.error}`);
      console.error('Response data:', JSON.stringify(error.response.data, null, 2));
    } else {
      console.error(error.message);
    }
  }
}

testGuestOrder();
