/**
 * Seed Phone Inventory with IMEI tracking
 * Creates sample inventory with individual phone devices and IMEI numbers
 */

const mongoose = require('mongoose');
const Product = require('../models/Product');
const Inventory = require('../models/Inventory');
const phoneInventoryService = require('../services/phoneInventoryService');

// MongoDB connection
const connectDB = async () => {
  try {
    await mongoose.connect('mongodb://localhost:27017/aixcelerate-dev');
    console.log('MongoDB connected successfully');
  } catch (error) {
    console.error('MongoDB connection error:', error);
    process.exit(1);
  }
};

// Sample IMEI numbers (for testing only - these are not real IMEIs)
const generateTestIMEI = (base) => {
  const baseStr = base.toString().padStart(15, '0');
  return baseStr.substring(0, 15); // Ensure exactly 15 digits
};

const sampleDevices = [
  // Smartphones with IMEI tracking
  {
    productName: 'iPhone 15 Pro',
    variantSku: 'IPH15PRO128-NT',
    trackingType: 'imei',
    devices: [
      { imei: generateTestIMEI(123456789012340), condition: 'new', warrantyMonths: 12 },
      { imei: generateTestIMEI(123456789012341), condition: 'new', warrantyMonths: 12 },
      { imei: generateTestIMEI(123456789012342), condition: 'new', warrantyMonths: 12 },
      { imei: generateTestIMEI(123456789012343), condition: 'open_box', warrantyMonths: 12 },
      { imei: generateTestIMEI(123456789012344), condition: 'new', warrantyMonths: 12 }
    ]
  },
  {
    productName: 'iPhone 15 Pro',
    variantSku: 'IPH15PRO256-NT',
    trackingType: 'imei',
    devices: [
      { imei: generateTestIMEI(123456789012350), condition: 'new', warrantyMonths: 12 },
      { imei: generateTestIMEI(123456789012351), condition: 'new', warrantyMonths: 12 },
      { imei: generateTestIMEI(123456789012352), condition: 'refurbished', warrantyMonths: 6 }
    ]
  },
  {
    productName: 'Samsung Galaxy S24 Ultra',
    variantSku: 'SGS24U256-TG',
    trackingType: 'imei',
    devices: [
      { imei: generateTestIMEI(234567890123450), condition: 'new', warrantyMonths: 24 },
      { imei: generateTestIMEI(234567890123451), condition: 'new', warrantyMonths: 24 },
      { imei: generateTestIMEI(234567890123452), condition: 'new', warrantyMonths: 24 },
      { imei: generateTestIMEI(234567890123453), condition: 'open_box', warrantyMonths: 24 }
    ]
  },
  {
    productName: 'Samsung Galaxy S24 Ultra',
    variantSku: 'SGS24U512-TG',
    trackingType: 'imei',
    devices: [
      { imei: generateTestIMEI(234567890123460), condition: 'new', warrantyMonths: 24 },
      { imei: generateTestIMEI(234567890123461), condition: 'new', warrantyMonths: 24 }
    ]
  },
  {
    productName: 'Xiaomi Redmi Note 13',
    variantSku: 'XRN13128-MB',
    trackingType: 'imei',
    devices: [
      { imei: generateTestIMEI(345678901234560), condition: 'new', warrantyMonths: 12 },
      { imei: generateTestIMEI(345678901234561), condition: 'new', warrantyMonths: 12 },
      { imei: generateTestIMEI(345678901234562), condition: 'new', warrantyMonths: 12 },
      { imei: generateTestIMEI(345678901234563), condition: 'new', warrantyMonths: 12 },
      { imei: generateTestIMEI(345678901234564), condition: 'new', warrantyMonths: 12 },
      { imei: generateTestIMEI(345678901234565), condition: 'refurbished', warrantyMonths: 6 },
      { imei: generateTestIMEI(345678901234566), condition: 'open_box', warrantyMonths: 12 }
    ]
  },
  {
    productName: 'Xiaomi Redmi Note 13',
    variantSku: 'XRN13256-MB',
    trackingType: 'imei',
    devices: [
      { imei: generateTestIMEI(345678901234570), condition: 'new', warrantyMonths: 12 },
      { imei: generateTestIMEI(345678901234571), condition: 'new', warrantyMonths: 12 },
      { imei: generateTestIMEI(345678901234572), condition: 'new', warrantyMonths: 12 }
    ]
  },

  // High-value items with serial number tracking
  {
    productName: 'Apple Watch Series 9',
    variantSku: 'SWT-APP-AW9MID-001',
    trackingType: 'serial',
    devices: [
      { serial: 'AW9-001-' + Date.now().toString().slice(-6), condition: 'new', warrantyMonths: 12 },
      { serial: 'AW9-002-' + (Date.now() + 1).toString().slice(-6), condition: 'new', warrantyMonths: 12 },
      { serial: 'AW9-003-' + (Date.now() + 2).toString().slice(-6), condition: 'new', warrantyMonths: 12 }
    ]
  },
  {
    productName: 'Apple AirPods Pro 2nd Gen',
    variantSku: 'EBD-APP-AIRPRO2-001',
    trackingType: 'serial',
    devices: [
      { serial: 'APP-001-' + Date.now().toString().slice(-6), condition: 'new', warrantyMonths: 12 },
      { serial: 'APP-002-' + (Date.now() + 1).toString().slice(-6), condition: 'new', warrantyMonths: 12 },
      { serial: 'APP-003-' + (Date.now() + 2).toString().slice(-6), condition: 'open_box', warrantyMonths: 12 }
    ]
  }
];

const seedPhoneInventory = async () => {
  try {
    console.log('🔄 Starting phone inventory seeding...');

    // Clear existing inventory
    await Inventory.deleteMany({});
    console.log('✅ Cleared existing inventory');

    let totalDevicesAdded = 0;

    for (const deviceGroup of sampleDevices) {
      // Find the product
      const product = await Product.findOne({ name: deviceGroup.productName });
      if (!product) {
        console.log(`❌ Product not found: ${deviceGroup.productName}`);
        continue;
      }

      console.log(`📱 Processing ${deviceGroup.productName} - ${deviceGroup.variantSku}`);

      // Create inventory item
      const inventoryItem = new Inventory({
        product: product._id,
        variant_sku: deviceGroup.variantSku,
        location: 'main_warehouse',
        quantity_on_hand: deviceGroup.devices.length,
        quantity_reserved: 0,
        quantity_available: deviceGroup.devices.length,
        reorder_point: 5,
        reorder_quantity: 10,
        max_stock_level: 50,
        average_cost: product.price * 0.7, // Assume 70% cost ratio
        last_cost: product.price * 0.7,
        devices: deviceGroup.devices.map(device => {
          const baseDevice = {
            condition: device.condition,
            warranty_months: device.warrantyMonths,
            status: 'available',
            received_at: new Date(),
            purchase_price: product.price * 0.7,
            supplier: 'Phone Point Dar Suppliers Ltd'
          };

          // Add IMEI or serial number based on tracking type
          if (deviceGroup.trackingType === 'imei') {
            baseDevice.imei = device.imei;
          } else if (deviceGroup.trackingType === 'serial') {
            baseDevice.serial_number = device.serial;
          }

          return baseDevice;
        })
      });

      await inventoryItem.save();
      totalDevicesAdded += deviceGroup.devices.length;

      console.log(`  ✅ Added ${deviceGroup.devices.length} devices for ${deviceGroup.variantSku}`);
    }

    console.log(`\n🎉 Device inventory seeding completed!`);
    console.log(`📊 Summary:`);
    console.log(`   - Total devices added: ${totalDevicesAdded}`);
    console.log(`   - Product variants: ${sampleDevices.length}`);
    console.log(`   - Smartphones have unique IMEI numbers`);
    console.log(`   - High-value items have serial number tracking`);
    console.log(`   - All devices are available for reservation and sale`);

    // Display inventory summary
    const inventoryItems = await Inventory.find({}).populate('product', 'name');
    console.log(`\n📋 Inventory Summary:`);
    
    for (const item of inventoryItems) {
      const availableDevices = item.devices.filter(d => d.status === 'available').length;
      console.log(`   ${item.product.name} (${item.variant_sku}): ${availableDevices} available devices`);
    }

  } catch (error) {
    console.error('❌ Error seeding phone inventory:', error);
    throw error;
  }
};

// Run the seeder
const runSeeder = async () => {
  try {
    await connectDB();
    await seedPhoneInventory();
    console.log('\n✅ Phone inventory seeding completed successfully!');
    process.exit(0);
  } catch (error) {
    console.error('❌ Seeding failed:', error);
    process.exit(1);
  }
};

// Execute if run directly
if (require.main === module) {
  runSeeder();
}

module.exports = { seedPhoneInventory };
