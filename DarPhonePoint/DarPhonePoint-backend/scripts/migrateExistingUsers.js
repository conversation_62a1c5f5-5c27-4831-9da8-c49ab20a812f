const mongoose = require('mongoose');
const User = require('../models/User');
const config = require('../config/config');

/**
 * Migration script to mark existing users as email verified
 * This ensures existing users can continue logging in after email verification is implemented
 */

async function migrateExistingUsers() {
  try {
    // Connect to MongoDB
    await mongoose.connect(config.MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });

    console.log('Connected to MongoDB');

    // Find all users who don't have the isEmailVerified field or it's false
    const usersToUpdate = await User.find({
      $or: [
        { isEmailVerified: { $exists: false } },
        { isEmailVerified: false }
      ]
    });

    console.log(`Found ${usersToUpdate.length} users to update`);

    if (usersToUpdate.length === 0) {
      console.log('No users need to be updated');
      return;
    }

    // Update all existing users to be verified
    const result = await User.updateMany(
      {
        $or: [
          { isEmailVerified: { $exists: false } },
          { isEmailVerified: false }
        ]
      },
      {
        $set: {
          isEmailVerified: true
        },
        $unset: {
          emailVerificationToken: 1,
          emailVerificationExpire: 1
        }
      }
    );

    console.log(`Successfully updated ${result.modifiedCount} users`);
    console.log('All existing users are now marked as email verified');

    // Verify the update
    const verifiedCount = await User.countDocuments({ isEmailVerified: true });
    const totalCount = await User.countDocuments();
    
    console.log(`Verification complete: ${verifiedCount}/${totalCount} users are now verified`);

  } catch (error) {
    console.error('Migration failed:', error);
    process.exit(1);
  } finally {
    // Close the connection
    await mongoose.connection.close();
    console.log('Database connection closed');
    process.exit(0);
  }
}

// Run the migration if this script is executed directly
if (require.main === module) {
  console.log('Starting user migration...');
  migrateExistingUsers();
}

module.exports = migrateExistingUsers;
