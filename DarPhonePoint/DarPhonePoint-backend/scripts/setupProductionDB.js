const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const config = require('../config/config');
const logger = require('../utils/logger');

// Import models
const User = require('../models/User');
const Product = require('../models/Product');

/**
 * Setup production database with admin user and indexes
 */
async function setupProductionDB() {
  try {
    // Connect to database
    await mongoose.connect(config.MONGODB_URI, {
      maxPoolSize: 10,
      minPoolSize: 5,
      serverSelectionTimeoutMS: 5000,
      socketTimeoutMS: 45000,
    });

    logger.info('Connected to MongoDB for production setup');

    // Clean database (disabled - uncomment only if you want fresh start)
    // await cleanDatabase();

    // Create admin user
    await createAdminUser();

    // Create database indexes for production performance
    await createDatabaseIndexes();

    // Create sample products if none exist
    await createSampleProducts();

    logger.info('Production database setup completed successfully');

  } catch (error) {
    logger.error('Production database setup failed:', error);
    throw error;
  } finally {
    await mongoose.connection.close();
    logger.info('Database connection closed');
  }
}

/**
 * Clean database (use with caution)
 */
async function cleanDatabase() {
  logger.warn('Cleaning database - all data will be lost!');

  const collections = await mongoose.connection.db.listCollections().toArray();

  for (const collection of collections) {
    await mongoose.connection.db.collection(collection.name).deleteMany({});
    logger.info(`Cleaned collection: ${collection.name}`);
  }

  logger.info('Database cleaned successfully');
}

/**
 * Create admin user
 */
async function createAdminUser() {
  try {
    // Check if admin user already exists
    const existingAdmin = await User.findOne({ email: '<EMAIL>' });

    if (existingAdmin) {
      logger.info('Admin user already exists');
      return;
    }

    // Create admin user (password will be auto-hashed by User model pre-save hook)
    const adminUser = new User({
      name: 'System Administrator',
      email: '<EMAIL>',
      password: 'Admin123!@#',
      role: 'admin',
      status: 'active',
      user_type: 'premium',
      created_at: new Date(),
      updated_at: new Date()
    });

    await adminUser.save();
    logger.info('Admin user created successfully');
    logger.info('Admin credentials: <EMAIL> / Admin123!@#');

  } catch (error) {
    logger.error('Failed to create admin user:', error);
    throw error;
  }
}

/**
 * Create database indexes for production performance
 */
async function createDatabaseIndexes() {
  try {
    logger.info('Creating database indexes...');

    // User collection indexes
    await User.collection.createIndex({ email: 1 }, { unique: true });
    await User.collection.createIndex({ role: 1 });
    await User.collection.createIndex({ isActive: 1 });
    await User.collection.createIndex({ createdAt: -1 });
    await User.collection.createIndex({ emailVerified: 1 });
    logger.info('User indexes created');

    // Order collection indexes (if Order model exists)
    try {
      const Order = require('../models/Order');
      await Order.collection.createIndex({ user: 1, createdAt: -1 });
      await Order.collection.createIndex({ transaction_id: 1 }, { unique: true });
      await Order.collection.createIndex({ payment_status: 1 });
      await Order.collection.createIndex({ createdAt: -1 });
      await Order.collection.createIndex({ amount: 1 });
      logger.info('Order indexes created');
    } catch (err) {
      logger.warn('Order model not found, skipping order indexes');
    }

    // Product collection indexes
    await Product.collection.createIndex({ isActive: 1 });
    await Product.collection.createIndex({ category: 1 });
    await Product.collection.createIndex({ price: 1 });
    await Product.collection.createIndex({ createdAt: -1 });
    await Product.collection.createIndex({ name: 'text', description: 'text' });
    logger.info('Product indexes created');

    // Lead collection indexes (if Lead model exists)
    try {
      const Lead = require('../models/Lead');
      await Lead.collection.createIndex({ email: 1 }, { unique: true });
      await Lead.collection.createIndex({ status: 1 });
      await Lead.collection.createIndex({ createdAt: -1 });
      await Lead.collection.createIndex({ source: 1 });
      logger.info('Lead indexes created');
    } catch (err) {
      logger.warn('Lead model not found, skipping lead indexes');
    }

    logger.info('All database indexes created successfully');

  } catch (error) {
    logger.error('Failed to create database indexes:', error);
    throw error;
  }
}

/**
 * Create sample products if none exist
 */
async function createSampleProducts() {
  try {
    const productCount = await Product.countDocuments();

    if (productCount > 0) {
      logger.info(`${productCount} products already exist`);
      return;
    }

    const sampleProducts = [
      {
        name: '50 Essential AI Prompts',
        slug: '50-essential-ai-prompts',
        description: 'A comprehensive collection of 50 essential AI prompts to boost your productivity',
        price: 29.99,
        product_type: 'lead_magnet',
        file_path: '/uploads/products/50_Essential_AI_Prompts.pdf',
        features: [
          'Instant download',
          'PDF format',
          'Ready to use prompts',
          'Productivity focused'
        ],
        is_active: true,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        name: 'AI Productivity Masterclass',
        slug: 'ai-productivity-masterclass',
        description: 'Complete guide to using AI tools for maximum productivity',
        price: 99.99,
        product_type: 'premium',
        file_path: '/uploads/products/ai_productivity_course.zip',
        features: [
          'Video tutorials',
          'Downloadable resources',
          'Lifetime access',
          'Expert guidance'
        ],
        is_active: true,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        name: 'Free AI Starter Guide',
        slug: 'free-ai-starter-guide',
        description: 'Get started with AI tools - completely free guide for beginners',
        price: 0,
        product_type: 'lead_magnet',
        file_path: '/uploads/products/Free_AI_Starter_Guide.pdf',
        features: [
          'Beginner friendly',
          'Step-by-step guide',
          'Free download',
          'Quick start tips'
        ],
        is_active: true,
        created_at: new Date(),
        updated_at: new Date()
      }
    ];

    await Product.insertMany(sampleProducts);
    logger.info(`Created ${sampleProducts.length} sample products`);

  } catch (error) {
    logger.error('Failed to create sample products:', error);
    throw error;
  }
}

// Run setup if called directly
if (require.main === module) {
  setupProductionDB()
    .then(() => {
      logger.info('Production database setup completed');
      process.exit(0);
    })
    .catch((error) => {
      logger.error('Production database setup failed:', error);
      process.exit(1);
    });
}

module.exports = {
  setupProductionDB,
  createAdminUser,
  createDatabaseIndexes,
  createSampleProducts,
  cleanDatabase
};
