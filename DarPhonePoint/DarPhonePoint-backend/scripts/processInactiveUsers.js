/**
 * Process Inactive Users Script
 *
 * This script finds users who haven't logged in for a specified period
 * and triggers the user inactivity email sequence.
 * It should be run daily via a cron job.
 */

const mongoose = require('mongoose');
const User = require('../models/User');
const EmailSequence = require('../models/EmailSequence');
const EmailSequenceTracking = require('../models/EmailSequenceTracking');
const dotenv = require('dotenv');
const logger = require('../utils/logger');

// Load environment variables
dotenv.config();

// Connect to MongoDB
mongoose.connect(process.env.MONGO_URI || 'mongodb://localhost:27017/aixcelerate')
  .then(() => {
    logger.info('MongoDB connected for inactive user processing');
    processInactiveUsers();
  })
  .catch(err => {
    logger.error('MongoDB connection error:', err);
    process.exit(1);
  });

/**
 * Process inactive users
 */
async function processInactiveUsers() {
  try {
    logger.info('Processing inactive users...');

    // Find users who haven't logged in for at least 30 days
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const inactiveUsers = await User.find({
      last_login: { $lt: thirtyDaysAgo },
      status: 'active'
    });

    logger.info(`Found ${inactiveUsers.length} inactive users`);

    // Find the user inactivity email sequence
    const inactivitySequence = await EmailSequence.findOne({
      trigger: 'user_inactive',
      isActive: true
    });

    if (!inactivitySequence) {
      logger.warn('No active user inactivity email sequence found');
      mongoose.disconnect();
      return;
    }

    let processedCount = 0;
    let errorCount = 0;

    // Process each inactive user
    for (const user of inactiveUsers) {
      try {
        // Skip if user has no email
        if (!user.email) {
          continue;
        }

        // Check if user already has this sequence
        const existingTracking = await EmailSequenceTracking.findOne({
          user: user._id,
          email_sequence: inactivitySequence._id,
          is_completed: false
        });

        if (existingTracking) {
          // Already being processed, skip
          continue;
        }

        // Create new tracking for this user and sequence
        const tracking = new EmailSequenceTracking({
          user: user._id,
          email: user.email,
          email_sequence: inactivitySequence._id,
          current_position: 0,
          next_email_date: new Date(), // Send first email immediately
          is_completed: false,
          is_paused: false,
          unsubscribed: false,
          metadata: {
            days_inactive: Math.floor((new Date() - user.last_login) / (1000 * 60 * 60 * 24)),
            last_login: user.last_login
          }
        });

        await tracking.save();

        // Mark user as sent re-engagement sequence
        user.re_engagement_sent = true;
        await user.save();

        processedCount++;

        logger.info(`Created inactivity sequence for user ${user.email}`);
      } catch (err) {
        logger.error(`Error processing inactive user ${user.email}:`, err);
        errorCount++;
      }
    }

    logger.info(`Inactive user processing complete. Processed: ${processedCount}, Errors: ${errorCount}`);
    mongoose.disconnect();
  } catch (err) {
    logger.error('Error processing inactive users:', err);
    mongoose.disconnect();
    process.exit(1);
  }
}
