/**
 * Critical Database Indexes Script
 * Adds missing compound indexes identified in the deep data analysis
 */

const mongoose = require('mongoose');
const config = require('../config/config');
const logger = require('../utils/logger');

// Import models to ensure they're registered
require('../models/Product');
require('../models/User');
require('../models/Order');
require('../models/Cart');
require('../models/Inventory');
require('../models/Analytics');

// Helper function to create index with error handling
async function createIndexSafely(collection, indexSpec, options, indexName) {
  try {
    await collection.createIndex(indexSpec, options);
    console.log(`✅ Added: ${indexName}`);
    return true;
  } catch (error) {
    if (error.code === 11000 || error.code === 85 || error.codeName === 'IndexOptionsConflict' || error.codeName === 'IndexKeySpecsConflict') {
      console.log(`⚠️  Skipped: ${indexName} (already exists or conflicts)`);
      return false;
    } else if (error.code === 67) {
      console.log(`⚠️  Skipped: ${indexName} (text index limit reached)`);
      return false;
    } else {
      console.error(`❌ Failed: ${indexName} - ${error.message}`);
      return false;
    }
  }
}

async function addCriticalIndexes() {
  try {
    console.log('🔧 Starting critical database index creation...');

    // Connect to database
    await mongoose.connect(config.MONGODB_URI);

    console.log('✅ Connected to MongoDB');

    const db = mongoose.connection.db;
    let successCount = 0;
    let skipCount = 0;
    
    // Product Collection Indexes
    console.log('📦 Adding Product collection indexes...');

    // Critical compound index for product filtering (category + brand + active status)
    if (await createIndexSafely(
      db.collection('products'),
      { is_active: 1, category: 1, brand: 1 },
      { name: 'idx_active_category_brand', background: true },
      'idx_active_category_brand'
    )) successCount++;
    else skipCount++;

    // Price range queries with active status
    if (await createIndexSafely(
      db.collection('products'),
      { is_active: 1, price: 1 },
      { name: 'idx_active_price', background: true },
      'idx_active_price'
    )) successCount++;
    else skipCount++;

    // Stock queries for inventory management
    if (await createIndexSafely(
      db.collection('products'),
      { track_inventory: 1, stock_quantity: 1 },
      { name: 'idx_inventory_stock', background: true },
      'idx_inventory_stock'
    )) successCount++;
    else skipCount++;

    // Featured products with category
    if (await createIndexSafely(
      db.collection('products'),
      { is_active: 1, is_featured: 1, category: 1 },
      { name: 'idx_active_featured_category', background: true },
      'idx_active_featured_category'
    )) successCount++;
    else skipCount++;
    
    // Text search optimization (skip if already exists)
    if (await createIndexSafely(
      db.collection('products'),
      { is_active: 1, name: 'text', brand: 'text', description: 'text' },
      { name: 'idx_active_text_search', background: true },
      'idx_active_text_search'
    )) successCount++;
    else skipCount++;
    
    // Order Collection Indexes
    console.log('📋 Adding Order collection indexes...');

    // User orders with status and date
    if (await createIndexSafely(
      db.collection('orders'),
      { user: 1, order_status: 1, created_at: -1 },
      { name: 'idx_user_status_date', background: true },
      'idx_user_status_date'
    )) successCount++;
    else skipCount++;

    // Admin order management
    if (await createIndexSafely(
      db.collection('orders'),
      { order_status: 1, fulfillment_status: 1, created_at: -1 },
      { name: 'idx_status_fulfillment_date', background: true },
      'idx_status_fulfillment_date'
    )) successCount++;
    else skipCount++;

    // Payment status queries
    if (await createIndexSafely(
      db.collection('orders'),
      { payment_status: 1, created_at: -1 },
      { name: 'idx_payment_status_date', background: true },
      'idx_payment_status_date'
    )) successCount++;
    else skipCount++;

    // Customer email lookup
    if (await createIndexSafely(
      db.collection('orders'),
      { customer_email: 1, created_at: -1 },
      { name: 'idx_customer_email_date', background: true },
      'idx_customer_email_date'
    )) successCount++;
    else skipCount++;
    
    // Inventory Collection Indexes
    console.log('📦 Adding Inventory collection indexes...');

    // Product inventory lookup
    if (await createIndexSafely(
      db.collection('inventories'),
      { product: 1, location: 1 },
      { name: 'idx_product_location', background: true },
      'idx_product_location'
    )) successCount++;
    else skipCount++;

    // Low stock alerts
    if (await createIndexSafely(
      db.collection('inventories'),
      { quantity_available: 1, reorder_point: 1 },
      { name: 'idx_stock_reorder', background: true },
      'idx_stock_reorder'
    )) successCount++;
    else skipCount++;

    // IMEI tracking for phones
    if (await createIndexSafely(
      db.collection('inventories'),
      { 'devices.imei': 1 },
      { name: 'idx_device_imei', background: true, sparse: true },
      'idx_device_imei'
    )) successCount++;
    else skipCount++;

    // Cart Collection Indexes
    console.log('🛒 Adding Cart collection indexes...');

    // User cart lookup
    if (await createIndexSafely(
      db.collection('carts'),
      { user: 1, updated_at: -1 },
      { name: 'idx_user_updated', background: true },
      'idx_user_updated'
    )) successCount++;
    else skipCount++;
    
    // Analytics Collection Indexes
    console.log('📊 Adding Analytics collection indexes...');

    // Event tracking
    if (await createIndexSafely(
      db.collection('analytics'),
      { event: 1, timestamp: -1 },
      { name: 'idx_event_timestamp', background: true },
      'idx_event_timestamp'
    )) successCount++;
    else skipCount++;

    // User analytics
    if (await createIndexSafely(
      db.collection('analytics'),
      { userId: 1, event: 1, timestamp: -1 },
      { name: 'idx_user_event_timestamp', background: true },
      'idx_user_event_timestamp'
    )) successCount++;
    else skipCount++;

    // Session analytics
    if (await createIndexSafely(
      db.collection('analytics'),
      { sessionId: 1, timestamp: -1 },
      { name: 'idx_session_timestamp', background: true },
      'idx_session_timestamp'
    )) successCount++;
    else skipCount++;
    
    // User Collection Indexes (additional)
    console.log('👥 Adding User collection indexes...');

    // Role and status queries
    if (await createIndexSafely(
      db.collection('users'),
      { role: 1, is_active: 1, created_at: -1 },
      { name: 'idx_role_active_created', background: true },
      'idx_role_active_created'
    )) successCount++;
    else skipCount++;

    // Email verification status
    if (await createIndexSafely(
      db.collection('users'),
      { isEmailVerified: 1, created_at: -1 },
      { name: 'idx_email_verified_created', background: true },
      'idx_email_verified_created'
    )) successCount++;
    else skipCount++;

    console.log(`\n🎉 Index creation completed!`);
    console.log(`✅ Successfully created: ${successCount} indexes`);
    console.log(`⚠️  Skipped (already exist): ${skipCount} indexes`);

    // Get index statistics
    const collections = ['products', 'orders', 'inventories', 'carts', 'analytics', 'users'];

    console.log('\n📊 Index Statistics:');
    for (const collectionName of collections) {
      try {
        const indexes = await db.collection(collectionName).indexes();
        console.log(`${collectionName}: ${indexes.length} indexes`);
      } catch (error) {
        console.log(`${collectionName}: Collection not found or error getting indexes`);
      }
    }

    logger.info('Critical database indexes processed successfully', {
      indexesCreated: successCount,
      indexesSkipped: skipCount,
      collections: collections.length
    });
    
  } catch (error) {
    console.error('❌ Error adding indexes:', error);
    logger.error('Database index creation failed:', error);
    throw error;
  } finally {
    await mongoose.connection.close();
    console.log('🔌 Database connection closed');
  }
}

// Run the script
if (require.main === module) {
  addCriticalIndexes()
    .then(() => {
      console.log('✅ Index creation completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Index creation failed:', error);
      process.exit(1);
    });
}

module.exports = addCriticalIndexes;
