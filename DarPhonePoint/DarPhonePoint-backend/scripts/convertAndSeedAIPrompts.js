const fs = require('fs');
const path = require('path');
const pdf = require('html-pdf');
const mongoose = require('mongoose');
const config = require('../config/config');
const Product = require('../models/Product');

// Connect to MongoDB
mongoose.connect(config.MONGODB_URI, {
  useNewUrlParser: true,
  useUnifiedTopology: true
})
.then(() => console.log('MongoDB Connected...'))
.catch(err => {
  console.error('MongoDB connection error:', err);
  process.exit(1);
});

// Path to the HTML file
const htmlFilePath = path.join(__dirname, '../../ai_prompts_lead_magnet/index.html');
// Output PDF path
const outputDir = path.join(__dirname, '../public/products/ai_prompts_lead_magnet');
const outputPdfPath = path.join(outputDir, '50_Essential_AI_Prompts.pdf');

// Ensure the output directory exists
if (!fs.existsSync(outputDir)) {
  fs.mkdirSync(outputDir, { recursive: true });
  console.log(`Created directory: ${outputDir}`);
}

// Read the HTML file
fs.readFile(htmlFilePath, 'utf8', (err, html) => {
  if (err) {
    console.error('Error reading HTML file:', err);
    mongoose.disconnect();
    return;
  }

  console.log('HTML file read successfully');

  // PDF generation options
  const options = {
    format: 'Letter',
    border: {
      top: '0.5in',
      right: '0.5in',
      bottom: '0.5in',
      left: '0.5in'
    },
    footer: {
      height: '0.5in',
      contents: {
        default: '<div style="text-align: center; font-size: 10px; color: #777;">© 2023 AIXcelerate - All rights reserved</div>'
      }
    }
  };

  // Generate PDF
  console.log('Generating PDF...');
  pdf.create(html, options).toFile(outputPdfPath, async function(err, res) {
    if (err) {
      console.error('Error generating PDF:', err);
      mongoose.disconnect();
      return;
    }

    console.log(`PDF successfully created at: ${res.filename}`);

    // Product data for the database
    const productData = {
      name: '50 Essential AI Prompts',
      slug: '50-essential-ai-prompts',
      description: 'A collection of 50 powerful AI prompts to skyrocket your productivity across writing, research, business, and problem-solving tasks.',
      price: 0,
      product_type: 'lead_magnet',
      file_path: 'products/ai_prompts_lead_magnet/50_Essential_AI_Prompts.pdf',
      features: [
        '26-page downloadable PDF guide',
        '50 proven AI prompt templates',
        'Example outputs for every prompt',
        'Use cases and implementation tips',
        'Immediate download after signup'
      ],
      is_active: true
    };

    try {
      // Check if product already exists
      const existingProduct = await Product.findOne({ slug: productData.slug });
      
      if (existingProduct) {
        console.log('Product already exists in the database. Updating...');
        const updatedProduct = await Product.findOneAndUpdate(
          { slug: productData.slug },
          { ...productData, updated_at: Date.now() },
          { new: true }
        );
        console.log(`Product updated: ${updatedProduct.name}`);
      } else {
        console.log('Adding new product to the database...');
        const newProduct = await Product.create(productData);
        console.log(`New product added: ${newProduct.name}`);
      }
      
      console.log('Process completed successfully');
      mongoose.disconnect();
      console.log('Database connection closed');
    } catch (error) {
      console.error('Error updating database:', error);
      mongoose.disconnect();
    }
  });
});
