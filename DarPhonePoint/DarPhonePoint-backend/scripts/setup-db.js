const readline = require('readline');
const fs = require('fs');
const path = require('path');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

const questions = [
  {
    name: 'host',
    question: 'Enter MongoDB host (default: localhost): ',
    default: 'localhost'
  },
  {
    name: 'port',
    question: 'Enter MongoDB port (default: 27017): ',
    default: '27017'
  },
  {
    name: 'database',
    question: 'Enter database name (default: aixcelerate): ',
    default: 'aixcelerate'
  },
  {
    name: 'username',
    question: 'Enter MongoDB username (leave empty if none): ',
    default: ''
  },
  {
    name: 'password',
    question: 'Enter MongoDB password (leave empty if none): ',
    default: ''
  }
];

const answers = {};

const askQuestion = (index) => {
  if (index === questions.length) {
    generateConnectionString();
    return;
  }

  const question = questions[index];
  rl.question(question.question, (answer) => {
    answers[question.name] = answer || question.default;
    askQuestion(index + 1);
  });
};

const generateConnectionString = () => {
  let uri = 'mongodb://';
  
  if (answers.username && answers.password) {
    uri += `${encodeURIComponent(answers.username)}:${encodeURIComponent(answers.password)}@`;
  }
  
  uri += `${answers.host}:${answers.port}/${answers.database}`;

  // Update .env file
  const envPath = path.join(__dirname, '..', '.env');
  let envContent = '';

  try {
    envContent = fs.readFileSync(envPath, 'utf8');
  } catch (error) {
    // File doesn't exist, create new
    envContent = '';
  }

  // Update or add MONGODB_URI
  if (envContent.includes('MONGODB_URI=')) {
    envContent = envContent.replace(/MONGODB_URI=.*/, `MONGODB_URI=${uri}`);
  } else {
    envContent += `\nMONGODB_URI=${uri}`;
  }

  // Update or add MongoDB credentials
  if (answers.username) {
    if (envContent.includes('MONGODB_USER=')) {
      envContent = envContent.replace(/MONGODB_USER=.*/, `MONGODB_USER=${answers.username}`);
    } else {
      envContent += `\nMONGODB_USER=${answers.username}`;
    }
  }

  if (answers.password) {
    if (envContent.includes('MONGODB_PASSWORD=')) {
      envContent = envContent.replace(/MONGODB_PASSWORD=.*/, `MONGODB_PASSWORD=${answers.password}`);
    } else {
      envContent += `\nMONGODB_PASSWORD=${answers.password}`;
    }
  }

  fs.writeFileSync(envPath, envContent.trim() + '\n');
  console.log('\nMongoDB connection string has been updated in .env file');
  console.log('Connection string:', uri);
  rl.close();
};

console.log('MongoDB Connection Setup\n');
askQuestion(0); 