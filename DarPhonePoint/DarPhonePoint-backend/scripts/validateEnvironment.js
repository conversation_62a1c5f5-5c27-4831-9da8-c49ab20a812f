#!/usr/bin/env node

/**
 * Environment Validation Script for Phone Point Dar
 * Validates that all required environment variables are properly configured
 */

const fs = require('fs');
const path = require('path');

// Load environment variables from .env file
require('dotenv').config();

// ANSI color codes for console output
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m',
  reset: '\x1b[0m',
  bright: '\x1b[1m'
};

// Required environment variables by category
const requiredVars = {
  critical: [
    'NODE_ENV',
    'MONGODB_URI',
    'JWT_SECRET',
    'FRONTEND_URL'
  ],
  security: [
    'JWT_REFRESH_SECRET',
    'SESSION_SECRET',
    'BCRYPT_ROUNDS'
  ],
  email: [
    'EMAIL_HOST',
    'EMAIL_USER',
    'EMAIL_PASSWORD',
    'EMAIL_FROM'
  ],
  urls: [
    'BASE_URL',
    'API_URL',
    'FRONTEND_URL'
  ],
  production: [
    'CORS_ORIGIN',
    'RATE_LIMIT_MAX_REQUESTS',
    'LOG_LEVEL'
  ]
};

// Environment-specific requirements
const productionRequired = [
  'MONGODB_URI',
  'JWT_SECRET',
  'JWT_REFRESH_SECRET',
  'EMAIL_PASSWORD',
  'CORS_ORIGIN'
];

// Security validation patterns
const securityPatterns = {
  JWT_SECRET: {
    minLength: 32,
    pattern: /^[A-Za-z0-9+/=_-]+$/,
    message: 'JWT_SECRET should be at least 32 characters and contain only alphanumeric characters and +/=_-'
  },
  JWT_REFRESH_SECRET: {
    minLength: 32,
    pattern: /^[A-Za-z0-9+/=_-]+$/,
    message: 'JWT_REFRESH_SECRET should be at least 32 characters and contain only alphanumeric characters and +/=_-'
  },
  MONGODB_URI: {
    pattern: /^mongodb(\+srv)?:\/\/.+/,
    message: 'MONGODB_URI should start with mongodb:// or mongodb+srv://'
  },
  EMAIL_USER: {
    pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    message: 'EMAIL_USER should be a valid email address'
  },
  EMAIL_FROM: {
    pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    message: 'EMAIL_FROM should be a valid email address'
  }
};

// URL validation patterns
const urlPatterns = {
  development: /^https?:\/\/localhost(:\d+)?/,
  production: /^https:\/\/[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/
};

class EnvironmentValidator {
  constructor() {
    this.errors = [];
    this.warnings = [];
    this.info = [];
    this.nodeEnv = process.env.NODE_ENV || 'development';
  }

  log(message, color = 'white') {
    console.log(`${colors[color]}${message}${colors.reset}`);
  }

  logBright(message, color = 'white') {
    console.log(`${colors.bright}${colors[color]}${message}${colors.reset}`);
  }

  addError(message) {
    this.errors.push(message);
  }

  addWarning(message) {
    this.warnings.push(message);
  }

  addInfo(message) {
    this.info.push(message);
  }

  validateRequired() {
    this.logBright('\n🔍 Validating Required Environment Variables...', 'cyan');

    // Check critical variables
    requiredVars.critical.forEach(varName => {
      if (!process.env[varName]) {
        this.addError(`Missing critical environment variable: ${varName}`);
      } else {
        this.log(`✅ ${varName}: Set`, 'green');
      }
    });

    // Check production-specific requirements
    if (this.nodeEnv === 'production') {
      productionRequired.forEach(varName => {
        if (!process.env[varName]) {
          this.addError(`Missing production environment variable: ${varName}`);
        }
      });
    }
  }

  validateSecurity() {
    this.logBright('\n🔒 Validating Security Configuration...', 'cyan');

    Object.entries(securityPatterns).forEach(([varName, config]) => {
      const value = process.env[varName];
      
      if (!value) {
        if (requiredVars.security.includes(varName) || this.nodeEnv === 'production') {
          this.addError(`Missing security variable: ${varName}`);
        }
        return;
      }

      // Check minimum length
      if (config.minLength && value.length < config.minLength) {
        this.addError(`${varName} is too short (minimum ${config.minLength} characters)`);
      }

      // Check pattern
      if (config.pattern && !config.pattern.test(value)) {
        this.addError(`${varName} format invalid: ${config.message}`);
      }

      if (!this.errors.some(err => err.includes(varName))) {
        this.log(`✅ ${varName}: Valid`, 'green');
      }
    });

    // Check for default/weak secrets
    const weakSecrets = [
      'secret',
      'password',
      'default',
      'test',
      'dev',
      'development',
      'aixcelerate',
      'phonepointdar'
    ];

    ['JWT_SECRET', 'JWT_REFRESH_SECRET', 'SESSION_SECRET'].forEach(varName => {
      const value = process.env[varName];
      if (value && weakSecrets.some(weak => value.toLowerCase().includes(weak))) {
        if (this.nodeEnv === 'production') {
          this.addError(`${varName} appears to contain weak/default values`);
        } else {
          this.addWarning(`${varName} appears to contain weak/default values (OK for development)`);
        }
      }
    });
  }

  validateUrls() {
    this.logBright('\n🌐 Validating URL Configuration...', 'cyan');

    const urlVars = ['BASE_URL', 'API_URL', 'FRONTEND_URL'];
    const expectedPattern = urlPatterns[this.nodeEnv] || urlPatterns.development;

    urlVars.forEach(varName => {
      const value = process.env[varName];
      
      if (!value) {
        this.addWarning(`Missing URL variable: ${varName}`);
        return;
      }

      try {
        new URL(value); // Validate URL format
        
        if (this.nodeEnv === 'production' && !urlPatterns.production.test(value)) {
          this.addError(`${varName} should use HTTPS and a proper domain in production: ${value}`);
        } else if (this.nodeEnv === 'development' && !urlPatterns.development.test(value) && !urlPatterns.production.test(value)) {
          this.addWarning(`${varName} has unexpected format: ${value}`);
        } else {
          this.log(`✅ ${varName}: ${value}`, 'green');
        }
      } catch (error) {
        this.addError(`${varName} is not a valid URL: ${value}`);
      }
    });
  }

  validateDatabase() {
    this.logBright('\n🗄️ Validating Database Configuration...', 'cyan');

    const mongoUri = process.env.MONGODB_URI;
    if (!mongoUri) {
      this.addError('MONGODB_URI is required');
      return;
    }

    // Check for development database in production
    if (this.nodeEnv === 'production') {
      const devPatterns = ['localhost', '127.0.0.1', 'test', 'dev', 'development'];
      if (devPatterns.some(pattern => mongoUri.includes(pattern))) {
        this.addError('Production environment should not use development database');
      }
    }

    // Check database name
    if (mongoUri.includes('aixcelerate') && !mongoUri.includes('phonepointdar')) {
      this.addWarning('Database name still contains "aixcelerate" - consider updating to "phonepointdar"');
    }

    this.log(`✅ MONGODB_URI: Configured`, 'green');
  }

  validateEmail() {
    this.logBright('\n📧 Validating Email Configuration...', 'cyan');

    const emailVars = ['EMAIL_HOST', 'EMAIL_USER', 'EMAIL_FROM'];
    emailVars.forEach(varName => {
      const value = process.env[varName];
      if (value) {
        this.log(`✅ ${varName}: ${value}`, 'green');
      } else {
        this.addWarning(`Missing email variable: ${varName}`);
      }
    });

    // Check for development email in production
    if (this.nodeEnv === 'production') {
      const emailUser = process.env.EMAIL_USER;
      if (emailUser && (emailUser.includes('test') || emailUser.includes('dev'))) {
        this.addWarning('Production should use production email addresses');
      }
    }
  }

  validatePayments() {
    this.logBright('\n💳 Validating Payment Configuration...', 'cyan');

    const paymentVars = [
      'MPESA_CONSUMER_KEY',
      'MPESA_CONSUMER_SECRET',
      'TIGO_PESA_API_KEY',
      'AIRTEL_MONEY_API_KEY'
    ];

    let hasPaymentConfig = false;
    paymentVars.forEach(varName => {
      if (process.env[varName]) {
        hasPaymentConfig = true;
        this.log(`✅ ${varName}: Configured`, 'green');
      }
    });

    if (!hasPaymentConfig) {
      if (this.nodeEnv === 'production') {
        this.addWarning('No Tanzania payment methods configured (M-Pesa, Tigo Pesa, Airtel Money)');
      } else {
        this.addInfo('Payment methods not configured (OK for development)');
      }
    }
  }

  checkEnvironmentFile() {
    this.logBright('\n📁 Checking Environment File...', 'cyan');

    const envFile = path.join(process.cwd(), '.env');
    if (fs.existsSync(envFile)) {
      this.log(`✅ .env file found`, 'green');
      
      // Check if it's a copy of example file
      const content = fs.readFileSync(envFile, 'utf8');
      if (content.includes('your-') || content.includes('example') || content.includes('CHANGE_ME')) {
        this.addWarning('.env file appears to contain placeholder values');
      }
    } else {
      this.addWarning('.env file not found - using system environment variables');
    }
  }

  generateReport() {
    this.logBright('\n📊 Environment Validation Report', 'magenta');
    this.logBright('='.repeat(50), 'magenta');

    this.log(`Environment: ${this.nodeEnv}`, 'cyan');
    this.log(`Node.js Version: ${process.version}`, 'cyan');
    this.log(`Platform: ${process.platform}`, 'cyan');

    if (this.errors.length > 0) {
      this.logBright('\n❌ ERRORS:', 'red');
      this.errors.forEach(error => this.log(`  • ${error}`, 'red'));
    }

    if (this.warnings.length > 0) {
      this.logBright('\n⚠️  WARNINGS:', 'yellow');
      this.warnings.forEach(warning => this.log(`  • ${warning}`, 'yellow'));
    }

    if (this.info.length > 0) {
      this.logBright('\nℹ️  INFO:', 'blue');
      this.info.forEach(info => this.log(`  • ${info}`, 'blue'));
    }

    this.logBright('\n' + '='.repeat(50), 'magenta');

    if (this.errors.length === 0) {
      this.logBright('✅ Environment validation passed!', 'green');
      return true;
    } else {
      this.logBright('❌ Environment validation failed!', 'red');
      this.log(`Found ${this.errors.length} error(s) and ${this.warnings.length} warning(s)`, 'red');
      return false;
    }
  }

  validate() {
    this.logBright('🔧 Phone Point Dar - Environment Validation', 'magenta');
    this.logBright('='.repeat(50), 'magenta');

    this.checkEnvironmentFile();
    this.validateRequired();
    this.validateSecurity();
    this.validateUrls();
    this.validateDatabase();
    this.validateEmail();
    this.validatePayments();

    return this.generateReport();
  }
}

// Run validation if called directly
if (require.main === module) {
  const validator = new EnvironmentValidator();
  const isValid = validator.validate();
  process.exit(isValid ? 0 : 1);
}

module.exports = EnvironmentValidator;
