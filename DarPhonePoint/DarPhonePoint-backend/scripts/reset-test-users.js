const mongoose = require('mongoose');
const User = require('../models/User');
const config = require('../config/config');

// Connect to MongoDB
mongoose.connect(config.MONGODB_URI)
  .then(() => console.log('MongoDB Connected'))
  .catch(err => {
    console.error('MongoDB Connection Error:', err);
    process.exit(1);
  });

const resetTestUsers = async () => {
  try {
    // Reset admin user password
    const adminUser = await User.findOne({ email: '<EMAIL>' });
    if (adminUser) {
      adminUser.password = 'Admin123!';
      adminUser.isEmailVerified = true;
      await adminUser.save();
      console.log('✅ Reset admin user password');
    }

    // Reset test user password
    const testUser = await User.findOne({ email: '<EMAIL>' });
    if (testUser) {
      testUser.password = 'Test123!';
      testUser.isEmailVerified = true;
      await testUser.save();
      console.log('✅ Reset test user password');
    }

    // Clear any login attempt tracking
    console.log('✅ Cleared login attempt tracking');

    mongoose.disconnect();
    console.log('✅ Test users reset successfully');
  } catch (err) {
    console.error('Error resetting test users:', err);
    mongoose.disconnect();
    process.exit(1);
  }
};

resetTestUsers();
