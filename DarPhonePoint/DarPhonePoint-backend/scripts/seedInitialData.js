const mongoose = require('mongoose');
const config = require('../config/config');
const User = require('../models/User');
const Product = require('../models/Product');
const Lead = require('../models/Lead');
const Order = require('../models/Order');
const Analytics = require('../models/Analytics');
const EmailSequence = require('../models/EmailSequence');
const bcrypt = require('bcrypt');

// Connect to MongoDB
mongoose.connect(config.MONGODB_URI)
  .then(() => console.log('MongoDB Connected'))
  .catch(err => {
    console.error('MongoDB Connection Error:', err);
    process.exit(1);
  });

// Seed initial data
const seedData = async () => {
  try {
    console.log('Starting data seeding...');
    
    // Check if data already exists
    const userCount = await User.countDocuments();
    const productCount = await Product.countDocuments();
    
    if (userCount > 0 && productCount > 0) {
      console.log('Data already exists. Skipping seeding.');
      mongoose.disconnect();
      return;
    }
    
    // Create admin user if it doesn't exist
    let adminUser = await User.findOne({ email: '<EMAIL>' });
    
    if (!adminUser) {
      const salt = await bcrypt.genSalt(10);
      const hashedPassword = await bcrypt.hash('admin123', salt);
      
      adminUser = await User.create({
        name: 'Admin User',
        email: '<EMAIL>',
        password: hashedPassword,
        role: 'admin'
      });
      
      console.log('Admin user created:', adminUser.email);
    }
    
    // Create regular user if it doesn't exist
    let regularUser = await User.findOne({ email: '<EMAIL>' });
    
    if (!regularUser) {
      const salt = await bcrypt.genSalt(10);
      const hashedPassword = await bcrypt.hash('user123', salt);
      
      regularUser = await User.create({
        name: 'Regular User',
        email: '<EMAIL>',
        password: hashedPassword,
        role: 'user'
      });
      
      console.log('Regular user created:', regularUser.email);
    }
    
    // Create products
    const products = [
      {
        name: 'AI Productivity Master Guide',
        description: 'Comprehensive guide to using AI tools for productivity',
        price: 47,
        slug: 'ai-productivity-master-guide',
        image: '/images/products/ai-productivity-master-guide.jpg',
        file_path: '/products/ai-productivity-master-guide.pdf',
        is_digital: true,
        is_published: true
      },
      {
        name: 'AI Business Revolution Blueprint',
        description: 'Transform your business with AI technologies',
        price: 97,
        slug: 'ai-business-revolution-blueprint',
        image: '/images/products/ai-business-revolution-blueprint.jpg',
        file_path: '/products/ai-business-revolution-blueprint.pdf',
        is_digital: true,
        is_published: true
      },
      {
        name: '50 Essential AI Prompts',
        description: 'Free collection of AI prompts for various use cases',
        price: 0,
        slug: '50-essential-ai-prompts',
        image: '/images/products/50-essential-ai-prompts.jpg',
        file_path: '/products/50-essential-ai-prompts.pdf',
        is_digital: true,
        is_published: true,
        is_lead_magnet: true
      }
    ];
    
    const createdProducts = [];
    
    for (const product of products) {
      const existingProduct = await Product.findOne({ slug: product.slug });
      
      if (!existingProduct) {
        const newProduct = await Product.create(product);
        createdProducts.push(newProduct);
        console.log('Product created:', newProduct.name);
      } else {
        createdProducts.push(existingProduct);
      }
    }
    
    // Create leads
    const leads = [
      {
        email: '<EMAIL>',
        name: 'John Doe',
        source: 'landing_page'
      },
      {
        email: '<EMAIL>',
        name: 'Jane Smith',
        source: 'facebook_ad'
      },
      {
        email: '<EMAIL>',
        name: 'Bob Johnson',
        source: 'google_ad'
      },
      {
        email: '<EMAIL>',
        name: 'Alice Brown',
        source: 'referral'
      },
      {
        email: '<EMAIL>',
        name: 'Charlie Wilson',
        source: 'landing_page'
      }
    ];
    
    for (const lead of leads) {
      const existingLead = await Lead.findOne({ email: lead.email });
      
      if (!existingLead) {
        const newLead = await Lead.create({
          ...lead,
          created_at: new Date(Date.now() - Math.floor(Math.random() * 30) * 24 * 60 * 60 * 1000)
        });
        console.log('Lead created:', newLead.email);
      }
    }
    
    // Create orders
    const orders = [
      {
        user: regularUser._id,
        product: createdProducts[0],
        amount: createdProducts[0].price,
        payment_status: 'completed',
        created_at: new Date(Date.now() - 20 * 24 * 60 * 60 * 1000)
      },
      {
        user: regularUser._id,
        product: createdProducts[1],
        amount: createdProducts[1].price,
        payment_status: 'completed',
        created_at: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000)
      },
      {
        user: regularUser._id,
        product: createdProducts[2],
        amount: 0,
        payment_status: 'completed',
        created_at: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000)
      }
    ];
    
    for (const order of orders) {
      const existingOrder = await Order.findOne({
        user: order.user,
        'product._id': order.product._id
      });
      
      if (!existingOrder) {
        const newOrder = await Order.create(order);
        console.log('Order created for:', order.product.name);
      }
    }
    
    // Create analytics events
    const events = [
      {
        event_type: 'page_view',
        page_url: '/',
        device_type: 'desktop',
        browser: 'Chrome',
        referrer: 'google.com',
        created_at: new Date(Date.now() - 25 * 24 * 60 * 60 * 1000)
      },
      {
        event_type: 'page_view',
        page_url: '/products',
        device_type: 'mobile',
        browser: 'Safari',
        referrer: 'facebook.com',
        created_at: new Date(Date.now() - 20 * 24 * 60 * 60 * 1000)
      },
      {
        event_type: 'product_view',
        page_url: `/products/${createdProducts[0].slug}`,
        device_type: 'desktop',
        browser: 'Chrome',
        referrer: 'google.com',
        metadata: { product_id: createdProducts[0]._id.toString() },
        created_at: new Date(Date.now() - 22 * 24 * 60 * 60 * 1000)
      },
      {
        event_type: 'product_view',
        page_url: `/products/${createdProducts[1].slug}`,
        device_type: 'mobile',
        browser: 'Safari',
        referrer: 'facebook.com',
        metadata: { product_id: createdProducts[1]._id.toString() },
        created_at: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000)
      },
      {
        event_type: 'lead_capture',
        device_type: 'desktop',
        browser: 'Chrome',
        referrer: 'google.com',
        created_at: new Date(Date.now() - 18 * 24 * 60 * 60 * 1000)
      },
      {
        event_type: 'purchase',
        device_type: 'desktop',
        browser: 'Chrome',
        referrer: 'direct',
        created_at: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000)
      }
    ];
    
    for (const event of events) {
      await Analytics.create(event);
    }
    
    console.log('Analytics events created');
    
    // Create email sequences
    const emailSequences = [
      {
        name: 'Welcome Sequence',
        description: 'Sent to new leads after they sign up',
        trigger: 'lead_capture',
        isActive: true,
        emails: [
          {
            subject: 'Welcome to AIXcelerate!',
            body: '<p>Thank you for signing up! Here\'s what you can expect...</p>',
            delayDays: 0,
            order: 1,
            isActive: true
          },
          {
            subject: 'Getting Started with AI Tools',
            body: '<p>Here are some tips to get started with our AI tools...</p>',
            delayDays: 2,
            order: 2,
            isActive: true
          },
          {
            subject: 'Exclusive Offer Inside',
            body: '<p>As a valued subscriber, we\'re offering you a special discount...</p>',
            delayDays: 5,
            order: 3,
            isActive: true
          }
        ],
        created_by: adminUser._id
      },
      {
        name: 'Purchase Follow-up',
        description: 'Sent after a customer makes a purchase',
        trigger: 'purchase',
        isActive: true,
        emails: [
          {
            subject: 'Thank You for Your Purchase!',
            body: '<p>Thank you for purchasing from AIXcelerate! Here\'s how to access your products...</p>',
            delayDays: 0,
            order: 1,
            isActive: true
          },
          {
            subject: 'How to Get the Most from Your Purchase',
            body: '<p>Here are some tips to maximize the value of your purchase...</p>',
            delayDays: 3,
            order: 2,
            isActive: true
          }
        ],
        created_by: adminUser._id
      }
    ];
    
    for (const sequence of emailSequences) {
      const existingSequence = await EmailSequence.findOne({ name: sequence.name });
      
      if (!existingSequence) {
        const newSequence = await EmailSequence.create(sequence);
        console.log('Email sequence created:', newSequence.name);
      }
    }
    
    console.log('Data seeding completed successfully!');
    mongoose.disconnect();
  } catch (err) {
    console.error('Error seeding data:', err);
    mongoose.disconnect();
    process.exit(1);
  }
};

seedData();
