const mongoose = require('mongoose');
const User = require('../models/User');
const config = require('../config/config');
const bcrypt = require('bcryptjs');

// Connect to MongoDB
mongoose.connect(config.MONGODB_URI)
  .then(() => console.log('MongoDB Connected'))
  .catch(err => {
    console.error('MongoDB Connection Error:', err);
    process.exit(1);
  });

// Create admin user for Phone Point Dar
const createPhonePointAdmin = async () => {
  try {
    // Check if admin user already exists
    const existingAdmin = await User.findOne({ email: '<EMAIL>' });

    if (existingAdmin) {
      console.log('Phone Point Dar admin user already exists:', existingAdmin.email);
      mongoose.disconnect();
      return;
    }

    // Create new admin user (password will be hashed by the model's pre-save hook)
    const adminUser = new User({
      name: 'Phone Point Admin',
      email: '<EMAIL>',
      password: 'PhonePoint123!',
      role: 'admin',
      user_type: 'premium',
      status: 'active',
      isEmailVerified: true // Admin users created via scripts are automatically verified
    });

    await adminUser.save();

    console.log('Phone Point Dar admin user created successfully:');
    console.log('Email: <EMAIL>');
    console.log('Password: PhonePoint123!');
    console.log('Role: admin');
    console.log('User Type: premium');
    console.log('Status: active');

    // Create a staff user as well
    const staffUser = new User({
      name: 'Store Staff',
      email: '<EMAIL>',
      password: 'Staff123!',
      role: 'user',  // Using 'user' role which is valid
      user_type: 'premium',
      status: 'active',
      isEmailVerified: true
    });

    await staffUser.save();

    console.log('\nStore Staff user created successfully:');
    console.log('Email: <EMAIL>');
    console.log('Password: Staff123!');
    console.log('Role: user');

    mongoose.disconnect();
  } catch (err) {
    console.error('Error creating users:', err);
    mongoose.disconnect();
    process.exit(1);
  }
};

createPhonePointAdmin();
