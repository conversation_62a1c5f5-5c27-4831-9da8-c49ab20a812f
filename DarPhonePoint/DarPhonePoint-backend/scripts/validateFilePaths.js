const mongoose = require('mongoose');
const Product = require('../models/Product');
const fs = require('fs');
const path = require('path');

async function validateFilePaths() {
  try {
    await mongoose.connect('mongodb://localhost:27017/aixcelerate');
    console.log('Connected to MongoDB');
    
    const products = await Product.find({ is_active: true }).select('name slug file_path product_type price');
    
    console.log('\n🔍 FILE PATH VALIDATION ANALYSIS:');
    console.log('=====================================');
    
    const issues = [];
    const validProducts = [];
    
    for (const product of products) {
      console.log(`\n📋 ${product.name}:`);
      console.log(`   Slug: ${product.slug}`);
      console.log(`   Current Path: ${product.file_path || 'NOT SET'}`);
      
      // Expected path based on slug
      const expectedPath = `/uploads/products/${product.slug}.pdf`;
      console.log(`   Expected Path: ${expectedPath}`);
      
      // Check if file exists
      const actualFilePath = path.join(__dirname, '../public', product.file_path || '');
      const fileExists = product.file_path && fs.existsSync(actualFilePath);
      console.log(`   File Exists: ${fileExists ? '✅ YES' : '❌ NO'}`);
      
      // Check path consistency
      const pathConsistent = product.file_path === expectedPath;
      console.log(`   Path Consistent: ${pathConsistent ? '✅ YES' : '❌ NO'}`);
      
      // Check file size if exists
      if (fileExists) {
        const stats = fs.statSync(actualFilePath);
        console.log(`   File Size: ${stats.size} bytes`);
        console.log(`   Last Modified: ${stats.mtime.toDateString()}`);
      }
      
      if (!fileExists || !pathConsistent) {
        issues.push({
          product,
          fileExists,
          pathConsistent,
          expectedPath,
          currentPath: product.file_path
        });
      } else {
        validProducts.push(product);
      }
    }
    
    console.log('\n📊 VALIDATION SUMMARY:');
    console.log('======================');
    console.log(`   Total Products: ${products.length}`);
    console.log(`   Valid Products: ${validProducts.length}`);
    console.log(`   Products with Issues: ${issues.length}`);
    
    if (issues.length > 0) {
      console.log('\n⚠️  ISSUES FOUND:');
      issues.forEach((issue, index) => {
        console.log(`   ${index + 1}. ${issue.product.name}:`);
        if (!issue.fileExists) {
          console.log(`      - File does not exist: ${issue.currentPath}`);
        }
        if (!issue.pathConsistent) {
          console.log(`      - Path inconsistent: ${issue.currentPath} → ${issue.expectedPath}`);
        }
      });
    } else {
      console.log('\n✅ ALL PRODUCTS HAVE VALID FILE PATHS AND EXISTING FILES!');
    }
    
    // Check for naming convention consistency
    console.log('\n🔤 NAMING CONVENTION ANALYSIS:');
    console.log('==============================');
    products.forEach(product => {
      const hasUnderscores = product.slug.includes('_');
      const hasDashes = product.slug.includes('-');
      const convention = hasUnderscores ? 'underscores' : hasDashes ? 'dashes' : 'single_word';
      console.log(`   ${product.name}: ${product.slug} (${convention})`);
    });
    
    await mongoose.disconnect();
    return { products, validProducts, issues };
  } catch (error) {
    console.error('Error:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  validateFilePaths();
}

module.exports = validateFilePaths;
