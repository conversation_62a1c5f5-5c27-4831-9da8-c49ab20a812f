const mongoose = require('mongoose');
const Settings = require('../models/Settings');
require('dotenv').config();

/**
 * Initialize default settings for the application
 */
const initializeDefaultSettings = async () => {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/aixcelerate');
    console.log('Connected to MongoDB');

    // Check if settings already exist
    const existingSettings = await Settings.countDocuments();
    if (existingSettings > 0) {
      console.log(`Settings already exist (${existingSettings} settings found). Skipping initialization.`);
      process.exit(0);
    }

    // Default settings configuration based on environment variables
    const defaultSettings = {
      general: {
        siteName: { value: 'AIXcelerate', type: 'string', description: 'Site name', isPublic: true },
        siteDescription: { value: 'AI Productivity Tools for Business', type: 'string', description: 'Site description', isPublic: true },
        contactEmail: { value: process.env.EMAIL_FROM || '<EMAIL>', type: 'string', description: 'Contact email', isPublic: true },
        supportPhone: { value: '', type: 'string', description: 'Support phone number', isPublic: true },
        logoUrl: { value: '', type: 'string', description: 'Logo URL', isPublic: true },
        faviconUrl: { value: '', type: 'string', description: 'Favicon URL', isPublic: true },
        frontendUrl: { value: process.env.FRONTEND_URL || 'http://localhost:5173', type: 'string', description: 'Frontend URL', isPublic: true },
        baseUrl: { value: process.env.BASE_URL || 'http://localhost:5001', type: 'string', description: 'Backend base URL', isPublic: true }
      },
      email: {
        smtpHost: { value: process.env.EMAIL_HOST || 'smtp.gmail.com', type: 'string', description: 'SMTP host' },
        smtpPort: { value: parseInt(process.env.EMAIL_PORT) || 587, type: 'number', description: 'SMTP port' },
        smtpSecure: { value: process.env.EMAIL_SECURE === 'true', type: 'boolean', description: 'Use secure connection (TLS)' },
        smtpUsername: { value: process.env.EMAIL_USER || '', type: 'string', description: 'SMTP username' },
        smtpPassword: { value: process.env.EMAIL_PASSWORD || '', type: 'string', description: 'SMTP password', isEncrypted: true },
        fromEmail: { value: process.env.EMAIL_FROM || '<EMAIL>', type: 'string', description: 'From email address' },
        fromName: { value: 'AIXcelerate', type: 'string', description: 'From name' },
        enableEmailVerification: { value: true, type: 'boolean', description: 'Enable email verification' }
      },
      payment: {
        stripePublicKey: { value: process.env.STRIPE_PUBLISHABLE_KEY || '', type: 'string', description: 'Stripe public key', isPublic: true },
        stripeSecretKey: { value: process.env.STRIPE_SECRET_KEY || '', type: 'string', description: 'Stripe secret key', isEncrypted: true },
        stripeWebhookSecret: { value: process.env.STRIPE_WEBHOOK_SECRET || '', type: 'string', description: 'Stripe webhook secret', isEncrypted: true },
        currency: { value: 'USD', type: 'string', description: 'Default currency', isPublic: true },
        enableTestMode: { value: process.env.NODE_ENV !== 'production', type: 'boolean', description: 'Enable test mode' }
      },
      storage: {
        storageType: { value: 'local', type: 'string', description: 'Storage type (local, s3)' },
        uploadPath: { value: process.env.UPLOAD_PATH || './uploads', type: 'string', description: 'Local upload path' },
        maxFileSize: { value: parseInt(process.env.MAX_FILE_SIZE) || 10485760, type: 'number', description: 'Maximum file size in bytes' },
        allowedFileTypes: { value: process.env.ALLOWED_FILE_TYPES || 'pdf,doc,docx,txt,png,jpg,jpeg', type: 'string', description: 'Allowed file types (comma-separated)' },
        s3AccessKey: { value: '', type: 'string', description: 'S3 access key', isEncrypted: true },
        s3SecretKey: { value: '', type: 'string', description: 'S3 secret key', isEncrypted: true },
        s3Bucket: { value: '', type: 'string', description: 'S3 bucket name' },
        s3Region: { value: 'us-east-1', type: 'string', description: 'S3 region' }
      },
      security: {
        jwtSecret: { value: process.env.JWT_SECRET || '', type: 'string', description: 'JWT secret key', isEncrypted: true },
        jwtExpire: { value: process.env.JWT_EXPIRE || '24h', type: 'string', description: 'JWT expiration time' },
        bcryptRounds: { value: parseInt(process.env.BCRYPT_ROUNDS) || 12, type: 'number', description: 'Bcrypt hash rounds' },
        sessionSecret: { value: process.env.SESSION_SECRET || '', type: 'string', description: 'Session secret key', isEncrypted: true },
        corsOrigin: { value: process.env.CORS_ORIGIN || 'http://localhost:5173', type: 'string', description: 'CORS allowed origins' },
        trustProxy: { value: process.env.TRUST_PROXY === 'true', type: 'boolean', description: 'Trust proxy headers' },
        secureCookies: { value: process.env.SECURE_COOKIES === 'true', type: 'boolean', description: 'Use secure cookies' },
        rateLimitWindowMs: { value: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 900000, type: 'number', description: 'Rate limit window in milliseconds' },
        rateLimitMax: { value: parseInt(process.env.RATE_LIMIT_MAX) || 100, type: 'number', description: 'Maximum requests per window' }
      }
    };

    let totalSettings = 0;

    // Initialize settings for each category
    for (const [category, settings] of Object.entries(defaultSettings)) {
      console.log(`Initializing ${category} settings...`);

      for (const [key, config] of Object.entries(settings)) {
        await Settings.setSetting(category, key, config.value, {
          type: config.type || 'string',
          description: config.description || '',
          isPublic: config.isPublic || false,
          isEncrypted: config.isEncrypted || false
        });
        totalSettings++;
      }
    }

    console.log(`Successfully initialized ${totalSettings} default settings`);
    console.log('Settings initialization completed!');

  } catch (error) {
    console.error('Error initializing settings:', error);
    process.exit(1);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
    process.exit(0);
  }
};

// Run the initialization
initializeDefaultSettings();
