const mongoose = require('mongoose');
const Product = require('../models/Product');
require('dotenv').config();

// Connect to MongoDB
mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/darphonepoint', {
  useNewUrlParser: true,
  useUnifiedTopology: true,
});

// Sample specifications for different phone models
const phoneSpecifications = {
  'iPhone 15 Pro Max': [
    { name: 'Display Size', value: '6.7 inches', category: 'display' },
    { name: 'Display Type', value: 'Super Retina XDR OLED', category: 'display' },
    { name: 'Resolution', value: '2796 x 1290 pixels', category: 'display' },
    { name: 'Processor', value: 'A17 Pro Bionic', category: 'performance' },
    { name: 'RAM', value: '8GB', category: 'performance' },
    { name: 'Storage', value: '128GB/256GB/512GB/1TB', category: 'storage' },
    { name: 'Main Camera', value: '48MP Triple Camera System', category: 'camera' },
    { name: 'Front Camera', value: '12MP TrueDepth', category: 'camera' },
    { name: 'Battery', value: '4441 mAh', category: 'battery' },
    { name: 'Operating System', value: 'iOS 17', category: 'software' },
    { name: 'Network', value: '5G, 4G LTE', category: 'connectivity' },
    { name: 'Weight', value: '221g', category: 'design' },
    { name: 'Dimensions', value: '159.9 x 76.7 x 8.25 mm', category: 'design' }
  ],
  'Samsung Galaxy S24 Ultra': [
    { name: 'Display Size', value: '6.8 inches', category: 'display' },
    { name: 'Display Type', value: 'Dynamic AMOLED 2X', category: 'display' },
    { name: 'Resolution', value: '3120 x 1440 pixels', category: 'display' },
    { name: 'Processor', value: 'Snapdragon 8 Gen 3', category: 'performance' },
    { name: 'RAM', value: '12GB', category: 'performance' },
    { name: 'Storage', value: '256GB/512GB/1TB', category: 'storage' },
    { name: 'Main Camera', value: '200MP Quad Camera', category: 'camera' },
    { name: 'Front Camera', value: '12MP', category: 'camera' },
    { name: 'Battery', value: '5000 mAh', category: 'battery' },
    { name: 'Operating System', value: 'Android 14', category: 'software' },
    { name: 'Network', value: '5G, 4G LTE', category: 'connectivity' },
    { name: 'Weight', value: '232g', category: 'design' },
    { name: 'Dimensions', value: '162.3 x 79.0 x 8.6 mm', category: 'design' }
  ],
  'iPhone 15 Pro': [
    { name: 'Display Size', value: '6.1 inches', category: 'display' },
    { name: 'Display Type', value: 'Super Retina XDR OLED', category: 'display' },
    { name: 'Resolution', value: '2556 x 1179 pixels', category: 'display' },
    { name: 'Processor', value: 'A17 Pro Bionic', category: 'performance' },
    { name: 'RAM', value: '8GB', category: 'performance' },
    { name: 'Storage', value: '128GB/256GB/512GB/1TB', category: 'storage' },
    { name: 'Main Camera', value: '48MP Triple Camera System', category: 'camera' },
    { name: 'Front Camera', value: '12MP TrueDepth', category: 'camera' },
    { name: 'Battery', value: '3274 mAh', category: 'battery' },
    { name: 'Operating System', value: 'iOS 17', category: 'software' },
    { name: 'Network', value: '5G, 4G LTE', category: 'connectivity' },
    { name: 'Weight', value: '187g', category: 'design' },
    { name: 'Dimensions', value: '146.6 x 70.6 x 8.25 mm', category: 'design' }
  ],
  'Samsung Galaxy S24': [
    { name: 'Display Size', value: '6.2 inches', category: 'display' },
    { name: 'Display Type', value: 'Dynamic AMOLED 2X', category: 'display' },
    { name: 'Resolution', value: '2340 x 1080 pixels', category: 'display' },
    { name: 'Processor', value: 'Snapdragon 8 Gen 3', category: 'performance' },
    { name: 'RAM', value: '8GB', category: 'performance' },
    { name: 'Storage', value: '128GB/256GB/512GB', category: 'storage' },
    { name: 'Main Camera', value: '50MP Triple Camera', category: 'camera' },
    { name: 'Front Camera', value: '12MP', category: 'camera' },
    { name: 'Battery', value: '4000 mAh', category: 'battery' },
    { name: 'Operating System', value: 'Android 14', category: 'software' },
    { name: 'Network', value: '5G, 4G LTE', category: 'connectivity' },
    { name: 'Weight', value: '167g', category: 'design' },
    { name: 'Dimensions', value: '147.0 x 70.6 x 7.6 mm', category: 'design' }
  ],
  'iPhone 15': [
    { name: 'Display Size', value: '6.1 inches', category: 'display' },
    { name: 'Display Type', value: 'Super Retina XDR OLED', category: 'display' },
    { name: 'Resolution', value: '2556 x 1179 pixels', category: 'display' },
    { name: 'Processor', value: 'A16 Bionic', category: 'performance' },
    { name: 'RAM', value: '6GB', category: 'performance' },
    { name: 'Storage', value: '128GB/256GB/512GB', category: 'storage' },
    { name: 'Main Camera', value: '48MP Dual Camera', category: 'camera' },
    { name: 'Front Camera', value: '12MP TrueDepth', category: 'camera' },
    { name: 'Battery', value: '3349 mAh', category: 'battery' },
    { name: 'Operating System', value: 'iOS 17', category: 'software' },
    { name: 'Network', value: '5G, 4G LTE', category: 'connectivity' },
    { name: 'Weight', value: '171g', category: 'design' },
    { name: 'Dimensions', value: '147.6 x 71.6 x 7.80 mm', category: 'design' }
  ],
  'Samsung Galaxy A54': [
    { name: 'Display Size', value: '6.4 inches', category: 'display' },
    { name: 'Display Type', value: 'Super AMOLED', category: 'display' },
    { name: 'Resolution', value: '2340 x 1080 pixels', category: 'display' },
    { name: 'Processor', value: 'Exynos 1380', category: 'performance' },
    { name: 'RAM', value: '6GB/8GB', category: 'performance' },
    { name: 'Storage', value: '128GB/256GB', category: 'storage' },
    { name: 'Main Camera', value: '50MP Triple Camera', category: 'camera' },
    { name: 'Front Camera', value: '32MP', category: 'camera' },
    { name: 'Battery', value: '5000 mAh', category: 'battery' },
    { name: 'Operating System', value: 'Android 13', category: 'software' },
    { name: 'Network', value: '5G, 4G LTE', category: 'connectivity' },
    { name: 'Weight', value: '202g', category: 'design' },
    { name: 'Dimensions', value: '158.2 x 76.7 x 8.2 mm', category: 'design' }
  ]
};

async function addSpecificationsToProducts() {
  try {
    console.log('🔍 Finding products to add specifications...');
    
    for (const [productName, specifications] of Object.entries(phoneSpecifications)) {
      console.log(`\n📱 Looking for product: ${productName}`);
      
      // Find product by name (case insensitive, partial match)
      const product = await Product.findOne({
        name: { $regex: productName, $options: 'i' }
      });
      
      if (product) {
        console.log(`✅ Found product: ${product.name} (ID: ${product._id})`);
        
        // Update product with specifications
        product.specifications = specifications;
        await product.save();
        
        console.log(`✅ Added ${specifications.length} specifications to ${product.name}`);
      } else {
        console.log(`❌ Product not found: ${productName}`);
      }
    }
    
    console.log('\n🎉 Finished adding specifications to products!');
    
    // Show summary
    const productsWithSpecs = await Product.find({
      specifications: { $exists: true, $ne: [] }
    }).select('name specifications');
    
    console.log(`\n📊 Summary: ${productsWithSpecs.length} products now have specifications:`);
    productsWithSpecs.forEach(product => {
      console.log(`  - ${product.name}: ${product.specifications.length} specs`);
    });
    
  } catch (error) {
    console.error('❌ Error adding specifications:', error);
  } finally {
    mongoose.connection.close();
  }
}

// Run the script
addSpecificationsToProducts();
