const mongoose = require('mongoose');
const Product = require('../models/Product');
const fs = require('fs');
const path = require('path');

async function standardizeFilePaths() {
  try {
    await mongoose.connect('mongodb://localhost:27017/aixcelerate-dev');
    console.log('Connected to MongoDB');
    
    const products = await Product.find({ is_active: true }).select('name slug file_path product_type');
    
    console.log('\n🔧 STANDARDIZING FILE PATHS:');
    console.log('============================');
    
    const operations = [];
    
    for (const product of products) {
      const currentPath = product.file_path;
      const expectedPath = `/uploads/products/${product.slug}.pdf`;
      
      console.log(`\n📋 ${product.name}:`);
      console.log(`   Current Path: ${currentPath}`);
      console.log(`   Expected Path: ${expectedPath}`);
      
      if (currentPath !== expectedPath) {
        const currentFilePath = path.join(__dirname, '../public', currentPath);
        const expectedFilePath = path.join(__dirname, '../public', expectedPath);
        
        // Check if current file exists
        if (fs.existsSync(currentFilePath)) {
          console.log(`   Action: Rename file and update database`);
          operations.push({
            type: 'rename_and_update',
            product,
            currentPath,
            expectedPath,
            currentFilePath,
            expectedFilePath
          });
        } else {
          console.log(`   Action: Update database path only (file doesn't exist)`);
          operations.push({
            type: 'update_db_only',
            product,
            currentPath,
            expectedPath
          });
        }
      } else {
        console.log(`   Action: No change needed ✅`);
      }
    }
    
    console.log(`\n📊 OPERATIONS SUMMARY:`);
    console.log(`   Total products: ${products.length}`);
    console.log(`   Operations needed: ${operations.length}`);
    
    if (operations.length === 0) {
      console.log('\n✅ All file paths are already standardized!');
      await mongoose.disconnect();
      return;
    }
    
    console.log('\n🔄 EXECUTING OPERATIONS:');
    console.log('========================');
    
    for (const op of operations) {
      try {
        console.log(`\n🔧 Processing: ${op.product.name}`);
        
        if (op.type === 'rename_and_update') {
          // Rename the file
          fs.renameSync(op.currentFilePath, op.expectedFilePath);
          console.log(`   ✅ File renamed: ${path.basename(op.currentFilePath)} → ${path.basename(op.expectedFilePath)}`);
          
          // Update database
          await Product.findByIdAndUpdate(op.product._id, { file_path: op.expectedPath });
          console.log(`   ✅ Database updated: ${op.currentPath} → ${op.expectedPath}`);
          
        } else if (op.type === 'update_db_only') {
          // Update database only
          await Product.findByIdAndUpdate(op.product._id, { file_path: op.expectedPath });
          console.log(`   ✅ Database updated: ${op.currentPath} → ${op.expectedPath}`);
        }
        
      } catch (error) {
        console.error(`   ❌ Error processing ${op.product.name}:`, error.message);
      }
    }
    
    console.log('\n🎉 STANDARDIZATION COMPLETED!');
    
    // Verify the results
    console.log('\n🔍 VERIFICATION:');
    console.log('================');
    
    const updatedProducts = await Product.find({ is_active: true }).select('name slug file_path');
    
    for (const product of updatedProducts) {
      const filePath = path.join(__dirname, '../public', product.file_path);
      const fileExists = fs.existsSync(filePath);
      const pathMatches = product.file_path === `/uploads/products/${product.slug}.pdf`;
      
      console.log(`   ${product.name}:`);
      console.log(`     Path: ${product.file_path}`);
      console.log(`     File Exists: ${fileExists ? '✅' : '❌'}`);
      console.log(`     Path Standard: ${pathMatches ? '✅' : '❌'}`);
    }
    
    await mongoose.disconnect();
    console.log('\n✅ Standardization process completed successfully!');
    
  } catch (error) {
    console.error('Error:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  standardizeFilePaths();
}

module.exports = standardizeFilePaths;
