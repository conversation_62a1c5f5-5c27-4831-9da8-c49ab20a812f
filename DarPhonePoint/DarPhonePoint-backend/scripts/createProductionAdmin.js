const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const config = require('../config/config');
const User = require('../models/User');
const logger = require('../utils/logger');

/**
 * Create production admin user
 */
async function createProductionAdmin() {
  try {
    // Connect to database
    await mongoose.connect(process.env.MONGODB_URI || config.MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true
    });

    console.log('Connected to MongoDB');

    // Admin user details
    const adminEmail = process.env.ADMIN_EMAIL || '<EMAIL>';
    const adminPassword = process.env.ADMIN_PASSWORD || 'PhonePointDar2024!';
    const adminName = process.env.ADMIN_NAME || 'Phone Point Dar Admin';
    const adminPhone = process.env.ADMIN_PHONE || '+255-XXX-XXX-XXX';

    // Check if admin user already exists
    const existingAdmin = await User.findOne({ email: adminEmail });
    
    if (existingAdmin) {
      console.log('Admin user already exists. Updating password...');
      
      // Update existing admin user
      const salt = await bcrypt.genSalt(12);
      const hashedPassword = await bcrypt.hash(adminPassword, salt);
      
      existingAdmin.name = adminName;
      existingAdmin.password = hashedPassword;
      existingAdmin.phone = adminPhone;
      existingAdmin.role = 'admin';
      existingAdmin.user_type = 'premium';
      existingAdmin.status = 'active';
      existingAdmin.isEmailVerified = true;
      existingAdmin.updated_at = new Date();
      
      await existingAdmin.save();
      
      console.log('✅ Admin user updated successfully!');
      console.log(`Name: ${existingAdmin.name}`);
      console.log(`Email: ${adminEmail}`);
      console.log(`Phone: ${existingAdmin.phone}`);
      console.log(`Role: ${existingAdmin.role}`);
      console.log(`User Type: ${existingAdmin.user_type}`);
    } else {
      console.log('Creating new admin user...');
      
      // Create new admin user
      const salt = await bcrypt.genSalt(12);
      const hashedPassword = await bcrypt.hash(adminPassword, salt);
      
      const adminUser = new User({
        name: adminName,
        email: adminEmail,
        password: hashedPassword,
        phone: adminPhone,
        role: 'admin',
        user_type: 'premium',
        status: 'active',
        isEmailVerified: true,
        preferences: {
          language: 'en',
          currency: 'TZS',
          notifications: {
            email: true,
            sms: Boolean(adminPhone && adminPhone !== '+255-XXX-XXX-XXX'),
            push: true
          }
        },
        profile: {
          address: {
            country: 'Tanzania',
            city: 'Dar es Salaam'
          }
        },
        purchased_products: [],
        email_sequences: [],
        last_login: new Date(),
        created_at: new Date(),
        updated_at: new Date()
      });

      await adminUser.save();
      
      console.log('✅ Admin user created successfully!');
      console.log(`Name: ${adminUser.name}`);
      console.log(`Email: ${adminEmail}`);
      console.log(`Phone: ${adminUser.phone}`);
      console.log(`Role: ${adminUser.role}`);
      console.log(`User Type: ${adminUser.user_type}`);
      console.log(`User ID: ${adminUser._id}`);
    }

    // Verify admin user can authenticate
    const testAdmin = await User.findOne({ email: adminEmail }).select('+password');
    if (testAdmin) {
      const isValidPassword = await bcrypt.compare(adminPassword, testAdmin.password);
      if (isValidPassword) {
        console.log('✅ Admin authentication test passed');
      } else {
        console.log('❌ Admin authentication test failed');
      }
    }

    // Create test products if they don't exist
    await createTestProducts();

    // Log admin creation for audit
    logger.info('Production admin user created/updated', {
      email: adminEmail,
      role: 'admin',
      timestamp: new Date().toISOString()
    });

    console.log('\n🎉 Production admin setup completed successfully!');
    console.log('\n📋 Admin Login Details:');
    console.log(`   Email: ${adminEmail}`);
    console.log(`   Password: ${adminPassword}`);
    console.log('\n🔒 Security Notes:');
    console.log('   - Change the admin password after first login');
    console.log('   - Enable 2FA if available');
    console.log('   - Regularly review admin access logs');

  } catch (error) {
    console.error('❌ Error creating admin user:', error);
    logger.error('Admin user creation error:', error);
    process.exit(1);
  } finally {
    await mongoose.connection.close();
    console.log('Database connection closed');
    process.exit(0);
  }
}

/**
 * Create test products for production
 */
async function createTestProducts() {
  try {
    const Product = require('../models/Product');
    
    const products = [
      {
        name: 'AI Prompts Lead Magnet',
        description: 'Free collection of powerful AI prompts to boost your productivity',
        price: 0,
        category: 'Free Resources',
        status: 'active',
        featured: true,
        file_path: '/products/ai_prompts_lead_magnet.pdf',
        download_url: '/api/downloads/ai_prompts_lead_magnet.pdf',
        whop_product_id: 'free_ai_prompts',
        whop_price_id: 'price_free',
        checkout_url: '/products/ai-prompts-lead-magnet',
        metadata: {
          type: 'lead_magnet',
          delivery_method: 'email'
        }
      },
      {
        name: 'AI Productivity Master Guide',
        description: 'Complete guide to mastering AI tools for maximum productivity',
        price: 2799, // $27.99
        category: 'Guides',
        status: 'active',
        featured: true,
        file_path: '/products/ai_productivity_master_guide.pdf',
        download_url: '/api/downloads/ai_productivity_master_guide.pdf',
        whop_product_id: 'ai_productivity_guide',
        whop_price_id: 'price_2799',
        checkout_url: 'https://whop.com/checkout/ai_productivity_guide',
        metadata: {
          type: 'paid_product',
          delivery_method: 'download'
        }
      },
      {
        name: 'AI Business Revolution',
        description: 'Transform your business with cutting-edge AI strategies and tools',
        price: 4799, // $47.99
        category: 'Business',
        status: 'active',
        featured: true,
        file_path: '/products/ai_business_revolution.pdf',
        download_url: '/api/downloads/ai_business_revolution.pdf',
        whop_product_id: 'ai_business_revolution',
        whop_price_id: 'price_4799',
        checkout_url: 'https://whop.com/checkout/ai_business_revolution',
        metadata: {
          type: 'paid_product',
          delivery_method: 'download'
        }
      }
    ];

    for (const productData of products) {
      const existingProduct = await Product.findOne({ name: productData.name });
      
      if (!existingProduct) {
        const product = new Product({
          ...productData,
          created_at: new Date(),
          updated_at: new Date()
        });
        
        await product.save();
        console.log(`✅ Created product: ${productData.name}`);
      } else {
        console.log(`ℹ️ Product already exists: ${productData.name}`);
      }
    }

    console.log('✅ Test products setup completed');
  } catch (error) {
    console.error('❌ Error creating test products:', error);
  }
}

/**
 * Clean database for fresh start
 */
async function cleanDatabase() {
  try {
    console.log('🧹 Cleaning database for fresh production start...');
    
    // Remove test users (keep admin)
    const testUsers = await User.find({ 
      email: { $ne: '<EMAIL>' },
      role: { $ne: 'admin' }
    });
    
    if (testUsers.length > 0) {
      await User.deleteMany({ 
        email: { $ne: '<EMAIL>' },
        role: { $ne: 'admin' }
      });
      console.log(`✅ Removed ${testUsers.length} test users`);
    }

    // Clean test orders
    const Order = require('../models/Order');
    const testOrders = await Order.find({});
    if (testOrders.length > 0) {
      await Order.deleteMany({});
      console.log(`✅ Removed ${testOrders.length} test orders`);
    }

    // Clean test leads
    const Lead = require('../models/Lead');
    const testLeads = await Lead.find({});
    if (testLeads.length > 0) {
      await Lead.deleteMany({});
      console.log(`✅ Removed ${testLeads.length} test leads`);
    }

    // Clean analytics data
    const Analytics = require('../models/Analytics');
    const testAnalytics = await Analytics.find({});
    if (testAnalytics.length > 0) {
      await Analytics.deleteMany({});
      console.log(`✅ Removed ${testAnalytics.length} test analytics records`);
    }

    console.log('✅ Database cleaned successfully');
  } catch (error) {
    console.error('❌ Error cleaning database:', error);
  }
}

// Check command line arguments
const args = process.argv.slice(2);
const shouldClean = args.includes('--clean');

// Main execution
async function main() {
  console.log('🚀 AIXcelerate Production Admin Setup');
  console.log('=====================================\n');
  
  if (shouldClean) {
    await cleanDatabase();
  }
  
  await createProductionAdmin();
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = {
  createProductionAdmin,
  createTestProducts,
  cleanDatabase
};
