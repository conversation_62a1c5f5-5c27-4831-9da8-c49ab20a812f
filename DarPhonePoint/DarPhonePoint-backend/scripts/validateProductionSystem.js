const mongoose = require('mongoose');
const axios = require('axios');
const config = require('../config/config');
const logger = require('../utils/logger');

// Import services for validation
const advancedCacheService = require('../services/advancedCacheService');
const enhancedMonitoringService = require('../services/enhancedMonitoringService');
const databaseOptimizationService = require('../services/databaseOptimizationService');
const cdnService = require('../services/cdnService');
const emailService = require('../services/emailService');

/**
 * Comprehensive production system validation
 */
class ProductionSystemValidator {
  constructor() {
    this.results = {
      overall: 'pending',
      tests: [],
      score: 0,
      maxScore: 0,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Add test result
   * @param {string} category - Test category
   * @param {string} name - Test name
   * @param {boolean} passed - Test result
   * @param {string} message - Test message
   * @param {number} weight - Test weight (default: 1)
   */
  addTest(category, name, passed, message, weight = 1) {
    this.results.tests.push({
      category,
      name,
      passed,
      message,
      weight,
      timestamp: new Date().toISOString()
    });

    this.results.maxScore += weight;
    if (passed) {
      this.results.score += weight;
    }
  }

  /**
   * Run all validation tests
   */
  async runValidation() {
    console.log('🔍 Starting comprehensive production system validation...\n');

    try {
      // Database validation
      await this.validateDatabase();
      
      // Cache validation
      await this.validateCache();
      
      // Monitoring validation
      await this.validateMonitoring();
      
      // CDN validation
      await this.validateCDN();
      
      // Email validation
      await this.validateEmail();
      
      // Security validation
      await this.validateSecurity();
      
      // Performance validation
      await this.validatePerformance();
      
      // Integration validation
      await this.validateIntegrations();
      
      // Environment validation
      await this.validateEnvironment();

      // Calculate final score
      this.calculateFinalScore();
      
      // Generate report
      this.generateReport();

    } catch (error) {
      console.error('❌ Validation failed:', error);
      this.results.overall = 'failed';
      this.results.error = error.message;
    }

    return this.results;
  }

  /**
   * Validate database connectivity and optimization
   */
  async validateDatabase() {
    console.log('📊 Validating database...');

    try {
      // Test database connection
      const isConnected = mongoose.connection.readyState === 1;
      this.addTest('Database', 'Connection', isConnected, 
        isConnected ? 'Database connected successfully' : 'Database connection failed', 3);

      if (isConnected) {
        // Test database operations
        const User = require('../models/User');
        const userCount = await User.countDocuments();
        this.addTest('Database', 'Operations', true, 
          `Database operations working (${userCount} users)`, 2);

        // Test database optimization
        const dbHealth = await databaseOptimizationService.healthCheck();
        this.addTest('Database', 'Optimization', dbHealth.status === 'healthy',
          `Database optimization: ${dbHealth.status}`, 2);

        // Test indexes
        const stats = await databaseOptimizationService.getDatabaseStats();
        const hasIndexes = stats.database && stats.database.indexes > 0;
        this.addTest('Database', 'Indexes', hasIndexes,
          `Database indexes: ${stats.database?.indexes || 0}`, 1);
      }
    } catch (error) {
      this.addTest('Database', 'Connection', false, `Database error: ${error.message}`, 3);
    }
  }

  /**
   * Validate cache system
   */
  async validateCache() {
    console.log('🗄️ Validating cache system...');

    try {
      // Test cache connectivity
      const cacheHealth = await advancedCacheService.healthCheck();
      this.addTest('Cache', 'Connectivity', cacheHealth.status === 'healthy',
        `Cache status: ${cacheHealth.status}`, 2);

      // Test cache operations
      const testKey = 'validation_test';
      const testValue = { test: true, timestamp: Date.now() };
      
      const setResult = await advancedCacheService.set(testKey, testValue, 60);
      this.addTest('Cache', 'Set Operation', setResult,
        setResult ? 'Cache set operation successful' : 'Cache set operation failed', 1);

      const getValue = await advancedCacheService.get(testKey);
      const getResult = getValue && getValue.test === true;
      this.addTest('Cache', 'Get Operation', getResult,
        getResult ? 'Cache get operation successful' : 'Cache get operation failed', 1);

      // Clean up test data
      await advancedCacheService.del(testKey);

      // Test cache statistics
      const stats = await advancedCacheService.getStats();
      this.addTest('Cache', 'Statistics', stats.connected,
        `Cache statistics available: ${stats.connected}`, 1);

    } catch (error) {
      this.addTest('Cache', 'System', false, `Cache error: ${error.message}`, 2);
    }
  }

  /**
   * Validate monitoring system
   */
  async validateMonitoring() {
    console.log('📈 Validating monitoring system...');

    try {
      // Test monitoring health
      const monitoringHealth = await enhancedMonitoringService.healthCheck();
      this.addTest('Monitoring', 'Health', monitoringHealth.status === 'healthy',
        `Monitoring status: ${monitoringHealth.status}`, 2);

      // Test metrics collection
      const dashboardData = await enhancedMonitoringService.getDashboardData();
      const hasMetrics = dashboardData && !dashboardData.error;
      this.addTest('Monitoring', 'Metrics', hasMetrics,
        hasMetrics ? 'Metrics collection active' : 'Metrics collection failed', 2);

      // Test error recording
      enhancedMonitoringService.recordError({
        error: new Error('Test error'),
        context: 'validation',
        severity: 'info'
      });
      this.addTest('Monitoring', 'Error Recording', true, 'Error recording functional', 1);

    } catch (error) {
      this.addTest('Monitoring', 'System', false, `Monitoring error: ${error.message}`, 2);
    }
  }

  /**
   * Validate CDN service
   */
  async validateCDN() {
    console.log('🌐 Validating CDN service...');

    try {
      // Test CDN health
      const cdnHealth = await cdnService.healthCheck();
      this.addTest('CDN', 'Health', cdnHealth.status !== 'unhealthy',
        `CDN status: ${cdnHealth.status}`, 1);

      // Test asset URL generation
      const testUrl = cdnService.getAssetUrl('test/asset.js');
      const urlValid = testUrl && testUrl.includes('test/asset');
      this.addTest('CDN', 'URL Generation', urlValid,
        urlValid ? 'Asset URL generation working' : 'Asset URL generation failed', 1);

      // Test cache headers
      const headers = cdnService.getCacheHeaders('test.css');
      const hasHeaders = headers && headers['Cache-Control'];
      this.addTest('CDN', 'Cache Headers', hasHeaders,
        hasHeaders ? 'Cache headers generated' : 'Cache headers missing', 1);

    } catch (error) {
      this.addTest('CDN', 'System', false, `CDN error: ${error.message}`, 1);
    }
  }

  /**
   * Validate email system
   */
  async validateEmail() {
    console.log('📧 Validating email system...');

    try {
      // Test email service health
      const emailStats = await emailService.getStats();
      const emailHealthy = emailStats && !emailStats.error;
      this.addTest('Email', 'Service', emailHealthy,
        emailHealthy ? 'Email service operational' : 'Email service error', 2);

      // Test email configuration
      const hasConfig = process.env.EMAIL_USER && process.env.EMAIL_PASSWORD;
      this.addTest('Email', 'Configuration', hasConfig,
        hasConfig ? 'Email configuration present' : 'Email configuration missing', 2);

    } catch (error) {
      this.addTest('Email', 'System', false, `Email error: ${error.message}`, 2);
    }
  }

  /**
   * Validate security configuration
   */
  async validateSecurity() {
    console.log('🔒 Validating security...');

    try {
      // Test JWT secret
      const hasJwtSecret = process.env.JWT_SECRET && process.env.JWT_SECRET.length > 32;
      this.addTest('Security', 'JWT Secret', hasJwtSecret,
        hasJwtSecret ? 'JWT secret configured' : 'JWT secret weak or missing', 3);

      // Test environment
      const isProduction = process.env.NODE_ENV === 'production';
      this.addTest('Security', 'Environment', isProduction,
        `Environment: ${process.env.NODE_ENV}`, 2);

      // Test CORS configuration
      const hasCors = process.env.CORS_ORIGIN;
      this.addTest('Security', 'CORS', hasCors,
        hasCors ? 'CORS configured' : 'CORS not configured', 2);

      // Test HTTPS configuration
      const hasHttps = process.env.BASE_URL && process.env.BASE_URL.startsWith('https');
      this.addTest('Security', 'HTTPS', hasHttps,
        hasHttps ? 'HTTPS configured' : 'HTTPS not configured', 2);

    } catch (error) {
      this.addTest('Security', 'System', false, `Security error: ${error.message}`, 3);
    }
  }

  /**
   * Validate performance optimizations
   */
  async validatePerformance() {
    console.log('⚡ Validating performance...');

    try {
      // Test cache enabled
      const cacheEnabled = process.env.CACHE_ENABLED === 'true';
      this.addTest('Performance', 'Caching', cacheEnabled,
        cacheEnabled ? 'Caching enabled' : 'Caching disabled', 2);

      // Test CDN enabled
      const cdnEnabled = process.env.CDN_ENABLED === 'true';
      this.addTest('Performance', 'CDN', cdnEnabled,
        cdnEnabled ? 'CDN enabled' : 'CDN disabled', 1);

      // Test database optimization
      const dbOptEnabled = process.env.DB_OPTIMIZATION_ENABLED === 'true';
      this.addTest('Performance', 'DB Optimization', dbOptEnabled,
        dbOptEnabled ? 'DB optimization enabled' : 'DB optimization disabled', 2);

      // Test monitoring
      const monitoringEnabled = process.env.MONITORING_ENABLED === 'true';
      this.addTest('Performance', 'Monitoring', monitoringEnabled,
        monitoringEnabled ? 'Monitoring enabled' : 'Monitoring disabled', 1);

    } catch (error) {
      this.addTest('Performance', 'System', false, `Performance error: ${error.message}`, 2);
    }
  }

  /**
   * Validate integrations
   */
  async validateIntegrations() {
    console.log('🔗 Validating integrations...');

    try {
      // Test Whop configuration
      const whopConfigured = process.env.WHOP_API_KEY && process.env.WHOP_WEBHOOK_SECRET;
      this.addTest('Integrations', 'Whop', whopConfigured,
        whopConfigured ? 'Whop integration configured' : 'Whop integration missing', 3);

      // Test email integration
      const emailConfigured = process.env.EMAIL_HOST && process.env.EMAIL_USER;
      this.addTest('Integrations', 'Email', emailConfigured,
        emailConfigured ? 'Email integration configured' : 'Email integration missing', 2);

      // Test database integration
      const dbConfigured = process.env.MONGODB_URI;
      this.addTest('Integrations', 'Database', dbConfigured,
        dbConfigured ? 'Database integration configured' : 'Database integration missing', 3);

      // Test Redis integration
      const redisConfigured = process.env.REDIS_URL;
      this.addTest('Integrations', 'Redis', redisConfigured,
        redisConfigured ? 'Redis integration configured' : 'Redis integration missing', 2);

    } catch (error) {
      this.addTest('Integrations', 'System', false, `Integration error: ${error.message}`, 3);
    }
  }

  /**
   * Validate environment configuration
   */
  async validateEnvironment() {
    console.log('🌍 Validating environment...');

    try {
      // Test required environment variables
      const requiredVars = [
        'NODE_ENV', 'PORT', 'MONGODB_URI', 'JWT_SECRET',
        'EMAIL_HOST', 'EMAIL_USER', 'EMAIL_PASSWORD',
        'WHOP_API_KEY', 'WHOP_WEBHOOK_SECRET',
        'FRONTEND_URL', 'BASE_URL'
      ];

      let missingVars = [];
      requiredVars.forEach(varName => {
        if (!process.env[varName]) {
          missingVars.push(varName);
        }
      });

      const allVarsPresent = missingVars.length === 0;
      this.addTest('Environment', 'Variables', allVarsPresent,
        allVarsPresent ? 'All required variables present' : `Missing: ${missingVars.join(', ')}`, 3);

      // Test production URLs
      const hasProductionUrls = process.env.BASE_URL && process.env.BASE_URL.includes('aixcelerate.og');
      this.addTest('Environment', 'Production URLs', hasProductionUrls,
        hasProductionUrls ? 'Production URLs configured' : 'Development URLs detected', 2);

    } catch (error) {
      this.addTest('Environment', 'System', false, `Environment error: ${error.message}`, 3);
    }
  }

  /**
   * Calculate final score and overall status
   */
  calculateFinalScore() {
    const percentage = this.results.maxScore > 0 ? 
      Math.round((this.results.score / this.results.maxScore) * 100) : 0;

    this.results.percentage = percentage;

    if (percentage >= 95) {
      this.results.overall = 'excellent';
    } else if (percentage >= 85) {
      this.results.overall = 'good';
    } else if (percentage >= 70) {
      this.results.overall = 'fair';
    } else {
      this.results.overall = 'poor';
    }
  }

  /**
   * Generate validation report
   */
  generateReport() {
    console.log('\n' + '='.repeat(80));
    console.log('🎯 PRODUCTION SYSTEM VALIDATION REPORT');
    console.log('='.repeat(80));

    console.log(`\n📊 Overall Score: ${this.results.score}/${this.results.maxScore} (${this.results.percentage}%)`);
    console.log(`🎖️ Overall Status: ${this.results.overall.toUpperCase()}`);

    // Group tests by category
    const categories = {};
    this.results.tests.forEach(test => {
      if (!categories[test.category]) {
        categories[test.category] = [];
      }
      categories[test.category].push(test);
    });

    // Display results by category
    Object.entries(categories).forEach(([category, tests]) => {
      console.log(`\n📋 ${category}:`);
      tests.forEach(test => {
        const status = test.passed ? '✅' : '❌';
        console.log(`   ${status} ${test.name}: ${test.message}`);
      });
    });

    // Recommendations
    console.log('\n💡 Recommendations:');
    const failedTests = this.results.tests.filter(test => !test.passed);
    if (failedTests.length === 0) {
      console.log('   🎉 All tests passed! System is production ready.');
    } else {
      failedTests.forEach(test => {
        console.log(`   🔧 Fix ${test.category} - ${test.name}: ${test.message}`);
      });
    }

    console.log('\n' + '='.repeat(80));
    console.log(`✅ Validation completed at ${this.results.timestamp}`);
    console.log('='.repeat(80));
  }
}

/**
 * Main validation function
 */
async function validateProductionSystem() {
  const validator = new ProductionSystemValidator();
  
  try {
    // Connect to database if not connected
    if (mongoose.connection.readyState !== 1) {
      // Use local database for validation if production DB not available
      const dbUri = process.env.NODE_ENV === 'production' ?
        (process.env.MONGODB_URI || config.MONGODB_URI) :
        'mongodb://localhost:27017/aixcelerate-dev';

      await mongoose.connect(dbUri);
    }

    const results = await validator.runValidation();
    
    // Save results to file
    const fs = require('fs');
    const reportPath = `validation-report-${Date.now()}.json`;
    fs.writeFileSync(reportPath, JSON.stringify(results, null, 2));
    console.log(`\n📄 Detailed report saved to: ${reportPath}`);

    return results;
  } catch (error) {
    console.error('❌ Validation failed:', error);
    return { overall: 'failed', error: error.message };
  } finally {
    if (mongoose.connection.readyState === 1) {
      await mongoose.connection.close();
    }
  }
}

// Run validation if called directly
if (require.main === module) {
  validateProductionSystem()
    .then(results => {
      process.exit(results.overall === 'failed' ? 1 : 0);
    })
    .catch(error => {
      console.error('Validation error:', error);
      process.exit(1);
    });
}

module.exports = {
  ProductionSystemValidator,
  validateProductionSystem
};
