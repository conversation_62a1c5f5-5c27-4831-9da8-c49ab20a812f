/**
 * Create <NAME_EMAIL>
 */

const mongoose = require('mongoose');
require('dotenv').config();

// Import models
const User = require('../models/User');

async function createShirhanUser() {
  try {
    console.log('🧪 Creating <NAME_EMAIL>...\n');

    // Connect to database
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/phonepoint-dar');
    console.log('✅ Connected to MongoDB');

    // Check if user already exists
    let user = await User.findOne({ email: '<EMAIL>' });
    if (user) {
      console.log('🗑️ Deleting existing user to recreate with proper password...');
      await User.deleteOne({ email: '<EMAIL>' });
    }

    // Create user
    console.log('👤 Creating new user...');
    user = new User({
      name: '<PERSON><PERSON><PERSON>',
      email: 'shir<PERSON><EMAIL>',
      password: '<PERSON><PERSON>hanPass123!', // Meets all validation requirements
      isEmailVerified: true,
      role: 'user'
    });
    await user.save();
    
    console.log('✅ User created successfully!');
    console.log('📧 Email: <EMAIL>');
    console.log('🔑 Password: ShirhanPass123!');
    console.log('🆔 User ID:', user._id);
    
    return user;

  } catch (error) {
    console.error('❌ Error creating user:', error);
    throw error;
  } finally {
    await mongoose.disconnect();
    console.log('📤 Disconnected from MongoDB');
  }
}

// Test login functionality
async function testLogin() {
  console.log('\n🧪 Testing login functionality...');
  
  const axios = require('axios');
  const baseURL = 'http://localhost:5001/api';
  
  try {
    const response = await axios.post(`${baseURL}/auth/login`, {
      email: '<EMAIL>',
      password: 'ShirhanPass123!'
    });
    
    if (response.data.success) {
      console.log('✅ Login successful!');
      console.log('🎫 Token:', response.data.token.substring(0, 20) + '...');
      console.log('👤 User:', response.data.user);
      
      return response.data.token;
    } else {
      console.log('❌ Login failed:', response.data.message);
    }
  } catch (error) {
    console.log('❌ Login test failed:', error.response?.data || error.message);
  }
}

// Test wishlist operations
async function testWishlistOperations(token) {
  if (!token) {
    console.log('⚠️ No token available, skipping wishlist operations test');
    return;
  }
  
  console.log('\n🧪 Testing wishlist operations...');
  
  const axios = require('axios');
  const baseURL = 'http://localhost:5001/api';
  
  const headers = {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  };
  
  try {
    // Test product ID (AirPods Pro)
    const productId = '68775e53849a0097534ea99a';
    
    // Add to wishlist
    console.log('➕ Adding product to wishlist...');
    const addResponse = await axios.post(`${baseURL}/wishlist`, 
      { productId }, 
      { headers }
    );
    console.log('✅ Add to wishlist:', addResponse.data.success ? 'Success' : 'Failed');
    
    // Get wishlist
    console.log('📋 Getting wishlist...');
    const getResponse = await axios.get(`${baseURL}/wishlist`, { headers });
    console.log('✅ Get wishlist:', getResponse.data.data?.items?.length || 0, 'items');
    
  } catch (error) {
    console.log('❌ Wishlist operations test failed:', error.response?.data || error.message);
  }
}

// Main function
async function main() {
  console.log('🚀 Starting user <NAME_EMAIL>...\n');
  
  try {
    // Create user
    await createShirhanUser();
    
    // Test login
    const token = await testLogin();
    
    // Test wishlist operations
    await testWishlistOperations(token);
    
    console.log('\n✅ All tests completed!');
    console.log('\n📋 Login credentials:');
    console.log('📧 Email: <EMAIL>');
    console.log('🔑 Password: ShirhanPass123!');
    
  } catch (error) {
    console.error('❌ Setup failed:', error);
  }
}

// Run if called directly
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { createShirhanUser, testLogin, testWishlistOperations };
