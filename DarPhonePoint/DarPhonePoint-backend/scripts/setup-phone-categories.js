const mongoose = require('mongoose');
const ProductCategory = require('../models/ProductCategory');
const config = require('../config/config');

// Phone product categories structure
const phoneCategories = [
  // Main categories
  {
    name: 'Smartphones',
    slug: 'smartphones',
    description: 'Latest smartphones from top brands including iPhone, Samsung, Google Pixel, and more',
    level: 0,
    display_order: 1,
    icon: 'smartphone',
    is_featured: true,
    subcategories: [
      {
        name: 'iPhone',
        slug: 'iphone',
        description: 'Apple iPhone smartphones - latest models and accessories',
        level: 1,
        display_order: 1
      },
      {
        name: 'Samsung Galaxy',
        slug: 'samsung-galaxy',
        description: 'Samsung Galaxy smartphones - S series, Note series, and A series',
        level: 1,
        display_order: 2
      },
      {
        name: 'Google Pixel',
        slug: 'google-pixel',
        description: 'Google Pixel smartphones with pure Android experience',
        level: 1,
        display_order: 3
      },
      {
        name: 'OnePlus',
        slug: 'oneplus',
        description: 'OnePlus smartphones - flagship killers with premium features',
        level: 1,
        display_order: 4
      },
      {
        name: '<PERSON><PERSON>',
        slug: 'xiaomi',
        description: 'Xiaomi smartphones - great value for money devices',
        level: 1,
        display_order: 5
      },
      {
        name: '<PERSON>awei',
        slug: 'huawei',
        description: 'Huawei smartphones with advanced camera technology',
        level: 1,
        display_order: 6
      }
    ]
  },
  {
    name: 'Tablets',
    slug: 'tablets',
    description: 'Tablets and iPads for work, entertainment, and creativity',
    level: 0,
    display_order: 2,
    icon: 'tablet',
    is_featured: true,
    subcategories: [
      {
        name: 'iPad',
        slug: 'ipad',
        description: 'Apple iPad tablets - iPad Pro, iPad Air, and iPad mini',
        level: 1,
        display_order: 1
      },
      {
        name: 'Samsung Tablets',
        slug: 'samsung-tablets',
        description: 'Samsung Galaxy Tab series tablets',
        level: 1,
        display_order: 2
      },
      {
        name: 'Android Tablets',
        slug: 'android-tablets',
        description: 'Android tablets from various manufacturers',
        level: 1,
        display_order: 3
      }
    ]
  },
  {
    name: 'Smartwatches',
    slug: 'smartwatches',
    description: 'Smartwatches and fitness trackers for health and connectivity',
    level: 0,
    display_order: 3,
    icon: 'watch',
    is_featured: true,
    subcategories: [
      {
        name: 'Apple Watch',
        slug: 'apple-watch',
        description: 'Apple Watch series with health and fitness tracking',
        level: 1,
        display_order: 1
      },
      {
        name: 'Samsung Galaxy Watch',
        slug: 'samsung-galaxy-watch',
        description: 'Samsung Galaxy Watch and Galaxy Watch Active',
        level: 1,
        display_order: 2
      },
      {
        name: 'Fitness Trackers',
        slug: 'fitness-trackers',
        description: 'Fitness trackers and activity monitors',
        level: 1,
        display_order: 3
      }
    ]
  },
  {
    name: 'Audio',
    slug: 'audio',
    description: 'Headphones, earbuds, and speakers for mobile devices',
    level: 0,
    display_order: 4,
    icon: 'headphones',
    is_featured: false,
    subcategories: [
      {
        name: 'Wireless Earbuds',
        slug: 'wireless-earbuds',
        description: 'True wireless earbuds including AirPods, Galaxy Buds, and more',
        level: 1,
        display_order: 1
      },
      {
        name: 'Headphones',
        slug: 'headphones',
        description: 'Over-ear and on-ear headphones for mobile devices',
        level: 1,
        display_order: 2
      },
      {
        name: 'Bluetooth Speakers',
        slug: 'bluetooth-speakers',
        description: 'Portable Bluetooth speakers for mobile audio',
        level: 1,
        display_order: 3
      }
    ]
  },
  {
    name: 'Accessories',
    slug: 'accessories',
    description: 'Phone cases, chargers, cables, and other mobile accessories',
    level: 0,
    display_order: 5,
    icon: 'accessories',
    is_featured: false,
    subcategories: [
      {
        name: 'Phone Cases',
        slug: 'phone-cases',
        description: 'Protective cases and covers for smartphones',
        level: 1,
        display_order: 1
      },
      {
        name: 'Screen Protectors',
        slug: 'screen-protectors',
        description: 'Tempered glass and film screen protectors',
        level: 1,
        display_order: 2
      },
      {
        name: 'Chargers & Cables',
        slug: 'chargers-cables',
        description: 'Charging cables, wireless chargers, and power adapters',
        level: 1,
        display_order: 3
      },
      {
        name: 'Power Banks',
        slug: 'power-banks',
        description: 'Portable power banks and battery packs',
        level: 1,
        display_order: 4
      },
      {
        name: 'Car Accessories',
        slug: 'car-accessories',
        description: 'Car mounts, chargers, and mobile accessories for vehicles',
        level: 1,
        display_order: 5
      }
    ]
  }
];

async function setupPhoneCategories() {
  try {
    // Connect to MongoDB
    await mongoose.connect(config.MONGO_URI);
    console.log('Connected to MongoDB');

    // Clear existing categories
    await ProductCategory.deleteMany({});
    console.log('Cleared existing categories');

    // Create categories
    for (const categoryData of phoneCategories) {
      const { subcategories, ...mainCategoryData } = categoryData;
      
      // Create main category
      const mainCategory = await ProductCategory.create(mainCategoryData);
      console.log(`Created main category: ${mainCategory.name}`);

      // Create subcategories
      if (subcategories && subcategories.length > 0) {
        for (const subCategoryData of subcategories) {
          const subCategory = await ProductCategory.create({
            ...subCategoryData,
            parent_category: mainCategory._id
          });
          console.log(`  Created subcategory: ${subCategory.name}`);
        }
      }
    }

    console.log('Phone categories setup completed successfully!');
    
  } catch (error) {
    console.error('Error setting up phone categories:', error);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  }
}

// Run the setup if this file is executed directly
if (require.main === module) {
  setupPhoneCategories();
}

module.exports = setupPhoneCategories;
