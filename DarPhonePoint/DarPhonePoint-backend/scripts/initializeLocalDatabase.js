const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const User = require('../models/User');
const Product = require('../models/Product');
const Lead = require('../models/Lead');
const logger = require('../utils/logger');

/**
 * Initialize local database with production-standard test data
 */
async function initializeLocalDatabase() {
  try {
    console.log('🚀 Initializing local database with production-standard test data...');

    // Connect to local database
    await mongoose.connect('mongodb://localhost:27017/aixcelerate-dev', {
      useNewUrlParser: true,
      useUnifiedTopology: true
    });

    console.log('✅ Connected to local MongoDB');

    // Clear existing data
    console.log('🧹 Clearing existing data...');
    await User.deleteMany({});
    await Product.deleteMany({});
    await Lead.deleteMany({});

    // Create admin user with production-standard credentials
    console.log('👨‍💼 Creating admin user...');
    const adminPassword = 'AIXcelerate2024!Admin#Secure';
    const salt = await bcrypt.genSalt(12);
    const hashedPassword = await bcrypt.hash(adminPassword, salt);

    const adminUser = new User({
      name: 'AIXcelerate Admin',
      email: '<EMAIL>',
      password: hashedPassword,
      role: 'admin',
      user_type: 'premium',
      status: 'active',
      isEmailVerified: true,
      purchased_products: [],
      email_sequences: [],
      last_login: new Date(),
      created_at: new Date(),
      updated_at: new Date()
    });

    await adminUser.save();
    console.log(`✅ Admin user created: <EMAIL> / ${adminPassword}`);

    // Create test user
    console.log('👤 Creating test user...');
    const testPassword = 'TestUser2024!';
    const testSalt = await bcrypt.genSalt(12);
    const testHashedPassword = await bcrypt.hash(testPassword, testSalt);

    const testUser = new User({
      name: 'Test User',
      email: '<EMAIL>',
      password: testHashedPassword,
      role: 'user',
      user_type: 'free',
      status: 'active',
      isEmailVerified: true,
      purchased_products: [],
      email_sequences: [],
      last_login: new Date(),
      created_at: new Date(),
      updated_at: new Date()
    });

    await testUser.save();
    console.log(`✅ Test user created: <EMAIL> / ${testPassword}`);

    // Create products with proper schema compliance
    console.log('📦 Creating test products...');

    const products = [
      {
        name: 'AI Prompts Lead Magnet',
        slug: 'ai-prompts-lead-magnet',
        description: 'Free collection of powerful AI prompts to boost your productivity. Get instant access to 100+ carefully crafted prompts for ChatGPT, Claude, and other AI tools.',
        price: 0,
        product_type: 'lead_magnet',
        file_path: '/products/ai_prompts_lead_magnet.pdf',
        features: ['100+ AI Prompts', 'ChatGPT Compatible', 'Instant Download', 'PDF Format'],
        is_active: true,
        whop_checkout_link: '/products/ai-prompts-lead-magnet',
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        name: 'AI Productivity Master Guide',
        slug: 'ai-productivity-master-guide',
        description: 'Complete guide to mastering AI tools for maximum productivity. Learn how to integrate AI into your daily workflow and boost your efficiency by 300%.',
        price: 2799, // $27.99
        product_type: 'basic',
        file_path: '/products/ai_productivity_master_guide.pdf',
        features: ['120-page comprehensive guide', 'Step-by-step tutorials', 'Real-world case studies', 'Bonus templates'],
        is_active: true,
        whop_checkout_link: 'https://whop.com/checkout/ai_productivity_guide',
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        name: 'AI Business Revolution',
        slug: 'ai-business-revolution',
        description: 'Transform your business with cutting-edge AI strategies and tools. Complete blueprint for implementing AI in your business operations.',
        price: 4799, // $47.99
        product_type: 'premium',
        file_path: '/products/ai_business_revolution.pdf',
        features: ['200-page business guide', 'AI implementation strategies', 'ROI calculation tools', 'Case studies', 'Video tutorials'],
        is_active: true,
        whop_checkout_link: 'https://whop.com/checkout/ai_business_revolution',
        created_at: new Date(),
        updated_at: new Date()
      }
    ];

    for (const productData of products) {
      const product = new Product(productData);
      await product.save();
      console.log(`✅ Created product: ${productData.name}`);
    }

    // Create test leads
    console.log('📧 Creating test leads...');
    const leads = [
      {
        email: '<EMAIL>',
        name: 'John Doe',
        source: 'homepage',
        status: 'new',
        lead_magnet: 'ai-prompts-lead-magnet',
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        email: '<EMAIL>',
        name: 'Jane Smith',
        source: 'product-page',
        status: 'contacted',
        lead_magnet: 'ai-prompts-lead-magnet',
        created_at: new Date(),
        updated_at: new Date()
      }
    ];

    for (const leadData of leads) {
      const lead = new Lead(leadData);
      await lead.save();
      console.log(`✅ Created lead: ${leadData.email}`);
    }

    // Verify data
    const userCount = await User.countDocuments();
    const productCount = await Product.countDocuments();
    const leadCount = await Lead.countDocuments();

    console.log('\n🎉 Database initialization completed successfully!');
    console.log('='.repeat(50));
    console.log(`👥 Users created: ${userCount}`);
    console.log(`📦 Products created: ${productCount}`);
    console.log(`📧 Leads created: ${leadCount}`);
    console.log('='.repeat(50));
    
    console.log('\n🔑 Login Credentials:');
    console.log(`Admin: <EMAIL> / ${adminPassword}`);
    console.log(`Test User: <EMAIL> / ${testPassword}`);
    
    console.log('\n✅ You can now test the complete system!');

  } catch (error) {
    console.error('❌ Database initialization failed:', error);
    throw error;
  } finally {
    await mongoose.connection.close();
    console.log('📝 Database connection closed');
  }
}

// Run initialization if called directly
if (require.main === module) {
  initializeLocalDatabase()
    .then(() => {
      console.log('🎯 Database initialization completed successfully!');
      process.exit(0);
    })
    .catch(error => {
      console.error('💥 Database initialization failed:', error);
      process.exit(1);
    });
}

module.exports = { initializeLocalDatabase };
