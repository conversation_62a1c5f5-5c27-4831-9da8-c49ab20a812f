const mongoose = require('mongoose');
const User = require('../models/User');
const config = require('../config/config');
const bcrypt = require('bcrypt');

// Connect to MongoDB
mongoose.connect(config.MONGODB_URI)
  .then(() => console.log('MongoDB Connected'))
  .catch(err => {
    console.error('MongoDB Connection Error:', err);
    process.exit(1);
  });

// Update admin user password
const updateAdminPassword = async () => {
  try {
    // Find admin user
    const adminUser = await User.findOne({ email: '<EMAIL>' });
    
    if (!adminUser) {
      console.log('Admin user not found');
      mongoose.disconnect();
      return;
    }
    
    // Generate new password hash using bcrypt
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash('Admin123!@#', salt);
    
    // Update password directly in the database to bypass the pre-save hook
    await User.updateOne(
      { _id: adminUser._id },
      { $set: { password: hashedPassword } }
    );
    
    console.log('Admin password updated successfully for:', adminUser.email);
    console.log('New password: Admin123!@#');
    
    // Verify the password works
    const updatedUser = await User.findById(adminUser._id).select('+password');
    const isMatch = await bcrypt.compare('Admin123!@#', updatedUser.password);
    
    console.log('Password verification:', isMatch ? 'SUCCESS' : 'FAILED');
    
    mongoose.disconnect();
  } catch (err) {
    console.error('Error updating admin password:', err);
    mongoose.disconnect();
    process.exit(1);
  }
};

updateAdminPassword();
