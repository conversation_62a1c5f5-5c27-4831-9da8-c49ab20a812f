/**
 * Fix inventory records by creating missing inventory entries
 * and syncing quantities with product variants
 */

const mongoose = require('mongoose');
const Inventory = require('../models/Inventory');
const Product = require('../models/Product');

const connectDB = async () => {
  try {
    await mongoose.connect('mongodb://localhost:27017/aixcelerate-dev');
    console.log('MongoDB connected successfully');
  } catch (error) {
    console.error('MongoDB connection error:', error);
    process.exit(1);
  }
};

const fixInventoryRecords = async () => {
  try {
    console.log('🔧 Fixing inventory records...\n');
    
    const products = await Product.find({ track_inventory: true });
    console.log(`📱 Found ${products.length} products with track_inventory=true\n`);
    
    let createdCount = 0;
    let updatedCount = 0;
    let skippedCount = 0;
    
    for (const product of products) {
      console.log(`\n📱 Processing: ${product.name}`);
      
      if (product.variants && product.variants.length > 0) {
        // Product has variants
        for (const variant of product.variants) {
          console.log(`   - Variant: ${variant.name} (${variant.sku})`);
          
          let inventory = await Inventory.findOne({
            product: product._id,
            variant_sku: variant.sku
          });
          
          if (!inventory) {
            // Create new inventory record
            inventory = new Inventory({
              product: product._id,
              variant_sku: variant.sku,
              location: 'main_warehouse',
              quantity_on_hand: variant.stock_quantity || 0,
              quantity_reserved: 0,
              quantity_available: variant.stock_quantity || 0,
              reorder_point: 5,
              reorder_quantity: 20,
              max_stock_level: Math.max(50, (variant.stock_quantity || 0) * 2),
              average_cost: (variant.price || product.price) * 0.7,
              last_cost: (variant.price || product.price) * 0.7,
              devices: [] // Empty for non-IMEI products
            });
            
            await inventory.save();
            console.log(`     ✅ Created inventory record (${variant.stock_quantity} available)`);
            createdCount++;
          } else {
            // Update existing inventory record
            const oldQuantity = inventory.quantity_available;
            inventory.quantity_on_hand = variant.stock_quantity || 0;
            inventory.quantity_available = variant.stock_quantity || 0;
            inventory.average_cost = (variant.price || product.price) * 0.7;
            inventory.last_cost = (variant.price || product.price) * 0.7;
            
            await inventory.save();
            console.log(`     🔄 Updated inventory record (${oldQuantity} → ${variant.stock_quantity})`);
            updatedCount++;
          }
        }
      } else {
        // Product has no variants
        let inventory = await Inventory.findOne({
          product: product._id,
          variant_sku: null
        });
        
        if (!inventory) {
          // Create new inventory record
          inventory = new Inventory({
            product: product._id,
            variant_sku: null,
            location: 'main_warehouse',
            quantity_on_hand: product.stock_quantity || 0,
            quantity_reserved: 0,
            quantity_available: product.stock_quantity || 0,
            reorder_point: 5,
            reorder_quantity: 20,
            max_stock_level: Math.max(50, (product.stock_quantity || 0) * 2),
            average_cost: product.price * 0.7,
            last_cost: product.price * 0.7,
            devices: [] // Empty for non-IMEI products
          });
          
          await inventory.save();
          console.log(`   ✅ Created inventory record (${product.stock_quantity} available)`);
          createdCount++;
        } else {
          // Update existing inventory record
          const oldQuantity = inventory.quantity_available;
          inventory.quantity_on_hand = product.stock_quantity || 0;
          inventory.quantity_available = product.stock_quantity || 0;
          inventory.average_cost = product.price * 0.7;
          inventory.last_cost = product.price * 0.7;
          
          await inventory.save();
          console.log(`   🔄 Updated inventory record (${oldQuantity} → ${product.stock_quantity})`);
          updatedCount++;
        }
      }
    }
    
    console.log(`\n🎉 Inventory fix completed!`);
    console.log(`📊 Summary:`);
    console.log(`   - Created new records: ${createdCount}`);
    console.log(`   - Updated existing records: ${updatedCount}`);
    console.log(`   - Skipped records: ${skippedCount}`);
    console.log(`   - Total processed: ${createdCount + updatedCount + skippedCount}`);
    
    // Verify the fix
    console.log(`\n🔍 Verification:`);
    const totalInventoryRecords = await Inventory.countDocuments({});
    console.log(`   - Total inventory records now: ${totalInventoryRecords}`);
    
    // Check for any remaining mismatches
    const productsWithMismatches = [];
    for (const product of products) {
      if (product.variants && product.variants.length > 0) {
        for (const variant of product.variants) {
          const inventory = await Inventory.findOne({
            product: product._id,
            variant_sku: variant.sku
          });
          if (!inventory || inventory.quantity_available !== variant.stock_quantity) {
            productsWithMismatches.push(`${product.name} - ${variant.name}`);
          }
        }
      } else {
        const inventory = await Inventory.findOne({
          product: product._id,
          variant_sku: null
        });
        if (!inventory || inventory.quantity_available !== product.stock_quantity) {
          productsWithMismatches.push(product.name);
        }
      }
    }
    
    if (productsWithMismatches.length === 0) {
      console.log(`   ✅ All inventory records are now in sync!`);
    } else {
      console.log(`   ⚠️  Still ${productsWithMismatches.length} mismatches found`);
      productsWithMismatches.forEach(item => console.log(`     - ${item}`));
    }
    
    process.exit(0);
  } catch (error) {
    console.error('❌ Error fixing inventory:', error);
    process.exit(1);
  }
};

const runFix = async () => {
  await connectDB();
  await fixInventoryRecords();
};

if (require.main === module) {
  runFix();
}

module.exports = { fixInventoryRecords };
