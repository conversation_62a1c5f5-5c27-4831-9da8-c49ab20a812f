#!/usr/bin/env node

/**
 * Comprehensive Test Runner for AIXcelerate Backend
 * This script runs all tests and generates coverage reports
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

console.log('🚀 Starting Comprehensive Test Suite for AIXcelerate Backend\n');

// Test configuration
const testConfig = {
  unit: {
    pattern: 'tests/unit/**/*.test.js',
    description: 'Unit Tests'
  },
  integration: {
    pattern: 'tests/integration/**/*.test.js',
    description: 'Integration Tests'
  },
  e2e: {
    pattern: 'tests/e2e/**/*.test.js',
    description: 'End-to-End Tests'
  },
  api: {
    pattern: 'tests/api/**/*.test.js',
    description: 'API Tests'
  }
};

// Coverage thresholds
const coverageThresholds = {
  statements: 80,
  branches: 75,
  functions: 80,
  lines: 80
};

async function runTestSuite(suiteName, config) {
  return new Promise((resolve, reject) => {
    console.log(`\n📋 Running ${config.description}...`);
    
    const jestArgs = [
      config.pattern,
      '--coverage',
      '--verbose',
      '--detectOpenHandles',
      '--forceExit',
      '--maxWorkers=1'
    ];

    const jest = spawn('npx', ['jest', ...jestArgs], {
      stdio: 'inherit',
      cwd: process.cwd()
    });

    jest.on('close', (code) => {
      if (code === 0) {
        console.log(`✅ ${config.description} completed successfully`);
        resolve({ suite: suiteName, success: true, code });
      } else {
        console.log(`❌ ${config.description} failed with code ${code}`);
        resolve({ suite: suiteName, success: false, code });
      }
    });

    jest.on('error', (error) => {
      console.error(`Error running ${config.description}:`, error);
      reject(error);
    });
  });
}

async function runAllTests() {
  const results = [];
  
  try {
    // Run each test suite
    for (const [suiteName, config] of Object.entries(testConfig)) {
      const result = await runTestSuite(suiteName, config);
      results.push(result);
    }

    // Generate summary report
    console.log('\n' + '='.repeat(60));
    console.log('📊 TEST RESULTS SUMMARY');
    console.log('='.repeat(60));

    let totalPassed = 0;
    let totalFailed = 0;

    results.forEach(result => {
      const status = result.success ? '✅ PASSED' : '❌ FAILED';
      console.log(`${status} - ${testConfig[result.suite].description}`);
      
      if (result.success) {
        totalPassed++;
      } else {
        totalFailed++;
      }
    });

    console.log('\n' + '-'.repeat(40));
    console.log(`Total Test Suites: ${results.length}`);
    console.log(`Passed: ${totalPassed}`);
    console.log(`Failed: ${totalFailed}`);
    console.log(`Success Rate: ${((totalPassed / results.length) * 100).toFixed(1)}%`);

    // Check if coverage directory exists
    const coverageDir = path.join(process.cwd(), 'coverage');
    if (fs.existsSync(coverageDir)) {
      console.log('\n📄 Coverage report generated in ./coverage directory');
      console.log('📄 Open ./coverage/lcov-report/index.html to view detailed coverage');
    }

    // Final verdict
    if (totalFailed === 0) {
      console.log('\n🎉 ALL TESTS PASSED! System is ready for production.');
      process.exit(0);
    } else {
      console.log('\n⚠️  Some tests failed. Please review and fix before deployment.');
      process.exit(1);
    }

  } catch (error) {
    console.error('Error running test suite:', error);
    process.exit(1);
  }
}

// Check if Jest is available
function checkJestAvailability() {
  return new Promise((resolve) => {
    const jest = spawn('npx', ['jest', '--version'], { stdio: 'pipe' });
    
    jest.on('close', (code) => {
      if (code === 0) {
        resolve(true);
      } else {
        console.error('❌ Jest is not available. Please install Jest first.');
        console.log('Run: npm install --save-dev jest');
        resolve(false);
      }
    });
  });
}

// Main execution
async function main() {
  console.log('🔍 Checking test environment...');
  
  const jestAvailable = await checkJestAvailability();
  if (!jestAvailable) {
    process.exit(1);
  }

  console.log('✅ Jest is available');
  console.log('✅ Test environment ready');

  await runAllTests();
}

// Handle process termination
process.on('SIGINT', () => {
  console.log('\n\n⚠️  Test execution interrupted by user');
  process.exit(1);
});

process.on('SIGTERM', () => {
  console.log('\n\n⚠️  Test execution terminated');
  process.exit(1);
});

// Run the main function
main().catch((error) => {
  console.error('Fatal error:', error);
  process.exit(1);
});
