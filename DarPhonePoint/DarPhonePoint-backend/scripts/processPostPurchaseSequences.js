/**
 * Process Post-Purchase Sequences Script
 *
 * This script finds completed orders and triggers the post-purchase email sequence.
 * It should be run periodically via a cron job.
 */

const mongoose = require('mongoose');
const Order = require('../models/Order');
const User = require('../models/User');
const Product = require('../models/Product');
const EmailSequence = require('../models/EmailSequence');
const EmailSequenceTracking = require('../models/EmailSequenceTracking');
const dotenv = require('dotenv');
const logger = require('../utils/logger');

// Load environment variables
dotenv.config();

// Connect to MongoDB
mongoose.connect(process.env.MONGO_URI || 'mongodb://localhost:27017/aixcelerate')
  .then(() => {
    logger.info('MongoDB connected for post-purchase sequence processing');
    processPostPurchaseSequences();
  })
  .catch(err => {
    logger.error('MongoDB connection error:', err);
    process.exit(1);
  });

/**
 * Process post-purchase sequences
 */
async function processPostPurchaseSequences() {
  try {
    logger.info('Processing post-purchase sequences...');

    // Find completed orders that haven't been processed for email sequences
    const completedOrders = await Order.find({
      payment_status: 'completed',
      post_purchase_email_sent: { $ne: true }
    }).populate('user').populate('product');

    logger.info(`Found ${completedOrders.length} completed orders to process`);

    // Find the post-purchase email sequence
    const postPurchaseSequence = await EmailSequence.findOne({
      trigger: 'purchase',
      isActive: true
    });

    if (!postPurchaseSequence) {
      logger.warn('No active post-purchase email sequence found');
      mongoose.disconnect();
      return;
    }

    let processedCount = 0;
    let errorCount = 0;

    // Process each completed order
    for (const order of completedOrders) {
      try {
        // Get user from order
        let user = order.user;

        // If order doesn't have user but has customer_email, try to find user by email
        if (!user && order.customer_email) {
          user = await User.findOne({ email: order.customer_email });
        }

        // Skip if no user or user has no email
        if (!user || !user.email) {
          continue;
        }

        // Get product from order
        const product = order.product;

        // Skip if no product
        if (!product) {
          continue;
        }

        // Check if user already has this sequence for this order
        const existingTracking = await EmailSequenceTracking.findOne({
          user: user._id,
          email_sequence: postPurchaseSequence._id,
          'metadata.order_id': order._id.toString()
        });

        if (existingTracking) {
          // Already being processed, skip
          continue;
        }

        // Create new tracking for this user and sequence
        const tracking = new EmailSequenceTracking({
          user: user._id,
          email: user.email,
          email_sequence: postPurchaseSequence._id,
          current_position: 0,
          next_email_date: new Date(), // Send first email immediately
          is_completed: false,
          is_paused: false,
          unsubscribed: false,
          metadata: {
            order_id: order._id.toString(),
            product_id: product._id.toString(),
            product_name: product.name,
            order_date: order.created_at
          }
        });

        await tracking.save();

        // Mark order as processed
        order.post_purchase_email_sent = true;
        await order.save();

        processedCount++;

        logger.info(`Created post-purchase sequence for user ${user.email} and product ${product.name}`);
      } catch (err) {
        logger.error(`Error processing order ${order._id}:`, err);
        errorCount++;
      }
    }

    logger.info(`Post-purchase sequence processing complete. Processed: ${processedCount}, Errors: ${errorCount}`);
    mongoose.disconnect();
  } catch (err) {
    logger.error('Error processing post-purchase sequences:', err);
    mongoose.disconnect();
    process.exit(1);
  }
}
