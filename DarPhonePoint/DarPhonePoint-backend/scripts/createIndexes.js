#!/usr/bin/env node

/**
 * Simple Database Index Creation Script for Phone Point Dar
 * Creates all necessary indexes for optimal performance
 */

const mongoose = require('mongoose');
const config = require('../config/config');

async function createIndexes() {
  try {
    console.log('🚀 Connecting to MongoDB...');
    await mongoose.connect(config.MONGODB_URI, {
      maxPoolSize: 10,
      minPoolSize: 5,
      serverSelectionTimeoutMS: 5000,
      socketTimeoutMS: 45000,
    });
    console.log('✅ Connected to MongoDB');

    const db = mongoose.connection.db;
    let indexesCreated = 0;

    console.log('📊 Creating indexes for Phone Point Dar...');

    // Helper function to create index safely
    const createIndex = async (collection, indexSpec, options = {}) => {
      try {
        await db.collection(collection).createIndex(indexSpec, options);
        console.log(`✅ Created index on ${collection}:`, indexSpec);
        indexesCreated++;
        return true;
      } catch (error) {
        if (error.code === 85) {
          console.log(`ℹ️  Index already exists on ${collection}:`, indexSpec);
        } else {
          console.error(`❌ Failed to create index on ${collection}:`, error.message);
        }
        return false;
      }
    };

    // User collection indexes
    console.log('\n👥 Creating User indexes...');
    // email index already created by unique: true constraint in User schema
    await createIndex('users', { role: 1, status: 1 });
    await createIndex('users', { created_at: -1 });
    await createIndex('users', { phone: 1 });

    // Product collection indexes (critical for Phone Point Dar)
    console.log('\n📱 Creating Product indexes...');
    await createIndex('products', { is_active: 1, category: 1 });
    await createIndex('products', { brand: 1, model: 1 });
    await createIndex('products', { price: 1 });
    await createIndex('products', { created_at: -1 });
    await createIndex('products', { slug: 1 }, { unique: true });
    await createIndex('products', { stock_quantity: 1, is_low_stock: 1 });
    await createIndex('products', { category: 1, brand: 1, price: 1 });

    // Order collection indexes
    console.log('\n🛒 Creating Order indexes...');
    await createIndex('orders', { user: 1, order_status: 1 });
    await createIndex('orders', { order_status: 1, created_at: -1 });
    await createIndex('orders', { created_at: -1 });
    await createIndex('orders', { order_number: 1 }, { unique: true });
    await createIndex('orders', { customer_email: 1 });
    await createIndex('orders', { payment_status: 1 });

    // Cart collection indexes
    console.log('\n🛍️ Creating Cart indexes...');
    // user index already created in Cart schema
    await createIndex('carts', { session_id: 1 });
    await createIndex('carts', { updated_at: -1 });

    // Inventory collection indexes (critical for Phone Point Dar)
    console.log('\n📦 Creating Inventory indexes...');
    await createIndex('inventories', { product: 1, variant_sku: 1 });
    // devices.imei index already created by optimizePhonePointIndexes.js
    await createIndex('inventories', { 'devices.status': 1 });
    await createIndex('inventories', { location: 1, quantity: 1 });
    await createIndex('inventories', { is_low_stock: 1 });

    // Wishlist collection indexes
    console.log('\n❤️ Creating Wishlist indexes...');
    // user index already created in Wishlist schema with unique: true
    await createIndex('wishlists', { 'items.product': 1 });

    // Analytics collection indexes
    console.log('\n📈 Creating Analytics indexes...');
    await createIndex('analytics', { event_type: 1, timestamp: -1 });
    await createIndex('analytics', { user_id: 1, timestamp: -1 });
    await createIndex('analytics', { session_id: 1 });

    // Trade-in collection indexes
    console.log('\n🔄 Creating Trade-in indexes...');
    await createIndex('tradeins', { customer: 1 });
    await createIndex('tradeins', { imei: 1 }, { unique: true });
    await createIndex('tradeins', { status: 1, created_at: -1 });

    // Shipping collection indexes
    console.log('\n🚚 Creating Shipping indexes...');
    await createIndex('shippings', { order: 1 }, { unique: true });
    await createIndex('shippings', { tracking_number: 1 });
    await createIndex('shippings', { status: 1, updated_at: -1 });

    console.log(`\n🎉 Index creation completed! Created ${indexesCreated} new indexes.`);

    // Get collection stats
    console.log('\n📊 Collection Statistics:');
    const collections = await db.listCollections().toArray();
    
    for (const collection of collections) {
      try {
        const stats = await db.collection(collection.name).stats();
        console.log(`${collection.name}: ${stats.count} documents, ${stats.nindexes} indexes`);
      } catch (error) {
        console.log(`${collection.name}: Unable to get stats`);
      }
    }

    await mongoose.connection.close();
    console.log('\n✅ Database optimization completed successfully!');
    process.exit(0);

  } catch (error) {
    console.error('❌ Database optimization failed:', error);
    process.exit(1);
  }
}

// Run the script
createIndexes();
