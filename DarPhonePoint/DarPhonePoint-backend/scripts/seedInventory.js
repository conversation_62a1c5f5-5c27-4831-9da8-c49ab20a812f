const mongoose = require('mongoose');
const Product = require('../models/Product');
const Inventory = require('../models/Inventory');

// Connect to database
mongoose.connect('mongodb://localhost:27017/aixcelerate-dev');

// Inventory data for Phone Point Dar products
const inventoryData = {
  // SMARTPHONES - Higher value items with moderate stock
  'IP15PM-BASE': { quantity: 15, reorder_point: 5, reorder_quantity: 10 },
  'SGS24U-BASE': { quantity: 20, reorder_point: 5, reorder_quantity: 15 },
  'RN13-BASE': { quantity: 50, reorder_point: 10, reorder_quantity: 30 },
  'GP8-BASE': { quantity: 12, reorder_point: 3, reorder_quantity: 10 },
  
  // AUDIO ACCESSORIES - Premium items with moderate stock
  'AIRPODS-PRO-2': { quantity: 25, reorder_point: 8, reorder_quantity: 20 },
  'GALAXY-BUDS2-PRO': { quantity: 30, reorder_point: 10, reorder_quantity: 25 }
};

async function seedInventory() {
  try {
    console.log('📦 Starting Phone Point Dar inventory seeding...');
    
    // Clear existing inventory
    await Inventory.deleteMany({});
    console.log('🗑️  Cleared existing inventory records');
    
    // Get all products
    const products = await Product.find({});
    console.log(`📱 Found ${products.length} products`);
    
    let inventoryRecords = [];
    let updatedProducts = [];
    
    for (const product of products) {
      console.log(`\n📦 Processing: ${product.name}`);
      
      if (product.track_inventory) {
        // Create inventory record for products that track inventory
        const stockData = inventoryData[product.sku];
        
        if (stockData) {
          const inventoryRecord = {
            product: product._id,
            variant_sku: null, // Base product inventory
            location: 'main_warehouse',
            quantity_on_hand: stockData.quantity,
            quantity_available: stockData.quantity,
            quantity_reserved: 0,
            reorder_point: stockData.reorder_point,
            reorder_quantity: stockData.reorder_quantity,
            cost_per_unit: Math.round(product.price * 0.6), // Assume 40% markup
            notes: `Initial stock for ${product.name}`
          };
          
          inventoryRecords.push(inventoryRecord);
          
          // Update product stock_quantity
          product.stock_quantity = stockData.quantity;
          updatedProducts.push(product);
          
          console.log(`   ✅ Inventory: ${stockData.quantity} units`);
        } else {
          console.log(`   ⚠️  No inventory data found for SKU: ${product.sku}`);
        }
      } else {
        // For products that don't track inventory, set a high stock quantity
        product.stock_quantity = 999;
        updatedProducts.push(product);
        console.log(`   ✅ Non-tracked: Set to 999 units (unlimited)`);
      }
    }
    
    // Insert inventory records
    if (inventoryRecords.length > 0) {
      await Inventory.insertMany(inventoryRecords);
      console.log(`\n📦 Created ${inventoryRecords.length} inventory records`);
    }
    
    // Update product stock quantities
    for (const product of updatedProducts) {
      await product.save();
    }
    console.log(`📱 Updated ${updatedProducts.length} product stock quantities`);
    
    // Summary
    console.log('\n📊 Inventory Summary:');
    const trackingProducts = products.filter(p => p.track_inventory);
    const nonTrackingProducts = products.filter(p => !p.track_inventory);
    
    console.log(`   📱 Products with inventory tracking: ${trackingProducts.length}`);
    console.log(`   📦 Products without inventory tracking: ${nonTrackingProducts.length}`);
    console.log(`   📋 Total inventory records created: ${inventoryRecords.length}`);
    
    console.log('\n📦 Stock levels by product:');
    for (const product of products) {
      const stockData = inventoryData[product.sku];
      if (product.track_inventory && stockData) {
        console.log(`   ${product.name}: ${stockData.quantity} units`);
      } else if (!product.track_inventory) {
        console.log(`   ${product.name}: Unlimited (no tracking)`);
      }
    }
    
    console.log('\n🎉 Phone Point Dar inventory seeding completed successfully!');
    console.log('💡 All products should now show as "In Stock" on the website');
    
    process.exit(0);
    
  } catch (error) {
    console.error('❌ Error seeding inventory:', error);
    process.exit(1);
  }
}

seedInventory();
