/**
 * Seed script to create sample orders for testing
 * Run with: node scripts/seedOrders.js
 */

const mongoose = require('mongoose');
const dotenv = require('dotenv');
const Product = require('../models/Product');
const Order = require('../models/Order');
const User = require('../models/User');
const Analytics = require('../models/Analytics');

// Load environment variables
dotenv.config();

// Connect to MongoDB
mongoose.connect(process.env.MONGO_URI || 'mongodb://localhost:27017/aixcelerate', {
  useNewUrlParser: true,
  useUnifiedTopology: true
});

// Generate a random date within the last 30 days
const getRandomDate = () => {
  const now = new Date();
  const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
  return new Date(thirtyDaysAgo.getTime() + Math.random() * (now.getTime() - thirtyDaysAgo.getTime()));
};

// Generate a random session ID
const generateSessionId = () => {
  return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
};

// Create sample orders
const seedOrders = async () => {
  try {
    // Clear existing orders and analytics
    await Order.deleteMany({});
    await Analytics.deleteMany({ event_type: { $in: ['product_view', 'purchase'] } });

    // Get all products
    const products = await Product.find();
    if (products.length === 0) {
      console.log('No products found. Please run seedProducts.js first.');
      process.exit(1);
    }

    // Get admin user
    const adminUser = await User.findOne({ email: '<EMAIL>' });
    if (!adminUser) {
      console.log('Admin user not found. Please run seedInitialData.js first.');
      process.exit(1);
    }

    // Create sample orders for each product
    const orders = [];
    const analytics = [];

    for (const product of products) {
      // Generate random number of orders (1-5) for each product
      const numOrders = Math.floor(Math.random() * 5) + 1;
      
      for (let i = 0; i < numOrders; i++) {
        const sessionId = generateSessionId();
        const orderDate = getRandomDate();
        
        // Create product view analytics event
        analytics.push({
          event_type: 'product_view',
          session_id: sessionId,
          page_url: `/products/${product.slug}`,
          product_id: product._id,
          user_id: adminUser._id,
          device_type: Math.random() > 0.5 ? 'desktop' : 'mobile',
          browser: 'Chrome',
          referrer: 'direct',
          metadata: { product_id: product._id.toString() },
          created_at: new Date(orderDate.getTime() - 1000 * 60 * 5) // 5 minutes before order
        });

        // Create order
        const order = {
          user: adminUser._id,
          product: product._id,
          amount: product.price,
          payment_status: 'completed',
          transaction_id: `tr_${Math.random().toString(36).substring(2, 10)}`,
          payment_method: 'credit_card',
          customer_email: adminUser.email,
          created_at: orderDate,
          updated_at: orderDate
        };
        
        orders.push(order);
        
        // Create purchase analytics event
        analytics.push({
          event_type: 'purchase',
          session_id: sessionId,
          page_url: '/checkout/success',
          product_id: product._id,
          user_id: adminUser._id,
          device_type: Math.random() > 0.5 ? 'desktop' : 'mobile',
          browser: 'Chrome',
          referrer: 'direct',
          metadata: { product_id: product._id.toString() },
          created_at: orderDate
        });
      }
      
      // Add extra product views (without purchase) to create realistic conversion rates
      const extraViews = Math.floor(Math.random() * 10) + 5;
      
      for (let i = 0; i < extraViews; i++) {
        analytics.push({
          event_type: 'product_view',
          session_id: generateSessionId(),
          page_url: `/products/${product.slug}`,
          product_id: product._id,
          device_type: Math.random() > 0.5 ? 'desktop' : 'mobile',
          browser: 'Chrome',
          referrer: 'direct',
          metadata: { product_id: product._id.toString() },
          created_at: getRandomDate()
        });
      }
    }

    // Insert orders and analytics
    await Order.insertMany(orders);
    await Analytics.insertMany(analytics);

    console.log(`Created ${orders.length} sample orders and ${analytics.length} analytics events.`);
    process.exit(0);
  } catch (error) {
    console.error('Error seeding orders:', error);
    process.exit(1);
  }
};

// Run the seed function
seedOrders();
