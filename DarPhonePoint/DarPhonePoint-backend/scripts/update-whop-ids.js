const mongoose = require('mongoose');
const Product = require('../models/Product');
require('dotenv').config();

async function updateWhopIds() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ Connected to MongoDB');

    // Update products with Whop IDs
    // You'll need to replace these with actual IDs from Whop dashboard
    
    const updates = [
      {
        name: 'AI Prompts Lead Magnet',
        whop_product_id: 'REPLACE_WITH_WHOP_PRODUCT_ID_1',
        whop_price_id: 'REPLACE_WITH_WHOP_PRICE_ID_1'
      },
      {
        name: 'AI Productivity Master Guide',
        whop_product_id: 'REPLACE_WITH_WHOP_PRODUCT_ID_2',
        whop_price_id: 'REPLACE_WITH_WHOP_PRICE_ID_2'
      },
      {
        name: 'AI Business Revolution',
        whop_product_id: 'REPLACE_WITH_WHOP_PRODUCT_ID_3',
        whop_price_id: 'REPLACE_WITH_WHOP_PRICE_ID_3'
      }
    ];

    console.log('🔄 Updating products with Whop IDs...\n');

    for (const update of updates) {
      const result = await Product.updateOne(
        { name: update.name },
        { 
          $set: { 
            whop_product_id: update.whop_product_id,
            whop_price_id: update.whop_price_id
          }
        }
      );

      if (result.matchedCount > 0) {
        console.log(`✅ Updated "${update.name}"`);
        console.log(`   Product ID: ${update.whop_product_id}`);
        console.log(`   Price ID: ${update.whop_price_id}\n`);
      } else {
        console.log(`❌ Product "${update.name}" not found in database\n`);
      }
    }

    // Verify updates
    console.log('📋 Current products with Whop IDs:');
    const products = await Product.find({}, 'name whop_product_id whop_price_id price');
    
    products.forEach(product => {
      console.log(`\n📦 ${product.name}`);
      console.log(`   Price: $${product.price}`);
      console.log(`   Whop Product ID: ${product.whop_product_id || 'NOT SET'}`);
      console.log(`   Whop Price ID: ${product.whop_price_id || 'NOT SET'}`);
    });

    console.log('\n🎉 Whop IDs update completed!');
    
  } catch (error) {
    console.error('❌ Error updating Whop IDs:', error);
  } finally {
    await mongoose.disconnect();
    console.log('📤 Disconnected from MongoDB');
  }
}

// Instructions for usage
console.log(`
🔧 WHOP IDS UPDATE SCRIPT

BEFORE RUNNING THIS SCRIPT:
1. Create products in Whop dashboard
2. Copy the Product IDs and Price IDs
3. Replace the placeholder IDs in this script
4. Run: node scripts/update-whop-ids.js

STEPS TO GET WHOP IDS:
1. Go to: https://whop.com/dashboard/developer/
2. Find your app: app_y3gwfWiwbFd8uJ
3. Create products and copy their IDs
4. Update this script with real IDs
5. Run the script

`);

// Only run if called directly
if (require.main === module) {
  updateWhopIds();
}

module.exports = updateWhopIds;
