const mongoose = require('mongoose');
const User = require('../models/User');
const config = require('../config/config');

// Connect to MongoDB
mongoose.connect(config.MONGODB_URI)
  .then(() => console.log('MongoDB Connected'))
  .catch(err => {
    console.error('MongoDB Connection Error:', err);
    process.exit(1);
  });

// Check for admin users
const checkAdminUsers = async () => {
  try {
    const adminUsers = await User.find({ role: 'admin' });

    console.log(`Found ${adminUsers.length} admin users:`);

    if (adminUsers.length > 0) {
      adminUsers.forEach(user => {
        console.log(`- ${user.name} (${user.email})`);
      });
    } else {
      console.log('No admin users found.');
    }

    // Check for regular users as well
    const regularUsers = await User.find({ role: 'user' });

    console.log(`\nFound ${regularUsers.length} regular users:`);

    if (regularUsers.length > 0) {
      regularUsers.forEach(user => {
        console.log(`- ${user.name} (${user.email})`);
      });
    } else {
      console.log('No regular users found.');
    }

    mongoose.disconnect();
  } catch (err) {
    console.error('Error checking admin users:', err);
    mongoose.disconnect();
    process.exit(1);
  }
};

checkAdminUsers();
