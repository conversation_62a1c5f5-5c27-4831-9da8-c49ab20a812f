const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const User = require('../models/User');
const config = require('../config/config');

// Connect to MongoDB
mongoose.connect(config.MONGODB_URI)
  .then(() => console.log('MongoDB Connected'))
  .catch(err => {
    console.error('MongoDB Connection Error:', err);
    process.exit(1);
  });

// Update admin user
const updateAdmin = async () => {
  try {
    // Find admin user
    const adminUser = await User.findOne({ email: '<EMAIL>' });
    
    if (!adminUser) {
      console.log('Admin user not found');
      mongoose.disconnect();
      return;
    }
    
    // Generate new password hash
    const salt = await bcrypt.genSalt(12);
    const hashedPassword = await bcrypt.hash('Admin123!', salt);
    
    // Update admin user
    adminUser.password = hashedPassword;
    adminUser.isEmailVerified = true;
    adminUser.role = 'admin';
    adminUser.user_type = 'premium';
    adminUser.status = 'active';
    
    await adminUser.save();
    
    console.log('Admin user updated successfully:');
    console.log('Email:', adminUser.email);
    console.log('Password: Admin123!');
    console.log('Role:', adminUser.role);
    console.log('User Type:', adminUser.user_type);
    console.log('Status:', adminUser.status);
    console.log('Email Verified:', adminUser.isEmailVerified);
    
    mongoose.disconnect();
  } catch (err) {
    console.error('Error updating admin user:', err);
    mongoose.disconnect();
    process.exit(1);
  }
};

// Run the function
updateAdmin(); 