const mongoose = require('mongoose');
const User = require('../models/User');
const config = require('../config/config');

// Connect to MongoDB
mongoose.connect(config.MONGODB_URI, {
  useNewUrlParser: true,
  useUnifiedTopology: true,
});

// Create specific admin user
const createSpecificAdmin = async () => {
  try {
    console.log('Connecting to MongoDB...');
    
    // Find the user by email
    const user = await User.findOne({ email: '<EMAIL>' });
    
    if (!user) {
      console.log('User not found. Creating new admin user...');
      
      // Create new admin user
      const adminUser = new User({
        name: 'Admin User',
        email: '<EMAIL>',
        password: 'aix@admin123',
        role: 'admin',
        user_type: 'premium',
        status: 'active'
      });
      
      await adminUser.save();
      console.log('✅ Admin user created successfully!');
      console.log('Email: <EMAIL>');
      console.log('Password: aix@admin123');
      console.log('Role: admin');
    } else {
      console.log('User found. Updating role to admin...');
      
      // Update user role to admin
      user.role = 'admin';
      user.user_type = 'premium';
      user.status = 'active';
      
      await user.save();
      console.log('✅ User role updated to admin successfully!');
      console.log('Email: <EMAIL>');
      console.log('Password: aix@admin123');
      console.log('Role: admin');
    }
    
    // Verify the admin user
    const adminUser = await User.findOne({ email: '<EMAIL>' });
    console.log('\n📋 Admin User Details:');
    console.log('ID:', adminUser._id);
    console.log('Name:', adminUser.name);
    console.log('Email:', adminUser.email);
    console.log('Role:', adminUser.role);
    console.log('User Type:', adminUser.user_type);
    console.log('Status:', adminUser.status);
    
    mongoose.disconnect();
    console.log('\n🎉 Admin user setup completed successfully!');
    
  } catch (error) {
    console.error('❌ Error creating admin user:', error);
    mongoose.disconnect();
    process.exit(1);
  }
};

// Run the script
createSpecificAdmin();
