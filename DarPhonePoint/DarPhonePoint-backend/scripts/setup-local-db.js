const mongoose = require('mongoose');
const User = require('../models/User');
const Product = require('../models/Product');

// Use local environment
require('dotenv').config({ path: '.env.local' });

async function setupLocalDatabase() {
  try {
    console.log('🔧 Setting up local development database...\n');

    // Connect to local MongoDB
    const mongoUri = process.env.MONGODB_URI || 'mongodb://localhost:27017/aixcelerate-dev';
    await mongoose.connect(mongoUri);
    console.log('✅ Connected to local MongoDB:', mongoUri);

    // Clear existing data
    console.log('\n🗑️ Clearing existing data...');
    await User.deleteMany({});
    await Product.deleteMany({});
    console.log('✅ Database cleared');

    // Create admin user
    console.log('\n👤 Creating admin user...');
    const adminUser = await User.create({
      name: 'Admin User',
      email: '<EMAIL>',
      password: 'Admin123!',
      role: 'admin',
      user_type: 'premium',
      isEmailVerified: true,
      purchased_products: []
    });
    console.log('✅ Admin user created:', adminUser.email);

    // Create test user
    console.log('\n👤 Creating test user...');
    const testUser = await User.create({
      name: 'Test User',
      email: '<EMAIL>',
      password: 'Test123!',
      role: 'user',
      user_type: 'free',
      isEmailVerified: true,
      purchased_products: []
    });
    console.log('✅ Test user created:', testUser.email);

    // Create products with Whop IDs
    console.log('\n📦 Creating products...');

    const products = [
      {
        name: 'AI Prompts Lead Magnet',
        slug: 'ai-prompts-lead-magnet',
        description: 'Free collection of AI prompts to boost productivity and streamline workflows',
        price: 0,
        product_type: 'lead_magnet',
        file_path: '/downloads/ai-prompts-lead-magnet.pdf',
        is_active: true,
        whop_product_id: 'free_ai_prompts_guide',
        whop_price_id: 'price_free_prompts',
        features: [
          '50+ AI prompts for productivity',
          'ChatGPT optimization tips',
          'Workflow automation guides',
          'Instant download'
        ]
      },
      {
        name: 'AI Productivity Master Guide',
        slug: 'ai-productivity-master-guide',
        description: 'Complete guide to mastering AI productivity tools for professionals and businesses',
        price: 27.99,
        product_type: 'basic',
        file_path: '/downloads/ai-productivity-master-guide.pdf',
        is_active: true,
        whop_product_id: 'ai_productivity_guide_27',
        whop_price_id: 'price_productivity_27',
        features: [
          'Comprehensive AI tool comparison',
          'Step-by-step implementation guides',
          'ROI calculation frameworks',
          'Expert interviews and case studies',
          'Lifetime updates'
        ]
      },
      {
        name: 'AI Business Revolution',
        slug: 'ai-business-revolution',
        description: 'Transform your business with AI automation and advanced productivity strategies',
        price: 47.99,
        product_type: 'premium',
        file_path: '/downloads/ai-business-revolution.pdf',
        is_active: true,
        whop_product_id: 'ai_business_revolution_47',
        whop_price_id: 'price_business_47',
        features: [
          'Complete business transformation framework',
          'AI implementation roadmap',
          'Team training materials',
          'Custom automation templates',
          '1-on-1 consultation call',
          'Private community access'
        ]
      }
    ];

    for (const productData of products) {
      const product = await Product.create(productData);
      console.log(`✅ Created product: ${product.name} ($${product.price})`);
      console.log(`   Whop Product ID: ${product.whop_product_id}`);
      console.log(`   Whop Price ID: ${product.whop_price_id}`);
    }

    console.log('\n📊 Database setup summary:');
    console.log(`👥 Users: ${await User.countDocuments()}`);
    console.log(`📦 Products: ${await Product.countDocuments()}`);

    console.log('\n🎉 Local database setup complete!');
    console.log('\n📋 Login credentials:');
    console.log('Admin: <EMAIL> / Admin123!');
    console.log('Test User: <EMAIL> / Test123!');

    console.log('\n🚀 Ready to start development server!');

  } catch (error) {
    console.error('❌ Database setup failed:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\n📤 Disconnected from MongoDB');
  }
}

// Run setup
setupLocalDatabase().catch(console.error);
