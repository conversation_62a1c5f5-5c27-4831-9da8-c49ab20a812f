const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

const questions = [
  {
    name: 'domain',
    question: 'Enter your domain name (e.g., example.com): ',
    required: true
  },
  {
    name: 'email',
    question: 'Enter your email address for Let\'s Encrypt notifications: ',
    required: true
  }
];

const answers = {};

const askQuestion = (index) => {
  if (index === questions.length) {
    setupSSL();
    return;
  }

  const question = questions[index];
  rl.question(question.question, (answer) => {
    if (question.required && !answer) {
      console.log('This field is required!');
      askQuestion(index);
      return;
    }
    answers[question.name] = answer;
    askQuestion(index + 1);
  });
};

const setupSSL = async () => {
  try {
    console.log('\nSetting up SSL certificates...');

    // Create certificates directory
    const certsDir = path.join(__dirname, '..', 'certs');
    if (!fs.existsSync(certsDir)) {
      fs.mkdirSync(certsDir, { recursive: true });
    }

    // Install certbot if not installed
    try {
      execSync('which certbot');
    } catch (error) {
      console.log('Installing certbot...');
      execSync('sudo apt-get update && sudo apt-get install -y certbot');
    }

    // Obtain certificate
    console.log('Obtaining SSL certificate...');
    execSync(`sudo certbot certonly --standalone -d ${answers.domain} -m ${answers.email} --agree-tos --non-interactive`);

    // Copy certificates to application directory
    console.log('Copying certificates...');
    const certPath = `/etc/letsencrypt/live/${answers.domain}`;
    execSync(`sudo cp ${certPath}/fullchain.pem ${certsDir}/fullchain.pem`);
    execSync(`sudo cp ${certPath}/privkey.pem ${certsDir}/privkey.pem`);
    execSync(`sudo chown -R $(whoami):$(whoami) ${certsDir}`);

    // Update .env file
    const envPath = path.join(__dirname, '..', '.env');
    let envContent = '';

    try {
      envContent = fs.readFileSync(envPath, 'utf8');
    } catch (error) {
      envContent = '';
    }

    // Update SSL configuration
    const sslConfig = `
# SSL Configuration
SSL_CERT_PATH=${path.join(certsDir, 'fullchain.pem')}
SSL_KEY_PATH=${path.join(certsDir, 'privkey.pem')}
DOMAIN=${answers.domain}
`;

    if (envContent.includes('SSL_CERT_PATH=')) {
      envContent = envContent.replace(/SSL_CERT_PATH=.*/, `SSL_CERT_PATH=${path.join(certsDir, 'fullchain.pem')}`);
      envContent = envContent.replace(/SSL_KEY_PATH=.*/, `SSL_KEY_PATH=${path.join(certsDir, 'privkey.pem')}`);
      envContent = envContent.replace(/DOMAIN=.*/, `DOMAIN=${answers.domain}`);
    } else {
      envContent += sslConfig;
    }

    fs.writeFileSync(envPath, envContent.trim() + '\n');

    console.log('\nSSL certificates have been set up successfully!');
    console.log('Certificates are stored in:', certsDir);
    console.log('\nTo renew certificates automatically, add this to your crontab:');
    console.log('0 0 1 * * certbot renew --quiet && cp /etc/letsencrypt/live/${answers.domain}/fullchain.pem ${certsDir}/fullchain.pem && cp /etc/letsencrypt/live/${answers.domain}/privkey.pem ${certsDir}/privkey.pem');

  } catch (error) {
    console.error('Error setting up SSL:', error.message);
  } finally {
    rl.close();
  }
};

console.log('SSL Certificate Setup\n');
askQuestion(0); 