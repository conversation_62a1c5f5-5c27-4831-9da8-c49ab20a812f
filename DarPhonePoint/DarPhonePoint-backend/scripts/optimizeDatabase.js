#!/usr/bin/env node

/**
 * Database Optimization Script for Phone Point Dar
 * 
 * This script optimizes the database by:
 * 1. Creating comprehensive indexes
 * 2. Analyzing query performance
 * 3. Cleaning up old data
 * 4. Optimizing collections
 */

const mongoose = require('mongoose');
const config = require('../config/config');
const logger = require('../utils/logger');
const databaseOptimizationService = require('../services/databaseOptimizationService');

// Import models to ensure they're registered
require('../models/User');
require('../models/Product');
require('../models/Order');
require('../models/Cart');
require('../models/Inventory');
require('../models/Wishlist');
require('../models/Analytics');
require('../models/TradeIn');
require('../models/Shipping');
require('../models/SerialNumber');
require('../models/AuditLog');
require('../models/Lead');
require('../models/Settings');

class DatabaseOptimizer {
  constructor() {
    this.optimizationService = databaseOptimizationService;
  }

  async connect() {
    try {
      await mongoose.connect(config.MONGODB_URI, {
        maxPoolSize: 10,
        minPoolSize: 5,
        serverSelectionTimeoutMS: 5000,
        socketTimeoutMS: 45000,
      });
      logger.info('Connected to MongoDB for optimization');
    } catch (error) {
      logger.error('Database connection failed:', error);
      process.exit(1);
    }
  }

  async disconnect() {
    try {
      await mongoose.connection.close();
      logger.info('Disconnected from MongoDB');
    } catch (error) {
      logger.error('Error disconnecting from MongoDB:', error);
    }
  }

  async analyzeCollections() {
    try {
      logger.info('Analyzing collection statistics...');
      
      const db = mongoose.connection.db;
      const collections = await db.listCollections().toArray();
      
      const stats = {};
      
      for (const collection of collections) {
        try {
          const collectionStats = await db.collection(collection.name).stats();
          stats[collection.name] = {
            documents: collectionStats.count,
            avgDocSize: Math.round(collectionStats.avgObjSize || 0),
            totalSize: Math.round(collectionStats.size / 1024 / 1024 * 100) / 100, // MB
            indexes: collectionStats.nindexes,
            indexSize: Math.round(collectionStats.totalIndexSize / 1024 / 1024 * 100) / 100 // MB
          };
        } catch (error) {
          logger.warn(`Could not get stats for collection ${collection.name}:`, error.message);
        }
      }
      
      logger.info('Collection Statistics:', stats);
      return stats;
    } catch (error) {
      logger.error('Error analyzing collections:', error);
      return {};
    }
  }

  async optimizeIndexes() {
    try {
      logger.info('Starting index optimization...');
      
      const result = await this.optimizationService.createOptimalIndexes();
      
      logger.info('Index optimization completed:', result);
      return result;
    } catch (error) {
      logger.error('Error optimizing indexes:', error);
      throw error;
    }
  }

  async cleanupOldData() {
    try {
      logger.info('Starting data cleanup...');
      
      const cleanupResults = {};
      
      // Clean up old analytics data (older than 6 months)
      const sixMonthsAgo = new Date();
      sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);
      
      try {
        const Analytics = mongoose.model('Analytics');
        const oldAnalytics = await Analytics.deleteMany({
          timestamp: { $lt: sixMonthsAgo }
        });
        cleanupResults.analytics = oldAnalytics.deletedCount;
        logger.info(`Cleaned up ${oldAnalytics.deletedCount} old analytics records`);
      } catch (error) {
        logger.warn('Analytics cleanup failed:', error.message);
      }

      // Clean up expired cart sessions (older than 30 days)
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
      
      try {
        const Cart = mongoose.model('Cart');
        const expiredCarts = await Cart.deleteMany({
          user: { $exists: false },
          updated_at: { $lt: thirtyDaysAgo }
        });
        cleanupResults.carts = expiredCarts.deletedCount;
        logger.info(`Cleaned up ${expiredCarts.deletedCount} expired guest carts`);
      } catch (error) {
        logger.warn('Cart cleanup failed:', error.message);
      }

      // Clean up old audit logs (older than 1 year)
      const oneYearAgo = new Date();
      oneYearAgo.setFullYear(oneYearAgo.getFullYear() - 1);
      
      try {
        const AuditLog = mongoose.model('AuditLog');
        const oldLogs = await AuditLog.deleteMany({
          timestamp: { $lt: oneYearAgo }
        });
        cleanupResults.auditLogs = oldLogs.deletedCount;
        logger.info(`Cleaned up ${oldLogs.deletedCount} old audit logs`);
      } catch (error) {
        logger.warn('Audit log cleanup failed:', error.message);
      }

      logger.info('Data cleanup completed:', cleanupResults);
      return cleanupResults;
    } catch (error) {
      logger.error('Error during data cleanup:', error);
      throw error;
    }
  }

  async validateDataIntegrity() {
    try {
      logger.info('Validating data integrity...');
      
      const issues = [];
      
      // Check for orphaned cart items
      try {
        const Cart = mongoose.model('Cart');
        const Product = mongoose.model('Product');
        
        const carts = await Cart.find({}).populate('items.product');
        let orphanedItems = 0;
        
        for (const cart of carts) {
          const validItems = cart.items.filter(item => item.product);
          if (validItems.length !== cart.items.length) {
            orphanedItems += cart.items.length - validItems.length;
            cart.items = validItems;
            await cart.save();
          }
        }
        
        if (orphanedItems > 0) {
          issues.push(`Fixed ${orphanedItems} orphaned cart items`);
        }
      } catch (error) {
        logger.warn('Cart validation failed:', error.message);
      }

      // Check for duplicate IMEI numbers
      try {
        const Inventory = mongoose.model('Inventory');
        
        const duplicateIMEIs = await Inventory.aggregate([
          { $unwind: '$devices' },
          { $match: { 'devices.imei': { $exists: true, $ne: null } } },
          { $group: { _id: '$devices.imei', count: { $sum: 1 } } },
          { $match: { count: { $gt: 1 } } }
        ]);
        
        if (duplicateIMEIs.length > 0) {
          issues.push(`Found ${duplicateIMEIs.length} duplicate IMEI numbers`);
          logger.warn('Duplicate IMEIs found:', duplicateIMEIs.map(d => d._id));
        }
      } catch (error) {
        logger.warn('IMEI validation failed:', error.message);
      }

      // Check for orders without valid products
      try {
        const Order = mongoose.model('Order');
        
        const ordersWithInvalidProducts = await Order.find({
          'items.product': { $exists: false }
        });
        
        if (ordersWithInvalidProducts.length > 0) {
          issues.push(`Found ${ordersWithInvalidProducts.length} orders with invalid products`);
        }
      } catch (error) {
        logger.warn('Order validation failed:', error.message);
      }

      if (issues.length === 0) {
        logger.info('Data integrity validation passed - no issues found');
      } else {
        logger.warn('Data integrity issues found:', issues);
      }
      
      return issues;
    } catch (error) {
      logger.error('Error validating data integrity:', error);
      throw error;
    }
  }

  async generateOptimizationReport() {
    try {
      const report = {
        timestamp: new Date().toISOString(),
        database: config.MONGODB_URI.split('/').pop(),
        collections: await this.analyzeCollections(),
        optimization: {
          indexesCreated: 0,
          dataCleanedUp: {},
          integrityIssues: []
        }
      };

      return report;
    } catch (error) {
      logger.error('Error generating optimization report:', error);
      throw error;
    }
  }

  /**
   * Phone Point Dar specific optimizations
   */
  async optimizePhonePointDarSpecific() {
    try {
      logger.info('📱 Optimizing Phone Point Dar specific collections...');

      // Optimize IMEI tracking
      await this.optimizeIMEITracking();

      // Optimize warranty queries
      await this.optimizeWarrantyQueries();

      // Optimize inventory management
      await this.optimizeInventoryManagement();

      // Optimize order processing
      await this.optimizeOrderProcessing();

      logger.info('✅ Phone Point Dar specific optimizations completed');
    } catch (error) {
      logger.error('❌ Phone Point Dar optimization failed:', error);
      throw error;
    }
  }

  async optimizeIMEITracking() {
    const db = mongoose.connection.db;
    const collection = db.collection('serialnumbers');

    // IMEI-specific indexes
    await collection.createIndex({ imei: 1 }, { unique: true });
    await collection.createIndex({ product: 1, status: 1 });
    await collection.createIndex({ customer: 1, warrantyStatus: 1 });
    await collection.createIndex({ warrantyEndDate: 1 }, { sparse: true });

    logger.info('IMEI tracking optimized');
  }

  async optimizeWarrantyQueries() {
    const db = mongoose.connection.db;
    const collection = db.collection('serialnumbers');

    // Warranty-specific compound indexes
    await collection.createIndex({
      warrantyStatus: 1,
      warrantyEndDate: 1,
      customer: 1
    });

    await collection.createIndex({
      customer: 1,
      warrantyStatus: 1,
      product: 1
    });

    logger.info('Warranty queries optimized');
  }

  async optimizeInventoryManagement() {
    const db = mongoose.connection.db;
    const collection = db.collection('inventory');

    // Inventory-specific indexes
    await collection.createIndex({ product: 1, location: 1 }, { unique: true });
    await collection.createIndex({ quantity_available: 1, reorder_level: 1 });
    await collection.createIndex({ location: 1, quantity_available: 1 });

    logger.info('Inventory management optimized');
  }

  async optimizeOrderProcessing() {
    const db = mongoose.connection.db;
    const collection = db.collection('orders');

    // Order processing indexes
    await collection.createIndex({ user: 1, order_status: 1 });
    await collection.createIndex({ order_status: 1, createdAt: -1 });
    await collection.createIndex({ payment_status: 1, order_status: 1 });
    await collection.createIndex({ transaction_id: 1 }, { sparse: true });

    logger.info('Order processing optimized');
  }

  async run() {
    try {
      logger.info('🚀 Starting Phone Point Dar Database Optimization...');
      
      await this.connect();
      
      // Generate initial report
      const initialReport = await this.generateOptimizationReport();
      logger.info('Initial database state:', initialReport.collections);
      
      // Step 1: Optimize indexes
      logger.info('📊 Step 1: Optimizing database indexes...');
      const indexResult = await this.optimizeIndexes();

      // Step 1.5: Phone Point Dar specific optimizations
      logger.info('📱 Step 1.5: Phone Point Dar specific optimizations...');
      await this.optimizePhonePointDarSpecific();
      
      // Step 2: Clean up old data
      logger.info('🧹 Step 2: Cleaning up old data...');
      const cleanupResult = await this.cleanupOldData();
      
      // Step 3: Validate data integrity
      logger.info('🔍 Step 3: Validating data integrity...');
      const integrityIssues = await this.validateDataIntegrity();
      
      // Step 4: Generate final report
      logger.info('📋 Step 4: Generating optimization report...');
      const finalReport = await this.generateOptimizationReport();
      finalReport.optimization = {
        indexesCreated: indexResult.indexesCreated || 0,
        dataCleanedUp: cleanupResult,
        integrityIssues
      };
      
      logger.info('✅ Database optimization completed successfully!');
      logger.info('Final Report:', finalReport);
      
      await this.disconnect();
      
      return finalReport;
    } catch (error) {
      logger.error('❌ Database optimization failed:', error);
      await this.disconnect();
      process.exit(1);
    }
  }
}

// Run optimization if called directly
if (require.main === module) {
  const optimizer = new DatabaseOptimizer();
  optimizer.run()
    .then(() => {
      logger.info('Database optimization script completed');
      process.exit(0);
    })
    .catch((error) => {
      logger.error('Database optimization script failed:', error);
      process.exit(1);
    });
}

module.exports = DatabaseOptimizer;
