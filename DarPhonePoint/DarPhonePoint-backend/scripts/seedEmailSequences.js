/**
 * Seed script to create pre-built email sequences
 * Run with: node scripts/seedEmailSequences.js
 */

const mongoose = require('mongoose');
const dotenv = require('dotenv');
const EmailSequence = require('../models/EmailSequence');
const User = require('../models/User');

// Load environment variables
dotenv.config();

// Connect to MongoDB
mongoose.connect(process.env.MONGO_URI || 'mongodb://localhost:27017/aixcelerate', {
  useNewUrlParser: true,
  useUnifiedTopology: true
});

// Pre-built email sequences optimized for AIXcelerate
const emailSequences = [
  // Lead Nurturing Sequence (triggered by free product download)
  {
    name: 'AI Productivity Lead Nurturing',
    description: 'Nurture leads who downloaded free AI prompts and guide them to paid products',
    trigger: 'lead_capture',
    isActive: true,
    emails: [
      {
        subject: 'Your AI Prompts are here! Plus, what\'s next... 🚀',
        body: `<p>Hi {{firstName}},</p>
<p>Thanks for downloading our <strong>AI Prompts Lead Magnet</strong>! You should have received it in your inbox as a PDF attachment.</p>
<p>These prompts are just the beginning of your AI productivity journey. Here's what successful users do next:</p>
<ul>
  <li><strong>Start with 3 prompts</strong> - Pick the ones most relevant to your daily tasks</li>
  <li><strong>Test and iterate</strong> - Modify the prompts to fit your specific needs</li>
  <li><strong>Track your time savings</strong> - Most users save 2-3 hours per week immediately</li>
</ul>
<p>🎯 <strong>Pro Tip:</strong> The prompts in Section 3 (Content Creation) typically deliver the fastest results.</p>
<p>Questions about implementing these prompts? Just reply to this email!</p>`,
        delayDays: 0,
        order: 1,
        isActive: true
      },
      {
        subject: 'How are those AI prompts working for you? 🤔',
        body: `<p>Hi {{firstName}},</p>
<p>It's been a couple of days since you downloaded our AI Prompts Lead Magnet. How's it going?</p>
<p>I'm curious - have you tried any of the prompts yet? Most people start with the email writing or content creation ones since they deliver immediate results.</p>
<p><strong>Quick Success Story:</strong> Sarah, a marketing manager, used Prompt #23 (Content Brainstorming) and generated 15 blog post ideas in under 10 minutes. That used to take her 2 hours!</p>
<p>If you haven't started yet, here's my recommendation:</p>
<ol>
  <li>Pick ONE prompt that matches a task you do regularly</li>
  <li>Try it with a real project (not just a test)</li>
  <li>Note the time you save</li>
</ol>
<p>Ready to take your AI productivity to the next level? Check out our <a href="{{siteUrl}}/products/ai-productivity-master-guide">AI Productivity Master Guide</a> - it's like having 100+ advanced prompts plus the strategies to use them effectively.</p>`,
        delayDays: 3,
        order: 2,
        isActive: true
      },
      {
        subject: 'The #1 mistake people make with AI prompts (and how to fix it)',
        body: `<p>Hi {{firstName}},</p>
<p>After helping thousands of people implement AI in their workflows, I've noticed the #1 mistake that prevents people from getting great results:</p>
<p><strong>They use prompts exactly as written, without customizing them.</strong></p>
<p>Here's the thing - the best prompts are templates, not scripts. You need to adapt them to your specific situation, industry, and style.</p>
<p>For example, instead of using:</p>
<p><em>"Write a professional email"</em></p>
<p>Try this:</p>
<p><em>"Write a professional email to a potential client in the [YOUR INDUSTRY] industry, explaining [SPECIFIC BENEFIT] in a friendly but authoritative tone, keeping it under 150 words."</em></p>
<p>See the difference? The second version gives you much better, more targeted results.</p>
<p>Want to master this skill? Our <a href="{{siteUrl}}/products/ai-productivity-master-guide">AI Productivity Master Guide</a> includes 50+ customizable prompt templates plus the exact framework for adapting any prompt to your needs.</p>`,
        delayDays: 7,
        order: 3,
        isActive: true
      },
      {
        subject: 'Last chance: Your AI productivity transformation awaits',
        body: `<p>Hi {{firstName}},</p>
<p>I've been thinking about your AI productivity journey, and I wanted to reach out one more time.</p>
<p>You downloaded our AI Prompts Lead Magnet, which tells me you're serious about using AI to boost your productivity. That's awesome!</p>
<p>But here's what I've learned from working with thousands of people: <strong>The difference between those who succeed and those who don't isn't talent or time - it's having the right system.</strong></p>
<p>The free prompts are a great start, but they're just the tip of the iceberg. Our <a href="{{siteUrl}}/products/ai-productivity-master-guide">AI Productivity Master Guide</a> gives you the complete system:</p>
<ul>
  <li>✅ 50+ advanced prompts for every business situation</li>
  <li>✅ The exact framework for customizing any prompt</li>
  <li>✅ Step-by-step workflows that save 5+ hours per week</li>
  <li>✅ Real case studies from successful implementations</li>
</ul>
<p>At just $27.99, it's less than what most people spend on lunch for a week, but it could transform how you work forever.</p>
<p><a href="{{siteUrl}}/products/ai-productivity-master-guide">Get the AI Productivity Master Guide now</a></p>
<p>Your future, more productive self will thank you.</p>`,
        delayDays: 14,
        order: 4,
        isActive: true
      }
    ]
  },
  // Post-Purchase Success Sequence
  {
    name: 'Post-Purchase Success Sequence',
    description: 'Help customers get maximum value from their AIXcelerate purchase',
    trigger: 'purchase',
    isActive: true,
    emails: [
      {
        subject: '🎉 Welcome to the AIXcelerate family! Your {{productName}} is ready',
        body: `<p>Hi {{firstName}},</p>
<p>Congratulations on your purchase of <strong>{{productName}}</strong>! You've just taken a huge step toward transforming your productivity with AI.</p>
<p><strong>Here's what happens next:</strong></p>
<ol>
  <li>📥 <strong>Access your purchase</strong> - Log into your <a href="{{siteUrl}}/dashboard">AIXcelerate dashboard</a> to download your materials</li>
  <li>📖 <strong>Start with the Quick Start Guide</strong> - This will help you get results in the first 24 hours</li>
  <li>⚡ <strong>Implement one strategy today</strong> - Pick the easiest one that applies to your work</li>
</ol>
<p><strong>💡 Pro Tip:</strong> The most successful customers implement strategies gradually. Start with 1-2 techniques this week, then add more as they become habits.</p>
<p>Questions? Just reply to this email - I personally read and respond to every message.</p>
<p>Excited to see what you accomplish!</p>`,
        delayDays: 0,
        order: 1,
        isActive: true
      },
      {
        subject: 'Day 2: Your first AI productivity win is waiting... 🚀',
        body: `<p>Hi {{firstName}},</p>
<p>How did your first day with {{productName}} go? I hope you had a chance to explore the materials!</p>
<p>If you haven't started yet, no worries - here's the fastest way to get your first win:</p>
<p><strong>🎯 The 15-Minute Challenge:</strong></p>
<ol>
  <li>Open {{productName}} and go to the Quick Wins section</li>
  <li>Pick ONE strategy that matches something you do regularly</li>
  <li>Implement it with your next task (don't wait for the "perfect" moment)</li>
  <li>Time yourself and note the difference</li>
</ol>
<p>Most people are amazed at how much time they save on their very first try!</p>
<p><strong>Already started?</strong> I'd love to hear about your experience! Reply and tell me which strategy you tried and how it went.</p>
<p>Remember: The goal isn't perfection, it's progress. Every small improvement compounds over time.</p>`,
        delayDays: 1,
        order: 2,
        isActive: true
      },
      {
        subject: 'Week 1 check-in: How are you doing with {{productName}}? 📈',
        body: `<p>Hi {{firstName}},</p>
<p>It's been a week since you got {{productName}}, and I wanted to check in on your progress!</p>
<p>By now, you've probably tried a few strategies. Here's what I typically see at the one-week mark:</p>
<p>✅ <strong>Quick wins</strong> - Most people have saved 2-3 hours already<br>
✅ <strong>Favorite strategies</strong> - You've found 1-2 techniques that really work for you<br>
✅ <strong>Questions</strong> - You're wondering how to adapt certain strategies to your specific situation</p>
<p>If you're stuck on anything or want to share a success story, just reply to this email! I love hearing about wins, big and small.</p>
<p><strong>Pro tip for week 2:</strong> Focus on making your favorite strategies into habits. Consistency beats perfection every time.</p>
<p>Keep up the great work!</p>`,
        delayDays: 7,
        order: 3,
        isActive: true
      },
      {
        subject: 'Ready for the next level? Here\'s your upgrade path 🚀',
        body: `<p>Hi {{firstName}},</p>
<p>You've been using {{productName}} for two weeks now, and I hope you're seeing some great results!</p>
<p>If you're ready to take your AI productivity to the next level, I wanted to let you know about our <strong>AI Business Revolution Blueprint</strong>.</p>
<p>While {{productName}} focuses on personal productivity, the Blueprint shows you how to implement AI across entire business operations:</p>
<ul>
  <li>🏢 <strong>Team AI workflows</strong> - Scale your productivity gains across your whole organization</li>
  <li>📊 <strong>AI automation systems</strong> - Set up processes that run themselves</li>
  <li>💰 <strong>ROI tracking</strong> - Measure and optimize your AI investments</li>
  <li>📋 <strong>Implementation roadmaps</strong> - Step-by-step plans for different business types</li>
</ul>
<p>As a {{productName}} customer, you can get the Blueprint for just $47.99 (normally $97).</p>
<p><a href="{{siteUrl}}/products/ai-business-revolution">Learn more about the AI Business Revolution Blueprint</a></p>
<p>This is perfect if you want to help your team or business benefit from AI like you have.</p>`,
        delayDays: 14,
        order: 4,
        isActive: true
      }
    ]
  },
  // User Re-engagement Sequence
  {
    name: 'User Re-engagement Sequence',
    description: 'Re-engage users who haven\'t been active on the platform',
    trigger: 'user_inactive',
    isActive: true,
    emails: [
      {
        subject: 'We miss you! Here\'s what you\'ve been missing at AIXcelerate 👋',
        body: `<p>Hi {{firstName}},</p>
<p>I noticed it's been a while since you last visited AIXcelerate, and I wanted to reach out personally.</p>
<p>Life gets busy, I get it. But I didn't want you to miss out on the progress you could be making with AI productivity.</p>
<p><strong>Here's what's new since your last visit:</strong></p>
<ul>
  <li>🆕 <strong>Updated AI Prompts</strong> - We've added 25+ new prompts based on user feedback</li>
  <li>📊 <strong>Enhanced Dashboard</strong> - Easier access to your downloads and progress tracking</li>
  <li>🎯 <strong>Quick Start Guides</strong> - Get results in 15 minutes or less</li>
</ul>
<p>Your account is still active and waiting for you. <a href="{{siteUrl}}/dashboard">Click here to log back in</a> and see what's new.</p>
<p>Is there anything specific that's been holding you back? Just reply to this email - I read every response personally.</p>`,
        delayDays: 0,
        order: 1,
        isActive: true
      },
      {
        subject: 'Quick question: What\'s your biggest productivity challenge right now?',
        body: `<p>Hi {{firstName}},</p>
<p>I wanted to follow up on my last email and ask you a direct question:</p>
<p><strong>What's your biggest productivity challenge right now?</strong></p>
<p>Is it:</p>
<ul>
  <li>📧 Email overload and communication management?</li>
  <li>📝 Content creation taking too much time?</li>
  <li>📊 Data analysis and reporting?</li>
  <li>🎯 Staying focused and avoiding distractions?</li>
  <li>⏰ Time management and prioritization?</li>
  <li>🤖 Not knowing which AI tools to use?</li>
</ul>
<p>Whatever it is, I bet we have a solution that can help. Our AI productivity resources are specifically designed to tackle these exact challenges.</p>
<p>Reply to this email and let me know what you're struggling with most. I'll personally send you the best resources we have for that specific problem.</p>
<p>Looking forward to helping you break through!</p>`,
        delayDays: 7,
        order: 2,
        isActive: true
      },
      {
        subject: 'Last chance: Your AI productivity transformation is waiting',
        body: `<p>Hi {{firstName}},</p>
<p>This is my final email in this series, and I wanted to make it count.</p>
<p>I've been helping people implement AI productivity solutions for years, and here's what I've learned:</p>
<p><strong>The people who succeed aren't necessarily the smartest or most tech-savvy. They're the ones who take action.</strong></p>
<p>You signed up for AIXcelerate because you wanted to improve your productivity with AI. That goal is still achievable - you just need to take the first step.</p>
<p>Here's what I suggest:</p>
<ol>
  <li><a href="{{siteUrl}}/dashboard">Log into your account</a> right now (it takes 30 seconds)</li>
  <li>Download our free AI Prompts if you haven't already</li>
  <li>Try ONE prompt with your next task</li>
</ol>
<p>That's it. No big commitment, no pressure. Just one small action that could change how you work forever.</p>
<p>If you're ready for more advanced strategies, check out our <a href="{{siteUrl}}/products/ai-productivity-master-guide">AI Productivity Master Guide</a> - it's helped thousands of people save 5+ hours per week.</p>
<p>Your future, more productive self is waiting. What are you going to do about it?</p>`,
        delayDays: 14,
        order: 3,
        isActive: true
      }
    ]
  }
];

// Seed email sequences
const seedEmailSequences = async () => {
  try {
    // Find admin user
    const adminUser = await User.findOne({ email: '<EMAIL>' });

    if (!adminUser) {
      console.error('Admin user not found. Please create an admin user first.');
      process.exit(1);
    }

    // Clear existing sequences
    await EmailSequence.deleteMany({});

    // Add admin user as creator
    const sequencesWithCreator = emailSequences.map(sequence => ({
      ...sequence,
      created_by: adminUser._id
    }));

    // Insert sequences
    await EmailSequence.insertMany(sequencesWithCreator);

    console.log(`Created ${sequencesWithCreator.length} email sequences.`);
    process.exit(0);
  } catch (error) {
    console.error('Error seeding email sequences:', error);
    process.exit(1);
  }
};

// Run the seed function
seedEmailSequences();
