const mongoose = require('mongoose');
const User = require('../models/User');
const config = require('../config/config');
const bcrypt = require('bcryptjs');

// Connect to MongoDB
mongoose.connect(config.MONGODB_URI)
  .then(() => console.log('MongoDB Connected'))
  .catch(err => {
    console.error('MongoDB Connection Error:', err);
    process.exit(1);
  });

// Create admin user
const createAdminUser = async () => {
  try {
    // Check if admin user already exists
    const existingAdmin = await User.findOne({ email: '<EMAIL>' });

    if (existingAdmin) {
      console.log('Admin user already exists:', existingAdmin.email);
      mongoose.disconnect();
      return;
    }

    // Create new admin user (password will be hashed by the model's pre-save hook)
    const adminUser = new User({
      name: 'Admin User',
      email: '<EMAIL>',
      password: 'Admin123!@#',
      role: 'admin',
      user_type: 'premium',
      status: 'active',
      isEmailVerified: true // Admin users created via scripts are automatically verified
    });

    await adminUser.save();

    console.log('Admin user created successfully:', adminUser.email);
    console.log('Password: Admin123!@#');
    console.log('Role: admin');
    console.log('User Type: premium');
    console.log('Status: active');

    // Create a regular user as well
    const regularUser = new User({
      name: 'Regular User',
      email: '<EMAIL>',
      password: 'User123!',
      role: 'user'
    });

    await regularUser.save();

    console.log('Regular user created successfully:', regularUser.email);

    mongoose.disconnect();
  } catch (err) {
    console.error('Error creating users:', err);
    mongoose.disconnect();
    process.exit(1);
  }
};

createAdminUser();
