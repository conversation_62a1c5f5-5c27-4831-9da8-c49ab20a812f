const fs = require('fs');
const path = require('path');
const logger = require('../utils/logger');

/**
 * Create upload directories for local file storage
 */
function createUploadDirectories() {
  const baseUploadPath = process.env.UPLOAD_PATH || './uploads';
  
  const directories = [
    baseUploadPath,
    path.join(baseUploadPath, 'products'),
    path.join(baseUploadPath, 'user-uploads'),
    path.join(baseUploadPath, 'temp'),
    path.join(baseUploadPath, 'backups')
  ];

  directories.forEach(dir => {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
      logger.info(`Created directory: ${dir}`);
    } else {
      logger.info(`Directory already exists: ${dir}`);
    }
  });

  // Create .gitkeep files to ensure directories are tracked in git
  directories.forEach(dir => {
    const gitkeepPath = path.join(dir, '.gitkeep');
    if (!fs.existsSync(gitkeepPath)) {
      fs.writeFileSync(gitkeepPath, '');
      logger.info(`Created .gitkeep in: ${dir}`);
    }
  });

  logger.info('Upload directories setup completed');
}

// Run if called directly
if (require.main === module) {
  createUploadDirectories();
}

module.exports = { createUploadDirectories };
