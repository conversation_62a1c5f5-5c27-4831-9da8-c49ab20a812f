/**
 * Seed Admin and Test Users
 * Creates admin user and test customer for Phone Point Dar
 */

const mongoose = require('mongoose');
const User = require('../models/User');
const config = require('../config/config');

// Sample users to create
const sampleUsers = [
  {
    name: 'Admin User',
    email: '<EMAIL>',
    password: 'Admin123!',
    role: 'admin',
    phone: '+255123456789',
    user_type: 'premium',
    shipping_addresses: [{
      first_name: 'Admin',
      last_name: 'User',
      company: 'Phone Point Dar',
      address_line_1: 'Msimbazi Street',
      address_line_2: 'Kariakoo',
      city: 'Dar es Salaam',
      state: 'Dar es Salaam',
      postal_code: '11101',
      country: 'Tanzania',
      phone: '+255123456789',
      is_default: true
    }],
    isEmailVerified: true,
    is_active: true
  },
  {
    name: '<PERSON>',
    email: '<EMAIL>',
    password: 'Customer123!',
    role: 'user',
    phone: '+255987654321',
    user_type: 'free',
    shipping_addresses: [{
      first_name: '<PERSON>',
      last_name: '<PERSON><PERSON><PERSON><PERSON>',
      company: '',
      address_line_1: 'Uhuru Street',
      address_line_2: 'Upanga',
      city: 'Dar es Salaam',
      state: 'Dar es Salaam',
      postal_code: '11103',
      country: 'Tanzania',
      phone: '+255987654321',
      is_default: true
    }],
    isEmailVerified: true,
    is_active: true
  },
  {
    name: 'Fatuma Hassan',
    email: '<EMAIL>',
    password: 'Customer456!',
    role: 'user',
    phone: '+255765432109',
    user_type: 'basic',
    shipping_addresses: [{
      first_name: 'Fatuma',
      last_name: 'Hassan',
      company: '',
      address_line_1: 'Morogoro Road',
      address_line_2: 'Temeke',
      city: 'Dar es Salaam',
      state: 'Dar es Salaam',
      postal_code: '11105',
      country: 'Tanzania',
      phone: '+255765432109',
      is_default: true
    }],
    isEmailVerified: true,
    is_active: true
  }
];

async function seedUsers() {
  try {
    // Connect to MongoDB
    await mongoose.connect(config.MONGODB_URI);
    console.log('Connected to MongoDB');

    // Check if users already exist
    const existingUsers = await User.countDocuments();
    if (existingUsers > 0) {
      console.log(`${existingUsers} users already exist. Clearing existing users...`);
      await User.deleteMany({});
      console.log('Cleared existing users');
    }

    // Create users
    const createdUsers = [];
    for (const userData of sampleUsers) {
      try {
        const user = await User.create(userData);
        createdUsers.push({
          name: user.name,
          email: user.email,
          role: user.role,
          id: user._id
        });
        console.log(`✅ Created ${user.role}: ${user.name} (${user.email})`);
      } catch (error) {
        console.error(`❌ Failed to create user ${userData.email}:`, error.message);
      }
    }

    console.log('\n🎉 User seeding completed!');
    console.log('\n📋 Created Users:');
    createdUsers.forEach(user => {
      console.log(`- ${user.role.toUpperCase()}: ${user.name} (${user.email})`);
    });

    console.log('\n🔑 Login Credentials:');
    console.log('👨‍💼 Admin Login:');
    console.log('   Email: <EMAIL>');
    console.log('   Password: Admin123!');
    console.log('\n👤 Test Customer 1:');
    console.log('   Email: <EMAIL>');
    console.log('   Password: Customer123!');
    console.log('\n👤 Test Customer 2:');
    console.log('   Email: <EMAIL>');
    console.log('   Password: Customer456!');

    process.exit(0);
  } catch (error) {
    console.error('❌ Error seeding users:', error);
    process.exit(1);
  }
}

// Run the seeding function
if (require.main === module) {
  seedUsers();
}

module.exports = seedUsers;
