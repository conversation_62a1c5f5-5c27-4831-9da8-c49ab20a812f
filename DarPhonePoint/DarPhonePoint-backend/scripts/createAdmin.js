const mongoose = require('mongoose');
const config = require('../config/config');
const User = require('../models/User');
const bcrypt = require('bcrypt');

// Connect to MongoDB
mongoose.connect(config.MONGODB_URI, {
  useNewUrlParser: true,
  useUnifiedTopology: true
})
.then(() => console.log('MongoDB Connected...'))
.catch(err => {
  console.error('MongoDB connection error:', err);
  process.exit(1);
});

// Create admin user
const createAdmin = async () => {
  try {
    // Check if admin already exists
    const adminExists = await User.findOne({ email: '<EMAIL>' });
    
    if (adminExists) {
      console.log('Admin user already exists');
      mongoose.disconnect();
      return;
    }
    
    // Generate password hash
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash('admin123', salt);
    
    // Create admin user
    const admin = new User({
      name: 'Admin User',
      email: '<EMAIL>',
      password: hashedPassword,
      user_type: 'premium',
      role: 'admin',
      status: 'active',
      isEmailVerified: true // Admin users created via scripts are automatically verified
    });
    
    await admin.save();
    console.log('Admin user created successfully');
    console.log('Email: <EMAIL>');
    console.log('Password: admin123');
    console.log('Please change this password after first login!');
    
    mongoose.disconnect();
  } catch (error) {
    console.error('Error creating admin user:', error);
    mongoose.disconnect();
  }
};

// Run the function
createAdmin();