const readline = require('readline');
const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

const questions = [
  {
    name: 'NODE_ENV',
    question: 'Enter environment (development/production): ',
    default: 'development'
  },
  {
    name: 'PORT',
    question: 'Enter port number (default: 3000): ',
    default: '3000'
  },
  {
    name: 'MONGODB_URI',
    question: 'Enter MongoDB URI (default: mongodb://localhost:27017/aixcelerate): ',
    default: 'mongodb://localhost:27017/aixcelerate'
  },
  {
    name: 'JWT_SECRET',
    question: 'Enter JWT secret (press enter to generate): ',
    default: crypto.randomBytes(32).toString('hex')
  },
  {
    name: 'SMTP_HOST',
    question: 'Enter SMTP host: ',
    required: true
  },
  {
    name: '<PERSON>TP_USER',
    question: 'Enter SMTP username: ',
    required: true
  },
  {
    name: 'SMTP_PASSWORD',
    question: 'Enter SMTP password: ',
    required: true
  },
  {
    name: 'AWS_ACCESS_KEY_ID',
    question: 'Enter AWS Access Key ID: ',
    required: true
  },
  {
    name: 'AWS_SECRET_ACCESS_KEY',
    question: 'Enter AWS Secret Access Key: ',
    required: true
  },
  {
    name: 'AWS_REGION',
    question: 'Enter AWS Region (default: us-east-1): ',
    default: 'us-east-1'
  },
  {
    name: 'AWS_S3_BUCKET',
    question: 'Enter AWS S3 Bucket name: ',
    required: true
  }
];

const answers = {};

const askQuestion = (index) => {
  if (index === questions.length) {
    generateEnvFile();
    return;
  }

  const question = questions[index];
  rl.question(question.question, (answer) => {
    if (question.required && !answer && !question.default) {
      console.log('This field is required!');
      askQuestion(index);
      return;
    }
    answers[question.name] = answer || question.default;
    askQuestion(index + 1);
  });
};

const generateEnvFile = () => {
  const envPath = path.join(__dirname, '..', '.env');
  let envContent = '';

  // Add all environment variables
  Object.entries(answers).forEach(([key, value]) => {
    envContent += `${key}=${value}\n`;
  });

  // Add additional configuration
  envContent += `
# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379

# Security
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX=100

# Logging
LOG_LEVEL=info
`;

  fs.writeFileSync(envPath, envContent.trim() + '\n');
  console.log('\nEnvironment variables have been set up in .env file');
  rl.close();
};

console.log('Environment Setup\n');
askQuestion(0); 