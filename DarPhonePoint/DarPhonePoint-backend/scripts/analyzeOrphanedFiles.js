const mongoose = require('mongoose');
const Product = require('../models/Product');
const fs = require('fs');
const path = require('path');

async function analyzeOrphanedFiles() {
  try {
    await mongoose.connect('mongodb://localhost:27017/aixcelerate');
    console.log('Connected to MongoDB');
    
    const products = await Product.find({ is_active: true }).select('file_path');
    const dbFilePaths = products.map(p => p.file_path).filter(Boolean);
    
    // Get all PDF files in the directory
    const uploadsDir = path.join(__dirname, '../public/uploads/products');
    const allPdfFiles = fs.readdirSync(uploadsDir)
      .filter(file => file.endsWith('.pdf'))
      .map(file => `/uploads/products/${file}`);
    
    console.log('\n📋 DATABASE REFERENCED FILES:');
    dbFilePaths.forEach(filePath => {
      console.log(`   ${filePath}`);
    });
    
    console.log('\n📁 ALL PDF FILES IN DIRECTORY:');
    allPdfFiles.forEach(filePath => {
      console.log(`   ${filePath}`);
    });
    
    // Find orphaned files
    const orphanedFiles = allPdfFiles.filter(file => !dbFilePaths.includes(file));
    
    console.log('\n🗑️  ORPHANED FILES (not referenced in database):');
    if (orphanedFiles.length === 0) {
      console.log('   None found');
    } else {
      orphanedFiles.forEach(file => {
        const fullPath = path.join(__dirname, '../public', file);
        const stats = fs.statSync(fullPath);
        console.log(`   ${file} - ${stats.size} bytes - ${stats.mtime.toDateString()}`);
      });
    }
    
    console.log(`\n📊 SUMMARY:`);
    console.log(`   Total PDF files: ${allPdfFiles.length}`);
    console.log(`   Referenced in DB: ${dbFilePaths.length}`);
    console.log(`   Orphaned files: ${orphanedFiles.length}`);
    
    // Export orphaned files list for cleanup
    if (orphanedFiles.length > 0) {
      console.log('\n📝 ORPHANED FILES TO REMOVE:');
      orphanedFiles.forEach(file => console.log(file));
    }
    
    await mongoose.disconnect();
    return { orphanedFiles, dbFilePaths, allPdfFiles };
  } catch (error) {
    console.error('Error:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  analyzeOrphanedFiles();
}

module.exports = analyzeOrphanedFiles;
