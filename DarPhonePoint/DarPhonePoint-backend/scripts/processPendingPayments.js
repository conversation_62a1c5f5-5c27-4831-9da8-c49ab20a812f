#!/usr/bin/env node

/**
 * Process Pending Payments Script
 * Handles payment verification, order completion, and cleanup
 * Part of the AIXcelerate automated payment processing system
 */

const mongoose = require('mongoose');
const logger = require('../utils/logger');
const Order = require('../models/Order');
const User = require('../models/User');
const Product = require('../models/Product');
// const whopService = require('../services/whopService'); // Removed - Phone Point Dar uses local payment methods
const emailService = require('../services/emailService');

// Database connection
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    logger.info('Database connected for payment processing');
  } catch (error) {
    logger.error('Database connection failed:', error);
    process.exit(1);
  }
};

// Process pending payments
const processPendingPayments = async () => {
  try {
    logger.info('Starting pending payments processing...');

    // Find orders with pending payment status older than 30 minutes
    const thirtyMinutesAgo = new Date(Date.now() - 30 * 60 * 1000);

    const pendingOrders = await Order.find({
      status: 'pending',
      createdAt: { $lt: thirtyMinutesAgo }
    }).populate('user').populate('items.product');

    logger.info(`Found ${pendingOrders.length} pending orders to process`);

    for (const order of pendingOrders) {
      try {
        await processOrder(order);
      } catch (error) {
        logger.error(`Failed to process order ${order._id}:`, error);
      }
    }

    // Clean up abandoned carts older than 24 hours
    await cleanupAbandonedCarts();

    logger.info('Pending payments processing completed');
  } catch (error) {
    logger.error('Error in processPendingPayments:', error);
  }
};

// Process individual order
const processOrder = async (order) => {
  try {
    if (order.transaction_id) {
      try {
        // For Phone Point Dar: Check payment status with local payment providers
        // M-Pesa, Tigo Pesa, Airtel Money, etc.

        // Since we don't have Whop integration, we'll handle local payment verification
        // For now, we'll check if the order is older than 24 hours and mark as expired
        const orderAge = Date.now() - new Date(order.created_at).getTime();
        const twentyFourHours = 24 * 60 * 60 * 1000;

        if (orderAge > twentyFourHours) {
          // Order is too old, mark as expired
          await expireOrder(order);
          logger.info(`Order ${order._id} expired due to age (>24h)`);
        } else {
          // Order is still within valid timeframe, keep as pending
          logger.info(`Order ${order._id} still pending payment verification`);
        }
      } catch (error) {
        // If there's an error processing, mark as expired
        logger.warn(`Error processing payment for order ${order._id}:`, error.message);
        await expireOrder(order);
        logger.info(`Order ${order._id} expired due to processing error`);
      }
    } else {
      // No transaction ID - likely abandoned, mark as expired
      await expireOrder(order);
      logger.info(`Order ${order._id} expired due to no payment attempt`);
    }
  } catch (error) {
    logger.error(`Error processing order ${order._id}:`, error);
  }
};

// Complete successful order
const completeOrder = async (order) => {
  try {
    // Update order status
    order.status = 'completed';
    order.completedAt = new Date();
    await order.save();

    // Send confirmation email
    if (order.user && order.user.email) {
      await emailService.sendOrderConfirmation(order.user.email, {
        orderId: order._id,
        items: order.items,
        total: order.total,
        user: order.user
      });
    }

    // Update user's purchased products
    if (order.user) {
      const productIds = order.items.map(item => item.product._id);
      await User.findByIdAndUpdate(order.user._id, {
        $addToSet: { purchasedProducts: { $each: productIds } }
      });
    }

    logger.info(`Order ${order._id} completion process finished`);
  } catch (error) {
    logger.error(`Error completing order ${order._id}:`, error);
  }
};

// Mark order as failed
const failOrder = async (order) => {
  try {
    order.status = 'failed';
    order.failedAt = new Date();
    await order.save();

    // Send failure notification email
    if (order.user && order.user.email) {
      await emailService.sendPaymentFailedNotification(order.user.email, {
        orderId: order._id,
        user: order.user
      });
    }

    logger.info(`Order ${order._id} marked as failed`);
  } catch (error) {
    logger.error(`Error failing order ${order._id}:`, error);
  }
};

// Mark order as expired
const expireOrder = async (order) => {
  try {
    order.status = 'expired';
    order.expiredAt = new Date();
    await order.save();

    logger.info(`Order ${order._id} marked as expired`);
  } catch (error) {
    logger.error(`Error expiring order ${order._id}:`, error);
  }
};

// Clean up abandoned carts
const cleanupAbandonedCarts = async () => {
  try {
    const twentyFourHoursAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);

    const result = await Order.deleteMany({
      status: 'pending',
      createdAt: { $lt: twentyFourHoursAgo },
      transaction_id: { $exists: false }
    });

    logger.info(`Cleaned up ${result.deletedCount} abandoned carts`);
  } catch (error) {
    logger.error('Error cleaning up abandoned carts:', error);
  }
};

// Main execution
const main = async () => {
  try {
    await connectDB();
    await processPendingPayments();
  } catch (error) {
    logger.error('Script execution failed:', error);
  } finally {
    await mongoose.connection.close();
    logger.info('Database connection closed');
  }
};

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = { processPendingPayments, processOrder, completeOrder };
