const mongoose = require('mongoose');
const Inventory = require('../models/Inventory');
const Product = require('../models/Product');

const checkCarMountInventory = async () => {
  try {
    await mongoose.connect('mongodb://localhost:27017/aixcelerate-dev');
    console.log('Checking Car Phone Mount inventory...');
    
    const product = await Product.findOne({ name: 'Car Phone Mount' });
    if (!product) {
      console.log('Product not found');
      process.exit(1);
    }
    
    console.log('Product:', product.name);
    console.log('Product stock:', product.stock_quantity);
    console.log('Track inventory:', product.track_inventory);
    console.log('Variants:', product.variants?.length || 0);
    
    if (product.variants && product.variants.length > 0) {
      product.variants.forEach(v => {
        console.log('- Variant:', v.name, 'SKU:', v.sku, 'Stock:', v.stock_quantity);
      });
    }
    
    const inventory = await Inventory.findOne({
      product: product._id,
      variant_sku: null
    });
    
    if (inventory) {
      console.log('Inventory found:');
      console.log('- Available:', inventory.quantity_available);
      console.log('- On hand:', inventory.quantity_on_hand);
      console.log('- Reserved:', inventory.quantity_reserved);
    } else {
      console.log('No inventory record found');
    }
    
    process.exit(0);
  } catch (err) {
    console.error('Error:', err);
    process.exit(1);
  }
};

checkCarMountInventory();
