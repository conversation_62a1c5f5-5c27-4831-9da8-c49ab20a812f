const mongoose = require('mongoose');
const config = require('../config/config');

async function optimizePhonePointIndexes() {
  try {
    console.log('🔧 Optimizing Phone Point Dar database indexes...');
    
    await mongoose.connect(config.MONGODB_URI);
    const db = mongoose.connection.db;
    
    let indexesCreated = 0;
    
    // Helper function to create index if it doesn't exist
    const createIndexIfNotExists = async (collection, indexSpec, options = {}) => {
      try {
        const existingIndexes = await db.collection(collection).indexes();
        const indexName = options.name || Object.keys(indexSpec).map(key => `${key}_${indexSpec[key]}`).join('_');
        
        const exists = existingIndexes.some(index => 
          index.name === indexName || 
          JSON.stringify(index.key) === JSON.stringify(indexSpec)
        );
        
        if (!exists) {
          await db.collection(collection).createIndex(indexSpec, options);
          console.log(`✅ Created index on ${collection}:`, indexSpec);
          indexesCreated++;
          return true;
        } else {
          console.log(`⏭️ Index already exists on ${collection}:`, indexSpec);
          return false;
        }
      } catch (error) {
        console.error(`❌ Failed to create index on ${collection}:`, error.message);
        return false;
      }
    };

    // Phone Point Dar specific product indexes
    console.log('\n📱 Creating Phone Point Dar product indexes...');
    
    // Phone category and brand combinations (most common queries)
    await createIndexIfNotExists('products', { 
      category: 1, 
      brand: 1, 
      is_active: 1, 
      price: 1 
    }, { name: 'phone_category_brand_active_price' });
    
    // Phone storage and memory combinations
    await createIndexIfNotExists('products', { 
      'specifications.name': 1, 
      'specifications.value': 1, 
      is_active: 1 
    }, { name: 'phone_specs_active' });
    
    // Price range queries for phone shopping
    await createIndexIfNotExists('products', { 
      price: 1, 
      category: 1, 
      is_active: 1 
    }, { name: 'phone_price_category_active' });
    
    // Featured phones on homepage
    await createIndexIfNotExists('products', { 
      is_featured: 1, 
      is_active: 1, 
      category: 1, 
      created_at: -1 
    }, { name: 'phone_featured_active_category_recent' });
    
    // Phone search by brand and model
    await createIndexIfNotExists('products', { 
      brand: 1, 
      model: 1, 
      is_active: 1 
    }, { name: 'phone_brand_model_active' });

    // Inventory optimization for Phone Point Dar
    console.log('\n📦 Creating Phone Point Dar inventory indexes...');
    
    // IMEI tracking for individual phones
    await createIndexIfNotExists('inventories', { 
      'devices.imei': 1 
    }, { 
      unique: true, 
      sparse: true,
      name: 'phone_imei_unique' 
    });
    
    // Stock level monitoring
    await createIndexIfNotExists('inventories', { 
      quantity_available: 1, 
      reorder_point: 1, 
      location: 1 
    }, { name: 'phone_stock_monitoring' });
    
    // Product variant inventory lookup
    await createIndexIfNotExists('inventories', { 
      product: 1, 
      variant_sku: 1, 
      'devices.status': 1 
    }, { name: 'phone_variant_device_status' });

    // Order optimization for Phone Point Dar
    console.log('\n🛒 Creating Phone Point Dar order indexes...');
    
    // Order status and payment tracking
    await createIndexIfNotExists('orders', { 
      order_status: 1, 
      payment_status: 1, 
      created_at: -1 
    }, { name: 'phone_order_payment_status' });
    
    // Customer order history
    await createIndexIfNotExists('orders', { 
      customer_email: 1, 
      created_at: -1 
    }, { name: 'phone_customer_order_history' });
    
    // Phone delivery tracking
    await createIndexIfNotExists('orders', { 
      'shipping_address.city': 1, 
      order_status: 1 
    }, { name: 'phone_delivery_city_status' });

    // Analytics for Phone Point Dar
    console.log('\n📈 Creating Phone Point Dar analytics indexes...');
    
    // Product view tracking
    await createIndexIfNotExists('analytics', { 
      event_type: 1, 
      'metadata.product_id': 1, 
      timestamp: -1 
    }, { name: 'phone_product_analytics' });
    
    // User behavior tracking
    await createIndexIfNotExists('analytics', { 
      user_id: 1, 
      event_type: 1, 
      timestamp: -1 
    }, { name: 'phone_user_behavior' });
    
    // Session analytics
    await createIndexIfNotExists('analytics', { 
      session_id: 1, 
      timestamp: -1 
    }, { name: 'phone_session_analytics' });

    // Cart optimization
    console.log('\n🛍️ Creating Phone Point Dar cart indexes...');
    
    // Cart item IMEI tracking
    await createIndexIfNotExists('carts', { 
      'items.imei': 1 
    }, { 
      sparse: true,
      name: 'phone_cart_imei' 
    });
    
    // Abandoned cart recovery
    await createIndexIfNotExists('carts', { 
      updated_at: 1, 
      abandoned_cart_processed: 1 
    }, { name: 'phone_abandoned_cart' });

    // Trade-in optimization (Phone Point Dar specific)
    console.log('\n🔄 Creating Phone Point Dar trade-in indexes...');
    
    // Trade-in device lookup
    await createIndexIfNotExists('tradeins', { 
      device_brand: 1, 
      device_model: 1, 
      status: 1 
    }, { name: 'phone_tradein_device' });
    
    // Trade-in value estimation
    await createIndexIfNotExists('tradeins', { 
      device_brand: 1, 
      device_model: 1, 
      condition: 1, 
      estimated_value: -1 
    }, { name: 'phone_tradein_value' });

    console.log(`\n🎉 Phone Point Dar index optimization completed!`);
    console.log(`📊 Total indexes created: ${indexesCreated}`);
    
    // Display collection statistics
    console.log('\n📈 Collection Statistics:');
    const collections = ['products', 'inventories', 'orders', 'carts', 'analytics', 'tradeins'];
    
    for (const collectionName of collections) {
      try {
        const stats = await db.collection(collectionName).stats();
        const indexes = await db.collection(collectionName).indexes();
        console.log(`${collectionName}: ${stats.count} documents, ${indexes.length} indexes`);
      } catch (error) {
        console.log(`${collectionName}: Collection not found or error getting stats`);
      }
    }
    
    mongoose.disconnect();
    console.log('\n✅ Database optimization completed successfully!');
    
  } catch (error) {
    console.error('❌ Database optimization failed:', error);
    mongoose.disconnect();
    process.exit(1);
  }
}

// Run the optimization
optimizePhonePointIndexes();
