const mongoose = require('mongoose');
const User = require('../models/User');
const config = require('../config/config');

// Connect to MongoDB
mongoose.connect(config.MONGODB_URI)
  .then(() => console.log('MongoDB Connected'))
  .catch(err => {
    console.error('MongoDB Connection Error:', err);
    process.exit(1);
  });

// Update admin user role
const updateAdminRole = async () => {
  try {
    // Find admin user
    const adminUser = await User.findOne({ email: '<EMAIL>' });
    
    if (!adminUser) {
      console.log('Admin user not found');
      mongoose.disconnect();
      return;
    }
    
    // Update role to admin
    adminUser.role = 'admin';
    await adminUser.save();
    
    console.log('Admin user role updated successfully:', adminUser.email);
    
    mongoose.disconnect();
  } catch (err) {
    console.error('Error updating admin role:', err);
    mongoose.disconnect();
    process.exit(1);
  }
};

updateAdminRole();
