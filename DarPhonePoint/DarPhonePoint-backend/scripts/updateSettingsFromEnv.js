const mongoose = require('mongoose');
const Settings = require('../models/Settings');
require('dotenv').config();

/**
 * Update settings to match current environment variables
 */
const updateSettingsFromEnv = async () => {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/aixcelerate');
    console.log('Connected to MongoDB');

    console.log('Updating settings to match environment variables...\n');

    // Email Settings
    console.log('📧 Updating Email Settings:');
    
    if (process.env.EMAIL_HOST) {
      await Settings.setSetting('email', 'smtpHost', process.env.EMAIL_HOST);
      console.log(`✅ SMTP Host: ${process.env.EMAIL_HOST}`);
    }
    
    if (process.env.EMAIL_PORT) {
      await Settings.setSetting('email', 'smtpPort', parseInt(process.env.EMAIL_PORT));
      console.log(`✅ SMTP Port: ${process.env.EMAIL_PORT}`);
    }
    
    if (process.env.EMAIL_SECURE) {
      await Settings.setSetting('email', 'smtpSecure', process.env.EMAIL_SECURE === 'true');
      console.log(`✅ SMTP Secure: ${process.env.EMAIL_SECURE}`);
    }
    
    if (process.env.EMAIL_USER) {
      await Settings.setSetting('email', 'smtpUsername', process.env.EMAIL_USER);
      console.log(`✅ SMTP Username: ${process.env.EMAIL_USER}`);
    }
    
    if (process.env.EMAIL_PASSWORD) {
      await Settings.setSetting('email', 'smtpPassword', process.env.EMAIL_PASSWORD, {
        isEncrypted: true
      });
      console.log(`✅ SMTP Password: [ENCRYPTED]`);
    }
    
    if (process.env.EMAIL_FROM) {
      await Settings.setSetting('email', 'fromEmail', process.env.EMAIL_FROM);
      console.log(`✅ From Email: ${process.env.EMAIL_FROM}`);
    }

    // Payment Settings
    console.log('\n💳 Updating Payment Settings:');
    
    if (process.env.STRIPE_PUBLISHABLE_KEY) {
      await Settings.setSetting('payment', 'stripePublicKey', process.env.STRIPE_PUBLISHABLE_KEY, {
        isPublic: true
      });
      console.log(`✅ Stripe Public Key: ${process.env.STRIPE_PUBLISHABLE_KEY.substring(0, 20)}...`);
    }
    
    if (process.env.STRIPE_SECRET_KEY) {
      await Settings.setSetting('payment', 'stripeSecretKey', process.env.STRIPE_SECRET_KEY, {
        isEncrypted: true
      });
      console.log(`✅ Stripe Secret Key: ${process.env.STRIPE_SECRET_KEY.substring(0, 20)}...`);
    }
    
    if (process.env.STRIPE_WEBHOOK_SECRET) {
      await Settings.setSetting('payment', 'stripeWebhookSecret', process.env.STRIPE_WEBHOOK_SECRET, {
        isEncrypted: true
      });
      console.log(`✅ Stripe Webhook Secret: [ENCRYPTED]`);
    }

    // General Settings
    console.log('\n🌐 Updating General Settings:');
    
    if (process.env.FRONTEND_URL) {
      await Settings.setSetting('general', 'frontendUrl', process.env.FRONTEND_URL, {
        isPublic: true
      });
      console.log(`✅ Frontend URL: ${process.env.FRONTEND_URL}`);
    }
    
    if (process.env.BASE_URL) {
      await Settings.setSetting('general', 'baseUrl', process.env.BASE_URL, {
        isPublic: true
      });
      console.log(`✅ Base URL: ${process.env.BASE_URL}`);
    }
    
    if (process.env.EMAIL_FROM) {
      await Settings.setSetting('general', 'contactEmail', process.env.EMAIL_FROM, {
        isPublic: true
      });
      console.log(`✅ Contact Email: ${process.env.EMAIL_FROM}`);
    }

    // Security Settings
    console.log('\n🔒 Updating Security Settings:');
    
    if (process.env.JWT_SECRET) {
      await Settings.setSetting('security', 'jwtSecret', process.env.JWT_SECRET, {
        isEncrypted: true
      });
      console.log(`✅ JWT Secret: [ENCRYPTED]`);
    }
    
    if (process.env.JWT_EXPIRE) {
      await Settings.setSetting('security', 'jwtExpire', process.env.JWT_EXPIRE);
      console.log(`✅ JWT Expire: ${process.env.JWT_EXPIRE}`);
    }
    
    if (process.env.BCRYPT_ROUNDS) {
      await Settings.setSetting('security', 'bcryptRounds', parseInt(process.env.BCRYPT_ROUNDS));
      console.log(`✅ Bcrypt Rounds: ${process.env.BCRYPT_ROUNDS}`);
    }
    
    if (process.env.SESSION_SECRET) {
      await Settings.setSetting('security', 'sessionSecret', process.env.SESSION_SECRET, {
        isEncrypted: true
      });
      console.log(`✅ Session Secret: [ENCRYPTED]`);
    }
    
    if (process.env.CORS_ORIGIN) {
      await Settings.setSetting('security', 'corsOrigin', process.env.CORS_ORIGIN);
      console.log(`✅ CORS Origin: ${process.env.CORS_ORIGIN}`);
    }

    // Storage Settings
    console.log('\n📁 Updating Storage Settings:');
    
    if (process.env.UPLOAD_PATH) {
      await Settings.setSetting('storage', 'uploadPath', process.env.UPLOAD_PATH);
      console.log(`✅ Upload Path: ${process.env.UPLOAD_PATH}`);
    }
    
    if (process.env.MAX_FILE_SIZE) {
      await Settings.setSetting('storage', 'maxFileSize', parseInt(process.env.MAX_FILE_SIZE));
      console.log(`✅ Max File Size: ${process.env.MAX_FILE_SIZE} bytes`);
    }
    
    if (process.env.ALLOWED_FILE_TYPES) {
      await Settings.setSetting('storage', 'allowedFileTypes', process.env.ALLOWED_FILE_TYPES);
      console.log(`✅ Allowed File Types: ${process.env.ALLOWED_FILE_TYPES}`);
    }

    console.log('\n🎉 Settings successfully updated to match environment variables!');

    // Verify the updates
    console.log('\n📋 Verification - Current Email & Payment Settings:');
    const emailSettings = await Settings.getByCategory('email');
    const paymentSettings = await Settings.getByCategory('payment');
    
    console.log('\nEmail Settings:');
    Object.entries(emailSettings).forEach(([key, value]) => {
      if (key.includes('password') || key.includes('secret')) {
        console.log(`  ${key}: [ENCRYPTED]`);
      } else {
        console.log(`  ${key}: ${value}`);
      }
    });
    
    console.log('\nPayment Settings:');
    Object.entries(paymentSettings).forEach(([key, value]) => {
      if (key.includes('secret') || key.includes('Secret')) {
        console.log(`  ${key}: [ENCRYPTED]`);
      } else if (key.includes('Key') && typeof value === 'string' && value.length > 20) {
        console.log(`  ${key}: ${value.substring(0, 20)}...`);
      } else {
        console.log(`  ${key}: ${value}`);
      }
    });

  } catch (error) {
    console.error('Error updating settings:', error);
    process.exit(1);
  } finally {
    await mongoose.disconnect();
    console.log('\nDisconnected from MongoDB');
    process.exit(0);
  }
};

// Run the update
updateSettingsFromEnv();
