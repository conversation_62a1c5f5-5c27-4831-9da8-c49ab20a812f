const mongoose = require('mongoose');
const Product = require('../models/Product');

// Connect to database
mongoose.connect('mongodb://localhost:27017/aixcelerate-dev');

// Helper function to generate slug
function generateSlug(name) {
  return name.toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '')
    .replace(/\s+/g, '-')
    .replace(/-+/g, '-')
    .trim('-');
}

const phonePointProducts = [
  // SMARTPHONES
  {
    name: "iPhone 15 Pro Max",
    slug: generateSlug("iPhone 15 Pro Max"),
    sku: "IP15PM-BASE",
    brand: "Apple",
    model: "iPhone 15 Pro Max",
    category: "smartphone",
    price: 2800000,
    description: "Latest iPhone with titanium design, A17 Pro chip, and advanced camera system",
    specifications: [
      { name: "Display", value: "6.7-inch Super Retina XDR", category: "display" },
      { name: "Processor", value: "A17 Pro chip", category: "performance" },
      { name: "Storage", value: "256GB", category: "storage" },
      { name: "Camera", value: "48MP Main + 12MP Ultra Wide + 12MP Telephoto", category: "camera" },
      { name: "Battery", value: "Up to 29 hours video playback", category: "battery" },
      { name: "OS", value: "iOS 17", category: "software" }
    ],
    variants: [
      { name: "256GB Natural Titanium", sku: "IP15PM-256-NT", storage: "256GB", color: "Natural Titanium", price: 2800000 },
      { name: "512GB Natural Titanium", sku: "IP15PM-512-NT", storage: "512GB", color: "Natural Titanium", price: 3200000 },
      { name: "1TB Natural Titanium", sku: "IP15PM-1TB-NT", storage: "1TB", color: "Natural Titanium", price: 3800000 }
    ],
    images: [{ url: "/images/phones/iphone-15-pro-max.jpg", alt_text: "iPhone 15 Pro Max", is_primary: true }],
    is_active: true,
    track_inventory: true,
    weight: 221,
    warranty_months: 12
  },
  {
    name: "Samsung Galaxy S24 Ultra",
    slug: generateSlug("Samsung Galaxy S24 Ultra"),
    sku: "SGS24U-BASE",
    brand: "Samsung",
    model: "Galaxy S24 Ultra",
    category: "smartphone",
    price: 2600000,
    description: "Premium Android flagship with S Pen, AI features, and 200MP camera",
    specifications: [
      { name: "Display", value: "6.8-inch Dynamic AMOLED 2X", category: "display" },
      { name: "Processor", value: "Snapdragon 8 Gen 3", category: "performance" },
      { name: "Storage", value: "256GB", category: "storage" },
      { name: "Camera", value: "200MP Main + 50MP Periscope + 12MP Ultra Wide + 10MP Telephoto", category: "camera" },
      { name: "Battery", value: "5000mAh", category: "battery" },
      { name: "OS", value: "Android 14", category: "software" }
    ],
    variants: [
      { name: "256GB Titanium Gray", sku: "SGS24U-256-TG", storage: "256GB", color: "Titanium Gray", price: 2600000 },
      { name: "512GB Titanium Gray", sku: "SGS24U-512-TG", storage: "512GB", color: "Titanium Gray", price: 2900000 },
      { name: "1TB Titanium Gray", sku: "SGS24U-1TB-TG", storage: "1TB", color: "Titanium Gray", price: 3400000 }
    ],
    images: [{ url: "/images/phones/galaxy-s24-ultra.jpg", alt_text: "Samsung Galaxy S24 Ultra", is_primary: true }],
    is_active: true,
    track_inventory: true,
    weight: 232,
    warranty_months: 12
  },
  {
    name: "Xiaomi Redmi Note 13",
    slug: generateSlug("Xiaomi Redmi Note 13"),
    sku: "RN13-BASE",
    brand: "Xiaomi",
    model: "Redmi Note 13",
    category: "smartphone",
    price: 450000,
    description: "Affordable smartphone with great camera and long battery life",
    specifications: [
      { name: "Display", value: "6.67-inch AMOLED", category: "display" },
      { name: "Processor", value: "Snapdragon 685", category: "performance" },
      { name: "Storage", value: "128GB", category: "storage" },
      { name: "Camera", value: "108MP Main + 8MP Ultra Wide + 2MP Macro", category: "camera" },
      { name: "Battery", value: "5000mAh", category: "battery" },
      { name: "OS", value: "MIUI 14", category: "software" }
    ],
    variants: [
      { name: "128GB Mint Blue", sku: "RN13-128-MB", storage: "128GB", color: "Mint Blue", price: 450000 },
      { name: "256GB Mint Blue", sku: "RN13-256-MB", storage: "256GB", color: "Mint Blue", price: 520000 }
    ],
    images: [{ url: "/images/phones/redmi-note-13.jpg", alt_text: "Xiaomi Redmi Note 13", is_primary: true }],
    is_active: true,
    track_inventory: true,
    weight: 188,
    warranty_months: 12
  },
  {
    name: "Google Pixel 8",
    slug: generateSlug("Google Pixel 8"),
    sku: "GP8-BASE",
    brand: "Google",
    model: "Pixel 8",
    category: "smartphone",
    price: 1800000,
    description: "Pure Android experience with advanced AI photography",
    specifications: [
      { name: "Display", value: "6.2-inch OLED", category: "display" },
      { name: "Processor", value: "Google Tensor G3", category: "performance" },
      { name: "Storage", value: "128GB", category: "storage" },
      { name: "Camera", value: "50MP Main + 12MP Ultra Wide", category: "camera" },
      { name: "Battery", value: "4575mAh", category: "battery" },
      { name: "OS", value: "Android 14", category: "software" }
    ],
    variants: [
      { name: "128GB Hazel", sku: "GP8-128-HZ", storage: "128GB", color: "Hazel", price: 1800000 },
      { name: "256GB Hazel", sku: "GP8-256-HZ", storage: "256GB", color: "Hazel", price: 2000000 }
    ],
    images: [{ url: "/images/phones/pixel-8.jpg", alt_text: "Google Pixel 8", is_primary: true }],
    is_active: true,
    track_inventory: true,
    weight: 187,
    warranty_months: 12
  },

  // PHONE ACCESSORIES
  {
    name: "iPhone 15 Pro Max Silicone Case",
    slug: generateSlug("iPhone 15 Pro Max Silicone Case"),
    sku: "IP15PM-CASE-BASE",
    brand: "Apple",
    model: "Silicone Case",
    category: "case",
    price: 85000,
    description: "Official Apple silicone case for iPhone 15 Pro Max",
    specifications: [
      { name: "Material", value: "Silicone", category: "material" },
      { name: "Compatibility", value: "iPhone 15 Pro Max", category: "compatibility" },
      { name: "Protection", value: "Drop protection up to 2 meters", category: "protection" }
    ],
    variants: [
      { name: "Black", sku: "IP15PM-CASE-BLK", color: "Black", price: 85000 },
      { name: "Blue", sku: "IP15PM-CASE-BLU", color: "Blue", price: 85000 },
      { name: "Red", sku: "IP15PM-CASE-RED", color: "Red", price: 85000 }
    ],
    images: [{ url: "/images/accessories/iphone-case.jpg", alt_text: "iPhone 15 Pro Max Silicone Case", is_primary: true }],
    is_active: true,
    track_inventory: false,
    weight: 45,
    warranty_months: 6
  },
  {
    name: "Samsung Galaxy S24 Ultra Clear Case",
    slug: generateSlug("Samsung Galaxy S24 Ultra Clear Case"),
    sku: "SGS24U-CASE-BASE",
    brand: "Samsung",
    model: "Clear Case",
    category: "case",
    price: 65000,
    description: "Transparent protective case for Galaxy S24 Ultra",
    specifications: [
      { name: "Material", value: "TPU + PC", category: "material" },
      { name: "Compatibility", value: "Galaxy S24 Ultra", category: "compatibility" },
      { name: "Protection", value: "Shock absorption", category: "protection" }
    ],
    variants: [
      { name: "Clear", sku: "SGS24U-CASE-CLR", color: "Clear", price: 65000 }
    ],
    images: [{ url: "/images/accessories/samsung-case.jpg", alt_text: "Samsung Galaxy S24 Ultra Clear Case", is_primary: true }],
    is_active: true,
    track_inventory: false,
    weight: 35,
    warranty_months: 6
  },

  // CHARGERS & CABLES
  {
    name: "USB-C Fast Charger 65W",
    slug: generateSlug("USB-C Fast Charger 65W"),
    sku: "ANKER-USBC-65W",
    brand: "Anker",
    model: "PowerPort III",
    category: "charger",
    price: 120000,
    description: "High-speed USB-C charger compatible with most devices",
    specifications: [
      { name: "Power", value: "65W", category: "power" },
      { name: "Ports", value: "1x USB-C", category: "connectivity" },
      { name: "Compatibility", value: "iPhone 15 series, Android devices, laptops", category: "compatibility" },
      { name: "Cable Included", value: "USB-C to USB-C cable", category: "accessories" }
    ],
    images: [{ url: "/images/accessories/usb-c-charger.jpg", alt_text: "USB-C Fast Charger 65W", is_primary: true }],
    is_active: true,
    track_inventory: false,
    weight: 180,
    warranty_months: 24
  },
  {
    name: "Lightning to USB-C Cable",
    slug: generateSlug("Lightning to USB-C Cable"),
    sku: "APPLE-LTNG-USBC",
    brand: "Apple",
    model: "Lightning Cable",
    category: "cable",
    price: 45000,
    description: "Official Apple Lightning to USB-C cable",
    specifications: [
      { name: "Length", value: "1 meter", category: "physical" },
      { name: "Compatibility", value: "iPhone 14 and earlier, iPad", category: "compatibility" },
      { name: "Data Transfer", value: "USB 2.0", category: "performance" }
    ],
    images: [{ url: "/images/accessories/lightning-cable.jpg", alt_text: "Lightning to USB-C Cable", is_primary: true }],
    is_active: true,
    track_inventory: false,
    weight: 25,
    warranty_months: 12
  },

  // AUDIO ACCESSORIES
  {
    name: "AirPods Pro (2nd Generation)",
    slug: generateSlug("AirPods Pro 2nd Generation"),
    sku: "AIRPODS-PRO-2",
    brand: "Apple",
    model: "AirPods Pro",
    category: "earbuds",
    price: 650000,
    description: "Premium wireless earbuds with active noise cancellation",
    specifications: [
      { name: "Driver", value: "Custom Apple driver", category: "audio" },
      { name: "Noise Cancellation", value: "Active Noise Cancellation", category: "features" },
      { name: "Battery", value: "Up to 6 hours listening time", category: "battery" },
      { name: "Case Battery", value: "Up to 30 hours total", category: "battery" },
      { name: "Connectivity", value: "Bluetooth 5.3", category: "connectivity" }
    ],
    images: [{ url: "/images/audio/airpods-pro.jpg", alt_text: "AirPods Pro (2nd Generation)", is_primary: true }],
    is_active: true,
    track_inventory: true,
    weight: 56,
    warranty_months: 12
  },
  {
    name: "Samsung Galaxy Buds2 Pro",
    slug: generateSlug("Samsung Galaxy Buds2 Pro"),
    sku: "GALAXY-BUDS2-PRO",
    brand: "Samsung",
    model: "Galaxy Buds2 Pro",
    category: "earbuds",
    price: 480000,
    description: "Premium wireless earbuds with 360 Audio",
    specifications: [
      { name: "Driver", value: "10mm woofer + 5.3mm tweeter", category: "audio" },
      { name: "Noise Cancellation", value: "Intelligent Active Noise Cancellation", category: "features" },
      { name: "Battery", value: "Up to 5 hours listening time", category: "battery" },
      { name: "Case Battery", value: "Up to 18 hours total", category: "battery" },
      { name: "Connectivity", value: "Bluetooth 5.3", category: "connectivity" }
    ],
    images: [{ url: "/images/audio/galaxy-buds-pro.jpg", alt_text: "Samsung Galaxy Buds2 Pro", is_primary: true }],
    is_active: true,
    track_inventory: true,
    weight: 58,
    warranty_months: 12
  }
];

async function seedProducts() {
  try {
    console.log('🌱 Starting Phone Point Dar product seeding...');
    
    // Clear existing products
    await Product.deleteMany({});
    console.log('🗑️  Cleared existing products');
    
    // Insert new products
    const insertedProducts = await Product.insertMany(phonePointProducts);
    console.log(`✅ Successfully seeded ${insertedProducts.length} products:`);
    
    // Group by category for summary
    const categories = {};
    insertedProducts.forEach(product => {
      const category = product.category;
      if (!categories[category]) categories[category] = 0;
      categories[category]++;
    });
    
    console.log('\n📊 Products by category:');
    Object.entries(categories).forEach(([category, count]) => {
      console.log(`   ${category}: ${count} products`);
    });
    
    console.log('\n🎉 Phone Point Dar product seeding completed successfully!');
    process.exit(0);
    
  } catch (error) {
    console.error('❌ Error seeding products:', error);
    process.exit(1);
  }
}

seedProducts();
