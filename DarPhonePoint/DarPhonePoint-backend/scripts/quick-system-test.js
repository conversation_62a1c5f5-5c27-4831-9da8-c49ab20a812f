#!/usr/bin/env node

const axios = require('axios');
const colors = require('colors');

const BASE_URL = 'http://localhost:5000';
const FRONTEND_URL = 'http://localhost:5173';

// Test results
let passed = 0;
let failed = 0;

const log = (message, type = 'info') => {
  const colorMap = {
    success: 'green',
    error: 'red',
    warning: 'yellow',
    info: 'cyan'
  };
  console.log(message[colorMap[type] || 'white']);
};

const test = async (name, testFn) => {
  try {
    log(`🧪 Testing: ${name}`, 'info');
    const result = await testFn();
    
    if (result.success) {
      passed++;
      log(`✅ ${name}: PASSED - ${result.details}`, 'success');
    } else {
      failed++;
      log(`❌ ${name}: FAILED - ${result.details}`, 'error');
    }
  } catch (error) {
    failed++;
    log(`💥 ${name}: ERROR - ${error.message}`, 'error');
  }
};

// Test functions
const testBackendHealth = async () => {
  const response = await axios.get(`${BASE_URL}/api/health`);
  return {
    success: response.status === 200 && response.data.status === 'healthy',
    details: `Backend healthy, Database: ${response.data.services?.database?.status}`
  };
};

const testAuthentication = async () => {
  const response = await axios.post(`${BASE_URL}/api/auth/login`, {
    email: '<EMAIL>',
    password: 'Admin123!'
  });
  
  return {
    success: response.data.success && response.data.token,
    details: `Login successful for ${response.data.user?.name} (${response.data.user?.role})`
  };
};

const testProducts = async () => {
  const response = await axios.get(`${BASE_URL}/api/products`);
  return {
    success: response.data.success && response.data.count > 0,
    details: `${response.data.count} products loaded successfully`
  };
};

const testWhopConfig = async () => {
  const response = await axios.get(`${BASE_URL}/api/whop/config`);
  return {
    success: response.data.success && response.data.data.configured,
    details: `Whop configured with App ID: ${response.data.data.appId}`
  };
};

const testFrontend = async () => {
  const response = await axios.get(FRONTEND_URL);
  return {
    success: response.status === 200,
    details: `Frontend accessible on port 5173`
  };
};

// Main test runner
async function runTests() {
  log('\n🚀 AIXcelerate System Test', 'info');
  log('=' * 40, 'info');
  
  await test('Backend Health', testBackendHealth);
  await test('Authentication', testAuthentication);
  await test('Products API', testProducts);
  await test('Whop Integration', testWhopConfig);
  await test('Frontend Access', testFrontend);
  
  log('\n' + '=' * 40, 'info');
  log('📊 TEST SUMMARY', 'info');
  log('=' * 40, 'info');
  log(`✅ Passed: ${passed}`, 'success');
  log(`❌ Failed: ${failed}`, failed > 0 ? 'error' : 'success');
  log(`📊 Total: ${passed + failed}`, 'info');
  
  if (failed === 0) {
    log('\n🎉 ALL TESTS PASSED! 🎉', 'success');
    log('🚀 System is ready for production!', 'success');
    log('\n✅ Backend: Running on port 5000', 'success');
    log('✅ Frontend: Running on port 5173', 'success');
    log('✅ Database: Connected and populated', 'success');
    log('✅ Authentication: Working', 'success');
    log('✅ Whop Integration: Configured', 'success');
    log('\n🎯 Ready for CapRover deployment!', 'success');
  } else {
    log('\n⚠️  Some tests failed', 'warning');
    log('🔧 Please check the errors above', 'warning');
  }
  
  return failed === 0;
}

// Run tests
if (require.main === module) {
  runTests().then(success => {
    process.exit(success ? 0 : 1);
  }).catch(error => {
    log(`💥 Test suite crashed: ${error.message}`, 'error');
    process.exit(1);
  });
}

module.exports = { runTests };
