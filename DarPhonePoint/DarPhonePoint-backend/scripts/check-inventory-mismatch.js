/**
 * Check for inventory mismatches between Product and Inventory models
 */

const mongoose = require('mongoose');
const Inventory = require('../models/Inventory');
const Product = require('../models/Product');

const connectDB = async () => {
  try {
    await mongoose.connect('mongodb://localhost:27017/aixcelerate-dev');
    console.log('MongoDB connected successfully');
  } catch (error) {
    console.error('MongoDB connection error:', error);
    process.exit(1);
  }
};

const checkInventoryMismatch = async () => {
  try {
    console.log('🔍 Checking inventory mismatches...\n');
    
    // Check inventory records
    const inventoryItems = await Inventory.find({}).populate('product', 'name category');
    console.log(`📦 Inventory records found: ${inventoryItems.length}`);
    
    inventoryItems.forEach(item => {
      console.log(`- ${item.product?.name} (${item.variant_sku || 'no variant'}): ${item.quantity_available} available`);
    });
    
    console.log('\n📱 Checking products with track_inventory=true...');
    
    // Check products without inventory
    const products = await Product.find({ track_inventory: true });
    console.log(`Products with track_inventory=true: ${products.length}\n`);
    
    let missingInventoryCount = 0;
    
    for (const product of products) {
      console.log(`\n📱 Checking: ${product.name} (Category: ${product.category})`);
      console.log(`   Product stock_quantity: ${product.stock_quantity}`);
      
      if (product.variants && product.variants.length > 0) {
        console.log(`   Has ${product.variants.length} variants:`);
        
        for (const variant of product.variants) {
          console.log(`   - Variant: ${variant.name} (${variant.sku})`);
          console.log(`     Variant stock: ${variant.stock_quantity}`);
          
          const inventory = await Inventory.findOne({
            product: product._id,
            variant_sku: variant.sku
          });
          
          if (!inventory) {
            console.log(`     ❌ Missing inventory record`);
            missingInventoryCount++;
          } else {
            console.log(`     ✅ Inventory available: ${inventory.quantity_available}`);
            if (inventory.quantity_available !== variant.stock_quantity) {
              console.log(`     ⚠️  Mismatch: Variant(${variant.stock_quantity}) vs Inventory(${inventory.quantity_available})`);
            }
          }
        }
      } else {
        const inventory = await Inventory.findOne({
          product: product._id,
          variant_sku: null
        });
        
        if (!inventory) {
          console.log(`   ❌ Missing inventory record (no variants)`);
          missingInventoryCount++;
        } else {
          console.log(`   ✅ Inventory available: ${inventory.quantity_available}`);
          if (inventory.quantity_available !== product.stock_quantity) {
            console.log(`   ⚠️  Mismatch: Product(${product.stock_quantity}) vs Inventory(${inventory.quantity_available})`);
          }
        }
      }
    }
    
    console.log(`\n📊 Summary:`);
    console.log(`   - Total products with track_inventory: ${products.length}`);
    console.log(`   - Missing inventory records: ${missingInventoryCount}`);
    console.log(`   - Existing inventory records: ${inventoryItems.length}`);
    
    if (missingInventoryCount > 0) {
      console.log(`\n💡 Solution: Create inventory records for missing products`);
    }
    
    process.exit(0);
  } catch (error) {
    console.error('❌ Error checking inventory:', error);
    process.exit(1);
  }
};

const runCheck = async () => {
  await connectDB();
  await checkInventoryMismatch();
};

if (require.main === module) {
  runCheck();
}

module.exports = { checkInventoryMismatch };
