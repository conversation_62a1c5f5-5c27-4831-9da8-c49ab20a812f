const mongoose = require('mongoose');
const Product = require('../models/Product');
require('dotenv').config();

const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/phonepointdar');
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  }
};

const testProducts = [
  {
    name: 'iPhone 15 Pro',
    slug: 'iphone-15-pro',
    sku: 'IPH15PRO',
    description: 'Latest iPhone with A17 Pro chip and titanium design',
    category: 'smartphone',
    brand: 'Apple',
    model: 'iPhone 15 Pro',
    price: 2800000, // TZS
    compare_at_price: 3000000,
    stock_quantity: 10,
    low_stock_threshold: 2,
    is_active: true,
    is_featured: true,
    specifications: [
      { name: 'Screen Size', value: '6.1 inches', category: 'display', unit: 'inches', is_key_spec: true },
      { name: 'Processor', value: 'A17 Pro', category: 'performance', is_key_spec: true },
      { name: 'Storage', value: '128GB', category: 'storage', unit: 'GB', is_key_spec: true },
      { name: 'Camera', value: '48MP Main', category: 'camera', unit: 'MP', is_key_spec: true }
    ],
    images: [
      { url: '/images/iphone-15-pro.jpg', alt_text: 'iPhone 15 Pro', is_primary: true, order: 1 }
    ],
    variants: [
      {
        name: '128GB Natural Titanium',
        sku: 'IPH15P-128-NT',
        price: 2800000,
        color: 'Natural Titanium',
        storage: '128GB',
        stock_quantity: 5
      },
      {
        name: '256GB Blue Titanium',
        sku: 'IPH15P-256-BT',
        price: 3200000,
        color: 'Blue Titanium',
        storage: '256GB',
        stock_quantity: 3
      }
    ]
  },
  {
    name: 'Samsung Galaxy S24 Ultra',
    slug: 'samsung-galaxy-s24-ultra',
    sku: 'SGS24ULTRA',
    description: 'Premium Android flagship with S Pen and AI features',
    category: 'smartphone',
    brand: 'Samsung',
    model: 'Galaxy S24 Ultra',
    price: 2200000, // TZS
    compare_at_price: 2500000,
    stock_quantity: 8,
    low_stock_threshold: 2,
    is_active: true,
    is_featured: true,
    specifications: [
      { name: 'Screen Size', value: '6.8 inches', category: 'display', unit: 'inches', is_key_spec: true },
      { name: 'Processor', value: 'Snapdragon 8 Gen 3', category: 'performance', is_key_spec: true },
      { name: 'Storage', value: '256GB', category: 'storage', unit: 'GB', is_key_spec: true },
      { name: 'Camera', value: '200MP Main', category: 'camera', unit: 'MP', is_key_spec: true }
    ],
    images: [
      { url: '/images/galaxy-s24-ultra.jpg', alt_text: 'Samsung Galaxy S24 Ultra', is_primary: true, order: 1 }
    ],
    variants: [
      {
        name: '256GB Titanium Black',
        sku: 'SGS24U-256-TB',
        price: 2200000,
        color: 'Titanium Black',
        storage: '256GB',
        stock_quantity: 4
      },
      {
        name: '512GB Titanium Violet',
        sku: 'SGS24U-512-TV',
        price: 2600000,
        color: 'Titanium Violet',
        storage: '512GB',
        stock_quantity: 4
      }
    ]
  },
  {
    name: 'AirPods Pro 2nd Gen',
    slug: 'airpods-pro-2nd-gen',
    sku: 'AIRPODSPRO2',
    description: 'Premium wireless earbuds with active noise cancellation',
    category: 'earbuds',
    brand: 'Apple',
    model: 'AirPods Pro',
    price: 450000, // TZS
    compare_at_price: 500000,
    stock_quantity: 15,
    low_stock_threshold: 3,
    is_active: true,
    is_featured: true,
    specifications: [
      { name: 'Driver Size', value: 'Custom', category: 'audio', is_key_spec: true },
      { name: 'Battery Life', value: '6 hours', category: 'battery', unit: 'hours', is_key_spec: true },
      { name: 'Connectivity', value: 'Bluetooth 5.3', category: 'connectivity', is_key_spec: true },
      { name: 'Noise Cancellation', value: 'Active', category: 'audio', is_key_spec: true }
    ],
    images: [
      { url: '/images/airpods-pro-2.jpg', alt_text: 'AirPods Pro 2nd Gen', is_primary: true, order: 1 }
    ],
    variants: [
      {
        name: 'AirPods Pro 2nd Gen White',
        sku: 'APP2-WHT',
        price: 450000,
        color: 'White',
        stock_quantity: 15
      }
    ]
  },
  {
    name: 'iPhone 15 Silicone Case',
    slug: 'iphone-15-silicone-case',
    sku: 'IPH15CASE',
    description: 'Official Apple silicone case for iPhone 15',
    category: 'phone_case',
    brand: 'Apple',
    model: 'iPhone 15 Case',
    price: 85000, // TZS
    stock_quantity: 20,
    low_stock_threshold: 5,
    is_active: true,
    specifications: [
      { name: 'Material', value: 'Silicone', category: 'build', is_key_spec: true },
      { name: 'Compatibility', value: 'iPhone 15', category: 'compatibility', is_key_spec: true },
      { name: 'Protection', value: 'Drop Protection', category: 'protection', is_key_spec: true }
    ],
    images: [
      { url: '/images/iphone-15-case.jpg', alt_text: 'iPhone 15 Silicone Case', is_primary: true, order: 1 }
    ],
    variants: [
      {
        name: 'iPhone 15 Case Blue',
        sku: 'IPH15C-BLU',
        price: 85000,
        color: 'Blue',
        stock_quantity: 10
      },
      {
        name: 'iPhone 15 Case Black',
        sku: 'IPH15C-BLK',
        price: 85000,
        color: 'Black',
        stock_quantity: 10
      }
    ]
  },
  {
    name: 'USB-C Fast Charger',
    slug: 'usb-c-fast-charger',
    sku: 'USBCCHARGER',
    description: '20W USB-C power adapter for fast charging',
    category: 'charger',
    brand: 'Apple',
    model: '20W USB-C Adapter',
    price: 65000, // TZS
    stock_quantity: 25,
    low_stock_threshold: 5,
    is_active: true,
    specifications: [
      { name: 'Power Output', value: '20W', category: 'power_output', unit: 'W', is_key_spec: true },
      { name: 'Connector', value: 'USB-C', category: 'connectivity', is_key_spec: true },
      { name: 'Fast Charging', value: 'Yes', category: 'special_features', is_key_spec: true }
    ],
    images: [
      { url: '/images/usb-c-charger.jpg', alt_text: 'USB-C Fast Charger', is_primary: true, order: 1 }
    ],
    variants: [
      {
        name: '20W USB-C Charger White',
        sku: 'USBC20W-WHT',
        price: 65000,
        color: 'White',
        stock_quantity: 25
      }
    ]
  }
];

const seedProducts = async () => {
  try {
    console.log('🗑️  Clearing existing products...');
    await Product.deleteMany({});
    
    console.log('🌱 Creating test products...');
    
    for (const productData of testProducts) {
      try {
        const product = new Product(productData);
        await product.save();
        console.log(`✅ Created: ${product.name}`);
      } catch (error) {
        console.error(`❌ Error creating ${productData.name}:`, error.message);
      }
    }
    
    const count = await Product.countDocuments();
    console.log(`\n🎉 Product seeding completed!`);
    console.log(`📊 Total products created: ${count}`);
    
  } catch (error) {
    console.error('❌ Error seeding products:', error);
  }
};

const main = async () => {
  await connectDB();
  await seedProducts();
  await mongoose.disconnect();
  console.log('✅ Disconnected from MongoDB');
};

main().catch(console.error);
