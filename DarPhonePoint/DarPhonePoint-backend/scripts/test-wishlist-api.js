/**
 * Test wishlist API with proper authentication
 */

const axios = require('axios');
const mongoose = require('mongoose');
require('dotenv').config();

// Import models
const User = require('../models/User');
const Product = require('../models/Product');
const Wishlist = require('../models/Wishlist');

const baseURL = 'http://localhost:5001/api';

async function testWishlistAPI() {
  try {
    console.log('🧪 Testing Wishlist API...\n');

    // Connect to database
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/phonepoint-dar');
    console.log('✅ Connected to MongoDB');

    // Get a test product
    const product = await Product.findOne();
    if (!product) {
      console.log('❌ No products found in database');
      return;
    }
    console.log('📱 Test product:', product.name, '(ID:', product._id + ')');

    // Get or create a test user
    let user = await User.findOne({ email: '<EMAIL>' });
    if (user) {
      // Delete existing user to recreate with proper password
      await User.deleteOne({ email: '<EMAIL>' });
      console.log('🗑️ Deleted existing test user');
    }

    console.log('👤 Creating fresh test user...');
    user = new User({
      name: 'Test API User',
      email: '<EMAIL>',
      password: 'TestPassword123!', // Meets all validation requirements
      isEmailVerified: true,
      role: 'user'
    });
    await user.save();
    console.log('✅ Test user created with proper password hashing');

    // Test login
    console.log('\n🔐 Testing login...');
    const loginResponse = await axios.post(`${baseURL}/auth/login`, {
      email: '<EMAIL>',
      password: 'TestPassword123!'
    });

    if (!loginResponse.data.success) {
      console.log('❌ Login failed:', loginResponse.data.message);
      return;
    }

    const token = loginResponse.data.token;
    console.log('✅ Login successful, token:', token.substring(0, 20) + '...');

    const headers = {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    };

    // Test get wishlist (should be empty initially)
    console.log('\n📋 Testing get wishlist...');
    const getResponse = await axios.get(`${baseURL}/wishlist`, { headers });
    console.log('✅ Get wishlist response:', {
      success: getResponse.data.success,
      itemCount: getResponse.data.data?.items?.length || 0
    });

    // Test add to wishlist
    console.log('\n➕ Testing add to wishlist...');
    const addResponse = await axios.post(`${baseURL}/wishlist`, 
      { productId: product._id.toString() }, 
      { headers }
    );
    console.log('✅ Add to wishlist response:', {
      success: addResponse.data.success,
      message: addResponse.data.message
    });

    // Test get wishlist again (should have 1 item)
    console.log('\n📋 Testing get wishlist after add...');
    const getResponse2 = await axios.get(`${baseURL}/wishlist`, { headers });
    console.log('✅ Get wishlist response:', {
      success: getResponse2.data.success,
      itemCount: getResponse2.data.data?.items?.length || 0
    });

    // Test remove from wishlist
    console.log('\n➖ Testing remove from wishlist...');
    const removeResponse = await axios.delete(`${baseURL}/wishlist/${product._id}`, { headers });
    console.log('✅ Remove from wishlist response:', {
      success: removeResponse.data.success,
      message: removeResponse.data.message
    });

    // Test get wishlist final (should be empty)
    console.log('\n📋 Testing get wishlist after remove...');
    const getResponse3 = await axios.get(`${baseURL}/wishlist`, { headers });
    console.log('✅ Get wishlist response:', {
      success: getResponse3.data.success,
      itemCount: getResponse3.data.data?.items?.length || 0
    });

    console.log('\n🎉 All wishlist API tests completed successfully!');
    console.log('\n📋 Test credentials for frontend:');
    console.log('📧 Email: <EMAIL>');
    console.log('🔑 Password: TestPassword123!');

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
    if (error.response?.status) {
      console.error('Status:', error.response.status);
      console.error('Headers:', error.response.headers);
    }
  } finally {
    await mongoose.disconnect();
    console.log('📤 Disconnected from MongoDB');
  }
}

// Run the test
if (require.main === module) {
  testWishlistAPI().catch(console.error);
}

module.exports = { testWishlistAPI };
