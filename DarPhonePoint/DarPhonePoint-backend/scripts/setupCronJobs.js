/**
 * <PERSON><PERSON><PERSON> to set up all cron jobs for AIXcelerate
 * Run this script once to set up all the cron jobs
 * 
 * Usage: node scripts/setupCronJobs.js
 */

const fs = require('fs');
const path = require('path');
const { exec } = require('child_process');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

// Define cron jobs
const cronJobs = [
  {
    name: 'Process Email Sequences',
    schedule: '0 * * * *', // Every hour
    command: 'node /path/to/aixcelerate-backend/scripts/processEmailSequences.js',
    description: 'Processes email sequences and sends scheduled emails'
  },
  {
    name: 'Process Inactive Users',
    schedule: '0 9 * * 1', // Every Monday at 9:00 AM
    command: 'node /path/to/aixcelerate-backend/scripts/processInactiveUsers.js',
    description: 'Processes inactive users and triggers re-engagement sequences'
  },
  {
    name: 'Process Course Completions',
    schedule: '0 12 * * *', // Every day at 12:00 PM
    command: 'node /path/to/aixcelerate-backend/scripts/processCourseCompletions.js',
    description: 'Processes course completions and triggers completion sequences'
  }
];

// Get the absolute path to the backend directory
const backendDir = path.resolve(__dirname, '..');

// Update the command paths with the actual backend directory
cronJobs.forEach(job => {
  job.command = job.command.replace('/path/to/aixcelerate-backend', backendDir);
});

// Generate crontab entries
const crontabEntries = cronJobs.map(job => 
  `${job.schedule} ${job.command} >> ${backendDir}/logs/cron.log 2>&1 # ${job.name}`
).join('\n');

// Create logs directory if it doesn't exist
const logsDir = path.join(backendDir, 'logs');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir);
}

// Write crontab entries to a file
const crontabFile = path.join(backendDir, 'crontab.txt');
fs.writeFileSync(crontabFile, crontabEntries + '\n');

console.log('Crontab entries written to:', crontabFile);
console.log('To install the cron jobs, run:');
console.log(`crontab ${crontabFile}`);

// Optionally, install the cron jobs automatically
const installCronJobs = process.env.AUTO_INSTALL_CRON === 'true';

if (installCronJobs) {
  console.log('Installing cron jobs...');
  
  exec(`crontab ${crontabFile}`, (error, stdout, stderr) => {
    if (error) {
      console.error('Error installing cron jobs:', error);
      return;
    }
    
    if (stderr) {
      console.error('Crontab stderr:', stderr);
      return;
    }
    
    console.log('Cron jobs installed successfully!');
    console.log('To view installed cron jobs, run: crontab -l');
  });
} else {
  console.log('Skipping automatic installation of cron jobs.');
  console.log('To install manually, run the command above.');
}
