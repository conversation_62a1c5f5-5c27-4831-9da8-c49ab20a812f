#!/usr/bin/env node

/**
 * 🎯 PRODUCTION CONTENT STANDARDIZATION SCRIPT
 *
 * This script standardizes the product structure for production deployment:
 * 1. Cleans up existing products
 * 2. Creates new standardized products with correct pricing
 * 3. Sets up proper file structure for HTML-to-PDF conversion
 */

const mongoose = require('mongoose');
const fs = require('fs');
const path = require('path');
const config = require('../config/config');

// Import models
const Product = require('../models/Product');

// Connect to MongoDB
mongoose.connect(config.MONGODB_URI, {
  useNewUrlParser: true,
  useUnifiedTopology: true
});

// New standardized product structure
const STANDARDIZED_PRODUCTS = [
  {
    name: 'AI Prompts Starter Pack',
    slug: 'ai-prompts-starter-pack',
    description: 'Get started with AI productivity using our curated collection of 50 essential AI prompts. Perfect for beginners and professionals looking to boost their workflow efficiency.',
    price: 0,
    product_type: 'lead_magnet',
    category: 'AI Tools',
    file_path: '/uploads/products/ai_prompts_starter_pack.pdf',
    html_source_path: '/content/ai_prompts_lead_magnet/',
    features: [
      '50 carefully curated AI prompts',
      'Organized by use case and industry',
      'Copy-paste ready templates',
      'Beginner-friendly explanations',
      'Instant download'
    ],
    tags: ['AI', 'Prompts', 'Productivity', 'Free', 'Starter'],
    is_active: true,
    is_featured: true,
    sort_order: 1,
    meta: {
      seo_title: 'Free AI Prompts Starter Pack - 50 Essential Templates',
      seo_description: 'Download our free collection of 50 essential AI prompts to boost your productivity. Perfect for beginners and professionals.',
      og_image: '/images/products/ai-prompts-starter-pack.jpg'
    }
  },
  {
    name: 'AI Productivity Master Guide',
    slug: 'ai-productivity-master-guide',
    description: 'Master AI productivity with our comprehensive guide. Learn advanced strategies, workflows, and tools to 10x your efficiency and transform your work processes.',
    price: 27.99,
    product_type: 'premium',
    category: 'AI Training',
    file_path: '/uploads/products/ai_productivity_master_guide.pdf',
    html_source_path: '/content/ai_productivity_master_guide/',
    features: [
      'Complete AI productivity framework',
      '100+ advanced prompts and templates',
      'Step-by-step implementation guides',
      'Real-world case studies',
      'Bonus automation scripts',
      'Lifetime updates included'
    ],
    tags: ['AI', 'Productivity', 'Advanced', 'Training', 'Automation'],
    is_active: true,
    is_featured: true,
    sort_order: 2,
    meta: {
      seo_title: 'AI Productivity Master Guide - Advanced Training Course',
      seo_description: 'Master AI productivity with our comprehensive guide. Advanced strategies, workflows, and tools to 10x your efficiency.',
      og_image: '/images/products/ai-productivity-master-guide.jpg'
    }
  },
  {
    name: 'AI Business Revolution Blueprint',
    slug: 'ai-business-revolution-blueprint',
    description: 'Transform your entire business with AI. This comprehensive blueprint covers strategy, implementation, team training, and scaling AI across all business operations.',
    price: 47.99,
    product_type: 'premium',
    category: 'Business Strategy',
    file_path: '/uploads/products/ai_business_revolution_blueprint.pdf',
    html_source_path: '/content/ai_business_revolution/',
    features: [
      'Complete AI business transformation strategy',
      'Implementation roadmap and timelines',
      'Team training materials and resources',
      'ROI calculation frameworks',
      'Risk assessment and mitigation plans',
      'Scaling strategies for growth',
      '1-on-1 consultation call included'
    ],
    tags: ['AI', 'Business', 'Strategy', 'Transformation', 'Enterprise'],
    is_active: true,
    is_featured: true,
    sort_order: 3,
    meta: {
      seo_title: 'AI Business Revolution Blueprint - Complete Transformation Guide',
      seo_description: 'Transform your business with AI. Complete blueprint for strategy, implementation, and scaling AI across operations.',
      og_image: '/images/products/ai-business-revolution-blueprint.jpg'
    }
  }
];

/**
 * Create directory structure for HTML content
 */
async function createContentDirectories() {
  const contentDir = path.join(__dirname, '..', 'content');

  const directories = [
    'ai_prompts_lead_magnet',
    'ai_productivity_master_guide',
    'ai_business_revolution'
  ];

  // Create main content directory
  if (!fs.existsSync(contentDir)) {
    fs.mkdirSync(contentDir, { recursive: true });
    console.log('✅ Created content directory');
  }

  // Create subdirectories for each product
  for (const dir of directories) {
    const fullPath = path.join(contentDir, dir);
    if (!fs.existsSync(fullPath)) {
      fs.mkdirSync(fullPath, { recursive: true });
      console.log(`✅ Created directory: ${dir}`);

      // Create sample HTML files
      const sampleHTML = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${dir.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; margin: 40px; }
        h1 { color: #2c3e50; border-bottom: 3px solid #3498db; padding-bottom: 10px; }
        h2 { color: #34495e; margin-top: 30px; }
        .highlight { background-color: #f39c12; color: white; padding: 2px 6px; border-radius: 3px; }
        .note { background-color: #ecf0f1; padding: 15px; border-left: 4px solid #3498db; margin: 20px 0; }
    </style>
</head>
<body>
    <h1>${dir.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}</h1>
    <p>This is a sample HTML template for <span class="highlight">${dir}</span>.</p>
    <div class="note">
        <strong>Note:</strong> Replace this content with your actual product content.
    </div>
    <h2>Getting Started</h2>
    <p>Add your content here...</p>
</body>
</html>`;

      fs.writeFileSync(path.join(fullPath, 'index.html'), sampleHTML);
      console.log(`✅ Created sample HTML for: ${dir}`);
    }
  }
}

/**
 * Clean up existing products and create new ones
 */
async function standardizeProducts() {
  try {
    console.log('🧹 Cleaning up existing products...');

    // Remove all existing products
    await Product.deleteMany({});
    console.log('✅ Removed all existing products');

    console.log('🎯 Creating standardized products...');

    // Create new standardized products
    for (const productData of STANDARDIZED_PRODUCTS) {
      const product = new Product(productData);
      await product.save();
      console.log(`✅ Created product: ${product.name} - $${product.price}`);
    }

    console.log('🎉 Product standardization completed successfully!');

  } catch (error) {
    console.error('❌ Error during product standardization:', error);
    throw error;
  }
}

/**
 * Main execution function
 */
async function main() {
  try {
    console.log('🚀 Starting Production Content Standardization...');
    console.log('=' * 60);

    // Step 1: Create content directories
    await createContentDirectories();

    // Step 2: Standardize products
    await standardizeProducts();

    console.log('=' * 60);
    console.log('✅ Production content standardization completed!');
    console.log('');
    console.log('📋 Next Steps:');
    console.log('1. Add your HTML content to the /content directories');
    console.log('2. Use the admin dashboard to convert HTML to PDF');
    console.log('3. Test the download system');
    console.log('');

    // Disconnect from MongoDB
    await mongoose.disconnect();
    process.exit(0);

  } catch (error) {
    console.error('💥 Fatal error:', error);
    await mongoose.disconnect();
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = { standardizeProducts, createContentDirectories };
