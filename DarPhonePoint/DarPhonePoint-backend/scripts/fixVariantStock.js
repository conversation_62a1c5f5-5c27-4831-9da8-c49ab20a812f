const mongoose = require('mongoose');
const Product = require('../models/Product');

// Connect to database
mongoose.connect('mongodb://localhost:27017/aixcelerate-dev');

async function fixVariantStock() {
  try {
    console.log('🔧 Fixing variant stock quantities...');
    
    // Get all products with variants
    const products = await Product.find({
      variants: { $exists: true, $ne: [] }
    });
    
    console.log(`Found ${products.length} products with variants`);
    
    for (const product of products) {
      console.log(`\n📱 Processing: ${product.name}`);
      console.log(`   Product stock: ${product.stock_quantity}`);
      console.log(`   Variants: ${product.variants.length}`);
      
      if (product.stock_quantity > 0 && product.variants.length > 0) {
        // Distribute stock evenly among variants
        const stockPerVariant = Math.floor(product.stock_quantity / product.variants.length);
        const remainderStock = product.stock_quantity % product.variants.length;
        
        for (let i = 0; i < product.variants.length; i++) {
          const variant = product.variants[i];
          // Give each variant equal stock, with remainder going to first variants
          variant.stock_quantity = stockPerVariant + (i < remainderStock ? 1 : 0);
          console.log(`   ${variant.name}: ${variant.stock_quantity} units`);
        }
        
        // Save the updated product
        await product.save();
        console.log(`   ✅ Updated variant stock for ${product.name}`);
      } else {
        console.log(`   ⚠️  Skipping ${product.name} (no stock or no variants)`);
      }
    }
    
    console.log('\n🎉 Variant stock fixing completed!');
    
    // Verify the fix
    console.log('\n🔍 Verification:');
    const updatedProducts = await Product.find({
      variants: { $exists: true, $ne: [] }
    });
    
    for (const product of updatedProducts) {
      const totalVariantStock = product.variants.reduce((sum, variant) => sum + (variant.stock_quantity || 0), 0);
      const inStock = product.variants.some(variant => variant.stock_quantity > 0 && variant.is_active);
      
      console.log(`${product.name}:`);
      console.log(`   Product stock: ${product.stock_quantity}`);
      console.log(`   Total variant stock: ${totalVariantStock}`);
      console.log(`   In stock: ${inStock}`);
    }
    
    process.exit(0);
    
  } catch (error) {
    console.error('❌ Error fixing variant stock:', error);
    process.exit(1);
  }
}

fixVariantStock();
