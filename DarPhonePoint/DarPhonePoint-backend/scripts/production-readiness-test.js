#!/usr/bin/env node

/**
 * Production Readiness Test Script
 * 
 * Comprehensive testing of all critical systems before production deployment
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');

const API_BASE = 'http://localhost:5001/api';
const FRONTEND_BASE = 'http://localhost:5173';

// Test configuration
const TEST_CONFIG = {
  timeout: 10000,
  retries: 3
};

// Test results
let testResults = {
  passed: 0,
  failed: 0,
  warnings: 0,
  tests: []
};

// Helper functions
const log = (message, type = 'info') => {
  const timestamp = new Date().toISOString();
  const colors = {
    info: '\x1b[36m',    // Cyan
    success: '\x1b[32m', // Green
    warning: '\x1b[33m', // Yellow
    error: '\x1b[31m',   // Red
    reset: '\x1b[0m'     // Reset
  };
  
  console.log(`${colors[type]}[${timestamp}] ${message}${colors.reset}`);
};

const test = async (name, testFn, critical = true) => {
  try {
    log(`Testing: ${name}`, 'info');
    const result = await testFn();
    
    if (result.success) {
      testResults.passed++;
      testResults.tests.push({ name, status: 'PASSED', critical, details: result.details });
      log(`✅ ${name} - PASSED`, 'success');
    } else {
      if (critical) {
        testResults.failed++;
        testResults.tests.push({ name, status: 'FAILED', critical, details: result.details });
        log(`❌ ${name} - FAILED: ${result.details}`, 'error');
      } else {
        testResults.warnings++;
        testResults.tests.push({ name, status: 'WARNING', critical, details: result.details });
        log(`⚠️  ${name} - WARNING: ${result.details}`, 'warning');
      }
    }
  } catch (error) {
    if (critical) {
      testResults.failed++;
      testResults.tests.push({ name, status: 'FAILED', critical, error: error.message });
      log(`❌ ${name} - FAILED: ${error.message}`, 'error');
    } else {
      testResults.warnings++;
      testResults.tests.push({ name, status: 'WARNING', critical, error: error.message });
      log(`⚠️  ${name} - WARNING: ${error.message}`, 'warning');
    }
  }
};

// Test functions
const testBackendHealth = async () => {
  try {
    const response = await axios.get(`${API_BASE}/health`, { timeout: TEST_CONFIG.timeout });
    return {
      success: response.status === 200 && response.data.status === 'healthy',
      details: `Response time: ${response.headers['x-response-time'] || 'N/A'}`
    };
  } catch (error) {
    return { success: false, details: error.message };
  }
};

const testFrontendConnectivity = async () => {
  try {
    const response = await axios.get(FRONTEND_BASE, { timeout: TEST_CONFIG.timeout });
    return {
      success: response.status === 200,
      details: `Frontend accessible on port 5173`
    };
  } catch (error) {
    return { success: false, details: error.message };
  }
};

const testAuthentication = async () => {
  try {
    const response = await axios.post(`${API_BASE}/auth/login`, {
      email: '<EMAIL>',
      password: 'Admin123!@#'
    }, { timeout: TEST_CONFIG.timeout });
    
    return {
      success: response.status === 200 && response.data.token,
      details: `JWT token generated successfully`
    };
  } catch (error) {
    return { success: false, details: error.message };
  }
};

const testProductsAPI = async () => {
  try {
    const response = await axios.get(`${API_BASE}/products`, { timeout: TEST_CONFIG.timeout });
    const products = response.data.data || response.data;
    
    return {
      success: response.status === 200 && Array.isArray(products) && products.length > 0,
      details: `Found ${products.length} products`
    };
  } catch (error) {
    return { success: false, details: error.message };
  }
};

const testFileSystem = async () => {
  const requiredDirs = [
    'public/uploads/products',
    'public/products'
  ];
  
  const requiredFiles = [
    'public/uploads/products/50_Essential_AI_Prompts.pdf',
    'public/uploads/products/Free_AI_Starter_Guide.pdf',
    'public/uploads/products/ai_productivity_course.zip'
  ];
  
  let missingItems = [];
  
  // Check directories
  for (const dir of requiredDirs) {
    if (!fs.existsSync(dir)) {
      missingItems.push(`Directory: ${dir}`);
    }
  }
  
  // Check files
  for (const file of requiredFiles) {
    if (!fs.existsSync(file)) {
      missingItems.push(`File: ${file}`);
    }
  }
  
  return {
    success: missingItems.length === 0,
    details: missingItems.length > 0 ? `Missing: ${missingItems.join(', ')}` : 'All files and directories present'
  };
};

const testSecureDownload = async () => {
  try {
    // First get auth token
    const authResponse = await axios.post(`${API_BASE}/auth/login`, {
      email: '<EMAIL>',
      password: 'Admin123!@#'
    });
    
    const token = authResponse.data.token;
    
    // Test download URL generation
    const downloadResponse = await axios.get(`${API_BASE}/downloads/6831d631721c343f2f6bae61`, {
      headers: { Authorization: `Bearer ${token}` },
      timeout: TEST_CONFIG.timeout
    });
    
    const downloadUrl = downloadResponse.data.downloadUrl;
    const isSecure = downloadUrl.includes('/api/secure-download/');
    
    return {
      success: downloadResponse.status === 200 && isSecure,
      details: isSecure ? 'Secure download URLs generated' : 'Using old mock system'
    };
  } catch (error) {
    return { success: false, details: error.message };
  }
};

const testEmailSystem = async () => {
  try {
    const response = await axios.post(`${API_BASE}/leads`, {
      email: '<EMAIL>',
      name: 'Production Test',
      source: 'production_test'
    }, { timeout: TEST_CONFIG.timeout });
    
    return {
      success: response.status === 200,
      details: 'Email system responding (check logs for actual delivery)'
    };
  } catch (error) {
    return { success: false, details: error.message };
  }
};

const testPaymentSystem = async () => {
  try {
    // First get auth token
    const authResponse = await axios.post(`${API_BASE}/auth/login`, {
      email: '<EMAIL>',
      password: 'Admin123!@#'
    });
    
    const token = authResponse.data.token;
    
    // Test payment intent creation
    const paymentResponse = await axios.post(`${API_BASE}/stripe/payment-intent`, {
      amount: 2999,
      currency: 'usd'
    }, {
      headers: { Authorization: `Bearer ${token}` },
      timeout: TEST_CONFIG.timeout
    });
    
    return {
      success: paymentResponse.status === 200 && paymentResponse.data.success,
      details: 'Stripe payment intent creation working'
    };
  } catch (error) {
    return { success: false, details: error.message };
  }
};

const testDatabaseConnectivity = async () => {
  try {
    const response = await axios.get(`${API_BASE}/analytics/dashboard-stats`, { timeout: TEST_CONFIG.timeout });
    
    return {
      success: response.status === 200,
      details: 'Database queries executing successfully'
    };
  } catch (error) {
    return { success: false, details: error.message };
  }
};

// Main test runner
async function runTests() {
  log('🚀 Starting Production Readiness Tests', 'info');
  log('=' * 50, 'info');
  
  // Critical tests (must pass for production)
  await test('Backend Health Check', testBackendHealth, true);
  await test('Frontend Connectivity', testFrontendConnectivity, true);
  await test('Authentication System', testAuthentication, true);
  await test('Products API', testProductsAPI, true);
  await test('File System Structure', testFileSystem, true);
  await test('Secure Download System', testSecureDownload, true);
  await test('Database Connectivity', testDatabaseConnectivity, true);
  
  // Important tests (warnings if failed)
  await test('Email System', testEmailSystem, false);
  await test('Payment System', testPaymentSystem, false);
  
  // Generate report
  log('=' * 50, 'info');
  log('📊 TEST RESULTS SUMMARY', 'info');
  log('=' * 50, 'info');
  
  log(`✅ Passed: ${testResults.passed}`, 'success');
  log(`❌ Failed: ${testResults.failed}`, testResults.failed > 0 ? 'error' : 'info');
  log(`⚠️  Warnings: ${testResults.warnings}`, testResults.warnings > 0 ? 'warning' : 'info');
  
  // Detailed results
  log('\n📋 DETAILED RESULTS:', 'info');
  testResults.tests.forEach(test => {
    const status = test.status === 'PASSED' ? '✅' : test.status === 'FAILED' ? '❌' : '⚠️';
    const critical = test.critical ? '[CRITICAL]' : '[OPTIONAL]';
    log(`${status} ${test.name} ${critical} - ${test.details || test.error}`, 'info');
  });
  
  // Production readiness verdict
  log('\n🎯 PRODUCTION READINESS VERDICT:', 'info');
  if (testResults.failed === 0) {
    log('🎉 SYSTEM IS PRODUCTION READY! 🎉', 'success');
    log('All critical tests passed. You can deploy to production.', 'success');
    if (testResults.warnings > 0) {
      log(`Note: ${testResults.warnings} non-critical warnings should be addressed when possible.`, 'warning');
    }
  } else {
    log('🚫 SYSTEM NOT READY FOR PRODUCTION', 'error');
    log(`${testResults.failed} critical tests failed. Fix these issues before deployment.`, 'error');
  }
  
  // Save results to file
  const reportPath = path.join(__dirname, '..', 'production-readiness-report.json');
  fs.writeFileSync(reportPath, JSON.stringify({
    timestamp: new Date().toISOString(),
    summary: {
      passed: testResults.passed,
      failed: testResults.failed,
      warnings: testResults.warnings,
      productionReady: testResults.failed === 0
    },
    tests: testResults.tests
  }, null, 2));
  
  log(`\n📄 Full report saved to: ${reportPath}`, 'info');
  
  process.exit(testResults.failed > 0 ? 1 : 0);
}

// Run tests
runTests().catch(error => {
  log(`Fatal error: ${error.message}`, 'error');
  process.exit(1);
});
