#!/usr/bin/env node

/**
 * ClickPesa Webhook Testing Script
 * Tests webhook processing, security, and signature verification
 */

require('dotenv').config();
const axios = require('axios');
const crypto = require('crypto');
const logger = require('../utils/logger');

// Configuration
const WEBHOOK_CONFIG = {
  baseUrl: process.env.BASE_URL || 'http://localhost:5001',
  webhookSecret: process.env.CLICKPESA_WEBHOOK_SECRET || 'test_webhook_secret',
  testOrderId: null // Will be set during test
};

/**
 * Generate webhook signature
 */
function generateWebhookSignature(payload, secret) {
  return crypto
    .createHmac('sha256', secret)
    .update(JSON.stringify(payload))
    .digest('hex');
}

/**
 * Create test order for webhook testing
 */
async function createTestOrder() {
  console.log('\n📦 Creating test order for webhook testing...');
  
  try {
    // This would normally be done through the API, but for testing we'll simulate
    const testOrder = {
      _id: `test_order_${Date.now()}`,
      transaction_id: `PPD-TEST-${Date.now()}`,
      total: 1500000, // 1.5M TZS
      payment_method: 'mpesa',
      payment_status: 'pending',
      customer_email: '<EMAIL>',
      customer_name: 'Test Customer'
    };
    
    WEBHOOK_CONFIG.testOrderId = testOrder.transaction_id;
    
    console.log('✅ Test order created:');
    console.log(`   Order ID: ${testOrder._id}`);
    console.log(`   Transaction ID: ${testOrder.transaction_id}`);
    console.log(`   Amount: ${testOrder.total} TZS`);
    
    return testOrder;
  } catch (error) {
    console.log('❌ Failed to create test order:', error.message);
    return null;
  }
}

/**
 * Test webhook with valid signature
 */
async function testValidWebhook() {
  console.log('\n✅ Testing webhook with valid signature...');
  console.log('==========================================');
  
  try {
    const webhookPayload = {
      request_id: WEBHOOK_CONFIG.testOrderId,
      transaction_id: `TZ${Date.now()}`,
      status: 'completed',
      amount: 1500000,
      currency: 'TZS',
      payment_method: 'mpesa',
      customer_phone: '255123456789',
      timestamp: new Date().toISOString(),
      reference: 'Test payment completion'
    };
    
    // Generate valid signature
    const signature = generateWebhookSignature(webhookPayload, WEBHOOK_CONFIG.webhookSecret);
    
    console.log('📤 Sending webhook with valid signature...');
    console.log(`   Payload: ${JSON.stringify(webhookPayload, null, 2)}`);
    console.log(`   Signature: ${signature}`);
    
    const response = await axios.post(
      `${WEBHOOK_CONFIG.baseUrl}/api/payments/clickpesa/callback`,
      webhookPayload,
      {
        headers: {
          'Content-Type': 'application/json',
          'X-ClickPesa-Signature': signature,
          'User-Agent': 'ClickPesa-Webhook/1.0'
        },
        timeout: 10000
      }
    );
    
    if (response.status === 200) {
      console.log('✅ Valid webhook processed successfully!');
      console.log(`📊 Response: ${JSON.stringify(response.data, null, 2)}`);
      return true;
    } else {
      console.log(`❌ Unexpected response status: ${response.status}`);
      return false;
    }
  } catch (error) {
    console.log('\n❌ Valid webhook test failed!');
    
    if (error.response) {
      console.log('📄 Error Response:');
      console.log(`   Status: ${error.response.status}`);
      console.log(`   Data: ${JSON.stringify(error.response.data, null, 2)}`);
    } else if (error.request) {
      console.log('📡 Network Error: No response received');
      console.log('   Check if server is running and webhook endpoint is accessible');
    } else {
      console.log('⚙️ Error:', error.message);
    }
    
    return false;
  }
}

/**
 * Test webhook with invalid signature
 */
async function testInvalidWebhook() {
  console.log('\n❌ Testing webhook with invalid signature...');
  console.log('============================================');
  
  try {
    const webhookPayload = {
      request_id: WEBHOOK_CONFIG.testOrderId,
      transaction_id: `TZ${Date.now()}`,
      status: 'completed',
      amount: 1500000,
      currency: 'TZS',
      payment_method: 'mpesa',
      timestamp: new Date().toISOString()
    };
    
    // Generate invalid signature
    const invalidSignature = 'invalid_signature_12345';
    
    console.log('📤 Sending webhook with invalid signature...');
    console.log(`   Invalid Signature: ${invalidSignature}`);
    
    const response = await axios.post(
      `${WEBHOOK_CONFIG.baseUrl}/api/payments/clickpesa/callback`,
      webhookPayload,
      {
        headers: {
          'Content-Type': 'application/json',
          'X-ClickPesa-Signature': invalidSignature,
          'User-Agent': 'ClickPesa-Webhook/1.0'
        },
        timeout: 10000
      }
    );
    
    // Should not reach here if security is working
    console.log('❌ Invalid webhook was accepted! Security issue detected.');
    console.log(`📊 Response: ${JSON.stringify(response.data, null, 2)}`);
    return false;
    
  } catch (error) {
    if (error.response && error.response.status === 401) {
      console.log('✅ Invalid webhook correctly rejected!');
      console.log(`📄 Response: ${JSON.stringify(error.response.data, null, 2)}`);
      return true;
    } else {
      console.log('❌ Unexpected error during invalid webhook test');
      console.log('📄 Error:', error.message);
      return false;
    }
  }
}

/**
 * Test webhook without signature
 */
async function testWebhookWithoutSignature() {
  console.log('\n🚫 Testing webhook without signature...');
  console.log('======================================');
  
  try {
    const webhookPayload = {
      request_id: WEBHOOK_CONFIG.testOrderId,
      transaction_id: `TZ${Date.now()}`,
      status: 'completed',
      amount: 1500000,
      currency: 'TZS'
    };
    
    console.log('📤 Sending webhook without signature header...');
    
    const response = await axios.post(
      `${WEBHOOK_CONFIG.baseUrl}/api/payments/clickpesa/callback`,
      webhookPayload,
      {
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'ClickPesa-Webhook/1.0'
          // No signature header
        },
        timeout: 10000
      }
    );
    
    // In development mode, this might be accepted
    console.log('⚠️ Webhook without signature was accepted (development mode)');
    console.log(`📊 Response: ${JSON.stringify(response.data, null, 2)}`);
    return true;
    
  } catch (error) {
    if (error.response && error.response.status === 401) {
      console.log('✅ Webhook without signature correctly rejected!');
      return true;
    } else {
      console.log('❌ Unexpected error during no-signature test');
      console.log('📄 Error:', error.message);
      return false;
    }
  }
}

/**
 * Test malformed webhook payload
 */
async function testMalformedWebhook() {
  console.log('\n🔧 Testing malformed webhook payload...');
  console.log('======================================');
  
  try {
    const malformedPayload = {
      // Missing required fields
      status: 'completed',
      amount: 'invalid_amount', // Should be number
      // Missing request_id
    };
    
    const signature = generateWebhookSignature(malformedPayload, WEBHOOK_CONFIG.webhookSecret);
    
    console.log('📤 Sending malformed webhook payload...');
    
    const response = await axios.post(
      `${WEBHOOK_CONFIG.baseUrl}/api/payments/clickpesa/callback`,
      malformedPayload,
      {
        headers: {
          'Content-Type': 'application/json',
          'X-ClickPesa-Signature': signature,
          'User-Agent': 'ClickPesa-Webhook/1.0'
        },
        timeout: 10000
      }
    );
    
    console.log('❌ Malformed webhook was accepted! Validation issue detected.');
    return false;
    
  } catch (error) {
    if (error.response && error.response.status === 400) {
      console.log('✅ Malformed webhook correctly rejected!');
      console.log(`📄 Response: ${JSON.stringify(error.response.data, null, 2)}`);
      return true;
    } else {
      console.log('❌ Unexpected error during malformed webhook test');
      console.log('📄 Error:', error.message);
      return false;
    }
  }
}

/**
 * Test webhook endpoint availability
 */
async function testWebhookEndpoint() {
  console.log('\n🌐 Testing webhook endpoint availability...');
  console.log('==========================================');
  
  try {
    // Test with GET request (should return method not allowed)
    const response = await axios.get(
      `${WEBHOOK_CONFIG.baseUrl}/api/payments/clickpesa/callback`,
      { timeout: 5000 }
    );
    
    console.log('❌ Webhook endpoint accepts GET requests (security issue)');
    return false;
    
  } catch (error) {
    if (error.response && error.response.status === 405) {
      console.log('✅ Webhook endpoint correctly rejects GET requests');
      return true;
    } else if (error.response && error.response.status === 404) {
      console.log('❌ Webhook endpoint not found (404)');
      return false;
    } else {
      console.log('⚠️ Unexpected response during endpoint test');
      console.log('📄 Error:', error.message);
      return false;
    }
  }
}

/**
 * Main test function
 */
async function runWebhookTests() {
  console.log('🔗 ClickPesa Webhook Security Test Suite');
  console.log('========================================');
  console.log('Phone Point Dar - Webhook Testing & Security Validation');
  console.log(`Timestamp: ${new Date().toISOString()}\n`);
  
  console.log('📋 Configuration:');
  console.log(`   Base URL: ${WEBHOOK_CONFIG.baseUrl}`);
  console.log(`   Webhook Secret: ${WEBHOOK_CONFIG.webhookSecret ? '✅ Configured' : '❌ Missing'}`);
  console.log(`   Environment: ${process.env.NODE_ENV || 'development'}`);
  
  let allTestsPassed = true;
  const results = {};
  
  // Test 1: Create test order
  const testOrder = await createTestOrder();
  if (!testOrder) {
    console.log('\n❌ Cannot proceed without test order');
    process.exit(1);
  }
  
  // Test 2: Endpoint availability
  results.endpointAvailable = await testWebhookEndpoint();
  if (!results.endpointAvailable) allTestsPassed = false;
  
  // Test 3: Valid webhook
  results.validWebhook = await testValidWebhook();
  if (!results.validWebhook) allTestsPassed = false;
  
  // Test 4: Invalid signature
  results.invalidSignature = await testInvalidWebhook();
  if (!results.invalidSignature) allTestsPassed = false;
  
  // Test 5: No signature
  results.noSignature = await testWebhookWithoutSignature();
  if (!results.noSignature) allTestsPassed = false;
  
  // Test 6: Malformed payload
  results.malformedPayload = await testMalformedWebhook();
  if (!results.malformedPayload) allTestsPassed = false;
  
  // Summary
  console.log('\n📋 Webhook Security Test Summary');
  console.log('================================');
  console.log(`Endpoint Available: ${results.endpointAvailable ? '✅ PASSED' : '❌ FAILED'}`);
  console.log(`Valid Webhook: ${results.validWebhook ? '✅ PASSED' : '❌ FAILED'}`);
  console.log(`Invalid Signature Rejected: ${results.invalidSignature ? '✅ PASSED' : '❌ FAILED'}`);
  console.log(`No Signature Handled: ${results.noSignature ? '✅ PASSED' : '❌ FAILED'}`);
  console.log(`Malformed Payload Rejected: ${results.malformedPayload ? '✅ PASSED' : '❌ FAILED'}`);
  console.log(`Overall Security Status: ${allTestsPassed ? '✅ SECURE' : '❌ VULNERABILITIES DETECTED'}`);
  
  if (allTestsPassed) {
    console.log('\n🎉 All webhook security tests passed!');
    console.log('Your ClickPesa webhook integration is secure and working correctly.');
  } else {
    console.log('\n⚠️ Some webhook security tests failed.');
    console.log('Please review the issues above and fix security vulnerabilities.');
  }
  
  process.exit(allTestsPassed ? 0 : 1);
}

// Run tests if script is executed directly
if (require.main === module) {
  runWebhookTests().catch(error => {
    console.error('💥 Webhook test suite crashed:', error);
    process.exit(1);
  });
}

module.exports = {
  testValidWebhook,
  testInvalidWebhook,
  testWebhookWithoutSignature,
  testMalformedWebhook,
  runWebhookTests
};
