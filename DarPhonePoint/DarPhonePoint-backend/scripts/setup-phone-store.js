const mongoose = require('mongoose');
const config = require('../config/config');
const setupPhoneCategories = require('./setup-phone-categories');
const setupBrands = require('./setup-brands');

// Common phone specifications templates
const phoneSpecifications = {
  smartphone: [
    { name: 'Display Size', category: 'display' },
    { name: 'Display Type', category: 'display' },
    { name: 'Resolution', category: 'display' },
    { name: 'Refresh Rate', category: 'display' },
    { name: 'Processor', category: 'performance' },
    { name: 'RAM', category: 'performance' },
    { name: 'Storage', category: 'performance' },
    { name: 'Operating System', category: 'software' },
    { name: 'Main Camera', category: 'camera' },
    { name: 'Front Camera', category: 'camera' },
    { name: 'Video Recording', category: 'camera' },
    { name: 'Battery Capacity', category: 'battery' },
    { name: 'Charging Speed', category: 'battery' },
    { name: 'Wireless Charging', category: 'battery' },
    { name: '5G Support', category: 'connectivity' },
    { name: 'WiFi', category: 'connectivity' },
    { name: 'Bluetooth', category: 'connectivity' },
    { name: 'NFC', category: 'connectivity' },
    { name: 'Water Resistance', category: 'build' },
    { name: 'Build Material', category: 'build' },
    { name: 'Weight', category: 'dimensions' },
    { name: 'Dimensions', category: 'dimensions' }
  ],
  tablet: [
    { name: 'Display Size', category: 'display' },
    { name: 'Display Type', category: 'display' },
    { name: 'Resolution', category: 'display' },
    { name: 'Processor', category: 'performance' },
    { name: 'RAM', category: 'performance' },
    { name: 'Storage', category: 'performance' },
    { name: 'Operating System', category: 'software' },
    { name: 'Main Camera', category: 'camera' },
    { name: 'Front Camera', category: 'camera' },
    { name: 'Battery Capacity', category: 'battery' },
    { name: 'Charging Speed', category: 'battery' },
    { name: 'WiFi', category: 'connectivity' },
    { name: 'Cellular', category: 'connectivity' },
    { name: 'Bluetooth', category: 'connectivity' },
    { name: 'Weight', category: 'dimensions' },
    { name: 'Dimensions', category: 'dimensions' }
  ],
  smartwatch: [
    { name: 'Display Size', category: 'display' },
    { name: 'Display Type', category: 'display' },
    { name: 'Operating System', category: 'software' },
    { name: 'Processor', category: 'performance' },
    { name: 'Storage', category: 'performance' },
    { name: 'Battery Life', category: 'battery' },
    { name: 'Water Resistance', category: 'build' },
    { name: 'GPS', category: 'connectivity' },
    { name: 'WiFi', category: 'connectivity' },
    { name: 'Bluetooth', category: 'connectivity' },
    { name: 'Cellular', category: 'connectivity' },
    { name: 'Health Sensors', category: 'health' },
    { name: 'Fitness Tracking', category: 'health' },
    { name: 'Weight', category: 'dimensions' },
    { name: 'Band Material', category: 'build' }
  ],
  earbuds: [
    { name: 'Driver Size', category: 'audio' },
    { name: 'Frequency Response', category: 'audio' },
    { name: 'Noise Cancellation', category: 'audio' },
    { name: 'Battery Life (Earbuds)', category: 'battery' },
    { name: 'Battery Life (Case)', category: 'battery' },
    { name: 'Charging Time', category: 'battery' },
    { name: 'Wireless Charging', category: 'battery' },
    { name: 'Bluetooth Version', category: 'connectivity' },
    { name: 'Codec Support', category: 'connectivity' },
    { name: 'Water Resistance', category: 'build' },
    { name: 'Weight (Single Earbud)', category: 'dimensions' },
    { name: 'Weight (Case)', category: 'dimensions' }
  ]
};

// Common color options for phones
const commonColors = [
  'Black', 'White', 'Silver', 'Gold', 'Rose Gold', 'Space Gray', 'Midnight',
  'Blue', 'Red', 'Green', 'Purple', 'Pink', 'Yellow', 'Orange',
  'Graphite', 'Sierra Blue', 'Alpine Green', 'Deep Purple', 'Starlight'
];

// Common storage options
const storageOptions = [
  '64GB', '128GB', '256GB', '512GB', '1TB', '2TB'
];

// Common RAM options
const ramOptions = [
  '4GB', '6GB', '8GB', '12GB', '16GB', '18GB'
];

async function setupPhoneStore() {
  try {
    console.log('🚀 Starting Phone Point Dar store setup...\n');

    // Connect to MongoDB
    await mongoose.connect(config.MONGO_URI);
    console.log('✅ Connected to MongoDB\n');

    // Setup categories
    console.log('📁 Setting up product categories...');
    await setupPhoneCategories();
    console.log('✅ Product categories setup completed\n');

    // Setup brands
    console.log('🏷️  Setting up brands...');
    await setupBrands();
    console.log('✅ Brands setup completed\n');

    console.log('🎉 Phone Point Dar store setup completed successfully!');
    console.log('\n📋 Setup Summary:');
    console.log('   ✅ Product categories created');
    console.log('   ✅ Phone brands created');
    console.log('   ✅ Database models updated for physical products');
    console.log('\n🔄 Next steps:');
    console.log('   1. Run sample product creation script');
    console.log('   2. Update frontend components for phone store');
    console.log('   3. Test the new product catalog functionality');
    
  } catch (error) {
    console.error('❌ Error setting up phone store:', error);
    process.exit(1);
  } finally {
    await mongoose.disconnect();
    console.log('\n🔌 Disconnected from MongoDB');
  }
}

// Export specification templates for use in other scripts
module.exports = {
  setupPhoneStore,
  phoneSpecifications,
  commonColors,
  storageOptions,
  ramOptions
};

// Run the setup if this file is executed directly
if (require.main === module) {
  setupPhoneStore();
}
