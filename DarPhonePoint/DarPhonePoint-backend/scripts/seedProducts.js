const mongoose = require('mongoose');
const config = require('../config/config');
const Product = require('../models/Product');

// Connect to MongoDB
mongoose.connect(config.MONGODB_URI, {
  useNewUrlParser: true,
  useUnifiedTopology: true
})
.then(() => console.log('MongoDB Connected...'))
.catch(err => {
  console.error('MongoDB connection error:', err);
  process.exit(1);
});

// Initial products data
const productsData = [
  {
    name: 'AI Prompts Lead Magnet',
    slug: 'ai-prompts-lead-magnet',
    description: 'A comprehensive collection of AI prompts to boost your productivity and creativity. This lead magnet provides valuable prompts for various AI tools and platforms.',
    price: 0,
    product_type: 'lead_magnet',
    file_path: 'products/ai_prompts_lead_magnet.pdf',
    features: [
      'Over 100 ready-to-use AI prompts',
      'Categorized by use case and platform',
      'Beginner-friendly explanations',
      'Regular updates with new prompts'
    ],
    is_active: true
  },
  {
    name: 'AI Productivity Master Guide',
    slug: 'ai-productivity-master-guide',
    description: 'Master the art of AI-powered productivity with this comprehensive guide. Learn how to leverage AI tools to automate tasks, enhance creativity, and streamline your workflow.',
    price: 27.99,
    product_type: 'basic',
    file_path: 'products/ai_productivity_master_guide.pdf',
    features: [
      'Step-by-step AI productivity workflows',
      'Tool recommendations and comparisons',
      'Case studies and success stories',
      'Exclusive templates and frameworks'
    ],
    is_active: true
  },
  {
    name: 'AI Business Revolution',
    slug: 'ai-business-revolution',
    description: 'Transform your business with the power of AI. This comprehensive guide shows you how to implement AI solutions across your organization to drive growth, efficiency, and innovation.',
    price: 47.99,
    product_type: 'premium',
    file_path: 'products/ai_business_revolution.pdf',
    features: [
      'AI implementation strategies for businesses',
      'ROI calculations and cost-benefit analysis',
      'Industry-specific AI applications',
      'Future-proofing your business with AI'
    ],
    is_active: true
  }
];

// Seed function
const seedProducts = async () => {
  try {
    // Clear existing products
    await Product.deleteMany({});
    console.log('Existing products cleared');

    // Insert new products
    const createdProducts = await Product.insertMany(productsData);
    console.log(`${createdProducts.length} products seeded successfully`);

    // Log product details
    createdProducts.forEach(product => {
      console.log(`- ${product.name} (${product.product_type}) - $${product.price}`);
    });

    mongoose.disconnect();
    console.log('Database connection closed');
  } catch (error) {
    console.error('Error seeding products:', error);
    mongoose.disconnect();
  }
};

// Run the seed function
seedProducts();