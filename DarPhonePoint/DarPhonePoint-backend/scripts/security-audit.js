#!/usr/bin/env node

/**
 * Security Audit Script for AIXcelerate Backend
 * Identifies and fixes security vulnerabilities
 */

const fs = require('fs').promises;
const path = require('path');
const crypto = require('crypto');

class SecurityAuditor {
  constructor() {
    this.issues = [];
    this.warnings = [];
    this.fixes = [];
    this.sensitivePatterns = [
      /api[_-]?key/i,
      /secret/i,
      /password/i,
      /token/i,
      /private[_-]?key/i,
      /auth[_-]?token/i,
      /access[_-]?token/i,
      /refresh[_-]?token/i,
      /webhook[_-]?secret/i,
      /encryption[_-]?key/i,
      /jwt[_-]?secret/i,
      /session[_-]?secret/i
    ];
  }

  /**
   * Run comprehensive security audit
   */
  async runAudit() {
    console.log('🔒 Starting Security Audit for AIXcelerate Backend\n');

    try {
      // 1. Check environment variables
      await this.auditEnvironmentVariables();
      
      // 2. Check configuration files
      await this.auditConfigurationFiles();
      
      // 3. Check API endpoints for sensitive data exposure
      await this.auditAPIEndpoints();
      
      // 4. Check file permissions
      await this.auditFilePermissions();
      
      // 5. Check dependencies for vulnerabilities
      await this.auditDependencies();
      
      // 6. Generate security report
      this.generateSecurityReport();
      
    } catch (error) {
      console.error('Security audit failed:', error);
      process.exit(1);
    }
  }

  /**
   * Audit environment variables
   */
  async auditEnvironmentVariables() {
    console.log('📋 Auditing Environment Variables...');
    
    const envFile = path.join(process.cwd(), '.env');
    
    try {
      const envContent = await fs.readFile(envFile, 'utf8');
      const lines = envContent.split('\n');
      
      for (let i = 0; i < lines.length; i++) {
        const line = lines[i].trim();
        if (line && !line.startsWith('#')) {
          const [key, value] = line.split('=');
          
          if (key && value) {
            // Check for weak secrets
            if (this.isSensitiveKey(key)) {
              if (value.length < 32) {
                this.issues.push({
                  type: 'WEAK_SECRET',
                  file: '.env',
                  line: i + 1,
                  message: `Secret ${key} is too short (${value.length} characters)`
                });
              }
              
              // Check for development defaults in production
              if (process.env.NODE_ENV === 'production') {
                const developmentIndicators = ['test', 'dev', 'development', 'localhost', '127.0.0.1'];
                if (developmentIndicators.some(indicator => value.toLowerCase().includes(indicator))) {
                  this.issues.push({
                    type: 'DEVELOPMENT_SECRET_IN_PRODUCTION',
                    file: '.env',
                    line: i + 1,
                    message: `Production environment using development value for ${key}`
                  });
                }
              }
            }
          }
        }
      }
      
    } catch (error) {
      this.warnings.push({
        type: 'ENV_FILE_NOT_FOUND',
        message: '.env file not found - using system environment variables'
      });
    }
  }

  /**
   * Audit configuration files
   */
  async auditConfigurationFiles() {
    console.log('📋 Auditing Configuration Files...');
    
    const configFiles = [
      'config/config.js',
      'config/env.js',
      'config/database.js',
      'config/secureConfig.js'
    ];
    
    for (const configFile of configFiles) {
      const filePath = path.join(process.cwd(), configFile);
      
      try {
        const content = await fs.readFile(filePath, 'utf8');
        
        // Check for hardcoded secrets
        const lines = content.split('\n');
        for (let i = 0; i < lines.length; i++) {
          const line = lines[i];
          
          // Look for hardcoded API keys or secrets
          if (this.containsHardcodedSecret(line)) {
            this.issues.push({
              type: 'HARDCODED_SECRET',
              file: configFile,
              line: i + 1,
              message: 'Potential hardcoded secret found'
            });
          }
        }
        
      } catch (error) {
        // File doesn't exist, skip
      }
    }
  }

  /**
   * Audit API endpoints for sensitive data exposure
   */
  async auditAPIEndpoints() {
    console.log('📋 Auditing API Endpoints...');
    
    const routeFiles = await this.findFiles('routes', '.js');
    const controllerFiles = await this.findFiles('controllers', '.js');
    
    const allFiles = [...routeFiles, ...controllerFiles];
    
    for (const file of allFiles) {
      try {
        const content = await fs.readFile(file, 'utf8');
        const lines = content.split('\n');
        
        for (let i = 0; i < lines.length; i++) {
          const line = lines[i];
          
          // Check for sensitive data in responses
          if (line.includes('res.json') || line.includes('res.send')) {
            if (this.containsSensitiveDataExposure(line)) {
              this.issues.push({
                type: 'SENSITIVE_DATA_EXPOSURE',
                file: path.relative(process.cwd(), file),
                line: i + 1,
                message: 'Potential sensitive data exposure in API response'
              });
            }
          }
          
          // Check for API keys in client-side code
          if (line.includes('WHOP_API_KEY') || line.includes('apiKey')) {
            if (!line.includes('process.env') && !line.includes('config.')) {
              this.issues.push({
                type: 'API_KEY_EXPOSURE',
                file: path.relative(process.cwd(), file),
                line: i + 1,
                message: 'API key potentially exposed in client-side code'
              });
            }
          }
        }
        
      } catch (error) {
        // Skip files that can't be read
      }
    }
  }

  /**
   * Audit file permissions
   */
  async auditFilePermissions() {
    console.log('📋 Auditing File Permissions...');
    
    const sensitiveFiles = [
      '.env',
      '.secrets.key',
      'config/config.js',
      'private.key',
      'server.key'
    ];
    
    for (const file of sensitiveFiles) {
      const filePath = path.join(process.cwd(), file);
      
      try {
        const stats = await fs.stat(filePath);
        const mode = stats.mode & parseInt('777', 8);
        
        // Check if file is readable by others
        if (mode & parseInt('044', 8)) {
          this.issues.push({
            type: 'INSECURE_FILE_PERMISSIONS',
            file: file,
            message: `File ${file} is readable by others (permissions: ${mode.toString(8)})`
          });
        }
        
      } catch (error) {
        // File doesn't exist, skip
      }
    }
  }

  /**
   * Audit dependencies for vulnerabilities
   */
  async auditDependencies() {
    console.log('📋 Auditing Dependencies...');
    
    try {
      const packageJson = JSON.parse(await fs.readFile('package.json', 'utf8'));
      const dependencies = { ...packageJson.dependencies, ...packageJson.devDependencies };
      
      // Check for known vulnerable packages
      const vulnerablePackages = [
        'lodash@<4.17.21',
        'axios@<0.21.1',
        'express@<4.17.1',
        'mongoose@<5.13.15'
      ];
      
      for (const [pkg, version] of Object.entries(dependencies)) {
        // This is a simplified check - in production, use npm audit
        if (vulnerablePackages.some(vuln => vuln.startsWith(pkg + '@'))) {
          this.warnings.push({
            type: 'POTENTIALLY_VULNERABLE_DEPENDENCY',
            message: `Package ${pkg}@${version} may have known vulnerabilities`
          });
        }
      }
      
    } catch (error) {
      this.warnings.push({
        type: 'PACKAGE_JSON_NOT_FOUND',
        message: 'package.json not found'
      });
    }
  }

  /**
   * Check if key is sensitive
   */
  isSensitiveKey(key) {
    return this.sensitivePatterns.some(pattern => pattern.test(key));
  }

  /**
   * Check for hardcoded secrets
   */
  containsHardcodedSecret(line) {
    // Look for patterns like: apiKey: "actual-key-value"
    const hardcodedPatterns = [
      /['"](?:sk_|pk_|api_)[a-zA-Z0-9]{20,}['"]/,
      /['"][a-zA-Z0-9]{32,}['"].*(?:secret|key|token)/i,
      /(?:secret|key|token).*['"][a-zA-Z0-9]{20,}['"]/i
    ];
    
    return hardcodedPatterns.some(pattern => pattern.test(line));
  }

  /**
   * Check for sensitive data exposure
   */
  containsSensitiveDataExposure(line) {
    const exposurePatterns = [
      /password/i,
      /secret/i,
      /private.*key/i,
      /api.*key/i,
      /token/i
    ];
    
    return exposurePatterns.some(pattern => pattern.test(line));
  }

  /**
   * Find files recursively
   */
  async findFiles(dir, extension) {
    const files = [];
    const dirPath = path.join(process.cwd(), dir);
    
    try {
      const items = await fs.readdir(dirPath);
      
      for (const item of items) {
        const itemPath = path.join(dirPath, item);
        const stats = await fs.stat(itemPath);
        
        if (stats.isDirectory()) {
          const subFiles = await this.findFiles(path.join(dir, item), extension);
          files.push(...subFiles);
        } else if (item.endsWith(extension)) {
          files.push(itemPath);
        }
      }
    } catch (error) {
      // Directory doesn't exist, skip
    }
    
    return files;
  }

  /**
   * Generate security report
   */
  generateSecurityReport() {
    console.log('\n' + '='.repeat(60));
    console.log('🔒 SECURITY AUDIT REPORT');
    console.log('='.repeat(60));
    
    // Critical Issues
    if (this.issues.length > 0) {
      console.log('\n❌ CRITICAL SECURITY ISSUES:');
      this.issues.forEach((issue, index) => {
        console.log(`${index + 1}. ${issue.type}`);
        console.log(`   File: ${issue.file}${issue.line ? `:${issue.line}` : ''}`);
        console.log(`   Message: ${issue.message}\n`);
      });
    }
    
    // Warnings
    if (this.warnings.length > 0) {
      console.log('\n⚠️  SECURITY WARNINGS:');
      this.warnings.forEach((warning, index) => {
        console.log(`${index + 1}. ${warning.type}`);
        console.log(`   Message: ${warning.message}\n`);
      });
    }
    
    // Summary
    console.log('\n' + '-'.repeat(40));
    console.log('SUMMARY:');
    console.log(`Critical Issues: ${this.issues.length}`);
    console.log(`Warnings: ${this.warnings.length}`);
    
    if (this.issues.length === 0) {
      console.log('\n✅ No critical security issues found!');
    } else {
      console.log('\n🚨 Critical security issues must be fixed before production deployment!');
    }
    
    // Recommendations
    console.log('\n📋 SECURITY RECOMMENDATIONS:');
    console.log('1. Use environment variables for all sensitive configuration');
    console.log('2. Implement proper secrets management');
    console.log('3. Regular security audits and dependency updates');
    console.log('4. Use HTTPS in production');
    console.log('5. Implement proper input validation and sanitization');
    console.log('6. Regular backup and disaster recovery testing');
    
    return {
      criticalIssues: this.issues.length,
      warnings: this.warnings.length,
      isSecure: this.issues.length === 0
    };
  }
}

// Main execution
async function main() {
  const auditor = new SecurityAuditor();
  const result = await auditor.runAudit();
  
  process.exit(result.isSecure ? 0 : 1);
}

if (require.main === module) {
  main().catch(error => {
    console.error('Security audit failed:', error);
    process.exit(1);
  });
}

module.exports = SecurityAuditor;
