/**
 * Create a test user for wishlist testing
 */

const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
require('dotenv').config();

// Import models
const User = require('../models/User');

async function createTestUser() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/phonepoint-dar');
    console.log('✅ Connected to MongoDB');

    // Check if test user already exists
    const existingUser = await User.findOne({ email: '<EMAIL>' });
    
    if (existingUser) {
      console.log('✅ Test user already exists');
      console.log('📧 Email: <EMAIL>');
      console.log('🔑 Password: testpassword123');
      console.log('🆔 User ID:', existingUser._id);
      return existingUser;
    }

    // Create test user
    const hashedPassword = await bcrypt.hash('testpassword123', 12);
    
    const testUser = new User({
      name: 'Test User',
      email: '<EMAIL>',
      password: hashedPassword,
      isEmailVerified: true,
      role: 'user',
      phoneNumber: '+255123456789'
    });

    await testUser.save();
    
    console.log('✅ Test user created successfully!');
    console.log('📧 Email: <EMAIL>');
    console.log('🔑 Password: testpassword123');
    console.log('🆔 User ID:', testUser._id);
    
    return testUser;

  } catch (error) {
    console.error('❌ Error creating test user:', error);
    throw error;
  } finally {
    await mongoose.disconnect();
    console.log('📤 Disconnected from MongoDB');
  }
}

// Test login functionality
async function testLogin() {
  console.log('\n🧪 Testing login functionality...');
  
  const axios = require('axios');
  const baseURL = 'http://localhost:5001/api';
  
  try {
    const response = await axios.post(`${baseURL}/auth/login`, {
      email: '<EMAIL>',
      password: 'testpassword123'
    });
    
    if (response.data.success) {
      console.log('✅ Login successful!');
      console.log('🎫 Token:', response.data.token.substring(0, 20) + '...');
      
      // Test wishlist endpoint with token
      const wishlistResponse = await axios.get(`${baseURL}/wishlist`, {
        headers: {
          'Authorization': `Bearer ${response.data.token}`
        }
      });
      
      console.log('✅ Wishlist endpoint accessible');
      console.log('📝 Wishlist data:', wishlistResponse.data);
      
      return response.data.token;
    } else {
      console.log('❌ Login failed:', response.data.message);
    }
  } catch (error) {
    console.log('❌ Login test failed:', error.response?.data || error.message);
  }
}

// Test wishlist operations
async function testWishlistOperations(token) {
  if (!token) {
    console.log('⚠️ No token available, skipping wishlist operations test');
    return;
  }
  
  console.log('\n🧪 Testing wishlist operations...');
  
  const axios = require('axios');
  const baseURL = 'http://localhost:5001/api';
  
  const headers = {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  };
  
  try {
    // Test product ID (iPhone 15 Pro Max)
    const productId = '68775e53849a0097534ea98f';
    
    // Add to wishlist
    console.log('➕ Adding product to wishlist...');
    const addResponse = await axios.post(`${baseURL}/wishlist`, 
      { productId }, 
      { headers }
    );
    console.log('✅ Add to wishlist:', addResponse.data.success ? 'Success' : 'Failed');
    
    // Get wishlist
    console.log('📋 Getting wishlist...');
    const getResponse = await axios.get(`${baseURL}/wishlist`, { headers });
    console.log('✅ Get wishlist:', getResponse.data.data?.items?.length || 0, 'items');
    
    // Remove from wishlist
    console.log('➖ Removing product from wishlist...');
    const removeResponse = await axios.delete(`${baseURL}/wishlist/${productId}`, { headers });
    console.log('✅ Remove from wishlist:', removeResponse.data.success ? 'Success' : 'Failed');
    
  } catch (error) {
    console.log('❌ Wishlist operations test failed:', error.response?.data || error.message);
  }
}

// Main function
async function main() {
  console.log('🚀 Starting wishlist testing setup...\n');
  
  try {
    // Create test user
    await createTestUser();
    
    // Test login
    const token = await testLogin();
    
    // Test wishlist operations
    await testWishlistOperations(token);
    
    console.log('\n✅ All tests completed!');
    console.log('\n📋 Next steps:');
    console.log('1. Go to http://localhost:5173/login');
    console.log('2. Login with: <EMAIL> / testpassword123');
    console.log('3. Test wishlist functionality on products page');
    
  } catch (error) {
    console.error('❌ Test setup failed:', error);
  }
}

// Run if called directly
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { createTestUser, testLogin, testWishlistOperations };
