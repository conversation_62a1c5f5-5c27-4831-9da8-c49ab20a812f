/**
 * Seed Sample Phone Products
 * Adds sample phone products to the database for testing
 */

const mongoose = require('mongoose');
const Product = require('../models/Product');
const Brand = require('../models/Brand');
const ProductCategory = require('../models/ProductCategory');
const config = require('../config/config');

// Sample phone products
const samplePhones = [
  {
    name: 'iPhone 15 Pro',
    slug: 'iphone-15-pro',
    sku: 'IPH15PRO128',
    brand: 'Apple',
    model: 'iPhone 15 Pro',
    category: 'smartphone',
    short_description: 'Latest iPhone with titanium design and A17 Pro chip',
    description: 'The iPhone 15 Pro features a titanium design, A17 Pro chip, and advanced camera system with 3x telephoto lens.',
    price: 2500000, // 2.5M TZS
    cost_price: 2200000,
    stock_quantity: 25,
    low_stock_threshold: 5,
    weight: 187,
    dimensions: { length: 146.6, width: 70.6, height: 8.25 },
    specifications: [
      { name: 'Display', value: '6.1-inch Super Retina XDR', category: 'Display' },
      { name: 'Processor', value: 'A17 Pro chip', category: 'Performance' },
      { name: 'Storage', value: '128GB', category: 'Storage' },
      { name: 'Camera', value: '48MP Main + 12MP Ultra Wide + 12MP Telephoto', category: 'Camera' },
      { name: 'Battery', value: 'Up to 23 hours video playback', category: 'Battery' },
      { name: 'OS', value: 'iOS 17', category: 'Software' }
    ],
    variants: [
      {
        name: 'iPhone 15 Pro 128GB Natural Titanium',
        sku: 'IPH15PRO128-NT',
        color: 'Natural Titanium',
        storage: '128GB',
        price: 2500000,
        stock_quantity: 10,
        is_active: true
      },
      {
        name: 'iPhone 15 Pro 256GB Natural Titanium',
        sku: 'IPH15PRO256-NT',
        color: 'Natural Titanium',
        storage: '256GB',
        price: 2800000,
        stock_quantity: 8,
        is_active: true
      }
    ],
    images: [
      {
        url: '/images/phones/iphone-15-pro-natural-titanium.jpg',
        alt: 'iPhone 15 Pro Natural Titanium',
        is_primary: true
      }
    ],
    is_active: true,
    is_featured: true
  },
  {
    name: 'Samsung Galaxy S24 Ultra',
    slug: 'samsung-galaxy-s24-ultra',
    sku: 'SGS24U256',
    brand: 'Samsung',
    model: 'Galaxy S24 Ultra',
    category: 'smartphone',
    short_description: 'Premium Android flagship with S Pen and AI features',
    description: 'Samsung Galaxy S24 Ultra with built-in S Pen, 200MP camera, and Galaxy AI features.',
    price: 2300000, // 2.3M TZS
    cost_price: 2000000,
    stock_quantity: 20,
    low_stock_threshold: 5,
    weight: 232,
    dimensions: { length: 162.3, width: 79.0, height: 8.6 },
    specifications: [
      { name: 'Display', value: '6.8-inch Dynamic AMOLED 2X', category: 'Display' },
      { name: 'Processor', value: 'Snapdragon 8 Gen 3', category: 'Performance' },
      { name: 'Storage', value: '256GB', category: 'Storage' },
      { name: 'RAM', value: '12GB', category: 'Performance' },
      { name: 'Camera', value: '200MP Main + 50MP Periscope + 12MP Ultra Wide + 10MP Telephoto', category: 'Camera' },
      { name: 'Battery', value: '5000mAh', category: 'Battery' },
      { name: 'OS', value: 'Android 14 with One UI 6.1', category: 'Software' }
    ],
    variants: [
      {
        name: 'Galaxy S24 Ultra 256GB Titanium Gray',
        sku: 'SGS24U256-TG',
        color: 'Titanium Gray',
        storage: '256GB',
        memory: '12GB',
        price: 2300000,
        stock_quantity: 12,
        is_active: true
      },
      {
        name: 'Galaxy S24 Ultra 512GB Titanium Gray',
        sku: 'SGS24U512-TG',
        color: 'Titanium Gray',
        storage: '512GB',
        memory: '12GB',
        price: 2600000,
        stock_quantity: 8,
        is_active: true
      }
    ],
    images: [
      {
        url: '/images/phones/galaxy-s24-ultra-titanium-gray.jpg',
        alt: 'Samsung Galaxy S24 Ultra Titanium Gray',
        is_primary: true
      }
    ],
    is_active: true,
    is_featured: true
  },
  {
    name: 'Xiaomi Redmi Note 13',
    slug: 'xiaomi-redmi-note-13',
    sku: 'XRN13128',
    brand: 'Xiaomi',
    model: 'Redmi Note 13',
    category: 'smartphone',
    short_description: 'Affordable smartphone with great camera and performance',
    description: 'Xiaomi Redmi Note 13 offers excellent value with 108MP camera and fast charging.',
    price: 450000, // 450K TZS
    cost_price: 380000,
    stock_quantity: 50,
    low_stock_threshold: 10,
    weight: 188,
    dimensions: { length: 162.24, width: 75.55, height: 7.97 },
    specifications: [
      { name: 'Display', value: '6.67-inch AMOLED', category: 'Display' },
      { name: 'Processor', value: 'Snapdragon 685', category: 'Performance' },
      { name: 'Storage', value: '128GB', category: 'Storage' },
      { name: 'RAM', value: '6GB', category: 'Performance' },
      { name: 'Camera', value: '108MP Main + 8MP Ultra Wide + 2MP Macro', category: 'Camera' },
      { name: 'Battery', value: '5000mAh with 33W fast charging', category: 'Battery' },
      { name: 'OS', value: 'MIUI 14 based on Android 13', category: 'Software' }
    ],
    variants: [
      {
        name: 'Redmi Note 13 128GB Mint Blue',
        sku: 'XRN13128-MB',
        color: 'Mint Blue',
        storage: '128GB',
        memory: '6GB',
        price: 450000,
        stock_quantity: 25,
        is_active: true
      },
      {
        name: 'Redmi Note 13 256GB Mint Blue',
        sku: 'XRN13256-MB',
        color: 'Mint Blue',
        storage: '256GB',
        memory: '8GB',
        price: 520000,
        stock_quantity: 25,
        is_active: true
      }
    ],
    images: [
      {
        url: '/images/phones/redmi-note-13-mint-blue.jpg',
        alt: 'Xiaomi Redmi Note 13 Mint Blue',
        is_primary: true
      }
    ],
    is_active: true,
    is_featured: false
  }
];

async function seedSamplePhones() {
  try {
    // Connect to MongoDB
    await mongoose.connect(config.MONGODB_URI);
    console.log('Connected to MongoDB');

    // Clear existing products
    await Product.deleteMany({});
    console.log('Cleared existing products');

    // Insert sample phones
    const insertedProducts = await Product.insertMany(samplePhones);
    console.log(`Inserted ${insertedProducts.length} sample phone products`);

    console.log('Sample phones seeded successfully!');
    process.exit(0);
  } catch (error) {
    console.error('Error seeding sample phones:', error);
    process.exit(1);
  }
}

// Run the seeding function
if (require.main === module) {
  seedSamplePhones();
}

module.exports = seedSamplePhones;
