/**
 * <PERSON>ript to process email sequences
 * This script should be run by a cron job every hour
 *
 * Example cron entry:
 * 0 * * * * node /path/to/aixcelerate-backend/scripts/processEmailSequences.js
 */

const mongoose = require('mongoose');
const dotenv = require('dotenv');
const emailSequenceController = require('../controllers/emailSequenceController');
const config = require('../config/config');

// Load environment variables
dotenv.config();

// Connect to MongoDB
mongoose.connect(config.MONGODB_URI, {
  useNewUrlParser: true,
  useUnifiedTopology: true
});

// Process email sequences
const processEmailSequences = async () => {
  try {
    console.log('Processing email sequences...');

    // Call the controller directly
    const result = await emailSequenceController.processSequences({}, {
      status: (code) => ({
        json: (data) => {
          console.log('Email sequences processed:', data);
        }
      })
    }, (err) => {
      console.error('Error processing email sequences:', err);
    });

    // Disconnect from MongoDB
    await mongoose.disconnect();
    process.exit(0);
  } catch (error) {
    console.error('Error processing email sequences:', error.message);

    // Disconnect from MongoDB
    await mongoose.disconnect();
    process.exit(1);
  }
};

// Run the function
processEmailSequences();
