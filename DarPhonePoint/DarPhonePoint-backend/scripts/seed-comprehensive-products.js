/**
 * Comprehensive Product Seeding Script for Phone Point Dar
 * Creates a diverse inventory of mobile accessories and tech products
 */

const mongoose = require('mongoose');
const Product = require('../models/Product');

// MongoDB connection
const connectDB = async () => {
  try {
    await mongoose.connect('mongodb://localhost:27017/aixcelerate-dev');
    console.log('MongoDB connected successfully');
  } catch (error) {
    console.error('MongoDB connection error:', error);
    process.exit(1);
  }
};

// Helper function to generate SKU
const generateSKU = (category, brand, model) => {
  const categoryCode = {
    'smartphone': 'SPH',
    'tablet': 'TAB',
    'smartwatch': 'SWT',
    'earbuds': 'EBD',
    'headphones': 'HPH',
    'charger': 'CHR',
    'cable': 'CBL',
    'case': 'CSE',
    'screen_protector': 'SPR',
    'power_bank': 'PWB',
    'speaker': 'SPK',
    'mount': 'MNT',
    'other': 'OTH'
  };
  
  const brandCode = brand.substring(0, 3).toUpperCase();
  const modelCode = model.replace(/\s+/g, '').substring(0, 6).toUpperCase();
  const random = Math.random().toString(36).substring(2, 5).toUpperCase();
  
  return `${categoryCode[category] || 'OTH'}-${brandCode}-${modelCode}-${random}`;
};

// Comprehensive product data
const productCategories = {
  // Phone Cases
  cases: [
    {
      name: "iPhone 15 Pro Silicone Case",
      brand: "Apple",
      model: "Silicone Case",
      category: "case",
      price: 45000,
      cost_price: 32000,
      description: "Official Apple silicone case for iPhone 15 Pro. Provides excellent protection with a soft-touch finish.",
      short_description: "Premium silicone protection for iPhone 15 Pro",
      specifications: [
        { name: "Material", value: "Premium Silicone", category: "Build" },
        { name: "Compatibility", value: "iPhone 15 Pro", category: "Compatibility" },
        { name: "Protection", value: "Drop & Scratch Protection", category: "Features" },
        { name: "Wireless Charging", value: "Compatible", category: "Features" }
      ],
      variants: [
        { name: "Black Silicone Case", sku: "CSE-APP-SILBLK-001", price: 45000, color: "Black", stock_quantity: 25 },
        { name: "Blue Silicone Case", sku: "CSE-APP-SILBLU-002", price: 45000, color: "Blue", stock_quantity: 20 },
        { name: "Pink Silicone Case", sku: "CSE-APP-SILPNK-003", price: 45000, color: "Pink", stock_quantity: 15 }
      ],
      weight: 50,
      track_inventory: true,
      stock_quantity: 60,
      low_stock_threshold: 10
    },
    {
      name: "Samsung Galaxy S24 Ultra Rugged Case",
      brand: "OtterBox",
      model: "Defender Pro",
      category: "case",
      price: 85000,
      cost_price: 60000,
      description: "Military-grade protection for Samsung Galaxy S24 Ultra. Multi-layer defense against drops, dust, and scratches.",
      short_description: "Military-grade protection for Galaxy S24 Ultra",
      specifications: [
        { name: "Material", value: "Polycarbonate + TPU", category: "Build" },
        { name: "Drop Protection", value: "4x Military Standard", category: "Protection" },
        { name: "Port Covers", value: "Dust Protection", category: "Features" },
        { name: "Belt Clip", value: "Included", category: "Accessories" }
      ],
      variants: [
        { name: "Black Defender Case", sku: "CSE-OTT-DEFBLK-001", price: 85000, color: "Black", stock_quantity: 15 },
        { name: "Blue Defender Case", sku: "CSE-OTT-DEFBLU-002", price: 85000, color: "Blue", stock_quantity: 10 }
      ],
      weight: 120,
      track_inventory: true,
      stock_quantity: 25,
      low_stock_threshold: 5
    },
    {
      name: "Universal Phone Case",
      brand: "Generic",
      model: "Leather Wallet",
      category: "case",
      price: 15000,
      cost_price: 8000,
      description: "Universal leather wallet case compatible with most smartphones. Features card slots and cash compartment.",
      short_description: "Universal leather wallet case with card storage",
      specifications: [
        { name: "Material", value: "PU Leather", category: "Build" },
        { name: "Card Slots", value: "3 Slots", category: "Storage" },
        { name: "Cash Pocket", value: "Yes", category: "Storage" },
        { name: "Compatibility", value: "4.7-6.7 inch phones", category: "Compatibility" }
      ],
      variants: [
        { name: "Brown Leather Case", sku: "CSE-GEN-LTHBRN-001", price: 15000, color: "Brown", stock_quantity: 30 },
        { name: "Black Leather Case", sku: "CSE-GEN-LTHBLK-002", price: 15000, color: "Black", stock_quantity: 35 }
      ],
      weight: 80,
      track_inventory: true,
      stock_quantity: 65,
      low_stock_threshold: 15
    }
  ],

  // Screen Protectors
  screen_protectors: [
    {
      name: "Tempered Glass Screen Protector",
      brand: "Belkin",
      model: "UltraGlass",
      category: "screen_protector",
      price: 25000,
      cost_price: 15000,
      description: "Premium tempered glass screen protector with 9H hardness. Crystal clear with oleophobic coating.",
      short_description: "9H tempered glass protection with crystal clarity",
      specifications: [
        { name: "Hardness", value: "9H", category: "Protection" },
        { name: "Thickness", value: "0.33mm", category: "Build" },
        { name: "Coating", value: "Oleophobic", category: "Features" },
        { name: "Installation", value: "Bubble-free", category: "Features" }
      ],
      variants: [
        { name: "iPhone 15 Pro Glass", sku: "SPR-BEL-IP15PRO-001", price: 25000, compatibility: "iPhone 15 Pro", stock_quantity: 40 },
        { name: "Galaxy S24 Ultra Glass", sku: "SPR-BEL-GS24ULT-002", price: 25000, compatibility: "Galaxy S24 Ultra", stock_quantity: 35 },
        { name: "Xiaomi Redmi Note 13 Glass", sku: "SPR-BEL-XRN13-003", price: 20000, compatibility: "Redmi Note 13", stock_quantity: 50 }
      ],
      weight: 20,
      track_inventory: true,
      stock_quantity: 125,
      low_stock_threshold: 20
    }
  ],

  // Chargers and Cables
  chargers: [
    {
      name: "USB-C Fast Charger",
      brand: "Anker",
      model: "PowerPort III",
      category: "charger",
      price: 35000,
      cost_price: 22000,
      description: "65W USB-C fast charger with PowerIQ 3.0 technology. Compatible with smartphones, tablets, and laptops.",
      short_description: "65W USB-C fast charger with PowerIQ 3.0",
      specifications: [
        { name: "Power Output", value: "65W", category: "Performance" },
        { name: "Technology", value: "PowerIQ 3.0", category: "Features" },
        { name: "Ports", value: "1x USB-C", category: "Connectivity" },
        { name: "Safety", value: "MultiProtect", category: "Safety" }
      ],
      variants: [
        { name: "White 65W Charger", sku: "CHR-ANK-65WWHT-001", price: 35000, color: "White", stock_quantity: 30 },
        { name: "Black 65W Charger", sku: "CHR-ANK-65WBLK-002", price: 35000, color: "Black", stock_quantity: 25 }
      ],
      weight: 150,
      track_inventory: true,
      stock_quantity: 55,
      low_stock_threshold: 10
    },
    {
      name: "Wireless Charging Pad",
      brand: "Samsung",
      model: "Fast Charge 2.0",
      category: "charger",
      price: 55000,
      cost_price: 38000,
      description: "15W wireless charging pad with fast charge 2.0 technology. Compatible with Qi-enabled devices.",
      short_description: "15W wireless fast charging pad",
      specifications: [
        { name: "Power Output", value: "15W", category: "Performance" },
        { name: "Technology", value: "Qi Wireless", category: "Features" },
        { name: "LED Indicator", value: "Yes", category: "Features" },
        { name: "Case Compatible", value: "Up to 3mm", category: "Compatibility" }
      ],
      variants: [
        { name: "Black Wireless Charger", sku: "CHR-SAM-WIRBLK-001", price: 55000, color: "Black", stock_quantity: 20 },
        { name: "White Wireless Charger", sku: "CHR-SAM-WIRWHT-002", price: 55000, color: "White", stock_quantity: 15 }
      ],
      weight: 200,
      track_inventory: true,
      stock_quantity: 35,
      low_stock_threshold: 8
    }
  ],

  // Cables
  cables: [
    {
      name: "USB-C to Lightning Cable",
      brand: "Apple",
      model: "Original Cable",
      category: "cable",
      price: 28000,
      cost_price: 18000,
      description: "Official Apple USB-C to Lightning cable. Supports fast charging and data transfer for iPhone and iPad.",
      short_description: "Official Apple USB-C to Lightning cable",
      specifications: [
        { name: "Length", value: "1 meter", category: "Physical" },
        { name: "Connector 1", value: "USB-C", category: "Connectivity" },
        { name: "Connector 2", value: "Lightning", category: "Connectivity" },
        { name: "Data Transfer", value: "USB 2.0", category: "Performance" }
      ],
      variants: [
        { name: "1m USB-C to Lightning", sku: "CBL-APP-USBLTN-1M", price: 28000, length: "1m", stock_quantity: 50 },
        { name: "2m USB-C to Lightning", sku: "CBL-APP-USBLTN-2M", price: 35000, length: "2m", stock_quantity: 30 }
      ],
      weight: 30,
      track_inventory: true,
      stock_quantity: 80,
      low_stock_threshold: 15
    },
    {
      name: "USB-C to USB-C Cable",
      brand: "Anker",
      model: "PowerLine III",
      category: "cable",
      price: 18000,
      cost_price: 12000,
      description: "High-speed USB-C to USB-C cable with 100W power delivery. Perfect for fast charging and data transfer.",
      short_description: "100W USB-C to USB-C fast charging cable",
      specifications: [
        { name: "Power Delivery", value: "100W", category: "Performance" },
        { name: "Data Transfer", value: "USB 3.1 Gen 2", category: "Performance" },
        { name: "Durability", value: "25,000+ bend lifespan", category: "Build" },
        { name: "Length", value: "1.8 meters", category: "Physical" }
      ],
      variants: [
        { name: "1.8m USB-C Cable Black", sku: "CBL-ANK-USBCBLK-001", price: 18000, color: "Black", stock_quantity: 60 },
        { name: "1.8m USB-C Cable White", sku: "CBL-ANK-USBCWHT-002", price: 18000, color: "White", stock_quantity: 40 }
      ],
      weight: 45,
      track_inventory: true,
      stock_quantity: 100,
      low_stock_threshold: 20
    }
  ],

  // Power Banks
  power_banks: [
    {
      name: "Anker PowerCore 10000",
      brand: "Anker",
      model: "PowerCore 10000",
      category: "other",
      price: 65000,
      cost_price: 45000,
      description: "Compact 10,000mAh power bank with PowerIQ technology. Charges iPhone 15 Pro 2.5 times or Galaxy S24 2.2 times.",
      short_description: "10,000mAh portable power bank with fast charging",
      specifications: [
        { name: "Capacity", value: "10,000mAh", category: "Battery" },
        { name: "Input", value: "USB-C 18W", category: "Charging" },
        { name: "Output", value: "USB-A 12W, USB-C 18W", category: "Charging" },
        { name: "Weight", value: "180g", category: "Physical" }
      ],
      variants: [
        { name: "Black PowerCore 10000", sku: "PWB-ANK-10KBLK-001", price: 65000, color: "Black", stock_quantity: 25 },
        { name: "Blue PowerCore 10000", sku: "PWB-ANK-10KBLU-002", price: 65000, color: "Blue", stock_quantity: 20 }
      ],
      weight: 180,
      track_inventory: true,
      stock_quantity: 45,
      low_stock_threshold: 8
    },
    {
      name: "Xiaomi Mi Power Bank 3",
      brand: "Xiaomi",
      model: "Mi Power Bank 3",
      category: "other",
      price: 45000,
      cost_price: 30000,
      description: "20,000mAh high-capacity power bank with dual USB-A and USB-C ports. Perfect for extended trips and heavy usage.",
      short_description: "20,000mAh high-capacity power bank",
      specifications: [
        { name: "Capacity", value: "20,000mAh", category: "Battery" },
        { name: "Input", value: "USB-C/Micro-USB", category: "Charging" },
        { name: "Output", value: "2x USB-A, 1x USB-C", category: "Charging" },
        { name: "Fast Charge", value: "18W", category: "Performance" }
      ],
      variants: [
        { name: "Black Mi Power Bank 20K", sku: "PWB-XIA-20KBLK-001", price: 45000, color: "Black", stock_quantity: 30 },
        { name: "White Mi Power Bank 20K", sku: "PWB-XIA-20KWHT-002", price: 45000, color: "White", stock_quantity: 25 }
      ],
      weight: 420,
      track_inventory: true,
      stock_quantity: 55,
      low_stock_threshold: 10
    }
  ],

  // Audio Accessories
  audio: [
    {
      name: "Apple AirPods Pro 2nd Gen",
      brand: "Apple",
      model: "AirPods Pro",
      category: "earbuds",
      price: 450000,
      cost_price: 320000,
      description: "Active Noise Cancellation earbuds with Transparency mode, Spatial Audio, and up to 6 hours of listening time.",
      short_description: "Premium noise-cancelling wireless earbuds",
      specifications: [
        { name: "Noise Cancellation", value: "Active", category: "Audio" },
        { name: "Battery Life", value: "6 hours + 24 hours case", category: "Battery" },
        { name: "Connectivity", value: "Bluetooth 5.3", category: "Connectivity" },
        { name: "Water Resistance", value: "IPX4", category: "Durability" }
      ],
      variants: [
        { name: "AirPods Pro 2nd Gen", sku: "EBD-APP-AIRPRO2-001", price: 450000, color: "White", stock_quantity: 15 }
      ],
      weight: 56,
      track_inventory: true,
      stock_quantity: 15,
      low_stock_threshold: 3,
      requires_serial_tracking: true
    },
    {
      name: "Samsung Galaxy Buds2 Pro",
      brand: "Samsung",
      model: "Galaxy Buds2 Pro",
      category: "earbuds",
      price: 280000,
      cost_price: 200000,
      description: "Premium wireless earbuds with intelligent ANC, 360 Audio, and seamless Galaxy device integration.",
      short_description: "Premium wireless earbuds with intelligent ANC",
      specifications: [
        { name: "Noise Cancellation", value: "Intelligent ANC", category: "Audio" },
        { name: "Battery Life", value: "5 hours + 13 hours case", category: "Battery" },
        { name: "Audio", value: "360 Audio", category: "Audio" },
        { name: "Water Resistance", value: "IPX7", category: "Durability" }
      ],
      variants: [
        { name: "Galaxy Buds2 Pro Graphite", sku: "EBD-SAM-GB2PGRA-001", price: 280000, color: "Graphite", stock_quantity: 20 },
        { name: "Galaxy Buds2 Pro White", sku: "EBD-SAM-GB2PWHT-002", price: 280000, color: "White", stock_quantity: 15 }
      ],
      weight: 52,
      track_inventory: true,
      stock_quantity: 35,
      low_stock_threshold: 5
    },
    {
      name: "JBL Tune 230NC TWS",
      brand: "JBL",
      model: "Tune 230NC",
      category: "earbuds",
      price: 120000,
      cost_price: 85000,
      description: "True wireless earbuds with Active Noise Cancelling and JBL Pure Bass Sound. Great value for money.",
      short_description: "Affordable wireless earbuds with noise cancelling",
      specifications: [
        { name: "Noise Cancellation", value: "Active", category: "Audio" },
        { name: "Battery Life", value: "8 hours + 32 hours case", category: "Battery" },
        { name: "Sound", value: "JBL Pure Bass", category: "Audio" },
        { name: "Fast Charge", value: "10 min = 2 hours", category: "Charging" }
      ],
      variants: [
        { name: "JBL Tune 230NC Black", sku: "EBD-JBL-T230BLK-001", price: 120000, color: "Black", stock_quantity: 40 },
        { name: "JBL Tune 230NC Blue", sku: "EBD-JBL-T230BLU-002", price: 120000, color: "Blue", stock_quantity: 30 }
      ],
      weight: 58,
      track_inventory: true,
      stock_quantity: 70,
      low_stock_threshold: 15
    }
  ],

  // Smartwatches
  smartwatches: [
    {
      name: "Apple Watch Series 9",
      brand: "Apple",
      model: "Watch Series 9",
      category: "smartwatch",
      price: 850000,
      cost_price: 600000,
      description: "Advanced smartwatch with S9 chip, Double Tap gesture, and comprehensive health monitoring.",
      short_description: "Advanced smartwatch with health monitoring",
      specifications: [
        { name: "Display", value: "45mm Always-On Retina", category: "Display" },
        { name: "Processor", value: "S9 SiP", category: "Performance" },
        { name: "Battery Life", value: "18 hours", category: "Battery" },
        { name: "Health Features", value: "ECG, Blood Oxygen, Heart Rate", category: "Health" }
      ],
      variants: [
        { name: "Apple Watch 45mm Midnight", sku: "SWT-APP-AW9MID-001", price: 850000, color: "Midnight", size: "45mm", stock_quantity: 8 },
        { name: "Apple Watch 45mm Starlight", sku: "SWT-APP-AW9STR-002", price: 850000, color: "Starlight", size: "45mm", stock_quantity: 6 }
      ],
      weight: 38,
      track_inventory: true,
      stock_quantity: 14,
      low_stock_threshold: 2,
      requires_serial_tracking: true
    },
    {
      name: "Xiaomi Mi Watch",
      brand: "Xiaomi",
      model: "Mi Watch",
      category: "smartwatch",
      price: 180000,
      cost_price: 125000,
      description: "Feature-rich smartwatch with GPS, heart rate monitoring, and 16-day battery life. Great value proposition.",
      short_description: "Feature-rich smartwatch with long battery life",
      specifications: [
        { name: "Display", value: "1.39 AMOLED", category: "Display" },
        { name: "GPS", value: "Built-in", category: "Navigation" },
        { name: "Battery Life", value: "16 days", category: "Battery" },
        { name: "Water Resistance", value: "5ATM", category: "Durability" }
      ],
      variants: [
        { name: "Mi Watch Black", sku: "SWT-XIA-MIWBLK-001", price: 180000, color: "Black", stock_quantity: 25 },
        { name: "Mi Watch Silver", sku: "SWT-XIA-MIWSIL-002", price: 180000, color: "Silver", stock_quantity: 20 }
      ],
      weight: 32,
      track_inventory: true,
      stock_quantity: 45,
      low_stock_threshold: 8
    }
  ],

  // Speakers
  speakers: [
    {
      name: "JBL Flip 6 Portable Speaker",
      brand: "JBL",
      model: "Flip 6",
      category: "speaker",
      price: 220000,
      cost_price: 155000,
      description: "Portable Bluetooth speaker with powerful JBL Original Pro Sound, IP67 waterproof rating, and 12-hour battery.",
      short_description: "Waterproof portable Bluetooth speaker",
      specifications: [
        { name: "Power Output", value: "30W", category: "Audio" },
        { name: "Battery Life", value: "12 hours", category: "Battery" },
        { name: "Water Resistance", value: "IP67", category: "Durability" },
        { name: "Connectivity", value: "Bluetooth 5.1", category: "Connectivity" }
      ],
      variants: [
        { name: "JBL Flip 6 Black", sku: "SPK-JBL-FLP6BLK-001", price: 220000, color: "Black", stock_quantity: 20 },
        { name: "JBL Flip 6 Blue", sku: "SPK-JBL-FLP6BLU-002", price: 220000, color: "Blue", stock_quantity: 15 },
        { name: "JBL Flip 6 Red", sku: "SPK-JBL-FLP6RED-003", price: 220000, color: "Red", stock_quantity: 12 }
      ],
      weight: 550,
      track_inventory: true,
      stock_quantity: 47,
      low_stock_threshold: 8
    }
  ],

  // Phone Mounts and Stands
  mounts: [
    {
      name: "Car Phone Mount",
      brand: "iOttie",
      model: "Easy One Touch 5",
      category: "mount",
      price: 45000,
      cost_price: 30000,
      description: "Dashboard and windshield car mount with one-touch mechanism. Compatible with phones 4-6.5 inches.",
      short_description: "Universal car phone mount with one-touch release",
      specifications: [
        { name: "Compatibility", value: "4-6.5 inch phones", category: "Compatibility" },
        { name: "Mount Type", value: "Dashboard/Windshield", category: "Installation" },
        { name: "Adjustment", value: "360° rotation", category: "Features" },
        { name: "Release", value: "One-touch", category: "Features" }
      ],
      variants: [
        { name: "Car Mount Black", sku: "MNT-IOT-CARMNT-001", price: 45000, color: "Black", stock_quantity: 35 }
      ],
      weight: 200,
      track_inventory: true,
      stock_quantity: 35,
      low_stock_threshold: 8
    },
    {
      name: "Desktop Phone Stand",
      brand: "Lamicall",
      model: "Adjustable Stand",
      category: "mount",
      price: 15000,
      cost_price: 8000,
      description: "Adjustable aluminum desktop stand for phones and tablets. Perfect for video calls and media viewing.",
      short_description: "Adjustable aluminum desktop phone stand",
      specifications: [
        { name: "Material", value: "Aluminum Alloy", category: "Build" },
        { name: "Compatibility", value: "4-13 inch devices", category: "Compatibility" },
        { name: "Adjustment", value: "Multi-angle", category: "Features" },
        { name: "Base", value: "Anti-slip", category: "Features" }
      ],
      variants: [
        { name: "Silver Desktop Stand", sku: "MNT-LAM-DSKSIL-001", price: 15000, color: "Silver", stock_quantity: 50 },
        { name: "Black Desktop Stand", sku: "MNT-LAM-DSKBLK-002", price: 15000, color: "Black", stock_quantity: 40 }
      ],
      weight: 150,
      track_inventory: true,
      stock_quantity: 90,
      low_stock_threshold: 20
    }
  ],

  // Gaming Accessories
  gaming: [
    {
      name: "Mobile Gaming Controller",
      brand: "Razer",
      model: "Kishi V2",
      category: "other",
      price: 180000,
      cost_price: 125000,
      description: "Universal mobile gaming controller with console-quality controls. Compatible with Android and iPhone.",
      short_description: "Console-quality mobile gaming controller",
      specifications: [
        { name: "Compatibility", value: "Android/iOS", category: "Compatibility" },
        { name: "Connection", value: "USB-C/Lightning", category: "Connectivity" },
        { name: "Buttons", value: "Console-grade", category: "Controls" },
        { name: "Latency", value: "Sub-1ms", category: "Performance" }
      ],
      variants: [
        { name: "Kishi V2 for Android", sku: "GAM-RAZ-KSHV2AND-001", price: 180000, platform: "Android", stock_quantity: 15 },
        { name: "Kishi V2 for iPhone", sku: "GAM-RAZ-KSHV2IOS-002", price: 180000, platform: "iPhone", stock_quantity: 12 }
      ],
      weight: 120,
      track_inventory: true,
      stock_quantity: 27,
      low_stock_threshold: 5
    }
  ],

  // Tablet Accessories
  tablets: [
    {
      name: "iPad Pro 12.9 Keyboard Case",
      brand: "Apple",
      model: "Magic Keyboard",
      category: "case",
      price: 650000,
      cost_price: 450000,
      description: "Premium keyboard case for iPad Pro with backlit keys, trackpad, and floating cantilever design.",
      short_description: "Premium keyboard case with trackpad for iPad Pro",
      specifications: [
        { name: "Compatibility", value: "iPad Pro 12.9 (3rd-6th gen)", category: "Compatibility" },
        { name: "Keyboard", value: "Backlit", category: "Features" },
        { name: "Trackpad", value: "Multi-touch", category: "Features" },
        { name: "Charging", value: "Pass-through", category: "Features" }
      ],
      variants: [
        { name: "Magic Keyboard Black", sku: "TAB-APP-MGKBLK-001", price: 650000, color: "Black", stock_quantity: 8 },
        { name: "Magic Keyboard White", sku: "TAB-APP-MGKWHT-002", price: 650000, color: "White", stock_quantity: 6 }
      ],
      weight: 700,
      track_inventory: true,
      stock_quantity: 14,
      low_stock_threshold: 3
    }
  ],

  // Smartphones (Original Products)
  smartphones: [
    {
      name: "iPhone 15 Pro",
      brand: "Apple",
      model: "iPhone 15 Pro",
      category: "smartphone",
      price: 2500000,
      cost_price: 1800000,
      description: "The most advanced iPhone ever with titanium design, A17 Pro chip, and professional camera system.",
      short_description: "Premium smartphone with titanium design and A17 Pro chip",
      specifications: [
        { name: "Display", value: "6.1-inch Super Retina XDR", category: "Display" },
        { name: "Processor", value: "A17 Pro chip", category: "Performance" },
        { name: "Camera", value: "48MP Main + 12MP Ultra Wide + 12MP Telephoto", category: "Camera" },
        { name: "Storage", value: "128GB/256GB/512GB/1TB", category: "Storage" }
      ],
      variants: [
        { name: "iPhone 15 Pro 128GB Natural Titanium", sku: "IPH15PRO128-NT", price: 2500000, storage: "128GB", color: "Natural Titanium", stock_quantity: 15 },
        { name: "iPhone 15 Pro 256GB Natural Titanium", sku: "IPH15PRO256-NT", price: 2800000, storage: "256GB", color: "Natural Titanium", stock_quantity: 12 }
      ],
      weight: 187,
      track_inventory: true,
      stock_quantity: 27,
      low_stock_threshold: 5,
      requires_imei_tracking: true
    },
    {
      name: "Samsung Galaxy S24 Ultra",
      brand: "Samsung",
      model: "Galaxy S24 Ultra",
      category: "smartphone",
      price: 2300000,
      cost_price: 1650000,
      description: "Ultimate Galaxy experience with S Pen, 200MP camera, and Galaxy AI features.",
      short_description: "Premium Android flagship with S Pen and AI features",
      specifications: [
        { name: "Display", value: "6.8-inch Dynamic AMOLED 2X", category: "Display" },
        { name: "Processor", value: "Snapdragon 8 Gen 3", category: "Performance" },
        { name: "Camera", value: "200MP Main + 50MP Periscope + 12MP Ultra Wide + 10MP Telephoto", category: "Camera" },
        { name: "S Pen", value: "Built-in", category: "Features" }
      ],
      variants: [
        { name: "Galaxy S24 Ultra 256GB Titanium Gray", sku: "SGS24U256-TG", price: 2300000, storage: "256GB", color: "Titanium Gray", stock_quantity: 18 },
        { name: "Galaxy S24 Ultra 512GB Titanium Gray", sku: "SGS24U512-TG", price: 2600000, storage: "512GB", color: "Titanium Gray", stock_quantity: 10 }
      ],
      weight: 232,
      track_inventory: true,
      stock_quantity: 28,
      low_stock_threshold: 5,
      requires_imei_tracking: true
    },
    {
      name: "Xiaomi Redmi Note 13",
      brand: "Xiaomi",
      model: "Redmi Note 13",
      category: "smartphone",
      price: 450000,
      cost_price: 320000,
      description: "Exceptional value smartphone with 108MP camera, 120Hz display, and all-day battery life.",
      short_description: "Value smartphone with 108MP camera and 120Hz display",
      specifications: [
        { name: "Display", value: "6.67-inch AMOLED 120Hz", category: "Display" },
        { name: "Processor", value: "Snapdragon 685", category: "Performance" },
        { name: "Camera", value: "108MP Main + 8MP Ultra Wide + 2MP Macro", category: "Camera" },
        { name: "Battery", value: "5000mAh with 33W fast charging", category: "Battery" }
      ],
      variants: [
        { name: "Redmi Note 13 128GB Mint Blue", sku: "XRN13128-MB", price: 450000, storage: "128GB", color: "Mint Blue", stock_quantity: 35 },
        { name: "Redmi Note 13 256GB Mint Blue", sku: "XRN13256-MB", price: 520000, storage: "256GB", color: "Mint Blue", stock_quantity: 25 }
      ],
      weight: 188,
      track_inventory: true,
      stock_quantity: 60,
      low_stock_threshold: 10,
      requires_imei_tracking: true
    }
  ],

  // Repair Tools and Parts
  repair_tools: [
    {
      name: "iPhone Repair Tool Kit",
      brand: "iFixit",
      model: "Essential Electronics Toolkit",
      category: "other",
      price: 85000,
      cost_price: 55000,
      description: "Professional repair toolkit with precision screwdrivers, spudgers, and opening tools for iPhone repairs.",
      short_description: "Professional iPhone repair toolkit",
      specifications: [
        { name: "Tools Included", value: "64 pieces", category: "Contents" },
        { name: "Screwdrivers", value: "Precision magnetic", category: "Tools" },
        { name: "Case", value: "Portable zippered", category: "Storage" },
        { name: "Compatibility", value: "iPhone, iPad, MacBook", category: "Compatibility" }
      ],
      variants: [
        { name: "iFixit Essential Toolkit", sku: "RPR-IFX-ESNTLKT-001", price: 85000, stock_quantity: 20 }
      ],
      weight: 400,
      track_inventory: true,
      stock_quantity: 20,
      low_stock_threshold: 5
    },
    {
      name: "Phone Screen Replacement Kit",
      brand: "Generic",
      model: "Universal Kit",
      category: "other",
      price: 25000,
      cost_price: 15000,
      description: "Basic screen replacement kit with essential tools for phone screen repairs. Includes heat gun, suction cups, and prying tools.",
      short_description: "Basic phone screen replacement toolkit",
      specifications: [
        { name: "Heat Gun", value: "Included", category: "Tools" },
        { name: "Suction Cups", value: "2 pieces", category: "Tools" },
        { name: "Prying Tools", value: "Plastic and metal", category: "Tools" },
        { name: "Screwdrivers", value: "Basic set", category: "Tools" }
      ],
      variants: [
        { name: "Screen Repair Kit", sku: "RPR-GEN-SCRNKIT-001", price: 25000, stock_quantity: 40 }
      ],
      weight: 200,
      track_inventory: true,
      stock_quantity: 40,
      low_stock_threshold: 10
    }
  ]
};

// Function to create products
const createProducts = async () => {
  console.log('🔄 Starting comprehensive product seeding...');
  
  let totalProductsCreated = 0;
  
  for (const [categoryName, products] of Object.entries(productCategories)) {
    console.log(`\n📱 Creating ${categoryName}...`);
    
    for (const productData of products) {
      try {
        // Generate main SKU
        const mainSKU = generateSKU(productData.category, productData.brand, productData.model);
        
        // Create product
        const product = new Product({
          name: productData.name,
          slug: productData.name.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, ''),
          description: productData.description,
          short_description: productData.short_description,
          category: productData.category,
          brand: productData.brand,
          model: productData.model,
          price: productData.price,
          cost_price: productData.cost_price,
          variants: productData.variants.map(variant => ({
            ...variant,
            is_active: true,
            low_stock_threshold: 5
          })),
          specifications: productData.specifications,
          images: [{
            url: `/images/${productData.category}/${productData.name.toLowerCase().replace(/\s+/g, '-')}.jpg`,
            alt_text: productData.name,
            is_primary: true,
            order: 0
          }],
          track_inventory: productData.track_inventory,
          stock_quantity: productData.stock_quantity,
          low_stock_threshold: productData.low_stock_threshold,
          sku: mainSKU,
          weight: productData.weight,
          requires_shipping: true,
          shipping_class: 'standard',
          is_active: true,
          is_featured: Math.random() > 0.7, // 30% chance of being featured
          product_type: 'basic',
          created_at: new Date(),
          updated_at: new Date()
        });
        
        await product.save();
        totalProductsCreated++;
        console.log(`  ✅ Created: ${productData.name}`);
        
      } catch (error) {
        console.error(`  ❌ Error creating ${productData.name}:`, error.message);
      }
    }
  }
  
  return totalProductsCreated;
};

// Main seeding function
const seedProducts = async () => {
  try {
    await connectDB();
    
    console.log('🗑️  Clearing existing products...');
    await Product.deleteMany({});
    
    const totalCreated = await createProducts();
    
    console.log(`\n🎉 Product seeding completed!`);
    console.log(`📊 Summary:`);
    console.log(`   - Total products created: ${totalCreated}`);
    console.log(`   - Categories covered: ${Object.keys(productCategories).length}`);
    console.log(`   - All products are active and ready for sale`);
    console.log(`   - Prices set in Tanzania Shillings (TZS)`);
    
    process.exit(0);
  } catch (error) {
    console.error('❌ Seeding failed:', error);
    process.exit(1);
  }
};

// Execute if run directly
if (require.main === module) {
  seedProducts();
}

module.exports = { seedProducts };
