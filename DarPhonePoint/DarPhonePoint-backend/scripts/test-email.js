#!/usr/bin/env node

/**
 * Email Service Test Script for Phone Point Dar
 * Tests Gmail SMTP configuration and email templates
 */

require('dotenv').config();
const nodemailer = require('nodemailer');
const logger = require('../utils/logger');
const emailSender = require('../services/emailSender');

// Email configuration from environment
const EMAIL_CONFIG = {
  host: process.env.EMAIL_HOST,
  port: process.env.EMAIL_PORT,
  user: process.env.EMAIL_USER,
  password: process.env.EMAIL_PASSWORD || process.env.EMAIL_PASS,
  from: process.env.EMAIL_FROM,
  developmentMode: process.env.EMAIL_DEVELOPMENT_MODE === 'true'
};

// Test email addresses
const TEST_EMAILS = {
  admin: process.env.EMAIL_FROM, // Send to self for testing
  test: '<EMAIL>' // Placeholder test email
};

/**
 * Test SMTP connection
 */
async function testSMTPConnection() {
  console.log('\n📧 Testing SMTP Connection...');
  console.log('================================');
  
  console.log('📋 Configuration:');
  console.log(`   Host: ${EMAIL_CONFIG.host}`);
  console.log(`   Port: ${EMAIL_CONFIG.port}`);
  console.log(`   User: ${EMAIL_CONFIG.user}`);
  console.log(`   From: ${EMAIL_CONFIG.from}`);
  console.log(`   Development Mode: ${EMAIL_CONFIG.developmentMode}`);
  console.log(`   Password: ${EMAIL_CONFIG.password ? '✅ Configured' : '❌ Missing'}`);
  
  if (!EMAIL_CONFIG.host || !EMAIL_CONFIG.user || !EMAIL_CONFIG.password) {
    console.log('\n❌ Missing email configuration!');
    return false;
  }
  
  try {
    // Create transporter
    const transporter = nodemailer.createTransporter({
      host: EMAIL_CONFIG.host,
      port: parseInt(EMAIL_CONFIG.port),
      secure: false, // true for 465, false for other ports
      auth: {
        user: EMAIL_CONFIG.user,
        pass: EMAIL_CONFIG.password
      }
    });
    
    console.log('\n🔍 Verifying SMTP connection...');
    await transporter.verify();
    
    console.log('✅ SMTP connection successful!');
    return transporter;
  } catch (error) {
    console.log('\n❌ SMTP connection failed!');
    console.log('📄 Error:', error.message);
    
    if (error.code === 'EAUTH') {
      console.log('\n💡 Authentication failed. Please check:');
      console.log('   - Gmail app password is correct');
      console.log('   - 2-factor authentication is enabled');
      console.log('   - App password is generated for this app');
    }
    
    return false;
  }
}

/**
 * Test basic email sending
 */
async function testBasicEmail(transporter) {
  console.log('\n📤 Testing Basic Email Sending...');
  console.log('==================================');
  
  try {
    const testEmail = {
      from: EMAIL_CONFIG.from,
      to: EMAIL_CONFIG.from, // Send to self
      subject: 'Phone Point Dar - Email Test',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #2563eb;">📱 Phone Point Dar Email Test</h2>
          <p>This is a test email from your Phone Point Dar system.</p>
          <div style="background: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3>✅ Email Service Status: Working</h3>
            <p><strong>Timestamp:</strong> ${new Date().toISOString()}</p>
            <p><strong>Environment:</strong> ${process.env.NODE_ENV || 'development'}</p>
            <p><strong>SMTP Host:</strong> ${EMAIL_CONFIG.host}</p>
          </div>
          <p>If you received this email, your Gmail SMTP configuration is working correctly!</p>
          <hr style="margin: 30px 0; border: none; border-top: 1px solid #e5e7eb;">
          <p style="color: #6b7280; font-size: 14px;">
            Phone Point Dar - Your Mobile Store<br>
            Dar es Salaam, Tanzania
          </p>
        </div>
      `
    };
    
    console.log(`📧 Sending test email to: ${testEmail.to}`);
    const result = await transporter.sendMail(testEmail);
    
    console.log('✅ Basic email sent successfully!');
    console.log(`📨 Message ID: ${result.messageId}`);
    console.log(`📬 Response: ${result.response}`);
    
    return true;
  } catch (error) {
    console.log('\n❌ Basic email sending failed!');
    console.log('📄 Error:', error.message);
    return false;
  }
}

/**
 * Test email service integration
 */
async function testEmailService() {
  console.log('\n🔧 Testing Email Service Integration...');
  console.log('======================================');
  
  try {
    // Test using the actual email service
    const result = await emailSender.sendEmail({
      to: EMAIL_CONFIG.from,
      subject: 'Phone Point Dar - Service Integration Test',
      template: 'test', // We'll create a simple test template
      templateData: {
        customerName: 'Test Customer',
        orderNumber: 'PPD-TEST-001',
        amount: '1,500,000 TZS',
        timestamp: new Date().toISOString()
      }
    });
    
    if (result.success) {
      console.log('✅ Email service integration working!');
      console.log(`📨 Tracking ID: ${result.trackingId}`);
      console.log(`📬 Message ID: ${result.messageId}`);
      console.log(`🎭 Mock Mode: ${result.mock || false}`);
      return true;
    } else {
      console.log('❌ Email service integration failed');
      return false;
    }
  } catch (error) {
    console.log('\n❌ Email service integration failed!');
    console.log('📄 Error:', error.message);
    return false;
  }
}

/**
 * Test Phone Point Dar specific email templates
 */
async function testPhonePointDarEmails(transporter) {
  console.log('\n📱 Testing Phone Point Dar Email Templates...');
  console.log('=============================================');
  
  const testEmails = [
    {
      name: 'Order Confirmation',
      subject: 'Order Confirmed - Phone Point Dar',
      template: 'orderConfirmation'
    },
    {
      name: 'Payment Confirmation',
      subject: 'Payment Received - Phone Point Dar',
      template: 'paymentConfirmation'
    },
    {
      name: 'Shipping Notification',
      subject: 'Your Phone is on the Way - Phone Point Dar',
      template: 'shippingNotification'
    }
  ];
  
  let successCount = 0;
  
  for (const emailTest of testEmails) {
    try {
      console.log(`\n📧 Testing ${emailTest.name}...`);
      
      const emailContent = generateTestEmailContent(emailTest.template);
      
      const result = await transporter.sendMail({
        from: EMAIL_CONFIG.from,
        to: EMAIL_CONFIG.from,
        subject: emailTest.subject,
        html: emailContent
      });
      
      console.log(`   ✅ ${emailTest.name} sent successfully`);
      console.log(`   📨 Message ID: ${result.messageId}`);
      successCount++;
      
      // Wait 1 second between emails to avoid rate limiting
      await new Promise(resolve => setTimeout(resolve, 1000));
      
    } catch (error) {
      console.log(`   ❌ ${emailTest.name} failed: ${error.message}`);
    }
  }
  
  console.log(`\n📊 Template Test Results: ${successCount}/${testEmails.length} successful`);
  return successCount === testEmails.length;
}

/**
 * Generate test email content for different templates
 */
function generateTestEmailContent(template) {
  const baseStyle = `
    <style>
      body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
      .container { max-width: 600px; margin: 0 auto; padding: 20px; }
      .header { background: #2563eb; color: white; padding: 20px; text-align: center; }
      .content { padding: 20px; background: #f9fafb; }
      .footer { padding: 20px; text-align: center; color: #6b7280; font-size: 14px; }
      .button { background: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block; }
    </style>
  `;
  
  const templates = {
    orderConfirmation: `
      ${baseStyle}
      <div class="container">
        <div class="header">
          <h1>📱 Order Confirmed!</h1>
          <p>Phone Point Dar - Your Mobile Store</p>
        </div>
        <div class="content">
          <h2>Thank you for your order!</h2>
          <p><strong>Order Number:</strong> PPD-TEST-001</p>
          <p><strong>Total:</strong> 1,500,000 TZS</p>
          <p><strong>Items:</strong> iPhone 15 Pro Max 256GB - Blue</p>
          <p>We'll process your order and send you tracking information soon.</p>
          <a href="#" class="button">Track Your Order</a>
        </div>
        <div class="footer">
          <p>Phone Point Dar | Dar es Salaam, Tanzania | +255-XXX-XXX-XXX</p>
        </div>
      </div>
    `,
    paymentConfirmation: `
      ${baseStyle}
      <div class="container">
        <div class="header">
          <h1>💳 Payment Received!</h1>
          <p>Phone Point Dar - Your Mobile Store</p>
        </div>
        <div class="content">
          <h2>Payment confirmed via M-Pesa</h2>
          <p><strong>Amount:</strong> 1,500,000 TZS</p>
          <p><strong>Transaction ID:</strong> TZ123456789</p>
          <p><strong>Order:</strong> PPD-TEST-001</p>
          <p>Your payment has been successfully processed. We'll prepare your order for shipping.</p>
        </div>
        <div class="footer">
          <p>Phone Point Dar | Dar es Salaam, Tanzania | +255-XXX-XXX-XXX</p>
        </div>
      </div>
    `,
    shippingNotification: `
      ${baseStyle}
      <div class="container">
        <div class="header">
          <h1>🚚 Your Phone is on the Way!</h1>
          <p>Phone Point Dar - Your Mobile Store</p>
        </div>
        <div class="content">
          <h2>Order shipped successfully</h2>
          <p><strong>Tracking Number:</strong> PPD-SHIP-001</p>
          <p><strong>Estimated Delivery:</strong> 2-3 business days</p>
          <p><strong>Carrier:</strong> DHL Tanzania</p>
          <p>Your iPhone 15 Pro Max is on its way to you!</p>
          <a href="#" class="button">Track Package</a>
        </div>
        <div class="footer">
          <p>Phone Point Dar | Dar es Salaam, Tanzania | +255-XXX-XXX-XXX</p>
        </div>
      </div>
    `
  };
  
  return templates[template] || templates.orderConfirmation;
}

/**
 * Main test function
 */
async function runEmailTests() {
  console.log('📧 Phone Point Dar Email Service Test Suite');
  console.log('===========================================');
  console.log(`Timestamp: ${new Date().toISOString()}\n`);
  
  let allTestsPassed = true;
  const results = {};
  
  // Test 1: SMTP Connection
  const transporter = await testSMTPConnection();
  results.smtpConnection = !!transporter;
  if (!transporter) {
    allTestsPassed = false;
  }
  
  // Test 2: Basic Email (only if SMTP works)
  if (transporter) {
    results.basicEmail = await testBasicEmail(transporter);
    if (!results.basicEmail) allTestsPassed = false;
  }
  
  // Test 3: Email Service Integration
  results.serviceIntegration = await testEmailService();
  if (!results.serviceIntegration) allTestsPassed = false;
  
  // Test 4: Phone Point Dar Templates (only if basic email works)
  if (transporter && results.basicEmail) {
    results.templateEmails = await testPhonePointDarEmails(transporter);
    if (!results.templateEmails) allTestsPassed = false;
  }
  
  // Summary
  console.log('\n📋 Email Test Summary');
  console.log('=====================');
  console.log(`SMTP Connection: ${results.smtpConnection ? '✅ PASSED' : '❌ FAILED'}`);
  console.log(`Basic Email: ${results.basicEmail ? '✅ PASSED' : '❌ FAILED'}`);
  console.log(`Service Integration: ${results.serviceIntegration ? '✅ PASSED' : '❌ FAILED'}`);
  console.log(`Template Emails: ${results.templateEmails ? '✅ PASSED' : '❌ FAILED'}`);
  console.log(`Overall Status: ${allTestsPassed ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED'}`);
  
  if (allTestsPassed) {
    console.log('\n🎉 Email service is working correctly!');
    console.log('Phone Point Dar can now send emails to customers.');
    console.log('\n📧 Check your inbox for test emails.');
  } else {
    console.log('\n⚠️ Email service needs attention.');
    console.log('Please check the error messages above and fix the issues.');
  }
  
  process.exit(allTestsPassed ? 0 : 1);
}

// Run tests if script is executed directly
if (require.main === module) {
  runEmailTests().catch(error => {
    console.error('💥 Email test suite crashed:', error);
    process.exit(1);
  });
}

module.exports = {
  testSMTPConnection,
  testBasicEmail,
  testEmailService,
  runEmailTests
};
