/**
 * Migration script to update existing products with new comprehensive category system
 * Run this script after updating the Product model to ensure data consistency
 */

const mongoose = require('mongoose');
const Product = require('../models/Product');
const { categorySpecifications } = require('../config/categorySpecifications');
const { brandCategories } = require('../config/brandCategories');
require('dotenv').config();

// Category mapping for existing products
const categoryMigrationMap = {
  // Old category -> New category
  'smartphones': 'smartphone',
  'tablets': 'tablet',
  'accessories': 'accessory',
  'smartwatches': 'smartwatch',
  'Wearables': 'smartwatch',
  'Audio': 'headphones',
  'Chargers': 'charger',
  'Cases': 'case',
  'Screen Protectors': 'screen_protector'
};

// Brand normalization map
const brandNormalizationMap = {
  'apple': 'Apple',
  'samsung': 'Samsung',
  'huawei': 'Huawei',
  'xiaomi': 'Xiaomi',
  'oppo': 'Oppo',
  'vivo': 'Vivo',
  'realme': 'Realme',
  'oneplus': 'OnePlus',
  'google': 'Google',
  'sony': 'Sony',
  'nokia': 'Nokia',
  'tecno': 'Tecno',
  'infinix': 'Infinix'
};

/**
 * Connect to MongoDB
 */
const connectDB = async () => {
  try {
    const mongoUri = process.env.MONGO_URI || 'mongodb://localhost:27017/phonepointdar';
    await mongoose.connect(mongoUri);
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  }
};

/**
 * Migrate product categories
 */
const migrateCategories = async () => {
  try {
    console.log('🔄 Starting category migration...');
    
    const products = await Product.find({});
    console.log(`📦 Found ${products.length} products to migrate`);
    
    let migrated = 0;
    let errors = 0;
    
    for (const product of products) {
      try {
        let updated = false;
        const updates = {};
        
        // Migrate category
        if (product.category && categoryMigrationMap[product.category]) {
          updates.category = categoryMigrationMap[product.category];
          updated = true;
          console.log(`📝 Migrating category: ${product.category} -> ${updates.category} for ${product.name}`);
        }
        
        // Normalize brand
        if (product.brand) {
          const normalizedBrand = brandNormalizationMap[product.brand.toLowerCase()] || product.brand;
          if (normalizedBrand !== product.brand) {
            updates.brand = normalizedBrand;
            updated = true;
            console.log(`📝 Normalizing brand: ${product.brand} -> ${normalizedBrand} for ${product.name}`);
          }
        }
        
        // Add default specifications if missing
        if (!product.specifications || product.specifications.length === 0) {
          const category = updates.category || product.category;
          if (categorySpecifications[category]) {
            const template = categorySpecifications[category];
            updates.specifications = template.specifications.slice(0, 3).map(spec => ({
              name: spec.name,
              value: 'Not specified',
              category: spec.category,
              unit: spec.unit,
              is_key_spec: spec.is_key_spec
            }));
            updated = true;
            console.log(`📝 Adding default specifications for ${product.name}`);
          }
        }
        
        // Update product if changes were made
        if (updated) {
          await Product.findByIdAndUpdate(product._id, updates);
          migrated++;
          console.log(`✅ Updated product: ${product.name}`);
        }
        
      } catch (error) {
        console.error(`❌ Error updating product ${product.name}:`, error.message);
        errors++;
      }
    }
    
    console.log(`\n📊 Migration Summary:`);
    console.log(`✅ Successfully migrated: ${migrated} products`);
    console.log(`❌ Errors: ${errors} products`);
    console.log(`📦 Total processed: ${products.length} products`);
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
    throw error;
  }
};

/**
 * Add sample products for new categories
 */
const addSampleProducts = async () => {
  try {
    console.log('\n🔄 Adding sample products for new categories...');
    
    const sampleProducts = [
      // Laptop
      {
        name: 'Dell XPS 13',
        brand: 'Dell',
        model: 'XPS 13',
        category: 'laptop',
        slug: 'dell-xps-13-laptop',
        price: 2500000, // TZS
        cost_price: 1800000,
        description: 'Premium ultrabook with Intel Core i7, 16GB RAM, and 512GB SSD. Perfect for professionals and students.',
        short_description: 'Premium ultrabook with excellent performance',
        specifications: [
          { name: 'Processor', value: 'Intel Core i7-1360P', category: 'processor', unit: '', is_key_spec: true },
          { name: 'RAM', value: '16', category: 'memory', unit: 'GB', is_key_spec: true },
          { name: 'Storage Type', value: 'NVMe SSD', category: 'storage', unit: '', is_key_spec: true },
          { name: 'Storage Capacity', value: '512', category: 'storage', unit: 'GB', is_key_spec: true },
          { name: 'Display Size', value: '13.4', category: 'display', unit: 'inches', is_key_spec: true },
          { name: 'Graphics Card', value: 'Intel Iris Xe', category: 'graphics', unit: '', is_key_spec: true },
          { name: 'Operating System', value: 'Windows 11', category: 'software', unit: '', is_key_spec: true },
          { name: 'Battery Life', value: '12', category: 'battery', unit: 'hours', is_key_spec: true },
          { name: 'Weight', value: '1.27', category: 'dimensions', unit: 'kg', is_key_spec: true }
        ],
        stock_quantity: 5,
        low_stock_threshold: 2,
        sku: 'DELL-XPS13-I7-16-512',
        variants: [{
          sku: 'DELL-XPS13-I7-16-512',
          name: 'Dell XPS 13 - i7/16GB/512GB',
          price: 2500000,
          cost_price: 1800000,
          stock_quantity: 5,
          is_active: true
        }],
        warranty_period: '2 years',
        status: 'active',
        is_featured: true
      },
      
      // Gaming Console
      {
        name: 'PlayStation 5',
        brand: 'PlayStation',
        model: 'PS5',
        category: 'gaming_console',
        slug: 'playstation-5-gaming-console',
        price: 1200000, // TZS
        cost_price: 900000,
        description: 'Next-generation gaming console with 4K gaming, ray tracing, and ultra-fast SSD loading.',
        short_description: 'Next-gen gaming console with 4K support',
        specifications: [
          { name: 'Platform', value: 'PlayStation 5', category: 'gaming_performance', unit: '', is_key_spec: true },
          { name: 'Processor', value: 'AMD Zen 2', category: 'performance', unit: '', is_key_spec: true },
          { name: 'Storage', value: '825', category: 'storage', unit: 'GB', is_key_spec: true },
          { name: 'Max Resolution', value: '4K UHD', category: 'display', unit: '', is_key_spec: true },
          { name: 'Max Frame Rate', value: '120', category: 'gaming_performance', unit: 'fps', is_key_spec: true },
          { name: 'Backwards Compatibility', value: 'PS4 Games', category: 'compatibility', unit: '', is_key_spec: false },
          { name: 'Controller Included', value: 'DualSense Wireless', category: 'accessories', unit: '', is_key_spec: false }
        ],
        stock_quantity: 3,
        low_stock_threshold: 1,
        sku: 'SONY-PS5-825GB',
        variants: [{
          sku: 'SONY-PS5-825GB',
          name: 'PlayStation 5 - 825GB SSD',
          price: 1200000,
          cost_price: 900000,
          stock_quantity: 3,
          is_active: true
        }],
        warranty_period: '1 year',
        status: 'active',
        is_featured: true
      },
      
      // Router
      {
        name: 'TP-Link Archer AX73',
        brand: 'TP-Link',
        model: 'Archer AX73',
        category: 'router',
        slug: 'tp-link-archer-ax73-wifi6-router',
        price: 350000, // TZS
        cost_price: 250000,
        description: 'Wi-Fi 6 router with high-speed connectivity, perfect for homes and small offices.',
        short_description: 'Wi-Fi 6 router with high-speed connectivity',
        specifications: [
          { name: 'WiFi Standard', value: 'Wi-Fi 6 (802.11ax)', category: 'wireless', unit: '', is_key_spec: true },
          { name: 'Max Speed', value: '5400', category: 'speed', unit: 'Mbps', is_key_spec: true },
          { name: 'Frequency Bands', value: 'Dual Band (2.4GHz + 5GHz)', category: 'wireless', unit: '', is_key_spec: true },
          { name: 'Ethernet Ports', value: '4 x Gigabit', category: 'ethernet', unit: '', is_key_spec: true },
          { name: 'Range', value: '200', category: 'range', unit: 'm', is_key_spec: true },
          { name: 'Antenna Count', value: '6', category: 'wireless', unit: '', is_key_spec: false }
        ],
        stock_quantity: 8,
        low_stock_threshold: 3,
        sku: 'TPLINK-AX73-WIFI6',
        variants: [{
          sku: 'TPLINK-AX73-WIFI6',
          name: 'TP-Link Archer AX73 - Wi-Fi 6',
          price: 350000,
          cost_price: 250000,
          stock_quantity: 8,
          is_active: true
        }],
        warranty_period: '2 years',
        status: 'active',
        is_featured: false
      }
    ];
    
    let added = 0;
    for (const productData of sampleProducts) {
      try {
        // Check if product already exists
        const existing = await Product.findOne({ sku: productData.sku });
        if (!existing) {
          await Product.create(productData);
          added++;
          console.log(`✅ Added sample product: ${productData.name}`);
        } else {
          console.log(`⏭️  Product already exists: ${productData.name}`);
        }
      } catch (error) {
        console.error(`❌ Error adding product ${productData.name}:`, error.message);
      }
    }
    
    console.log(`\n📊 Sample Products Summary:`);
    console.log(`✅ Successfully added: ${added} products`);
    
  } catch (error) {
    console.error('❌ Failed to add sample products:', error);
    throw error;
  }
};

/**
 * Main migration function
 */
const runMigration = async () => {
  try {
    console.log('🚀 Starting Phone Point Dar category migration...\n');
    
    await connectDB();
    await migrateCategories();
    await addSampleProducts();
    
    console.log('\n🎉 Migration completed successfully!');
    console.log('📝 Next steps:');
    console.log('   1. Update frontend forms to use new categories');
    console.log('   2. Test product creation with new categories');
    console.log('   3. Update admin dashboard filters');
    
  } catch (error) {
    console.error('\n💥 Migration failed:', error);
    process.exit(1);
  } finally {
    await mongoose.connection.close();
    console.log('🔌 Database connection closed');
  }
};

// Run migration if called directly
if (require.main === module) {
  runMigration();
}

module.exports = {
  runMigration,
  migrateCategories,
  addSampleProducts
};
