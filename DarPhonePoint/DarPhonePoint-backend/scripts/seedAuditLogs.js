const mongoose = require('mongoose');
const connectDB = require('../config/db');
const AuditLog = require('../models/AuditLog');
const User = require('../models/User');
const Product = require('../models/Product');
const Order = require('../models/Order');
const logger = require('../utils/logger');

// Connect to database
connectDB();

// Generate a random date within the last 30 days
const randomDate = () => {
  const now = new Date();
  const daysAgo = Math.floor(Math.random() * 30);
  const hoursAgo = Math.floor(Math.random() * 24);
  const minutesAgo = Math.floor(Math.random() * 60);
  
  now.setDate(now.getDate() - daysAgo);
  now.setHours(now.getHours() - hoursAgo);
  now.setMinutes(now.getMinutes() - minutesAgo);
  
  return now;
};

// Generate a random action
const randomAction = () => {
  const actions = [
    'create',
    'update',
    'delete',
    'login',
    'logout',
    'view',
    'export',
    'import',
    'settings_change',
    'password_reset',
    'email_sent',
    'payment_processed',
    'refund_processed'
  ];
  
  return actions[Math.floor(Math.random() * actions.length)];
};

// Generate a random resource type
const randomResourceType = () => {
  const resourceTypes = [
    'user',
    'product',
    'order',
    'lead',
    'email_sequence',
    'settings',
    'analytics',
    'auth',
    'payment'
  ];
  
  return resourceTypes[Math.floor(Math.random() * resourceTypes.length)];
};

// Generate a random description
const getDescription = (action, resourceType, resourceName) => {
  const descriptions = {
    create: `Created new ${resourceType}: ${resourceName}`,
    update: `Updated ${resourceType}: ${resourceName}`,
    delete: `Deleted ${resourceType}: ${resourceName}`,
    login: 'User logged in',
    logout: 'User logged out',
    view: `Viewed ${resourceType}: ${resourceName}`,
    export: `Exported ${resourceType} data`,
    import: `Imported ${resourceType} data`,
    settings_change: 'Changed system settings',
    password_reset: 'Reset password',
    email_sent: `Sent email to ${resourceName}`,
    payment_processed: `Processed payment for order ${resourceName}`,
    refund_processed: `Processed refund for order ${resourceName}`
  };
  
  return descriptions[action] || `Performed ${action} on ${resourceType}: ${resourceName}`;
};

// Seed audit logs
const seedAuditLogs = async () => {
  try {
    // Clear existing audit logs
    await AuditLog.deleteMany({});
    
    // Get users, products, and orders for reference
    const users = await User.find({});
    const products = await Product.find({});
    const orders = await Order.find({});
    
    if (users.length === 0) {
      logger.error('No users found. Please seed users first.');
      process.exit(1);
    }
    
    // Create sample audit logs
    const auditLogs = [];
    
    // Generate 100 random audit logs
    for (let i = 0; i < 100; i++) {
      const action = randomAction();
      const resourceType = randomResourceType();
      
      // Get a random user
      const user = users[Math.floor(Math.random() * users.length)];
      
      // Get resource ID and name based on resource type
      let resourceId = '';
      let resourceName = '';
      
      if (resourceType === 'user' && users.length > 0) {
        const resource = users[Math.floor(Math.random() * users.length)];
        resourceId = resource._id;
        resourceName = resource.name;
      } else if (resourceType === 'product' && products.length > 0) {
        const resource = products[Math.floor(Math.random() * products.length)];
        resourceId = resource._id;
        resourceName = resource.name;
      } else if (resourceType === 'order' && orders.length > 0) {
        const resource = orders[Math.floor(Math.random() * orders.length)];
        resourceId = resource._id;
        resourceName = resource._id.toString().substring(0, 8);
      } else {
        resourceId = new mongoose.Types.ObjectId();
        resourceName = `Sample ${resourceType} ${i}`;
      }
      
      // Create audit log
      auditLogs.push({
        user: user._id,
        action,
        resource_type: resourceType,
        resource_id: resourceId,
        description: getDescription(action, resourceType, resourceName),
        ip_address: '127.0.0.1',
        user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        created_at: randomDate()
      });
    }
    
    // Insert audit logs
    await AuditLog.insertMany(auditLogs);
    
    logger.info(`${auditLogs.length} audit logs seeded successfully`);
    process.exit(0);
  } catch (error) {
    logger.error('Error seeding audit logs', { error: error.message });
    process.exit(1);
  }
};

// Run the seed function
seedAuditLogs();
