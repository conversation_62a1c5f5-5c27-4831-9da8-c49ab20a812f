const mongoose = require('mongoose');
const Brand = require('../models/Brand');
const config = require('../config/config');

// Phone brands data
const phoneBrands = [
  {
    name: 'Apple',
    description: 'American multinational technology company known for iPhone, iPad, and innovative mobile devices',
    country_of_origin: 'United States',
    website_url: 'https://www.apple.com',
    is_featured: true,
    display_order: 1
  },
  {
    name: 'Samsung',
    description: 'South Korean multinational conglomerate, leading manufacturer of smartphones and mobile devices',
    country_of_origin: 'South Korea',
    website_url: 'https://www.samsung.com',
    is_featured: true,
    display_order: 2
  },
  {
    name: 'Google',
    description: 'American multinational technology company, creator of Pixel smartphones with pure Android experience',
    country_of_origin: 'United States',
    website_url: 'https://store.google.com',
    is_featured: true,
    display_order: 3
  },
  {
    name: 'OnePlus',
    description: 'Chinese smartphone manufacturer known for flagship killer devices with premium features',
    country_of_origin: 'China',
    website_url: 'https://www.oneplus.com',
    is_featured: true,
    display_order: 4
  },
  {
    name: '<PERSON><PERSON>',
    description: 'Chinese electronics company offering high-quality smartphones at competitive prices',
    country_of_origin: 'China',
    website_url: 'https://www.mi.com',
    is_featured: true,
    display_order: 5
  },
  {
    name: 'Huawei',
    description: 'Chinese multinational technology corporation, known for advanced camera technology in smartphones',
    country_of_origin: 'China',
    website_url: 'https://www.huawei.com',
    is_featured: false,
    display_order: 6
  },
  {
    name: 'Oppo',
    description: 'Chinese consumer electronics and mobile communications company',
    country_of_origin: 'China',
    website_url: 'https://www.oppo.com',
    is_featured: false,
    display_order: 7
  },
  {
    name: 'Vivo',
    description: 'Chinese technology company known for camera-focused smartphones',
    country_of_origin: 'China',
    website_url: 'https://www.vivo.com',
    is_featured: false,
    display_order: 8
  },
  {
    name: 'Realme',
    description: 'Chinese smartphone brand offering feature-rich devices for young consumers',
    country_of_origin: 'China',
    website_url: 'https://www.realme.com',
    is_featured: false,
    display_order: 9
  },
  {
    name: 'Nokia',
    description: 'Finnish multinational telecommunications company with durable and reliable smartphones',
    country_of_origin: 'Finland',
    website_url: 'https://www.nokia.com',
    is_featured: false,
    display_order: 10
  },
  {
    name: 'Sony',
    description: 'Japanese multinational conglomerate known for Xperia smartphones with advanced camera technology',
    country_of_origin: 'Japan',
    website_url: 'https://www.sony.com',
    is_featured: false,
    display_order: 11
  },
  {
    name: 'Motorola',
    description: 'American telecommunications company offering reliable smartphones with near-stock Android',
    country_of_origin: 'United States',
    website_url: 'https://www.motorola.com',
    is_featured: false,
    display_order: 12
  },
  {
    name: 'LG',
    description: 'South Korean multinational electronics company (legacy smartphone support)',
    country_of_origin: 'South Korea',
    website_url: 'https://www.lg.com',
    is_featured: false,
    display_order: 13
  },
  {
    name: 'Nothing',
    description: 'British consumer technology company known for transparent design smartphones',
    country_of_origin: 'United Kingdom',
    website_url: 'https://www.nothing.tech',
    is_featured: false,
    display_order: 14
  },
  {
    name: 'Fairphone',
    description: 'Dutch social enterprise creating sustainable and repairable smartphones',
    country_of_origin: 'Netherlands',
    website_url: 'https://www.fairphone.com',
    is_featured: false,
    display_order: 15
  }
];

async function setupBrands() {
  try {
    // Connect to MongoDB
    await mongoose.connect(config.MONGO_URI);
    console.log('Connected to MongoDB');

    // Clear existing brands
    await Brand.deleteMany({});
    console.log('Cleared existing brands');

    // Create brands
    for (const brandData of phoneBrands) {
      const brand = await Brand.create(brandData);
      console.log(`Created brand: ${brand.name}`);
    }

    console.log('Phone brands setup completed successfully!');
    
  } catch (error) {
    console.error('Error setting up phone brands:', error);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  }
}

// Run the setup if this file is executed directly
if (require.main === module) {
  setupBrands();
}

module.exports = setupBrands;
