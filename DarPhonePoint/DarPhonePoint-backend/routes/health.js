const express = require('express');
const mongoose = require('mongoose');
const router = express.Router();
const env = require('../config/env');
const productionMonitoringService = require('../services/productionMonitoringService');
const { protect, authorize } = require('../middleware/auth');

/**
 * @swagger
 * /api/health:
 *   get:
 *     summary: Health check endpoint
 *     description: Returns the health status of the API and its dependencies
 *     tags: [Health]
 *     responses:
 *       200:
 *         description: Service is healthy
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "healthy"
 *                 timestamp:
 *                   type: string
 *                   format: date-time
 *                 version:
 *                   type: string
 *                   example: "1.0.0"
 *                 environment:
 *                   type: string
 *                   example: "production"
 *                 services:
 *                   type: object
 *                   properties:
 *                     database:
 *                       type: object
 *                       properties:
 *                         status:
 *                           type: string
 *                         responseTime:
 *                           type: number
 *       503:
 *         description: Service is unhealthy
 */
router.get('/', async (req, res) => {
  const healthCheck = {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: process.env.npm_package_version || '1.0.0',
    environment: env.NODE_ENV,
    uptime: process.uptime(),
    services: {}
  };

  let isHealthy = true;

  // Check database connection
  try {
    const dbStart = Date.now();
    await mongoose.connection.db.admin().ping();
    healthCheck.services.database = {
      status: 'healthy',
      responseTime: Date.now() - dbStart
    };
  } catch (error) {
    isHealthy = false;
    healthCheck.services.database = {
      status: 'unhealthy',
      error: error.message
    };
  }

  // Check memory usage
  const memoryUsage = process.memoryUsage();
  healthCheck.memory = {
    rss: Math.round(memoryUsage.rss / 1024 / 1024), // MB
    heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024), // MB
    heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024), // MB
    external: Math.round(memoryUsage.external / 1024 / 1024) // MB
  };

  // Set overall status
  if (!isHealthy) {
    healthCheck.status = 'unhealthy';
    return res.status(503).json(healthCheck);
  }

  res.json(healthCheck);
});

/**
 * @swagger
 * /api/health/ready:
 *   get:
 *     summary: Readiness check endpoint
 *     description: Returns whether the service is ready to accept traffic
 *     tags: [Health]
 *     responses:
 *       200:
 *         description: Service is ready
 *       503:
 *         description: Service is not ready
 */
router.get('/ready', async (req, res) => {
  try {
    // Check if database is connected
    if (mongoose.connection.readyState !== 1) {
      return res.status(503).json({
        status: 'not ready',
        reason: 'Database not connected'
      });
    }

    res.json({
      status: 'ready',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(503).json({
      status: 'not ready',
      error: error.message
    });
  }
});

/**
 * @swagger
 * /api/health/live:
 *   get:
 *     summary: Liveness check endpoint
 *     description: Returns whether the service is alive
 *     tags: [Health]
 *     responses:
 *       200:
 *         description: Service is alive
 */
router.get('/live', (req, res) => {
  res.json({
    status: 'alive',
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  });
});

/**
 * @swagger
 * /api/health/detailed:
 *   get:
 *     summary: Detailed health check with system metrics
 *     description: Returns comprehensive health status including system metrics (Admin only)
 *     tags: [Health]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Detailed health status
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden
 */
router.get('/detailed', protect, authorize('admin'), async (req, res) => {
  try {
    const healthStatus = await productionMonitoringService.performHealthCheck();
    const metrics = productionMonitoringService.getMetrics();

    const detailedHealth = {
      ...healthStatus,
      metrics: {
        system: metrics.system,
        application: metrics.application,
        database: metrics.database,
        cache: metrics.cache
      },
      alerts: metrics.alerts.slice(-10), // Last 10 alerts
      summary: metrics.summary
    };

    const statusCode = healthStatus.status === 'healthy' ? 200 : 503;
    res.status(statusCode).json(detailedHealth);

  } catch (error) {
    res.status(500).json({
      status: 'error',
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * @swagger
 * /api/health/metrics:
 *   get:
 *     summary: Get current system metrics
 *     description: Returns current system and application metrics (Admin only)
 *     tags: [Health]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Current metrics
 */
router.get('/metrics', protect, authorize('admin'), (req, res) => {
  try {
    const metrics = productionMonitoringService.getMetrics();
    res.status(200).json({
      success: true,
      data: metrics
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @swagger
 * /api/health/alerts:
 *   get:
 *     summary: Get current alerts
 *     description: Returns current system alerts (Admin only)
 *     tags: [Health]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Current alerts
 */
router.get('/alerts', protect, authorize('admin'), (req, res) => {
  try {
    const metrics = productionMonitoringService.getMetrics();
    res.status(200).json({
      success: true,
      data: {
        alerts: metrics.alerts,
        summary: {
          total: metrics.alerts.length,
          critical: metrics.alerts.filter(a => a.severity === 'critical').length,
          warning: metrics.alerts.filter(a => a.severity === 'warning').length,
          info: metrics.alerts.filter(a => a.severity === 'info').length
        }
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

module.exports = router;
