const express = require('express');
const router = express.Router();
const Product = require('../models/Product');
const Order = require('../models/Order');
const User = require('../models/User');
const SerialNumber = require('../models/SerialNumber');
const Supplier = require('../models/Supplier');
const AdvancedSearchService = require('../services/advancedSearchService');
const { protect, authorize } = require('../middleware/auth');
const { validateRequest } = require('../middleware/enhancedValidation');

/**
 * Admin Dashboard Routes for Phone Point Dar
 * Provides comprehensive analytics and insights
 */

// Apply authentication and admin authorization to all routes
router.use(protect);
router.use(authorize('admin'));

/**
 * @route   GET /api/admin/dashboard/overview
 * @desc    Get dashboard overview statistics
 * @access  Private/Admin
 */
router.get('/overview', async (req, res) => {
  try {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    // Get overview statistics in parallel
    const [
      totalRevenue,
      totalOrders,
      totalProducts,
      totalCustomers,
      todayOrders,
      pendingOrders,
      lowStockProducts
    ] = await Promise.all([
      // Total revenue from completed orders
      Order.aggregate([
        { $match: { status: { $in: ['completed', 'delivered'] } } },
        { $group: { _id: null, total: { $sum: '$total' } } }
      ]),
      
      // Total orders count
      Order.countDocuments(),
      
      // Total products count
      Product.countDocuments({ status: 'active' }),
      
      // Total customers count
      User.countDocuments({ role: 'customer' }),
      
      // Today's orders
      Order.aggregate([
        { 
          $match: { 
            createdAt: { $gte: today, $lt: tomorrow }
          } 
        },
        {
          $group: {
            _id: null,
            count: { $sum: 1 },
            revenue: { $sum: '$total' }
          }
        }
      ]),
      
      // Pending orders count
      Order.countDocuments({ status: 'pending' }),
      
      // Low stock products count
      Product.countDocuments({
        status: 'active',
        $expr: { $lte: ['$stock_quantity', '$low_stock_threshold'] }
      })
    ]);

    const overview = {
      totalRevenue: totalRevenue[0]?.total || 0,
      totalOrders,
      totalProducts,
      totalCustomers,
      todayOrders: todayOrders[0]?.count || 0,
      todayRevenue: todayOrders[0]?.revenue || 0,
      pendingOrders,
      lowStockItems: lowStockProducts
    };

    res.status(200).json({
      success: true,
      data: overview
    });

  } catch (error) {
    console.error('Dashboard overview error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch dashboard overview'
    });
  }
});

/**
 * @route   GET /api/admin/dashboard/category-stats
 * @desc    Get sales statistics by product category
 * @access  Private/Admin
 */
router.get('/category-stats', async (req, res) => {
  try {
    const categoryStats = await Product.aggregate([
      {
        $match: { status: 'active' }
      },
      {
        $group: {
          _id: '$category',
          count: { $sum: 1 },
          totalValue: { $sum: { $multiply: ['$price', '$stock_quantity'] } },
          averagePrice: { $avg: '$price' }
        }
      },
      {
        $lookup: {
          from: 'orders',
          let: { category: '$_id' },
          pipeline: [
            { $unwind: '$items' },
            {
              $lookup: {
                from: 'products',
                localField: 'items.product',
                foreignField: '_id',
                as: 'productInfo'
              }
            },
            { $unwind: '$productInfo' },
            {
              $match: {
                $expr: { $eq: ['$productInfo.category', '$$category'] },
                status: { $in: ['completed', 'delivered'] }
              }
            },
            {
              $group: {
                _id: null,
                revenue: { $sum: { $multiply: ['$items.quantity', '$items.price'] } },
                unitsSold: { $sum: '$items.quantity' }
              }
            }
          ],
          as: 'salesData'
        }
      },
      {
        $addFields: {
          revenue: { $ifNull: [{ $arrayElemAt: ['$salesData.revenue', 0] }, 0] },
          unitsSold: { $ifNull: [{ $arrayElemAt: ['$salesData.unitsSold', 0] }, 0] }
        }
      },
      {
        $project: {
          category: '$_id',
          count: 1,
          totalValue: 1,
          averagePrice: 1,
          revenue: 1,
          unitsSold: 1,
          _id: 0
        }
      },
      { $sort: { revenue: -1 } }
    ]);

    // Calculate percentages
    const totalRevenue = categoryStats.reduce((sum, cat) => sum + cat.revenue, 0);
    const statsWithPercentage = categoryStats.map(stat => ({
      ...stat,
      percentage: totalRevenue > 0 ? (stat.revenue / totalRevenue) * 100 : 0
    }));

    res.status(200).json({
      success: true,
      data: statsWithPercentage
    });

  } catch (error) {
    console.error('Category stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch category statistics'
    });
  }
});

/**
 * @route   GET /api/admin/dashboard/top-products
 * @desc    Get top-selling products
 * @access  Private/Admin
 */
router.get('/top-products', async (req, res) => {
  try {
    const limit = parseInt(req.query.limit) || 5;

    const topProducts = await Order.aggregate([
      { $unwind: '$items' },
      {
        $match: {
          status: { $in: ['completed', 'delivered'] }
        }
      },
      {
        $group: {
          _id: '$items.product',
          salesCount: { $sum: '$items.quantity' },
          revenue: { $sum: { $multiply: ['$items.quantity', '$items.price'] } }
        }
      },
      {
        $lookup: {
          from: 'products',
          localField: '_id',
          foreignField: '_id',
          as: 'product'
        }
      },
      { $unwind: '$product' },
      {
        $project: {
          _id: '$product._id',
          name: '$product.name',
          brand: '$product.brand',
          category: '$product.category',
          price: '$product.price',
          salesCount: 1,
          revenue: 1
        }
      },
      { $sort: { salesCount: -1 } },
      { $limit: limit }
    ]);

    res.status(200).json({
      success: true,
      data: topProducts
    });

  } catch (error) {
    console.error('Top products error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch top products'
    });
  }
});

/**
 * @route   GET /api/admin/dashboard/sales-trends
 * @desc    Get sales trends over time
 * @access  Private/Admin
 */
router.get('/sales-trends', async (req, res) => {
  try {
    const { period = 'week' } = req.query; // week, month, year
    
    let groupBy;
    let dateRange = new Date();
    
    switch (period) {
      case 'week':
        dateRange.setDate(dateRange.getDate() - 7);
        groupBy = {
          year: { $year: '$createdAt' },
          month: { $month: '$createdAt' },
          day: { $dayOfMonth: '$createdAt' }
        };
        break;
      case 'month':
        dateRange.setMonth(dateRange.getMonth() - 1);
        groupBy = {
          year: { $year: '$createdAt' },
          month: { $month: '$createdAt' },
          day: { $dayOfMonth: '$createdAt' }
        };
        break;
      case 'year':
        dateRange.setFullYear(dateRange.getFullYear() - 1);
        groupBy = {
          year: { $year: '$createdAt' },
          month: { $month: '$createdAt' }
        };
        break;
      default:
        dateRange.setDate(dateRange.getDate() - 7);
        groupBy = {
          year: { $year: '$createdAt' },
          month: { $month: '$createdAt' },
          day: { $dayOfMonth: '$createdAt' }
        };
    }

    const salesTrends = await Order.aggregate([
      {
        $match: {
          createdAt: { $gte: dateRange },
          status: { $in: ['completed', 'delivered'] }
        }
      },
      {
        $group: {
          _id: groupBy,
          orders: { $sum: 1 },
          revenue: { $sum: '$total' },
          averageOrderValue: { $avg: '$total' }
        }
      },
      { $sort: { '_id.year': 1, '_id.month': 1, '_id.day': 1 } }
    ]);

    res.status(200).json({
      success: true,
      data: salesTrends
    });

  } catch (error) {
    console.error('Sales trends error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch sales trends'
    });
  }
});

/**
 * @route   GET /api/admin/dashboard/inventory-alerts
 * @desc    Get inventory alerts and warnings
 * @access  Private/Admin
 */
router.get('/inventory-alerts', async (req, res) => {
  try {
    const [lowStockProducts, outOfStockProducts, overstockProducts] = await Promise.all([
      // Low stock products
      Product.find({
        status: 'active',
        $expr: { 
          $and: [
            { $lte: ['$stock_quantity', '$low_stock_threshold'] },
            { $gt: ['$stock_quantity', 0] }
          ]
        }
      }).select('name brand category stock_quantity low_stock_threshold sku'),
      
      // Out of stock products
      Product.find({
        status: 'active',
        stock_quantity: 0
      }).select('name brand category sku'),
      
      // Overstock products (more than 50 units)
      Product.find({
        status: 'active',
        stock_quantity: { $gt: 50 }
      }).select('name brand category stock_quantity sku')
    ]);

    res.status(200).json({
      success: true,
      data: {
        lowStock: lowStockProducts,
        outOfStock: outOfStockProducts,
        overstock: overstockProducts
      }
    });

  } catch (error) {
    console.error('Inventory alerts error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch inventory alerts'
    });
  }
});

/**
 * @route   POST /api/admin/dashboard/bulk-operations
 * @desc    Perform bulk operations on products or orders
 * @access  Private/Admin
 */
router.post('/bulk-operations', async (req, res) => {
  try {
    const { operation, itemType, ids, data } = req.body;

    if (!operation || !itemType || !ids || !Array.isArray(ids)) {
      return res.status(400).json({
        success: false,
        message: 'Missing required fields: operation, itemType, ids'
      });
    }

    let result = {};

    switch (itemType) {
      case 'products':
        result = await handleProductBulkOperation(operation, ids, data);
        break;
      case 'orders':
        result = await handleOrderBulkOperation(operation, ids, data);
        break;
      default:
        return res.status(400).json({
          success: false,
          message: 'Invalid item type'
        });
    }

    res.status(200).json({
      success: true,
      data: result
    });

  } catch (error) {
    console.error('Bulk operation error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to perform bulk operation'
    });
  }
});

// Helper function for product bulk operations
async function handleProductBulkOperation(operation, ids, data) {
  switch (operation) {
    case 'update-status':
      return await Product.updateMany(
        { _id: { $in: ids } },
        { status: data.status }
      );

    case 'update-category':
      return await Product.updateMany(
        { _id: { $in: ids } },
        { category: data.category }
      );

    case 'update-pricing':
      const products = await Product.find({ _id: { $in: ids } });
      const updates = products.map(product => {
        let newPrice = product.price;

        switch (data.adjustmentType) {
          case 'percentage':
            newPrice = product.price * (1 + data.value / 100);
            break;
          case 'fixed':
            newPrice = product.price + data.value;
            break;
          case 'set':
            newPrice = data.value;
            break;
        }

        return {
          updateOne: {
            filter: { _id: product._id },
            update: { price: Math.max(0, newPrice) }
          }
        };
      });

      return await Product.bulkWrite(updates);

    case 'update-stock':
      const stockProducts = await Product.find({ _id: { $in: ids } });
      const stockUpdates = stockProducts.map(product => {
        let newStock = product.stock_quantity;

        switch (data.adjustmentType) {
          case 'add':
            newStock = product.stock_quantity + data.quantity;
            break;
          case 'subtract':
            newStock = product.stock_quantity - data.quantity;
            break;
          case 'set':
            newStock = data.quantity;
            break;
        }

        return {
          updateOne: {
            filter: { _id: product._id },
            update: { stock_quantity: Math.max(0, newStock) }
          }
        };
      });

      return await Product.bulkWrite(stockUpdates);

    case 'delete':
      return await Product.deleteMany({ _id: { $in: ids } });

    default:
      throw new Error('Invalid product operation');
  }
}

// Helper function for order bulk operations
async function handleOrderBulkOperation(operation, ids, data) {
  switch (operation) {
    case 'update-status':
      return await Order.updateMany(
        { _id: { $in: ids } },
        { status: data.status }
      );

    case 'mark-shipped':
      return await Order.updateMany(
        { _id: { $in: ids } },
        {
          status: 'shipped',
          shippedAt: new Date()
        }
      );

    case 'cancel':
      return await Order.updateMany(
        { _id: { $in: ids } },
        {
          status: 'cancelled',
          cancelledAt: new Date()
        }
      );

    default:
      throw new Error('Invalid order operation');
  }
}

/**
 * @route   GET /api/admin/dashboard/advanced-search
 * @desc    Advanced search across all entities
 * @access  Private/Admin
 */
router.get('/advanced-search', async (req, res) => {
  try {
    const { type, ...searchParams } = req.query;

    let results;

    switch (type) {
      case 'products':
        results = await AdvancedSearchService.searchProducts(searchParams);
        break;
      case 'serial-numbers':
        results = await AdvancedSearchService.searchSerialNumbers(searchParams);
        break;
      case 'orders':
        results = await AdvancedSearchService.searchOrders(searchParams);
        break;
      case 'global':
        results = await AdvancedSearchService.globalSearch(searchParams.query, searchParams);
        break;
      default:
        return res.status(400).json({
          success: false,
          message: 'Invalid search type. Use: products, serial-numbers, orders, or global'
        });
    }

    res.status(200).json({
      success: true,
      data: results
    });

  } catch (error) {
    console.error('Advanced search error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to perform advanced search'
    });
  }
});

/**
 * @route   GET /api/admin/dashboard/serial-numbers
 * @desc    Get serial numbers with filtering
 * @access  Private/Admin
 */
router.get('/serial-numbers', async (req, res) => {
  try {
    const {
      product,
      status = 'available',
      condition,
      page = 1,
      limit = 20
    } = req.query;

    let query = {};

    if (product) query.product = product;
    if (status !== 'all') query.status = status;
    if (condition) query.condition = condition;

    const totalCount = await SerialNumber.countDocuments(query);
    const serialNumbers = await SerialNumber.find(query)
      .populate('product', 'name brand model category price')
      .populate('supplier', 'name code')
      .sort({ createdAt: -1 })
      .skip((page - 1) * limit)
      .limit(limit);

    res.status(200).json({
      success: true,
      data: {
        serialNumbers,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: totalCount,
          pages: Math.ceil(totalCount / limit)
        }
      }
    });

  } catch (error) {
    console.error('Serial numbers fetch error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch serial numbers'
    });
  }
});

/**
 * @route   POST /api/admin/dashboard/serial-numbers
 * @desc    Create new serial number entry
 * @access  Private/Admin
 */
router.post('/serial-numbers', async (req, res) => {
  try {
    const {
      product,
      serialNumber,
      imei,
      macAddress,
      condition = 'new',
      supplier,
      purchaseDate,
      purchasePrice,
      warrantyStartDate,
      warrantyEndDate,
      location,
      notes
    } = req.body;

    // Validate required fields
    if (!product || !serialNumber) {
      return res.status(400).json({
        success: false,
        message: 'Product and serial number are required'
      });
    }

    // Check for duplicate serial number
    const existingSerial = await SerialNumber.findOne({ serialNumber });
    if (existingSerial) {
      return res.status(400).json({
        success: false,
        message: 'Serial number already exists'
      });
    }

    // Check for duplicate IMEI if provided
    if (imei) {
      const existingIMEI = await SerialNumber.findOne({ imei });
      if (existingIMEI) {
        return res.status(400).json({
          success: false,
          message: 'IMEI already exists'
        });
      }
    }

    const newSerialNumber = new SerialNumber({
      product,
      serialNumber,
      imei,
      macAddress,
      condition,
      supplier,
      purchaseDate: purchaseDate ? new Date(purchaseDate) : undefined,
      purchasePrice,
      warrantyStartDate: warrantyStartDate ? new Date(warrantyStartDate) : new Date(),
      warrantyEndDate: warrantyEndDate ? new Date(warrantyEndDate) : undefined,
      location,
      notes,
      createdBy: req.user._id
    });

    await newSerialNumber.save();
    await newSerialNumber.populate('product supplier');

    res.status(201).json({
      success: true,
      data: newSerialNumber
    });

  } catch (error) {
    console.error('Serial number creation error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create serial number entry'
    });
  }
});

/**
 * @route   GET /api/admin/dashboard/suppliers
 * @desc    Get suppliers with filtering and search
 * @access  Private/Admin
 */
router.get('/suppliers', async (req, res) => {
  try {
    const {
      search,
      category,
      brand,
      status = 'active',
      isPreferred,
      sortBy = 'name',
      sortOrder = 'asc',
      page = 1,
      limit = 20
    } = req.query;

    let query = {};

    // Status filter
    if (status !== 'all') {
      query.status = status;
    }

    // Search filter
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { code: { $regex: search, $options: 'i' } },
        { 'contactInfo.email': { $regex: search, $options: 'i' } }
      ];
    }

    // Category filter
    if (category) {
      query.categories = category;
    }

    // Brand filter
    if (brand) {
      query.brands = { $regex: brand, $options: 'i' };
    }

    // Preferred filter
    if (isPreferred !== undefined) {
      query.isPreferred = isPreferred === 'true';
    }

    // Sort criteria
    let sortCriteria = {};
    sortCriteria[sortBy] = sortOrder === 'asc' ? 1 : -1;

    const totalCount = await Supplier.countDocuments(query);
    const suppliers = await Supplier.find(query)
      .sort(sortCriteria)
      .skip((page - 1) * limit)
      .limit(limit);

    res.status(200).json({
      success: true,
      data: {
        suppliers,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: totalCount,
          pages: Math.ceil(totalCount / limit)
        }
      }
    });

  } catch (error) {
    console.error('Suppliers fetch error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch suppliers'
    });
  }
});

module.exports = router;
