/**
 * ClickPesa Payment Callback Routes
 * Handles payment notifications and callbacks from ClickPesa
 */

const express = require('express');
const router = express.Router();
const Order = require('../models/Order');
const tanzaniaPaymentService = require('../services/tanzaniaPaymentService');
const auditLogService = require('../services/auditLogService');
const logger = require('../utils/logger');
const AppError = require('../utils/AppError');

/**
 * @route   POST /api/payments/clickpesa/callback
 * @desc    Handle ClickPesa payment callback/webhook
 * @access  Public (ClickPesa webhook)
 */
// Security middleware for webhook
const webhookSecurity = (req, res, next) => {
  // Check request method
  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      message: 'Method not allowed'
    });
  }

  // Check content type
  if (!req.headers['content-type'] || !req.headers['content-type'].includes('application/json')) {
    return res.status(400).json({
      success: false,
      message: 'Invalid content type'
    });
  }

  // Rate limiting check (basic)
  const clientIP = req.ip || req.connection.remoteAddress;
  logger.info('Webhook request from IP:', clientIP);

  next();
};

router.post('/callback', webhookSecurity, async (req, res) => {
  const startTime = Date.now();

  try {
    const callbackData = req.body;
    const signature = req.headers['x-clickpesa-signature'] || req.headers['clickpesa-signature'];
    const userAgent = req.headers['user-agent'];
    const clientIP = req.ip || req.connection.remoteAddress;

    // Enhanced logging
    logger.info('ClickPesa callback received:', {
      request_id: callbackData.request_id,
      status: callbackData.status,
      transaction_id: callbackData.transaction_id,
      hasSignature: !!signature,
      userAgent,
      clientIP,
      timestamp: new Date().toISOString()
    });

    // Validate required fields
    if (!callbackData.request_id && !callbackData.transaction_id) {
      logger.error('ClickPesa callback missing required identifiers:', callbackData);
      return res.status(400).json({
        success: false,
        message: 'Missing required fields: request_id or transaction_id'
      });
    }

    // Validate status field
    const validStatuses = ['pending', 'completed', 'failed', 'cancelled'];
    if (callbackData.status && !validStatuses.includes(callbackData.status)) {
      logger.error('ClickPesa callback invalid status:', callbackData.status);
      return res.status(400).json({
        success: false,
        message: 'Invalid status value'
      });
    }

    // Process webhook with signature verification
    const webhookResult = await tanzaniaPaymentService.processWebhook(callbackData, signature);

    if (!webhookResult.paymentId) {
      logger.error('ClickPesa callback processing failed:', callbackData);
      return res.status(400).json({
        success: false,
        message: 'Invalid callback data'
      });
    }

    // Find the order by transaction ID (our request_id)
    const order = await Order.findOne({
      transaction_id: webhookResult.paymentId
    });

    if (!order) {
      logger.error('Order not found for ClickPesa callback:', {
        request_id: callbackData.request_id
      });
      return res.status(404).json({
        success: false,
        message: 'Order not found'
      });
    }

    // Map ClickPesa status to our internal status
    const statusMap = {
      'PENDING': 'pending',
      'PROCESSING': 'pending', 
      'COMPLETED': 'completed',
      'SUCCESS': 'completed',
      'FAILED': 'failed',
      'CANCELLED': 'cancelled',
      'EXPIRED': 'failed'
    };

    const newPaymentStatus = statusMap[callbackData.status?.toUpperCase()] || 'pending';
    const previousStatus = order.payment_status;

    // Update order payment status
    order.payment_status = newPaymentStatus;
    
    // Update order status based on payment status
    if (newPaymentStatus === 'completed' && order.order_status === 'pending') {
      order.order_status = 'confirmed';
      order.confirmed_at = new Date();
    } else if (newPaymentStatus === 'failed' || newPaymentStatus === 'cancelled') {
      order.order_status = 'cancelled';
      order.cancelled_at = new Date();
    }

    // Store ClickPesa transaction details
    if (callbackData.transaction_id) {
      order.provider_transaction_id = callbackData.transaction_id;
    }

    // Store callback metadata
    order.payment_metadata = {
      ...order.payment_metadata,
      clickpesa_callback: {
        received_at: new Date(),
        status: callbackData.status,
        transaction_id: callbackData.transaction_id,
        amount: callbackData.amount,
        currency: callbackData.currency,
        raw_callback: callbackData
      }
    };

    await order.save();

    // Log the payment status change
    await auditLogService.logPaymentStatusChanged(
      null, // No user for webhook
      order._id,
      previousStatus,
      newPaymentStatus,
      {
        provider: 'clickpesa',
        transaction_id: callbackData.transaction_id,
        callback_data: callbackData
      }
    );

    logger.info('ClickPesa payment status updated:', {
      order_id: order._id,
      request_id: callbackData.request_id,
      previous_status: previousStatus,
      new_status: newPaymentStatus,
      transaction_id: callbackData.transaction_id
    });

    // Send success response to ClickPesa
    res.status(200).json({
      success: true,
      message: 'Callback processed successfully',
      order_id: order._id
    });

  } catch (error) {
    logger.error('ClickPesa callback processing error:', error);
    
    // Still send success to ClickPesa to avoid retries
    // Log the error for manual investigation
    res.status(200).json({
      success: true,
      message: 'Callback received'
    });
  }
});

/**
 * @route   GET /api/payments/clickpesa/status/:orderId
 * @desc    Check payment status for an order
 * @access  Private
 */
router.get('/status/:orderId', async (req, res) => {
  try {
    const { orderId } = req.params;

    const order = await Order.findById(orderId);
    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Order not found'
      });
    }

    // If we have a transaction ID, verify with ClickPesa
    if (order.transaction_id) {
      try {
        const verificationResult = await tanzaniaPaymentService.verifyPayment(order.transaction_id);
        
        // Update order if status changed
        if (verificationResult.status !== order.payment_status) {
          const previousStatus = order.payment_status;
          order.payment_status = verificationResult.status;
          
          if (verificationResult.status === 'completed' && order.order_status === 'pending') {
            order.order_status = 'confirmed';
            order.confirmed_at = new Date();
          }
          
          await order.save();
          
          logger.info('Payment status updated via verification:', {
            order_id: order._id,
            previous_status: previousStatus,
            new_status: verificationResult.status
          });
        }
      } catch (verificationError) {
        logger.error('Payment verification failed:', verificationError);
        // Continue with current order status
      }
    }

    res.status(200).json({
      success: true,
      data: {
        order_id: order._id,
        payment_status: order.payment_status,
        order_status: order.order_status,
        transaction_id: order.transaction_id,
        provider_transaction_id: order.provider_transaction_id,
        amount: order.total,
        currency: 'TZS'
      }
    });

  } catch (error) {
    logger.error('Payment status check error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to check payment status'
    });
  }
});

module.exports = router;
