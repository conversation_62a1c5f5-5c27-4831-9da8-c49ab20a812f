/**
 * Cart Routes
 */

const express = require('express');
const router = express.Router();
const cartController = require('../controllers/cartController');
const { protect, authorize } = require('../middleware/auth');

// @route   POST /api/cart
// @desc    Add item to cart
// @access  Private
router.post('/', protect, cartController.addToCart);

// @route   GET /api/cart
// @desc    Get user cart
// @access  Private
router.get('/', protect, cartController.getCart);

// @route   PATCH /api/cart/:itemId
// @desc    Update cart item
// @access  Private
router.patch('/:itemId', protect, cartController.updateCartItem);

// @route   DELETE /api/cart/:itemId
// @desc    Remove item from cart
// @access  Private
router.delete('/:itemId', protect, cartController.removeCartItem);

// @route   DELETE /api/cart
// @desc    Clear cart
// @access  Private
router.delete('/', protect, cartController.clearCart);

// @route   POST /api/cart/calculate-shipping
// @desc    Calculate shipping for cart
// @access  Private
router.post('/calculate-shipping', protect, cartController.calculateCartShipping);

// @route   POST /api/cart/validate
// @desc    Validate cart before checkout
// @access  Private
router.post('/validate', protect, cartController.validateCart);

// @route   POST /api/cart/process-abandoned
// @desc    Process abandoned carts
// @access  Admin
router.post('/process-abandoned', protect, authorize('admin'), cartController.processAbandonedCarts);

// @route   POST /api/cart/:id/mark-abandoned
// @desc    Mark cart as abandoned
// @access  Admin
router.post('/:id/mark-abandoned', protect, authorize('admin'), cartController.markCartAbandoned);

module.exports = router;
