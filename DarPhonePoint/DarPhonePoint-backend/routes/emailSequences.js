const express = require('express');
const router = express.Router();
const emailSequenceController = require('../controllers/emailSequenceController');
const { protect, authorize } = require('../middleware/auth');

// @route   POST /api/email-sequences
// @desc    Create a new email sequence
// @access  Admin
router.post('/', protect, authorize('admin'), emailSequenceController.createEmailSequence);

// @route   GET /api/email-sequences
// @desc    Get all email sequences
// @access  Admin
router.get('/', protect, authorize('admin'), emailSequenceController.getEmailSequences);

// @route   GET /api/email-sequences/:id
// @desc    Get email sequence by ID
// @access  Admin
router.get('/:id', protect, authorize('admin'), emailSequenceController.getEmailSequence);

// @route   PUT /api/email-sequences/:id
// @desc    Update email sequence
// @access  Admin
router.put('/:id', protect, authorize('admin'), emailSequenceController.updateEmailSequence);

// @route   POST /api/email-sequences/trigger
// @desc    Trigger email sequence for a user or lead
// @access  Public
router.post('/trigger', emailSequenceController.triggerSequence);

// @route   POST /api/email-sequences/process
// @desc    Process due emails in sequences
// @access  Admin
router.post('/process', protect, authorize('admin'), emailSequenceController.processSequences);

// V2 API Routes (new model format)
// @route   POST /api/email-sequences/v2
// @desc    Create a new email sequence (v2)
// @access  Admin
router.post('/v2', protect, authorize('admin'), emailSequenceController.createEmailSequence);

// @route   GET /api/email-sequences/v2
// @desc    Get all email sequences (v2)
// @access  Admin
router.get('/v2', protect, authorize('admin'), emailSequenceController.getEmailSequences);

// @route   GET /api/email-sequences/v2/:id
// @desc    Get email sequence by ID (v2)
// @access  Admin
router.get('/v2/:id', protect, authorize('admin'), emailSequenceController.getEmailSequence);

module.exports = router;
