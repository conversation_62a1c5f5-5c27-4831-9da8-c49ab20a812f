const express = require('express');
const router = express.Router();
const emailController = require('../controllers/emailController');
const { protect, authorize } = require('../middleware/auth');
const { validateRequest, schemas } = require('../middleware/validateRequest');
const Joi = require('joi');

/**
 * @swagger
 * components:
 *   schemas:
 *     EmailRequest:
 *       type: object
 *       required:
 *         - template
 *         - to
 *         - subject
 *       properties:
 *         template:
 *           type: string
 *           description: Email template name
 *         to:
 *           type: string
 *           format: email
 *           description: Recipient email address
 *         subject:
 *           type: string
 *           description: Email subject
 *         data:
 *           type: object
 *           description: Template data
 */

// Email validation schema
const emailSchema = Joi.object({
  template: Joi.string().required().messages({
    'string.empty': 'Template is required',
    'any.required': 'Template is required'
  }),
  to: Joi.string().email().required().messages({
    'string.email': 'Please provide a valid email address',
    'string.empty': 'Recipient email is required',
    'any.required': 'Recipient email is required'
  }),
  subject: Joi.string().required().min(1).max(200).messages({
    'string.empty': 'Subject is required',
    'string.min': 'Subject must not be empty',
    'string.max': 'Subject must not exceed 200 characters',
    'any.required': 'Subject is required'
  }),
  data: Joi.object().default({})
});

/**
 * @swagger
 * /api/email/send:
 *   post:
 *     summary: Send an email
 *     tags: [Email]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/EmailRequest'
 *     responses:
 *       200:
 *         description: Email queued successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     jobId:
 *                       type: string
 *                     message:
 *                       type: string
 *       400:
 *         description: Invalid input
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.post('/send', 
  protect, 
  authorize('admin'), 
  validateRequest(emailSchema), 
  emailController.sendEmail
);

/**
 * @swagger
 * /api/email/track/{trackingId}:
 *   get:
 *     summary: Track email open
 *     tags: [Email]
 *     parameters:
 *       - in: path
 *         name: trackingId
 *         required: true
 *         schema:
 *           type: string
 *         description: Email tracking ID
 *     responses:
 *       200:
 *         description: Tracking pixel
 *         content:
 *           image/gif:
 *             schema:
 *               type: string
 *               format: binary
 */
router.get('/track/:trackingId', emailController.trackEmail);

/**
 * @swagger
 * /api/email/stats:
 *   get:
 *     summary: Get email statistics
 *     tags: [Email]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Email statistics
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     queue:
 *                       type: object
 *                     tracking:
 *                       type: object
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden
 */
router.get('/stats', 
  protect, 
  authorize('admin'), 
  emailController.getEmailStats
);

/**
 * @swagger
 * /api/email/templates:
 *   get:
 *     summary: Get available email templates
 *     tags: [Email]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: List of available templates
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: array
 *                   items:
 *                     type: string
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden
 */
router.get('/templates', 
  protect, 
  authorize('admin'), 
  emailController.getTemplates
);

/**
 * @swagger
 * /api/email/test:
 *   post:
 *     summary: Send test email
 *     tags: [Email]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - to
 *             properties:
 *               to:
 *                 type: string
 *                 format: email
 *                 description: Test email recipient
 *     responses:
 *       200:
 *         description: Test email sent
 *       400:
 *         description: Invalid input
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden
 */
router.post('/test', 
  protect, 
  authorize('admin'), 
  emailController.sendTestEmail
);

module.exports = router;
