const express = require('express');
const router = express.Router();
const analyticsController = require('../controllers/analyticsController');
const { protect, authorize } = require('../middleware/auth');

// @route   POST /api/analytics/track
// @desc    Track an analytics event
// @access  Public
router.post('/track', analyticsController.trackEvent);

// @route   GET /api/analytics/dashboard
// @desc    Get analytics dashboard data
// @access  Admin
router.get('/dashboard', protect, authorize('admin'), analyticsController.getDashboardData);

// @route   GET /api/analytics/dashboard-stats
// @desc    Get analytics dashboard stats
// @access  Admin
router.get('/dashboard-stats', protect, authorize('admin'), analyticsController.getDashboardStats);

// @route   GET /api/analytics/user-journey/:sessionId
// @desc    Get user journey data
// @access  Admin
router.get('/user-journey/:sessionId', protect, authorize('admin'), analyticsController.getUserJourney);

// @route   GET /api/analytics/heatmap
// @desc    Get weekly activity heatmap data
// @access  Admin
router.get('/heatmap', protect, authorize('admin'), analyticsController.getWeeklyActivityHeatmap);

// @route   GET /api/analytics/email-sequences
// @desc    Get all email sequences stats
// @access  Admin
router.get('/email-sequences', protect, authorize('admin'), analyticsController.getAllEmailSequencesStats);

// @route   GET /api/analytics/email-sequences/:id
// @desc    Get email sequence stats
// @access  Admin
router.get('/email-sequences/:id', protect, authorize('admin'), analyticsController.getEmailSequenceStats);

// @route   GET /api/analytics/ab-test/:emailId
// @desc    Get A/B test results for an email
// @access  Admin
router.get('/ab-test/:emailId', protect, authorize('admin'), analyticsController.getABTestResults);

// @route   GET /api/analytics/unsubscribes
// @desc    Get unsubscribe analytics
// @access  Admin
router.get('/unsubscribes', protect, authorize('admin'), analyticsController.getUnsubscribeAnalytics);

// @route   GET /api/analytics/unsubscribes/:sequenceId
// @desc    Get unsubscribe analytics for a specific sequence
// @access  Admin
router.get('/unsubscribes/:sequenceId', protect, authorize('admin'), analyticsController.getSequenceUnsubscribeAnalytics);

module.exports = router;
