const express = require('express');
const router = express.Router();
const webhookController = require('../controllers/webhookController');
const { webhookLimiter } = require('../middleware/rateLimiter');
const { validateRequest, schemas } = require('../middleware/validateRequest');
const ipWhitelist = require('../middleware/ipWhitelist');

/**
 * @swagger
 * /api/webhooks/stripe:
 *   post:
 *     summary: Handle Stripe webhook events
 *     tags: [Webhooks]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *     responses:
 *       200:
 *         description: Webhook processed successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 received:
 *                   type: boolean
 *       400:
 *         description: Invalid webhook data
 *       500:
 *         description: Server error
 */

// Apply rate limiting and IP whitelisting to all webhook routes
router.use(webhookLimiter);
router.use(ipWhitelist);

// Handle Stripe webhook
router.post('/stripe',
  webhookController.stripeWebhook
);

module.exports = router;