/**
 * Trade-In Routes
 * Routes for device trade-in functionality
 */

const express = require('express');
const router = express.Router();
const tradeInController = require('../controllers/tradeInController');
const { protect, authorize } = require('../middleware/auth');

// Public routes
router.post('/estimate', tradeInController.getTradeInEstimate);

// Protected routes (authenticated users)
router.use(protect);

router.post('/', tradeInController.createTradeIn);
router.get('/', tradeInController.getUserTradeIns);
router.get('/:id', tradeInController.getTradeInById);
router.delete('/:id', tradeInController.cancelTradeIn);

// Admin routes
router.get('/admin/all', authorize('admin'), tradeInController.getAllTradeIns);
router.patch('/:id/status', authorize('admin'), tradeInController.updateTradeInStatus);

module.exports = router;
