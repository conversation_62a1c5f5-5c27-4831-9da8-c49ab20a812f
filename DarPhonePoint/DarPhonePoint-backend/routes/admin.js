const express = require('express');
const router = express.Router();
const { protect, authorize } = require('../middleware/auth');
const MonitoringService = require('../services/monitoringService');
const paymentService = require('../services/paymentService');
const webhookBatchProcessor = require('../services/webhookBatchProcessor');
const emailSender = require('../services/emailSender');
const auditLogService = require('../services/auditLogService');
// Phone Point Dar - Admin routes for phone retail management
const logger = require('../utils/logger');

// Import models
const User = require('../models/User');
const Order = require('../models/Order');
const Product = require('../models/Product');
const Inventory = require('../models/Inventory');

/**
 * @swagger
 * /api/admin/metrics/payments:
 *   get:
 *     summary: Get payment system metrics
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Payment metrics retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 totalPayments:
 *                   type: number
 *                 successfulPayments:
 *                   type: number
 *                 failedPayments:
 *                   type: number
 *                 cancelledPayments:
 *                   type: number
 *                 webhookSuccess:
 *                   type: number
 *                 webhookFailures:
 *                   type: number
 *                 errors:
 *                   type: number
 *                 successRate:
 *                   type: string
 *                 webhookSuccessRate:
 *                   type: string
 *       401:
 *         description: Not authorized
 *       403:
 *         description: Forbidden - Admin access required
 */
router.get('/metrics/payments', protect, authorize('admin'), (req, res) => {
  res.status(200).json({
    success: true,
    data: MonitoringService.getMetrics()
  });
});

/**
 * @swagger
 * /api/admin/metrics/payments/reset:
 *   post:
 *     summary: Reset payment metrics
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Metrics reset successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *       401:
 *         description: Not authorized
 *       403:
 *         description: Forbidden - Admin access required
 */
router.post('/metrics/payments/reset', protect, authorize('admin'), (req, res) => {
  MonitoringService.resetMetrics();
  res.status(200).json({
    success: true,
    message: 'Payment metrics reset successfully'
  });
});

/**
 * @swagger
 * /api/admin/monitoring/circuit-breaker:
 *   get:
 *     summary: Get circuit breaker state
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Circuit breaker state retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     isOpen:
 *                       type: boolean
 *                     failures:
 *                       type: number
 *                     lastFailureTime:
 *                       type: string
 *                       format: date-time
 *                     failureThreshold:
 *                       type: number
 *                     resetTimeout:
 *                       type: number
 *       401:
 *         description: Not authorized
 *       403:
 *         description: Forbidden - Admin access required
 */
router.get('/monitoring/circuit-breaker', protect, authorize('admin'), (req, res) => {
  res.status(200).json({
    success: true,
    data: {
      isOpen: false,
      failures: 0,
      lastFailureTime: null,
      failureThreshold: 5,
      resetTimeout: 60000,
      message: 'Circuit breaker monitoring available for payment services'
    }
  });
});

/**
 * @swagger
 * /api/admin/monitoring/webhooks:
 *   get:
 *     summary: Get webhook processing statistics
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Webhook statistics retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     waiting:
 *                       type: number
 *                     active:
 *                       type: number
 *                     completed:
 *                       type: number
 *                     failed:
 *                       type: number
 *                     batchSize:
 *                       type: number
 *                     batchTimeout:
 *                       type: number
 *       401:
 *         description: Not authorized
 *       403:
 *         description: Forbidden - Admin access required
 */
router.get('/monitoring/webhooks', protect, authorize('admin'), async (req, res) => {
  try {
    const stats = await webhookBatchProcessor.getQueueStats();
    res.status(200).json({
      success: true,
      data: stats
    });
  } catch (error) {
    logger.error('Webhook stats error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Phone Point Dar - Admin Dashboard Statistics for Phone Retail Business

/**
 * @swagger
 * /api/admin/dashboard/stats:
 *   get:
 *     summary: Get Phone Point Dar dashboard statistics
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Dashboard statistics retrieved successfully
 *       401:
 *         description: Not authorized
 *       403:
 *         description: Forbidden - Admin access required
 */
router.get('/dashboard/stats', protect, authorize('admin'), async (req, res) => {
  try {
    const User = require('../models/User');
    const Order = require('../models/Order');
    const Product = require('../models/Product');
    const Inventory = require('../models/Inventory');

    // Get current date for monthly calculations
    const now = new Date();
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const startOfDay = new Date(now.getFullYear(), now.getMonth(), now.getDate());

    // Parallel queries for better performance
    const [
      totalCustomers,
      totalOrders,
      totalProducts,
      monthlyOrders,
      pendingOrders,
      lowStockProducts,
      recentOrders,
      topProducts
    ] = await Promise.all([
      // Total customers (non-admin users)
      User.countDocuments({ role: { $ne: 'admin' } }),

      // Total orders
      Order.countDocuments(),

      // Total products (active phones)
      Product.countDocuments({ status: 'active' }),

      // Monthly orders for revenue calculation
      Order.find({
        created_at: { $gte: startOfMonth },
        payment_status: 'completed'
      }).populate('product'),

      // Pending orders (need processing)
      Order.countDocuments({
        order_status: { $in: ['pending', 'processing'] }
      }),

      // Low stock products (less than 10 units)
      Inventory.countDocuments({ quantity: { $lt: 10 } }),

      // Recent orders (last 10)
      Order.find()
        .populate('product', 'name brand')
        .populate('user', 'name email')
        .sort({ created_at: -1 })
        .limit(10),

      // Top selling products this month
      Order.aggregate([
        {
          $match: {
            created_at: { $gte: startOfMonth },
            payment_status: 'completed'
          }
        },
        {
          $group: {
            _id: '$product',
            totalSold: { $sum: '$quantity' },
            totalRevenue: { $sum: '$total_amount' }
          }
        },
        {
          $lookup: {
            from: 'products',
            localField: '_id',
            foreignField: '_id',
            as: 'productInfo'
          }
        },
        {
          $unwind: '$productInfo'
        },
        {
          $sort: { totalSold: -1 }
        },
        {
          $limit: 5
        }
      ])
    ]);

    // Calculate monthly revenue in Tanzanian Shillings
    const monthlyRevenue = monthlyOrders.reduce((total, order) => {
      return total + (order.total_amount || 0);
    }, 0);

    // Calculate average order value
    const averageOrderValue = monthlyOrders.length > 0
      ? monthlyRevenue / monthlyOrders.length
      : 0;

    // Format response
    const stats = {
      totalCustomers,
      totalOrders,
      totalProducts,
      monthlyRevenue,
      averageOrderValue,
      pendingOrders,
      lowStockItems: lowStockProducts,
      recentOrders: recentOrders.map(order => ({
        _id: order._id,
        customer: order.user?.name || 'Unknown',
        product: {
          name: order.product?.name || 'Unknown Product',
          brand: order.product?.brand || 'Unknown Brand'
        },
        amount: order.total_amount || 0,
        status: order.order_status || 'pending',
        created_at: order.created_at
      })),
      topProducts: topProducts.map(item => ({
        name: item.productInfo.name,
        brand: item.productInfo.brand,
        totalSold: item.totalSold,
        revenue: item.totalRevenue
      }))
    };

    res.status(200).json({
      success: true,
      data: stats
    });

  } catch (error) {
    logger.error('Dashboard stats error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch dashboard statistics',
      details: error.message
    });
  }
});

/**
 * Get comprehensive dashboard overview
 */
router.get('/dashboard/overview', protect, authorize('admin'), async (req, res) => {
  try {
    const now = new Date();
    const startOfToday = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);

    const [
      totalCustomers,
      totalOrders,
      totalProducts,
      monthlyOrders,
      todayOrders,
      pendingOrders,
      lowStockProducts,
      monthlyRevenue,
      todayRevenue
    ] = await Promise.all([
      // Total customers (non-admin users)
      User.countDocuments({ role: { $ne: 'admin' } }),

      // Total orders
      Order.countDocuments(),

      // Total products (active)
      Product.countDocuments({ status: 'active' }),

      // Monthly orders for revenue calculation
      Order.find({
        created_at: { $gte: startOfMonth },
        payment_status: 'completed'
      }),

      // Today's orders
      Order.countDocuments({
        created_at: { $gte: startOfToday }
      }),

      // Pending orders (need processing)
      Order.countDocuments({
        order_status: { $in: ['pending', 'confirmed', 'processing'] }
      }),

      // Low stock products (less than 10 units)
      Product.countDocuments({
        'inventory.quantity': { $lt: 10 },
        status: 'active'
      }),

      // Monthly revenue
      Order.aggregate([
        {
          $match: {
            created_at: { $gte: startOfMonth },
            payment_status: 'completed'
          }
        },
        {
          $group: {
            _id: null,
            total: { $sum: '$total_amount' }
          }
        }
      ]),

      // Today's revenue
      Order.aggregate([
        {
          $match: {
            created_at: { $gte: startOfToday },
            payment_status: 'completed'
          }
        },
        {
          $group: {
            _id: null,
            total: { $sum: '$total_amount' }
          }
        }
      ])
    ]);

    // Calculate monthly revenue
    const monthlyRevenueAmount = monthlyRevenue[0]?.total || 0;
    const todayRevenueAmount = todayRevenue[0]?.total || 0;
    const averageOrderValue = totalOrders > 0 ? monthlyRevenueAmount / monthlyOrders.length : 0;

    const overview = {
      totalCustomers,
      totalOrders,
      totalProducts,
      monthlyRevenue: monthlyRevenueAmount,
      todayRevenue: todayRevenueAmount,
      todayOrders,
      pendingOrders,
      lowStockItems: lowStockProducts,
      averageOrderValue
    };

    res.status(200).json({
      success: true,
      data: overview
    });
  } catch (error) {
    logger.error('Dashboard overview error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch dashboard overview'
    });
  }
});

/**
 * Get category statistics for dashboard
 */
router.get('/dashboard/category-stats', protect, authorize('admin'), async (req, res) => {
  try {
    const categoryStats = await Product.aggregate([
      {
        $match: { status: 'active' }
      },
      {
        $group: {
          _id: '$category',
          count: { $sum: 1 },
          totalValue: { $sum: '$price' },
          averagePrice: { $avg: '$price' }
        }
      },
      {
        $sort: { count: -1 }
      }
    ]);

    res.status(200).json({
      success: true,
      data: categoryStats
    });
  } catch (error) {
    logger.error('Category stats error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch category statistics'
    });
  }
});

/**
 * Get top selling products for dashboard
 */
router.get('/dashboard/top-products', protect, authorize('admin'), async (req, res) => {
  try {
    const startOfMonth = new Date();
    startOfMonth.setDate(1);

    const topProducts = await Order.aggregate([
      {
        $match: {
          created_at: { $gte: startOfMonth },
          payment_status: 'completed'
        }
      },
      {
        $unwind: '$items'
      },
      {
        $group: {
          _id: '$items.product',
          totalSold: { $sum: '$items.quantity' },
          totalRevenue: { $sum: '$items.total_price' }
        }
      },
      {
        $lookup: {
          from: 'products',
          localField: '_id',
          foreignField: '_id',
          as: 'productInfo'
        }
      },
      {
        $unwind: '$productInfo'
      },
      {
        $project: {
          name: '$productInfo.name',
          brand: '$productInfo.brand',
          totalSold: 1,
          totalRevenue: 1
        }
      },
      {
        $sort: { totalSold: -1 }
      },
      {
        $limit: 10
      }
    ]);

    res.status(200).json({
      success: true,
      data: topProducts
    });
  } catch (error) {
    logger.error('Top products error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch top products'
    });
  }
});

/**
 * @swagger
 * /api/admin/templates:
 *   get:
 *     summary: Get available HTML templates
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Templates retrieved successfully
 *       401:
 *         description: Not authorized
 *       403:
 *         description: Forbidden - Admin access required
 */
router.get('/templates', protect, authorize('admin'), async (req, res) => {
  try {
    const fs = require('fs');
    const path = require('path');
    const contentDir = path.join(__dirname, '..', 'content');

    // Get all template directories
    const templates = [];
    if (fs.existsSync(contentDir)) {
      const dirs = fs.readdirSync(contentDir, { withFileTypes: true })
        .filter(dirent => dirent.isDirectory())
        .map(dirent => dirent.name);

      for (const dir of dirs) {
        const htmlPath = path.join(contentDir, dir, 'index.html');
        if (fs.existsSync(htmlPath)) {
          templates.push({
            id: dir,
            name: dir.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
            path: htmlPath,
            content: fs.readFileSync(htmlPath, 'utf8')
          });
        }
      }
    }

    res.status(200).json({
      success: true,
      templates
    });

  } catch (error) {
    logger.error('Template fetch error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch templates',
      details: error.message
    });
  }
});

/**
 * @swagger
 * /api/admin/templates/{templateId}:
 *   get:
 *     summary: Get specific HTML template content
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: templateId
 *         required: true
 *         schema:
 *           type: string
 *         description: Template ID
 *     responses:
 *       200:
 *         description: Template content retrieved successfully
 *       404:
 *         description: Template not found
 *       401:
 *         description: Not authorized
 *       403:
 *         description: Forbidden - Admin access required
 */
router.get('/templates/:templateId', protect, authorize('admin'), async (req, res) => {
  try {
    const fs = require('fs');
    const path = require('path');
    const { templateId } = req.params;

    const htmlPath = path.join(__dirname, '..', 'content', templateId, 'index.html');

    if (!fs.existsSync(htmlPath)) {
      return res.status(404).json({
        success: false,
        error: 'Template not found'
      });
    }

    const content = fs.readFileSync(htmlPath, 'utf8');

    res.status(200).json({
      success: true,
      template: {
        id: templateId,
        name: templateId.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
        content
      }
    });

  } catch (error) {
    logger.error('Template fetch error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch template',
      details: error.message
    });
  }
});

// Product Management Routes for Phone Point Dar Admin

/**
 * Get all products for admin management
 */
router.get('/products', protect, authorize('admin'), async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      search = '',
      category = '',
      brand = '',
      status = '',
      sort = '-created_at'
    } = req.query;

    // Build search query
    let searchQuery = {};

    if (search) {
      searchQuery.$or = [
        { name: { $regex: search, $options: 'i' } },
        { brand: { $regex: search, $options: 'i' } },
        { model: { $regex: search, $options: 'i' } },
        { sku: { $regex: search, $options: 'i' } }
      ];
    }

    if (category) {
      searchQuery.category = category;
    }

    if (brand) {
      searchQuery.brand = { $regex: brand, $options: 'i' };
    }

    if (status) {
      searchQuery.status = status;
    }

    // Build sort object
    let sortObj = {};
    switch (sort) {
      case 'name':
        sortObj = { name: 1 };
        break;
      case '-name':
        sortObj = { name: -1 };
        break;
      case 'price':
        sortObj = { price: 1 };
        break;
      case '-price':
        sortObj = { price: -1 };
        break;
      case 'created_at':
        sortObj = { created_at: 1 };
        break;
      case '-created_at':
        sortObj = { created_at: -1 };
        break;
      default:
        sortObj = { created_at: -1 };
    }

    const skip = (page - 1) * limit;

    // Get products with inventory information
    const products = await Product.find(searchQuery)
      .populate('inventory', 'quantity low_stock_threshold')
      .sort(sortObj)
      .limit(parseInt(limit))
      .skip(skip)
      .select('name brand model category price compare_at_price sku status images variants created_at updated_at');

    // Get total count for pagination
    const totalProducts = await Product.countDocuments(searchQuery);

    res.status(200).json({
      success: true,
      data: {
        products,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(totalProducts / limit),
          totalItems: totalProducts,
          itemsPerPage: parseInt(limit)
        }
      }
    });
  } catch (error) {
    logger.error('Admin products fetch error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch products'
    });
  }
});

/**
 * Get single product for admin editing
 */
router.get('/products/:id', protect, authorize('admin'), async (req, res) => {
  try {
    const product = await Product.findById(req.params.id)
      .populate('inventory')
      .populate('category_info', 'name description');

    if (!product) {
      return res.status(404).json({
        success: false,
        error: 'Product not found'
      });
    }

    res.status(200).json({
      success: true,
      data: product
    });
  } catch (error) {
    logger.error('Admin product fetch error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch product'
    });
  }
});

/**
 * Create new product
 */
router.post('/products', protect, authorize('admin'), async (req, res) => {
  try {
    const productData = {
      ...req.body,
      created_by: req.user._id,
      updated_by: req.user._id
    };

    // Generate slug from name
    if (!productData.slug) {
      productData.slug = productData.name
        .toLowerCase()
        .replace(/[^a-z0-9]+/g, '-')
        .replace(/(^-|-$)/g, '');
    }

    const product = new Product(productData);
    await product.save();

    // Log the action
    await auditLogService.log({
      user: req.user._id,
      action: 'CREATE_PRODUCT',
      resource: 'Product',
      resourceId: product._id,
      details: {
        productName: product.name,
        brand: product.brand,
        category: product.category
      }
    });

    res.status(201).json({
      success: true,
      data: product,
      message: 'Product created successfully'
    });
  } catch (error) {
    logger.error('Admin product creation error:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to create product'
    });
  }
});

/**
 * Update product
 */
router.put('/products/:id', protect, authorize('admin'), async (req, res) => {
  try {
    const product = await Product.findById(req.params.id);

    if (!product) {
      return res.status(404).json({
        success: false,
        error: 'Product not found'
      });
    }

    // Update product data
    const updateData = {
      ...req.body,
      updated_by: req.user._id,
      updated_at: new Date()
    };

    const updatedProduct = await Product.findByIdAndUpdate(
      req.params.id,
      updateData,
      { new: true, runValidators: true }
    );

    // Log the action
    await auditLogService.log({
      user: req.user._id,
      action: 'UPDATE_PRODUCT',
      resource: 'Product',
      resourceId: product._id,
      details: {
        productName: updatedProduct.name,
        changes: Object.keys(req.body)
      }
    });

    res.status(200).json({
      success: true,
      data: updatedProduct,
      message: 'Product updated successfully'
    });
  } catch (error) {
    logger.error('Admin product update error:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to update product'
    });
  }
});

/**
 * Delete product
 */
router.delete('/products/:id', protect, authorize('admin'), async (req, res) => {
  try {
    const product = await Product.findById(req.params.id);

    if (!product) {
      return res.status(404).json({
        success: false,
        error: 'Product not found'
      });
    }

    // Soft delete by setting status to inactive
    await Product.findByIdAndUpdate(req.params.id, {
      status: 'inactive',
      updated_by: req.user._id,
      updated_at: new Date()
    });

    // Log the action
    await auditLogService.log({
      user: req.user._id,
      action: 'DELETE_PRODUCT',
      resource: 'Product',
      resourceId: product._id,
      details: {
        productName: product.name,
        brand: product.brand
      }
    });

    res.status(200).json({
      success: true,
      message: 'Product deleted successfully'
    });
  } catch (error) {
    logger.error('Admin product deletion error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to delete product'
    });
  }
});

/**
 * Bulk operations for products
 */
router.post('/products/bulk', protect, authorize('admin'), async (req, res) => {
  try {
    const { action, productIds, updateData } = req.body;

    if (!action || !productIds || !Array.isArray(productIds)) {
      return res.status(400).json({
        success: false,
        error: 'Action and product IDs are required'
      });
    }

    let result;
    let message;

    switch (action) {
      case 'delete':
        result = await Product.updateMany(
          { _id: { $in: productIds } },
          {
            status: 'inactive',
            updated_by: req.user._id,
            updated_at: new Date()
          }
        );
        message = `${result.modifiedCount} products deleted successfully`;
        break;

      case 'activate':
        result = await Product.updateMany(
          { _id: { $in: productIds } },
          {
            status: 'active',
            updated_by: req.user._id,
            updated_at: new Date()
          }
        );
        message = `${result.modifiedCount} products activated successfully`;
        break;

      case 'deactivate':
        result = await Product.updateMany(
          { _id: { $in: productIds } },
          {
            status: 'inactive',
            updated_by: req.user._id,
            updated_at: new Date()
          }
        );
        message = `${result.modifiedCount} products deactivated successfully`;
        break;

      case 'update':
        if (!updateData) {
          return res.status(400).json({
            success: false,
            error: 'Update data is required for bulk update'
          });
        }
        result = await Product.updateMany(
          { _id: { $in: productIds } },
          {
            ...updateData,
            updated_by: req.user._id,
            updated_at: new Date()
          }
        );
        message = `${result.modifiedCount} products updated successfully`;
        break;

      default:
        return res.status(400).json({
          success: false,
          error: 'Invalid bulk action'
        });
    }

    // Log the bulk action
    await auditLogService.log({
      user: req.user._id,
      action: `BULK_${action.toUpperCase()}_PRODUCTS`,
      resource: 'Product',
      details: {
        action,
        productCount: productIds.length,
        modifiedCount: result.modifiedCount
      }
    });

    res.status(200).json({
      success: true,
      data: result,
      message
    });
  } catch (error) {
    logger.error('Admin bulk products operation error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to perform bulk operation'
    });
  }
});

/**
 * Get product categories and brands for filters
 */
router.get('/products/filters', protect, authorize('admin'), async (req, res) => {
  try {
    const [categories, brands] = await Promise.all([
      Product.distinct('category'),
      Product.distinct('brand')
    ]);

    res.status(200).json({
      success: true,
      data: {
        categories: categories.sort(),
        brands: brands.sort()
      }
    });
  } catch (error) {
    logger.error('Admin product filters error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch product filters'
    });
  }
});

/**
 * Get product statistics for admin dashboard
 */
router.get('/products/stats', protect, authorize('admin'), async (req, res) => {
  try {
    const [
      totalProducts,
      activeProducts,
      lowStockProducts,
      totalValue
    ] = await Promise.all([
      Product.countDocuments(),
      Product.countDocuments({ status: 'active' }),
      Product.countDocuments({ 'inventory.quantity': { $lte: 5 } }),
      Product.aggregate([
        { $match: { status: 'active' } },
        { $group: { _id: null, total: { $sum: '$price' } } }
      ])
    ]);

    res.status(200).json({
      success: true,
      data: {
        totalProducts,
        activeProducts,
        inactiveProducts: totalProducts - activeProducts,
        lowStockProducts,
        totalValue: totalValue[0]?.total || 0,
        averagePrice: totalProducts > 0 ? (totalValue[0]?.total || 0) / totalProducts : 0
      }
    });
  } catch (error) {
    logger.error('Product stats error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch product statistics'
    });
  }
});

/**
 * Get low stock products
 */
router.get('/products/low-stock', protect, authorize('admin'), async (req, res) => {
  try {
    const { threshold = 5 } = req.query;

    const lowStockProducts = await Product.find({
      'inventory.quantity': { $lte: parseInt(threshold) },
      status: 'active'
    })
    .populate('inventory')
    .select('name brand price inventory')
    .sort({ 'inventory.quantity': 1 });

    res.status(200).json({
      success: true,
      data: lowStockProducts
    });
  } catch (error) {
    logger.error('Low stock products error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch low stock products'
    });
  }
});

/**
 * Update product inventory
 */
router.put('/products/:id/inventory', protect, authorize('admin'), async (req, res) => {
  try {
    const { quantity, low_stock_threshold, location } = req.body;

    const product = await Product.findById(req.params.id);
    if (!product) {
      return res.status(404).json({
        success: false,
        error: 'Product not found'
      });
    }

    // Update inventory
    const updatedProduct = await Product.findByIdAndUpdate(
      req.params.id,
      {
        $set: {
          'inventory.quantity': quantity,
          'inventory.low_stock_threshold': low_stock_threshold,
          'inventory.location': location,
          'inventory.last_updated': new Date(),
          updated_by: req.user._id,
          updated_at: new Date()
        }
      },
      { new: true }
    ).populate('inventory');

    // Log the inventory update
    await auditLogService.log({
      user: req.user._id,
      action: 'UPDATE_INVENTORY',
      resource: 'Product',
      resourceId: product._id,
      details: {
        productName: product.name,
        oldQuantity: product.inventory?.quantity || 0,
        newQuantity: quantity
      }
    });

    res.status(200).json({
      success: true,
      data: updatedProduct,
      message: 'Inventory updated successfully'
    });
  } catch (error) {
    logger.error('Inventory update error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update inventory'
    });
  }
});

// Order Management Routes for Phone Point Dar Admin

/**
 * Get all orders for admin management
 */
router.get('/orders', protect, authorize('admin'), async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      search = '',
      status = '',
      payment_status = '',
      date_from = '',
      date_to = '',
      sort = '-created_at'
    } = req.query;

    // Build search query
    let searchQuery = {};

    if (search) {
      searchQuery.$or = [
        { order_number: { $regex: search, $options: 'i' } },
        { customer_email: { $regex: search, $options: 'i' } },
        { customer_name: { $regex: search, $options: 'i' } },
        { customer_phone: { $regex: search, $options: 'i' } }
      ];
    }

    if (status) {
      searchQuery.order_status = status;
    }

    if (payment_status) {
      searchQuery.payment_status = payment_status;
    }

    // Date range filter
    if (date_from || date_to) {
      searchQuery.created_at = {};
      if (date_from) {
        searchQuery.created_at.$gte = new Date(date_from);
      }
      if (date_to) {
        searchQuery.created_at.$lte = new Date(date_to);
      }
    }

    // Build sort object
    let sortObj = {};
    switch (sort) {
      case 'order_number':
        sortObj = { order_number: 1 };
        break;
      case '-order_number':
        sortObj = { order_number: -1 };
        break;
      case 'total_amount':
        sortObj = { total_amount: 1 };
        break;
      case '-total_amount':
        sortObj = { total_amount: -1 };
        break;
      case 'created_at':
        sortObj = { created_at: 1 };
        break;
      case '-created_at':
        sortObj = { created_at: -1 };
        break;
      default:
        sortObj = { created_at: -1 };
    }

    const skip = (page - 1) * limit;

    // Get orders with populated product information
    const orders = await Order.find(searchQuery)
      .populate('user', 'firstName lastName email')
      .populate('items.product', 'name brand model images')
      .sort(sortObj)
      .limit(parseInt(limit))
      .skip(skip)
      .select('order_number customer_name customer_email customer_phone total_amount order_status payment_status payment_method created_at confirmed_at shipped_at delivered_at items');

    // Get total count for pagination
    const totalOrders = await Order.countDocuments(searchQuery);

    res.status(200).json({
      success: true,
      data: {
        orders,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(totalOrders / limit),
          totalItems: totalOrders,
          itemsPerPage: parseInt(limit)
        }
      }
    });
  } catch (error) {
    logger.error('Admin orders fetch error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch orders'
    });
  }
});

/**
 * Get single order details for admin
 */
router.get('/orders/:id', protect, authorize('admin'), async (req, res) => {
  try {
    const order = await Order.findById(req.params.id)
      .populate('user', 'firstName lastName email phone')
      .populate('items.product', 'name brand model images specifications')
      .populate('product', 'name brand model images specifications'); // Legacy support

    if (!order) {
      return res.status(404).json({
        success: false,
        error: 'Order not found'
      });
    }

    res.status(200).json({
      success: true,
      data: order
    });
  } catch (error) {
    logger.error('Admin order fetch error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch order'
    });
  }
});

/**
 * Update order status
 */
router.put('/orders/:id/status', protect, authorize('admin'), async (req, res) => {
  try {
    const { order_status, fulfillment_status, internal_notes } = req.body;

    const order = await Order.findById(req.params.id);
    if (!order) {
      return res.status(404).json({
        success: false,
        error: 'Order not found'
      });
    }

    // Update order status and related timestamps
    const updateData = {
      updated_at: new Date()
    };

    if (order_status) {
      updateData.order_status = order_status;

      // Set appropriate timestamps based on status
      switch (order_status) {
        case 'confirmed':
          updateData.confirmed_at = new Date();
          break;
        case 'shipped':
          updateData.shipped_at = new Date();
          updateData.fulfillment_status = 'fulfilled';
          break;
        case 'delivered':
          updateData.delivered_at = new Date();
          updateData.fulfillment_status = 'fulfilled';
          break;
        case 'cancelled':
          updateData.cancelled_at = new Date();
          break;
      }
    }

    if (fulfillment_status) {
      updateData.fulfillment_status = fulfillment_status;
    }

    if (internal_notes) {
      updateData.internal_notes = internal_notes;
    }

    const updatedOrder = await Order.findByIdAndUpdate(
      req.params.id,
      updateData,
      { new: true, runValidators: true }
    ).populate('items.product', 'name brand model');

    // Log the status change
    await auditLogService.log({
      user: req.user._id,
      action: 'UPDATE_ORDER_STATUS',
      resource: 'Order',
      resourceId: order._id,
      details: {
        orderNumber: order.order_number,
        oldStatus: order.order_status,
        newStatus: order_status || order.order_status,
        oldFulfillmentStatus: order.fulfillment_status,
        newFulfillmentStatus: fulfillment_status || order.fulfillment_status
      }
    });

    res.status(200).json({
      success: true,
      data: updatedOrder,
      message: 'Order status updated successfully'
    });
  } catch (error) {
    logger.error('Order status update error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update order status'
    });
  }
});

/**
 * Bulk update order status
 */
router.post('/orders/bulk-status', protect, authorize('admin'), async (req, res) => {
  try {
    const { orderIds, order_status, fulfillment_status } = req.body;

    if (!orderIds || !Array.isArray(orderIds) || orderIds.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'Order IDs are required'
      });
    }

    const updateData = {
      updated_at: new Date()
    };

    if (order_status) {
      updateData.order_status = order_status;

      // Set appropriate timestamps based on status
      switch (order_status) {
        case 'confirmed':
          updateData.confirmed_at = new Date();
          break;
        case 'shipped':
          updateData.shipped_at = new Date();
          updateData.fulfillment_status = 'fulfilled';
          break;
        case 'delivered':
          updateData.delivered_at = new Date();
          updateData.fulfillment_status = 'fulfilled';
          break;
        case 'cancelled':
          updateData.cancelled_at = new Date();
          break;
      }
    }

    if (fulfillment_status) {
      updateData.fulfillment_status = fulfillment_status;
    }

    const result = await Order.updateMany(
      { _id: { $in: orderIds } },
      updateData
    );

    // Log the bulk action
    await auditLogService.log({
      user: req.user._id,
      action: 'BULK_UPDATE_ORDER_STATUS',
      resource: 'Order',
      details: {
        orderCount: orderIds.length,
        newStatus: order_status,
        newFulfillmentStatus: fulfillment_status,
        modifiedCount: result.modifiedCount
      }
    });

    res.status(200).json({
      success: true,
      data: result,
      message: `${result.modifiedCount} orders updated successfully`
    });
  } catch (error) {
    logger.error('Bulk order status update error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update order statuses'
    });
  }
});

/**
 * Get order statistics for admin dashboard
 */
router.get('/orders/stats', protect, authorize('admin'), async (req, res) => {
  try {
    const { period = '30d' } = req.query;

    // Calculate date range based on period
    let startDate = new Date();
    switch (period) {
      case '7d':
        startDate.setDate(startDate.getDate() - 7);
        break;
      case '30d':
        startDate.setDate(startDate.getDate() - 30);
        break;
      case '90d':
        startDate.setDate(startDate.getDate() - 90);
        break;
      case '1y':
        startDate.setFullYear(startDate.getFullYear() - 1);
        break;
      default:
        startDate.setDate(startDate.getDate() - 30);
    }

    const [
      totalOrders,
      pendingOrders,
      completedOrders,
      cancelledOrders,
      totalRevenue,
      averageOrderValue,
      recentOrders
    ] = await Promise.all([
      // Total orders in period
      Order.countDocuments({
        created_at: { $gte: startDate }
      }),

      // Pending orders
      Order.countDocuments({
        order_status: { $in: ['pending', 'confirmed', 'processing'] },
        created_at: { $gte: startDate }
      }),

      // Completed orders
      Order.countDocuments({
        order_status: 'delivered',
        created_at: { $gte: startDate }
      }),

      // Cancelled orders
      Order.countDocuments({
        order_status: 'cancelled',
        created_at: { $gte: startDate }
      }),

      // Total revenue from completed orders
      Order.aggregate([
        {
          $match: {
            payment_status: 'completed',
            created_at: { $gte: startDate }
          }
        },
        {
          $group: {
            _id: null,
            total: { $sum: '$total_amount' }
          }
        }
      ]),

      // Average order value
      Order.aggregate([
        {
          $match: {
            payment_status: 'completed',
            created_at: { $gte: startDate }
          }
        },
        {
          $group: {
            _id: null,
            average: { $avg: '$total_amount' }
          }
        }
      ]),

      // Recent orders (last 10)
      Order.find({
        created_at: { $gte: startDate }
      })
      .populate('items.product', 'name brand')
      .sort({ created_at: -1 })
      .limit(10)
      .select('order_number customer_name total_amount order_status payment_status created_at')
    ]);

    res.status(200).json({
      success: true,
      data: {
        period,
        totalOrders,
        pendingOrders,
        completedOrders,
        cancelledOrders,
        totalRevenue: totalRevenue[0]?.total || 0,
        averageOrderValue: averageOrderValue[0]?.average || 0,
        recentOrders
      }
    });
  } catch (error) {
    logger.error('Order stats error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch order statistics'
    });
  }
});

/**
 * Get order filters (statuses, payment methods)
 */
router.get('/orders/filters', protect, authorize('admin'), async (req, res) => {
  try {
    const [orderStatuses, paymentMethods] = await Promise.all([
      Order.distinct('order_status'),
      Order.distinct('payment_method')
    ]);

    res.status(200).json({
      success: true,
      data: {
        orderStatuses: orderStatuses.sort(),
        paymentMethods: paymentMethods.sort(),
        paymentStatuses: ['pending', 'completed', 'failed', 'refunded', 'partially_refunded']
      }
    });
  } catch (error) {
    logger.error('Order filters error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch order filters'
    });
  }
});

// Customer Management Routes for Phone Point Dar Admin

/**
 * Get customers for admin operations
 */
router.get('/customers', protect, authorize('admin'), async (req, res) => {
  try {
    const { limit = 50, page = 1, search = '' } = req.query;
    const skip = (page - 1) * limit;

    // Build search query
    let searchQuery = { role: { $ne: 'admin' } }; // Exclude admin users

    if (search) {
      searchQuery.$or = [
        { email: { $regex: search, $options: 'i' } },
        { firstName: { $regex: search, $options: 'i' } },
        { lastName: { $regex: search, $options: 'i' } }
      ];
    }

    // Get customers with pagination
    const customers = await User.find(searchQuery)
      .select('email firstName lastName createdAt lastLogin isActive')
      .sort({ createdAt: -1 })
      .limit(parseInt(limit))
      .skip(skip);

    // Get total count for pagination
    const totalCustomers = await User.countDocuments(searchQuery);

    res.status(200).json({
      success: true,
      data: {
        customers,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(totalCustomers / limit),
          totalItems: totalCustomers,
          itemsPerPage: parseInt(limit)
        }
      }
    });
  } catch (error) {
    logger.error('Customers fetch error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch customers'
    });
  }
});

// Email Management Routes for Phone Point Dar Admin

/**
 * Get email statistics for admin dashboard
 */
router.get('/emails/stats', protect, authorize('admin'), async (req, res) => {
  try {
    // Mock email statistics for demonstration
    const stats = {
      totalSent: 15420,
      totalDelivered: 14890,
      totalFailed: 530,
      deliveryRate: 96.6,
      recentEmails: [
        {
          subject: 'Welcome to Phone Point Dar',
          recipient: '<EMAIL>',
          status: 'delivered',
          sentAt: new Date().toISOString()
        }
      ],
      templates: [
        {
          _id: 'welcome',
          name: 'Welcome Email',
          description: 'Welcome new customers',
          isActive: true
        }
      ],
      sequences: []
    };

    res.status(200).json({
      success: true,
      data: stats
    });
  } catch (error) {
    logger.error('Email stats error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch email statistics'
    });
  }
});

/**
 * Send email from admin panel
 */
router.post('/emails/send', protect, authorize('admin'), async (req, res) => {
  try {
    const { recipients, subject, message, template, priority, sendNow, scheduledDate, scheduledTime } = req.body;

    // Validate required fields
    if (!subject || !message) {
      return res.status(400).json({
        success: false,
        error: 'Subject and message are required'
      });
    }

    // For now, we'll simulate sending the email
    const emailResult = {
      success: true,
      messageId: `admin_email_${Date.now()}`,
      recipients: Array.isArray(recipients) ? recipients : [recipients],
      sentAt: new Date().toISOString(),
      scheduled: !sendNow
    };

    logger.info('Admin email sent:', {
      subject,
      recipients: emailResult.recipients,
      sentBy: req.user.email,
      scheduled: !sendNow
    });

    res.status(200).json({
      success: true,
      data: emailResult
    });
  } catch (error) {
    logger.error('Admin email send error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to send email'
    });
  }
});

/**
 * Get email templates for admin
 */
router.get('/emails/templates', protect, authorize('admin'), async (req, res) => {
  try {
    // Return built-in templates
    const templates = [
      {
        _id: 'email-verification',
        name: 'Email Verification',
        description: 'Template for email address verification',
        subject: 'Verify Your Email - Phone Point Dar',
        type: 'system',
        isActive: true
      },
      {
        _id: 'order-confirmation',
        name: 'Order Confirmation',
        description: 'Template for order confirmation emails',
        subject: 'Order Confirmed - Phone Point Dar',
        type: 'transactional',
        isActive: true
      },
      {
        _id: 'welcome',
        name: 'Welcome Email',
        description: 'Template for welcoming new customers',
        subject: 'Welcome to Phone Point Dar',
        type: 'marketing',
        isActive: true
      }
    ];

    res.status(200).json({
      success: true,
      data: templates
    });
  } catch (error) {
    logger.error('Email templates error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch email templates'
    });
  }
});

module.exports = router;