const express = require('express');
const router = express.Router();
const { protect, authorize } = require('../middleware/auth');
const MonitoringService = require('../services/monitoringService');
const paymentService = require('../services/paymentService');
const webhookBatchProcessor = require('../services/webhookBatchProcessor');
const emailSender = require('../services/emailSender');
// Phone Point Dar - Admin routes for phone retail management
const logger = require('../utils/logger');

// Import models
const User = require('../models/User');
const Order = require('../models/Order');
const Product = require('../models/Product');

/**
 * @swagger
 * /api/admin/metrics/payments:
 *   get:
 *     summary: Get payment system metrics
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Payment metrics retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 totalPayments:
 *                   type: number
 *                 successfulPayments:
 *                   type: number
 *                 failedPayments:
 *                   type: number
 *                 cancelledPayments:
 *                   type: number
 *                 webhookSuccess:
 *                   type: number
 *                 webhookFailures:
 *                   type: number
 *                 errors:
 *                   type: number
 *                 successRate:
 *                   type: string
 *                 webhookSuccessRate:
 *                   type: string
 *       401:
 *         description: Not authorized
 *       403:
 *         description: Forbidden - Admin access required
 */
router.get('/metrics/payments', protect, authorize('admin'), (req, res) => {
  res.status(200).json({
    success: true,
    data: MonitoringService.getMetrics()
  });
});

/**
 * @swagger
 * /api/admin/metrics/payments/reset:
 *   post:
 *     summary: Reset payment metrics
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Metrics reset successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *       401:
 *         description: Not authorized
 *       403:
 *         description: Forbidden - Admin access required
 */
router.post('/metrics/payments/reset', protect, authorize('admin'), (req, res) => {
  MonitoringService.resetMetrics();
  res.status(200).json({
    success: true,
    message: 'Payment metrics reset successfully'
  });
});

/**
 * @swagger
 * /api/admin/monitoring/circuit-breaker:
 *   get:
 *     summary: Get circuit breaker state
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Circuit breaker state retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     isOpen:
 *                       type: boolean
 *                     failures:
 *                       type: number
 *                     lastFailureTime:
 *                       type: string
 *                       format: date-time
 *                     failureThreshold:
 *                       type: number
 *                     resetTimeout:
 *                       type: number
 *       401:
 *         description: Not authorized
 *       403:
 *         description: Forbidden - Admin access required
 */
router.get('/monitoring/circuit-breaker', protect, authorize('admin'), (req, res) => {
  res.status(200).json({
    success: true,
    data: {
      isOpen: false,
      failures: 0,
      lastFailureTime: null,
      failureThreshold: 5,
      resetTimeout: 60000,
      message: 'Circuit breaker monitoring available for payment services'
    }
  });
});

/**
 * @swagger
 * /api/admin/monitoring/webhooks:
 *   get:
 *     summary: Get webhook processing statistics
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Webhook statistics retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     waiting:
 *                       type: number
 *                     active:
 *                       type: number
 *                     completed:
 *                       type: number
 *                     failed:
 *                       type: number
 *                     batchSize:
 *                       type: number
 *                     batchTimeout:
 *                       type: number
 *       401:
 *         description: Not authorized
 *       403:
 *         description: Forbidden - Admin access required
 */
router.get('/monitoring/webhooks', protect, authorize('admin'), async (req, res) => {
  try {
    const stats = await webhookBatchProcessor.getQueueStats();
    res.status(200).json({
      success: true,
      data: stats
    });
  } catch (error) {
    logger.error('Webhook stats error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Phone Point Dar - Admin Dashboard Statistics for Phone Retail Business

/**
 * @swagger
 * /api/admin/dashboard/stats:
 *   get:
 *     summary: Get Phone Point Dar dashboard statistics
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Dashboard statistics retrieved successfully
 *       401:
 *         description: Not authorized
 *       403:
 *         description: Forbidden - Admin access required
 */
router.get('/dashboard/stats', protect, authorize('admin'), async (req, res) => {
  try {
    const User = require('../models/User');
    const Order = require('../models/Order');
    const Product = require('../models/Product');
    const Inventory = require('../models/Inventory');

    // Get current date for monthly calculations
    const now = new Date();
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const startOfDay = new Date(now.getFullYear(), now.getMonth(), now.getDate());

    // Parallel queries for better performance
    const [
      totalCustomers,
      totalOrders,
      totalProducts,
      monthlyOrders,
      pendingOrders,
      lowStockProducts,
      recentOrders,
      topProducts
    ] = await Promise.all([
      // Total customers (non-admin users)
      User.countDocuments({ role: { $ne: 'admin' } }),

      // Total orders
      Order.countDocuments(),

      // Total products (active phones)
      Product.countDocuments({ status: 'active' }),

      // Monthly orders for revenue calculation
      Order.find({
        created_at: { $gte: startOfMonth },
        payment_status: 'completed'
      }).populate('product'),

      // Pending orders (need processing)
      Order.countDocuments({
        order_status: { $in: ['pending', 'processing'] }
      }),

      // Low stock products (less than 10 units)
      Inventory.countDocuments({ quantity: { $lt: 10 } }),

      // Recent orders (last 10)
      Order.find()
        .populate('product', 'name brand')
        .populate('user', 'name email')
        .sort({ created_at: -1 })
        .limit(10),

      // Top selling products this month
      Order.aggregate([
        {
          $match: {
            created_at: { $gte: startOfMonth },
            payment_status: 'completed'
          }
        },
        {
          $group: {
            _id: '$product',
            totalSold: { $sum: '$quantity' },
            totalRevenue: { $sum: '$total_amount' }
          }
        },
        {
          $lookup: {
            from: 'products',
            localField: '_id',
            foreignField: '_id',
            as: 'productInfo'
          }
        },
        {
          $unwind: '$productInfo'
        },
        {
          $sort: { totalSold: -1 }
        },
        {
          $limit: 5
        }
      ])
    ]);

    // Calculate monthly revenue in Tanzanian Shillings
    const monthlyRevenue = monthlyOrders.reduce((total, order) => {
      return total + (order.total_amount || 0);
    }, 0);

    // Calculate average order value
    const averageOrderValue = monthlyOrders.length > 0
      ? monthlyRevenue / monthlyOrders.length
      : 0;

    // Format response
    const stats = {
      totalCustomers,
      totalOrders,
      totalProducts,
      monthlyRevenue,
      averageOrderValue,
      pendingOrders,
      lowStockItems: lowStockProducts,
      recentOrders: recentOrders.map(order => ({
        _id: order._id,
        customer: order.user?.name || 'Unknown',
        product: {
          name: order.product?.name || 'Unknown Product',
          brand: order.product?.brand || 'Unknown Brand'
        },
        amount: order.total_amount || 0,
        status: order.order_status || 'pending',
        created_at: order.created_at
      })),
      topProducts: topProducts.map(item => ({
        name: item.productInfo.name,
        brand: item.productInfo.brand,
        totalSold: item.totalSold,
        revenue: item.totalRevenue
      }))
    };

    res.status(200).json({
      success: true,
      data: stats
    });

  } catch (error) {
    logger.error('Dashboard stats error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch dashboard statistics',
      details: error.message
    });
  }
});

/**
 * @swagger
 * /api/admin/templates:
 *   get:
 *     summary: Get available HTML templates
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Templates retrieved successfully
 *       401:
 *         description: Not authorized
 *       403:
 *         description: Forbidden - Admin access required
 */
router.get('/templates', protect, authorize('admin'), async (req, res) => {
  try {
    const fs = require('fs');
    const path = require('path');
    const contentDir = path.join(__dirname, '..', 'content');

    // Get all template directories
    const templates = [];
    if (fs.existsSync(contentDir)) {
      const dirs = fs.readdirSync(contentDir, { withFileTypes: true })
        .filter(dirent => dirent.isDirectory())
        .map(dirent => dirent.name);

      for (const dir of dirs) {
        const htmlPath = path.join(contentDir, dir, 'index.html');
        if (fs.existsSync(htmlPath)) {
          templates.push({
            id: dir,
            name: dir.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
            path: htmlPath,
            content: fs.readFileSync(htmlPath, 'utf8')
          });
        }
      }
    }

    res.status(200).json({
      success: true,
      templates
    });

  } catch (error) {
    logger.error('Template fetch error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch templates',
      details: error.message
    });
  }
});

/**
 * @swagger
 * /api/admin/templates/{templateId}:
 *   get:
 *     summary: Get specific HTML template content
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: templateId
 *         required: true
 *         schema:
 *           type: string
 *         description: Template ID
 *     responses:
 *       200:
 *         description: Template content retrieved successfully
 *       404:
 *         description: Template not found
 *       401:
 *         description: Not authorized
 *       403:
 *         description: Forbidden - Admin access required
 */
router.get('/templates/:templateId', protect, authorize('admin'), async (req, res) => {
  try {
    const fs = require('fs');
    const path = require('path');
    const { templateId } = req.params;

    const htmlPath = path.join(__dirname, '..', 'content', templateId, 'index.html');

    if (!fs.existsSync(htmlPath)) {
      return res.status(404).json({
        success: false,
        error: 'Template not found'
      });
    }

    const content = fs.readFileSync(htmlPath, 'utf8');

    res.status(200).json({
      success: true,
      template: {
        id: templateId,
        name: templateId.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
        content
      }
    });

  } catch (error) {
    logger.error('Template fetch error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch template',
      details: error.message
    });
  }
});

// Customer Management Routes for Phone Point Dar Admin

/**
 * Get customers for admin operations
 */
router.get('/customers', protect, authorize('admin'), async (req, res) => {
  try {
    const { limit = 50, page = 1, search = '' } = req.query;
    const skip = (page - 1) * limit;

    // Build search query
    let searchQuery = { role: { $ne: 'admin' } }; // Exclude admin users

    if (search) {
      searchQuery.$or = [
        { email: { $regex: search, $options: 'i' } },
        { firstName: { $regex: search, $options: 'i' } },
        { lastName: { $regex: search, $options: 'i' } }
      ];
    }

    // Get customers with pagination
    const customers = await User.find(searchQuery)
      .select('email firstName lastName createdAt lastLogin isActive')
      .sort({ createdAt: -1 })
      .limit(parseInt(limit))
      .skip(skip);

    // Get total count for pagination
    const totalCustomers = await User.countDocuments(searchQuery);

    res.status(200).json({
      success: true,
      data: {
        customers,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(totalCustomers / limit),
          totalItems: totalCustomers,
          itemsPerPage: parseInt(limit)
        }
      }
    });
  } catch (error) {
    logger.error('Customers fetch error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch customers'
    });
  }
});

// Email Management Routes for Phone Point Dar Admin

/**
 * Get email statistics for admin dashboard
 */
router.get('/emails/stats', protect, authorize('admin'), async (req, res) => {
  try {
    // Mock email statistics for demonstration
    const stats = {
      totalSent: 15420,
      totalDelivered: 14890,
      totalFailed: 530,
      deliveryRate: 96.6,
      recentEmails: [
        {
          subject: 'Welcome to Phone Point Dar',
          recipient: '<EMAIL>',
          status: 'delivered',
          sentAt: new Date().toISOString()
        }
      ],
      templates: [
        {
          _id: 'welcome',
          name: 'Welcome Email',
          description: 'Welcome new customers',
          isActive: true
        }
      ],
      sequences: []
    };

    res.status(200).json({
      success: true,
      data: stats
    });
  } catch (error) {
    logger.error('Email stats error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch email statistics'
    });
  }
});

/**
 * Send email from admin panel
 */
router.post('/emails/send', protect, authorize('admin'), async (req, res) => {
  try {
    const { recipients, subject, message, template, priority, sendNow, scheduledDate, scheduledTime } = req.body;

    // Validate required fields
    if (!subject || !message) {
      return res.status(400).json({
        success: false,
        error: 'Subject and message are required'
      });
    }

    // For now, we'll simulate sending the email
    const emailResult = {
      success: true,
      messageId: `admin_email_${Date.now()}`,
      recipients: Array.isArray(recipients) ? recipients : [recipients],
      sentAt: new Date().toISOString(),
      scheduled: !sendNow
    };

    logger.info('Admin email sent:', {
      subject,
      recipients: emailResult.recipients,
      sentBy: req.user.email,
      scheduled: !sendNow
    });

    res.status(200).json({
      success: true,
      data: emailResult
    });
  } catch (error) {
    logger.error('Admin email send error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to send email'
    });
  }
});

/**
 * Get email templates for admin
 */
router.get('/emails/templates', protect, authorize('admin'), async (req, res) => {
  try {
    // Return built-in templates
    const templates = [
      {
        _id: 'email-verification',
        name: 'Email Verification',
        description: 'Template for email address verification',
        subject: 'Verify Your Email - Phone Point Dar',
        type: 'system',
        isActive: true
      },
      {
        _id: 'order-confirmation',
        name: 'Order Confirmation',
        description: 'Template for order confirmation emails',
        subject: 'Order Confirmed - Phone Point Dar',
        type: 'transactional',
        isActive: true
      },
      {
        _id: 'welcome',
        name: 'Welcome Email',
        description: 'Template for welcoming new customers',
        subject: 'Welcome to Phone Point Dar',
        type: 'marketing',
        isActive: true
      }
    ];

    res.status(200).json({
      success: true,
      data: templates
    });
  } catch (error) {
    logger.error('Email templates error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch email templates'
    });
  }
});

module.exports = router;