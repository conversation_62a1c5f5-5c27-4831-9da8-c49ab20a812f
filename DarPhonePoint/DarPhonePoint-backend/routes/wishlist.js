const express = require('express');
const router = express.Router();
const Wishlist = require('../models/Wishlist');
const Product = require('../models/Product');
const { protect } = require('../middleware/auth');

/**
 * GET /api/wishlist
 * Get user's wishlist
 */
router.get('/', protect, async (req, res) => {
  try {
    let wishlist = await Wishlist.findOne({ user: req.user._id })
      .populate({
        path: 'items.product',
        select: 'name brand category price primary_image images in_stock stock_quantity variants'
      })
      .lean();

    if (!wishlist) {
      wishlist = {
        user: req.user._id,
        items: [],
        created_at: new Date(),
        updated_at: new Date()
      };
    }

    res.json({
      success: true,
      data: wishlist
    });

  } catch (error) {
    console.error('Get wishlist error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch wishlist'
    });
  }
});

/**
 * POST /api/wishlist/debug
 * Debug endpoint to test request format
 */
router.post('/debug', async (req, res) => {
  console.log('🔍 DEBUG: Wishlist request received:');
  console.log('  - Body:', req.body);
  console.log('  - Headers:', req.headers);
  console.log('  - Content-Type:', req.headers['content-type']);
  console.log('  - Authorization:', req.headers.authorization ? 'Present' : 'Missing');

  res.json({
    success: true,
    message: 'Debug endpoint reached',
    receivedData: {
      body: req.body,
      contentType: req.headers['content-type'],
      hasAuth: !!req.headers.authorization
    }
  });
});

/**
 * POST /api/wishlist
 * Add product to wishlist
 */
router.post('/', protect, async (req, res) => {
  try {

    const { productId } = req.body;

    // Validate user
    if (!req.user || !req.user._id) {
      console.log('❌ Invalid user in request:', req.user);
      return res.status(401).json({
        success: false,
        error: 'User authentication failed'
      });
    }

    if (!productId) {
      console.log('❌ Missing productId. Body:', req.body);
      return res.status(400).json({
        success: false,
        error: 'Product ID is required'
      });
    }

    // Check if product exists
    const product = await Product.findById(productId);
    if (!product) {
      return res.status(404).json({
        success: false,
        error: 'Product not found'
      });
    }

    // Find or create wishlist
    let wishlist = await Wishlist.findOne({ user: req.user._id });
    
    if (!wishlist) {
      wishlist = new Wishlist({
        user: req.user._id,
        items: []
      });
    }

    // Check if product already in wishlist
    const existingItem = wishlist.items.find(
      item => item.product.toString() === productId
    );

    if (existingItem) {
      return res.status(400).json({
        success: false,
        error: 'Product is already in your wishlist'
      });
    }

    // Add product to wishlist
    wishlist.items.push({
      product: productId,
      added_at: new Date()
    });

    wishlist.updated_at = new Date();
    await wishlist.save();

    // Populate the new item for response
    await wishlist.populate({
      path: 'items.product',
      select: 'name brand category price primary_image images in_stock stock_quantity variants'
    });

    res.status(201).json({
      success: true,
      data: wishlist,
      message: 'Product added to wishlist successfully'
    });

  } catch (error) {
    console.error('❌ Add to wishlist error:', error);
    console.error('❌ Error details:', {
      message: error.message,
      stack: error.stack,
      user: req.user?.email,
      productId: req.body?.productId
    });

    // Send more specific error message
    res.status(500).json({
      success: false,
      error: 'Failed to add product to wishlist',
      details: error.message
    });
  }
});

/**
 * DELETE /api/wishlist/:productId
 * Remove product from wishlist
 */
router.delete('/:productId', protect, async (req, res) => {
  try {
    const { productId } = req.params;

    const wishlist = await Wishlist.findOne({ user: req.user._id });
    
    if (!wishlist) {
      return res.status(404).json({
        success: false,
        error: 'Wishlist not found'
      });
    }

    // Remove product from wishlist
    const initialLength = wishlist.items.length;
    wishlist.items = wishlist.items.filter(
      item => item.product.toString() !== productId
    );

    if (wishlist.items.length === initialLength) {
      return res.status(404).json({
        success: false,
        error: 'Product not found in wishlist'
      });
    }

    wishlist.updated_at = new Date();
    await wishlist.save();

    // Populate for response
    await wishlist.populate({
      path: 'items.product',
      select: 'name brand category price primary_image images in_stock stock_quantity variants'
    });

    res.json({
      success: true,
      data: wishlist,
      message: 'Product removed from wishlist successfully'
    });

  } catch (error) {
    console.error('Remove from wishlist error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to remove product from wishlist'
    });
  }
});

/**
 * DELETE /api/wishlist
 * Clear entire wishlist
 */
router.delete('/', protect, async (req, res) => {
  try {
    const wishlist = await Wishlist.findOne({ user: req.user._id });
    
    if (!wishlist) {
      return res.json({
        success: true,
        data: { items: [] },
        message: 'Wishlist is already empty'
      });
    }

    wishlist.items = [];
    wishlist.updated_at = new Date();
    await wishlist.save();

    res.json({
      success: true,
      data: wishlist,
      message: 'Wishlist cleared successfully'
    });

  } catch (error) {
    console.error('Clear wishlist error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to clear wishlist'
    });
  }
});

/**
 * POST /api/wishlist/move-to-cart
 * Move wishlist items to cart
 */
router.post('/move-to-cart', protect, async (req, res) => {
  try {
    const { productIds } = req.body; // Array of product IDs to move

    const wishlist = await Wishlist.findOne({ user: req.user._id })
      .populate('items.product');
    
    if (!wishlist) {
      return res.status(404).json({
        success: false,
        error: 'Wishlist not found'
      });
    }

    const Cart = require('../models/Cart');
    let cart = await Cart.findOne({ user: req.user._id });
    
    if (!cart) {
      cart = new Cart({
        user: req.user._id,
        items: []
      });
    }

    let movedCount = 0;
    const itemsToMove = productIds ? 
      wishlist.items.filter(item => productIds.includes(item.product._id.toString())) :
      wishlist.items;

    for (const wishlistItem of itemsToMove) {
      const product = wishlistItem.product;
      
      // Check if product is available
      if (!product.in_stock || !product.is_active) {
        continue;
      }

      // Check if already in cart
      const existingCartItem = cart.items.find(
        item => item.product.toString() === product._id.toString()
      );

      if (!existingCartItem) {
        cart.items.push({
          product: product._id,
          quantity: 1,
          price: product.price,
          warranty_option: 'standard',
          warranty_price: 0,
          warranty_duration: 12,
          device_condition: 'new'
        });
        movedCount++;
      }

      // Remove from wishlist
      wishlist.items = wishlist.items.filter(
        item => item.product._id.toString() !== product._id.toString()
      );
    }

    // Save both cart and wishlist
    await Promise.all([
      cart.save(),
      wishlist.save()
    ]);

    res.json({
      success: true,
      data: {
        movedCount,
        cart,
        wishlist
      },
      message: `${movedCount} item(s) moved to cart successfully`
    });

  } catch (error) {
    console.error('Move to cart error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to move items to cart'
    });
  }
});

module.exports = router;
