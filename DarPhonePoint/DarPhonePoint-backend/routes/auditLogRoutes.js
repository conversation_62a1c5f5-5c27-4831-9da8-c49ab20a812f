const express = require('express');
const router = express.Router();
const {
  getAuditLogs,
  getRecentLogs,
  getResourceLogs,
  getUserLogs,
  createAuditLog,
  getAuditLogStats
} = require('../controllers/auditLogController');
const { protect, authorize } = require('../middleware/auth');
const { adminLimiter } = require('../middleware/rateLimiter');

// Apply admin rate limiting to all routes
router.use(adminLimiter);

/**
 * @route   GET /api/audit-logs
 * @desc    Get all audit logs with pagination and filtering
 * @access  Private/Admin
 */
router.get('/', protect, authorize('admin'), getAuditLogs);

/**
 * @route   GET /api/audit-logs/recent
 * @desc    Get recent audit logs
 * @access  Private/Admin
 */
router.get('/recent', protect, authorize('admin'), getRecentLogs);

/**
 * @route   GET /api/audit-logs/resource/:type/:id
 * @desc    Get audit logs for a specific resource
 * @access  Private/Admin
 */
router.get('/resource/:type/:id', protect, authorize('admin'), getResourceLogs);

/**
 * @route   GET /api/audit-logs/user/:id
 * @desc    Get audit logs for a specific user
 * @access  Private/Admin
 */
router.get('/user/:id', protect, authorize('admin'), getUserLogs);

/**
 * @route   GET /api/audit-logs/stats
 * @desc    Get audit log statistics
 * @access  Private/Admin
 */
router.get('/stats', protect, authorize('admin'), getAuditLogStats);

/**
 * @route   POST /api/audit-logs
 * @desc    Create an audit log
 * @access  Private
 */
router.post('/', protect, createAuditLog);

module.exports = router;
