const express = require('express');
const router = express.Router();
const passport = require('passport');
const authController = require('../controllers/authController');
const { protect } = require('../middleware/auth');
const { authLimiter } = require('../middleware/rateLimiter');
const loginAttemptTracker = require('../middleware/loginAttemptTracker');
const { validationRules, handleValidationErrors } = require('../middleware/inputValidation');

// @route   POST /api/auth/register
// @desc    Register user
// @access  Public
router.post('/register',
  authLimiter,
  validationRules.userRegistration,
  handleValidationErrors,
  authController.register
);

// @route   POST /api/auth/login
// @desc    Login user
// @access  Public
router.post('/login',
  authLimiter,
  validationRules.userLogin,
  handleValidationErrors,
  loginAttemptTracker.checkLockMiddleware.bind(loginAttemptTracker),
  authController.login
);

// @route   GET /api/auth/google
// @desc    Google OAuth login
// @access  Public
router.get('/google',
  authLimiter,
  passport.authenticate('google', { scope: ['profile', 'email'] })
);

// @route   GET /api/auth/google/callback
// @desc    Google OAuth callback
// @access  Public
router.get('/google/callback',
  passport.authenticate('google', { failureRedirect: '/login' }),
  authController.googleCallback
);

// @route   GET /api/auth/me
// @desc    Get current user
// @access  Private
router.get('/me', protect, authController.getCurrentUser);

// @route   POST /api/auth/forgot-password
// @desc    Send password reset email
// @access  Public
router.post('/forgot-password', authLimiter, authController.forgotPassword);

// @route   PUT /api/auth/reset-password/:token
// @desc    Reset password with token
// @access  Public
router.put('/reset-password/:token', authLimiter, authController.resetPassword);

// @route   PUT /api/auth/profile
// @desc    Update user profile
// @access  Private
router.put('/profile', protect, authController.updateProfile);

// @route   PUT /api/auth/change-password
// @desc    Change user password
// @access  Private
router.put('/change-password', protect, authController.changePassword);

// @route   POST /api/auth/logout
// @desc    Logout user
// @access  Private
router.post('/logout', protect, authController.logout);

// @route   GET /api/auth/verify-email/:token
// @desc    Verify email with token
// @access  Public
router.get('/verify-email/:token', authController.verifyEmail);

// @route   POST /api/auth/resend-verification
// @desc    Resend verification email
// @access  Public
router.post('/resend-verification', authLimiter, authController.resendVerification);

// @route   POST /api/auth/logout
// @desc    Logout user and blacklist token
// @access  Private
router.post('/logout', protect, authController.logout);

// @route   POST /api/auth/logout-all
// @desc    Logout from all devices
// @access  Private
router.post('/logout-all', protect, authController.logoutAll);

// @route   GET /api/auth/sessions
// @desc    Get active sessions for current user
// @access  Private
router.get('/sessions', protect, authController.getActiveSessions);

// @route   POST /api/auth/sessions/end
// @desc    End specific session
// @access  Private
router.post('/sessions/end', protect, authController.endSession);

// Development mode manual verification removed for security

module.exports = router;