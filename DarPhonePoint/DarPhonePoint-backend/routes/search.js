const express = require('express');
const router = express.Router();
const Product = require('../models/Product');

/**
 * GET /api/search/suggestions
 * Get search suggestions for autocomplete
 */
router.get('/suggestions', async (req, res) => {
  try {
    const { q, limit = 8 } = req.query;
    
    if (!q || q.length < 2) {
      return res.json({
        success: true,
        data: []
      });
    }

    // Create search regex for partial matching
    const searchRegex = new RegExp(q, 'i');
    
    // Search in multiple fields
    const searchQuery = {
      $and: [
        { is_active: true },
        {
          $or: [
            { name: searchRegex },
            { brand: searchRegex },
            { category: searchRegex },
            { 'specifications.value': searchRegex },
            { description: searchRegex }
          ]
        }
      ]
    };

    const suggestions = await Product.find(searchQuery)
      .select('name brand category price primary_image images in_stock')
      .limit(parseInt(limit))
      .sort({ 
        // Prioritize exact matches in name
        name: 1,
        // Then by popularity (you could add a popularity field)
        created_at: -1
      })
      .lean();

    // Add relevance scoring
    const scoredSuggestions = suggestions.map(product => {
      let score = 0;
      
      // Higher score for name matches
      if (product.name.toLowerCase().includes(q.toLowerCase())) {
        score += 10;
        // Even higher for exact matches
        if (product.name.toLowerCase() === q.toLowerCase()) {
          score += 20;
        }
      }
      
      // Medium score for brand matches
      if (product.brand.toLowerCase().includes(q.toLowerCase())) {
        score += 5;
      }
      
      // Lower score for category matches
      if (product.category.toLowerCase().includes(q.toLowerCase())) {
        score += 2;
      }
      
      return { ...product, relevanceScore: score };
    });

    // Sort by relevance score
    scoredSuggestions.sort((a, b) => b.relevanceScore - a.relevanceScore);

    res.json({
      success: true,
      data: scoredSuggestions.map(({ relevanceScore, ...product }) => product)
    });

  } catch (error) {
    console.error('Search suggestions error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch search suggestions'
    });
  }
});

/**
 * GET /api/search/products
 * Advanced product search with filters
 */
router.get('/products', async (req, res) => {
  try {
    const {
      q,
      category,
      brand,
      minPrice,
      maxPrice,
      inStock,
      sortBy = 'relevance',
      page = 1,
      limit = 20
    } = req.query;

    let searchQuery = { is_active: true };

    // Text search
    if (q && q.length >= 2) {
      const searchRegex = new RegExp(q, 'i');
      searchQuery.$or = [
        { name: searchRegex },
        { brand: searchRegex },
        { category: searchRegex },
        { description: searchRegex },
        { 'specifications.value': searchRegex }
      ];
    }

    // Category filter
    if (category) {
      searchQuery.category = category;
    }

    // Brand filter
    if (brand) {
      searchQuery.brand = brand;
    }

    // Price range filter
    if (minPrice || maxPrice) {
      searchQuery.price = {};
      if (minPrice) searchQuery.price.$gte = parseInt(minPrice);
      if (maxPrice) searchQuery.price.$lte = parseInt(maxPrice);
    }

    // Stock filter
    if (inStock === 'true') {
      searchQuery.in_stock = true;
    }

    // Sorting
    let sortOptions = {};
    switch (sortBy) {
      case 'price_low':
        sortOptions = { price: 1 };
        break;
      case 'price_high':
        sortOptions = { price: -1 };
        break;
      case 'name':
        sortOptions = { name: 1 };
        break;
      case 'newest':
        sortOptions = { created_at: -1 };
        break;
      default:
        // Relevance sorting (for text search)
        if (q) {
          sortOptions = { score: { $meta: 'textScore' } };
        } else {
          sortOptions = { created_at: -1 };
        }
    }

    const skip = (parseInt(page) - 1) * parseInt(limit);

    const products = await Product.find(searchQuery)
      .select('name brand category price primary_image images in_stock stock_quantity is_featured variants')
      .sort(sortOptions)
      .skip(skip)
      .limit(parseInt(limit))
      .lean();

    const total = await Product.countDocuments(searchQuery);

    res.json({
      success: true,
      data: products,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / parseInt(limit))
      },
      filters: {
        query: q,
        category,
        brand,
        minPrice,
        maxPrice,
        inStock,
        sortBy
      }
    });

  } catch (error) {
    console.error('Product search error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to search products'
    });
  }
});

module.exports = router;
