/**
 * Warranty Management Routes
 * Handles warranty claims, tracking, and customer warranty services
 */

const express = require('express');
const router = express.Router();
const SerialNumber = require('../models/SerialNumber');
const Order = require('../models/Order');
const User = require('../models/User');
const Product = require('../models/Product');
const auditLogService = require('../services/auditLogService');
const emailService = require('../services/emailService');
const logger = require('../utils/logger');
const AppError = require('../utils/AppError');
const catchAsync = require('../utils/catchAsync');
const { protect } = require('../middleware/auth');

/**
 * @route   GET /api/warranty/check/:imei
 * @desc    Check warranty status by IMEI
 * @access  Public
 */
router.get('/check/:imei', catchAsync(async (req, res, next) => {
  const { imei } = req.params;

  if (!imei || imei.length < 10) {
    return next(new AppError('Valid IMEI is required', 400));
  }

  const serialNumber = await SerialNumber.findOne({ imei })
    .populate('product', 'name brand model category warranty_period')
    .populate('customer', 'name email phone');

  if (!serialNumber) {
    return next(new AppError('Device not found in our system', 404));
  }

  const warrantyStatus = serialNumber.warrantyStatus;
  const warrantyInfo = {
    imei: serialNumber.imei,
    product: serialNumber.product,
    warranty_status: warrantyStatus,
    warranty_start_date: serialNumber.warrantyStartDate,
    warranty_end_date: serialNumber.warrantyEndDate,
    purchase_date: serialNumber.saleDate,
    condition: serialNumber.condition,
    is_under_warranty: warrantyStatus === 'active',
    days_remaining: warrantyStatus === 'active' ? 
      Math.ceil((serialNumber.warrantyEndDate - new Date()) / (1000 * 60 * 60 * 24)) : 0
  };

  res.status(200).json({
    success: true,
    data: warrantyInfo
  });
}));

/**
 * @route   POST /api/warranty/claim
 * @desc    Submit warranty claim
 * @access  Private
 */
router.post('/claim', protect, catchAsync(async (req, res, next) => {
  const {
    imei,
    issue_description,
    issue_category,
    preferred_resolution,
    contact_phone,
    additional_notes
  } = req.body;

  // Validate required fields
  if (!imei || !issue_description || !issue_category) {
    return next(new AppError('IMEI, issue description, and category are required', 400));
  }

  // Find the device
  const serialNumber = await SerialNumber.findOne({ imei })
    .populate('product', 'name brand model category')
    .populate('customer', 'name email phone');

  if (!serialNumber) {
    return next(new AppError('Device not found in our system', 404));
  }

  // Check if device belongs to the user
  if (serialNumber.customer._id.toString() !== req.user.id) {
    return next(new AppError('You can only claim warranty for your own devices', 403));
  }

  // Check warranty status
  if (serialNumber.warrantyStatus !== 'active') {
    return next(new AppError('Device warranty has expired or is not active', 400));
  }

  // Create warranty claim
  const claimId = `WC-${Date.now()}-${Math.random().toString(36).substr(2, 5).toUpperCase()}`;
  
  const warrantyClaim = {
    claim_id: claimId,
    submitted_at: new Date(),
    issue_description,
    issue_category,
    preferred_resolution: preferred_resolution || 'repair',
    contact_phone: contact_phone || req.user.phone,
    additional_notes,
    status: 'submitted',
    submitted_by: req.user.id
  };

  // Add to serial number warranty claims
  if (!serialNumber.warranty_claims) {
    serialNumber.warranty_claims = [];
  }
  serialNumber.warranty_claims.push(warrantyClaim);
  
  await serialNumber.save();

  // Log the warranty claim
  await auditLogService.logWarrantyClaim(
    req.user.id,
    serialNumber._id,
    claimId,
    {
      imei,
      issue_category,
      issue_description: issue_description.substring(0, 100) + '...'
    }
  );

  // Send confirmation email
  try {
    await emailService.sendEmail({
      template: 'warranty_claim_confirmation',
      to: req.user.email,
      subject: `Warranty Claim Submitted - ${claimId}`,
      data: {
        customer_name: req.user.name,
        claim_id: claimId,
        product_name: serialNumber.product.name,
        imei: imei,
        issue_category,
        issue_description,
        estimated_response_time: '2-3 business days'
      }
    });
  } catch (emailError) {
    logger.error('Failed to send warranty claim confirmation email:', emailError);
  }

  res.status(201).json({
    success: true,
    message: 'Warranty claim submitted successfully',
    data: {
      claim_id: claimId,
      status: 'submitted',
      estimated_response_time: '2-3 business days',
      product: serialNumber.product,
      submitted_at: warrantyClaim.submitted_at
    }
  });
}));

/**
 * @route   GET /api/warranty/claims
 * @desc    Get user's warranty claims
 * @access  Private
 */
router.get('/claims', protect, catchAsync(async (req, res, next) => {
  const serialNumbers = await SerialNumber.find({ 
    customer: req.user.id,
    warranty_claims: { $exists: true, $ne: [] }
  })
  .populate('product', 'name brand model category')
  .select('imei product warranty_claims warrantyStatus warrantyEndDate');

  const claims = [];
  serialNumbers.forEach(device => {
    device.warranty_claims.forEach(claim => {
      claims.push({
        claim_id: claim.claim_id,
        imei: device.imei,
        product: device.product,
        issue_category: claim.issue_category,
        issue_description: claim.issue_description,
        status: claim.status,
        submitted_at: claim.submitted_at,
        last_updated: claim.last_updated || claim.submitted_at,
        preferred_resolution: claim.preferred_resolution,
        warranty_status: device.warrantyStatus
      });
    });
  });

  // Sort by submission date (newest first)
  claims.sort((a, b) => new Date(b.submitted_at) - new Date(a.submitted_at));

  res.status(200).json({
    success: true,
    count: claims.length,
    data: claims
  });
}));

/**
 * @route   GET /api/warranty/claim/:claimId
 * @desc    Get specific warranty claim details
 * @access  Private
 */
router.get('/claim/:claimId', protect, catchAsync(async (req, res, next) => {
  const { claimId } = req.params;

  const serialNumber = await SerialNumber.findOne({
    customer: req.user.id,
    'warranty_claims.claim_id': claimId
  })
  .populate('product', 'name brand model category warranty_period')
  .populate('customer', 'name email phone');

  if (!serialNumber) {
    return next(new AppError('Warranty claim not found', 404));
  }

  const claim = serialNumber.warranty_claims.find(c => c.claim_id === claimId);

  res.status(200).json({
    success: true,
    data: {
      claim,
      device: {
        imei: serialNumber.imei,
        product: serialNumber.product,
        warranty_status: serialNumber.warrantyStatus,
        warranty_end_date: serialNumber.warrantyEndDate,
        condition: serialNumber.condition
      }
    }
  });
}));

/**
 * @route   GET /api/warranty/devices
 * @desc    Get user's devices with warranty information
 * @access  Private
 */
router.get('/devices', protect, catchAsync(async (req, res, next) => {
  const devices = await SerialNumber.find({ customer: req.user.id })
    .populate('product', 'name brand model category warranty_period image')
    .select('imei product warrantyStatus warrantyStartDate warrantyEndDate condition saleDate warranty_claims')
    .sort({ saleDate: -1 });

  const devicesWithWarranty = devices.map(device => ({
    imei: device.imei,
    product: device.product,
    warranty_status: device.warrantyStatus,
    warranty_start_date: device.warrantyStartDate,
    warranty_end_date: device.warrantyEndDate,
    purchase_date: device.saleDate,
    condition: device.condition,
    days_remaining: device.warrantyStatus === 'active' ? 
      Math.ceil((device.warrantyEndDate - new Date()) / (1000 * 60 * 60 * 24)) : 0,
    active_claims: device.warranty_claims ? 
      device.warranty_claims.filter(claim => ['submitted', 'in_progress'].includes(claim.status)).length : 0,
    total_claims: device.warranty_claims ? device.warranty_claims.length : 0
  }));

  res.status(200).json({
    success: true,
    count: devicesWithWarranty.length,
    data: devicesWithWarranty
  });
}));

module.exports = router;
