const express = require('express');
const router = express.Router();
const orderController = require('../controllers/orderController');
const { protect, authorize, optionalAuth } = require('../middleware/auth');
const { paymentLimiter, adminLimiter } = require('../middleware/rateLimiter');
const { validateRequest, schemas } = require('../middleware/validateRequest');
const { validationRules, handleValidationErrors } = require('../middleware/inputValidation');

/**
 * @swagger
 * /api/orders:
 *   post:
 *     summary: Create a new order
 *     tags: [Orders]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - productId
 *               - customerEmail
 *               - customerName
 *             properties:
 *               productId:
 *                 type: string
 *                 description: ID of the product to purchase
 *               customerEmail:
 *                 type: string
 *                 format: email
 *                 description: Customer's email address
 *               customerName:
 *                 type: string
 *                 description: Customer's full name
 *     responses:
 *       200:
 *         description: Order created successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/PaymentResponse'
 *       400:
 *         description: Invalid input
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */

// Create order (with payment rate limiting) - supports both authenticated and guest users
router.post('/',
  paymentLimiter,
  optionalAuth,
  validationRules.orderCreation,
  handleValidationErrors,
  validateRequest(schemas.createOrder),
  orderController.createOrder
);

/**
 * @swagger
 * /api/orders/verify/{paymentId}:
 *   get:
 *     summary: Verify payment status
 *     tags: [Orders]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: paymentId
 *         required: true
 *         schema:
 *           type: string
 *         description: Payment ID to verify
 *     responses:
 *       200:
 *         description: Payment status retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     status:
 *                       type: string
 *                       enum: [completed, failed, pending]
 *                     order_id:
 *                       type: string
 *       404:
 *         description: Order not found
 *       500:
 *         description: Server error
 */

// Order fulfillment routes (admin only)
router.put('/:id/status',
  protect,
  authorize('admin'),
  orderController.updateOrderStatus
);

router.put('/:id/tracking',
  protect,
  authorize('admin'),
  orderController.addTrackingInfo
);

router.put('/:id/delivered',
  protect,
  authorize('admin'),
  orderController.markAsDelivered
);

/**
 * @swagger
 * /api/orders:
 *   get:
 *     summary: Get user's orders
 *     tags: [Orders]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: List of user's orders
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 count:
 *                   type: integer
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Order'
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */

// Get user orders
router.get('/',
  protect,
  orderController.getUserOrders
);

/**
 * @swagger
 * /api/orders/{id}:
 *   get:
 *     summary: Get order details
 *     tags: [Orders]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Order ID
 *     responses:
 *       200:
 *         description: Order details retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   $ref: '#/components/schemas/Order'
 *       404:
 *         description: Order not found
 *       403:
 *         description: Not authorized
 *       500:
 *         description: Server error
 */

// Get order details (supports both authenticated and guest access)
router.get('/:id',
  optionalAuth,
  orderController.getOrder
);

/**
 * @swagger
 * /api/orders/admin/all:
 *   get:
 *     summary: Get all orders (admin only)
 *     tags: [Orders]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [pending, completed, failed, cancelled]
 *         description: Filter by payment status
 *       - in: query
 *         name: startDate
 *         schema:
 *           type: string
 *           format: date
 *         description: Filter by start date
 *       - in: query
 *         name: endDate
 *         schema:
 *           type: string
 *           format: date
 *         description: Filter by end date
 *     responses:
 *       200:
 *         description: List of all orders
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 count:
 *                   type: integer
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Order'
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Not authorized
 *       500:
 *         description: Server error
 */

// Admin routes
router.use('/admin', adminLimiter);

// Get all orders (admin only)
router.get('/admin/all',
  protect,
  authorize('admin'),
  orderController.getAllOrders
);

// Get order analytics (admin only)
router.get('/admin/analytics',
  protect,
  authorize('admin'),
  orderController.getOrderAnalytics
);

// Cancel order (user or admin)
router.put('/:id/cancel',
  optionalAuth,
  orderController.cancelOrder
);

// Update order tracking (admin only)
router.put('/admin/:id/tracking',
  protect,
  authorize('admin'),
  orderController.updateOrderTracking
);

module.exports = router;