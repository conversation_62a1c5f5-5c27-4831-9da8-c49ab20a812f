/**
 * Phone Inventory Routes
 * Routes for IMEI tracking and phone-specific inventory management
 */

const express = require('express');
const router = express.Router();
const phoneInventoryController = require('../controllers/phoneInventoryController');
const { protect, authorize } = require('../middleware/auth');

// Public routes (no authentication required)
// Get available phones for a product variant - allow guests to see available phones
router.get('/phones/:productId/:variantSku', phoneInventoryController.getAvailablePhones);

// All other routes require authentication
router.use(protect);

// Get phone details by IMEI
router.get('/phones/imei/:imei', phoneInventoryController.getPhoneByIMEI);

// Reserve a phone
router.post('/phones/reserve', phoneInventoryController.reservePhone);

// Release phone reservation
router.post('/phones/release', phoneInventoryController.releaseReservation);

// Get phones by status
router.get('/phones/status/:status', authorize('admin'), phoneInventoryController.getPhonesByStatus);

// Update phone condition
router.patch('/phones/:imei/condition', authorize('admin'), phoneInventoryController.updatePhoneCondition);

// Admin-only routes
router.post('/phones', authorize('admin'), phoneInventoryController.addPhoneToInventory);
router.post('/phones/sold', authorize('admin'), phoneInventoryController.markPhoneAsSold);
router.get('/summary', authorize('admin'), phoneInventoryController.getInventorySummary);
router.post('/cleanup-reservations', authorize('admin'), phoneInventoryController.cleanupExpiredReservations);

module.exports = router;
