const express = require('express');
const router = express.Router();

// Generate SVG placeholder image
const generatePlaceholderSVG = (width, height, text = 'Phone Point Dar', bgColor = '#2563eb', textColor = '#ffffff') => {
  const fontSize = Math.min(width, height) / 8;
  
  return `
    <svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
      <rect width="100%" height="100%" fill="${bgColor}"/>
      <text x="50%" y="50%" font-family="Arial, sans-serif" font-size="${fontSize}" 
            fill="${textColor}" text-anchor="middle" dominant-baseline="middle">
        ${text}
      </text>
      <text x="50%" y="${50 + fontSize/2 + 10}%" font-family="Arial, sans-serif" font-size="${fontSize/3}" 
            fill="${textColor}" text-anchor="middle" dominant-baseline="middle" opacity="0.8">
        ${width}×${height}
      </text>
    </svg>
  `.trim();
};

// @route   GET /api/placeholder/:width/:height
// @desc    Generate placeholder image
// @access  Public
router.get('/:width/:height', (req, res) => {
  try {
    const { width, height } = req.params;
    const { text, bg, color } = req.query;
    
    // Validate dimensions
    const w = parseInt(width);
    const h = parseInt(height);
    
    if (isNaN(w) || isNaN(h) || w <= 0 || h <= 0 || w > 2000 || h > 2000) {
      return res.status(400).json({
        success: false,
        message: 'Invalid dimensions. Width and height must be positive numbers up to 2000px.'
      });
    }
    
    // Generate SVG
    const svg = generatePlaceholderSVG(
      w, 
      h, 
      text || 'Phone Point Dar',
      bg || '#2563eb',
      color || '#ffffff'
    );
    
    // Set headers for SVG
    res.setHeader('Content-Type', 'image/svg+xml');
    res.setHeader('Cache-Control', 'public, max-age=86400'); // Cache for 24 hours
    res.setHeader('Access-Control-Allow-Origin', '*');
    
    res.send(svg);
    
  } catch (error) {
    console.error('Placeholder image generation error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate placeholder image'
    });
  }
});

// @route   GET /api/placeholder/product/:width/:height
// @desc    Generate product-specific placeholder image
// @access  Public
router.get('/product/:width/:height', (req, res) => {
  try {
    const { width, height } = req.params;
    const { category, brand } = req.query;
    
    // Validate dimensions
    const w = parseInt(width);
    const h = parseInt(height);
    
    if (isNaN(w) || isNaN(h) || w <= 0 || h <= 0 || w > 2000 || h > 2000) {
      return res.status(400).json({
        success: false,
        message: 'Invalid dimensions. Width and height must be positive numbers up to 2000px.'
      });
    }
    
    // Generate product-specific text
    let text = 'Phone Point Dar';
    if (category && brand) {
      text = `${brand} ${category}`;
    } else if (category) {
      text = category;
    } else if (brand) {
      text = brand;
    }
    
    // Category-specific colors
    const categoryColors = {
      'smartphone': { bg: '#1e40af', text: '#ffffff' },
      'tablet': { bg: '#7c3aed', text: '#ffffff' },
      'earbuds': { bg: '#059669', text: '#ffffff' },
      'case': { bg: '#dc2626', text: '#ffffff' },
      'charger': { bg: '#ea580c', text: '#ffffff' },
      'accessory': { bg: '#4338ca', text: '#ffffff' }
    };
    
    const colors = categoryColors[category?.toLowerCase()] || { bg: '#2563eb', text: '#ffffff' };
    
    // Generate SVG with phone icon
    const svg = `
      <svg width="${w}" height="${h}" xmlns="http://www.w3.org/2000/svg">
        <rect width="100%" height="100%" fill="${colors.bg}"/>
        
        <!-- Phone icon -->
        <g transform="translate(${w/2 - 20}, ${h/2 - 40})">
          <rect x="0" y="0" width="40" height="60" rx="6" ry="6" 
                fill="none" stroke="${colors.text}" stroke-width="2"/>
          <rect x="5" y="8" width="30" height="40" rx="2" ry="2" 
                fill="none" stroke="${colors.text}" stroke-width="1"/>
          <circle cx="20" cy="53" r="3" fill="${colors.text}"/>
        </g>
        
        <!-- Text -->
        <text x="50%" y="${h/2 + 40}" font-family="Arial, sans-serif" font-size="${Math.min(w, h) / 12}" 
              fill="${colors.text}" text-anchor="middle" dominant-baseline="middle" font-weight="bold">
          ${text}
        </text>
        
        <!-- Dimensions -->
        <text x="50%" y="${h/2 + 60}" font-family="Arial, sans-serif" font-size="${Math.min(w, h) / 20}" 
              fill="${colors.text}" text-anchor="middle" dominant-baseline="middle" opacity="0.8">
          ${w}×${h}
        </text>
      </svg>
    `.trim();
    
    // Set headers for SVG
    res.setHeader('Content-Type', 'image/svg+xml');
    res.setHeader('Cache-Control', 'public, max-age=86400'); // Cache for 24 hours
    res.setHeader('Access-Control-Allow-Origin', '*');
    
    res.send(svg);
    
  } catch (error) {
    console.error('Product placeholder image generation error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate product placeholder image'
    });
  }
});

// @route   GET /api/placeholder/logo/:size
// @desc    Generate Phone Point Dar logo placeholder
// @access  Public
router.get('/logo/:size', (req, res) => {
  try {
    const { size } = req.params;
    const s = parseInt(size);
    
    if (isNaN(s) || s <= 0 || s > 500) {
      return res.status(400).json({
        success: false,
        message: 'Invalid size. Size must be a positive number up to 500px.'
      });
    }
    
    // Generate Phone Point Dar logo SVG
    const svg = `
      <svg width="${s}" height="${s}" xmlns="http://www.w3.org/2000/svg">
        <circle cx="${s/2}" cy="${s/2}" r="${s/2 - 2}" fill="#2563eb" stroke="#1e40af" stroke-width="2"/>
        
        <!-- Phone icon in center -->
        <g transform="translate(${s/2 - s/8}, ${s/2 - s/6})">
          <rect x="0" y="0" width="${s/4}" height="${s/3}" rx="${s/40}" ry="${s/40}" 
                fill="white" stroke="white" stroke-width="1"/>
          <rect x="${s/32}" y="${s/24}" width="${s/4 - s/16}" height="${s/4}" rx="${s/80}" ry="${s/80}" 
                fill="#2563eb"/>
          <circle cx="${s/8}" cy="${s/3 - s/24}" r="${s/48}" fill="white"/>
        </g>
        
        <!-- Text around the circle -->
        <text x="50%" y="${s/2 + s/3}" font-family="Arial, sans-serif" font-size="${s/12}" 
              fill="white" text-anchor="middle" dominant-baseline="middle" font-weight="bold">
          Phone Point
        </text>
        <text x="50%" y="${s/2 + s/3 + s/12}" font-family="Arial, sans-serif" font-size="${s/16}" 
              fill="white" text-anchor="middle" dominant-baseline="middle">
          Dar
        </text>
      </svg>
    `.trim();
    
    // Set headers for SVG
    res.setHeader('Content-Type', 'image/svg+xml');
    res.setHeader('Cache-Control', 'public, max-age=86400'); // Cache for 24 hours
    res.setHeader('Access-Control-Allow-Origin', '*');
    
    res.send(svg);
    
  } catch (error) {
    console.error('Logo placeholder generation error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate logo placeholder'
    });
  }
});

module.exports = router;
