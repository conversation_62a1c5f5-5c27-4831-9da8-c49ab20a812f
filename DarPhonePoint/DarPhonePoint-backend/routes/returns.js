/**
 * Returns & Refunds Management Routes
 * Handles return requests, refund processing, and return tracking
 */

const express = require('express');
const router = express.Router();
const SerialNumber = require('../models/SerialNumber');
const Order = require('../models/Order');
const User = require('../models/User');
const Product = require('../models/Product');
const auditLogService = require('../services/auditLogService');
const emailService = require('../services/emailService');
const logger = require('../utils/logger');
const AppError = require('../utils/AppError');
const catchAsync = require('../utils/catchAsync');
const { protect } = require('../middleware/auth');

/**
 * @route   POST /api/returns/initiate
 * @desc    Initiate return request
 * @access  Private
 */
router.post('/initiate', protect, catchAsync(async (req, res, next) => {
  const {
    order_id,
    imei,
    return_reason,
    return_category,
    condition_description,
    preferred_resolution,
    additional_notes
  } = req.body;

  // Validate required fields
  if (!order_id || !return_reason || !return_category) {
    return next(new AppError('Order ID, return reason, and category are required', 400));
  }

  // Find the order
  const order = await Order.findById(order_id)
    .populate('user', 'name email phone')
    .populate('items.product', 'name brand model category');

  if (!order) {
    return next(new AppError('Order not found', 404));
  }

  // Check if order belongs to the user
  if (order.user._id.toString() !== req.user.id) {
    return next(new AppError('You can only return items from your own orders', 403));
  }

  // Check if order is eligible for return (within return period)
  const returnPeriodDays = 14; // 14 days return policy
  const orderDate = new Date(order.createdAt);
  const currentDate = new Date();
  const daysSinceOrder = Math.ceil((currentDate - orderDate) / (1000 * 60 * 60 * 24));

  if (daysSinceOrder > returnPeriodDays) {
    return next(new AppError(`Return period has expired. Returns are only accepted within ${returnPeriodDays} days of purchase`, 400));
  }

  // Check order status
  if (!['delivered', 'completed'].includes(order.order_status)) {
    return next(new AppError('Returns can only be initiated for delivered orders', 400));
  }

  // Find the specific item/device if IMEI is provided
  let serialNumber = null;
  if (imei) {
    serialNumber = await SerialNumber.findOne({ 
      imei,
      order: order_id,
      customer: req.user.id
    }).populate('product', 'name brand model category');

    if (!serialNumber) {
      return next(new AppError('Device with specified IMEI not found in this order', 404));
    }

    // Check if device is already returned
    if (serialNumber.returnInfo && serialNumber.returnInfo.returned) {
      return next(new AppError('This device has already been returned', 400));
    }
  }

  // Create return request
  const returnId = `RET-${Date.now()}-${Math.random().toString(36).substr(2, 5).toUpperCase()}`;
  
  const returnRequest = {
    return_id: returnId,
    order_id: order_id,
    imei: imei || null,
    return_reason,
    return_category,
    condition_description,
    preferred_resolution: preferred_resolution || 'refund',
    additional_notes,
    status: 'submitted',
    submitted_at: new Date(),
    submitted_by: req.user.id,
    estimated_refund_amount: imei && serialNumber ? 
      serialNumber.salePrice : 
      order.total // For full order returns
  };

  // Add return request to order
  if (!order.return_requests) {
    order.return_requests = [];
  }
  order.return_requests.push(returnRequest);
  await order.save();

  // Update serial number if specific device return
  if (serialNumber) {
    serialNumber.status = 'return_requested';
    await serialNumber.save();
  }

  // Log the return request
  await auditLogService.logReturnRequest(
    req.user.id,
    order_id,
    returnId,
    {
      imei: imei || 'full_order',
      return_category,
      return_reason: return_reason.substring(0, 100) + '...'
    }
  );

  // Send confirmation email
  try {
    await emailService.sendEmail({
      template: 'return_request_confirmation',
      to: req.user.email,
      subject: `Return Request Submitted - ${returnId}`,
      data: {
        customer_name: req.user.name,
        return_id: returnId,
        order_id: order_id,
        product_info: imei && serialNumber ? 
          `${serialNumber.product.name} (IMEI: ${imei})` : 
          'Multiple items',
        return_reason,
        return_category,
        estimated_processing_time: '3-5 business days'
      }
    });
  } catch (emailError) {
    logger.error('Failed to send return request confirmation email:', emailError);
  }

  res.status(201).json({
    success: true,
    message: 'Return request submitted successfully',
    data: {
      return_id: returnId,
      status: 'submitted',
      estimated_processing_time: '3-5 business days',
      estimated_refund_amount: returnRequest.estimated_refund_amount,
      submitted_at: returnRequest.submitted_at
    }
  });
}));

/**
 * @route   GET /api/returns/requests
 * @desc    Get user's return requests
 * @access  Private
 */
router.get('/requests', protect, catchAsync(async (req, res, next) => {
  const orders = await Order.find({ 
    user: req.user.id,
    return_requests: { $exists: true, $ne: [] }
  })
  .populate('items.product', 'name brand model category')
  .select('_id order_number total return_requests createdAt');

  const returnRequests = [];
  orders.forEach(order => {
    order.return_requests.forEach(returnReq => {
      returnRequests.push({
        return_id: returnReq.return_id,
        order_id: order._id,
        order_number: order.order_number,
        imei: returnReq.imei,
        return_reason: returnReq.return_reason,
        return_category: returnReq.return_category,
        status: returnReq.status,
        submitted_at: returnReq.submitted_at,
        last_updated: returnReq.last_updated || returnReq.submitted_at,
        estimated_refund_amount: returnReq.estimated_refund_amount,
        preferred_resolution: returnReq.preferred_resolution
      });
    });
  });

  // Sort by submission date (newest first)
  returnRequests.sort((a, b) => new Date(b.submitted_at) - new Date(a.submitted_at));

  res.status(200).json({
    success: true,
    count: returnRequests.length,
    data: returnRequests
  });
}));

/**
 * @route   GET /api/returns/request/:returnId
 * @desc    Get specific return request details
 * @access  Private
 */
router.get('/request/:returnId', protect, catchAsync(async (req, res, next) => {
  const { returnId } = req.params;

  const order = await Order.findOne({
    user: req.user.id,
    'return_requests.return_id': returnId
  })
  .populate('items.product', 'name brand model category image')
  .populate('user', 'name email phone');

  if (!order) {
    return next(new AppError('Return request not found', 404));
  }

  const returnRequest = order.return_requests.find(r => r.return_id === returnId);

  // Get device details if IMEI is specified
  let deviceDetails = null;
  if (returnRequest.imei) {
    const serialNumber = await SerialNumber.findOne({ 
      imei: returnRequest.imei,
      order: order._id
    }).populate('product', 'name brand model category image');

    if (serialNumber) {
      deviceDetails = {
        imei: serialNumber.imei,
        product: serialNumber.product,
        condition: serialNumber.condition,
        sale_price: serialNumber.salePrice
      };
    }
  }

  res.status(200).json({
    success: true,
    data: {
      return_request: returnRequest,
      order: {
        _id: order._id,
        order_number: order.order_number,
        total: order.total,
        order_date: order.createdAt,
        items: order.items
      },
      device: deviceDetails
    }
  });
}));

/**
 * @route   GET /api/returns/eligible-orders
 * @desc    Get orders eligible for return
 * @access  Private
 */
router.get('/eligible-orders', protect, catchAsync(async (req, res, next) => {
  const returnPeriodDays = 14;
  const cutoffDate = new Date();
  cutoffDate.setDate(cutoffDate.getDate() - returnPeriodDays);

  const eligibleOrders = await Order.find({
    user: req.user.id,
    order_status: { $in: ['delivered', 'completed'] },
    createdAt: { $gte: cutoffDate }
  })
  .populate('items.product', 'name brand model category image')
  .select('_id order_number total items createdAt order_status')
  .sort({ createdAt: -1 });

  // Filter out orders that have been fully returned
  const ordersWithReturnInfo = eligibleOrders.map(order => {
    const daysSinceOrder = Math.ceil((new Date() - new Date(order.createdAt)) / (1000 * 60 * 60 * 24));
    const daysRemaining = returnPeriodDays - daysSinceOrder;
    
    return {
      _id: order._id,
      order_number: order.order_number,
      total: order.total,
      items: order.items,
      order_date: order.createdAt,
      order_status: order.order_status,
      days_remaining_for_return: Math.max(0, daysRemaining),
      can_return: daysRemaining > 0
    };
  });

  res.status(200).json({
    success: true,
    count: ordersWithReturnInfo.length,
    data: ordersWithReturnInfo
  });
}));

/**
 * @route   POST /api/returns/cancel/:returnId
 * @desc    Cancel return request
 * @access  Private
 */
router.post('/cancel/:returnId', protect, catchAsync(async (req, res, next) => {
  const { returnId } = req.params;
  const { cancellation_reason } = req.body;

  const order = await Order.findOne({
    user: req.user.id,
    'return_requests.return_id': returnId
  });

  if (!order) {
    return next(new AppError('Return request not found', 404));
  }

  const returnRequest = order.return_requests.find(r => r.return_id === returnId);

  if (!['submitted', 'under_review'].includes(returnRequest.status)) {
    return next(new AppError('Return request cannot be cancelled at this stage', 400));
  }

  // Update return request status
  returnRequest.status = 'cancelled';
  returnRequest.cancelled_at = new Date();
  returnRequest.cancellation_reason = cancellation_reason;
  returnRequest.last_updated = new Date();

  await order.save();

  // Update serial number status if applicable
  if (returnRequest.imei) {
    const serialNumber = await SerialNumber.findOne({ 
      imei: returnRequest.imei,
      order: order._id
    });

    if (serialNumber && serialNumber.status === 'return_requested') {
      serialNumber.status = 'sold';
      await serialNumber.save();
    }
  }

  // Log the cancellation
  await auditLogService.logReturnCancellation(
    req.user.id,
    order._id,
    returnId,
    { cancellation_reason }
  );

  res.status(200).json({
    success: true,
    message: 'Return request cancelled successfully',
    data: {
      return_id: returnId,
      status: 'cancelled',
      cancelled_at: returnRequest.cancelled_at
    }
  });
}));

module.exports = router;
