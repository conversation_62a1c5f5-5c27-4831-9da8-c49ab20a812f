/**
 * Unsubscribe Routes
 * Routes for handling unsubscribe requests
 */

const express = require('express');
const router = express.Router();
const unsubscribeController = require('../controllers/unsubscribeController');

// Unsubscribe from email sequence
router.get('/:token', unsubscribeController.unsubscribe);

// Submit unsubscribe feedback
router.post('/feedback', unsubscribeController.submitFeedback);

// Unsubscribe from all email sequences
router.post('/all', unsubscribeController.unsubscribeAll);

module.exports = router;
