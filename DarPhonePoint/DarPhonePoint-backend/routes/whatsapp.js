/**
 * WhatsApp Webhook Routes for Phone Point Dar
 * Handles WhatsApp Business API webhooks and customer interactions
 */

const express = require('express');
const router = express.Router();
const whatsappService = require('../services/whatsappService');
const logger = require('../utils/logger');
const AppError = require('../utils/AppError');
const catchAsync = require('../utils/catchAsync');

/**
 * @route   GET /api/whatsapp/webhook
 * @desc    WhatsApp webhook verification
 * @access  Public
 */
router.get('/webhook', (req, res) => {
  const mode = req.query['hub.mode'];
  const token = req.query['hub.verify_token'];
  const challenge = req.query['hub.challenge'];

  // Check if a token and mode were sent
  if (mode && token) {
    // Check the mode and token sent are correct
    if (mode === 'subscribe' && token === process.env.WHATSAPP_WEBHOOK_VERIFY_TOKEN) {
      // Respond with 200 OK and challenge token from the request
      logger.info('WhatsApp webhook verified successfully');
      res.status(200).send(challenge);
    } else {
      // Respond with '403 Forbidden' if verify tokens do not match
      logger.warn('WhatsApp webhook verification failed - invalid token');
      res.sendStatus(403);
    }
  } else {
    logger.warn('WhatsApp webhook verification failed - missing parameters');
    res.sendStatus(400);
  }
});

/**
 * @route   POST /api/whatsapp/webhook
 * @desc    WhatsApp webhook for incoming messages
 * @access  Public
 */
router.post('/webhook', catchAsync(async (req, res) => {
  const body = req.body;

  // Check if this is an event from a WhatsApp Business Account
  if (body.object) {
    if (body.entry && 
        body.entry[0].changes && 
        body.entry[0].changes[0] && 
        body.entry[0].changes[0].value.messages && 
        body.entry[0].changes[0].value.messages[0]
    ) {
      // Handle the incoming message
      await whatsappService.handleWebhook(body);
      
      logger.info('WhatsApp webhook processed successfully');
    }

    // Return a '200 OK' response to all requests
    res.status(200).send('EVENT_RECEIVED');
  } else {
    // Return a '404 Not Found' if event is not from a WhatsApp Business Account
    res.sendStatus(404);
  }
}));

/**
 * @route   POST /api/whatsapp/send-message
 * @desc    Send WhatsApp message (admin only)
 * @access  Private/Admin
 */
router.post('/send-message', catchAsync(async (req, res, next) => {
  const { phone, message, type = 'text' } = req.body;

  if (!phone || !message) {
    return next(new AppError('Phone number and message are required', 400));
  }

  const result = await whatsappService.sendMessage(phone, message, type);

  res.status(200).json({
    success: true,
    message: 'WhatsApp message sent successfully',
    data: result
  });
}));

/**
 * @route   POST /api/whatsapp/send-template
 * @desc    Send WhatsApp template message (admin only)
 * @access  Private/Admin
 */
router.post('/send-template', catchAsync(async (req, res, next) => {
  const { phone, templateName, parameters = [] } = req.body;

  if (!phone || !templateName) {
    return next(new AppError('Phone number and template name are required', 400));
  }

  const result = await whatsappService.sendTemplateMessage(phone, templateName, parameters);

  res.status(200).json({
    success: true,
    message: 'WhatsApp template message sent successfully',
    data: result
  });
}));

/**
 * @route   GET /api/whatsapp/status
 * @desc    Get WhatsApp service status
 * @access  Private/Admin
 */
router.get('/status', catchAsync(async (req, res) => {
  const status = whatsappService.getStatus();

  res.status(200).json({
    success: true,
    data: status
  });
}));

/**
 * @route   POST /api/whatsapp/broadcast
 * @desc    Send broadcast message to multiple recipients (admin only)
 * @access  Private/Admin
 */
router.post('/broadcast', catchAsync(async (req, res, next) => {
  const { phones, message, type = 'text' } = req.body;

  if (!phones || !Array.isArray(phones) || phones.length === 0) {
    return next(new AppError('Phone numbers array is required', 400));
  }

  if (!message) {
    return next(new AppError('Message is required', 400));
  }

  if (phones.length > 100) {
    return next(new AppError('Maximum 100 recipients allowed per broadcast', 400));
  }

  const results = [];
  let successCount = 0;
  let failureCount = 0;

  // Send messages with delay to avoid rate limiting
  for (let i = 0; i < phones.length; i++) {
    try {
      const result = await whatsappService.sendMessage(phones[i], message, type);
      results.push({
        phone: phones[i],
        success: result.success,
        messageId: result.messageId
      });
      
      if (result.success) {
        successCount++;
      } else {
        failureCount++;
      }

      // Add delay between messages (1 second)
      if (i < phones.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    } catch (error) {
      results.push({
        phone: phones[i],
        success: false,
        error: error.message
      });
      failureCount++;
    }
  }

  res.status(200).json({
    success: true,
    message: `Broadcast completed: ${successCount} successful, ${failureCount} failed`,
    data: {
      total_recipients: phones.length,
      successful_sends: successCount,
      failed_sends: failureCount,
      results: results
    }
  });
}));

/**
 * @route   POST /api/whatsapp/test
 * @desc    Test WhatsApp integration (admin only)
 * @access  Private/Admin
 */
router.post('/test', catchAsync(async (req, res, next) => {
  const { phone } = req.body;

  if (!phone) {
    return next(new AppError('Phone number is required for testing', 400));
  }

  const testMessage = `🧪 *Test Message - Phone Point Dar*

Hii ni ujumbe wa majaribio kutoka Phone Point Dar WhatsApp system.

✅ Kama umepokea ujumbe huu, maana yake mfumo wetu unafanya kazi vizuri!

📱 *Phone Point Dar* - Simu bora, bei nzuri!

_Ujumbe huu umetumwa ${new Date().toLocaleString('sw-TZ')}_`;

  const result = await whatsappService.sendMessage(phone, testMessage);

  res.status(200).json({
    success: true,
    message: 'Test message sent successfully',
    data: result
  });
}));

module.exports = router;
