/**
 * Payment Routes for Phone Point Dar
 * Handles payment processing, monitoring, and webhooks
 */

const express = require('express');
const router = express.Router();
const { protect, authorize } = require('../middleware/auth');
const { paymentLimiter } = require('../middleware/rateLimiter');
const paymentController = require('../controllers/paymentController');

// Apply payment rate limiting to all routes
router.use(paymentLimiter);

/**
 * @route   POST /api/payments
 * @desc    Create a payment for an order
 * @access  Private
 */
router.post('/', protect, paymentController.createPayment);

/**
 * @route   POST /api/payments/webhook
 * @desc    Handle payment webhook from Tanzania payment providers
 * @access  Public
 */
router.post('/webhook', paymentController.handleWebhook);

/**
 * @route   GET /api/payments/:id/status
 * @desc    Get payment status
 * @access  Private
 */
router.get('/:id/status', protect, paymentController.getPaymentStatus);

/**
 * @route   POST /api/payments/:id/verify
 * @desc    Manually verify payment status
 * @access  Private
 */
router.post('/:id/verify', protect, paymentController.verifyPayment);

// Payment Monitoring Routes (Admin only)

/**
 * @route   GET /api/payments/monitoring/metrics
 * @desc    Get payment monitoring metrics
 * @access  Private (Admin only)
 */
router.get('/monitoring/metrics', protect, authorize('admin'), paymentController.getPaymentMetrics);

/**
 * @route   GET /api/payments/monitoring/health
 * @desc    Get payment health status
 * @access  Private (Admin only)
 */
router.get('/monitoring/health', protect, authorize('admin'), paymentController.getPaymentHealth);

module.exports = router;
