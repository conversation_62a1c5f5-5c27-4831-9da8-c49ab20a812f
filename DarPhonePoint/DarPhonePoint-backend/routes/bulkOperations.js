/**
 * Bulk Operations Routes
 * Handles bulk operations for products, orders, and other entities
 */

const express = require('express');
const router = express.Router();
const multer = require('multer');
const csv = require('csv-parser');
const fs = require('fs');
const path = require('path');
const Product = require('../models/Product');
const Order = require('../models/Order');
const Inventory = require('../models/Inventory');
const User = require('../models/User');
const auditLogService = require('../services/auditLogService');
const logger = require('../utils/logger');
const AppError = require('../utils/AppError');
const catchAsync = require('../utils/catchAsync');
const { protect, authorize } = require('../middleware/auth');

// Configure multer for file uploads
const upload = multer({
  dest: 'uploads/bulk/',
  limits: {
    fileSize: 10 * 1024 * 1024 // 10MB limit
  },
  fileFilter: (req, file, cb) => {
    if (file.mimetype === 'text/csv' || file.originalname.endsWith('.csv')) {
      cb(null, true);
    } else {
      cb(new AppError('Only CSV files are allowed', 400), false);
    }
  }
});

/**
 * @route   POST /api/bulk/products/import
 * @desc    Bulk import products from CSV
 * @access  Private/Admin
 */
router.post('/products/import', protect, authorize('admin'), upload.single('file'), catchAsync(async (req, res, next) => {
  if (!req.file) {
    return next(new AppError('CSV file is required', 400));
  }

  const results = [];
  const errors = [];
  let successCount = 0;
  let errorCount = 0;

  try {
    // Read and parse CSV file
    const csvData = await new Promise((resolve, reject) => {
      const data = [];
      fs.createReadStream(req.file.path)
        .pipe(csv())
        .on('data', (row) => data.push(row))
        .on('end', () => resolve(data))
        .on('error', reject);
    });

    // Process each row
    for (let i = 0; i < csvData.length; i++) {
      const row = csvData[i];
      const rowNumber = i + 2; // +2 because CSV starts at row 1 and we skip header

      try {
        // Validate required fields
        if (!row.name || !row.price || !row.category) {
          throw new Error('Missing required fields: name, price, category');
        }

        // Check if product already exists
        const existingProduct = await Product.findOne({ 
          name: row.name.trim(),
          brand: row.brand?.trim() || undefined
        });

        if (existingProduct) {
          throw new Error('Product already exists');
        }

        // Create product
        const productData = {
          name: row.name.trim(),
          description: row.description?.trim() || '',
          price: parseFloat(row.price),
          category: row.category.trim(),
          brand: row.brand?.trim() || '',
          model: row.model?.trim() || '',
          specifications: {},
          is_active: row.is_active?.toLowerCase() === 'true' || true,
          is_featured: row.is_featured?.toLowerCase() === 'true' || false,
          track_inventory: row.track_inventory?.toLowerCase() === 'true' || true,
          warranty_period: parseInt(row.warranty_period) || 12,
          tags: row.tags ? row.tags.split(',').map(tag => tag.trim()) : []
        };

        // Add specifications based on category
        if (row.storage) productData.specifications.storage = row.storage.trim();
        if (row.ram) productData.specifications.ram = row.ram.trim();
        if (row.color) productData.specifications.color = row.color.trim();
        if (row.screen_size) productData.specifications.screen_size = row.screen_size.trim();
        if (row.battery) productData.specifications.battery = row.battery.trim();
        if (row.camera) productData.specifications.camera = row.camera.trim();
        if (row.operating_system) productData.specifications.operating_system = row.operating_system.trim();

        const product = new Product(productData);
        await product.save();

        // Create inventory record if quantity is provided
        if (row.quantity && parseInt(row.quantity) > 0) {
          const inventory = new Inventory({
            product: product._id,
            quantity_available: parseInt(row.quantity),
            quantity_reserved: 0,
            reorder_level: parseInt(row.reorder_level) || 10,
            location: row.location?.trim() || 'Main Warehouse'
          });
          await inventory.save();
        }

        results.push({
          row: rowNumber,
          status: 'success',
          product_id: product._id,
          name: product.name
        });
        successCount++;

      } catch (error) {
        errors.push({
          row: rowNumber,
          error: error.message,
          data: row
        });
        errorCount++;
      }
    }

    // Clean up uploaded file
    fs.unlinkSync(req.file.path);

    // Log bulk import
    await auditLogService.logBulkOperation(
      req.user.id,
      'product_import',
      {
        total_rows: csvData.length,
        successful: successCount,
        failed: errorCount
      }
    );

    res.status(200).json({
      success: true,
      message: `Bulk import completed: ${successCount} successful, ${errorCount} failed`,
      data: {
        total_rows: csvData.length,
        successful_imports: successCount,
        failed_imports: errorCount,
        results,
        errors: errors.slice(0, 10) // Limit errors in response
      }
    });

  } catch (error) {
    // Clean up uploaded file on error
    if (req.file && fs.existsSync(req.file.path)) {
      fs.unlinkSync(req.file.path);
    }
    
    logger.error('Bulk product import failed:', error);
    return next(new AppError('Bulk import failed', 500));
  }
}));

/**
 * @route   POST /api/bulk/products/update-status
 * @desc    Bulk update product status
 * @access  Private/Admin
 */
router.post('/products/update-status', protect, authorize('admin'), catchAsync(async (req, res, next) => {
  const { product_ids, status } = req.body;

  if (!product_ids || !Array.isArray(product_ids) || product_ids.length === 0) {
    return next(new AppError('Product IDs array is required', 400));
  }

  if (!['active', 'inactive'].includes(status)) {
    return next(new AppError('Status must be active or inactive', 400));
  }

  const updateData = { is_active: status === 'active' };
  const result = await Product.updateMany(
    { _id: { $in: product_ids } },
    updateData
  );

  await auditLogService.logBulkOperation(
    req.user.id,
    'product_status_update',
    {
      product_ids,
      status,
      modified_count: result.modifiedCount
    }
  );

  res.status(200).json({
    success: true,
    message: `Updated ${result.modifiedCount} products`,
    data: {
      matched_count: result.matchedCount,
      modified_count: result.modifiedCount
    }
  });
}));

/**
 * @route   POST /api/bulk/products/update-pricing
 * @desc    Bulk update product pricing
 * @access  Private/Admin
 */
router.post('/products/update-pricing', protect, authorize('admin'), catchAsync(async (req, res, next) => {
  const { product_ids, pricing_action, value } = req.body;

  if (!product_ids || !Array.isArray(product_ids) || product_ids.length === 0) {
    return next(new AppError('Product IDs array is required', 400));
  }

  if (!['set', 'increase', 'decrease', 'percentage_increase', 'percentage_decrease'].includes(pricing_action)) {
    return next(new AppError('Invalid pricing action', 400));
  }

  if (!value || isNaN(value)) {
    return next(new AppError('Valid numeric value is required', 400));
  }

  const products = await Product.find({ _id: { $in: product_ids } });
  let updatedCount = 0;

  for (const product of products) {
    let newPrice = product.price;

    switch (pricing_action) {
      case 'set':
        newPrice = parseFloat(value);
        break;
      case 'increase':
        newPrice = product.price + parseFloat(value);
        break;
      case 'decrease':
        newPrice = Math.max(0, product.price - parseFloat(value));
        break;
      case 'percentage_increase':
        newPrice = product.price * (1 + parseFloat(value) / 100);
        break;
      case 'percentage_decrease':
        newPrice = product.price * (1 - parseFloat(value) / 100);
        break;
    }

    if (newPrice > 0) {
      product.price = Math.round(newPrice * 100) / 100; // Round to 2 decimal places
      await product.save();
      updatedCount++;
    }
  }

  await auditLogService.logBulkOperation(
    req.user.id,
    'product_pricing_update',
    {
      product_ids,
      pricing_action,
      value,
      updated_count: updatedCount
    }
  );

  res.status(200).json({
    success: true,
    message: `Updated pricing for ${updatedCount} products`,
    data: {
      updated_count: updatedCount,
      total_products: products.length
    }
  });
}));

/**
 * @route   POST /api/bulk/orders/update-status
 * @desc    Bulk update order status
 * @access  Private/Admin
 */
router.post('/orders/update-status', protect, authorize('admin'), catchAsync(async (req, res, next) => {
  const { order_ids, status } = req.body;

  if (!order_ids || !Array.isArray(order_ids) || order_ids.length === 0) {
    return next(new AppError('Order IDs array is required', 400));
  }

  const validStatuses = ['pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled'];
  if (!validStatuses.includes(status)) {
    return next(new AppError(`Status must be one of: ${validStatuses.join(', ')}`, 400));
  }

  const updateData = { order_status: status };
  
  // Add timestamp for specific statuses
  if (status === 'confirmed') updateData.confirmed_at = new Date();
  if (status === 'shipped') updateData.shipped_at = new Date();
  if (status === 'delivered') updateData.delivered_at = new Date();
  if (status === 'cancelled') updateData.cancelled_at = new Date();

  const result = await Order.updateMany(
    { _id: { $in: order_ids } },
    updateData
  );

  await auditLogService.logBulkOperation(
    req.user.id,
    'order_status_update',
    {
      order_ids,
      status,
      modified_count: result.modifiedCount
    }
  );

  res.status(200).json({
    success: true,
    message: `Updated ${result.modifiedCount} orders`,
    data: {
      matched_count: result.matchedCount,
      modified_count: result.modifiedCount
    }
  });
}));

/**
 * @route   DELETE /api/bulk/products
 * @desc    Bulk delete products
 * @access  Private/Admin
 */
router.delete('/products', protect, authorize('admin'), catchAsync(async (req, res, next) => {
  const { product_ids } = req.body;

  if (!product_ids || !Array.isArray(product_ids) || product_ids.length === 0) {
    return next(new AppError('Product IDs array is required', 400));
  }

  // Check for existing orders with these products
  const ordersWithProducts = await Order.find({
    'items.product': { $in: product_ids }
  }).countDocuments();

  if (ordersWithProducts > 0) {
    return next(new AppError('Cannot delete products that have existing orders', 400));
  }

  // Delete related inventory records
  await Inventory.deleteMany({ product: { $in: product_ids } });

  // Delete products
  const result = await Product.deleteMany({ _id: { $in: product_ids } });

  await auditLogService.logBulkOperation(
    req.user.id,
    'product_bulk_delete',
    {
      product_ids,
      deleted_count: result.deletedCount
    }
  );

  res.status(200).json({
    success: true,
    message: `Deleted ${result.deletedCount} products`,
    data: {
      deleted_count: result.deletedCount
    }
  });
}));

module.exports = router;
