const express = require('express');
const {
  createShipping,
  getShippingByOrder,
  trackShipment,
  updateShippingStatus,
  generateShippingLabel,
  markAsPacked,
  getAllShipments,
  calculateShippingRates
} = require('../controllers/shippingController');

const { protect, authorize } = require('../middleware/auth');
const { validateShippingData, validateTrackingUpdate } = require('../middleware/validation');

const router = express.Router();

// Public routes
router.get('/track/:trackingNumber', trackShipment);
router.post('/calculate-rates', calculateShippingRates);

// Protected routes
router.use(protect);

// Customer routes
router.get('/order/:orderId', getShippingByOrder);

// Admin only routes
router.use(authorize('admin'));

router.route('/')
  .get(getAllShipments)
  .post(validateShippingData, createShipping);

router.patch('/:id/status', validateTrackingUpdate, updateShippingStatus);
router.post('/:id/label', generateShippingLabel);
router.patch('/:id/packed', markAsPacked);

module.exports = router;
