const express = require('express');
const router = express.Router();
const {
  getSettings,
  getPublicSettings,
  updateSettings,
  updateSetting,
  deleteSetting,
  resetSettings,
  initializeSettings
} = require('../controllers/settingsController');
const { protect, authorize } = require('../middleware/auth');
const { adminLimiter } = require('../middleware/rateLimiter');

/**
 * @route   GET /api/settings/public
 * @desc    Get public settings (accessible to all users)
 * @access  Public
 */
router.get('/public', getPublicSettings);

// Apply admin protection and rate limiting to all other routes
router.use(protect);
router.use(authorize('admin'));
router.use(adminLimiter);

/**
 * @route   GET /api/settings
 * @desc    Get all settings or settings by category
 * @access  Private/Admin
 * @query   category - Optional category filter
 */
router.get('/', getSettings);

/**
 * @route   PATCH /api/settings
 * @desc    Update settings for a category
 * @access  Private/Admin
 * @query   category - Required category parameter
 */
router.patch('/', updateSettings);

/**
 * @route   PUT /api/settings/:category/:key
 * @desc    Update a single setting
 * @access  Private/Admin
 */
router.put('/:category/:key', updateSetting);

/**
 * @route   DELETE /api/settings/:category/:key
 * @desc    Delete a setting
 * @access  Private/Admin
 */
router.delete('/:category/:key', deleteSetting);

/**
 * @route   POST /api/settings/reset
 * @desc    Reset settings to default values
 * @access  Private/Admin
 */
router.post('/reset', resetSettings);

/**
 * @route   POST /api/settings/initialize
 * @desc    Initialize default settings (first run only)
 * @access  Private/Admin
 */
router.post('/initialize', initializeSettings);

module.exports = router;
