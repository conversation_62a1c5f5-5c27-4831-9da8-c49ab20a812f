const express = require('express');
const router = express.Router();
const monitoringController = require('../controllers/monitoringController');
const { protect, authorize } = require('../middleware/auth');

// All monitoring routes are protected and require admin role
router.use(protect);
router.use(authorize('admin'));

// Get current metrics
router.get('/metrics', monitoringController.getMetrics);

// Get performance report
router.get('/performance', monitoringController.getPerformanceReport);

// Reset metrics
router.post('/reset', monitoringController.resetMetrics);

module.exports = router; 