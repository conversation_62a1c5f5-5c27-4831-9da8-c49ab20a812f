const express = require('express');
const router = express.Router();
const { protect, authorize } = require('../middleware/auth');
const { check } = require('express-validator');
const userController = require('../controllers/userController');

// @route   PUT api/users/profile
// @desc    Update user profile
// @access  Private
router.put(
  '/profile',
  protect,
  [
    check('name', 'Name is required').not().isEmpty(),
    check('email', 'Please include a valid email').isEmail(),
    check('newPassword', 'Password must be at least 6 characters').optional().isLength({ min: 6 })
  ],
  userController.updateProfile
);

// @route   GET api/users
// @desc    Get all users
// @access  Admin
router.get('/', protect, authorize('admin'), userController.getAllUsers);

// Specific routes must come before generic /:id route
// @route   GET api/users/addresses
// @desc    Get user's saved addresses
// @access  Private
router.get('/addresses', protect, userController.getUserAddresses);

// @route   POST api/users/addresses
// @desc    Add new address for user
// @access  Private
router.post('/addresses', protect, userController.addUserAddress);

// @route   PUT api/users/addresses/:addressId
// @desc    Update user address
// @access  Private
router.put('/addresses/:addressId', protect, userController.updateUserAddress);

// @route   DELETE api/users/addresses/:addressId
// @desc    Delete user address
// @access  Private
router.delete('/addresses/:addressId', protect, userController.deleteUserAddress);

// @route   GET api/users/:id
// @desc    Get user by ID
// @access  Admin
router.get('/:id', protect, authorize('admin'), userController.getUserById);

// @route   POST api/users
// @desc    Create a new user
// @access  Admin
router.post(
  '/',
  [
    protect,
    authorize('admin'),
    [
      check('name', 'Name is required').not().isEmpty(),
      check('email', 'Please include a valid email').isEmail(),
      check('password', 'Password must be at least 6 characters').isLength({ min: 6 }),
      check('role', 'Role must be either admin or user').optional().isIn(['admin', 'user'])
    ]
  ],
  userController.createUser
);

// @route   PUT api/users/:id
// @desc    Update a user
// @access  Admin
router.put(
  '/:id',
  [
    protect,
    authorize('admin'),
    [
      check('name', 'Name is required').optional().not().isEmpty(),
      check('email', 'Please include a valid email').optional().isEmail(),
      check('password', 'Password must be at least 6 characters').optional().isLength({ min: 6 }),
      check('role', 'Role must be either admin or user').optional().isIn(['admin', 'user'])
    ]
  ],
  userController.updateUser
);

// @route   DELETE api/users/:id
// @desc    Delete a user
// @access  Admin
router.delete('/:id', protect, authorize('admin'), userController.deleteUser);

module.exports = router;
