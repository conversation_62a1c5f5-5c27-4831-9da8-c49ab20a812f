const express = require('express');
const router = express.Router();
const leadController = require('../controllers/leadController');
const { protect, authorize } = require('../middleware/auth');
const { apiLimiter } = require('../middleware/rateLimiter');
const { validateRequest, schemas } = require('../middleware/validateRequest');

// @route   POST /api/leads/capture
// @desc    Capture new lead
// @access  Public
router.post('/capture', apiLimiter, validateRequest(schemas.leadCapture), leadController.captureLead);

// @route   POST /api/leads
// @desc    Capture new lead (alternative endpoint for frontend compatibility)
// @access  Public
router.post('/', apiLimiter, validateRequest(schemas.leadCapture), leadController.captureLead);

// @route   POST /api/leads/request-free-product
// @desc    Request free product delivery via email attachment
// @access  Public
router.post('/request-free-product', apiLimiter, leadController.requestFreeProduct);

// @route   GET /api/leads
// @desc    Get all leads
// @access  Admin
router.get('/', protect, authorize('admin'), leadController.getLeads);

module.exports = router;