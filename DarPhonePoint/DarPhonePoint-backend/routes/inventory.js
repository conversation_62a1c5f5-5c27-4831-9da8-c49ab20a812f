const express = require('express');
const {
  getAllInventory,
  getProductInventory,
  createOrUpdateInventory,
  addInventoryMovement,
  reserveInventory,
  releaseInventory,
  getLowStockAlerts,
  getInventorySummary
} = require('../controllers/inventoryController');

const { protect, authorize } = require('../middleware/auth');
const { validateInventoryData, validateMovementData } = require('../middleware/validation');

const router = express.Router();

// Protect all routes
router.use(protect);

// Public routes (for order processing)
router.post('/reserve', reserveInventory);
router.post('/release', releaseInventory);

// Admin only routes
router.use(authorize('admin'));

router.route('/')
  .get(getAllInventory)
  .post(validateInventoryData, createOrUpdateInventory);

router.get('/product/:productId', getProductInventory);
router.post('/:id/movement', validateMovementData, addInventoryMovement);
router.get('/alerts/low-stock', getLowStockAlerts);
router.get('/summary', getInventorySummary);

module.exports = router;
