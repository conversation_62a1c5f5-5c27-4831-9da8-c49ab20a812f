# Phone Point Dar - Environment Configuration Guide

This guide explains how to properly configure environment variables for Phone Point Dar in different deployment environments.

## 🔧 Environment Files Overview

### Frontend Environment Files
- `.env.development` - Development environment (localhost)
- `.env.production` - Production environment (live deployment)
- `.env.example` - Template with all available variables

### Backend Environment Files
- `.env.development` - Development environment (localhost)
- `.env.production` - Production environment (live deployment)
- `.env.example` - Template with all available variables

## 🚀 Quick Setup

### Development Environment

1. **Backend Setup:**
   ```bash
   cd DarPhonePoint-backend
   cp .env.example .env
   # Edit .env with your development values
   ```

2. **Frontend Setup:**
   ```bash
   cd DarPhonePoint-frontend
   cp .env.example .env
   # Edit .env with your development values
   ```

### Production Environment

1. **Backend Production:**
   ```bash
   cd DarPhonePoint-backend
   cp .env.production .env
   # Update all production values marked with "MUST be updated"
   ```

2. **Frontend Production:**
   ```bash
   cd DarPhonePoint-frontend
   cp .env.production .env
   # Update all production values
   ```

## 🔑 Critical Environment Variables

### Backend - MUST Configure for Production

```bash
# Database
MONGODB_URI=mongodb://your-production-mongodb-uri/phonepointdar

# Security
JWT_SECRET=your-super-secure-jwt-secret-key-for-production
JWT_REFRESH_SECRET=your-super-secure-jwt-refresh-secret-key-for-production
SESSION_SECRET=production_session_secret_64_characters_long_minimum

# URLs
BASE_URL=https://phonepointdar.com
API_URL=https://api.phonepointdar.com
FRONTEND_URL=https://phonepointdar.com

# Email
EMAIL_HOST=smtp.your-email-provider.com
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your-production-email-password

# Redis (if using)
REDIS_HOST=your-redis-host
REDIS_PASSWORD=your-redis-password

# Tanzania Payments
MPESA_CONSUMER_KEY=your-mpesa-consumer-key
MPESA_CONSUMER_SECRET=your-mpesa-consumer-secret
TIGO_PESA_API_KEY=your-tigo-pesa-api-key
AIRTEL_MONEY_API_KEY=your-airtel-money-api-key
```

### Frontend - MUST Configure for Production

```bash
# API Configuration
VITE_API_URL=/api  # Or full URL if different domain

# URLs
VITE_FRONTEND_URL=https://phonepointdar.com
VITE_BACKEND_URL=https://api.phonepointdar.com

# App Configuration
VITE_APP_NAME=Phone Point Dar
VITE_COMPANY_NAME=Phone Point Dar
```

## 🌍 Deployment-Specific Configuration

### Docker Deployment

Create a `docker-compose.yml` with environment variables:

```yaml
version: '3.8'
services:
  backend:
    build: ./DarPhonePoint-backend
    environment:
      - NODE_ENV=production
      - MONGODB_URI=${MONGODB_URI}
      - JWT_SECRET=${JWT_SECRET}
      - FRONTEND_URL=${FRONTEND_URL}
    ports:
      - "5001:5001"

  frontend:
    build: ./DarPhonePoint-frontend
    environment:
      - VITE_API_URL=/api
      - VITE_APP_NAME=Phone Point Dar
    ports:
      - "80:80"
```

### Nginx Configuration

For production deployment with Nginx:

```nginx
server {
    listen 80;
    server_name phonepointdar.com www.phonepointdar.com;
    
    # Frontend
    location / {
        root /var/www/phonepointdar/frontend;
        try_files $uri $uri/ /index.html;
    }
    
    # API Proxy
    location /api/ {
        proxy_pass http://localhost:5001/api/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## 🔒 Security Best Practices

### JWT Secrets
- Use at least 64 characters
- Include uppercase, lowercase, numbers, and symbols
- Generate using: `openssl rand -base64 64`

### Database Security
- Use strong MongoDB credentials
- Enable MongoDB authentication
- Use SSL/TLS for database connections

### Email Configuration
- Use app-specific passwords for Gmail
- Consider using dedicated email service (SendGrid, Mailgun)
- Set up SPF, DKIM, and DMARC records

## 🇹🇿 Tanzania-Specific Configuration

### Payment Providers

1. **M-Pesa (Vodacom)**
   - Register at: https://developer.mpesa.vm.co.tz/
   - Get Consumer Key and Secret
   - Configure shortcode and passkey

2. **Tigo Pesa**
   - Contact Tigo Business for API access
   - Get API credentials

3. **Airtel Money**
   - Contact Airtel Business for API access
   - Get API credentials

### Currency and Localization
```bash
DEFAULT_CURRENCY=TZS
DEFAULT_LANGUAGE=en
TIMEZONE=Africa/Dar_es_Salaam
TAX_RATE=0.18  # 18% VAT in Tanzania
```

## 🚨 Common Issues and Solutions

### Issue: API calls failing in production
**Solution:** Ensure `VITE_API_URL` is correctly set to `/api` or full API URL

### Issue: CORS errors
**Solution:** Update `CORS_ORIGIN` in backend to include your domain

### Issue: Email not sending
**Solution:** Check email credentials and enable "Less secure app access" for Gmail

### Issue: Database connection failed
**Solution:** Verify MongoDB URI and network connectivity

### Issue: JWT tokens not working
**Solution:** Ensure JWT_SECRET is set and consistent across all instances

## 📝 Environment Variable Reference

### Backend Variables
| Variable | Required | Default | Description |
|----------|----------|---------|-------------|
| `NODE_ENV` | Yes | development | Environment mode |
| `PORT` | No | 5001 | Server port |
| `MONGODB_URI` | Yes | - | MongoDB connection string |
| `JWT_SECRET` | Yes | - | JWT signing secret |
| `FRONTEND_URL` | Yes | - | Frontend URL for CORS |
| `EMAIL_HOST` | No | smtp.gmail.com | SMTP host |
| `REDIS_HOST` | No | localhost | Redis host |

### Frontend Variables
| Variable | Required | Default | Description |
|----------|----------|---------|-------------|
| `VITE_API_URL` | Yes | /api | Backend API URL |
| `VITE_APP_NAME` | No | Phone Point Dar | Application name |
| `VITE_FRONTEND_URL` | No | - | Frontend URL |

## 🔄 Migration from Development to Production

1. **Backup Development Data**
2. **Update Environment Files**
3. **Test Configuration**
4. **Deploy with New Environment**
5. **Verify All Services**

## 📞 Support

For environment configuration issues:
- Email: <EMAIL>
- Check logs for specific error messages
- Verify all required variables are set
