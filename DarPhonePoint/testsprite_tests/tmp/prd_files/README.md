# Phone Point Dar Backend

Backend API for the Phone Point Dar e-commerce platform, providing mobile device sales, payment processing, and customer management functionality.

## Setup Instructions

### Prerequisites

- Node.js (v18+)
- MongoDB
- ClickPesa account (for Tanzania payments)
- Gmail SMTP (for email notifications)
- Redis (for caching)

### Installation

1. Clone the repository

```bash
git clone <repository-url>
cd DarPhonePoint/DarPhonePoint-backend
```

2. Install dependencies

```bash
npm install
```

3. Create environment variables

```bash
cp .env.example .env
```

4. Update the `.env` file with your configuration values

5. Seed the database with products

```bash
npm run seed:products
```

6. Create an admin user

```bash
npm run create:admin
```

7. Start the server

````bash
# AIXcelerate Backend

Backend API for the AIXcelerate platform, providing lead generation, product delivery, and payment processing functionality.

## Setup Instructions

### Prerequisites

- Node.js (v14+)
- MongoDB
- AWS S3 account (for file storage)
- Stripe account (for payments)
- SMTP email provider

### Installation

1. Clone the repository

```bash
git clone <repository-url>
cd aixcelerate-backend
````

2. Install dependencies

```bash
npm install
```

3. Create environment variables

```bash
cp .env.example .env
```

4. Update the `.env` file with your configuration values

5. Seed the database with products

```bash
npm run seed:products
```

6. Create an admin user

```bash
npm run create:admin
```

7. Start the server

```bash
npm run server
```

### API Endpoints

#### Authentication

- `POST /api/auth/register` - Register a new user
- `POST /api/auth/login` - Login user
- `GET /api/auth/me` - Get current user

#### Leads

- `POST /api/leads/capture` - Capture new lead
- `GET /api/leads` - Get all leads (admin only)

#### Products

- `GET /api/products` - Get all products
- `GET /api/products/:id` - Get single product
- `POST /api/products` - Create product (admin only)
- `PUT /api/products/:id` - Update product (admin only)

### API Documentation

API documentation is available at `/api/docs`

### API Status

API status is available at `/api/status`

### Security

- All API endpoints are secured with JWT authentication
- Passwords are hashed using bcrypt
- Sensitive data is encrypted using AES-256

### Environment Variables

- `NODE_ENV`: Node environment (development, production, etc.)
- `MONGO_URI`: MongoDB connection string
- `AWS_ACCESS_KEY_ID`: AWS access key ID
- `AWS_SECRET_ACCESS_KEY`: AWS secret access key
- `STRIPE_SECRET_KEY`: Stripe secret key
- `SMTP_HOST`: SMTP host
- `SMTP_PORT`: SMTP port
- `SMTP_USERNAME`: SMTP username
- `SMTP_PASSWORD`: SMTP password

### Contributing

Contributions are welcome. Please submit a pull request with your changes.

### License

This project is licensed under the MIT License.# AIXcelerate Backend

Backend API for the AIXcelerate platform, providing lead generation, product delivery, and payment processing functionality.

## Setup Instructions

### Prerequisites

- Node.js (v14+)
- MongoDB
- AWS S3 account (for file storage)
- Stripe account (for payments)
- SMTP email provider

### Installation

1. Clone the repository

```bash
git clone <repository-url>
cd aixcelerate-backend
```

2. Install dependencies

```bash
npm install
```

3. Create environment variables

```bash
cp .env.example .env
```

4. Update the `.env` file with your configuration values

5. Seed the database with products

```bash
npm run seed:products
```

6. Create an admin user

```bash
npm run create:admin
```

7. Start the server

```bash
npm run server
```

### API Endpoints

#### Authentication

- `POST /api/auth/register` - Register a new user
- `POST /api/auth/login` - Login user
- `GET /api/auth/me` - Get current user

#### Leads

- `POST /api/leads/capture` - Capture new lead
- `GET /api/leads` - Get all leads (admin only)

#### Products

- `GET /api/products` - Get all products
- `GET /api/products/:id` - Get single product
- `POST /api/products` - Create product (admin only)
- `PUT /api/products/:id` - Update product (admin only)

### API Documentation

API documentation is available at `/api/docs`

### API Status

API status is available at `/api/status`

### Security

- All API endpoints are secured with JWT authentication
- Passwords are hashed using bcrypt
- Sensitive data is encrypted using AES-256

### Environment Variables

- `NODE_ENV`: Node environment (development, production, etc.)
- `MONGO_URI`: MongoDB connection string
- `AWS_ACCESS_KEY_ID`: AWS access key ID
- `AWS_SECRET_ACCESS_KEY`: AWS secret access key
- `STRIPE_SECRET_KEY`: Stripe secret key
- `SMTP_HOST`: SMTP host
- `SMTP_PORT`: SMTP port
- `SMTP_USERNAME`: SMTP username
- `SMTP_PASSWORD`: SMTP password

### Contributing

Contributions are welcome. Please submit a pull request with your changes.

### License

This project is licensed under the MIT License.

### API Endpoints

#### Authentication

- `POST /api/auth/register` - Register a new user
- `POST /api/auth/login` - Login user
- `GET /api/auth/me` - Get current user

#### Leads

- `POST /api/leads/capture` - Capture new lead
- `GET /api/leads` - Get all leads (admin only)

#### Products

- `GET /api/products` - Get all products
- `GET /api/products/:id` - Get single product
- `POST /api/products` - Create product (admin only)
- `PUT /api/products/:id` - Update product (admin only)npm run server

```

### API Endpoints

#### Authentication

- `POST /api/auth/register` - Register a new user
- `POST /api/auth/login` - Login user
- `GET /api/auth/me` - Get current user

#### Leads

- `POST /api/leads/capture` - Capture new lead
- `GET /api/leads` - Get all leads (admin only)

#### Products

- `GET /api/products` - Get all products
- `GET /api/products/:id` - Get single product
- `POST /api/products` - Create product (admin only)
- `PUT /api/products/:id` - Update product (admin only)
```
