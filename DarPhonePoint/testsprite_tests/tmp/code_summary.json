{"tech_stack": ["Node.js", "Express.js", "React 18", "MongoDB", "Redis", "Vite", "Tailwind CSS", "Material-UI", "JWT", "Passport.js", "Jest", "Vitest", "<PERSON>er", "Swagger", "<PERSON>", "<PERSON>", "Nodemailer", "React Router", "A<PERSON>os", "Mongoose"], "features": [{"name": "Authentication System", "description": "User authentication with JWT, Google OAuth, registration, login, and password management", "files": ["DarPhonePoint-backend/controllers/authController.js", "DarPhonePoint-backend/routes/auth.js", "DarPhonePoint-backend/middleware/auth.js", "DarPhonePoint-backend/models/User.js", "DarPhonePoint-frontend/src/pages/auth/LoginPage.jsx", "DarPhonePoint-frontend/src/pages/auth/RegisterPage.jsx", "DarPhonePoint-frontend/src/contexts/AuthContext.jsx"]}, {"name": "Product Management", "description": "Complete product catalog with CRUD operations, search, filtering, and phone-specific features", "files": ["DarPhonePoint-backend/controllers/productController.js", "DarPhonePoint-backend/routes/products.js", "DarPhonePoint-backend/models/Product.js", "DarPhonePoint-frontend/src/pages/ProductsPage.jsx", "DarPhonePoint-frontend/src/pages/ProductDetailPage.jsx", "DarPhonePoint-frontend/src/components/products/ProductCard.jsx", "DarPhonePoint-frontend/src/components/products/ProductFilters.jsx"]}, {"name": "Shopping Cart", "description": "Shopping cart functionality with guest and authenticated user support, cart persistence", "files": ["DarPhonePoint-backend/controllers/cartController.js", "DarPhonePoint-backend/routes/cart.js", "DarPhonePoint-backend/models/Cart.js", "DarPhonePoint-frontend/src/pages/CartPage.jsx", "DarPhonePoint-frontend/src/components/cart/CartItem.jsx", "DarPhonePoint-frontend/src/contexts/CartContext.jsx"]}, {"name": "Order Management", "description": "Complete order processing system with tracking, status updates, and order history", "files": ["DarPhonePoint-backend/controllers/orderController.js", "DarPhonePoint-backend/routes/orders.js", "DarPhonePoint-backend/models/Order.js", "DarPhonePoint-frontend/src/pages/OrdersPage.jsx", "DarPhonePoint-frontend/src/pages/OrderDetailPage.jsx", "DarPhonePoint-frontend/src/components/orders/OrderCard.jsx"]}, {"name": "Payment Processing", "description": "ClickPesa integration with mobile money support for Tanzania market", "files": ["DarPhonePoint-backend/controllers/paymentController.js", "DarPhonePoint-backend/routes/payments.js", "DarPhonePoint-backend/services/clickpesaService.js", "DarPhonePoint-frontend/src/pages/CheckoutPage.jsx", "DarPhonePoint-frontend/src/components/payment/PaymentForm.jsx"]}, {"name": "Inventory Management", "description": "Real-time inventory tracking with IMEI support, stock alerts, and warehouse management", "files": ["DarPhonePoint-backend/controllers/inventoryController.js", "DarPhonePoint-backend/routes/inventory.js", "DarPhonePoint-backend/models/Inventory.js", "DarPhonePoint-backend/models/SerialNumber.js", "DarPhonePoint-frontend/src/pages/admin/InventoryPage.jsx"]}, {"name": "Admin Dashboard", "description": "Comprehensive admin interface with analytics, user management, and system monitoring", "files": ["DarPhonePoint-backend/controllers/adminController.js", "DarPhonePoint-backend/controllers/analyticsController.js", "DarPhonePoint-backend/routes/admin.js", "DarPhonePoint-frontend/src/pages/admin/AdminDashboard.jsx", "DarPhonePoint-frontend/src/pages/admin/UsersPage.jsx", "DarPhonePoint-frontend/src/components/admin/AdminSidebar.jsx"]}, {"name": "Email System", "description": "Email notifications, campaigns, and tracking system with unsubscribe management", "files": ["DarPhonePoint-backend/controllers/emailController.js", "DarPhonePoint-backend/routes/email.js", "DarPhonePoint-backend/services/emailService.js", "DarPhonePoint-backend/models/EmailTracking.js", "DarPhonePoint-backend/utils/email.js"]}, {"name": "Performance Monitoring", "description": "Real-time performance tracking, health checks, and system monitoring", "files": ["DarPhonePoint-backend/middleware/performanceTrackingMiddleware.js", "DarPhonePoint-backend/services/performanceMonitoringService.js", "DarPhonePoint-backend/controllers/monitoringController.js", "DarPhonePoint-backend/middleware/performanceMiddleware.js"]}, {"name": "API Standardization", "description": "Standardized API responses and error handling across all endpoints", "files": ["DarPhonePoint-backend/utils/ApiResponse.js", "DarPhonePoint-backend/utils/AppError.js", "DarPhonePoint-backend/middleware/errorHandler.js"]}, {"name": "Frontend Error Boundaries", "description": "React error boundaries for robust error handling and user experience", "files": ["DarPhonePoint-frontend/src/components/ErrorBoundary.jsx", "DarPhonePoint-frontend/src/utils/performanceOptimizer.js"]}, {"name": "Database Optimization", "description": "Database indexes, query optimization, and performance enhancements", "files": ["DarPhonePoint-backend/scripts/addCriticalIndexes.js", "DarPhonePoint-backend/services/queryOptimizationService.js"]}]}