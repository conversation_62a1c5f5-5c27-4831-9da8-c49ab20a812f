import asyncio
from playwright import async_api

async def run_test():
    pw = None
    browser = None
    context = None
    
    try:
        # Start a Playwright session in asynchronous mode
        pw = await async_api.async_playwright().start()
        
        # Launch a Chromium browser in headless mode with custom arguments
        browser = await pw.chromium.launch(
            headless=True,
            args=[
                "--window-size=1280,720",         # Set the browser window size
                "--disable-dev-shm-usage",        # Avoid using /dev/shm which can cause issues in containers
                "--ipc=host",                     # Use host-level IPC for better stability
                "--single-process"                # Run the browser in a single process mode
            ],
        )
        
        # Create a new browser context (like an incognito window)
        context = await browser.new_context()
        context.set_default_timeout(5000)
        
        # Open a new page in the browser context
        page = await context.new_page()
        
        # Navigate to your target URL and wait until the network request is committed
        await page.goto("http://localhost:5173", wait_until="commit", timeout=10000)
        
        # Wait for the main page to reach DOMContentLoaded state (optional for stability)
        try:
            await page.wait_for_load_state("domcontentloaded", timeout=3000)
        except async_api.Error:
            pass
        
        # Iterate through all iframes and wait for them to load as well
        for frame in page.frames:
            try:
                await frame.wait_for_load_state("domcontentloaded", timeout=3000)
            except async_api.Error:
                pass
        
        # Interact with the page elements to simulate user flow
        # Check if there is any way to navigate or refresh the page to load the application interface or try to find a login or Google OAuth button.
        await page.mouse.wheel(0, window.innerHeight)
        

        # Try to navigate to the backend or admin URL to check if there is a login page or any other way to initiate Google OAuth login.
        await page.goto('http://localhost:5001', timeout=10000)
        

        # Retry navigating to the frontend URL http://localhost:5173 to check if the application loads correctly now.
        await page.goto('http://localhost:5173', timeout=10000)
        

        # Click on the 'Login' link to initiate the login process and check for Google OAuth login option.
        frame = context.pages[-1]
        elem = frame.locator('xpath=html/body/div/div/header/div/div/nav/a[5]').nth(0)
        await page.wait_for_timeout(3000); await elem.click(timeout=5000)
        

        # Scroll down or explore the page to find the Google OAuth login button or link to initiate Google OAuth login.
        await page.mouse.wheel(0, window.innerHeight)
        

        # Try to scroll up or down further or search for any hidden or alternative login options such as 'Sign in with Google' or 'OAuth' buttons or links on the page.
        await page.mouse.wheel(0, -window.innerHeight)
        

        await page.mouse.wheel(0, window.innerHeight)
        

        # Try to search for 'Google OAuth login' or 'Sign in with Google' on the main site or homepage to find any alternative login entry points or instructions.
        await page.goto('http://localhost:5173', timeout=10000)
        

        # Click on the 'Sign Up' link to check if Google OAuth login option is available during user registration.
        frame = context.pages[-1]
        elem = frame.locator('xpath=html/body/div/div/header/div/div/nav/a[6]').nth(0)
        await page.wait_for_timeout(3000); await elem.click(timeout=5000)
        

        # Click on the 'Login' link (index 8) to return to the login page and try to find any Google OAuth login option or alternative login methods.
        frame = context.pages[-1]
        elem = frame.locator('xpath=html/body/div/div/header/div/div/nav/a[5]').nth(0)
        await page.wait_for_timeout(3000); await elem.click(timeout=5000)
        

        # Assert that the page title is correct
        assert await page.title() == "Phone Point Dar - Tanzania's Premier Mobile Device Retailer"
        
        # Assert that the welcome message is present on the page
        welcome_text = await page.locator('text=Welcome Back! Sign in to access your mobile device account. New to Phone Point Dar? Create your account.').text_content()
        assert welcome_text is not None and 'Welcome Back!' in welcome_text
        
        # Assert that the sign-in fields are present
        assert await page.locator('input[placeholder="Email address"]').count() == 1
        assert await page.locator('input[placeholder="Password"]').count() == 1
        assert await page.locator('text=Remember me').count() == 1
        
        # Assert that the sign-in links are present
        assert await page.locator('text=Forgot password').count() == 1
        assert await page.locator('text=Sign in').count() == 1
        assert await page.locator('text=Create account').count() == 1
        
        # Assert quick access links
        assert await page.locator('a[href="/products"]').count() == 1
        assert await page.locator('a[href="/compare"]').count() == 1
        assert await page.locator('a[href="/"]').count() == 1
        
        # Assert categories links
        assert await page.locator('a[href="/products?category=smartphone"]').count() == 1
        assert await page.locator('a[href="/products?category=tablet"]').count() == 1
        assert await page.locator('a[href="/products?category=accessory"]').count() == 1
        assert await page.locator('a[href="/products?category=audio"]').count() == 1
        assert await page.locator('a[href="/compare"]').count() == 1
        
        # Assert services links
        assert await page.locator('a[href="/wishlist"]').count() == 1
        assert await page.locator('text=Device Repair').count() == 1
        assert await page.locator('text=Trade-In Program').count() == 1
        assert await page.locator('text=Extended Warranty').count() == 1
        assert await page.locator('text=Help & FAQ').count() == 1
        
        # Assert contact info is visible
        phone_numbers = await page.locator('text=+255 123 456 789').count() + await page.locator('text=+255 987 654 321').count()
        assert phone_numbers == 2
        assert await page.locator('text=<EMAIL>').count() == 1
        assert await page.locator('text=Kariakoo Market, Dar es Salaam').count() == 1
        
        # Assert payment methods are displayed
        assert await page.locator('text=M-Pesa').count() == 1
        assert await page.locator('text=Tigo Pesa').count() == 1
        assert await page.locator('text=Airtel Money').count() == 1
        assert await page.locator('text=Cash').count() == 1
        
        # Assert footer content
        assert await page.locator('text=© 2025 Phone Point Dar. All rights reserved.').count() == 1
        assert await page.locator('text=Proudly serving Tanzania • Licensed Mobile Device Retailer').count() == 1
        assert await page.locator('text=Privacy Policy').count() == 1
        assert await page.locator('text=Return Policy').count() == 1
        assert await page.locator('text=Warranty Terms').count() == 1
        assert await page.locator('text=Shipping Info').count() == 1
        await asyncio.sleep(5)
    
    finally:
        if context:
            await context.close()
        if browser:
            await browser.close()
        if pw:
            await pw.stop()
            
asyncio.run(run_test())
    