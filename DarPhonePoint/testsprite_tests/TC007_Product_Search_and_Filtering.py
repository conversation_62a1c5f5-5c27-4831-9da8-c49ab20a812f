import asyncio
from playwright import async_api

async def run_test():
    pw = None
    browser = None
    context = None
    
    try:
        # Start a Playwright session in asynchronous mode
        pw = await async_api.async_playwright().start()
        
        # Launch a Chromium browser in headless mode with custom arguments
        browser = await pw.chromium.launch(
            headless=True,
            args=[
                "--window-size=1280,720",         # Set the browser window size
                "--disable-dev-shm-usage",        # Avoid using /dev/shm which can cause issues in containers
                "--ipc=host",                     # Use host-level IPC for better stability
                "--single-process"                # Run the browser in a single process mode
            ],
        )
        
        # Create a new browser context (like an incognito window)
        context = await browser.new_context()
        context.set_default_timeout(5000)
        
        # Open a new page in the browser context
        page = await context.new_page()
        
        # Navigate to your target URL and wait until the network request is committed
        await page.goto("http://localhost:5173", wait_until="commit", timeout=10000)
        
        # Wait for the main page to reach DOMContentLoaded state (optional for stability)
        try:
            await page.wait_for_load_state("domcontentloaded", timeout=3000)
        except async_api.Error:
            pass
        
        # Iterate through all iframes and wait for them to load as well
        for frame in page.frames:
            try:
                await frame.wait_for_load_state("domcontentloaded", timeout=3000)
            except async_api.Error:
                pass
        
        # Interact with the page elements to simulate user flow
        # Find and navigate to the product browsing or search page to start testing search and filters.
        await page.mouse.wheel(0, window.innerHeight)
        

        # Enter a search keyword in the search input and submit to test product search functionality.
        frame = context.pages[-1]
        elem = frame.locator('xpath=html/body/div/div/header/div/div/div/div/div/input').nth(0)
        await page.wait_for_timeout(3000); await elem.fill('phone')
        

        frame = context.pages[-1]
        elem = frame.locator('xpath=html/body/div/div/header/div/div/div/div/div/input').nth(0)
        await page.wait_for_timeout(3000); await elem.click(timeout=5000)
        

        # Set a minimum and maximum price range using the price range input fields and verify the product list updates accordingly.
        frame = context.pages[-1]
        elem = frame.locator('xpath=html/body/div/div/main/div/div[2]/div/div/div/div[4]/div/input').nth(0)
        await page.wait_for_timeout(3000); await elem.fill('500000')
        

        frame = context.pages[-1]
        elem = frame.locator('xpath=html/body/div/div/main/div/div[2]/div/div/div/div[4]/div/input[2]').nth(0)
        await page.wait_for_timeout(3000); await elem.fill('2000000')
        

        # Assert that the product list contains only products matching the search keyword 'phone' and the price range 500,000 to 2,000,000 TZS
        products = await page.locator('div.product-item').all_text_contents()
        assert any('phone' in product.lower() for product in products), 'No products matching search keyword found'
        # Extract prices and assert they fall within the specified range
        import re
        prices = []
        for product in products:
            price_match = re.search(r'\d{1,3}(,\d{3})*( - \d{1,3}(,\d{3})*)?', product)
            if price_match:
                price_str = price_match.group(0).replace(',', '').replace(' - ', '-')
                if '-' in price_str:
                    low, high = map(int, price_str.split('-'))
                    prices.append((low, high))
                else:
                    prices.append((int(price_str), int(price_str)))
        for low, high in prices:
            assert low >= 500000 and high <= 2000000, f'Product price {low}-{high} out of filter range'
        # Verify pagination metadata if present
        pagination_info = await page.locator('div.pagination-info').text_content()
        if pagination_info:
            import json
            try:
                pagination_data = json.loads(pagination_info)
                assert 'current_page' in pagination_data and 'total_pages' in pagination_data, 'Pagination metadata missing keys'
                assert pagination_data['current_page'] <= pagination_data['total_pages'], 'Current page exceeds total pages'
            except Exception as e:
                assert False, f'Pagination metadata invalid: {e}'
        await asyncio.sleep(5)
    
    finally:
        if context:
            await context.close()
        if browser:
            await browser.close()
        if pw:
            await pw.stop()
            
asyncio.run(run_test())
    