import asyncio
from playwright import async_api

async def run_test():
    pw = None
    browser = None
    context = None
    
    try:
        # Start a Playwright session in asynchronous mode
        pw = await async_api.async_playwright().start()
        
        # Launch a Chromium browser in headless mode with custom arguments
        browser = await pw.chromium.launch(
            headless=True,
            args=[
                "--window-size=1280,720",         # Set the browser window size
                "--disable-dev-shm-usage",        # Avoid using /dev/shm which can cause issues in containers
                "--ipc=host",                     # Use host-level IPC for better stability
                "--single-process"                # Run the browser in a single process mode
            ],
        )
        
        # Create a new browser context (like an incognito window)
        context = await browser.new_context()
        context.set_default_timeout(5000)
        
        # Open a new page in the browser context
        page = await context.new_page()
        
        # Navigate to your target URL and wait until the network request is committed
        await page.goto("http://localhost:5173", wait_until="commit", timeout=10000)
        
        # Wait for the main page to reach DOMContentLoaded state (optional for stability)
        try:
            await page.wait_for_load_state("domcontentloaded", timeout=3000)
        except async_api.Error:
            pass
        
        # Iterate through all iframes and wait for them to load as well
        for frame in page.frames:
            try:
                await frame.wait_for_load_state("domcontentloaded", timeout=3000)
            except async_api.Error:
                pass
        
        # Interact with the page elements to simulate user flow
        # Try to reload the page or open a different tab to find the product list or homepage.
        await page.goto('http://localhost:5173', timeout=10000)
        

        # Try to open the backend URL or check if there is any other URL or tab to access the Phone Point Dar e-commerce app.
        await page.goto('http://localhost:5001', timeout=10000)
        

        # Try to access known API endpoints such as /api/cart or /api/products directly to check backend availability and functionality.
        await page.goto('http://localhost:5001/api/products', timeout=10000)
        

        # Try to reload the frontend URL http://localhost:5173 to attempt recovery and access the e-commerce app UI.
        await page.goto('http://localhost:5173', timeout=10000)
        

        # Click on 'Products' link to navigate to the products listing page and retrieve product IDs.
        frame = context.pages[-1]
        elem = frame.locator('xpath=html/body/div/div/header/div/div/nav/a[2]').nth(0)
        await page.wait_for_timeout(3000); await elem.click(timeout=5000)
        

        # Click 'View Details' button for 'AirPods Pro 2nd Gen' to get product ID and add to cart.
        frame = context.pages[-1]
        elem = frame.locator('xpath=html/body/div/div/main/div/div[2]/div/div[2]/div[2]/div/div[2]/div[5]/a/button').nth(0)
        await page.wait_for_timeout(3000); await elem.click(timeout=5000)
        

        # Click 'Add to Cart' button to add 1 quantity of 'AirPods Pro 2nd Gen' to the cart as guest user.
        frame = context.pages[-1]
        elem = frame.locator('xpath=html/body/div/div/main/div/div[2]/div/div[2]/div/div[3]/div/div[2]/button').nth(0)
        await page.wait_for_timeout(3000); await elem.click(timeout=5000)
        

        # Click on the cart icon or cart link to manually verify if the item was added to the cart.
        frame = context.pages[-1]
        elem = frame.locator('xpath=html/body/div/div/header/div/div/nav/a[4]').nth(0)
        await page.wait_for_timeout(3000); await elem.click(timeout=5000)
        

        # Assert the cart contains the added item with correct quantity and details
        cart_items = await page.locator('xpath=//div[contains(@class, "shopping_cart")]//div[contains(@class, "items")]//div[contains(@class, "item")]').all_text_contents()
        assert any('AirPods Pro 2nd Gen' in item for item in cart_items), "Cart does not contain 'AirPods Pro 2nd Gen'"
        assert any('Quantity: 1' in item or '1' in item for item in cart_items), "Cart item quantity is not 1"
        # Assert order summary totals are correct
        order_summary_text = await page.locator('xpath=//div[contains(@class, "order_summary")]').inner_text()
        assert 'TSh 450,000' in order_summary_text, "Subtotal is incorrect"
        assert 'TSh 531,000' in order_summary_text, "Total is incorrect"
        # Reload or revisit cart page to check persistence
        await page.goto('http://localhost:5173/cart')
        await page.wait_for_timeout(2000)
        # Re-assert cart contents after reload
        cart_items_after_reload = await page.locator('xpath=//div[contains(@class, "shopping_cart")]//div[contains(@class, "items")]//div[contains(@class, "item")]').all_text_contents()
        assert cart_items == cart_items_after_reload, "Cart contents changed after reload"
        await asyncio.sleep(5)
    
    finally:
        if context:
            await context.close()
        if browser:
            await browser.close()
        if pw:
            await pw.stop()
            
asyncio.run(run_test())
    