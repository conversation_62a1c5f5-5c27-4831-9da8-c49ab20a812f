#!/bin/bash

# Phone Point Dar Security Testing Script
# Performs automated security tests and vulnerability scanning

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
TARGET_URL="${1:-http://localhost:5000}"
REPORT_DIR="security-reports/$(date +%Y%m%d_%H%M%S)"
LOG_FILE="$REPORT_DIR/security-test.log"

# Functions
log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$LOG_FILE"
}

# Create report directory
mkdir -p "$REPORT_DIR"

# Check prerequisites
check_prerequisites() {
    log "Checking security testing prerequisites..."
    
    # Check if curl is installed
    if ! command -v curl &> /dev/null; then
        error "curl is not installed. Please install curl first."
        exit 1
    fi
    
    # Check if nmap is installed
    if ! command -v nmap &> /dev/null; then
        warning "nmap is not installed. Port scanning will be skipped."
    fi
    
    # Check if nikto is installed
    if ! command -v nikto &> /dev/null; then
        warning "nikto is not installed. Web vulnerability scanning will be skipped."
    fi
    
    # Check if sqlmap is installed
    if ! command -v sqlmap &> /dev/null; then
        warning "sqlmap is not installed. SQL injection testing will be skipped."
    fi
    
    success "Prerequisites check completed"
}

# Test SSL/TLS configuration
test_ssl_tls() {
    log "Testing SSL/TLS configuration..."
    
    if [[ "$TARGET_URL" == https* ]]; then
        # Test SSL certificate
        log "Checking SSL certificate..."
        openssl s_client -connect "${TARGET_URL#https://}:443" -servername "${TARGET_URL#https://}" < /dev/null 2>/dev/null | openssl x509 -noout -text > "$REPORT_DIR/ssl-certificate.txt"
        
        # Test SSL configuration with testssl.sh if available
        if command -v testssl.sh &> /dev/null; then
            log "Running comprehensive SSL/TLS test..."
            testssl.sh --quiet --jsonfile "$REPORT_DIR/ssl-test.json" "$TARGET_URL" > "$REPORT_DIR/ssl-test.txt"
        else
            warning "testssl.sh not found. Install it for comprehensive SSL testing."
        fi
        
        success "SSL/TLS testing completed"
    else
        warning "Target URL is not HTTPS. SSL/TLS testing skipped."
    fi
}

# Test HTTP security headers
test_security_headers() {
    log "Testing HTTP security headers..."
    
    curl -I -s "$TARGET_URL" > "$REPORT_DIR/headers.txt"
    
    # Check for important security headers
    local headers_file="$REPORT_DIR/headers.txt"
    local missing_headers=()
    
    if ! grep -qi "x-frame-options" "$headers_file"; then
        missing_headers+=("X-Frame-Options")
    fi
    
    if ! grep -qi "x-content-type-options" "$headers_file"; then
        missing_headers+=("X-Content-Type-Options")
    fi
    
    if ! grep -qi "x-xss-protection" "$headers_file"; then
        missing_headers+=("X-XSS-Protection")
    fi
    
    if ! grep -qi "strict-transport-security" "$headers_file"; then
        missing_headers+=("Strict-Transport-Security")
    fi
    
    if ! grep -qi "content-security-policy" "$headers_file"; then
        missing_headers+=("Content-Security-Policy")
    fi
    
    if [[ ${#missing_headers[@]} -eq 0 ]]; then
        success "All important security headers are present"
    else
        warning "Missing security headers: ${missing_headers[*]}"
    fi
    
    # Generate security headers report
    cat > "$REPORT_DIR/security-headers-report.txt" << EOF
Security Headers Analysis for $TARGET_URL
==========================================

Headers found:
$(cat "$headers_file")

Missing headers:
$(printf '%s\n' "${missing_headers[@]}")

Recommendations:
- Add missing security headers to improve security posture
- Review Content Security Policy for XSS protection
- Ensure HSTS is enabled for HTTPS sites
EOF
    
    success "Security headers testing completed"
}

# Test authentication and authorization
test_auth() {
    log "Testing authentication and authorization..."
    
    # Test login endpoint
    log "Testing login endpoint..."
    
    # Test with invalid credentials
    local invalid_login_response=$(curl -s -w "%{http_code}" -o /dev/null \
        -X POST "$TARGET_URL/api/auth/login" \
        -H "Content-Type: application/json" \
        -d '{"email":"<EMAIL>","password":"wrongpassword"}')
    
    if [[ "$invalid_login_response" == "401" ]]; then
        success "Login correctly rejects invalid credentials"
    else
        warning "Login endpoint response unexpected for invalid credentials: $invalid_login_response"
    fi
    
    # Test SQL injection in login
    log "Testing SQL injection in login..."
    local sqli_response=$(curl -s -w "%{http_code}" -o /dev/null \
        -X POST "$TARGET_URL/api/auth/login" \
        -H "Content-Type: application/json" \
        -d '{"email":"admin'\''OR 1=1--","password":"anything"}')
    
    if [[ "$sqli_response" == "401" ]] || [[ "$sqli_response" == "400" ]]; then
        success "Login endpoint appears protected against SQL injection"
    else
        error "Potential SQL injection vulnerability in login endpoint"
    fi
    
    # Test admin endpoints without authentication
    log "Testing admin endpoints without authentication..."
    local admin_response=$(curl -s -w "%{http_code}" -o /dev/null "$TARGET_URL/api/admin/dashboard")
    
    if [[ "$admin_response" == "401" ]] || [[ "$admin_response" == "403" ]]; then
        success "Admin endpoints properly protected"
    else
        error "Admin endpoints may be accessible without authentication"
    fi
    
    success "Authentication testing completed"
}

# Test input validation
test_input_validation() {
    log "Testing input validation..."
    
    # Test XSS in product search
    log "Testing XSS in product search..."
    local xss_payload="<script>alert('xss')</script>"
    local xss_response=$(curl -s "$TARGET_URL/api/products?search=$xss_payload")
    
    if echo "$xss_response" | grep -q "<script>"; then
        error "Potential XSS vulnerability in product search"
    else
        success "Product search appears protected against XSS"
    fi
    
    # Test large payload
    log "Testing large payload handling..."
    local large_payload=$(python3 -c "print('A' * 10000)")
    local large_response=$(curl -s -w "%{http_code}" -o /dev/null \
        -X POST "$TARGET_URL/api/auth/register" \
        -H "Content-Type: application/json" \
        -d "{\"name\":\"$large_payload\",\"email\":\"<EMAIL>\",\"password\":\"password123\"}")
    
    if [[ "$large_response" == "400" ]] || [[ "$large_response" == "413" ]]; then
        success "Application properly handles large payloads"
    else
        warning "Application may not properly limit payload size"
    fi
    
    success "Input validation testing completed"
}

# Test rate limiting
test_rate_limiting() {
    log "Testing rate limiting..."
    
    # Test login rate limiting
    log "Testing login rate limiting..."
    local rate_limit_count=0
    
    for i in {1..20}; do
        local response=$(curl -s -w "%{http_code}" -o /dev/null \
            -X POST "$TARGET_URL/api/auth/login" \
            -H "Content-Type: application/json" \
            -d '{"email":"<EMAIL>","password":"wrongpassword"}')
        
        if [[ "$response" == "429" ]]; then
            rate_limit_count=$((rate_limit_count + 1))
        fi
        
        sleep 0.1
    done
    
    if [[ $rate_limit_count -gt 0 ]]; then
        success "Rate limiting is active (triggered $rate_limit_count times)"
    else
        warning "Rate limiting may not be properly configured"
    fi
    
    success "Rate limiting testing completed"
}

# Test for common vulnerabilities
test_common_vulnerabilities() {
    log "Testing for common vulnerabilities..."
    
    # Test for directory traversal
    log "Testing directory traversal..."
    local traversal_response=$(curl -s -w "%{http_code}" -o /dev/null "$TARGET_URL/api/../../../etc/passwd")
    
    if [[ "$traversal_response" == "404" ]] || [[ "$traversal_response" == "403" ]]; then
        success "Application appears protected against directory traversal"
    else
        warning "Potential directory traversal vulnerability"
    fi
    
    # Test for information disclosure
    log "Testing information disclosure..."
    local info_response=$(curl -s "$TARGET_URL/api/nonexistent")
    
    if echo "$info_response" | grep -qi "stack trace\|error.*line\|debug"; then
        warning "Application may be disclosing sensitive information in error messages"
    else
        success "Error messages appear to be properly sanitized"
    fi
    
    success "Common vulnerability testing completed"
}

# Run port scan if nmap is available
run_port_scan() {
    if command -v nmap &> /dev/null; then
        log "Running port scan..."
        
        local target_host=$(echo "$TARGET_URL" | sed 's|http[s]*://||' | cut -d':' -f1)
        nmap -sS -O "$target_host" > "$REPORT_DIR/port-scan.txt" 2>&1
        
        success "Port scan completed"
    else
        warning "nmap not available, skipping port scan"
    fi
}

# Run web vulnerability scan if nikto is available
run_web_vuln_scan() {
    if command -v nikto &> /dev/null; then
        log "Running web vulnerability scan..."
        
        nikto -h "$TARGET_URL" -output "$REPORT_DIR/nikto-scan.txt" -Format txt
        
        success "Web vulnerability scan completed"
    else
        warning "nikto not available, skipping web vulnerability scan"
    fi
}

# Generate security report
generate_report() {
    log "Generating security report..."
    
    cat > "$REPORT_DIR/security-summary.md" << EOF
# Phone Point Dar Security Test Report

**Target:** $TARGET_URL  
**Date:** $(date)  
**Report Directory:** $REPORT_DIR

## Test Summary

### Tests Performed
- [x] SSL/TLS Configuration
- [x] HTTP Security Headers
- [x] Authentication & Authorization
- [x] Input Validation
- [x] Rate Limiting
- [x] Common Vulnerabilities
$(command -v nmap &> /dev/null && echo "- [x] Port Scan" || echo "- [ ] Port Scan (nmap not available)")
$(command -v nikto &> /dev/null && echo "- [x] Web Vulnerability Scan" || echo "- [ ] Web Vulnerability Scan (nikto not available)")

### Files Generated
- \`security-test.log\` - Detailed test log
- \`headers.txt\` - HTTP headers analysis
- \`security-headers-report.txt\` - Security headers detailed report
$(command -v nmap &> /dev/null && echo "- \`port-scan.txt\` - Port scan results")
$(command -v nikto &> /dev/null && echo "- \`nikto-scan.txt\` - Web vulnerability scan results")
$([[ "$TARGET_URL" == https* ]] && echo "- \`ssl-certificate.txt\` - SSL certificate details")

### Recommendations

1. **Review all WARNING messages** in the test log
2. **Implement missing security headers** if any were identified
3. **Review rate limiting configuration** to ensure it's properly tuned
4. **Regular security testing** should be performed as part of CI/CD pipeline
5. **Keep dependencies updated** to avoid known vulnerabilities
6. **Implement Web Application Firewall (WAF)** for additional protection

### Next Steps

1. Address any identified vulnerabilities
2. Implement additional security measures as needed
3. Schedule regular security assessments
4. Consider professional penetration testing for production systems

---

*This report was generated automatically. Manual review and additional testing may be required for comprehensive security assessment.*
EOF
    
    success "Security report generated: $REPORT_DIR/security-summary.md"
}

# Main function
main() {
    log "Starting security testing for Phone Point Dar..."
    log "Target URL: $TARGET_URL"
    log "Report directory: $REPORT_DIR"
    
    check_prerequisites
    test_ssl_tls
    test_security_headers
    test_auth
    test_input_validation
    test_rate_limiting
    test_common_vulnerabilities
    run_port_scan
    run_web_vuln_scan
    generate_report
    
    success "🔒 Security testing completed!"
    log "Review the generated reports in: $REPORT_DIR"
    log "Summary report: $REPORT_DIR/security-summary.md"
}

# Run main function
main
