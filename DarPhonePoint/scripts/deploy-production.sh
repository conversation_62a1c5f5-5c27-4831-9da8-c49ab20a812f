#!/bin/bash

# Phone Point Dar Production Deployment Script
# This script deploys the Phone Point Dar application to production

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_NAME="phonepointdar"
BACKUP_DIR="/backups/$(date +%Y%m%d_%H%M%S)"
LOG_FILE="/var/log/phonepointdar-deploy.log"

# Functions
log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
    exit 1
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$LOG_FILE"
}

# Check if running as root
check_root() {
    if [[ $EUID -eq 0 ]]; then
        error "This script should not be run as root for security reasons"
    fi
}

# Check prerequisites
check_prerequisites() {
    log "Checking prerequisites..."
    
    # Check if Docker is installed
    if ! command -v docker &> /dev/null; then
        error "Docker is not installed. Please install Docker first."
    fi
    
    # Check if Docker Compose is installed
    if ! command -v docker-compose &> /dev/null; then
        error "Docker Compose is not installed. Please install Docker Compose first."
    fi
    
    # Check if Git is installed
    if ! command -v git &> /dev/null; then
        error "Git is not installed. Please install Git first."
    fi
    
    # Check if .env.production exists
    if [[ ! -f "DarPhonePoint-backend/.env.production" ]]; then
        error "Production environment file not found. Please create DarPhonePoint-backend/.env.production"
    fi
    
    success "Prerequisites check passed"
}

# Create backup
create_backup() {
    log "Creating backup..."
    
    # Create backup directory
    mkdir -p "$BACKUP_DIR"
    
    # Backup database
    if docker ps | grep -q "${PROJECT_NAME}-mongodb"; then
        log "Backing up MongoDB..."
        docker exec "${PROJECT_NAME}-mongodb" mongodump --out /backup/mongodb
        docker cp "${PROJECT_NAME}-mongodb:/backup/mongodb" "$BACKUP_DIR/"
    fi
    
    # Backup uploads
    if [[ -d "uploads" ]]; then
        log "Backing up uploads..."
        cp -r uploads "$BACKUP_DIR/"
    fi
    
    # Backup current environment
    if [[ -f "DarPhonePoint-backend/.env.production" ]]; then
        cp "DarPhonePoint-backend/.env.production" "$BACKUP_DIR/"
    fi
    
    success "Backup created at $BACKUP_DIR"
}

# Pull latest code
pull_latest_code() {
    log "Pulling latest code from repository..."
    
    # Stash any local changes
    git stash
    
    # Pull latest changes
    git pull origin main
    
    success "Latest code pulled successfully"
}

# Build and deploy
build_and_deploy() {
    log "Building and deploying application..."
    
    # Stop existing containers
    log "Stopping existing containers..."
    docker-compose -f docker-compose.production.yml down
    
    # Remove old images (optional, saves space)
    log "Cleaning up old images..."
    docker image prune -f
    
    # Build new images
    log "Building new images..."
    docker-compose -f docker-compose.production.yml build --no-cache
    
    # Start services
    log "Starting services..."
    docker-compose -f docker-compose.production.yml up -d
    
    success "Application deployed successfully"
}

# Health check
health_check() {
    log "Performing health checks..."
    
    # Wait for services to start
    sleep 30
    
    # Check backend health
    if curl -f http://localhost:5000/api/health > /dev/null 2>&1; then
        success "Backend health check passed"
    else
        error "Backend health check failed"
    fi
    
    # Check frontend health
    if curl -f http://localhost/health > /dev/null 2>&1; then
        success "Frontend health check passed"
    else
        error "Frontend health check failed"
    fi
    
    # Check database connection
    if docker exec "${PROJECT_NAME}-mongodb" mongosh --eval "db.runCommand({ping: 1})" > /dev/null 2>&1; then
        success "Database health check passed"
    else
        error "Database health check failed"
    fi
    
    success "All health checks passed"
}

# Run database migrations/optimizations
run_migrations() {
    log "Running database migrations and optimizations..."
    
    # Wait for database to be ready
    sleep 10
    
    # Run database optimization
    docker exec "${PROJECT_NAME}-backend" npm run optimize:db
    
    success "Database migrations completed"
}

# Setup SSL certificates (if needed)
setup_ssl() {
    log "Checking SSL certificates..."
    
    if [[ ! -f "ssl/phonepointdar.com.crt" ]] || [[ ! -f "ssl/phonepointdar.com.key" ]]; then
        warning "SSL certificates not found. Please set up SSL certificates for production."
        warning "You can use Let's Encrypt or purchase certificates from a CA."
        warning "Place certificates in ssl/phonepointdar.com.crt and ssl/phonepointdar.com.key"
    else
        success "SSL certificates found"
    fi
}

# Setup monitoring
setup_monitoring() {
    log "Setting up monitoring..."
    
    # Start monitoring services
    docker-compose -f docker-compose.production.yml --profile monitoring up -d
    
    success "Monitoring services started"
}

# Cleanup old backups (keep last 7 days)
cleanup_old_backups() {
    log "Cleaning up old backups..."
    
    find /backups -type d -mtime +7 -name "*_*" -exec rm -rf {} \; 2>/dev/null || true
    
    success "Old backups cleaned up"
}

# Send deployment notification
send_notification() {
    log "Sending deployment notification..."
    
    # Send Slack notification if webhook is configured
    if [[ -n "${SLACK_WEBHOOK_URL}" ]]; then
        curl -X POST -H 'Content-type: application/json' \
            --data '{"text":"🚀 Phone Point Dar production deployment completed successfully!"}' \
            "${SLACK_WEBHOOK_URL}" || warning "Failed to send Slack notification"
    fi
    
    success "Deployment notification sent"
}

# Main deployment function
main() {
    log "Starting Phone Point Dar production deployment..."
    
    # Change to project directory
    cd "$(dirname "$0")/.."
    
    # Run deployment steps
    check_root
    check_prerequisites
    create_backup
    pull_latest_code
    build_and_deploy
    run_migrations
    health_check
    setup_ssl
    setup_monitoring
    cleanup_old_backups
    send_notification
    
    success "🎉 Phone Point Dar production deployment completed successfully!"
    log "Application is now running at:"
    log "  - Frontend: http://localhost (or your domain)"
    log "  - Backend API: http://localhost:5000"
    log "  - Monitoring: http://localhost:3000 (Grafana)"
    log ""
    log "Backup created at: $BACKUP_DIR"
    log "Deployment log: $LOG_FILE"
}

# Rollback function
rollback() {
    log "Rolling back to previous version..."
    
    # Stop current containers
    docker-compose -f docker-compose.production.yml down
    
    # Restore from latest backup
    LATEST_BACKUP=$(ls -t /backups | head -n1)
    if [[ -n "$LATEST_BACKUP" ]]; then
        log "Restoring from backup: $LATEST_BACKUP"
        
        # Restore database
        if [[ -d "/backups/$LATEST_BACKUP/mongodb" ]]; then
            docker exec "${PROJECT_NAME}-mongodb" mongorestore /backup/mongodb
        fi
        
        # Restore uploads
        if [[ -d "/backups/$LATEST_BACKUP/uploads" ]]; then
            cp -r "/backups/$LATEST_BACKUP/uploads" .
        fi
        
        success "Rollback completed"
    else
        error "No backup found for rollback"
    fi
}

# Handle script arguments
case "${1:-deploy}" in
    "deploy")
        main
        ;;
    "rollback")
        rollback
        ;;
    "health")
        health_check
        ;;
    *)
        echo "Usage: $0 {deploy|rollback|health}"
        echo "  deploy  - Deploy the application (default)"
        echo "  rollback - Rollback to previous version"
        echo "  health  - Run health checks"
        exit 1
        ;;
esac
