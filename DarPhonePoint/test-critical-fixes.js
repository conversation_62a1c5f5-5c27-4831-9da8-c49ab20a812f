#!/usr/bin/env node

/**
 * Critical Fixes Test Script for Phone Point Dar
 * Tests all the critical fixes we've implemented
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');

const BASE_URL = 'http://localhost:5001/api';
const FRONTEND_URL = 'http://localhost:5173';

// Test configuration
const TEST_CONFIG = {
  admin: {
    email: '<EMAIL>',
    password: 'Admin123!'
  },
  timeout: 10000
};

let authToken = null;

// Utility functions
const log = (message, type = 'info') => {
  const timestamp = new Date().toISOString();
  const colors = {
    info: '\x1b[36m',    // Cyan
    success: '\x1b[32m', // Green
    error: '\x1b[31m',   // Red
    warning: '\x1b[33m', // Yellow
    reset: '\x1b[0m'     // Reset
  };
  
  console.log(`${colors[type]}[${timestamp}] ${message}${colors.reset}`);
};

const sleep = (ms) => new Promise(resolve => setTimeout(resolve, ms));

// Test functions
async function testBackendHealth() {
  log('🏥 Testing backend health...', 'info');
  
  try {
    const response = await axios.get(`${BASE_URL}/health`, {
      timeout: TEST_CONFIG.timeout
    });
    
    if (response.status === 200) {
      log('✅ Backend is healthy', 'success');
      log(`   Database: ${response.data.database}`, 'info');
      log(`   Environment: ${response.data.environment}`, 'info');
      return true;
    }
  } catch (error) {
    log(`❌ Backend health check failed: ${error.message}`, 'error');
    return false;
  }
}

async function testAuthentication() {
  log('🔐 Testing admin authentication...', 'info');
  
  try {
    const response = await axios.post(`${BASE_URL}/auth/login`, {
      email: TEST_CONFIG.admin.email,
      password: TEST_CONFIG.admin.password
    }, {
      timeout: TEST_CONFIG.timeout
    });
    
    if (response.status === 200 && response.data.token) {
      authToken = response.data.token;
      log('✅ Admin authentication successful', 'success');
      log(`   Token: ${authToken.substring(0, 20)}...`, 'info');
      return true;
    }
  } catch (error) {
    log(`❌ Authentication failed: ${error.response?.data?.message || error.message}`, 'error');
    return false;
  }
}

async function testAdminRoutes() {
  log('🛡️ Testing admin route access...', 'info');
  
  if (!authToken) {
    log('❌ No auth token available', 'error');
    return false;
  }
  
  const headers = {
    'Authorization': `Bearer ${authToken}`,
    'Content-Type': 'application/json'
  };
  
  const routes = [
    { path: '/admin/products', name: 'Products List' },
    { path: '/admin/orders', name: 'Orders List' },
    { path: '/admin/customers', name: 'Customers List' },
    { path: '/admin/dashboard/overview', name: 'Dashboard Overview' }
  ];
  
  let successCount = 0;
  
  for (const route of routes) {
    try {
      const response = await axios.get(`${BASE_URL}${route.path}`, {
        headers,
        timeout: TEST_CONFIG.timeout
      });
      
      if (response.status === 200) {
        log(`✅ ${route.name}: Accessible`, 'success');
        successCount++;
      }
    } catch (error) {
      log(`❌ ${route.name}: ${error.response?.status || error.message}`, 'error');
    }
  }
  
  return successCount === routes.length;
}

async function testProductAPI() {
  log('📱 Testing product management API...', 'info');
  
  if (!authToken) {
    log('❌ No auth token available', 'error');
    return false;
  }
  
  const headers = {
    'Authorization': `Bearer ${authToken}`,
    'Content-Type': 'application/json'
  };
  
  try {
    // Test product list
    const listResponse = await axios.get(`${BASE_URL}/admin/products?limit=5`, {
      headers,
      timeout: TEST_CONFIG.timeout
    });
    
    if (listResponse.status === 200) {
      log('✅ Product list API working', 'success');
      log(`   Found ${listResponse.data.data?.products?.length || 0} products`, 'info');
    }
    
    // Test product filters
    const filtersResponse = await axios.get(`${BASE_URL}/admin/products/filters`, {
      headers,
      timeout: TEST_CONFIG.timeout
    });
    
    if (filtersResponse.status === 200) {
      log('✅ Product filters API working', 'success');
      log(`   Categories: ${filtersResponse.data.data?.categories?.length || 0}`, 'info');
      log(`   Brands: ${filtersResponse.data.data?.brands?.length || 0}`, 'info');
    }
    
    return true;
  } catch (error) {
    log(`❌ Product API test failed: ${error.response?.data?.message || error.message}`, 'error');
    return false;
  }
}

async function testOrderAPI() {
  log('📦 Testing order management API...', 'info');
  
  if (!authToken) {
    log('❌ No auth token available', 'error');
    return false;
  }
  
  const headers = {
    'Authorization': `Bearer ${authToken}`,
    'Content-Type': 'application/json'
  };
  
  try {
    // Test order list
    const listResponse = await axios.get(`${BASE_URL}/admin/orders?limit=5`, {
      headers,
      timeout: TEST_CONFIG.timeout
    });
    
    if (listResponse.status === 200) {
      log('✅ Order list API working', 'success');
      log(`   Found ${listResponse.data.data?.orders?.length || 0} orders`, 'info');
    }
    
    // Test order stats
    const statsResponse = await axios.get(`${BASE_URL}/admin/orders/stats`, {
      headers,
      timeout: TEST_CONFIG.timeout
    });
    
    if (statsResponse.status === 200) {
      log('✅ Order stats API working', 'success');
      log(`   Total orders: ${statsResponse.data.data?.totalOrders || 0}`, 'info');
    }
    
    return true;
  } catch (error) {
    log(`❌ Order API test failed: ${error.response?.data?.message || error.message}`, 'error');
    return false;
  }
}

async function testFrontendFiles() {
  log('🎨 Testing frontend file structure...', 'info');
  
  const requiredFiles = [
    'DarPhonePoint/DarPhonePoint-frontend/src/pages/admin/ProductManagementPage.jsx',
    'DarPhonePoint/DarPhonePoint-frontend/src/pages/admin/OrderManagementPage.jsx',
    'DarPhonePoint/DarPhonePoint-frontend/src/services/adminProductService.js',
    'DarPhonePoint/DarPhonePoint-frontend/src/services/adminOrderService.js',
    'DarPhonePoint/DarPhonePoint-frontend/src/components/ui/Modal.jsx',
    'DarPhonePoint/DarPhonePoint-frontend/src/components/ui/ErrorState.jsx'
  ];
  
  let successCount = 0;
  
  for (const file of requiredFiles) {
    if (fs.existsSync(file)) {
      log(`✅ ${path.basename(file)}: Exists`, 'success');
      successCount++;
    } else {
      log(`❌ ${path.basename(file)}: Missing`, 'error');
    }
  }
  
  return successCount === requiredFiles.length;
}

async function testRouteConflicts() {
  log('🔀 Testing for route conflicts...', 'info');
  
  const serverFile = 'DarPhonePoint/DarPhonePoint-backend/server-dev.js';
  
  if (!fs.existsSync(serverFile)) {
    log('❌ Server file not found', 'error');
    return false;
  }
  
  const serverContent = fs.readFileSync(serverFile, 'utf8');
  
  // Check for duplicate admin route registrations
  const adminRouteMatches = serverContent.match(/app\.use\(['"`]\/api\/admin['"`]/g);
  
  if (adminRouteMatches && adminRouteMatches.length <= 2) {
    log('✅ No duplicate admin route registrations detected', 'success');
    return true;
  } else {
    log(`❌ Found ${adminRouteMatches?.length || 0} admin route registrations (expected ≤ 2)`, 'error');
    return false;
  }
}

// Main test runner
async function runTests() {
  log('🚀 Starting Phone Point Dar Critical Fixes Test Suite', 'info');
  log('=' * 60, 'info');
  
  const tests = [
    { name: 'Backend Health', fn: testBackendHealth },
    { name: 'Authentication', fn: testAuthentication },
    { name: 'Admin Routes', fn: testAdminRoutes },
    { name: 'Product API', fn: testProductAPI },
    { name: 'Order API', fn: testOrderAPI },
    { name: 'Frontend Files', fn: testFrontendFiles },
    { name: 'Route Conflicts', fn: testRouteConflicts }
  ];
  
  const results = [];
  
  for (const test of tests) {
    log(`\n🧪 Running ${test.name} test...`, 'info');
    
    try {
      const result = await test.fn();
      results.push({ name: test.name, passed: result });
      
      if (result) {
        log(`✅ ${test.name} test PASSED`, 'success');
      } else {
        log(`❌ ${test.name} test FAILED`, 'error');
      }
    } catch (error) {
      log(`💥 ${test.name} test CRASHED: ${error.message}`, 'error');
      results.push({ name: test.name, passed: false });
    }
    
    await sleep(1000); // Brief pause between tests
  }
  
  // Summary
  log('\n📊 TEST SUMMARY', 'info');
  log('=' * 60, 'info');
  
  const passed = results.filter(r => r.passed).length;
  const total = results.length;
  
  results.forEach(result => {
    const status = result.passed ? '✅ PASS' : '❌ FAIL';
    log(`${status} ${result.name}`, result.passed ? 'success' : 'error');
  });
  
  log(`\n🎯 Overall: ${passed}/${total} tests passed (${Math.round(passed/total*100)}%)`, 
      passed === total ? 'success' : 'warning');
  
  if (passed === total) {
    log('🎉 All critical fixes are working correctly!', 'success');
    process.exit(0);
  } else {
    log('⚠️ Some tests failed. Please review the issues above.', 'warning');
    process.exit(1);
  }
}

// Run the tests
if (require.main === module) {
  runTests().catch(error => {
    log(`💥 Test suite crashed: ${error.message}`, 'error');
    process.exit(1);
  });
}

module.exports = { runTests };
