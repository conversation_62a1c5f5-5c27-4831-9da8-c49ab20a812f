# ClickPesa Payment Integration Setup Guide

## 📋 Overview

This guide walks you through setting up ClickPesa payment integration for Phone Point Dar. ClickPesa is a leading payment gateway in Tanzania that supports M-Pesa, Tigo Pesa, Airtel Money, and bank transfers.

## 🔑 Your ClickPesa Credentials

✅ **Already Configured:**
- **Name**: PPD
- **Client ID**: `IDUSrll2waj3bgl0q9YczUllxAsLSdTF`
- **API Key**: `SKeb3hEF8zyvj7srjJXowTInlpxVIPf7 NiN3gwcO8K`
- **BillPay Namba**: `1804`
- **Expires**: August 15th 2025, 22:15
- **API Documentation**: https://docs.clickpesa.com/payment-api/payment-api-overview

## ⚙️ Configuration Status

### ✅ Completed
- [x] Environment variables configured in `.env` and `.env.production`
- [x] ClickPesa service implementation updated
- [x] Webhook signature verification implemented
- [x] Payment controller integration complete
- [x] Test script created

### ⚠️ Pending
- [ ] Webhook secret configuration (production)
- [ ] SSL certificate setup for webhook endpoint
- [ ] Production domain configuration
- [ ] Payment flow testing

## 🚀 Quick Start

### 1. Test ClickPesa Integration

Run the test script to verify your credentials:

```bash
cd DarPhonePoint/DarPhonePoint-backend
npm run test:clickpesa
```

### 2. Environment Configuration

Your `.env` file is already configured with:

```bash
# ClickPesa Payment Configuration (Tanzania)
CLICKPESA_CLIENT_ID=IDUSrll2waj3bgl0q9YczUllxAsLSdTF
CLICKPESA_API_KEY=SKeb3hEF8zyvj7srjJXowTInlpxVIPf7 NiN3gwcO8K
CLICKPESA_API_URL=https://api.clickpesa.com
CLICKPESA_WEBHOOK_SECRET=your_clickpesa_webhook_secret_here
CLICKPESA_BILLPAY_NAMBA=1804
CLICKPESA_ENABLED=true
```

### 3. Production Setup

For production deployment, update these values in `.env.production`:

```bash
# Update these with your actual production URLs
FRONTEND_URL=https://phonepointdar.com
BASE_URL=https://api.phonepointdar.com
PAYMENT_WEBHOOK_URL=https://api.phonepointdar.com/api/payments/clickpesa/callback

# Generate a secure webhook secret
CLICKPESA_WEBHOOK_SECRET=your_secure_webhook_secret_here
```

## 🔧 Implementation Details

### Payment Flow

1. **Customer initiates payment** → Frontend calls `/api/payments`
2. **Backend creates payment** → Calls ClickPesa API
3. **ClickPesa processes** → Customer receives M-Pesa prompt
4. **Customer pays** → ClickPesa sends webhook to your server
5. **Webhook processed** → Order status updated automatically

### Supported Payment Methods

- **M-Pesa** (Vodacom Tanzania)
- **Tigo Pesa** (Tigo Tanzania)
- **Airtel Money** (Airtel Tanzania)
- **Bank Transfer** (Local banks)
- **Cash on Delivery** (Manual processing)

### API Endpoints

- `POST /api/payments` - Create payment
- `GET /api/payments/:id/status` - Check payment status
- `POST /api/payments/clickpesa/callback` - Webhook endpoint
- `POST /api/payments/:id/verify` - Manual verification

## 🔒 Security Features

### Webhook Verification
- HMAC SHA-256 signature verification
- Request ID validation
- Duplicate payment prevention
- Rate limiting on webhook endpoint

### Data Protection
- API keys stored in environment variables
- Sensitive data encrypted in database
- PCI DSS compliance considerations
- Audit logging for all transactions

## 🧪 Testing

### Development Testing

```bash
# Test ClickPesa connection
npm run test:clickpesa

# Test payment creation
curl -X POST http://localhost:5001/api/payments \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "orderId": "ORDER_ID",
    "paymentMethod": "mpesa",
    "phone": "************"
  }'
```

### Production Testing

1. **Small Amount Test**: Start with 1000 TZS test payment
2. **Phone Number**: Use your actual phone number
3. **Monitor Logs**: Check server logs for webhook processing
4. **Verify Database**: Confirm order status updates

## 📊 Monitoring

### Key Metrics to Track
- Payment success rate
- Average processing time
- Failed payment reasons
- Webhook delivery success
- Customer payment method preferences

### Logging
All payment activities are logged with:
- Transaction IDs
- Payment amounts
- Customer information (anonymized)
- Processing timestamps
- Error details

## 🚨 Troubleshooting

### Common Issues

**1. Payment Initialization Fails**
```
Error: ClickPesa API connection failed
```
- Check API credentials in environment variables
- Verify internet connectivity
- Confirm ClickPesa service status

**2. Webhook Not Received**
```
Order status not updating after payment
```
- Verify webhook URL is accessible from internet
- Check SSL certificate validity
- Confirm webhook secret configuration

**3. Invalid Phone Number**
```
Error: Invalid Tanzania phone number format
```
- Ensure phone number starts with 255 or 0
- Remove spaces and special characters
- Validate length (9-12 digits)

### Debug Commands

```bash
# Check environment configuration
node -e "console.log(process.env.CLICKPESA_CLIENT_ID)"

# Test webhook endpoint
curl -X POST https://your-domain.com/api/payments/clickpesa/callback \
  -H "Content-Type: application/json" \
  -d '{"test": true}'

# View payment logs
tail -f logs/combined.log | grep -i clickpesa
```

## 📞 Support

### ClickPesa Support
- **Email**: <EMAIL>
- **Phone**: +255 XXX XXX XXX
- **Documentation**: https://docs.clickpesa.com

### Phone Point Dar Development
- Check server logs in `logs/` directory
- Review payment service in `services/tanzaniaPaymentService.js`
- Test webhook processing in `routes/clickpesaCallback.js`

## 🎯 Next Steps

1. **Complete webhook secret setup** for production security
2. **Set up SSL certificate** for webhook endpoint
3. **Configure production domain** in ClickPesa dashboard
4. **Test payment flow** with real transactions
5. **Monitor payment metrics** and optimize as needed

## 📈 Production Checklist

- [ ] SSL certificate installed and verified
- [ ] Production domain configured in ClickPesa
- [ ] Webhook secret generated and configured
- [ ] Payment flow tested with real money (small amounts)
- [ ] Monitoring and alerting set up
- [ ] Customer support process documented
- [ ] Backup payment method configured
- [ ] Compliance requirements met

---

**🎉 Congratulations!** Your ClickPesa integration is ready for testing and production deployment.
