/**
 * ProductCard Component Tests
 * Tests for the product card component functionality
 */

import React from 'react';
import { screen, fireEvent, waitFor } from '@testing-library/react';
import { renderWithProviders, createMockProduct, createMockUser } from '../utils/testUtils';
import ProductCard from '../../components/products/ProductCard';

// Mock react-router-dom
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate
}));

// Mock toast notifications
jest.mock('react-hot-toast', () => ({
  success: jest.fn(),
  error: jest.fn()
}));

describe('ProductCard Component', () => {
  const mockProduct = createMockProduct();
  
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Rendering', () => {
    it('should render product information correctly', () => {
      renderWithProviders(<ProductCard product={mockProduct} />);

      expect(screen.getByText(mockProduct.name)).toBeInTheDocument();
      expect(screen.getByText(mockProduct.brand)).toBeInTheDocument();
      expect(screen.getByText(`TZS ${mockProduct.price.toLocaleString()}`)).toBeInTheDocument();
      
      // Should show discount if original price is different
      if (mockProduct.originalPrice > mockProduct.price) {
        expect(screen.getByText(`TZS ${mockProduct.originalPrice.toLocaleString()}`)).toBeInTheDocument();
      }
    });

    it('should render product image with alt text', () => {
      renderWithProviders(<ProductCard product={mockProduct} />);

      const image = screen.getByRole('img');
      expect(image).toHaveAttribute('alt', mockProduct.name);
      expect(image).toHaveAttribute('src', expect.stringContaining(mockProduct.images[0]));
    });

    it('should show stock status correctly', () => {
      const inStockProduct = createMockProduct({ stock: 5 });
      renderWithProviders(<ProductCard product={inStockProduct} />);
      expect(screen.getByText(/in stock/i)).toBeInTheDocument();

      const outOfStockProduct = createMockProduct({ stock: 0 });
      renderWithProviders(<ProductCard product={outOfStockProduct} />);
      expect(screen.getByText(/out of stock/i)).toBeInTheDocument();
    });

    it('should show discount percentage when applicable', () => {
      const discountedProduct = createMockProduct({
        price: 800,
        originalPrice: 1000
      });
      
      renderWithProviders(<ProductCard product={discountedProduct} />);
      expect(screen.getByText('20% OFF')).toBeInTheDocument();
    });

    it('should show IMEI badge for products with IMEI tracking', () => {
      const imeiProduct = createMockProduct({ hasIMEI: true });
      renderWithProviders(<ProductCard product={imeiProduct} />);
      expect(screen.getByText(/IMEI tracked/i)).toBeInTheDocument();
    });
  });

  describe('Navigation', () => {
    it('should navigate to product detail page when clicked', () => {
      renderWithProviders(<ProductCard product={mockProduct} />);

      fireEvent.click(screen.getByText(mockProduct.name));
      expect(mockNavigate).toHaveBeenCalledWith(`/products/${mockProduct._id}`);
    });

    it('should navigate when image is clicked', () => {
      renderWithProviders(<ProductCard product={mockProduct} />);

      fireEvent.click(screen.getByRole('img'));
      expect(mockNavigate).toHaveBeenCalledWith(`/products/${mockProduct._id}`);
    });
  });

  describe('Add to Cart Functionality', () => {
    it('should show add to cart button for products without IMEI', () => {
      const simpleProduct = createMockProduct({ hasIMEI: false });
      renderWithProviders(<ProductCard product={simpleProduct} />);

      expect(screen.getByRole('button', { name: /add to cart/i })).toBeInTheDocument();
    });

    it('should show "Select Options" button for products with IMEI', () => {
      const imeiProduct = createMockProduct({ hasIMEI: true });
      renderWithProviders(<ProductCard product={imeiProduct} />);

      expect(screen.getByRole('button', { name: /select options/i })).toBeInTheDocument();
    });

    it('should add product to cart when add to cart is clicked', async () => {
      const simpleProduct = createMockProduct({ hasIMEI: false });
      const { mockCartValue } = renderWithProviders(<ProductCard product={simpleProduct} />);

      fireEvent.click(screen.getByRole('button', { name: /add to cart/i }));

      await waitFor(() => {
        expect(mockCartValue.addItem).toHaveBeenCalledWith({
          product: simpleProduct,
          quantity: 1,
          selectedVariant: simpleProduct.variants[0]
        });
      });
    });

    it('should navigate to product page for IMEI products when select options is clicked', () => {
      const imeiProduct = createMockProduct({ hasIMEI: true });
      renderWithProviders(<ProductCard product={imeiProduct} />);

      fireEvent.click(screen.getByRole('button', { name: /select options/i }));
      expect(mockNavigate).toHaveBeenCalledWith(`/products/${imeiProduct._id}`);
    });

    it('should disable add to cart button when out of stock', () => {
      const outOfStockProduct = createMockProduct({ stock: 0 });
      renderWithProviders(<ProductCard product={outOfStockProduct} />);

      const button = screen.getByRole('button');
      expect(button).toBeDisabled();
      expect(button).toHaveTextContent(/out of stock/i);
    });
  });

  describe('Wishlist Functionality', () => {
    it('should show wishlist button when user is authenticated', () => {
      const user = createMockUser();
      renderWithProviders(<ProductCard product={mockProduct} />, { user });

      expect(screen.getByRole('button', { name: /add to wishlist/i })).toBeInTheDocument();
    });

    it('should not show wishlist button when user is not authenticated', () => {
      renderWithProviders(<ProductCard product={mockProduct} />);

      expect(screen.queryByRole('button', { name: /add to wishlist/i })).not.toBeInTheDocument();
    });

    it('should handle wishlist toggle', async () => {
      const user = createMockUser();
      renderWithProviders(<ProductCard product={mockProduct} />, { user });

      const wishlistButton = screen.getByRole('button', { name: /add to wishlist/i });
      fireEvent.click(wishlistButton);

      // Should show loading state
      await waitFor(() => {
        expect(wishlistButton).toBeDisabled();
      });
    });
  });

  describe('Responsive Behavior', () => {
    it('should handle mobile layout', () => {
      // Mock mobile viewport
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 375,
      });

      renderWithProviders(<ProductCard product={mockProduct} />);

      // Product card should still render all essential information
      expect(screen.getByText(mockProduct.name)).toBeInTheDocument();
      expect(screen.getByText(`TZS ${mockProduct.price.toLocaleString()}`)).toBeInTheDocument();
    });
  });

  describe('Loading States', () => {
    it('should show loading state when adding to cart', async () => {
      const simpleProduct = createMockProduct({ hasIMEI: false });
      renderWithProviders(<ProductCard product={simpleProduct} />);

      const addToCartButton = screen.getByRole('button', { name: /add to cart/i });
      
      // Mock slow cart operation
      const slowAddItem = jest.fn(() => new Promise(resolve => setTimeout(resolve, 100)));
      const { mockCartValue } = renderWithProviders(<ProductCard product={simpleProduct} />);
      mockCartValue.addItem = slowAddItem;

      fireEvent.click(addToCartButton);

      // Button should be disabled during operation
      expect(addToCartButton).toBeDisabled();
    });
  });

  describe('Error Handling', () => {
    it('should handle missing product image gracefully', () => {
      const productWithoutImage = createMockProduct({ images: [] });
      renderWithProviders(<ProductCard product={productWithoutImage} />);

      const image = screen.getByRole('img');
      expect(image).toHaveAttribute('src', expect.stringContaining('placeholder'));
    });

    it('should handle missing product variants', () => {
      const productWithoutVariants = createMockProduct({ variants: [] });
      renderWithProviders(<ProductCard product={productWithoutVariants} />);

      // Should still render the product
      expect(screen.getByText(productWithoutVariants.name)).toBeInTheDocument();
    });

    it('should handle cart addition errors', async () => {
      const simpleProduct = createMockProduct({ hasIMEI: false });
      const { mockCartValue } = renderWithProviders(<ProductCard product={simpleProduct} />);
      
      // Mock cart error
      mockCartValue.addItem = jest.fn().mockRejectedValue(new Error('Cart error'));

      fireEvent.click(screen.getByRole('button', { name: /add to cart/i }));

      await waitFor(() => {
        // Should handle error gracefully and re-enable button
        expect(screen.getByRole('button', { name: /add to cart/i })).not.toBeDisabled();
      });
    });
  });

  describe('Accessibility', () => {
    it('should have proper ARIA labels', () => {
      renderWithProviders(<ProductCard product={mockProduct} />);

      const image = screen.getByRole('img');
      expect(image).toHaveAttribute('alt', mockProduct.name);

      const addToCartButton = screen.getByRole('button', { name: /add to cart|select options/i });
      expect(addToCartButton).toBeInTheDocument();
    });

    it('should support keyboard navigation', () => {
      renderWithProviders(<ProductCard product={mockProduct} />);

      const productCard = screen.getByRole('article') || screen.getByTestId('product-card');
      expect(productCard).toHaveAttribute('tabIndex', '0');
    });

    it('should announce price changes to screen readers', () => {
      const discountedProduct = createMockProduct({
        price: 800,
        originalPrice: 1000
      });
      
      renderWithProviders(<ProductCard product={discountedProduct} />);

      const priceElement = screen.getByText(`TZS ${discountedProduct.price.toLocaleString()}`);
      expect(priceElement).toHaveAttribute('aria-label', expect.stringContaining('Current price'));
    });
  });

  describe('Performance', () => {
    it('should not re-render unnecessarily', () => {
      const { rerender } = renderWithProviders(<ProductCard product={mockProduct} />);
      
      // Re-render with same props
      rerender(<ProductCard product={mockProduct} />);
      
      // Component should handle re-renders efficiently
      expect(screen.getByText(mockProduct.name)).toBeInTheDocument();
    });
  });
});
