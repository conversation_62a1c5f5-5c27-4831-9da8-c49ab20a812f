/**
 * Test Utilities
 * Common utilities and helpers for React component testing
 */

import React from 'react';
import { render } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { AuthProvider } from '../../contexts/AuthContext';
import { CartProvider } from '../../contexts/CartContext';

/**
 * Custom render function that includes common providers
 */
export const renderWithProviders = (ui, options = {}) => {
  const {
    initialEntries = ['/'],
    user = null,
    cart = { items: [], total: 0 },
    ...renderOptions
  } = options;

  // Mock auth context value
  const mockAuthValue = {
    user,
    login: jest.fn(),
    logout: jest.fn(),
    register: jest.fn(),
    loading: false,
    error: null,
    isAuthenticated: !!user,
    updateProfile: jest.fn(),
    changePassword: jest.fn()
  };

  // Mock cart context value
  const mockCartValue = {
    ...cart,
    addItem: jest.fn(),
    removeItem: jest.fn(),
    updateQuantity: jest.fn(),
    clearCart: jest.fn(),
    getItemCount: jest.fn(() => cart.items?.length || 0),
    getTotal: jest.fn(() => cart.total || 0)
  };

  const Wrapper = ({ children }) => (
    <BrowserRouter>
      <AuthProvider value={mockAuthValue}>
        <CartProvider value={mockCartValue}>
          {children}
        </CartProvider>
      </AuthProvider>
    </BrowserRouter>
  );

  return {
    ...render(ui, { wrapper: Wrapper, ...renderOptions }),
    mockAuthValue,
    mockCartValue
  };
};

/**
 * Create mock user object
 */
export const createMockUser = (overrides = {}) => ({
  id: 'user-123',
  name: 'John Doe',
  email: '<EMAIL>',
  role: 'user',
  isEmailVerified: true,
  createdAt: '2024-01-01T00:00:00.000Z',
  ...overrides
});

/**
 * Create mock admin user object
 */
export const createMockAdmin = (overrides = {}) => ({
  id: 'admin-123',
  name: 'Admin User',
  email: '<EMAIL>',
  role: 'admin',
  isEmailVerified: true,
  createdAt: '2024-01-01T00:00:00.000Z',
  ...overrides
});

/**
 * Create mock product object
 */
export const createMockProduct = (overrides = {}) => ({
  _id: 'product-123',
  name: 'iPhone 15 Pro',
  description: 'Latest iPhone with advanced features',
  price: 999.99,
  originalPrice: 1099.99,
  category: 'Smartphones',
  brand: 'Apple',
  model: 'iPhone 15 Pro',
  specifications: {
    display: '6.1 inch Super Retina XDR',
    storage: '128GB',
    ram: '8GB',
    camera: '48MP Pro camera system',
    battery: '3274mAh',
    os: 'iOS 17'
  },
  images: ['/images/iphone-15-pro.jpg'],
  stock: 10,
  isActive: true,
  hasIMEI: true,
  variants: [
    { storage: '128GB', price: 999.99, stock: 10 },
    { storage: '256GB', price: 1099.99, stock: 5 }
  ],
  createdAt: '2024-01-01T00:00:00.000Z',
  ...overrides
});

/**
 * Create mock cart item object
 */
export const createMockCartItem = (overrides = {}) => ({
  product: createMockProduct(),
  quantity: 1,
  selectedVariant: { storage: '128GB', price: 999.99 },
  imei: null,
  ...overrides
});

/**
 * Create mock order object
 */
export const createMockOrder = (overrides = {}) => ({
  _id: 'order-123',
  orderNumber: 'ORD-2024-001',
  user: createMockUser(),
  items: [createMockCartItem()],
  total: 999.99,
  status: 'pending',
  paymentStatus: 'pending',
  paymentMethod: 'mpesa',
  shippingAddress: {
    street: '123 Main Street',
    city: 'Dar es Salaam',
    region: 'Dar es Salaam',
    postalCode: '12345',
    phone: '+255123456789'
  },
  createdAt: '2024-01-01T00:00:00.000Z',
  ...overrides
});

/**
 * Mock API responses
 */
export const mockApiResponses = {
  success: (data = {}) => ({
    success: true,
    data,
    message: 'Operation successful'
  }),
  
  error: (message = 'An error occurred', status = 400) => ({
    success: false,
    message,
    status
  }),
  
  validationError: (errors = []) => ({
    success: false,
    message: 'Validation failed',
    errors
  }),
  
  paginatedResponse: (data = [], page = 1, limit = 10, total = 0) => ({
    success: true,
    data,
    pagination: {
      page,
      limit,
      total,
      pages: Math.ceil(total / limit)
    }
  })
};

/**
 * Mock localStorage
 */
export const mockLocalStorage = () => {
  const store = {};
  
  return {
    getItem: jest.fn((key) => store[key] || null),
    setItem: jest.fn((key, value) => {
      store[key] = value.toString();
    }),
    removeItem: jest.fn((key) => {
      delete store[key];
    }),
    clear: jest.fn(() => {
      Object.keys(store).forEach(key => delete store[key]);
    })
  };
};

/**
 * Mock fetch API
 */
export const mockFetch = (responses = {}) => {
  const mockImplementation = jest.fn((url, options = {}) => {
    const method = options.method || 'GET';
    const key = `${method} ${url}`;
    
    const response = responses[key] || responses[url] || mockApiResponses.error('Not found', 404);
    
    return Promise.resolve({
      ok: response.success || response.status < 400,
      status: response.status || (response.success ? 200 : 400),
      json: () => Promise.resolve(response),
      text: () => Promise.resolve(JSON.stringify(response))
    });
  });
  
  global.fetch = mockImplementation;
  return mockImplementation;
};

/**
 * Wait for async operations to complete
 */
export const waitFor = (ms = 0) => new Promise(resolve => setTimeout(resolve, ms));

/**
 * Mock file object for file upload tests
 */
export const createMockFile = (name = 'test.jpg', type = 'image/jpeg', size = 1024) => {
  const file = new File(['test content'], name, { type });
  Object.defineProperty(file, 'size', { value: size });
  return file;
};

/**
 * Mock intersection observer for lazy loading tests
 */
export const mockIntersectionObserver = () => {
  const mockIntersectionObserver = jest.fn();
  mockIntersectionObserver.mockReturnValue({
    observe: jest.fn(),
    unobserve: jest.fn(),
    disconnect: jest.fn(),
  });
  
  window.IntersectionObserver = mockIntersectionObserver;
  return mockIntersectionObserver;
};

/**
 * Mock window.matchMedia for responsive tests
 */
export const mockMatchMedia = (matches = false) => {
  Object.defineProperty(window, 'matchMedia', {
    writable: true,
    value: jest.fn().mockImplementation(query => ({
      matches,
      media: query,
      onchange: null,
      addListener: jest.fn(),
      removeListener: jest.fn(),
      addEventListener: jest.fn(),
      removeEventListener: jest.fn(),
      dispatchEvent: jest.fn(),
    })),
  });
};

/**
 * Mock window.scrollTo for scroll tests
 */
export const mockScrollTo = () => {
  Object.defineProperty(window, 'scrollTo', {
    writable: true,
    value: jest.fn()
  });
};

/**
 * Custom matchers for common assertions
 */
export const customMatchers = {
  toBeLoading: (received) => {
    const pass = received.querySelector('[data-testid="loading"]') !== null ||
                 received.textContent.includes('Loading') ||
                 received.querySelector('.spinner') !== null;
    
    return {
      message: () => `Expected element ${pass ? 'not ' : ''}to be in loading state`,
      pass
    };
  },
  
  toHaveErrorMessage: (received, message) => {
    const errorElement = received.querySelector('[data-testid="error"]') ||
                        received.querySelector('.error') ||
                        received.querySelector('[role="alert"]');
    
    const pass = errorElement && (!message || errorElement.textContent.includes(message));
    
    return {
      message: () => `Expected element ${pass ? 'not ' : ''}to have error message${message ? ` "${message}"` : ''}`,
      pass
    };
  }
};

// Add custom matchers to expect
if (typeof expect !== 'undefined' && expect.extend) {
  expect.extend(customMatchers);
}
