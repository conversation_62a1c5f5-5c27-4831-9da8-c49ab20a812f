/**
 * AuthContext Tests
 * Tests for authentication context and related functionality
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { AuthProvider, useAuth } from '../../contexts/AuthContext';
import { mockFetch, mockApiResponses, createMockUser, mockLocalStorage } from '../utils/testUtils';

// Mock localStorage
const mockStorage = mockLocalStorage();
Object.defineProperty(window, 'localStorage', { value: mockStorage });

// Test component to access auth context
const TestComponent = () => {
  const auth = useAuth();
  
  return (
    <div>
      <div data-testid="user">{auth.user ? auth.user.name : 'No user'}</div>
      <div data-testid="loading">{auth.loading ? 'Loading' : 'Not loading'}</div>
      <div data-testid="error">{auth.error || 'No error'}</div>
      <div data-testid="authenticated">{auth.isAuthenticated ? 'Authenticated' : 'Not authenticated'}</div>
      
      <button onClick={() => auth.login('<EMAIL>', 'password')}>
        Login
      </button>
      <button onClick={() => auth.register('John Doe', '<EMAIL>', 'password')}>
        Register
      </button>
      <button onClick={() => auth.logout()}>
        Logout
      </button>
      <button onClick={() => auth.updateProfile({ name: 'Updated Name' })}>
        Update Profile
      </button>
    </div>
  );
};

describe('AuthContext', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockStorage.clear();
  });

  describe('Initial State', () => {
    it('should have correct initial state when no token in localStorage', () => {
      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );

      expect(screen.getByTestId('user')).toHaveTextContent('No user');
      expect(screen.getByTestId('loading')).toHaveTextContent('Not loading');
      expect(screen.getByTestId('error')).toHaveTextContent('No error');
      expect(screen.getByTestId('authenticated')).toHaveTextContent('Not authenticated');
    });

    it('should load user from token on mount', async () => {
      const mockUser = createMockUser();
      mockStorage.setItem('token', 'valid-token');
      
      mockFetch({
        'GET /api/auth/me': mockApiResponses.success({ user: mockUser })
      });

      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );

      // Should show loading initially
      expect(screen.getByTestId('loading')).toHaveTextContent('Loading');

      // Wait for user to load
      await waitFor(() => {
        expect(screen.getByTestId('user')).toHaveTextContent(mockUser.name);
        expect(screen.getByTestId('authenticated')).toHaveTextContent('Authenticated');
        expect(screen.getByTestId('loading')).toHaveTextContent('Not loading');
      });
    });

    it('should handle invalid token on mount', async () => {
      mockStorage.setItem('token', 'invalid-token');
      
      mockFetch({
        'GET /api/auth/me': mockApiResponses.error('Invalid token', 401)
      });

      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );

      await waitFor(() => {
        expect(screen.getByTestId('user')).toHaveTextContent('No user');
        expect(screen.getByTestId('authenticated')).toHaveTextContent('Not authenticated');
        expect(mockStorage.removeItem).toHaveBeenCalledWith('token');
      });
    });
  });

  describe('Login', () => {
    it('should login successfully', async () => {
      const mockUser = createMockUser();
      const mockToken = 'new-token';
      
      mockFetch({
        'POST /api/auth/login': mockApiResponses.success({
          token: mockToken,
          user: mockUser
        })
      });

      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );

      fireEvent.click(screen.getByText('Login'));

      // Should show loading during login
      await waitFor(() => {
        expect(screen.getByTestId('loading')).toHaveTextContent('Loading');
      });

      // Should complete login
      await waitFor(() => {
        expect(screen.getByTestId('user')).toHaveTextContent(mockUser.name);
        expect(screen.getByTestId('authenticated')).toHaveTextContent('Authenticated');
        expect(screen.getByTestId('loading')).toHaveTextContent('Not loading');
        expect(mockStorage.setItem).toHaveBeenCalledWith('token', mockToken);
      });
    });

    it('should handle login failure', async () => {
      mockFetch({
        'POST /api/auth/login': mockApiResponses.error('Invalid credentials', 401)
      });

      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );

      fireEvent.click(screen.getByText('Login'));

      await waitFor(() => {
        expect(screen.getByTestId('error')).toHaveTextContent('Invalid credentials');
        expect(screen.getByTestId('authenticated')).toHaveTextContent('Not authenticated');
        expect(screen.getByTestId('loading')).toHaveTextContent('Not loading');
      });
    });

    it('should handle network error during login', async () => {
      global.fetch = jest.fn().mockRejectedValue(new Error('Network error'));

      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );

      fireEvent.click(screen.getByText('Login'));

      await waitFor(() => {
        expect(screen.getByTestId('error')).toHaveTextContent('Network error occurred');
        expect(screen.getByTestId('loading')).toHaveTextContent('Not loading');
      });
    });
  });

  describe('Register', () => {
    it('should register successfully', async () => {
      mockFetch({
        'POST /api/auth/register': mockApiResponses.success({
          message: 'Registration successful. Please verify your email.',
          requiresVerification: true
        })
      });

      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );

      fireEvent.click(screen.getByText('Register'));

      await waitFor(() => {
        expect(screen.getByTestId('loading')).toHaveTextContent('Not loading');
        // Registration doesn't automatically log in, so user should still be null
        expect(screen.getByTestId('user')).toHaveTextContent('No user');
      });
    });

    it('should handle registration failure', async () => {
      mockFetch({
        'POST /api/auth/register': mockApiResponses.error('Email already exists', 400)
      });

      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );

      fireEvent.click(screen.getByText('Register'));

      await waitFor(() => {
        expect(screen.getByTestId('error')).toHaveTextContent('Email already exists');
        expect(screen.getByTestId('loading')).toHaveTextContent('Not loading');
      });
    });
  });

  describe('Logout', () => {
    it('should logout successfully', async () => {
      const mockUser = createMockUser();
      mockStorage.setItem('token', 'valid-token');
      
      // Setup initial authenticated state
      mockFetch({
        'GET /api/auth/me': mockApiResponses.success({ user: mockUser }),
        'POST /api/auth/logout': mockApiResponses.success({ message: 'Logged out successfully' })
      });

      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );

      // Wait for initial load
      await waitFor(() => {
        expect(screen.getByTestId('authenticated')).toHaveTextContent('Authenticated');
      });

      fireEvent.click(screen.getByText('Logout'));

      await waitFor(() => {
        expect(screen.getByTestId('user')).toHaveTextContent('No user');
        expect(screen.getByTestId('authenticated')).toHaveTextContent('Not authenticated');
        expect(mockStorage.removeItem).toHaveBeenCalledWith('token');
      });
    });

    it('should handle logout failure gracefully', async () => {
      const mockUser = createMockUser();
      mockStorage.setItem('token', 'valid-token');
      
      mockFetch({
        'GET /api/auth/me': mockApiResponses.success({ user: mockUser }),
        'POST /api/auth/logout': mockApiResponses.error('Logout failed', 500)
      });

      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );

      await waitFor(() => {
        expect(screen.getByTestId('authenticated')).toHaveTextContent('Authenticated');
      });

      fireEvent.click(screen.getByText('Logout'));

      // Should still logout locally even if server request fails
      await waitFor(() => {
        expect(screen.getByTestId('user')).toHaveTextContent('No user');
        expect(screen.getByTestId('authenticated')).toHaveTextContent('Not authenticated');
        expect(mockStorage.removeItem).toHaveBeenCalledWith('token');
      });
    });
  });

  describe('Update Profile', () => {
    it('should update profile successfully', async () => {
      const mockUser = createMockUser();
      const updatedUser = { ...mockUser, name: 'Updated Name' };
      mockStorage.setItem('token', 'valid-token');
      
      mockFetch({
        'GET /api/auth/me': mockApiResponses.success({ user: mockUser }),
        'PUT /api/auth/profile': mockApiResponses.success({ user: updatedUser })
      });

      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );

      await waitFor(() => {
        expect(screen.getByTestId('user')).toHaveTextContent(mockUser.name);
      });

      fireEvent.click(screen.getByText('Update Profile'));

      await waitFor(() => {
        expect(screen.getByTestId('user')).toHaveTextContent('Updated Name');
      });
    });

    it('should handle profile update failure', async () => {
      const mockUser = createMockUser();
      mockStorage.setItem('token', 'valid-token');
      
      mockFetch({
        'GET /api/auth/me': mockApiResponses.success({ user: mockUser }),
        'PUT /api/auth/profile': mockApiResponses.error('Update failed', 400)
      });

      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );

      await waitFor(() => {
        expect(screen.getByTestId('user')).toHaveTextContent(mockUser.name);
      });

      fireEvent.click(screen.getByText('Update Profile'));

      await waitFor(() => {
        expect(screen.getByTestId('error')).toHaveTextContent('Update failed');
        // User should remain unchanged
        expect(screen.getByTestId('user')).toHaveTextContent(mockUser.name);
      });
    });
  });

  describe('Error Handling', () => {
    it('should clear errors when starting new operations', async () => {
      mockFetch({
        'POST /api/auth/login': mockApiResponses.error('Login failed', 401)
      });

      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );

      // Trigger error
      fireEvent.click(screen.getByText('Login'));
      await waitFor(() => {
        expect(screen.getByTestId('error')).toHaveTextContent('Login failed');
      });

      // Start new operation - should clear error
      mockFetch({
        'POST /api/auth/register': mockApiResponses.success({ message: 'Success' })
      });

      fireEvent.click(screen.getByText('Register'));
      
      // Error should be cleared when new operation starts
      expect(screen.getByTestId('error')).toHaveTextContent('No error');
    });
  });
});
