/**
 * useApiRequest Hook Tests
 * Tests for the API request custom hook
 */

import { renderHook, act } from '@testing-library/react';
import useApiRequest from '../../hooks/useApiRequest';
import { mockFetch, mockApiResponses, waitFor } from '../utils/testUtils';

describe('useApiRequest Hook', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Initial State', () => {
    it('should have correct initial state', () => {
      const { result } = renderHook(() => useApiRequest());

      expect(result.current.data).toBeNull();
      expect(result.current.loading).toBe(false);
      expect(result.current.error).toBeNull();
    });
  });

  describe('GET Requests', () => {
    it('should handle successful GET request', async () => {
      const mockData = { id: 1, name: 'Test Product' };
      mockFetch({
        'GET /api/products/1': mockApiResponses.success(mockData)
      });

      const { result } = renderHook(() => useApiRequest());

      act(() => {
        result.current.request('/api/products/1');
      });

      // Should set loading to true
      expect(result.current.loading).toBe(true);
      expect(result.current.error).toBeNull();

      // Wait for request to complete
      await waitFor(() => {
        expect(result.current.loading).toBe(false);
        expect(result.current.data).toEqual(mockData);
        expect(result.current.error).toBeNull();
      });
    });

    it('should handle GET request with query parameters', async () => {
      const mockData = { products: [], total: 0 };
      mockFetch({
        'GET /api/products?page=1&limit=10': mockApiResponses.success(mockData)
      });

      const { result } = renderHook(() => useApiRequest());

      act(() => {
        result.current.request('/api/products', {
          params: { page: 1, limit: 10 }
        });
      });

      await waitFor(() => {
        expect(result.current.data).toEqual(mockData);
      });
    });

    it('should handle failed GET request', async () => {
      const errorMessage = 'Product not found';
      mockFetch({
        'GET /api/products/999': mockApiResponses.error(errorMessage, 404)
      });

      const { result } = renderHook(() => useApiRequest());

      act(() => {
        result.current.request('/api/products/999');
      });

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
        expect(result.current.data).toBeNull();
        expect(result.current.error).toBe(errorMessage);
      });
    });
  });

  describe('POST Requests', () => {
    it('should handle successful POST request', async () => {
      const requestData = { name: 'New Product', price: 99.99 };
      const responseData = { id: 1, ...requestData };
      
      mockFetch({
        'POST /api/products': mockApiResponses.success(responseData)
      });

      const { result } = renderHook(() => useApiRequest());

      act(() => {
        result.current.request('/api/products', {
          method: 'POST',
          data: requestData
        });
      });

      expect(result.current.loading).toBe(true);

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
        expect(result.current.data).toEqual(responseData);
        expect(result.current.error).toBeNull();
      });
    });

    it('should handle POST request with validation errors', async () => {
      const validationErrors = [
        { field: 'name', message: 'Name is required' },
        { field: 'price', message: 'Price must be positive' }
      ];
      
      mockFetch({
        'POST /api/products': mockApiResponses.validationError(validationErrors)
      });

      const { result } = renderHook(() => useApiRequest());

      act(() => {
        result.current.request('/api/products', {
          method: 'POST',
          data: { name: '', price: -10 }
        });
      });

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
        expect(result.current.error).toBe('Validation failed');
        expect(result.current.validationErrors).toEqual(validationErrors);
      });
    });
  });

  describe('PUT/PATCH Requests', () => {
    it('should handle successful PUT request', async () => {
      const updateData = { name: 'Updated Product' };
      const responseData = { id: 1, name: 'Updated Product', price: 99.99 };
      
      mockFetch({
        'PUT /api/products/1': mockApiResponses.success(responseData)
      });

      const { result } = renderHook(() => useApiRequest());

      act(() => {
        result.current.request('/api/products/1', {
          method: 'PUT',
          data: updateData
        });
      });

      await waitFor(() => {
        expect(result.current.data).toEqual(responseData);
      });
    });

    it('should handle PATCH request', async () => {
      const patchData = { price: 89.99 };
      const responseData = { id: 1, name: 'Product', price: 89.99 };
      
      mockFetch({
        'PATCH /api/products/1': mockApiResponses.success(responseData)
      });

      const { result } = renderHook(() => useApiRequest());

      act(() => {
        result.current.request('/api/products/1', {
          method: 'PATCH',
          data: patchData
        });
      });

      await waitFor(() => {
        expect(result.current.data).toEqual(responseData);
      });
    });
  });

  describe('DELETE Requests', () => {
    it('should handle successful DELETE request', async () => {
      mockFetch({
        'DELETE /api/products/1': mockApiResponses.success({ message: 'Product deleted' })
      });

      const { result } = renderHook(() => useApiRequest());

      act(() => {
        result.current.request('/api/products/1', {
          method: 'DELETE'
        });
      });

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
        expect(result.current.data).toEqual({ message: 'Product deleted' });
        expect(result.current.error).toBeNull();
      });
    });
  });

  describe('Request Cancellation', () => {
    it('should cancel ongoing request when component unmounts', async () => {
      mockFetch({
        'GET /api/products': new Promise(resolve => 
          setTimeout(() => resolve(mockApiResponses.success({ data: 'test' })), 1000)
        )
      });

      const { result, unmount } = renderHook(() => useApiRequest());

      act(() => {
        result.current.request('/api/products');
      });

      expect(result.current.loading).toBe(true);

      // Unmount before request completes
      unmount();

      // Should not update state after unmount
      await waitFor(() => {
        // No assertion needed - just ensuring no memory leaks or warnings
      });
    });

    it('should cancel previous request when new request is made', async () => {
      const { result } = renderHook(() => useApiRequest());

      // Start first request
      mockFetch({
        'GET /api/products/1': new Promise(resolve => 
          setTimeout(() => resolve(mockApiResponses.success({ id: 1 })), 1000)
        )
      });

      act(() => {
        result.current.request('/api/products/1');
      });

      expect(result.current.loading).toBe(true);

      // Start second request before first completes
      mockFetch({
        'GET /api/products/2': mockApiResponses.success({ id: 2 })
      });

      act(() => {
        result.current.request('/api/products/2');
      });

      await waitFor(() => {
        expect(result.current.data).toEqual({ id: 2 });
      });
    });
  });

  describe('Error Handling', () => {
    it('should handle network errors', async () => {
      global.fetch = jest.fn().mockRejectedValue(new Error('Network error'));

      const { result } = renderHook(() => useApiRequest());

      act(() => {
        result.current.request('/api/products');
      });

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
        expect(result.current.error).toBe('Network error occurred');
      });
    });

    it('should handle timeout errors', async () => {
      global.fetch = jest.fn().mockImplementation(() => 
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('Request timeout')), 100)
        )
      );

      const { result } = renderHook(() => useApiRequest());

      act(() => {
        result.current.request('/api/products', { timeout: 50 });
      });

      await waitFor(() => {
        expect(result.current.error).toContain('timeout');
      });
    });

    it('should handle malformed JSON responses', async () => {
      global.fetch = jest.fn().mockResolvedValue({
        ok: true,
        status: 200,
        json: () => Promise.reject(new Error('Invalid JSON'))
      });

      const { result } = renderHook(() => useApiRequest());

      act(() => {
        result.current.request('/api/products');
      });

      await waitFor(() => {
        expect(result.current.error).toBe('Invalid response format');
      });
    });
  });

  describe('Request Options', () => {
    it('should handle custom headers', async () => {
      const mockFetchSpy = mockFetch({
        'GET /api/products': mockApiResponses.success({ data: 'test' })
      });

      const { result } = renderHook(() => useApiRequest());

      act(() => {
        result.current.request('/api/products', {
          headers: {
            'Custom-Header': 'test-value',
            'Authorization': 'Bearer token'
          }
        });
      });

      await waitFor(() => {
        expect(mockFetchSpy).toHaveBeenCalledWith(
          expect.any(String),
          expect.objectContaining({
            headers: expect.objectContaining({
              'Custom-Header': 'test-value',
              'Authorization': 'Bearer token'
            })
          })
        );
      });
    });

    it('should handle request transformations', async () => {
      mockFetch({
        'POST /api/products': mockApiResponses.success({ id: 1 })
      });

      const { result } = renderHook(() => useApiRequest());

      act(() => {
        result.current.request('/api/products', {
          method: 'POST',
          data: { name: 'Test' },
          transformRequest: (data) => ({ ...data, transformed: true })
        });
      });

      await waitFor(() => {
        expect(result.current.data).toEqual({ id: 1 });
      });
    });

    it('should handle response transformations', async () => {
      mockFetch({
        'GET /api/products': mockApiResponses.success({ name: 'test product' })
      });

      const { result } = renderHook(() => useApiRequest());

      act(() => {
        result.current.request('/api/products', {
          transformResponse: (data) => ({ ...data, transformed: true })
        });
      });

      await waitFor(() => {
        expect(result.current.data).toEqual({ 
          name: 'test product', 
          transformed: true 
        });
      });
    });
  });

  describe('Utility Methods', () => {
    it('should provide reset method', () => {
      const { result } = renderHook(() => useApiRequest());

      // Set some state
      act(() => {
        result.current.request('/api/products');
      });

      // Reset state
      act(() => {
        result.current.reset();
      });

      expect(result.current.data).toBeNull();
      expect(result.current.loading).toBe(false);
      expect(result.current.error).toBeNull();
    });

    it('should provide clearError method', async () => {
      mockFetch({
        'GET /api/products': mockApiResponses.error('Test error')
      });

      const { result } = renderHook(() => useApiRequest());

      act(() => {
        result.current.request('/api/products');
      });

      await waitFor(() => {
        expect(result.current.error).toBe('Test error');
      });

      act(() => {
        result.current.clearError();
      });

      expect(result.current.error).toBeNull();
    });
  });
});
