import React from 'react';
import { render, screen } from '@testing-library/react';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import { AuthProvider } from '../contexts/AuthContext';
import { LoadingProvider } from '../contexts/LoadingContext';
import App from '../App';

// Mock the analytics utility
vi.mock('../utils/analytics', () => ({
  trackPageView: vi.fn()
}));

// Mock the components
vi.mock('../components/layout/Header', () => ({ default: () => <div data-testid="header">Header</div> }));
vi.mock('../components/layout/Footer', () => ({ default: () => <div data-testid="footer">Footer</div> }));
vi.mock('../pages/HomePage', () => ({ default: () => <div data-testid="home-page">Home Page</div> }));
vi.mock('../pages/LoginPage', () => ({ default: () => <div data-testid="login-page">Login Page</div> }));
vi.mock('../pages/RegisterPage', () => ({ default: () => <div data-testid="register-page">Register Page</div> }));
vi.mock('../pages/ProductsPage', () => ({ default: () => <div data-testid="products-page">Products Page</div> }));
vi.mock('../pages/ProductDetailPage', () => ({ default: () => <div data-testid="product-detail-page">Product Detail Page</div> }));
vi.mock('../pages/CartPage', () => ({ default: () => <div data-testid="cart-page">Cart Page</div> }));
vi.mock('../pages/CheckoutPage', () => ({ default: () => <div data-testid="checkout-page">Checkout Page</div> }));
vi.mock('../pages/DashboardPage', () => ({ default: () => <div data-testid="dashboard-page">Dashboard Page</div> }));
vi.mock('../pages/NotFoundPage', () => ({ default: () => <div data-testid="not-found-page">Not Found Page</div> }));

// Mock the useLocation hook
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useLocation: () => ({ pathname: '/' })
  };
});

describe('App Component', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });
  
  it('renders without crashing', () => {
    render(
      <BrowserRouter>
        <AuthProvider>
          <LoadingProvider>
            <App />
          </LoadingProvider>
        </AuthProvider>
      </BrowserRouter>
    );
    
    expect(screen.getByTestId('header')).toBeInTheDocument();
    expect(screen.getByTestId('footer')).toBeInTheDocument();
    expect(screen.getByTestId('home-page')).toBeInTheDocument();
  });
  
  it('tracks page view on mount', () => {
    render(
      <BrowserRouter>
        <AuthProvider>
          <LoadingProvider>
            <App />
          </LoadingProvider>
        </AuthProvider>
      </BrowserRouter>
    );
    
    expect(trackPageView).toHaveBeenCalledWith('home');
  });
});
