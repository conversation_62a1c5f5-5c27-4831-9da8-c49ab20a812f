import { post, get } from './unifiedApiClient';

const analytics = {
  // Track user events
  track: async (eventData) => {
    try {
      const response = await post('/analytics/track', eventData, { silent: true });
      return response.data?.data || response.data || response;
    } catch (error) {
      console.error('Error tracking analytics event:', error);
      throw error;
    }
  },

  // Get dashboard statistics
  getDashboardStats: async (timeRange = '30d') => {
    try {
      const response = await get(`/analytics/dashboard-stats`, { params: { timeRange } });
      return response.data?.data || response.data || response;
    } catch (error) {
      console.error('Error fetching dashboard stats:', error);
      throw error;
    }
  },

  // Get weekly activity heatmap data
  getWeeklyActivityHeatmap: async (days = 30) => {
    try {
      const response = await safeApiRequest({
        method: 'GET',
        url: `/analytics/heatmap`,
        params: { days }
      });
      return response.data?.data || response.data || response;
    } catch (error) {
      console.error('Error fetching weekly activity heatmap:', error);
      throw error;
    }
  },

  // Get conversion funnel data
  getConversionFunnel: async (timeRange = '30d') => {
    try {
      const response = await safeApiRequest({
        method: 'GET',
        url: `/analytics/conversion-funnel`,
        params: { timeRange }
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching conversion funnel:', error);
      throw error;
    }
  },

  // Get user behavior analytics
  getUserBehavior: async (params = {}) => {
    try {
      const response = await safeApiRequest({
        method: 'GET',
        url: '/analytics/user-behavior',
        params
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching user behavior analytics:', error);
      throw error;
    }
  },

  // Get revenue analytics
  getRevenueAnalytics: async (timeRange = '30d') => {
    try {
      const response = await safeApiRequest({
        method: 'GET',
        url: `/analytics/revenue`,
        params: { timeRange }
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching revenue analytics:', error);
      throw error;
    }
  }
};

export default analytics;
