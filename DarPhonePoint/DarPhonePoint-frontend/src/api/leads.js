import { get, post, put, del } from './unifiedApiClient';

const leads = {
  // Capture a new lead
  capture: async (leadData) => {
    const response = await post('/leads/capture', leadData);
    return response.data?.data || response.data || response;
  },

  // Create a new lead
  create: async (leadData) => {
    const response = await post('/leads', leadData);
    return response.data?.data || response.data || response;
  },

  // Get all leads (admin only)
  getAll: async (params = {}) => {
    const response = await get('/leads', { params });
    return response.data?.data || response.data || response;
  },

  // Get lead by ID (admin only)
  getById: async (id) => {
    const response = await get(`/leads/${id}`);
    return response.data?.data || response.data || response;
  },

  // Update lead (admin only)
  update: async (id, leadData) => {
    const response = await put(`/leads/${id}`, leadData);
    return response.data?.data || response.data || response;
  },

  // Delete lead (admin only)
  delete: async (id) => {
    return await apiRequest('delete', `/leads/${id}`);
  }
};

export default leads;
