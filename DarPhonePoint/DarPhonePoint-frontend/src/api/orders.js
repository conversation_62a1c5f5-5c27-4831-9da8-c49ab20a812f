import { safeApiRequest } from './apiClient';

const BASE_URL = '/orders';

const orders = {
  /**
   * Get all orders (admin only)
   * @param {Object} filters - Filter parameters
   * @returns {Promise<Array>} List of orders
   */
  getAll: async (filters = {}) => {
    try {
      const response = await safeApiRequest({
        method: 'GET',
        url: BASE_URL,
        params: filters
      });
      return response.data?.data || response.data || response;
    } catch (error) {
      console.error('Error fetching orders:', error);
      throw error;
    }
  },

  /**
   * Get order by ID
   * @param {string} orderId - The ID of the order to fetch
   * @returns {Promise<Object>} Order details
   */
  getById: async (orderId) => {
    try {
      const response = await safeApiRequest({
        method: 'GET',
        url: `${BASE_URL}/${orderId}`
      });
      return response.data?.data || response.data || response;
    } catch (error) {
      console.error(`Error fetching order ${orderId}:`, error);
      throw error;
    }
  },

  /**
   * Create a new order
   * @param {Object} orderData - The order data
   * @returns {Promise<Object>} Created order
   */
  create: async (orderData) => {
    try {
      const response = await safeApiRequest({
        method: 'POST',
        url: BASE_URL,
        data: orderData
      });
      return response.data;
    } catch (error) {
      console.error('Error creating order:', error);
      throw error;
    }
  },

  /**
   * Update order status (admin only)
   * @param {string} orderId - The ID of the order to update
   * @param {string} status - The new status
   * @returns {Promise<Object>} Updated order
   */
  updateStatus: async (orderId, status) => {
    try {
      const response = await safeApiRequest({
        method: 'PUT',
        url: `${BASE_URL}/${orderId}/status`,
        data: { status }
      });
      return response.data;
    } catch (error) {
      console.error(`Error updating order ${orderId} status:`, error);
      throw error;
    }
  },

  /**
   * Cancel order
   * @param {string} orderId - The ID of the order to cancel
   * @returns {Promise<Object>} Cancelled order
   */
  cancel: async (orderId) => {
    try {
      const response = await safeApiRequest({
        method: 'POST',
        url: `${BASE_URL}/${orderId}/cancel`
      });
      return response.data;
    } catch (error) {
      console.error(`Error cancelling order ${orderId}:`, error);
      throw error;
    }
  },

  /**
   * Get order items
   * @param {string} orderId - The ID of the order
   * @returns {Promise<Array>} List of order items
   */
  getItems: async (orderId) => {
    try {
      const response = await safeApiRequest({
        method: 'GET',
        url: `${BASE_URL}/${orderId}/items`
      });
      return response.data;
    } catch (error) {
      console.error(`Error fetching items for order ${orderId}:`, error);
      throw error;
    }
  },

  /**
   * Get order history
   * @param {string} orderId - The ID of the order
   * @returns {Promise<Array>} Order history
   */
  getHistory: async (orderId) => {
    try {
      const response = await safeApiRequest({
        method: 'GET',
        url: `${BASE_URL}/${orderId}/history`
      });
      return response.data;
    } catch (error) {
      console.error(`Error fetching history for order ${orderId}:`, error);
      throw error;
    }
  },

  /**
   * Get order invoice
   * @param {string} orderId - The ID of the order
   * @returns {Promise<Object>} Invoice data
   */
  getInvoice: async (orderId) => {
    try {
      const response = await safeApiRequest({
        method: 'GET',
        url: `${BASE_URL}/${orderId}/invoice`
      });
      return response.data;
    } catch (error) {
      console.error(`Error fetching invoice for order ${orderId}:`, error);
      throw error;
    }
  },

  /**
   * Get order tracking information
   * @param {string} orderId - The ID of the order
   * @returns {Promise<Object>} Tracking information
   */
  getTracking: async (orderId) => {
    try {
      const response = await safeApiRequest({
        method: 'GET',
        url: `${BASE_URL}/${orderId}/tracking`
      });
      return response.data;
    } catch (error) {
      console.error(`Error fetching tracking for order ${orderId}:`, error);
      throw error;
    }
  },

  /**
   * Get current user's orders
   * @returns {Promise<Array>} User's orders
   */
  getUserOrders: async () => {
    try {
      // Use safe API request wrapper to handle axios internal issues
      const response = await safeApiRequest({
        method: 'GET',
        url: BASE_URL
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching user orders:', error);
      throw error;
    }
  }
};

export default orders;
