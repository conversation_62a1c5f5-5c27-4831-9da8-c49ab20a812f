import { safeApiRequest } from './apiClient';

const BASE_URL = '/health';

const health = {
  /**
   * Basic health check
   * @returns {Promise<Object>} Health status
   */
  check: async () => {
    try {
      const response = await safeApiRequest({
        method: 'GET',
        url: BASE_URL
      });
      return response.data?.data || response.data || response;
    } catch (error) {
      console.error('Error checking health:', error);
      throw error;
    }
  },

  /**
   * Detailed health check with service dependencies
   * @returns {Promise<Object>} Detailed health status
   */
  detailed: async () => {
    try {
      const response = await safeApiRequest({
        method: 'GET',
        url: `${BASE_URL}/detailed`
      });
      return response.data?.data || response.data || response;
    } catch (error) {
      console.error('Error checking detailed health:', error);
      throw error;
    }
  },

  /**
   * Readiness probe
   * @returns {Promise<Object>} Readiness status
   */
  ready: async () => {
    try {
      const response = await safeApiRequest({
        method: 'GET',
        url: `${BASE_URL}/ready`
      });
      return response.data;
    } catch (error) {
      console.error('Error checking readiness:', error);
      throw error;
    }
  },

  /**
   * Liveness probe
   * @returns {Promise<Object>} Liveness status
   */
  live: async () => {
    try {
      const response = await safeApiRequest({
        method: 'GET',
        url: `${BASE_URL}/live`
      });
      return response.data;
    } catch (error) {
      console.error('Error checking liveness:', error);
      throw error;
    }
  }
};

export default health;
