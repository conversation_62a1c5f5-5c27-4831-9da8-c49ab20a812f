import { safeApiRequest } from './apiClient';

const BASE_URL = '/auth';

const auth = {
  /**
   * Register a new user
   * @param {Object} userData - The user registration data
   * @returns {Promise<Object>} Registration result
   */
  register: async (userData) => {
    try {
      const response = await safeApiRequest({
        method: 'POST',
        url: `${BASE_URL}/register`,
        data: userData
      });
      return response.data;
    } catch (error) {
      console.error('Error registering user:', error);
      throw error;
    }
  },

  /**
   * Login user
   * @param {Object} credentials - The login credentials
   * @returns {Promise<Object>} Login result with token
   */
  login: async (credentials) => {
    try {
      const response = await safeApiRequest({
        method: 'POST',
        url: `${BASE_URL}/login`,
        data: credentials
      });
      return response.data;
    } catch (error) {
      console.error('Error logging in:', error);
      throw error;
    }
  },

  /**
   * Logout user
   * @returns {Promise<Object>} Logout result
   */
  logout: async () => {
    try {
      const response = await safeApiRequest({
        method: 'POST',
        url: `${BASE_URL}/logout`
      });
      return response.data;
    } catch (error) {
      console.error('Error logging out:', error);
      throw error;
    }
  },

  /**
   * Get current user profile
   * @returns {Promise<Object>} User profile
   */
  getProfile: async () => {
    try {
      const response = await safeApiRequest({
        method: 'GET',
        url: `${BASE_URL}/me`
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching user profile:', error);
      throw error;
    }
  },

  /**
   * Get current user (alias for getProfile)
   * @returns {Promise<Object>} User profile data
   */
  getCurrentUser: async () => {
    try {
      const response = await safeApiRequest({
        method: 'GET',
        url: `${BASE_URL}/me`
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching current user:', error);
      throw error;
    }
  },

  /**
   * Update user profile
   * @param {Object} profileData - The profile data to update
   * @returns {Promise<Object>} Updated profile
   */
  updateProfile: async (profileData) => {
    try {
      const response = await safeApiRequest({
        method: 'PUT',
        url: `${BASE_URL}/profile`,
        data: profileData
      });
      return response.data;
    } catch (error) {
      console.error('Error updating user profile:', error);
      throw error;
    }
  },

  /**
   * Change password
   * @param {Object} passwordData - The password change data
   * @returns {Promise<Object>} Password change result
   */
  changePassword: async (passwordData) => {
    try {
      const response = await safeApiRequest({
        method: 'PUT',
        url: `${BASE_URL}/change-password`,
        data: passwordData
      });
      return response.data;
    } catch (error) {
      console.error('Error changing password:', error);
      throw error;
    }
  },

  /**
   * Request password reset
   * @param {string} email - The user's email
   * @returns {Promise<Object>} Password reset request result
   */
  requestPasswordReset: async (email) => {
    try {
      const response = await safeApiRequest({
        method: 'POST',
        url: `${BASE_URL}/forgot-password`,
        data: { email }
      });
      return response.data;
    } catch (error) {
      console.error('Error requesting password reset:', error);
      throw error;
    }
  },

  /**
   * Reset password
   * @param {string} token - The reset token
   * @param {string} password - The new password
   * @returns {Promise<Object>} Password reset result
   */
  resetPassword: async (token, password) => {
    try {
      const response = await safeApiRequest({
        method: 'PUT',
        url: `${BASE_URL}/reset-password/${token}`,
        data: { password }
      });
      return response.data;
    } catch (error) {
      console.error('Error resetting password:', error);
      throw error;
    }
  },

  /**
   * Verify email
   * @param {string} token - The verification token
   * @returns {Promise<Object>} Email verification result
   */
  verifyEmail: async (token) => {
    try {
      const response = await safeApiRequest({
        method: 'POST',
        url: `${BASE_URL}/verify-email`,
        data: { token }
      });
      return response.data;
    } catch (error) {
      console.error('Error verifying email:', error);
      throw error;
    }
  },

  /**
   * Request email verification
   * @returns {Promise<Object>} Email verification request result
   */
  requestEmailVerification: async () => {
    try {
      const response = await safeApiRequest({
        method: 'POST',
        url: `${BASE_URL}/request-email-verification`
      });
      return response.data;
    } catch (error) {
      console.error('Error requesting email verification:', error);
      throw error;
    }
  },

  /**
   * Refresh authentication token
   * @returns {Promise<Object>} New token data
   */
  refreshToken: async () => {
    try {
      const response = await safeApiRequest({
        method: 'POST',
        url: `${BASE_URL}/refresh-token`
      });
      return response.data;
    } catch (error) {
      console.error('Error refreshing token:', error);
      throw error;
    }
  }
};

export default auth;