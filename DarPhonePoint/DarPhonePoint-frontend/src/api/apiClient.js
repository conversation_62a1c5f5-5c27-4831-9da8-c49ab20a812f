import axios from 'axios';
import { toast } from 'react-toastify';
import errorHandlingService from '../services/errorHandlingService';

/**
 * Unified API Client for Phone Point Dar
 * Consolidates all API client patterns with simplified, consistent interface
 */

// Simple cache for GET requests
const requestCache = new Map();
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

// Cache implementations
const memoryCache = {
  cache: new Map(),
  get(key) {
    const item = this.cache.get(key);
    if (item && Date.now() < item.expiry) {
      return item.data;
    }
    this.cache.delete(key);
    return null;
  },
  set(key, data, ttl = CACHE_DURATION) {
    this.cache.set(key, {
      data,
      expiry: Date.now() + ttl
    });
  }
};

const storageCache = {
  get(key) {
    try {
      const item = localStorage.getItem(`cache_${key}`);
      if (item) {
        const parsed = JSON.parse(item);
        if (Date.now() < parsed.expiry) {
          return parsed.data;
        }
        localStorage.removeItem(`cache_${key}`);
      }
    } catch (e) {
      console.warn('Cache read error:', e);
    }
    return null;
  },
  set(key, data, ttl = CACHE_DURATION) {
    try {
      localStorage.setItem(`cache_${key}`, JSON.stringify({
        data,
        expiry: Date.now() + ttl
      }));
    } catch (e) {
      console.warn('Cache write error:', e);
    }
  }
};

// Helper functions
const getToken = () => localStorage.getItem('token');
const setToken = (token) => localStorage.setItem('token', token);
const removeToken = () => {
  localStorage.removeItem('token');
  localStorage.removeItem('user');
};

// Simple timing functions for performance tracking
const startTiming = (url, type) => ({ start: Date.now(), url, type });
const endTiming = (timing, success, metadata = {}) => {
  if (timing) {
    const duration = Date.now() - timing.start;
    console.debug(`API ${timing.type} ${success ? 'success' : 'error'}: ${timing.url} (${duration}ms)`, metadata);
  }
};

// Create axios instance with simplified config
const apiClient = axios.create({
  baseURL: '/api', // Use proxy in development, direct URL in production
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

/**
 * Request interceptor for authentication and caching
 */
apiClient.interceptors.request.use(
  (config) => {
    const token = getToken();
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    // Ensure method is always set and properly formatted
    if (!config.method) {
      config.method = 'GET'; // Default to GET
    }

    // Ensure method is always a string and uppercase
    config.method = String(config.method).toUpperCase();

    // Start performance timing for API calls
    const timing = startTiming(config.url, 'api');
    config.metadata = { ...config.metadata, timing };

    // Add cache control for GET requests
    const method = String(config.method || 'GET').toLowerCase();
    if (method === 'get' && config.cache !== false) {
      config.metadata = {
        ...config.metadata,
        startTime: new Date().getTime(),
        cacheKey: generateCacheKey(config),
        useCache: true,
        cacheTime: config.cacheTime || 5 * 60 * 1000, // Default: 5 minutes
        storageType: config.storageType || 'memory' // 'memory' or 'storage'
      };

      // Check cache first
      const cache = config.metadata.storageType === 'storage' ? storageCache : memoryCache;
      const cachedResponse = cache.get(config.metadata.cacheKey);

      if (cachedResponse) {
        // If from cache, end timing immediately with success
        endTiming(timing, true, { fromCache: true });
        // Return cached response as a resolved promise
        return Promise.resolve({
          ...cachedResponse,
          metadata: {
            fromCache: true,
            responseTime: 0
          }
        });
      }
    }

    return config;
  },
  (error) => {
    console.error('Request interceptor error:', error);
    // End timing for request errors
    if (error.config && error.config.metadata && error.config.metadata.timing) {
      endTiming(error.config.metadata.timing, false, { error: error.message });
    }
    return Promise.reject(error);
  }
);

/**
 * Response interceptor for error handling and caching
 */
apiClient.interceptors.response.use(
  (response) => {
    // End performance timing for successful API calls
    if (response.config && response.config.metadata && response.config.metadata.timing) {
      endTiming(response.config.metadata.timing, true);
    }

    // Cache the response if caching is enabled for this request
    const config = response.config;
    // Ensure method is always available
    if (!config.method) {
      config.method = 'get';
    }
    const method = (config.method || 'get').toLowerCase();
    if (method === 'get' && config.metadata?.useCache) {
      const cache = config.metadata.storageType === 'storage' ? storageCache : memoryCache;
      cache.set(config.metadata.cacheKey, response, config.metadata.cacheTime);

      // Add response time for performance monitoring
      response.metadata = {
        ...response.metadata,
        responseTime: new Date().getTime() - config.metadata.startTime,
        fromCache: false
      };
    }

    return response;
  },
  async (error) => {
    const originalRequest = error.config;

    // End performance timing for failed API calls
    if (originalRequest && originalRequest.metadata && originalRequest.metadata.timing) {
      endTiming(originalRequest.metadata.timing, false, { error: error.message });
    }

    // Handle token refresh
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;
      try {
        const newToken = await refreshToken();
        originalRequest.headers.Authorization = `Bearer ${newToken}`;
        return apiClient(originalRequest);
      } catch (refreshError) {
        // Handle refresh token failure
        toast.error('Session expired. Please login again.');
        return Promise.reject(refreshError);
      }
    }

    // Handle retry logic for network errors
    const { config } = error;
    const MAX_RETRIES = parseInt(import.meta.env.VITE_APP_API_RETRY_ATTEMPTS || '3');
    const RETRY_DELAY = parseInt(import.meta.env.VITE_APP_API_RETRY_DELAY || '1000');

    if (config && config.retry && error.code === 'NETWORK_ERROR') {
      config.__retryCount = config.__retryCount || 0;

      if (config.__retryCount < MAX_RETRIES) {
        config.__retryCount += 1;

        // Create new promise to handle retry delay
        const backoff = new Promise((resolve) => {
          setTimeout(() => {
            resolve();
          }, RETRY_DELAY * config.__retryCount);
        });

        await backoff;
        return apiClient(config);
      }
    }

    // Enhanced error handling with error service
    if (!originalRequest?.skipErrorInterceptor) {
      // Use the error handling service for comprehensive error management
      const endpoint = originalRequest?.url || 'unknown';
      const requestData = originalRequest?.data || {};

      errorHandlingService.handleApiError(error, endpoint, requestData);
    }

    return Promise.reject(error);
  }
);

/**
 * Generate a cache key from request config
 * @param {Object} config - Axios request config
 * @returns {string} Cache key
 */
function generateCacheKey(config) {
  const { url, params, data } = config;
  return `${url}|${JSON.stringify(params || {})}|${JSON.stringify(data || {})}`;
}

/**
 * Generic API request wrapper with error handling
 * @param {string} method - HTTP method
 * @param {string} url - API endpoint
 * @param {Object} data - Request data
 * @param {Object} config - Additional axios config
 * @returns {Promise} API response
 */
export const apiRequest = async (method, url, data = null, config = {}) => {
  try {
    // Normalize method to prevent axios internal errors
    const normalizedMethod = String(method || 'GET').toUpperCase();

    // Create a clean request configuration
    const requestConfig = {
      method: normalizedMethod,
      url: url,
      baseURL: '/api', // Use proxy in development
      headers: {
        'Content-Type': 'application/json',
        ...config.headers
      },
      timeout: 30000,
      ...config
    };

    // Add authentication token if available
    const token = getToken();
    if (token) {
      requestConfig.headers.Authorization = `Bearer ${token}`;
    }

    // Add data for non-GET requests
    if (['POST', 'PUT', 'PATCH'].includes(normalizedMethod) && data) {
      requestConfig.data = data;
    }

    // Add params for GET requests
    if (normalizedMethod === 'GET' && data) {
      requestConfig.params = data;
    }

    // Use fetch as fallback for problematic requests
    if (normalizedMethod === 'GET') {
      return await fetchFallback(requestConfig);
    }

    const response = await apiClient.request(requestConfig);
    return response.data;
  } catch (error) {
    console.error('API Request Error:', error);

    // Try fetch fallback for GET requests
    if (String(method || 'GET').toUpperCase() === 'GET') {
      try {
        return await fetchFallback({
          method: 'GET',
          url: url,
          baseURL: '/api', // Use proxy in development
          headers: {
            'Content-Type': 'application/json',
            'Authorization': getToken() ? `Bearer ${getToken()}` : undefined
          },
          params: data
        });
      } catch (fetchError) {
        console.error('Fetch fallback also failed:', fetchError);
      }
    }

    // Re-throw with additional context
    const enhancedError = new Error(
      error.response?.data?.message || error.message || 'API request failed'
    );
    enhancedError.status = error.response?.status;
    enhancedError.originalError = error;
    throw enhancedError;
  }
};

/**
 * Fetch-based fallback for problematic axios requests
 * @param {Object} config - Request configuration
 * @returns {Promise} Response data
 */
const fetchFallback = async (config) => {
  try {
    let url = `${config.baseURL}${config.url}`;

    // Add query parameters for GET requests
    if (config.params && Object.keys(config.params).length > 0) {
      const searchParams = new URLSearchParams();
      Object.entries(config.params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          searchParams.append(key, value.toString());
        }
      });
      url += `?${searchParams.toString()}`;
    }

    const fetchOptions = {
      method: config.method || 'GET',
      headers: {}
    };

    // Add headers, filtering out undefined values
    Object.entries(config.headers || {}).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        fetchOptions.headers[key] = value;
      }
    });

    // Add body for non-GET requests
    if (['POST', 'PUT', 'PATCH'].includes(config.method) && config.data) {
      fetchOptions.body = JSON.stringify(config.data);
    }

    const response = await fetch(url, fetchOptions);

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Fetch fallback error:', error);
    throw error;
  }
};

/**
 * Safe API request wrapper that handles axios internal issues
 * @param {Object} config - Request configuration
 * @returns {Promise} API response
 */
export const safeApiRequest = async (config) => {
  // Handle both string URLs and config objects
  const baseConfig = typeof config === 'string' ? { url: config } : config;

  // Normalize config to prevent axios internal issues
  const normalizedConfig = {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json'
    },
    ...baseConfig,
    // Ensure method is always uppercase string
    method: String(baseConfig.method || 'GET').toUpperCase(),
    // Disable error interceptor to prevent console spam
    skipErrorInterceptor: true
  };

  // Add authentication if available
  const token = getToken();
  if (token) {
    normalizedConfig.headers.Authorization = `Bearer ${token}`;
  }

  // Try fetch first to avoid axios issues entirely
  try {
    const baseURL = '/api'; // Use proxy in development
    // Avoid double /api if URL already starts with /api
    let fullUrl = normalizedConfig.url.startsWith('/api')
      ? normalizedConfig.url
      : `${baseURL}${normalizedConfig.url}`;

    // Handle query parameters for GET requests
    if (normalizedConfig.method === 'GET' && normalizedConfig.params && Object.keys(normalizedConfig.params).length > 0) {
      const searchParams = new URLSearchParams(normalizedConfig.params);
      fullUrl += `?${searchParams.toString()}`;
    }

    const fetchOptions = {
      method: normalizedConfig.method,
      headers: normalizedConfig.headers
    };

    // Add body for non-GET requests
    if (['POST', 'PUT', 'PATCH'].includes(normalizedConfig.method) && normalizedConfig.data) {
      fetchOptions.body = JSON.stringify(normalizedConfig.data);
    }

    const fetchResponse = await fetch(fullUrl, fetchOptions);

    if (!fetchResponse.ok) {
      throw new Error(`HTTP ${fetchResponse.status}: ${fetchResponse.statusText}`);
    }

    const data = await fetchResponse.json();

    // Return in axios-compatible format
    return {
      data,
      status: fetchResponse.status,
      statusText: fetchResponse.statusText,
      headers: fetchResponse.headers
    };
  } catch (fetchError) {
    // If fetch fails, fall back to axios
    console.warn('Fetch failed, falling back to axios:', fetchError.message);
  }

  // Fallback to axios for non-GET requests or if fetch failed
  try {
    const response = await apiClient.request(normalizedConfig);
    return response;
  } catch (error) {
    // Handle axios internal errors gracefully
    if (error.message?.includes('toUpperCase')) {
      // This shouldn't happen now since we try fetch first for GET requests
      console.warn('Axios toUpperCase error occurred despite fetch-first approach');
    }
    throw error;
  }
};

export { apiClient };
