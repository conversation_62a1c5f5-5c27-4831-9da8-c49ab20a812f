import { get, post, put, del } from './unifiedApiClient';

const BASE_URL = '/users';

const users = {
  /**
   * Get all users (admin only)
   * @param {Object} filters - Filter parameters
   * @returns {Promise<Array>} List of users
   */
  getAll: async (filters = {}) => {
    try {
      const response = await get(BASE_URL, { params: filters });
      return response.data?.data || response.data || response;
    } catch (error) {
      console.error('Error fetching users:', error);
      throw error;
    }
  },

  /**
   * Get user by ID
   * @param {string} userId - The ID of the user to fetch
   * @returns {Promise<Object>} User details
   */
  getById: async (userId) => {
    try {
      const response = await safeApiRequest({
        method: 'GET',
        url: `${BASE_URL}/${userId}`
      });
      return response.data?.data || response.data || response;
    } catch (error) {
      console.error(`Error fetching user ${userId}:`, error);
      throw error;
    }
  },

  /**
   * Create a new user (admin only)
   * @param {Object} userData - The user data
   * @returns {Promise<Object>} Created user
   */
  create: async (userData) => {
    try {
      const response = await safeApiRequest({
        method: 'POST',
        url: BASE_URL,
        data: userData
      });
      return response.data?.data || response.data || response;
    } catch (error) {
      console.error('Error creating user:', error);
      throw error;
    }
  },

  /**
   * Update user
   * @param {string} userId - The ID of the user to update
   * @param {Object} updateData - The data to update
   * @returns {Promise<Object>} Updated user
   */
  update: async (userId, updateData) => {
    try {
      const response = await safeApiRequest({
        method: 'PUT',
        url: `${BASE_URL}/${userId}`,
        data: updateData
      });
      return response.data?.data || response.data || response;
    } catch (error) {
      console.error(`Error updating user ${userId}:`, error);
      throw error;
    }
  },

  /**
   * Delete user (admin only)
   * @param {string} userId - The ID of the user to delete
   * @returns {Promise<Object>} Deletion result
   */
  delete: async (userId) => {
    try {
      const response = await safeApiRequest({
        method: 'DELETE',
        url: `${BASE_URL}/${userId}`
      });
      return response.data?.data || response.data || response;
    } catch (error) {
      console.error(`Error deleting user ${userId}:`, error);
      throw error;
    }
  },

  /**
   * Get user's orders
   * @param {string} userId - The ID of the user
   * @returns {Promise<Array>} List of user's orders
   */
  getOrders: async (userId) => {
    try {
      const response = await safeApiRequest({
        method: 'GET',
        url: `${BASE_URL}/${userId}/orders`
      });
      return response.data?.data || response.data || response;
    } catch (error) {
      console.error(`Error fetching orders for user ${userId}:`, error);
      throw error;
    }
  },

  /**
   * Get user's order history (Phone Point Dar)
   * @param {string} userId - The ID of the user
   * @returns {Promise<Array>} List of user's phone orders
   */
  getOrderHistory: async (userId) => {
    try {
      const response = await safeApiRequest({
        method: 'GET',
        url: `${BASE_URL}/${userId}/orders`
      });
      return response.data?.data || response.data || response;
    } catch (error) {
      console.error(`Error fetching order history for user ${userId}:`, error);
      throw error;
    }
  },


};

export default users;
