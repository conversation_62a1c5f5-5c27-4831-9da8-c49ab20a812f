import { safeApiRequest } from './apiClient';

const BASE_URL = '/payments';

const payments = {
  /**
   * Process a payment
   * @param {Object} paymentData - The payment data
   * @returns {Promise<Object>} Payment result
   */
  process: async (paymentData) => {
    try {
      const response = await safeApiRequest({
        method: 'POST',
        url: `${BASE_URL}/process`,
        data: paymentData
      });
      return response.data;
    } catch (error) {
      console.error('Error processing payment:', error);
      throw error;
    }
  },

  /**
   * Get payment status
   * @param {string} paymentId - The ID of the payment
   * @returns {Promise<Object>} Payment status
   */
  getStatus: async (paymentId) => {
    try {
      const response = await safeApiRequest({
        method: 'GET',
        url: `${BASE_URL}/${paymentId}/status`
      });
      return response.data;
    } catch (error) {
      console.error(`Error getting payment status for ${paymentId}:`, error);
      throw error;
    }
  },

  /**
   * Get payment history for the current user
   * @returns {Promise<Array>} List of payments
   */
  getHistory: async () => {
    try {
      const response = await safeApiRequest({
        method: 'GET',
        url: `${BASE_URL}/history`
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching payment history:', error);
      throw error;
    }
  },

  /**
   * Refund a payment
   * @param {string} paymentId - The ID of the payment to refund
   * @param {Object} refundData - The refund data
   * @returns {Promise<Object>} Refund result
   */
  refund: async (paymentId, refundData) => {
    try {
      const response = await safeApiRequest({
        method: 'POST',
        url: `${BASE_URL}/${paymentId}/refund`,
        data: refundData
      });
      return response.data;
    } catch (error) {
      console.error(`Error refunding payment ${paymentId}:`, error);
      throw error;
    }
  },

  /**
   * ClickPesa specific payment methods
   */
  clickpesa: {
    /**
     * Check ClickPesa payment status for an order
     * @param {string} orderId - The order ID
     * @returns {Promise<Object>} Payment status
     */
    getOrderStatus: async (orderId) => {
      try {
        const response = await safeApiRequest({
          method: 'GET',
          url: `/payments/clickpesa/status/${orderId}`
        });
        return response.data;
      } catch (error) {
        console.error(`Error getting ClickPesa status for order ${orderId}:`, error);
        throw error;
      }
    },

    /**
     * Verify ClickPesa payment
     * @param {string} transactionId - The transaction ID
     * @returns {Promise<Object>} Verification result
     */
    verify: async (transactionId) => {
      try {
        const response = await safeApiRequest({
          method: 'POST',
          url: `${BASE_URL}/verify`,
          data: { transaction_id: transactionId }
        });
        return response.data;
      } catch (error) {
        console.error(`Error verifying ClickPesa payment ${transactionId}:`, error);
        throw error;
      }
    }
  }
};

export default payments;