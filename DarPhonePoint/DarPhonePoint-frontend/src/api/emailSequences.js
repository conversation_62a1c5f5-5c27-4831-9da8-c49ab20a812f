import { get, post, put, del } from './unifiedApiClient';

const BASE_URL = '/email-sequences';

const emailSequences = {
  /**
   * Get all email sequences
   * @returns {Promise<Array>} List of email sequences
   */
  getAll: async () => {
    try {
      const response = await get(BASE_URL);
      return response.data?.data || response.data || response;
    } catch (error) {
      console.error('Error fetching email sequences:', error);
      throw error;
    }
  },

  /**
   * Get a specific email sequence by ID
   * @param {string} sequenceId - The ID of the sequence to fetch
   * @returns {Promise<Object>} Email sequence details
   */
  getById: async (sequenceId) => {
    try {
      const response = await safeApiRequest({
        method: 'GET',
        url: `${BASE_URL}/${sequenceId}`
      });
      return response.data?.data || response.data || response;
    } catch (error) {
      console.error('Error fetching email sequence:', error);
      throw error;
    }
  },

  /**
   * Create a new email sequence
   * @param {Object} sequenceData - The sequence data
   * @returns {Promise<Object>} Created sequence
   */
  create: async (sequenceData) => {
    try {
      const response = await safeApiRequest({
        method: 'POST',
        url: BASE_URL,
        data: sequenceData
      });
      return response.data?.data || response.data || response;
    } catch (error) {
      console.error('Error creating email sequence:', error);
      throw error;
    }
  },

  /**
   * Update an email sequence
   * @param {string} sequenceId - The ID of the sequence to update
   * @param {Object} sequenceData - The updated sequence data
   * @returns {Promise<Object>} Updated sequence
   */
  update: async (sequenceId, sequenceData) => {
    try {
      const response = await safeApiRequest({
        method: 'PUT',
        url: `${BASE_URL}/${sequenceId}`,
        data: sequenceData
      });
      return response.data?.data || response.data || response;
    } catch (error) {
      console.error('Error updating email sequence:', error);
      throw error;
    }
  },

  /**
   * Delete an email sequence
   * @param {string} sequenceId - The ID of the sequence to delete
   * @returns {Promise<Object>} Deletion confirmation
   */
  delete: async (sequenceId) => {
    try {
      const response = await safeApiRequest({
        method: 'DELETE',
        url: `${BASE_URL}/${sequenceId}`
      });
      return response.data?.data || response.data || response;
    } catch (error) {
      console.error('Error deleting email sequence:', error);
      throw error;
    }
  },

  /**
   * Subscribe a user to an email sequence
   * @param {string} sequenceId - The ID of the sequence
   * @param {string} userId - The ID of the user
   * @returns {Promise<Object>} Subscription confirmation
   */
  subscribe: async (sequenceId, userId) => {
    try {
      const response = await safeApiRequest({
        method: 'POST',
        url: `${BASE_URL}/${sequenceId}/subscribe`,
        data: { userId }
      });
      return response.data?.data || response.data || response;
    } catch (error) {
      console.error('Error subscribing to email sequence:', error);
      throw error;
    }
  },

  /**
   * Unsubscribe a user from an email sequence
   * @param {string} sequenceId - The ID of the sequence
   * @param {string} userId - The ID of the user
   * @returns {Promise<Object>} Unsubscription confirmation
   */
  unsubscribe: async (sequenceId, userId) => {
    try {
      const response = await safeApiRequest({
        method: 'POST',
        url: `${BASE_URL}/${sequenceId}/unsubscribe`,
        data: { userId }
      });
      return response.data?.data || response.data || response;
    } catch (error) {
      console.error('Error unsubscribing from email sequence:', error);
      throw error;
    }
  },

  /**
   * Get email sequence analytics
   * @param {string} sequenceId - The ID of the sequence
   * @returns {Promise<Object>} Sequence analytics
   */
  getAnalytics: async (sequenceId) => {
    try {
      const response = await safeApiRequest({
        method: 'GET',
        url: `${BASE_URL}/${sequenceId}/analytics`
      });
      return response.data?.data || response.data || response;
    } catch (error) {
      console.error('Error fetching email sequence analytics:', error);
      throw error;
    }
  }
};

export default emailSequences;
