import { safeApiRequest } from './apiClient';

const BASE_URL = '/audit-logs';

const auditLogs = {
  /**
   * Get all audit logs with pagination and filtering
   * @param {Object} params - Query parameters
   * @returns {Promise<Object>} Audit logs with pagination
   */
  getAll: async (params = {}) => {
    try {
      const response = await safeApiRequest({
        method: 'GET',
        url: BASE_URL,
        params
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching audit logs:', error);
      throw error;
    }
  },

  /**
   * Get recent audit logs
   * @param {number} limit - Number of logs to return
   * @returns {Promise<Array>} Recent audit logs
   */
  getRecent: async (limit = 100) => {
    try {
      const response = await safeApiRequest({
        method: 'GET',
        url: `${BASE_URL}/recent`,
        params: { limit }
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching recent audit logs:', error);
      throw error;
    }
  },

  /**
   * Get audit logs for a specific resource
   * @param {string} type - Resource type
   * @param {string} id - Resource ID
   * @param {number} limit - Number of logs to return
   * @returns {Promise<Array>} Resource audit logs
   */
  getResourceLogs: async (type, id, limit = 100) => {
    try {
      const response = await safeApiRequest({
        method: 'GET',
        url: `${BASE_URL}/resource/${type}/${id}`,
        params: { limit }
      });
      return response.data;
    } catch (error) {
      console.error(`Error fetching audit logs for ${type} ${id}:`, error);
      throw error;
    }
  },

  /**
   * Get audit logs for a specific user
   * @param {string} userId - User ID
   * @param {number} limit - Number of logs to return
   * @returns {Promise<Array>} User audit logs
   */
  getUserLogs: async (userId, limit = 100) => {
    try {
      const response = await safeApiRequest({
        method: 'GET',
        url: `${BASE_URL}/user/${userId}`,
        params: { limit }
      });
      return response.data;
    } catch (error) {
      console.error(`Error fetching audit logs for user ${userId}:`, error);
      throw error;
    }
  },

  /**
   * Create an audit log entry
   * @param {Object} logData - Audit log data
   * @returns {Promise<Object>} Created audit log
   */
  create: async (logData) => {
    try {
      const response = await safeApiRequest({
        method: 'POST',
        url: BASE_URL,
        data: logData
      });
      return response.data;
    } catch (error) {
      console.error('Error creating audit log:', error);
      throw error;
    }
  },

  /**
   * Get audit log statistics
   * @param {Object} params - Query parameters
   * @returns {Promise<Object>} Audit log statistics
   */
  getStats: async (params = {}) => {
    try {
      const response = await safeApiRequest({
        method: 'GET',
        url: `${BASE_URL}/stats`,
        params
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching audit log stats:', error);
      throw error;
    }
  },

  /**
   * Export audit logs as CSV
   * @param {Object} params - Query parameters
   * @returns {Promise<string>} CSV content
   */
  exportCSV: async (params = {}) => {
    try {
      const response = await safeApiRequest({
        method: 'GET',
        url: `${BASE_URL}/export`,
        params: { ...params, format: 'csv' },
        responseType: 'text'
      });
      return response.data;
    } catch (error) {
      console.error('Error exporting audit logs:', error);
      throw error;
    }
  }
};

export default auditLogs;
