import { safeApiRequest } from './apiClient';
import DataTransformService from '../services/dataTransformService';

const BASE_URL = '/products';

const products = {
  /**
   * Get all products with optional filtering
   * @param {Object} filters - Filter parameters
   * @returns {Promise<Object>} Standardized response with transformed products
   */
  getAll: async (filters = {}) => {
    try {
      const response = await safeApiRequest({
        method: 'GET',
        url: BASE_URL,
        params: filters
      });

      // Handle standardized API response format
      const apiData = response.data || response;
      const products = apiData.data || apiData;

      // Transform products using DataTransformService
      const transformedProducts = Array.isArray(products)
        ? products.map(product => DataTransformService.transformProduct(product))
        : [];

      return {
        success: apiData.success !== false,
        data: transformedProducts,
        meta: apiData.meta || {},
        message: apiData.message || 'Products retrieved successfully'
      };
    } catch (error) {
      console.error('Error fetching products:', error);
      throw error;
    }
  },

  /**
   * Get a specific product by ID
   * @param {string} productId - The ID of the product to fetch
   * @returns {Promise<Object>} Transformed product details
   */
  getById: async (productId) => {
    try {
      const response = await safeApiRequest({
        method: 'GET',
        url: `${BASE_URL}/${productId}`
      });

      // Handle standardized API response format
      const apiData = response.data || response;
      const product = apiData.data || apiData;

      // Transform product using DataTransformService
      const transformedProduct = DataTransformService.transformProduct(product);

      return {
        success: apiData.success !== false,
        data: transformedProduct,
        message: apiData.message || 'Product retrieved successfully'
      };
    } catch (error) {
      console.error(`Error fetching product ${productId}:`, error);
      throw error;
    }
  },

  /**
   * Create a new product (admin only)
   * @param {Object} productData - The product data
   * @returns {Promise<Object>} Created product
   */
  create: async (productData) => {
    try {
      const response = await safeApiRequest({
        method: 'POST',
        url: BASE_URL,
        data: productData
      });
      return response.data;
    } catch (error) {
      console.error('Error creating product:', error);
      throw error;
    }
  },

  /**
   * Update an existing product (admin only)
   * @param {string} productId - The ID of the product to update
   * @param {Object} updateData - The data to update
   * @returns {Promise<Object>} Updated product
   */
  update: async (productId, updateData) => {
    try {
      const response = await safeApiRequest({
        method: 'PUT',
        url: `${BASE_URL}/${productId}`,
        data: updateData
      });
      return response.data;
    } catch (error) {
      console.error(`Error updating product ${productId}:`, error);
      throw error;
    }
  },

  /**
   * Delete a product (admin only)
   * @param {string} productId - The ID of the product to delete
   * @returns {Promise<Object>} Deletion result
   */
  delete: async (productId) => {
    try {
      const response = await safeApiRequest({
        method: 'DELETE',
        url: `${BASE_URL}/${productId}`
      });
      return response.data;
    } catch (error) {
      console.error(`Error deleting product ${productId}:`, error);
      throw error;
    }
  },

  /**
   * Get product categories
   * @returns {Promise<Array>} List of categories
   */
  getCategories: async () => {
    try {
      const response = await safeApiRequest({
        method: 'GET',
        url: `${BASE_URL}/categories`
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching categories:', error);
      throw error;
    }
  },

  /**
   * Get products by category
   * @param {string} categoryId - The category ID
   * @returns {Promise<Array>} List of products in category
   */
  getByCategory: async (categoryId) => {
    try {
      const response = await safeApiRequest({
        method: 'GET',
        url: `${BASE_URL}/category/${categoryId}`
      });
      return response.data;
    } catch (error) {
      console.error(`Error fetching products for category ${categoryId}:`, error);
      throw error;
    }
  },

  /**
   * Search products
   * @param {string} query - Search query
   * @returns {Promise<Array>} Search results
   */
  search: async (query) => {
    try {
      const response = await safeApiRequest({
        method: 'GET',
        url: `${BASE_URL}/search`,
        params: { q: query }
      });
      return response.data;
    } catch (error) {
      console.error('Error searching products:', error);
      throw error;
    }
  },

  /**
   * Get featured products
   * @returns {Promise<Array>} List of featured products
   */
  getFeatured: async () => {
    try {
      const response = await safeApiRequest({
        method: 'GET',
        url: `${BASE_URL}/featured`
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching featured products:', error);
      throw error;
    }
  },

  /**
   * Get related products
   * @param {string} productId - The ID of the product
   * @returns {Promise<Array>} List of related products
   */
  getRelated: async (productId) => {
    try {
      const response = await safeApiRequest({
        method: 'GET',
        url: `${BASE_URL}/${productId}/related`
      });
      return response.data;
    } catch (error) {
      console.error(`Error fetching related products for ${productId}:`, error);
      throw error;
    }
  }
};

export default products;