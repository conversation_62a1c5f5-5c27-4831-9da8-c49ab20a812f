/**
 * Production-ready API module
 * Centralized API client with all service modules
 */

// Import all API services
import analytics from './analytics';
import auditLogs from './auditLogs';
import auth from './auth';
import email from './email';
import emailSequences from './emailSequences';
import health from './health';
import leads from './leads';
import orders from './orders';
import payments from './payments';
import products from './products';
import users from './users';

// Export the centralized API client
export { apiClient } from './apiClient';

// Create combined API object for easier imports
const api = {
  analytics,
  auditLogs,
  auth,
  email,
  emailSequences,
  health,
  leads,
  orders,
  payments,
  products,
  users
};

// Export individual services
export {
  analytics,
  auditLogs,
  auth,
  email,
  emailSequences,
  health,
  leads,
  orders,
  payments,
  products,
  users
};

// Export combined API as default
export default api;
