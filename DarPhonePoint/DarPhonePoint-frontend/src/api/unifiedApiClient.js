/**
 * Unified API Client for Phone Point Dar
 * Consolidates all API client patterns into a single, consistent interface
 */

import axios from 'axios';
import { toast } from 'react-toastify';
import { handleApiError, ERROR_TYPES } from '../utils/standardErrorHandler';

// Simple cache for GET requests
const requestCache = new Map();
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

// Helper functions
const getToken = () => localStorage.getItem('token');
const removeToken = () => {
  localStorage.removeItem('token');
  localStorage.removeItem('user');
};

// Create axios instance
const apiClient = axios.create({
  baseURL: import.meta.env.VITE_API_URL || '/api',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor for authentication
apiClient.interceptors.request.use(
  (config) => {
    const token = getToken();
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Response interceptor for standardized error handling
apiClient.interceptors.response.use(
  (response) => response,
  async (error) => {
    // Use standardized error handling
    const handledError = handleApiError(error, {
      showToast: !error.config?.silent,
      logError: true,
      onAuthError: () => {
        removeToken();
        // Could trigger redirect to login here
      }
    });

    // Handle network errors
    if (!error.response) {
      console.error('Network error:', error.message);
      if (!error.config?.silent) {
        toast.error('Network error. Please check your connection.');
      }
    }

    return Promise.reject(error);
  }
);

/**
 * Generate cache key for request
 */
const generateCacheKey = (config) => {
  const { url, params, data } = config;
  return `${url}|${JSON.stringify(params || {})}|${JSON.stringify(data || {})}`;
};

/**
 * Check if cached data is still valid
 */
const isCacheValid = (cachedData) => {
  return cachedData && (Date.now() - cachedData.timestamp) < CACHE_DURATION;
};

/**
 * Main API request function - consolidates all patterns
 * @param {Object} config - Request configuration
 * @returns {Promise} API response
 */
export const apiRequest = async (config) => {
  try {
    // Normalize config
    const normalizedConfig = {
      method: 'GET',
      ...config,
      method: String(config.method || 'GET').toUpperCase(),
    };

    // Check cache for GET requests
    if (normalizedConfig.method === 'GET' && !normalizedConfig.skipCache) {
      const cacheKey = generateCacheKey(normalizedConfig);
      const cachedData = requestCache.get(cacheKey);
      
      if (isCacheValid(cachedData)) {
        return {
          data: cachedData.data,
          fromCache: true,
        };
      }
    }

    // Make the request
    const response = await apiClient.request(normalizedConfig);

    // Cache GET requests
    if (normalizedConfig.method === 'GET' && !normalizedConfig.skipCache) {
      const cacheKey = generateCacheKey(normalizedConfig);
      requestCache.set(cacheKey, {
        data: response.data,
        timestamp: Date.now(),
      });
    }

    return response;
  } catch (error) {
    // Enhanced error handling with user-friendly messages
    let errorMessage = 'Request failed';

    if (error.response?.data?.message) {
      errorMessage = error.response.data.message;
    } else if (error.response?.data?.error) {
      errorMessage = error.response.data.error;
    } else if (error.message) {
      errorMessage = error.message;
    }

    // Log detailed error info for debugging
    console.error('API Request Error:', {
      url: config.url,
      method: config.method,
      error: errorMessage,
      status: error.response?.status,
      data: error.response?.data,
    });

    // Create enhanced error object
    const enhancedError = new Error(errorMessage);
    enhancedError.status = error.response?.status;
    enhancedError.response = error.response;
    enhancedError.config = config;

    throw enhancedError;
  }
};

/**
 * Convenience methods for different HTTP verbs
 */
export const get = (url, config = {}) => 
  apiRequest({ method: 'GET', url, ...config });

export const post = (url, data, config = {}) => 
  apiRequest({ method: 'POST', url, data, ...config });

export const put = (url, data, config = {}) => 
  apiRequest({ method: 'PUT', url, data, ...config });

export const patch = (url, data, config = {}) => 
  apiRequest({ method: 'PATCH', url, data, ...config });

export const del = (url, config = {}) => 
  apiRequest({ method: 'DELETE', url, ...config });

/**
 * Legacy compatibility - safeApiRequest
 * @param {Object} config - Request configuration
 * @returns {Promise} API response
 */
export const safeApiRequest = async (config) => {
  try {
    const response = await apiRequest(config);
    return response;
  } catch (error) {
    // Silent error handling for analytics and non-critical requests
    if (config.silent) {
      console.warn('Silent API request failed:', error.message);
      return { data: null, error: error.message };
    }
    throw error;
  }
};

/**
 * Clear cache
 */
export const clearCache = (key = null) => {
  if (key) {
    requestCache.delete(key);
  } else {
    requestCache.clear();
  }
};

/**
 * Health check
 */
export const checkHealth = () => get('/health');

// Default export with all methods
export default {
  request: apiRequest,
  get,
  post,
  put,
  patch,
  delete: del,
  safeApiRequest,
  clearCache,
  checkHealth,
  // Legacy compatibility
  apiRequest,
};
