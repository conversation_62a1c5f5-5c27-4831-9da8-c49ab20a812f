import { safeApiRequest } from './apiClient';

const BASE_URL = '/email';

const email = {
  /**
   * Send an email (admin only)
   * @param {Object} emailData - Email data
   * @returns {Promise<Object>} Send result
   */
  send: async (emailData) => {
    try {
      const response = await safeApiRequest({
        method: 'POST',
        url: `${BASE_URL}/send`,
        data: emailData
      });
      return response.data;
    } catch (error) {
      console.error('Error sending email:', error);
      throw error;
    }
  },

  /**
   * Get email statistics (admin only)
   * @returns {Promise<Object>} Email statistics
   */
  getStats: async () => {
    try {
      const response = await safeApiRequest({
        method: 'GET',
        url: `${BASE_URL}/stats`
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching email stats:', error);
      throw error;
    }
  },

  /**
   * Get available email templates (admin only)
   * @returns {Promise<Array>} List of templates
   */
  getTemplates: async () => {
    try {
      const response = await safeApiRequest({
        method: 'GET',
        url: `${BASE_URL}/templates`
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching email templates:', error);
      throw error;
    }
  },

  /**
   * Send test email (admin only)
   * @param {string} to - Recipient email
   * @returns {Promise<Object>} Send result
   */
  sendTest: async (to) => {
    try {
      const response = await safeApiRequest({
        method: 'POST',
        url: `${BASE_URL}/test`,
        data: { to }
      });
      return response.data;
    } catch (error) {
      console.error('Error sending test email:', error);
      throw error;
    }
  }
};

export default email;
