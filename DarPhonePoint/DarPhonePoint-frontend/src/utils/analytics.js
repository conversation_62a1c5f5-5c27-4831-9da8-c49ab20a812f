import { post } from '../api/unifiedApiClient';
import { v4 as uuidv4 } from 'uuid';

/**
 * Get or create a session ID for analytics tracking
 * @returns {string} Session ID
 */
export const getSessionId = () => {
  let sessionId = localStorage.getItem('session_id');

  if (!sessionId) {
    sessionId = uuidv4();
    localStorage.setItem('session_id', sessionId);
  }

  return sessionId;
};

/**
 * Get device type based on screen width
 * @returns {string} Device type (desktop, tablet, mobile)
 */
export const getDeviceType = () => {
  const width = window.innerWidth;

  if (width >= 1024) {
    return 'desktop';
  } else if (width >= 768) {
    return 'tablet';
  } else {
    return 'mobile';
  }
};

/**
 * Get browser information
 * @returns {string} Browser name
 */
export const getBrowser = () => {
  const userAgent = navigator.userAgent;

  if (userAgent.indexOf('Chrome') > -1) {
    return 'Chrome';
  } else if (userAgent.indexOf('Safari') > -1) {
    return 'Safari';
  } else if (userAgent.indexOf('Firefox') > -1) {
    return 'Firefox';
  } else if (userAgent.indexOf('MSIE') > -1 || userAgent.indexOf('Trident') > -1) {
    return 'Internet Explorer';
  } else if (userAgent.indexOf('Edge') > -1) {
    return 'Edge';
  } else {
    return 'Unknown';
  }
};

// Debounce analytics events to prevent spam
const eventQueue = new Map();
const lastEventTime = new Map();
const DEBOUNCE_DELAY = 2000; // 2 seconds
const MIN_EVENT_INTERVAL = 5000; // 5 seconds minimum between same events

/**
 * Track an analytics event with debouncing
 * @param {string} eventType - Type of event to track
 * @param {Object} metadata - Additional data to track
 */
export const trackEvent = async (eventType, metadata = {}) => {
  try {
    // Create a unique key for this event type and page
    const eventKey = `${eventType}_${window.location.pathname}`;
    const now = Date.now();

    // Check if we've sent this event recently (rate limiting)
    const lastTime = lastEventTime.get(eventKey);
    if (lastTime && (now - lastTime) < MIN_EVENT_INTERVAL) {
      if (import.meta.env.DEV) {
        console.log(`Analytics: Rate limited ${eventType} for ${window.location.pathname}`);
      }
      return; // Skip this event
    }

    // Clear existing timeout for this event
    if (eventQueue.has(eventKey)) {
      clearTimeout(eventQueue.get(eventKey));
    }

    // Set new timeout to debounce the event
    const timeoutId = setTimeout(async () => {
      try {
        const sessionId = getSessionId();
        const deviceType = getDeviceType();
        const browser = getBrowser();

        await post('/analytics/track', {
          event_type: eventType,
          session_id: sessionId,
          page_url: window.location.pathname,
          device_type: deviceType,
          browser,
          referrer: document.referrer,
          metadata
        }, { silent: true }); // Silent mode for analytics

        // Update last event time
        lastEventTime.set(eventKey, Date.now());
        eventQueue.delete(eventKey);
      } catch (error) {
        // Silently fail - analytics should not break the app
        if (import.meta.env.DEV) {
          console.warn('Analytics event failed:', eventType, error.message);
        }
        eventQueue.delete(eventKey);
      }
    }, DEBOUNCE_DELAY);

    eventQueue.set(eventKey, timeoutId);
  } catch (error) {
    // Silently fail in production - analytics should not break the app
    if (import.meta.env.DEV) {
      console.error('Error queuing analytics event:', error);
    }
  }
};

/**
 * Track page view
 * @param {string} pageName - Name of the page
 */
export const trackPageView = (pageName) => {
  trackEvent('page_view', { page_name: pageName });
};

/**
 * Track lead capture
 * @param {Object} leadData - Lead data
 */
export const trackLeadCapture = (leadData) => {
  trackEvent('lead_capture', leadData);
};

/**
 * Track product view
 * @param {Object} product - Product data
 */
export const trackProductView = (product) => {
  trackEvent('product_view', {
    product_id: product._id,
    product_name: product.name,
    product_type: product.product_type,
    price: product.price
  });
};

/**
 * Track add to cart
 * @param {Object} product - Product data
 * @param {number} quantity - Quantity added
 */
export const trackAddToCart = (product, quantity = 1) => {
  trackEvent('add_to_cart', {
    product_id: product._id,
    product_name: product.name,
    product_type: product.product_type,
    price: product.price,
    quantity
  });
};

/**
 * Track purchase attempt (direct Whop checkout)
 * @param {Object} product - Product data
 */
export const trackPurchaseAttempt = (product) => {
  trackEvent('purchase_attempt', {
    product_id: product._id,
    product_name: product.name,
    product_type: product.product_type,
    price: product.price,
    payment_provider: 'whop'
  });
};

/**
 * Track checkout start
 * @param {Array} cartItems - Cart items
 * @param {number} cartTotal - Cart total
 */
export const trackCheckoutStart = (cartItems, cartTotal) => {
  if (!Array.isArray(cartItems)) {
    if (import.meta.env.DEV) {
      console.warn('trackCheckoutStart: cartItems is not an array', cartItems);
    }
    return;
  }

  trackEvent('checkout_start', {
    cart_items: cartItems.map(item => ({
      product_id: item._id || item.product?._id,
      product_name: item.name || item.product?.name,
      quantity: item.quantity || 1,
      price: item.price || item.product?.price || 0
    })),
    cart_total: cartTotal || 0
  });
};

/**
 * Track checkout completion
 * @param {Object} order - Order data
 * @param {string} paymentMethod - Payment method used
 */
export const trackCheckoutComplete = (order, paymentMethod) => {
  if (!order || !Array.isArray(order.items)) {
    if (import.meta.env.DEV) {
      console.warn('trackCheckoutComplete: invalid order data', order);
    }
    return;
  }

  trackEvent('checkout_complete', {
    order_id: order._id,
    cart_items: order.items.map(item => ({
      product_id: item._id || item.product?._id,
      product_name: item.name || item.product?.name,
      quantity: item.quantity || 1,
      price: item.price || item.product?.price || 0
    })),
    total_amount: order.total || 0,
    payment_method: paymentMethod || 'unknown'
  });
};

/**
 * Track purchase
 * @param {Object} order - Order data
 */
export const trackPurchase = (order) => {
  trackEvent('purchase', {
    order_id: order._id,
    product_id: order.product._id,
    product_name: order.product.name,
    amount: order.amount,
    payment_method: order.payment_method
  });
};

/**
 * Track download
 * @param {Object} product - Product data
 */
export const trackDownload = (product) => {
  trackEvent('download', {
    product_id: product._id,
    product_name: product.name,
    product_type: product.product_type
  });
};
