/**
 * Guest Checkout Test Utilities
 * Helper functions to test guest checkout functionality in the browser
 */

import { addToGuestCart, getGuestCart, clearGuestCart } from '../services/guestCartService';

/**
 * Test adding products to guest cart
 */
export const testGuestCartFunctionality = () => {
  console.log('🧪 Testing Guest Cart Functionality...');
  
  // Clear any existing cart
  clearGuestCart();
  console.log('✅ Cleared existing cart');
  
  // Test product data (simulating a real product)
  const testProduct = {
    _id: '507f1f77bcf86cd799439011',
    name: 'Test USB-C Cable',
    price: 25000,
    images: ['test-image.jpg'],
    category: 'cable'
  };
  
  const testVariant = {
    sku: 'TEST-CABLE-1M',
    name: '1m USB-C Cable',
    price: 25000,
    color: 'Black',
    stock_quantity: 50
  };
  
  // Test adding to cart
  try {
    const cartItem = {
      productId: testProduct._id,
      variantSku: testVariant.sku,
      quantity: 2,
      price: testVariant.price,
      product: testProduct,
      variant: testVariant
    };
    
    addToGuestCart(cartItem);
    console.log('✅ Added item to guest cart');
    
    // Get cart and verify
    const cart = getGuestCart();
    console.log('✅ Retrieved guest cart:', cart);
    
    if (cart.items.length === 1) {
      console.log('✅ Cart has correct number of items');
    } else {
      console.error('❌ Cart item count mismatch');
    }
    
    if (cart.total === testVariant.price * 2) {
      console.log('✅ Cart total is correct');
    } else {
      console.error('❌ Cart total mismatch');
    }
    
    console.log('🎉 Guest cart functionality test passed!');
    return true;
    
  } catch (error) {
    console.error('❌ Guest cart test failed:', error);
    return false;
  }
};

/**
 * Test guest checkout data preparation
 */
export const testGuestCheckoutData = () => {
  console.log('🧪 Testing Guest Checkout Data Preparation...');
  
  const cart = getGuestCart();
  
  if (cart.items.length === 0) {
    console.log('⚠️ No items in cart, adding test item first...');
    testGuestCartFunctionality();
  }
  
  // Prepare checkout data
  const checkoutData = {
    guest_email: '<EMAIL>',
    guest_name: 'Test Guest',
    shipping_address: {
      first_name: 'Test',
      last_name: 'Guest',
      address_line_1: '123 Test Street',
      city: 'Dar es Salaam',
      state: 'Dar es Salaam',
      postal_code: '12345',
      country: 'Tanzania'
    },
    payment_method: 'mobile_money',
    is_guest: true,
    cart_items: cart.items.map(item => ({
      productId: item.productId,
      variantSku: item.variantSku,
      quantity: item.quantity,
      price: item.price
    }))
  };
  
  console.log('✅ Checkout data prepared:', checkoutData);
  console.log('✅ Guest email:', checkoutData.guest_email);
  console.log('✅ Shipping address:', checkoutData.shipping_address);
  console.log('✅ Cart items:', checkoutData.cart_items.length);
  
  return checkoutData;
};

/**
 * Display guest checkout instructions
 */
export const displayGuestCheckoutInstructions = () => {
  console.log(`
🎯 GUEST CHECKOUT TESTING INSTRUCTIONS

1. 📱 Browse Products:
   - Go to /products
   - Click on any product
   - Select variants if available
   - Click "Add to Cart" (no login required)

2. 🛒 View Cart:
   - Go to /cart
   - Verify items are displayed
   - Update quantities if needed
   - Click "Proceed to Checkout"

3. 📝 Checkout Process:
   - Fill in guest information (email, name)
   - Enter shipping address
   - Select payment method
   - Review order
   - Submit order

4. ✅ Expected Results:
   - No login required at any step
   - Cart persists in localStorage
   - Order validation passes
   - Payment processing initiated

5. 🧪 Test Different Product Types:
   - Simple products (cables, tools)
   - Variant products (cases, chargers)
   - IMEI products (smartphones) - for authenticated users

6. 📱 Mobile Testing:
   - Test on tablet (warehouse staff)
   - Test on mobile devices
   - Verify responsive design

Run testGuestCartFunctionality() in console to test cart functions.
Run testGuestCheckoutData() to prepare checkout data.
  `);
};

/**
 * Validate guest checkout requirements
 */
export const validateGuestCheckoutRequirements = () => {
  console.log('🔍 Validating Guest Checkout Requirements...');
  
  const requirements = [
    {
      name: 'localStorage Support',
      test: () => typeof Storage !== 'undefined',
      description: 'Browser supports localStorage for guest cart'
    },
    {
      name: 'Guest Cart Service',
      test: () => typeof addToGuestCart === 'function',
      description: 'Guest cart service functions are available'
    },
    {
      name: 'API Connectivity',
      test: () => fetch('/api/products?limit=1').then(r => r.ok).catch(() => false),
      description: 'Backend API is accessible'
    },
    {
      name: 'Product Data',
      test: async () => {
        try {
          const response = await fetch('/api/products?limit=1');
          const data = await response.json();
          return data.data && data.data.length > 0;
        } catch {
          return false;
        }
      },
      description: 'Products are available for testing'
    }
  ];
  
  requirements.forEach(async (req) => {
    try {
      const result = await req.test();
      if (result) {
        console.log(`✅ ${req.name}: ${req.description}`);
      } else {
        console.log(`❌ ${req.name}: ${req.description}`);
      }
    } catch (error) {
      console.log(`❌ ${req.name}: Error - ${error.message}`);
    }
  });
  
  console.log('🎉 Validation complete!');
};

// Auto-run instructions when imported
if (typeof window !== 'undefined') {
  console.log('🎯 Guest Checkout Test Utils Loaded!');
  console.log('Run displayGuestCheckoutInstructions() for testing guide');
}

export default {
  testGuestCartFunctionality,
  testGuestCheckoutData,
  displayGuestCheckoutInstructions,
  validateGuestCheckoutRequirements
};
