import { trackEvent } from './analytics';

/**
 * Performance monitoring utility
 */
class PerformanceMonitor {
  constructor() {
    this.metrics = {};
    this.marks = {};
    this.measures = {};
    this.isEnabled = true;

    // Initialize performance observer if available
    if (typeof window !== 'undefined' && 'PerformanceObserver' in window) {
      this.initPerformanceObserver();
    }
  }

  /**
   * Initialize performance observer to track browser metrics
   */
  initPerformanceObserver() {
    try {
      // Create performance observer for paint metrics
      const paintObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          this.metrics[entry.name] = entry.startTime;

          // Track FCP and LCP as analytics events
          if (entry.name === 'first-contentful-paint' || entry.name === 'largest-contentful-paint') {
            this.trackPerformanceEvent(entry.name, { value: entry.startTime });
          }
        }
      });

      // Observe paint metrics
      paintObserver.observe({ entryTypes: ['paint', 'largest-contentful-paint'] });

      // Create performance observer for navigation metrics
      const navigationObserver = new PerformanceObserver((list) => {
        const navEntry = list.getEntries()[0];

        if (navEntry) {
          this.metrics.navigationTiming = {
            domComplete: navEntry.domComplete,
            domInteractive: navEntry.domInteractive,
            loadEventEnd: navEntry.loadEventEnd,
            responseEnd: navEntry.responseEnd,
            responseStart: navEntry.responseStart,
            fetchStart: navEntry.fetchStart,
            domContentLoadedEventEnd: navEntry.domContentLoadedEventEnd
          };

          // Track page load time as analytics event
          this.trackPerformanceEvent('page_load_time', {
            value: navEntry.loadEventEnd - navEntry.fetchStart
          });
        }
      });

      // Observe navigation metrics
      navigationObserver.observe({ entryTypes: ['navigation'] });

      // Create performance observer for resource metrics
      const resourceObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          // Only track resources that take longer than 500ms to load
          if (entry.duration > 500) {
            this.trackPerformanceEvent('slow_resource', {
              name: entry.name,
              duration: entry.duration,
              initiatorType: entry.initiatorType
            });
          }
        }
      });

      // Observe resource metrics
      resourceObserver.observe({ entryTypes: ['resource'] });
    } catch (error) {
      // Silently fail in production - performance monitoring should not break the app
      if (import.meta.env.DEV) {
        console.error('Error initializing performance observer:', error);
      }
    }
  }

  /**
   * Start timing a custom metric
   * @param {string} name - Metric name
   */
  startMark(name) {
    if (!this.isEnabled) return;

    try {
      this.marks[name] = performance.now();

      // Also use the Performance API if available
      if (typeof performance !== 'undefined' && performance.mark) {
        performance.mark(`${name}_start`);
      }
    } catch (error) {
      if (import.meta.env.DEV) {
        console.error(`Error starting mark ${name}:`, error);
      }
    }
  }

  /**
   * End timing a custom metric and record the duration
   * @param {string} name - Metric name
   * @param {boolean} trackAsEvent - Whether to track as an analytics event
   * @returns {number} Duration in milliseconds
   */
  endMark(name, trackAsEvent = true) {
    if (!this.isEnabled || !this.marks[name]) return 0;

    try {
      const endTime = performance.now();
      const startTime = this.marks[name];
      const duration = endTime - startTime;

      // Record the measure
      this.measures[name] = duration;

      // Also use the Performance API if available
      if (typeof performance !== 'undefined' && performance.mark && performance.measure) {
        performance.mark(`${name}_end`);
        performance.measure(name, `${name}_start`, `${name}_end`);
      }

      // Track as analytics event if requested
      if (trackAsEvent) {
        this.trackPerformanceEvent('custom_timing', {
          name,
          duration
        });
      }

      return duration;
    } catch (error) {
      if (import.meta.env.DEV) {
        console.error(`Error ending mark ${name}:`, error);
      }
      return 0;
    }
  }

  /**
   * Track a performance event via analytics
   * @param {string} eventType - Type of performance event
   * @param {Object} data - Event data
   */
  trackPerformanceEvent(eventType, data) {
    if (!this.isEnabled) return;

    try {
      trackEvent('performance', {
        performance_type: eventType,
        ...data,
        timestamp: Date.now()
      });
    } catch (error) {
      if (import.meta.env.DEV) {
        console.error(`Error tracking performance event ${eventType}:`, error);
      }
    }
  }

  /**
   * Get all recorded metrics
   * @returns {Object} All metrics
   */
  getAllMetrics() {
    return {
      browserMetrics: this.metrics,
      customMeasures: this.measures
    };
  }

  /**
   * Enable or disable performance monitoring
   * @param {boolean} enabled - Whether monitoring is enabled
   */
  setEnabled(enabled) {
    this.isEnabled = enabled;
  }

  /**
   * Clear all recorded metrics
   */
  clearMetrics() {
    this.metrics = {};
    this.marks = {};
    this.measures = {};
  }
}

// Create and export a singleton instance
export const performanceMonitor = new PerformanceMonitor();

/**
 * Higher-order function to measure component render time
 * This function is meant to be used with React components
 * but is defined here without React dependencies for testing purposes
 *
 * @param {Object} Component - React component to measure
 * @param {string} name - Component name
 * @returns {Function} Wrapped component with performance monitoring
 */
export const withPerformanceTracking = (Component, name = '') => {
  // This is a placeholder implementation for testing
  // The actual implementation would use React hooks
  return Component;
};
