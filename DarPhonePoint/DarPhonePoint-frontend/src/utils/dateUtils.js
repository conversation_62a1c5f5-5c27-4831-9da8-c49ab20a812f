/**
 * Date Utilities for Phone Point Dar
 * Centralized date formatting and manipulation functions
 */

/**
 * Format date for display in Phone Point Dar admin interface
 * @param {string|Date} date - Date to format
 * @param {object} options - Formatting options
 * @returns {string} Formatted date string
 */
export const formatDate = (date, options = {}) => {
  try {
    if (!date) return 'N/A';
    
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    
    // Check if date is valid
    if (isNaN(dateObj.getTime())) {
      return 'Invalid Date';
    }

    // Default options for Phone Point Dar (Tanzania context)
    const defaultOptions = {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      timeZone: 'Africa/Dar_es_Salaam',
      ...options
    };

    return new Intl.DateTimeFormat('en-TZ', defaultOptions).format(dateObj);
  } catch (error) {
    console.error('Error formatting date:', error);
    return 'Invalid Date';
  }
};

/**
 * Format date for admin tables (shorter format)
 * @param {string|Date} date - Date to format
 * @returns {string} Formatted date string
 */
export const formatDateShort = (date) => {
  return formatDate(date, {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
};

/**
 * Format date with time for detailed views
 * @param {string|Date} date - Date to format
 * @returns {string} Formatted date string with time
 */
export const formatDateTime = (date) => {
  return formatDate(date, {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
};

/**
 * Format date for Tanzania business context
 * @param {string|Date} date - Date to format
 * @returns {string} Formatted date string
 */
export const formatTanzanianDate = (date) => {
  return formatDate(date, {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    timeZone: 'Africa/Dar_es_Salaam'
  });
};

/**
 * Get relative time (e.g., "2 hours ago", "3 days ago")
 * @param {string|Date} date - Date to format
 * @returns {string} Relative time string
 */
export const getRelativeTime = (date) => {
  try {
    if (!date) return 'N/A';
    
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    const now = new Date();
    const diffInSeconds = Math.floor((now - dateObj) / 1000);

    if (diffInSeconds < 60) {
      return 'Just now';
    } else if (diffInSeconds < 3600) {
      const minutes = Math.floor(diffInSeconds / 60);
      return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
    } else if (diffInSeconds < 86400) {
      const hours = Math.floor(diffInSeconds / 3600);
      return `${hours} hour${hours > 1 ? 's' : ''} ago`;
    } else if (diffInSeconds < 2592000) {
      const days = Math.floor(diffInSeconds / 86400);
      return `${days} day${days > 1 ? 's' : ''} ago`;
    } else {
      return formatDateShort(dateObj);
    }
  } catch (error) {
    console.error('Error calculating relative time:', error);
    return 'N/A';
  }
};

/**
 * Check if date is today
 * @param {string|Date} date - Date to check
 * @returns {boolean} True if date is today
 */
export const isToday = (date) => {
  try {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    const today = new Date();
    
    return dateObj.toDateString() === today.toDateString();
  } catch (error) {
    return false;
  }
};

/**
 * Check if date is within the last N days
 * @param {string|Date} date - Date to check
 * @param {number} days - Number of days to check
 * @returns {boolean} True if date is within the last N days
 */
export const isWithinDays = (date, days) => {
  try {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    const now = new Date();
    const diffInDays = Math.floor((now - dateObj) / (1000 * 60 * 60 * 24));
    
    return diffInDays <= days;
  } catch (error) {
    return false;
  }
};

/**
 * Format date for input fields (YYYY-MM-DD)
 * @param {string|Date} date - Date to format
 * @returns {string} Formatted date string for input
 */
export const formatDateForInput = (date) => {
  try {
    if (!date) return '';
    
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    
    if (isNaN(dateObj.getTime())) {
      return '';
    }

    return dateObj.toISOString().split('T')[0];
  } catch (error) {
    console.error('Error formatting date for input:', error);
    return '';
  }
};

/**
 * Format time for input fields (HH:MM)
 * @param {string|Date} date - Date to format
 * @returns {string} Formatted time string for input
 */
export const formatTimeForInput = (date) => {
  try {
    if (!date) return '';
    
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    
    if (isNaN(dateObj.getTime())) {
      return '';
    }

    return dateObj.toTimeString().slice(0, 5);
  } catch (error) {
    console.error('Error formatting time for input:', error);
    return '';
  }
};

/**
 * Add business days to a date (excludes weekends)
 * @param {Date} date - Starting date
 * @param {number} days - Number of business days to add
 * @returns {Date} New date with business days added
 */
export const addBusinessDays = (date, days) => {
  const result = new Date(date);
  let addedDays = 0;
  
  while (addedDays < days) {
    result.setDate(result.getDate() + 1);
    
    // Skip weekends (Saturday = 6, Sunday = 0)
    if (result.getDay() !== 0 && result.getDay() !== 6) {
      addedDays++;
    }
  }
  
  return result;
};

/**
 * Get Tanzania business hours status
 * @param {Date} date - Date to check (defaults to now)
 * @returns {object} Business hours status
 */
export const getTanzaniaBusinessHours = (date = new Date()) => {
  const tanzaniaTime = new Date(date.toLocaleString("en-US", {timeZone: "Africa/Dar_es_Salaam"}));
  const hour = tanzaniaTime.getHours();
  const day = tanzaniaTime.getDay();
  
  // Business hours: Monday-Friday 8:00-18:00, Saturday 8:00-14:00
  const isWeekday = day >= 1 && day <= 5;
  const isSaturday = day === 6;
  const isSunday = day === 0;
  
  let isOpen = false;
  let nextOpenTime = '';
  
  if (isWeekday && hour >= 8 && hour < 18) {
    isOpen = true;
  } else if (isSaturday && hour >= 8 && hour < 14) {
    isOpen = true;
  }
  
  if (!isOpen) {
    if (isWeekday && hour < 8) {
      nextOpenTime = 'Opens at 8:00 AM today';
    } else if (isWeekday && hour >= 18) {
      nextOpenTime = 'Opens at 8:00 AM tomorrow';
    } else if (isSaturday && hour < 8) {
      nextOpenTime = 'Opens at 8:00 AM today';
    } else if (isSaturday && hour >= 14) {
      nextOpenTime = 'Opens at 8:00 AM Monday';
    } else if (isSunday) {
      nextOpenTime = 'Opens at 8:00 AM Monday';
    }
  }
  
  return {
    isOpen,
    nextOpenTime,
    currentTime: tanzaniaTime.toLocaleTimeString('en-TZ', {
      hour: '2-digit',
      minute: '2-digit',
      timeZone: 'Africa/Dar_es_Salaam'
    })
  };
};

// Export all functions as default object for easier importing
export default {
  formatDate,
  formatDateShort,
  formatDateTime,
  formatTanzanianDate,
  getRelativeTime,
  isToday,
  isWithinDays,
  formatDateForInput,
  formatTimeForInput,
  addBusinessDays,
  getTanzaniaBusinessHours
};
