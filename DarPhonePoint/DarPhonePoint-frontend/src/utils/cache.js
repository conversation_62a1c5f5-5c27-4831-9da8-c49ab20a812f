/**
 * In-memory cache implementation
 */
class MemoryCache {
  constructor() {
    this.cache = new Map();
  }

  /**
   * Set a value in the cache with optional expiration
   * @param {string} key - Cache key
   * @param {any} value - Value to cache
   * @param {number} ttl - Time to live in milliseconds (default: 5 minutes)
   */
  set(key, value, ttl = 5 * 60 * 1000) {
    const item = {
      value,
      expiry: Date.now() + ttl
    };
    this.cache.set(key, item);
  }

  /**
   * Get a value from the cache
   * @param {string} key - Cache key
   * @returns {any|null} Cached value or null if not found or expired
   */
  get(key) {
    const item = this.cache.get(key);
    if (!item) return null;

    if (Date.now() > item.expiry) {
      this.cache.delete(key);
      return null;
    }

    return item.value;
  }

  /**
   * Remove a value from the cache
   * @param {string} key - Cache key
   */
  delete(key) {
    this.cache.delete(key);
  }

  /**
   * Clear all values from the cache
   */
  clear() {
    this.cache.clear();
  }

  /**
   * Get all keys in the cache
   * @returns {string[]} Array of cache keys
   */
  keys() {
    return Array.from(this.cache.keys());
  }

  /**
   * Clean expired items from the cache
   */
  cleanExpired() {
    const now = Date.now();
    this.cache.forEach((item, key) => {
      if (now > item.expiry) {
        this.delete(key);
      }
    });
  }
}

/**
 * Local storage cache implementation
 */
class StorageCache {
  constructor() {
    this.prefix = 'api_cache_';
  }

  /**
   * Set a value in the cache with optional expiration
   * @param {string} key - Cache key
   * @param {any} value - Value to cache
   * @param {number} ttl - Time to live in milliseconds (default: 5 minutes)
   */
  set(key, value, ttl = 5 * 60 * 1000) {
    const item = {
      value,
      expiry: Date.now() + ttl
    };
    localStorage.setItem(this.prefix + key, JSON.stringify(item));
  }

  /**
   * Get a value from the cache
   * @param {string} key - Cache key
   * @returns {any|null} Cached value or null if not found or expired
   */
  get(key) {
    const item = localStorage.getItem(this.prefix + key);
    if (!item) return null;

    const parsedItem = JSON.parse(item);
    if (Date.now() > parsedItem.expiry) {
      this.delete(key);
      return null;
    }

    return parsedItem.value;
  }

  /**
   * Remove a value from the cache
   * @param {string} key - Cache key
   */
  delete(key) {
    localStorage.removeItem(this.prefix + key);
  }

  /**
   * Clear all values from the cache
   */
  clear() {
    Object.keys(localStorage)
      .filter(key => key.startsWith(this.prefix))
      .forEach(key => localStorage.removeItem(key));
  }

  /**
   * Get all keys in the cache
   * @returns {string[]} Array of cache keys
   */
  keys() {
    return Object.keys(localStorage)
      .filter(key => key.startsWith(this.prefix))
      .map(key => key.substring(this.prefix.length));
  }

  /**
   * Clean expired items from the cache
   */
  cleanExpired() {
    const now = Date.now();
    this.keys().forEach(key => {
      const prefixedKey = this.prefix + key;
      const item = localStorage.getItem(prefixedKey);
      if (item) {
        const parsedItem = JSON.parse(item);
        if (now > parsedItem.expiry) {
          this.delete(key);
        }
      }
    });
  }
}

// Create instances
export const memoryCache = new MemoryCache();
export const storageCache = new StorageCache();

// Export classes for custom instances
export { MemoryCache, StorageCache };
