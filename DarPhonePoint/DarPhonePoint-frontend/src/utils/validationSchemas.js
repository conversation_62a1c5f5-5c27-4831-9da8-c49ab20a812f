import * as Yup from 'yup';

/**
 * Common validation schemas for forms
 */

// Product form validation schema
export const productSchema = Yup.object().shape({
  name: Yup.string()
    .required('Product name is required')
    .min(3, 'Product name must be at least 3 characters')
    .max(100, 'Product name must be less than 100 characters'),
  description: Yup.string()
    .required('Description is required')
    .min(10, 'Description must be at least 10 characters'),
  price: Yup.number()
    .required('Price is required')
    .min(0, 'Price must be a positive number'),
  product_type: Yup.string()
    .required('Product type is required')
    .oneOf(['lead_magnet', 'premium', 'basic'], 'Invalid product type'),
  file_path: Yup.string()
    .required('File path is required')
    .matches(/^\/uploads\/products\/.*\.(pdf|zip|doc|docx)$/i, 'File path must be a valid path to a PDF, ZIP, or DOC file in /uploads/products/'),
  is_active: Yup.boolean(),
  slug: Yup.string()
    .required('Slug is required')
    .matches(/^[a-z0-9-]+$/, 'Slug can only contain lowercase letters, numbers, and hyphens'),
  whop_checkout_link: Yup.string()
    .url('Must be a valid URL')
    .matches(/^https:\/\/whop\.com\/checkout\/plan_[a-zA-Z0-9]+\?d2c=true$/, 'Must be a valid Whop checkout link')
    .nullable()
});

// User form validation schema
export const userSchema = Yup.object().shape({
  name: Yup.string()
    .required('Name is required')
    .min(2, 'Name must be at least 2 characters')
    .max(50, 'Name must be less than 50 characters'),
  email: Yup.string()
    .required('Email is required')
    .email('Invalid email format'),
  password: Yup.string()
    .when('$isNew', {
      is: true,
      then: Yup.string()
        .required('Password is required')
        .min(8, 'Password must be at least 8 characters')
        .matches(
          /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
          'Password must contain at least one uppercase letter, one lowercase letter, and one number'
        )
    }),
  role: Yup.string()
    .required('Role is required')
    .oneOf(['user', 'admin'], 'Invalid role')
});

// Email sequence form validation schema
export const emailSequenceSchema = Yup.object().shape({
  name: Yup.string()
    .required('Sequence name is required')
    .min(3, 'Sequence name must be at least 3 characters')
    .max(100, 'Sequence name must be less than 100 characters'),
  description: Yup.string()
    .required('Description is required'),
  trigger_type: Yup.string()
    .required('Trigger type is required')
    .oneOf(['purchase', 'signup', 'abandoned_cart', 'manual'], 'Invalid trigger type'),
  isActive: Yup.boolean(),
  emails: Yup.array().of(
    Yup.object().shape({
      subject: Yup.string()
        .required('Email subject is required')
        .min(3, 'Subject must be at least 3 characters'),
      body: Yup.string()
        .required('Email body is required')
        .min(10, 'Body must be at least 10 characters'),
      delay_days: Yup.number()
        .required('Delay is required')
        .min(0, 'Delay must be a positive number')
    })
  ).min(1, 'At least one email is required')
});

// Login form validation schema
export const loginSchema = Yup.object().shape({
  email: Yup.string()
    .required('Email is required')
    .email('Invalid email format'),
  password: Yup.string()
    .required('Password is required')
});

// Registration form validation schema
export const registrationSchema = Yup.object().shape({
  name: Yup.string()
    .required('Name is required')
    .min(2, 'Name must be at least 2 characters'),
  email: Yup.string()
    .required('Email is required')
    .email('Invalid email format'),
  password: Yup.string()
    .required('Password is required')
    .min(8, 'Password must be at least 8 characters')
    .matches(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
      'Password must contain at least one uppercase letter, one lowercase letter, and one number'
    ),
  confirmPassword: Yup.string()
    .required('Please confirm your password')
    .oneOf([Yup.ref('password'), null], 'Passwords must match')
});

// Lead form validation schema
export const leadSchema = Yup.object().shape({
  name: Yup.string()
    .required('Name is required')
    .min(2, 'Name must be at least 2 characters'),
  email: Yup.string()
    .required('Email is required')
    .email('Invalid email format'),
  source: Yup.string()
    .required('Source is required')
});

// Helper function to validate using Yup schemas
export const validateWithSchema = (schema, values, context = {}) => {
  try {
    schema.validateSync(values, { abortEarly: false, context });
    return {};
  } catch (error) {
    return error.inner.reduce((errors, err) => {
      errors[err.path] = err.message;
      return errors;
    }, {});
  }
};
