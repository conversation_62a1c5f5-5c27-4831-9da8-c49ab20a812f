/**
 * Delivery Estimation Utilities for Tanzania
 * Calculates estimated delivery dates based on location and shipping method
 */

// Tanzania regions and their delivery times (in business days)
const TANZANIA_DELIVERY_ZONES = {
  // Dar es Salaam Region - Same day/Next day
  'dar_es_salaam': {
    regions: ['dar es salaam', 'kinondoni', 'ilala', 'temeke', 'ubungo'],
    standard: { min: 1, max: 2, label: '1-2 business days' },
    express: { min: 0, max: 1, label: 'Same day or next day' }
  },
  
  // Major Cities - 2-3 days
  'major_cities': {
    regions: ['arusha', 'mwanza', 'dodoma', 'mbeya', 'morogoro', 'tanga', 'iringa'],
    standard: { min: 2, max: 3, label: '2-3 business days' },
    express: { min: 1, max: 2, label: '1-2 business days' }
  },
  
  // Regional Towns - 3-5 days
  'regional_towns': {
    regions: ['mtwara', 'lindi', 'ruvuma', 'rukwa', 'kigoma', 'tabora', 'shinyanga', 'kagera', 'mara', 'simiyu'],
    standard: { min: 3, max: 5, label: '3-5 business days' },
    express: { min: 2, max: 3, label: '2-3 business days' }
  },
  
  // Remote Areas - 5-7 days
  'remote_areas': {
    regions: ['katavi', 'njombe', 'songwe', 'geita'],
    standard: { min: 5, max: 7, label: '5-7 business days' },
    express: { min: 3, max: 5, label: '3-5 business days' }
  }
};

// Shipping methods
const SHIPPING_METHODS = {
  standard: {
    name: 'Standard Delivery',
    description: 'Regular delivery via local couriers',
    cost: 10000, // 10,000 TZS
    freeThreshold: 500000 // Free for orders over 500,000 TZS
  },
  express: {
    name: 'Express Delivery',
    description: 'Priority delivery for faster arrival',
    cost: 25000, // 25,000 TZS
    freeThreshold: 1000000 // Free for orders over 1,000,000 TZS
  }
};

/**
 * Get delivery zone for a given city/region
 */
export const getDeliveryZone = (city, region) => {
  const searchTerm = (city || region || '').toLowerCase().trim();
  
  for (const [zoneKey, zone] of Object.entries(TANZANIA_DELIVERY_ZONES)) {
    if (zone.regions.some(r => searchTerm.includes(r) || r.includes(searchTerm))) {
      return { key: zoneKey, ...zone };
    }
  }
  
  // Default to regional towns if not found
  return { key: 'regional_towns', ...TANZANIA_DELIVERY_ZONES.regional_towns };
};

/**
 * Calculate estimated delivery date
 */
export const calculateDeliveryDate = (city, region, shippingMethod = 'standard') => {
  const zone = getDeliveryZone(city, region);
  const deliveryInfo = zone[shippingMethod] || zone.standard;
  
  const today = new Date();
  const minDate = addBusinessDays(today, deliveryInfo.min);
  const maxDate = addBusinessDays(today, deliveryInfo.max);
  
  return {
    zone: zone.key,
    method: shippingMethod,
    minDate,
    maxDate,
    label: deliveryInfo.label,
    formatted: formatDeliveryRange(minDate, maxDate)
  };
};

/**
 * Add business days to a date (excluding weekends)
 */
const addBusinessDays = (date, days) => {
  const result = new Date(date);
  let addedDays = 0;
  
  while (addedDays < days) {
    result.setDate(result.getDate() + 1);
    // Skip weekends (Saturday = 6, Sunday = 0)
    if (result.getDay() !== 0 && result.getDay() !== 6) {
      addedDays++;
    }
  }
  
  return result;
};

/**
 * Format delivery date range for display
 */
const formatDeliveryRange = (minDate, maxDate) => {
  const options = { 
    weekday: 'short', 
    month: 'short', 
    day: 'numeric' 
  };
  
  const minFormatted = minDate.toLocaleDateString('en-TZ', options);
  const maxFormatted = maxDate.toLocaleDateString('en-TZ', options);
  
  if (minDate.toDateString() === maxDate.toDateString()) {
    return minFormatted;
  }
  
  return `${minFormatted} - ${maxFormatted}`;
};

/**
 * Get shipping cost based on order total and method
 */
export const calculateShippingCost = (orderTotal, shippingMethod = 'standard') => {
  const method = SHIPPING_METHODS[shippingMethod];
  
  if (!method) {
    return { cost: 0, isFree: false, reason: 'Unknown method' };
  }
  
  const isFree = orderTotal >= method.freeThreshold;
  
  return {
    cost: isFree ? 0 : method.cost,
    isFree,
    reason: isFree ? `Free delivery for orders over TZS ${method.freeThreshold.toLocaleString()}` : null,
    method: method.name,
    description: method.description
  };
};

/**
 * Get all available shipping methods for a location
 */
export const getAvailableShippingMethods = (city, region, orderTotal) => {
  const zone = getDeliveryZone(city, region);
  
  return Object.entries(SHIPPING_METHODS).map(([key, method]) => {
    const delivery = calculateDeliveryDate(city, region, key);
    const shipping = calculateShippingCost(orderTotal, key);
    
    return {
      id: key,
      name: method.name,
      description: method.description,
      cost: shipping.cost,
      isFree: shipping.isFree,
      deliveryTime: delivery.label,
      estimatedDate: delivery.formatted,
      zone: zone.key
    };
  });
};

/**
 * Get delivery information for checkout display
 */
export const getDeliveryInfo = (shippingAddress, orderTotal, shippingMethod = 'standard') => {
  const city = shippingAddress?.city;
  const region = shippingAddress?.state;
  
  const delivery = calculateDeliveryDate(city, region, shippingMethod);
  const shipping = calculateShippingCost(orderTotal, shippingMethod);
  
  return {
    ...delivery,
    shipping,
    location: `${city}, ${region}`,
    isExpressAvailable: true,
    recommendations: getDeliveryRecommendations(delivery.zone, orderTotal)
  };
};

/**
 * Get delivery recommendations based on zone and order value
 */
const getDeliveryRecommendations = (zone, orderTotal) => {
  const recommendations = [];
  
  if (zone === 'dar_es_salaam' && orderTotal > 200000) {
    recommendations.push({
      type: 'express',
      message: 'Express delivery available for same-day delivery in Dar es Salaam'
    });
  }
  
  if (orderTotal < 500000) {
    const remaining = 500000 - orderTotal;
    recommendations.push({
      type: 'free_shipping',
      message: `Add TZS ${remaining.toLocaleString()} more for free standard delivery`
    });
  }
  
  return recommendations;
};
