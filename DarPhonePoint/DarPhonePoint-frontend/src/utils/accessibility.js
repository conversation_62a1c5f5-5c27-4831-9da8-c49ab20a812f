/**
 * Accessibility Utilities for Phone Point Dar
 * Provides helper functions and constants for accessibility features
 */

// ARIA labels and descriptions for common UI elements
export const ARIA_LABELS = {
  // Navigation
  mainNavigation: 'Main navigation',
  breadcrumb: 'Breadcrumb navigation',
  pagination: 'Pagination navigation',
  
  // Product related
  productGrid: 'Product grid',
  productCard: 'Product card',
  productImage: 'Product image',
  productPrice: 'Product price',
  addToCart: 'Add to cart',
  removeFromCart: 'Remove from cart',
  productQuantity: 'Product quantity',
  
  // Cart and checkout
  shoppingCart: 'Shopping cart',
  cartItems: 'Cart items',
  cartTotal: 'Cart total',
  checkoutForm: 'Checkout form',
  paymentForm: 'Payment form',
  
  // Forms
  searchForm: 'Search form',
  loginForm: 'Login form',
  registrationForm: 'Registration form',
  contactForm: 'Contact form',
  
  // Admin
  adminPanel: 'Administration panel',
  dataTable: 'Data table',
  filterControls: 'Filter controls',
  
  // Common actions
  close: 'Close',
  open: 'Open',
  edit: 'Edit',
  delete: 'Delete',
  save: 'Save',
  cancel: 'Cancel',
  submit: 'Submit',
  search: 'Search',
  filter: 'Filter',
  sort: 'Sort',
  
  // Status indicators
  loading: 'Loading',
  error: 'Error',
  success: 'Success',
  warning: 'Warning',
  info: 'Information'
};

// Screen reader announcements
export const SCREEN_READER_MESSAGES = {
  productAdded: 'Product added to cart',
  productRemoved: 'Product removed from cart',
  cartUpdated: 'Cart updated',
  formSubmitted: 'Form submitted successfully',
  formError: 'Form contains errors, please review',
  pageLoaded: 'Page loaded',
  searchResults: (count) => `${count} search results found`,
  filterApplied: 'Filters applied',
  sortChanged: 'Sort order changed'
};

/**
 * Announce message to screen readers
 * @param {string} message - Message to announce
 * @param {string} priority - Priority level (polite, assertive)
 */
export const announceToScreenReader = (message, priority = 'polite') => {
  const announcement = document.createElement('div');
  announcement.setAttribute('aria-live', priority);
  announcement.setAttribute('aria-atomic', 'true');
  announcement.setAttribute('class', 'sr-only');
  announcement.textContent = message;
  
  document.body.appendChild(announcement);
  
  // Remove after announcement
  setTimeout(() => {
    document.body.removeChild(announcement);
  }, 1000);
};

/**
 * Generate unique ID for form elements
 * @param {string} prefix - Prefix for the ID
 * @returns {string} Unique ID
 */
export const generateId = (prefix = 'element') => {
  return `${prefix}-${Math.random().toString(36).substr(2, 9)}`;
};

/**
 * Check if element is focusable
 * @param {HTMLElement} element - Element to check
 * @returns {boolean} Whether element is focusable
 */
export const isFocusable = (element) => {
  if (!element || element.disabled) return false;
  
  const focusableSelectors = [
    'a[href]',
    'button:not([disabled])',
    'input:not([disabled])',
    'select:not([disabled])',
    'textarea:not([disabled])',
    '[tabindex]:not([tabindex="-1"])'
  ];
  
  return focusableSelectors.some(selector => element.matches(selector));
};

/**
 * Get all focusable elements within a container
 * @param {HTMLElement} container - Container element
 * @returns {HTMLElement[]} Array of focusable elements
 */
export const getFocusableElements = (container) => {
  const focusableSelectors = [
    'a[href]',
    'button:not([disabled])',
    'input:not([disabled])',
    'select:not([disabled])',
    'textarea:not([disabled])',
    '[tabindex]:not([tabindex="-1"])'
  ].join(', ');
  
  return Array.from(container.querySelectorAll(focusableSelectors))
    .filter(element => {
      const style = window.getComputedStyle(element);
      return style.display !== 'none' && style.visibility !== 'hidden';
    });
};

/**
 * Trap focus within a container (for modals, dropdowns)
 * @param {HTMLElement} container - Container to trap focus in
 * @returns {Function} Cleanup function
 */
export const trapFocus = (container) => {
  const focusableElements = getFocusableElements(container);
  const firstElement = focusableElements[0];
  const lastElement = focusableElements[focusableElements.length - 1];
  
  const handleTabKey = (e) => {
    if (e.key !== 'Tab') return;
    
    if (e.shiftKey) {
      if (document.activeElement === firstElement) {
        e.preventDefault();
        lastElement.focus();
      }
    } else {
      if (document.activeElement === lastElement) {
        e.preventDefault();
        firstElement.focus();
      }
    }
  };
  
  const handleEscapeKey = (e) => {
    if (e.key === 'Escape') {
      const closeButton = container.querySelector('[data-close]');
      if (closeButton) closeButton.click();
    }
  };
  
  container.addEventListener('keydown', handleTabKey);
  container.addEventListener('keydown', handleEscapeKey);
  
  // Focus first element
  if (firstElement) firstElement.focus();
  
  // Return cleanup function
  return () => {
    container.removeEventListener('keydown', handleTabKey);
    container.removeEventListener('keydown', handleEscapeKey);
  };
};

/**
 * Add keyboard navigation to a list of items
 * @param {HTMLElement[]} items - Array of items
 * @param {Object} options - Configuration options
 */
export const addKeyboardNavigation = (items, options = {}) => {
  const {
    orientation = 'vertical', // 'vertical' or 'horizontal'
    loop = true,
    activateOnFocus = false
  } = options;
  
  let currentIndex = 0;
  
  const handleKeyDown = (e) => {
    const isVertical = orientation === 'vertical';
    const nextKey = isVertical ? 'ArrowDown' : 'ArrowRight';
    const prevKey = isVertical ? 'ArrowUp' : 'ArrowLeft';
    
    switch (e.key) {
      case nextKey:
        e.preventDefault();
        currentIndex = loop ? (currentIndex + 1) % items.length : Math.min(currentIndex + 1, items.length - 1);
        items[currentIndex].focus();
        if (activateOnFocus) items[currentIndex].click();
        break;
        
      case prevKey:
        e.preventDefault();
        currentIndex = loop ? (currentIndex - 1 + items.length) % items.length : Math.max(currentIndex - 1, 0);
        items[currentIndex].focus();
        if (activateOnFocus) items[currentIndex].click();
        break;
        
      case 'Home':
        e.preventDefault();
        currentIndex = 0;
        items[currentIndex].focus();
        if (activateOnFocus) items[currentIndex].click();
        break;
        
      case 'End':
        e.preventDefault();
        currentIndex = items.length - 1;
        items[currentIndex].focus();
        if (activateOnFocus) items[currentIndex].click();
        break;
    }
  };
  
  items.forEach((item, index) => {
    item.addEventListener('keydown', handleKeyDown);
    item.addEventListener('focus', () => {
      currentIndex = index;
    });
    
    // Set tabindex
    item.setAttribute('tabindex', index === 0 ? '0' : '-1');
  });
  
  return () => {
    items.forEach(item => {
      item.removeEventListener('keydown', handleKeyDown);
    });
  };
};

/**
 * Check color contrast ratio
 * @param {string} foreground - Foreground color (hex)
 * @param {string} background - Background color (hex)
 * @returns {number} Contrast ratio
 */
export const getContrastRatio = (foreground, background) => {
  const getLuminance = (color) => {
    const rgb = parseInt(color.slice(1), 16);
    const r = (rgb >> 16) & 0xff;
    const g = (rgb >> 8) & 0xff;
    const b = (rgb >> 0) & 0xff;
    
    const [rs, gs, bs] = [r, g, b].map(c => {
      c = c / 255;
      return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
    });
    
    return 0.2126 * rs + 0.7152 * gs + 0.0722 * bs;
  };
  
  const l1 = getLuminance(foreground);
  const l2 = getLuminance(background);
  
  return (Math.max(l1, l2) + 0.05) / (Math.min(l1, l2) + 0.05);
};

/**
 * Check if contrast ratio meets WCAG standards
 * @param {number} ratio - Contrast ratio
 * @param {string} level - WCAG level ('AA' or 'AAA')
 * @param {string} size - Text size ('normal' or 'large')
 * @returns {boolean} Whether ratio meets standards
 */
export const meetsContrastStandards = (ratio, level = 'AA', size = 'normal') => {
  const standards = {
    AA: { normal: 4.5, large: 3 },
    AAA: { normal: 7, large: 4.5 }
  };
  
  return ratio >= standards[level][size];
};

// CSS classes for screen reader only content
export const SR_ONLY_CLASS = 'sr-only';

// Common ARIA attributes
export const ARIA_ATTRIBUTES = {
  expanded: 'aria-expanded',
  selected: 'aria-selected',
  checked: 'aria-checked',
  disabled: 'aria-disabled',
  hidden: 'aria-hidden',
  label: 'aria-label',
  labelledby: 'aria-labelledby',
  describedby: 'aria-describedby',
  live: 'aria-live',
  atomic: 'aria-atomic',
  busy: 'aria-busy',
  current: 'aria-current',
  invalid: 'aria-invalid',
  required: 'aria-required'
};
