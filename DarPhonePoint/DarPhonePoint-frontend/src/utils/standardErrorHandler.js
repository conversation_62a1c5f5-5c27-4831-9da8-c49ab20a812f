/**
 * Standardized Error Handling for Phone Point Dar
 * Provides consistent error handling patterns across the application
 */

import { toast } from 'react-toastify';

/**
 * Standard error types
 */
export const ERROR_TYPES = {
  NETWORK: 'NETWORK_ERROR',
  AUTHENTICATION: 'AUTH_ERROR', 
  AUTHORIZATION: 'PERMISSION_ERROR',
  VALIDATION: 'VALIDATION_ERROR',
  NOT_FOUND: 'NOT_FOUND_ERROR',
  SERVER: 'SERVER_ERROR',
  UNKNOWN: 'UNKNOWN_ERROR'
};

/**
 * Standard error messages for user display
 */
const ERROR_MESSAGES = {
  [ERROR_TYPES.NETWORK]: 'Network connection error. Please check your internet connection.',
  [ERROR_TYPES.AUTHENTICATION]: 'Please log in to continue.',
  [ERROR_TYPES.AUTHORIZATION]: 'You do not have permission to perform this action.',
  [ERROR_TYPES.VALIDATION]: 'Please check your input and try again.',
  [ERROR_TYPES.NOT_FOUND]: 'The requested item was not found.',
  [ERROR_TYPES.SERVER]: 'Server error. Please try again later.',
  [ERROR_TYPES.UNKNOWN]: 'An unexpected error occurred. Please try again.'
};

/**
 * Classify error based on status code and response
 */
export function classifyError(error) {
  if (!error.response) {
    return ERROR_TYPES.NETWORK;
  }

  const status = error.response.status;
  
  switch (status) {
    case 401:
      return ERROR_TYPES.AUTHENTICATION;
    case 403:
      return ERROR_TYPES.AUTHORIZATION;
    case 404:
      return ERROR_TYPES.NOT_FOUND;
    case 422:
    case 400:
      return ERROR_TYPES.VALIDATION;
    case 500:
    case 502:
    case 503:
    case 504:
      return ERROR_TYPES.SERVER;
    default:
      return ERROR_TYPES.UNKNOWN;
  }
}

/**
 * Extract user-friendly error message
 */
export function extractErrorMessage(error) {
  // Try to get message from response
  if (error.response?.data?.message) {
    return error.response.data.message;
  }
  
  if (error.response?.data?.error) {
    return error.response.data.error;
  }

  // Fallback to standard message based on error type
  const errorType = classifyError(error);
  return ERROR_MESSAGES[errorType];
}

/**
 * Standard error handler for API calls
 */
export function handleApiError(error, options = {}) {
  const {
    showToast = true,
    logError = true,
    customMessage = null,
    onAuthError = null,
    onPermissionError = null
  } = options;

  const errorType = classifyError(error);
  const message = customMessage || extractErrorMessage(error);

  // Log error for debugging
  if (logError) {
    console.error('API Error:', {
      type: errorType,
      status: error.response?.status,
      message: message,
      url: error.config?.url,
      method: error.config?.method,
      timestamp: new Date().toISOString()
    });
  }

  // Handle specific error types
  switch (errorType) {
    case ERROR_TYPES.AUTHENTICATION:
      if (onAuthError) {
        onAuthError(error);
      } else {
        // Default: redirect to login
        localStorage.removeItem('token');
        localStorage.removeItem('user');
        if (showToast) {
          toast.error(message);
        }
        // Could trigger a redirect here
      }
      break;

    case ERROR_TYPES.AUTHORIZATION:
      if (onPermissionError) {
        onPermissionError(error);
      } else if (showToast) {
        toast.error(message);
      }
      break;

    case ERROR_TYPES.NETWORK:
      if (showToast) {
        toast.error(message, { autoClose: 5000 });
      }
      break;

    default:
      if (showToast) {
        toast.error(message);
      }
  }

  return {
    type: errorType,
    message,
    originalError: error
  };
}

/**
 * Wrapper for async operations with standardized error handling
 */
export async function withErrorHandling(asyncOperation, options = {}) {
  try {
    return await asyncOperation();
  } catch (error) {
    const handledError = handleApiError(error, options);
    
    // Re-throw if specified
    if (options.rethrow) {
      throw handledError;
    }
    
    return null;
  }
}

/**
 * React hook for standardized error handling
 */
export function useErrorHandler() {
  return {
    handleError: handleApiError,
    withErrorHandling,
    ERROR_TYPES
  };
}

/**
 * Form validation error handler
 */
export function handleFormErrors(errors, setFieldErrors) {
  if (errors && typeof errors === 'object') {
    // Handle validation errors object
    const fieldErrors = {};
    Object.keys(errors).forEach(field => {
      fieldErrors[field] = Array.isArray(errors[field]) 
        ? errors[field][0] 
        : errors[field];
    });
    setFieldErrors(fieldErrors);
  }
}

/**
 * Global error boundary error handler
 */
export function handleGlobalError(error, errorInfo) {
  console.error('Global Error:', {
    error: error.message,
    stack: error.stack,
    errorInfo,
    timestamp: new Date().toISOString(),
    url: window.location.href,
    userAgent: navigator.userAgent
  });

  // Could send to error reporting service here
  toast.error('An unexpected error occurred. Please refresh the page.');
}

export default {
  handleApiError,
  withErrorHandling,
  useErrorHandler,
  handleFormErrors,
  handleGlobalError,
  ERROR_TYPES,
  classifyError,
  extractErrorMessage
};
