/**
 * Performance Monitoring Utility
 * Provides tools for monitoring and reporting application performance
 */

// Store performance metrics
const metrics = {
  apiCalls: {},
  pageLoads: {},
  renderTimes: {},
  resourceLoads: {}
};

// Store performance marks
const marks = {};

/**
 * Start timing an operation
 * @param {String} name - Name of the operation
 * @param {String} category - Category of the operation (api, page, render, resource)
 * @returns {Object} Timing object with start time
 */
export const startTiming = (name, category = 'api') => {
  const startTime = performance.now();
  marks[`${category}:${name}:start`] = startTime;
  return { startTime, name, category };
};

/**
 * End timing an operation and record the metric
 * @param {Object} timing - Timing object from startTiming
 * @param {Boolean} success - Whether the operation was successful
 * @param {Object} metadata - Additional metadata about the operation
 * @returns {Number} Duration in milliseconds
 */
export const endTiming = (timing, success = true, metadata = {}) => {
  const { name, category, startTime } = timing;
  const endTime = performance.now();
  const duration = endTime - startTime;

  marks[`${category}:${name}:end`] = endTime;

  // Store the metric
  if (!metrics[`${category}Calls`]) {
    metrics[`${category}Calls`] = {};
  }

  if (!metrics[`${category}Calls`][name]) {
    metrics[`${category}Calls`][name] = [];
  }

  metrics[`${category}Calls`][name].push({
    duration,
    timestamp: new Date().toISOString(),
    success,
    ...metadata
  });

  // Track slow operations for monitoring
  if (duration > getThresholdForCategory(category)) {
    // In production, this would be sent to monitoring service
    // For now, we'll just track it in the metadata
    metadata.slowOperation = true;
  }

  return duration;
};

/**
 * Get the threshold for slow operations by category
 * @param {String} category - Category of the operation
 * @returns {Number} Threshold in milliseconds
 */
const getThresholdForCategory = (category) => {
  switch (category) {
    case 'api':
      return 1000; // 1 second
    case 'page':
      return 300; // 300ms
    case 'render':
      return 50; // 50ms
    case 'resource':
      return 500; // 500ms
    default:
      return 500;
  }
};

/**
 * Record a page load
 * @param {String} page - Page name
 * @param {Object} performance - Performance object from the browser
 */
export const recordPageLoad = (page) => {
  if (typeof window === 'undefined' || !window.performance) return;

  const perfData = window.performance.timing;
  const pageLoadTime = perfData.loadEventEnd - perfData.navigationStart;
  const domReadyTime = perfData.domComplete - perfData.domLoading;

  if (!metrics.pageLoads[page]) {
    metrics.pageLoads[page] = [];
  }

  metrics.pageLoads[page].push({
    pageLoadTime,
    domReadyTime,
    timestamp: new Date().toISOString()
  });

  // Track slow page loads for monitoring
  if (pageLoadTime > 3000) { // 3 seconds
    // In production, this would be sent to monitoring service
    metrics.pageLoads[page][metrics.pageLoads[page].length - 1].slowLoad = true;
  }
};

/**
 * Record a component render time
 * @param {String} component - Component name
 * @param {Number} duration - Render duration in milliseconds
 */
export const recordRenderTime = (component, duration) => {
  if (!metrics.renderTimes[component]) {
    metrics.renderTimes[component] = [];
  }

  metrics.renderTimes[component].push({
    duration,
    timestamp: new Date().toISOString()
  });

  // Track slow renders for monitoring
  if (duration > 50) { // 50ms
    // In production, this would be sent to monitoring service
    metrics.renderTimes[component][metrics.renderTimes[component].length - 1].slowRender = true;
  }
};

/**
 * Get performance metrics
 * @param {String} category - Category of metrics to get (api, page, render, resource)
 * @param {String} name - Name of the specific metric to get
 * @returns {Array|Object} Performance metrics
 */
export const getMetrics = (category, name) => {
  if (category && name) {
    return metrics[`${category}Calls`]?.[name] || [];
  } else if (category) {
    return metrics[`${category}Calls`] || {};
  } else {
    return metrics;
  }
};

/**
 * Clear performance metrics
 * @param {String} category - Category of metrics to clear (api, page, render, resource)
 * @param {String} name - Name of the specific metric to clear
 */
export const clearMetrics = (category, name) => {
  if (category && name) {
    if (metrics[`${category}Calls`]?.[name]) {
      metrics[`${category}Calls`][name] = [];
    }
  } else if (category) {
    metrics[`${category}Calls`] = {};
  } else {
    Object.keys(metrics).forEach(key => {
      metrics[key] = {};
    });
  }
};

/**
 * Report performance metrics to the server
 * @param {String} category - Category of metrics to report
 * @returns {Promise} Promise that resolves when metrics are reported
 */
export const reportMetrics = async (category) => {
  // In production, this would send metrics to a monitoring service
  // For now, we'll just prepare the data for potential reporting
  const metricsToReport = category ? metrics[`${category}Calls`] : metrics;

  // Clear reported metrics
  if (category) {
    clearMetrics(category);
  } else {
    clearMetrics();
  }

  return { success: true, metricsCount: Object.keys(metricsToReport).length };
};

export default {
  startTiming,
  endTiming,
  recordPageLoad,
  recordRenderTime,
  getMetrics,
  clearMetrics,
  reportMetrics
};
