/**
 * Price formatting utilities for consistent currency display
 */

/**
 * Format price for Phone Point Dar (Tanzania)
 * @param {number} price - Price in TZS (e.g., 150000 for 150,000 TZS)
 * @param {string} currency - Currency code (default: 'TZS')
 * @param {string} locale - Locale for formatting (default: 'sw-TZ')
 * @returns {string} Formatted price string (e.g., 'TZS 150,000')
 */
export const formatPrice = (price, currency = 'TZS', locale = 'sw-TZ') => {
  // Handle free products
  if (price === 0 || price === null || price === undefined) {
    return 'Free';
  }

  // Handle invalid prices
  if (typeof price !== 'number' || isNaN(price)) {
    return 'TZS 0';
  }

  // For TZS, we don't use cents/decimals, prices are in whole shillings
  if (currency === 'TZS') {
    return new Intl.NumberFormat('en-TZ', {
      style: 'currency',
      currency: 'TZS',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(price);
  }

  // For other currencies, use standard formatting
  const priceInMainUnit = currency === 'USD' ? price / 100 : price;

  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: currency === 'TZS' ? 0 : 2,
    maximumFractionDigits: currency === 'TZS' ? 0 : 2
  }).format(priceInMainUnit);
};

/**
 * Format price for display in product cards and listings
 * @param {Object} product - Product object
 * @returns {string} Formatted price string
 */
export const formatProductPrice = (product) => {
  if (!product) return 'N/A';
  
  // Handle lead magnets (free products)
  if (product.product_type === 'lead_magnet' || product.price === 0) {
    return 'Free';
  }
  
  return formatPrice(product.price);
};

/**
 * Format price for admin displays (shows exact cents value for debugging)
 * @param {number} priceInCents - Price in cents
 * @returns {string} Formatted price with cents info
 */
export const formatAdminPrice = (priceInCents) => {
  if (priceInCents === 0) {
    return 'FREE';
  }
  
  const formatted = formatPrice(priceInCents);
  return formatted;
};

/**
 * Parse price input from user (handles both dollar and cent inputs)
 * @param {string|number} input - User input (e.g., "27.99" or "2799")
 * @param {boolean} isInCents - Whether input is already in cents
 * @returns {number} Price in cents
 */
export const parsePrice = (input, isInCents = false) => {
  if (!input && input !== 0) return 0;
  
  const numericValue = typeof input === 'string' ? parseFloat(input) : input;
  
  if (isNaN(numericValue)) return 0;
  
  // If already in cents, return as is
  if (isInCents) {
    return Math.round(numericValue);
  }
  
  // Convert dollars to cents
  return Math.round(numericValue * 100);
};

/**
 * Get price display for buttons (e.g., "Buy Now - $27.99")
 * @param {Object} product - Product object
 * @param {string} prefix - Prefix text (default: "Buy Now")
 * @returns {string} Button text with price
 */
export const getPriceButtonText = (product, prefix = "Buy Now") => {
  if (!product) return prefix;
  
  if (product.product_type === 'lead_magnet' || product.price === 0) {
    return 'Get Free Access';
  }
  
  const formattedPrice = formatPrice(product.price);
  return `${prefix} - ${formattedPrice}`;
};

/**
 * Validate price input
 * @param {string|number} input - Price input to validate
 * @returns {Object} Validation result with isValid and error message
 */
export const validatePrice = (input) => {
  if (!input && input !== 0) {
    return { isValid: false, error: 'Price is required' };
  }
  
  const numericValue = typeof input === 'string' ? parseFloat(input) : input;
  
  if (isNaN(numericValue)) {
    return { isValid: false, error: 'Price must be a valid number' };
  }
  
  if (numericValue < 0) {
    return { isValid: false, error: 'Price cannot be negative' };
  }
  
  if (numericValue > 999999) {
    return { isValid: false, error: 'Price cannot exceed $9,999.99' };
  }
  
  return { isValid: true, error: null };
};

export default {
  formatPrice,
  formatProductPrice,
  formatAdminPrice,
  parsePrice,
  getPriceButtonText,
  validatePrice
};
