import { useState, useEffect } from 'react';

/**
 * Screen size breakpoints
 */
export const BREAKPOINTS = {
  xs: 0,
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
  '2xl': 1536,
};

/**
 * Hook to detect if the current device is mobile
 * @returns {Object} Object containing isMobile, isTablet, and isDesktop flags
 */
export const useDeviceDetect = () => {
  const [windowSize, setWindowSize] = useState({
    width: typeof window !== 'undefined' ? window.innerWidth : 0,
    height: typeof window !== 'undefined' ? window.innerHeight : 0,
  });

  useEffect(() => {
    // Handler to call on window resize
    const handleResize = () => {
      setWindowSize({
        width: window.innerWidth,
        height: window.innerHeight,
      });
    };
    
    // Add event listener
    window.addEventListener('resize', handleResize);
    
    // Call handler right away so state gets updated with initial window size
    handleResize();
    
    // Remove event listener on cleanup
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return {
    isMobile: windowSize.width < BREAKPOINTS.md,
    isTablet: windowSize.width >= BREAKPOINTS.md && windowSize.width < BREAKPOINTS.lg,
    isDesktop: windowSize.width >= BREAKPOINTS.lg,
    width: windowSize.width,
    height: windowSize.height,
  };
};

/**
 * Hook to get responsive font size based on screen size
 * @param {number} baseSize - Base font size in rem
 * @param {number} mobileSize - Mobile font size in rem
 * @param {number} tabletSize - Tablet font size in rem
 * @returns {string} Responsive font size in rem
 */
export const useResponsiveFontSize = (baseSize, mobileSize, tabletSize) => {
  const { isMobile, isTablet } = useDeviceDetect();
  
  if (isMobile) return `${mobileSize}rem`;
  if (isTablet) return `${tabletSize}rem`;
  return `${baseSize}rem`;
};

/**
 * Hook to detect touch capability
 * @returns {boolean} True if touch is supported
 */
export const useTouchDetect = () => {
  const [isTouchDevice, setIsTouchDevice] = useState(false);
  
  useEffect(() => {
    const touchSupported = 'ontouchstart' in window || 
      navigator.maxTouchPoints > 0 ||
      navigator.msMaxTouchPoints > 0;
    
    setIsTouchDevice(touchSupported);
  }, []);
  
  return isTouchDevice;
};

/**
 * Hook to detect network connection quality
 * @returns {Object} Network information
 */
export const useNetworkStatus = () => {
  const [networkStatus, setNetworkStatus] = useState({
    online: typeof navigator !== 'undefined' ? navigator.onLine : true,
    effectiveType: 'unknown',
    downlink: 0,
    rtt: 0,
  });
  
  useEffect(() => {
    const updateNetworkStatus = () => {
      const connection = 
        navigator.connection || 
        navigator.mozConnection || 
        navigator.webkitConnection;
      
      if (connection) {
        setNetworkStatus({
          online: navigator.onLine,
          effectiveType: connection.effectiveType || 'unknown',
          downlink: connection.downlink || 0,
          rtt: connection.rtt || 0,
        });
      } else {
        setNetworkStatus({
          online: navigator.onLine,
          effectiveType: 'unknown',
          downlink: 0,
          rtt: 0,
        });
      }
    };
    
    // Add event listeners
    window.addEventListener('online', updateNetworkStatus);
    window.addEventListener('offline', updateNetworkStatus);
    
    // Initial check
    updateNetworkStatus();
    
    // Cleanup
    return () => {
      window.removeEventListener('online', updateNetworkStatus);
      window.removeEventListener('offline', updateNetworkStatus);
    };
  }, []);
  
  return networkStatus;
};

/**
 * Optimize image URL based on device
 * @param {string} imageUrl - Original image URL
 * @param {Object} options - Options for optimization
 * @returns {string} Optimized image URL
 */
export const getOptimizedImageUrl = (imageUrl, options = {}) => {
  const { isMobile, isTablet } = useDeviceDetect();
  const { width = 800, quality = 80 } = options;
  
  // If using a CDN like Cloudinary, Imgix, etc.
  // You would modify the URL to include width and quality parameters
  
  // Example for a hypothetical image service:
  if (isMobile) {
    return `${imageUrl}?w=${Math.round(width * 0.5)}&q=${quality}`;
  }
  
  if (isTablet) {
    return `${imageUrl}?w=${Math.round(width * 0.75)}&q=${quality}`;
  }
  
  return `${imageUrl}?w=${width}&q=${quality}`;
};

/**
 * Get appropriate component based on device type
 * @param {Object} components - Object containing components for different devices
 * @returns {React.Component} Component for current device
 */
export const getResponsiveComponent = (components) => {
  const { isMobile, isTablet } = useDeviceDetect();
  
  if (isMobile && components.mobile) {
    return components.mobile;
  }
  
  if (isTablet && components.tablet) {
    return components.tablet;
  }
  
  return components.desktop;
};

/**
 * Utility to create responsive styles
 * @param {Object} styles - Object containing styles for different breakpoints
 * @returns {Object} Styles for current device
 */
export const createResponsiveStyles = (styles) => {
  const { width } = useDeviceDetect();
  
  if (width < BREAKPOINTS.sm && styles.xs) {
    return { ...styles.base, ...styles.xs };
  }
  
  if (width < BREAKPOINTS.md && styles.sm) {
    return { ...styles.base, ...styles.sm };
  }
  
  if (width < BREAKPOINTS.lg && styles.md) {
    return { ...styles.base, ...styles.md };
  }
  
  if (width < BREAKPOINTS.xl && styles.lg) {
    return { ...styles.base, ...styles.lg };
  }
  
  if (width < BREAKPOINTS['2xl'] && styles.xl) {
    return { ...styles.base, ...styles.xl };
  }
  
  return { ...styles.base, ...styles['2xl'] };
};
