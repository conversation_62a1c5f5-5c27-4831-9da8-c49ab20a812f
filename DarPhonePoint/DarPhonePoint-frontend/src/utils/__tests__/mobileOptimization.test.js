import { BREAKPOINTS } from '../mobileOptimization';

describe('Mobile Optimization Utilities', () => {
  describe('BREAKPOINTS', () => {
    it('should define correct breakpoints', () => {
      expect(BREAKPOINTS.xs).toBe(0);
      expect(BREAKPOINTS.sm).toBe(640);
      expect(BREAKPOINTS.md).toBe(768);
      expect(BREAKPOINTS.lg).toBe(1024);
      expect(BREAKPOINTS.xl).toBe(1280);
      expect(BREAKPOINTS['2xl']).toBe(1536);
    });
  });

  describe('Device Detection', () => {
    it('should have correct breakpoint values for mobile, tablet, and desktop', () => {
      // Mobile is less than 768px
      expect(BREAKPOINTS.md).toBe(768);

      // Tablet is between 768px and 1024px
      expect(BREAKPOINTS.md).toBe(768);
      expect(BREAKPOINTS.lg).toBe(1024);

      // Desktop is 1024px and above
      expect(BREAKPOINTS.lg).toBe(1024);
    });
  });
});
