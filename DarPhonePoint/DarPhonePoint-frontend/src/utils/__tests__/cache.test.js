import { MemoryCache, StorageCache } from '../cache';

describe('Cache Utilities', () => {
  describe('MemoryCache', () => {
    it('should be defined', () => {
      expect(MemoryCache).toBeDefined();
    });

    it('should have the correct methods', () => {
      const cache = new MemoryCache();
      expect(typeof cache.set).toBe('function');
      expect(typeof cache.get).toBe('function');
      expect(typeof cache.remove).toBe('function');
      expect(typeof cache.clear).toBe('function');
      expect(typeof cache.keys).toBe('function');
      expect(typeof cache.cleanExpired).toBe('function');
    });
  });

  describe('StorageCache', () => {
    it('should be defined', () => {
      expect(StorageCache).toBeDefined();
    });

    it('should have the correct methods', () => {
      const cache = new StorageCache();
      expect(typeof cache.set).toBe('function');
      expect(typeof cache.get).toBe('function');
      expect(typeof cache.remove).toBe('function');
      expect(typeof cache.clear).toBe('function');
      expect(typeof cache.keys).toBe('function');
      expect(typeof cache.cleanExpired).toBe('function');
    });
  });
});
