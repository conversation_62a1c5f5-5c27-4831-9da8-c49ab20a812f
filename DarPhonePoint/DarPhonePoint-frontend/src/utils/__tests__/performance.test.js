import { performanceMonitor } from '../performance';

describe('Performance Monitoring Utility', () => {
  describe('Performance Monitor', () => {
    it('should be defined', () => {
      expect(performanceMonitor).toBeDefined();
    });

    it('should have the correct methods', () => {
      expect(typeof performanceMonitor.startMark).toBe('function');
      expect(typeof performanceMonitor.endMark).toBe('function');
      expect(typeof performanceMonitor.trackPerformanceEvent).toBe('function');
      expect(typeof performanceMonitor.getAllMetrics).toBe('function');
      expect(typeof performanceMonitor.setEnabled).toBe('function');
      expect(typeof performanceMonitor.clearMetrics).toBe('function');
    });
  });
});
