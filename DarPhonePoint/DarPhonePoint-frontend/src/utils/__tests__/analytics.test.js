import axios from 'axios';
import { v4 as uuidv4 } from 'uuid';
import {
  getSessionId,
  getDeviceType,
  getBrowser,
  trackEvent,
  trackPageView,
  trackLeadCapture,
  trackProductView,
  trackAddToCart,
  trackCheckoutStart,
  trackPurchase,
  trackDownload
} from '../analytics';

// Mock axios
jest.mock('axios');

// Mock uuid
jest.mock('uuid', () => ({
  v4: jest.fn().mockReturnValue('test-uuid-123')
}));

describe('Analytics Utility Tests', () => {
  let originalLocalStorage;
  let originalNavigator;
  let originalWindow;
  
  beforeEach(() => {
    // Mock localStorage
    originalLocalStorage = global.localStorage;
    global.localStorage = {
      getItem: jest.fn(),
      setItem: jest.fn()
    };
    
    // Mock navigator
    originalNavigator = global.navigator;
    global.navigator = {
      userAgent: 'Mozilla/5.0 (<PERSON>; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    };
    
    // Mock window
    originalWindow = global.window;
    global.window = {
      innerWidth: 1200,
      location: {
        pathname: '/test-page'
      }
    };
    
    // Mock document
    global.document = {
      referrer: 'https://google.com'
    };
    
    // Reset mocks
    jest.clearAllMocks();
  });
  
  afterEach(() => {
    // Restore original objects
    global.localStorage = originalLocalStorage;
    global.navigator = originalNavigator;
    global.window = originalWindow;
  });
  
  describe('getSessionId', () => {
    it('should return existing session ID if available', () => {
      localStorage.getItem.mockReturnValue('existing-session-id');
      
      const sessionId = getSessionId();
      
      expect(sessionId).toBe('existing-session-id');
      expect(localStorage.getItem).toHaveBeenCalledWith('session_id');
      expect(localStorage.setItem).not.toHaveBeenCalled();
    });
    
    it('should create and store new session ID if not available', () => {
      localStorage.getItem.mockReturnValue(null);
      
      const sessionId = getSessionId();
      
      expect(sessionId).toBe('test-uuid-123');
      expect(localStorage.getItem).toHaveBeenCalledWith('session_id');
      expect(localStorage.setItem).toHaveBeenCalledWith('session_id', 'test-uuid-123');
    });
  });
  
  describe('getDeviceType', () => {
    it('should return "desktop" for large screens', () => {
      window.innerWidth = 1200;
      
      const deviceType = getDeviceType();
      
      expect(deviceType).toBe('desktop');
    });
    
    it('should return "tablet" for medium screens', () => {
      window.innerWidth = 800;
      
      const deviceType = getDeviceType();
      
      expect(deviceType).toBe('tablet');
    });
    
    it('should return "mobile" for small screens', () => {
      window.innerWidth = 500;
      
      const deviceType = getDeviceType();
      
      expect(deviceType).toBe('mobile');
    });
  });
  
  describe('getBrowser', () => {
    it('should detect Chrome browser', () => {
      navigator.userAgent = 'Mozilla/5.0 Chrome/91.0.4472.124';
      
      const browser = getBrowser();
      
      expect(browser).toBe('Chrome');
    });
    
    it('should detect Firefox browser', () => {
      navigator.userAgent = 'Mozilla/5.0 Firefox/89.0';
      
      const browser = getBrowser();
      
      expect(browser).toBe('Firefox');
    });
    
    it('should detect Safari browser', () => {
      navigator.userAgent = 'Mozilla/5.0 Safari/605.1.15';
      
      const browser = getBrowser();
      
      expect(browser).toBe('Safari');
    });
  });
  
  describe('trackEvent', () => {
    it('should send tracking data to the API', async () => {
      localStorage.getItem.mockReturnValue('existing-session-id');
      axios.post.mockResolvedValue({ data: { success: true } });
      
      await trackEvent('page_view', { page_name: 'home' });
      
      expect(axios.post).toHaveBeenCalledWith('/api/analytics/track', {
        event_type: 'page_view',
        session_id: 'existing-session-id',
        page_url: '/test-page',
        device_type: 'desktop',
        browser: 'Chrome',
        referrer: 'https://google.com',
        metadata: { page_name: 'home' }
      });
    });
    
    it('should handle errors gracefully', async () => {
      localStorage.getItem.mockReturnValue('existing-session-id');
      axios.post.mockRejectedValue(new Error('Network error'));
      
      console.error = jest.fn();
      
      await trackEvent('page_view');
      
      expect(console.error).toHaveBeenCalledWith('Error tracking event:', expect.any(Error));
    });
  });
  
  describe('Specialized tracking functions', () => {
    beforeEach(() => {
      // Mock trackEvent
      jest.spyOn(global, 'trackEvent').mockImplementation(() => {});
    });
    
    it('should track page views correctly', () => {
      trackPageView('home');
      
      expect(trackEvent).toHaveBeenCalledWith('page_view', { page_name: 'home' });
    });
    
    it('should track lead captures correctly', () => {
      const leadData = { email: '<EMAIL>', name: 'Test User' };
      
      trackLeadCapture(leadData);
      
      expect(trackEvent).toHaveBeenCalledWith('lead_capture', leadData);
    });
    
    it('should track product views correctly', () => {
      const product = {
        _id: '123',
        name: 'Test Product',
        product_type: 'premium',
        price: 99.99
      };
      
      trackProductView(product);
      
      expect(trackEvent).toHaveBeenCalledWith('product_view', {
        product_id: '123',
        product_name: 'Test Product',
        product_type: 'premium',
        price: 99.99
      });
    });
  });
});
