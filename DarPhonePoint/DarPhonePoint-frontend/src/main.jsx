import React from 'react';
import <PERSON>actD<PERSON> from 'react-dom/client';
import { <PERSON><PERSON><PERSON><PERSON>outer as Router } from 'react-router-dom';
import { Toaster } from 'react-hot-toast';
import App from './App';
import './index.css';
import { AuthProvider } from './contexts/AuthContext';
import { LoadingProvider } from './contexts/LoadingContext';
import GlobalErrorBoundary from './components/ui/GlobalErrorBoundary';

// Handle unhandled promise rejections and resource loading errors
window.addEventListener('unhandledrejection', (event) => {
  console.error('Unhandled promise rejection:', event.reason);
  // Prevent the default browser behavior
  event.preventDefault();
});

// Handle resource loading errors
window.addEventListener('error', (event) => {
  if (event.target !== window) {
    console.error('Resource loading error:', event.target.src || event.target.href);
    // For automated testing, we want to be more resilient to resource loading errors
    if (event.target.tagName === 'SCRIPT' || event.target.tagName === 'LINK') {
      console.warn('Attempting to recover from resource loading error...');
      // Don't prevent default for resource errors, let the app continue
    }
  }
});

ReactDOM.createRoot(document.getElementById('root')).render(
  <React.StrictMode>
    <GlobalErrorBoundary>
      <Router
        future={{
          v7_startTransition: true,
          v7_relativeSplatPath: true
        }}
      >
        <LoadingProvider>
          <AuthProvider>
            <App />
            <Toaster
              position="top-right"
              toastOptions={{
                duration: 4000,
                style: {
                  background: '#363636',
                  color: '#fff',
                },
                success: {
                  duration: 3000,
                  iconTheme: {
                    primary: '#10b981',
                    secondary: '#fff',
                  },
                },
                error: {
                  duration: 5000,
                  iconTheme: {
                    primary: '#ef4444',
                    secondary: '#fff',
                  },
                },
              }}
            />
          </AuthProvider>
        </LoadingProvider>
      </Router>
    </GlobalErrorBoundary>
  </React.StrictMode>
);
