import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { safeApiRequest } from '../api/apiClient';
import LoadingState from '../components/ui/LoadingState';
import Alert from '../components/ui/Alert';

const WarrantyPage = () => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('devices');
  const [devices, setDevices] = useState([]);
  const [claims, setClaims] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [showClaimForm, setShowClaimForm] = useState(false);
  const [selectedDevice, setSelectedDevice] = useState(null);
  const [claimForm, setClaimForm] = useState({
    imei: '',
    issue_description: '',
    issue_category: '',
    preferred_resolution: 'repair',
    contact_phone: '',
    additional_notes: ''
  });

  useEffect(() => {
    if (user) {
      fetchWarrantyData();
    }
  }, [user]);

  const fetchWarrantyData = async () => {
    try {
      setLoading(true);
      const [devicesResponse, claimsResponse] = await Promise.all([
        safeApiRequest({ method: 'GET', url: '/warranty/devices' }),
        safeApiRequest({ method: 'GET', url: '/warranty/claims' })
      ]);

      setDevices(devicesResponse.data || []);
      setClaims(claimsResponse.data || []);
    } catch (err) {
      setError('Failed to load warranty information');
      console.error('Error fetching warranty data:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleClaimSubmit = async (e) => {
    e.preventDefault();
    try {
      setLoading(true);
      const response = await safeApiRequest({
        method: 'POST',
        url: '/warranty/claim',
        data: claimForm
      });

      setSuccess(`Warranty claim submitted successfully. Claim ID: ${response.data.claim_id}`);
      setShowClaimForm(false);
      setClaimForm({
        imei: '',
        issue_description: '',
        issue_category: '',
        preferred_resolution: 'repair',
        contact_phone: '',
        additional_notes: ''
      });
      fetchWarrantyData();
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to submit warranty claim');
    } finally {
      setLoading(false);
    }
  };

  const getWarrantyStatusColor = (status) => {
    switch (status) {
      case 'active': return 'text-green-600 bg-green-100';
      case 'expiring_soon': return 'text-yellow-600 bg-yellow-100';
      case 'expired': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getClaimStatusColor = (status) => {
    switch (status) {
      case 'submitted': return 'text-blue-600 bg-blue-100';
      case 'in_progress': return 'text-yellow-600 bg-yellow-100';
      case 'completed': return 'text-green-600 bg-green-100';
      case 'rejected': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Login Required</h2>
          <p className="text-gray-600">Please log in to access warranty information.</p>
        </div>
      </div>
    );
  }

  if (loading && devices.length === 0) {
    return <LoadingState message="Loading warranty information..." />;
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Warranty Center</h1>
          <p className="mt-2 text-gray-600">
            Manage your device warranties and submit warranty claims
          </p>
        </div>

        {/* Alerts */}
        {error && (
          <Alert
            type="error"
            message={error}
            onClose={() => setError(null)}
            className="mb-6"
          />
        )}
        {success && (
          <Alert
            type="success"
            message={success}
            onClose={() => setSuccess(null)}
            className="mb-6"
          />
        )}

        {/* Tabs */}
        <div className="border-b border-gray-200 mb-6">
          <nav className="-mb-px flex space-x-8">
            <button
              onClick={() => setActiveTab('devices')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'devices'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              My Devices ({devices.length})
            </button>
            <button
              onClick={() => setActiveTab('claims')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'claims'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Warranty Claims ({claims.length})
            </button>
          </nav>
        </div>

        {/* Devices Tab */}
        {activeTab === 'devices' && (
          <div>
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-xl font-semibold text-gray-900">Your Devices</h2>
              <button
                onClick={() => setShowClaimForm(true)}
                className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
              >
                Submit Warranty Claim
              </button>
            </div>

            {devices.length === 0 ? (
              <div className="text-center py-12">
                <div className="text-gray-400 text-6xl mb-4">📱</div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">No Devices Found</h3>
                <p className="text-gray-600">You don't have any registered devices yet.</p>
              </div>
            ) : (
              <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                {devices.map((device) => (
                  <div key={device.imei} className="bg-white rounded-lg shadow-md p-6">
                    <div className="flex items-start justify-between mb-4">
                      <div>
                        <h3 className="font-semibold text-gray-900">{device.product.name}</h3>
                        <p className="text-sm text-gray-600">{device.product.brand} {device.product.model}</p>
                        <p className="text-xs text-gray-500 mt-1">IMEI: {device.imei}</p>
                      </div>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getWarrantyStatusColor(device.warranty_status)}`}>
                        {device.warranty_status.replace('_', ' ').toUpperCase()}
                      </span>
                    </div>

                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Purchase Date:</span>
                        <span className="text-gray-900">
                          {new Date(device.purchase_date).toLocaleDateString()}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Warranty Until:</span>
                        <span className="text-gray-900">
                          {new Date(device.warranty_end_date).toLocaleDateString()}
                        </span>
                      </div>
                      {device.warranty_status === 'active' && (
                        <div className="flex justify-between">
                          <span className="text-gray-600">Days Remaining:</span>
                          <span className="text-green-600 font-medium">{device.days_remaining} days</span>
                        </div>
                      )}
                      <div className="flex justify-between">
                        <span className="text-gray-600">Active Claims:</span>
                        <span className="text-gray-900">{device.active_claims}</span>
                      </div>
                    </div>

                    {device.warranty_status === 'active' && (
                      <button
                        onClick={() => {
                          setSelectedDevice(device);
                          setClaimForm(prev => ({ ...prev, imei: device.imei }));
                          setShowClaimForm(true);
                        }}
                        className="w-full mt-4 bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors text-sm"
                      >
                        Submit Claim
                      </button>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        {/* Claims Tab */}
        {activeTab === 'claims' && (
          <div>
            <h2 className="text-xl font-semibold text-gray-900 mb-6">Warranty Claims</h2>

            {claims.length === 0 ? (
              <div className="text-center py-12">
                <div className="text-gray-400 text-6xl mb-4">📋</div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">No Claims Found</h3>
                <p className="text-gray-600">You haven't submitted any warranty claims yet.</p>
              </div>
            ) : (
              <div className="space-y-4">
                {claims.map((claim) => (
                  <div key={claim.claim_id} className="bg-white rounded-lg shadow-md p-6">
                    <div className="flex items-start justify-between mb-4">
                      <div>
                        <h3 className="font-semibold text-gray-900">Claim #{claim.claim_id}</h3>
                        <p className="text-sm text-gray-600">{claim.product.name}</p>
                        <p className="text-xs text-gray-500">IMEI: {claim.imei}</p>
                      </div>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getClaimStatusColor(claim.status)}`}>
                        {claim.status.replace('_', ' ').toUpperCase()}
                      </span>
                    </div>

                    <div className="grid md:grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-gray-600">Issue Category:</span>
                        <p className="text-gray-900 capitalize">{claim.issue_category}</p>
                      </div>
                      <div>
                        <span className="text-gray-600">Submitted:</span>
                        <p className="text-gray-900">{new Date(claim.submitted_at).toLocaleDateString()}</p>
                      </div>
                      <div className="md:col-span-2">
                        <span className="text-gray-600">Issue Description:</span>
                        <p className="text-gray-900 mt-1">{claim.issue_description}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        {/* Warranty Claim Form Modal */}
        {showClaimForm && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
            <div className="bg-white rounded-lg max-w-md w-full max-h-[90vh] overflow-y-auto">
              <div className="p-6">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-semibold text-gray-900">Submit Warranty Claim</h3>
                  <button
                    onClick={() => setShowClaimForm(false)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    ✕
                  </button>
                </div>

                <form onSubmit={handleClaimSubmit} className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Device IMEI
                    </label>
                    <input
                      type="text"
                      value={claimForm.imei}
                      onChange={(e) => setClaimForm(prev => ({ ...prev, imei: e.target.value }))}
                      className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Issue Category
                    </label>
                    <select
                      value={claimForm.issue_category}
                      onChange={(e) => setClaimForm(prev => ({ ...prev, issue_category: e.target.value }))}
                      className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    >
                      <option value="">Select category</option>
                      <option value="hardware_defect">Hardware Defect</option>
                      <option value="software_issue">Software Issue</option>
                      <option value="battery_problem">Battery Problem</option>
                      <option value="screen_damage">Screen Damage</option>
                      <option value="charging_issue">Charging Issue</option>
                      <option value="other">Other</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Issue Description
                    </label>
                    <textarea
                      value={claimForm.issue_description}
                      onChange={(e) => setClaimForm(prev => ({ ...prev, issue_description: e.target.value }))}
                      rows={3}
                      className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="Describe the issue in detail..."
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Preferred Resolution
                    </label>
                    <select
                      value={claimForm.preferred_resolution}
                      onChange={(e) => setClaimForm(prev => ({ ...prev, preferred_resolution: e.target.value }))}
                      className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="repair">Repair</option>
                      <option value="replacement">Replacement</option>
                      <option value="refund">Refund</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Contact Phone
                    </label>
                    <input
                      type="tel"
                      value={claimForm.contact_phone}
                      onChange={(e) => setClaimForm(prev => ({ ...prev, contact_phone: e.target.value }))}
                      className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="Your phone number"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Additional Notes
                    </label>
                    <textarea
                      value={claimForm.additional_notes}
                      onChange={(e) => setClaimForm(prev => ({ ...prev, additional_notes: e.target.value }))}
                      rows={2}
                      className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="Any additional information..."
                    />
                  </div>

                  <div className="flex space-x-3 pt-4">
                    <button
                      type="button"
                      onClick={() => setShowClaimForm(false)}
                      className="flex-1 bg-gray-300 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-400 transition-colors"
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      disabled={loading}
                      className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50"
                    >
                      {loading ? 'Submitting...' : 'Submit Claim'}
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default WarrantyPage;
