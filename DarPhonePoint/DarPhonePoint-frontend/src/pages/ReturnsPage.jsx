import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { safeApiRequest } from '../api/apiClient';
import LoadingState from '../components/ui/LoadingState';
import Alert from '../components/ui/Alert';

const ReturnsPage = () => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('eligible');
  const [eligibleOrders, setEligibleOrders] = useState([]);
  const [returnRequests, setReturnRequests] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [showReturnForm, setShowReturnForm] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState(null);
  const [returnForm, setReturnForm] = useState({
    order_id: '',
    imei: '',
    return_reason: '',
    return_category: '',
    condition_description: '',
    preferred_resolution: 'refund',
    additional_notes: ''
  });

  useEffect(() => {
    if (user) {
      fetchReturnData();
    }
  }, [user]);

  const fetchReturnData = async () => {
    try {
      setLoading(true);
      const [ordersResponse, requestsResponse] = await Promise.all([
        safeApiRequest({ method: 'GET', url: '/returns/eligible-orders' }),
        safeApiRequest({ method: 'GET', url: '/returns/requests' })
      ]);

      setEligibleOrders(ordersResponse.data || []);
      setReturnRequests(requestsResponse.data || []);
    } catch (err) {
      setError('Failed to load return information');
      console.error('Error fetching return data:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleReturnSubmit = async (e) => {
    e.preventDefault();
    try {
      setLoading(true);
      const response = await safeApiRequest({
        method: 'POST',
        url: '/returns/initiate',
        data: returnForm
      });

      setSuccess(`Return request submitted successfully. Return ID: ${response.data.return_id}`);
      setShowReturnForm(false);
      setReturnForm({
        order_id: '',
        imei: '',
        return_reason: '',
        return_category: '',
        condition_description: '',
        preferred_resolution: 'refund',
        additional_notes: ''
      });
      fetchReturnData();
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to submit return request');
    } finally {
      setLoading(false);
    }
  };

  const getReturnStatusColor = (status) => {
    switch (status) {
      case 'submitted': return 'text-blue-600 bg-blue-100';
      case 'under_review': return 'text-yellow-600 bg-yellow-100';
      case 'approved': return 'text-green-600 bg-green-100';
      case 'rejected': return 'text-red-600 bg-red-100';
      case 'completed': return 'text-green-600 bg-green-100';
      case 'cancelled': return 'text-gray-600 bg-gray-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Login Required</h2>
          <p className="text-gray-600">Please log in to access return information.</p>
        </div>
      </div>
    );
  }

  if (loading && eligibleOrders.length === 0) {
    return <LoadingState message="Loading return information..." />;
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Returns & Refunds</h1>
          <p className="mt-2 text-gray-600">
            Manage your returns and refund requests (14-day return policy)
          </p>
        </div>

        {/* Alerts */}
        {error && (
          <Alert
            type="error"
            message={error}
            onClose={() => setError(null)}
            className="mb-6"
          />
        )}
        {success && (
          <Alert
            type="success"
            message={success}
            onClose={() => setSuccess(null)}
            className="mb-6"
          />
        )}

        {/* Tabs */}
        <div className="border-b border-gray-200 mb-6">
          <nav className="-mb-px flex space-x-8">
            <button
              onClick={() => setActiveTab('eligible')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'eligible'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Eligible Orders ({eligibleOrders.length})
            </button>
            <button
              onClick={() => setActiveTab('requests')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'requests'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Return Requests ({returnRequests.length})
            </button>
          </nav>
        </div>

        {/* Eligible Orders Tab */}
        {activeTab === 'eligible' && (
          <div>
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-xl font-semibold text-gray-900">Orders Eligible for Return</h2>
            </div>

            {eligibleOrders.length === 0 ? (
              <div className="text-center py-12">
                <div className="text-gray-400 text-6xl mb-4">📦</div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">No Eligible Orders</h3>
                <p className="text-gray-600">You don't have any orders eligible for return at this time.</p>
              </div>
            ) : (
              <div className="space-y-4">
                {eligibleOrders.map((order) => (
                  <div key={order._id} className="bg-white rounded-lg shadow-md p-6">
                    <div className="flex items-start justify-between mb-4">
                      <div>
                        <h3 className="font-semibold text-gray-900">Order #{order.order_number}</h3>
                        <p className="text-sm text-gray-600">
                          Ordered on {new Date(order.order_date).toLocaleDateString()}
                        </p>
                        <p className="text-sm text-gray-600">Total: TZS {order.total.toLocaleString()}</p>
                      </div>
                      <div className="text-right">
                        {order.can_return ? (
                          <span className="text-green-600 text-sm font-medium">
                            {order.days_remaining_for_return} days left
                          </span>
                        ) : (
                          <span className="text-red-600 text-sm font-medium">
                            Return period expired
                          </span>
                        )}
                      </div>
                    </div>

                    <div className="space-y-2 mb-4">
                      {order.items.map((item, index) => (
                        <div key={index} className="flex items-center space-x-3 p-2 bg-gray-50 rounded">
                          <img
                            src={item.product.image || '/placeholder-product.jpg'}
                            alt={item.product.name}
                            className="w-12 h-12 object-cover rounded"
                          />
                          <div className="flex-1">
                            <p className="font-medium text-gray-900">{item.product.name}</p>
                            <p className="text-sm text-gray-600">
                              {item.product.brand} {item.product.model}
                            </p>
                            <p className="text-sm text-gray-600">Qty: {item.quantity}</p>
                          </div>
                          <div className="text-right">
                            <p className="font-medium text-gray-900">
                              TZS {(item.price * item.quantity).toLocaleString()}
                            </p>
                          </div>
                        </div>
                      ))}
                    </div>

                    {order.can_return && (
                      <button
                        onClick={() => {
                          setSelectedOrder(order);
                          setReturnForm(prev => ({ ...prev, order_id: order._id }));
                          setShowReturnForm(true);
                        }}
                        className="w-full bg-red-600 text-white py-2 px-4 rounded-md hover:bg-red-700 transition-colors"
                      >
                        Request Return
                      </button>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        {/* Return Requests Tab */}
        {activeTab === 'requests' && (
          <div>
            <h2 className="text-xl font-semibold text-gray-900 mb-6">Return Requests</h2>

            {returnRequests.length === 0 ? (
              <div className="text-center py-12">
                <div className="text-gray-400 text-6xl mb-4">📋</div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">No Return Requests</h3>
                <p className="text-gray-600">You haven't submitted any return requests yet.</p>
              </div>
            ) : (
              <div className="space-y-4">
                {returnRequests.map((request) => (
                  <div key={request.return_id} className="bg-white rounded-lg shadow-md p-6">
                    <div className="flex items-start justify-between mb-4">
                      <div>
                        <h3 className="font-semibold text-gray-900">Return #{request.return_id}</h3>
                        <p className="text-sm text-gray-600">Order #{request.order_number}</p>
                        {request.imei && (
                          <p className="text-xs text-gray-500">IMEI: {request.imei}</p>
                        )}
                      </div>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getReturnStatusColor(request.status)}`}>
                        {request.status.replace('_', ' ').toUpperCase()}
                      </span>
                    </div>

                    <div className="grid md:grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-gray-600">Return Category:</span>
                        <p className="text-gray-900 capitalize">{request.return_category}</p>
                      </div>
                      <div>
                        <span className="text-gray-600">Submitted:</span>
                        <p className="text-gray-900">{new Date(request.submitted_at).toLocaleDateString()}</p>
                      </div>
                      <div>
                        <span className="text-gray-600">Preferred Resolution:</span>
                        <p className="text-gray-900 capitalize">{request.preferred_resolution}</p>
                      </div>
                      <div>
                        <span className="text-gray-600">Estimated Refund:</span>
                        <p className="text-gray-900">TZS {request.estimated_refund_amount?.toLocaleString()}</p>
                      </div>
                      <div className="md:col-span-2">
                        <span className="text-gray-600">Return Reason:</span>
                        <p className="text-gray-900 mt-1">{request.return_reason}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        {/* Return Request Form Modal */}
        {showReturnForm && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
            <div className="bg-white rounded-lg max-w-md w-full max-h-[90vh] overflow-y-auto">
              <div className="p-6">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-semibold text-gray-900">Request Return</h3>
                  <button
                    onClick={() => setShowReturnForm(false)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    ✕
                  </button>
                </div>

                <form onSubmit={handleReturnSubmit} className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Return Category
                    </label>
                    <select
                      value={returnForm.return_category}
                      onChange={(e) => setReturnForm(prev => ({ ...prev, return_category: e.target.value }))}
                      className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    >
                      <option value="">Select category</option>
                      <option value="defective">Defective Product</option>
                      <option value="wrong_item">Wrong Item Received</option>
                      <option value="not_as_described">Not as Described</option>
                      <option value="changed_mind">Changed Mind</option>
                      <option value="damaged_shipping">Damaged in Shipping</option>
                      <option value="other">Other</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Return Reason
                    </label>
                    <textarea
                      value={returnForm.return_reason}
                      onChange={(e) => setReturnForm(prev => ({ ...prev, return_reason: e.target.value }))}
                      rows={3}
                      className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="Please explain why you want to return this item..."
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Item Condition
                    </label>
                    <textarea
                      value={returnForm.condition_description}
                      onChange={(e) => setReturnForm(prev => ({ ...prev, condition_description: e.target.value }))}
                      rows={2}
                      className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="Describe the current condition of the item..."
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Preferred Resolution
                    </label>
                    <select
                      value={returnForm.preferred_resolution}
                      onChange={(e) => setReturnForm(prev => ({ ...prev, preferred_resolution: e.target.value }))}
                      className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="refund">Full Refund</option>
                      <option value="exchange">Exchange for Same Item</option>
                      <option value="store_credit">Store Credit</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Device IMEI (if applicable)
                    </label>
                    <input
                      type="text"
                      value={returnForm.imei}
                      onChange={(e) => setReturnForm(prev => ({ ...prev, imei: e.target.value }))}
                      className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="Enter IMEI for specific device return"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Additional Notes
                    </label>
                    <textarea
                      value={returnForm.additional_notes}
                      onChange={(e) => setReturnForm(prev => ({ ...prev, additional_notes: e.target.value }))}
                      rows={2}
                      className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="Any additional information..."
                    />
                  </div>

                  <div className="flex space-x-3 pt-4">
                    <button
                      type="button"
                      onClick={() => setShowReturnForm(false)}
                      className="flex-1 bg-gray-300 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-400 transition-colors"
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      disabled={loading}
                      className="flex-1 bg-red-600 text-white py-2 px-4 rounded-md hover:bg-red-700 transition-colors disabled:opacity-50"
                    >
                      {loading ? 'Submitting...' : 'Submit Return Request'}
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ReturnsPage;
