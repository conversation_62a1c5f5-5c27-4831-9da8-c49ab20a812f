import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { getAllLeads, updateLeadStatus, exportLeadsToCSV } from '../../services/leadService';
import Button from '../../components/ui/Button';
import Alert from '../../components/ui/Alert';
import Input from '../../components/ui/Input';

/**
 * Phone Point Dar Customer Leads Page
 * Manage tech product inquiries and potential customers in Tanzania
 */
const TechCustomerLeadsPage = () => {
  const [leads, setLeads] = useState([]);
  const [filteredLeads, setFilteredLeads] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [sourceFilter, setSourceFilter] = useState('all');
  const [exportLoading, setExportLoading] = useState(false);

  useEffect(() => {
    const fetchLeads = async () => {
      try {
        const response = await getAllLeads();
        console.log('Leads response:', response);

        // Handle the nested data structure from our API
        let leadsData = [];
        if (response.data && Array.isArray(response.data)) {
          leadsData = response.data;
        } else if (Array.isArray(response)) {
          leadsData = response;
        } else if (response.data && response.data.data && Array.isArray(response.data.data)) {
          leadsData = response.data.data;
        }

        // Sort leads by date (newest first)
        leadsData.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));

        setLeads(leadsData);
        setFilteredLeads(leadsData);
        setLoading(false);
      } catch (err) {
        console.error('Error fetching leads:', err);
        setError('Failed to load leads. Please try again later.');
        setLoading(false);
      }
    };

    fetchLeads();
  }, []);

  useEffect(() => {
    // Apply filters whenever search term or filters change
    let result = [...leads];

    // Apply search filter
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      result = result.filter(lead =>
        (lead.name && lead.name.toLowerCase().includes(term)) ||
        (lead.email && lead.email.toLowerCase().includes(term))
      );
    }

    // Apply status filter
    if (statusFilter !== 'all') {
      result = result.filter(lead => lead.status === statusFilter);
    }

    // Apply source filter
    if (sourceFilter !== 'all') {
      result = result.filter(lead => lead.source === sourceFilter);
    }

    setFilteredLeads(result);
  }, [searchTerm, statusFilter, sourceFilter, leads]);

  const handleExportCSV = async () => {
    setExportLoading(true);
    try {
      // Generate CSV content using the service
      const csvContent = exportLeadsToCSV(filteredLeads);

      // Create a blob and download link
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `leads_export_${new Date().toISOString().split('T')[0]}.csv`);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      setExportLoading(false);
    } catch (err) {
      console.error('Error exporting leads:', err);
      setError('Failed to export leads. Please try again later.');
      setExportLoading(false);
    }
  };

  const handleUpdateLeadStatus = async (leadId, newStatus) => {
    try {
      // Call the API to update the lead status
      await updateLeadStatus(leadId, newStatus);

      // Update the UI
      const updatedLeads = leads.map(lead =>
        lead._id === leadId ? { ...lead, status: newStatus } : lead
      );
      setLeads(updatedLeads);
    } catch (err) {
      console.error('Error updating lead status:', err);
      setError('Failed to update lead status. Please try again later.');
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen bg-gray-50">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500"></div>
      </div>
    );
  }

  // Get unique sources for filter dropdown
  const sources = ['all', ...new Set(leads.map(lead => lead.source))];

  return (
    <div className="bg-gray-50 py-12">
      <div className="container mx-auto px-4">
        <div className="max-w-7xl mx-auto">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
            <div>
              <h1 className="text-3xl font-bold mb-2 flex items-center">
                <i className="fas fa-mobile-alt mr-3 text-blue-600"></i>
                📱 Phone Point Dar - Customer Leads
              </h1>
              <p className="text-gray-600">Manage tech product inquiries and potential customers in Tanzania</p>
            </div>
            <div className="mt-4 md:mt-0">
              <Button
                onClick={handleExportCSV}
                isLoading={exportLoading}
              >
                <svg className="h-5 w-5 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                </svg>
                Export CSV
              </Button>
            </div>
          </div>

          {error && (
            <Alert
              type="error"
              message={error}
              onClose={() => setError('')}
              className="mb-8"
            />
          )}

          {/* Filters */}
          <div className="bg-white rounded-lg shadow-md p-6 mb-8">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div>
                <Input
                  label="Search"
                  id="search"
                  type="text"
                  placeholder="Search by name or email"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
              <div>
                <label htmlFor="status-filter" className="block text-sm font-medium text-gray-700 mb-1">
                  Status
                </label>
                <select
                  id="status-filter"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                >
                  <option value="all">All Statuses</option>
                  <option value="new">New</option>
                  <option value="contacted">Contacted</option>
                  <option value="converted">Converted</option>
                  <option value="unsubscribed">Unsubscribed</option>
                </select>
              </div>
              <div>
                <label htmlFor="source-filter" className="block text-sm font-medium text-gray-700 mb-1">
                  Source
                </label>
                <select
                  id="source-filter"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  value={sourceFilter}
                  onChange={(e) => setSourceFilter(e.target.value)}
                >
                  {sources.map(source => (
                    <option key={source} value={source}>
                      {source === 'all' ? 'All Sources' : source.charAt(0).toUpperCase() + source.slice(1)}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </div>

          {/* Leads Table */}
          <div className="bg-white rounded-lg shadow-md overflow-hidden">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Lead
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Source
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Date
                    </th>
                    <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredLeads.length > 0 ? (
                    filteredLeads.map((lead) => (
                      <tr key={lead._id}>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <div className="flex-shrink-0 h-10 w-10 bg-purple-100 rounded-full flex items-center justify-center">
                              <span className="text-purple-600 font-semibold">
                                {lead.name ? lead.name.charAt(0).toUpperCase() : lead.email.charAt(0).toUpperCase()}
                              </span>
                            </div>
                            <div className="ml-4">
                              <div className="text-sm font-medium text-gray-900">{lead.name || 'No Name'}</div>
                              <div className="text-sm text-gray-500">{lead.email}</div>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            {lead.source || 'Unknown'}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <select
                            className={`text-xs font-medium rounded-full px-2.5 py-1 border-0 ${
                              lead.status === 'new' ? 'bg-green-100 text-green-800' :
                              lead.status === 'contacted' ? 'bg-blue-100 text-blue-800' :
                              lead.status === 'converted' ? 'bg-purple-100 text-purple-800' :
                              'bg-red-100 text-red-800'
                            }`}
                            value={lead.status || 'new'}
                            onChange={(e) => handleUpdateLeadStatus(lead._id, e.target.value)}
                          >
                            <option value="new">New</option>
                            <option value="contacted">Contacted</option>
                            <option value="converted">Converted</option>
                            <option value="unsubscribed">Unsubscribed</option>
                          </select>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {new Date(lead.created_at).toLocaleDateString()}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <div className="flex justify-end space-x-2">
                            <Link to={`/admin/leads/${lead._id}`} className="text-indigo-600 hover:text-indigo-900">
                              View
                            </Link>
                            <a
                              href={`mailto:${lead.email}?subject=Phone Point Dar - Follow up on your tech inquiry&body=Hello ${lead.name || 'there'},\n\nThank you for your interest in Phone Point Dar. We have the latest tech products, phones, and accessories available in Tanzania.\n\nWe offer:\n- Latest smartphones and tech gadgets from top brands\n- Phone accessories and tech products\n- Competitive prices in TZS\n- M-Pesa and mobile money payments\n- Warranty and after-sales support\n- Delivery across Tanzania\n\nLet us know how we can help you find the perfect tech product!\n\nBest regards,\nPhone Point Dar Team`}
                              className="text-blue-600 hover:text-blue-900"
                              title={`Send email to ${lead.email}`}
                            >
                              Email
                            </a>
                          </div>
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan="5" className="px-6 py-4 text-center text-gray-500">
                        No leads found matching your filters.
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TechCustomerLeadsPage;
