import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import AdminPageWrapper, { QuickActionButton, AdminStatsCard } from '../../components/layout/AdminPageWrapper';
import Button from '../../components/ui/Button';
import Modal from '../../components/ui/Modal';
import { safeApiRequest } from '../../api/apiClient';

const CustomerManagementPage = () => {
  const [customers, setCustomers] = useState([]);
  const [filteredCustomers, setFilteredCustomers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [customerTypeFilter, setCustomerTypeFilter] = useState('all');
  const [statusFilter, setStatusFilter] = useState('all');
  const [showCustomerModal, setShowCustomerModal] = useState(false);
  const [selectedCustomer, setSelectedCustomer] = useState(null);

  useEffect(() => {
    fetchCustomers();
  }, []);

  const fetchCustomers = async () => {
    setLoading(true);
    try {
      const response = await safeApiRequest({
        method: 'GET',
        url: '/users'
      });

      if (response.data && response.data.data) {
        const customerData = response.data.data.map(customer => ({
          ...customer,
          customer_type: customer.customer_type || 'regular',
          status: customer.status || 'active',
          tech_purchases: customer.tech_purchases || [],
          total_spent: customer.total_spent || 0,
          last_purchase_date: customer.last_purchase_date,
          warranty_claims: customer.warranty_claims || [],
          trade_ins: customer.trade_ins || []
        }));

        setCustomers(customerData);
        setFilteredCustomers(customerData);
      }
    } catch (err) {
      console.error('Error fetching customers:', err);
      setError('Failed to load customers');
    } finally {
      setLoading(false);
    }
  };

  const formatTZS = (amount) => {
    return new Intl.NumberFormat('sw-TZ', {
      style: 'currency',
      currency: 'TZS',
      minimumFractionDigits: 0
    }).format(amount);
  };

  const getCustomerTypeColor = (type) => {
    switch (type) {
      case 'vip': return 'bg-purple-100 text-purple-800';
      case 'regular': return 'bg-blue-100 text-blue-800';
      case 'wholesale': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'inactive': return 'bg-gray-100 text-gray-800';
      case 'blocked': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  // Filter customers based on search and filters
  useEffect(() => {
    let filtered = customers;

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(customer =>
        customer.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        customer.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        customer.phone?.includes(searchTerm)
      );
    }

    // Customer type filter
    if (customerTypeFilter !== 'all') {
      filtered = filtered.filter(customer => customer.customer_type === customerTypeFilter);
    }

    // Status filter
    if (statusFilter !== 'all') {
      filtered = filtered.filter(customer => customer.status === statusFilter);
    }

    setFilteredCustomers(filtered);
  }, [customers, searchTerm, customerTypeFilter, statusFilter]);

  return (
    <AdminPageWrapper
      title="👥 Customer Management"
      description="Manage Phone Point Dar customers and their tech product purchase history"
      breadcrumbs={[
        { name: 'Dashboard', href: '/admin' },
        { name: 'Customers', href: '/admin/customers' }
      ]}
      actions={
        <div className="flex space-x-3">
          <QuickActionButton
            icon="fas fa-download"
            variant="secondary"
            onClick={() => {
              // Export functionality would go here
              alert('Export functionality coming soon!');
            }}
          >
            Export Customers
          </QuickActionButton>
          <QuickActionButton
            icon="fas fa-user-plus"
            onClick={() => setShowCustomerModal(true)}
          >
            Add Customer
          </QuickActionButton>
        </div>
      }
      loading={loading}
      error={error}
      success={success}
      onErrorClose={() => setError('')}
      onSuccessClose={() => setSuccess('')}
    >

      {/* Customer Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <AdminStatsCard
          title="Total Customers"
          value={customers.length}
          icon="fas fa-users"
          color="blue"
        />
        <AdminStatsCard
          title="VIP Customers"
          value={customers.filter(c => c.customer_type === 'vip').length}
          icon="fas fa-star"
          color="green"
        />
        <AdminStatsCard
          title="Active Customers"
          value={customers.filter(c => c.status === 'active').length}
          icon="fas fa-mobile-alt"
          color="purple"
        />
        <AdminStatsCard
          title="Total Revenue"
          value={formatTZS(customers.reduce((sum, c) => sum + (c.total_spent || 0), 0))}
          icon="fas fa-money-bill-wave"
          color="yellow"
        />
      </div>

      {/* Customer Filters */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          <i className="fas fa-filter mr-2 text-blue-600"></i>
          Filter Customers
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Search Customers
            </label>
            <input
              type="text"
              placeholder="Search by name, email, phone..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Customer Type
            </label>
            <select
              value={customerTypeFilter}
              onChange={(e) => setCustomerTypeFilter(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">All Types</option>
              <option value="regular">Regular</option>
              <option value="vip">VIP</option>
              <option value="wholesale">Wholesale</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Status
            </label>
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">All Statuses</option>
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
              <option value="blocked">Blocked</option>
            </select>
          </div>

          <div className="flex items-end">
            <Button
              onClick={() => {
                setSearchTerm('');
                setCustomerTypeFilter('all');
                setStatusFilter('all');
              }}
              variant="outline"
              className="w-full"
            >
              <i className="fas fa-times mr-2"></i>
              Clear Filters
            </Button>
          </div>
        </div>
      </div>

      {/* Customers Table */}
      <div className="bg-white rounded-lg shadow-md overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900 flex items-center">
            <i className="fas fa-users mr-2 text-blue-600"></i>
            Phone Point Dar Customers ({filteredCustomers.length})
          </h3>
        </div>

        {filteredCustomers.length === 0 ? (
          <div className="p-12 text-center">
            <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <i className="fas fa-users text-blue-600 text-2xl"></i>
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">No customers found</h3>
            <p className="text-gray-500">
              {searchTerm || customerTypeFilter !== 'all' || statusFilter !== 'all'
                ? "No customers match your current filters."
                : "No customers have been added yet."}
            </p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Customer Details
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Contact Info
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Purchase History
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Customer Type
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredCustomers.map((customer) => (
                  <tr key={customer._id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-10 w-10">
                          <div className="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                            <span className="text-sm font-medium text-blue-600">
                              {customer.name?.charAt(0) || 'C'}
                            </span>
                          </div>
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900">
                            {customer.name || 'N/A'}
                          </div>
                          <div className="text-sm text-gray-500">
                            Customer since {new Date(customer.created_at).toLocaleDateString()}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">{customer.email || 'N/A'}</div>
                      <div className="text-sm text-gray-500">{customer.phone || 'N/A'}</div>
                      {customer.mpesa_number && (
                        <div className="text-xs text-green-600">M-Pesa: {customer.mpesa_number}</div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">
                        {formatTZS(customer.total_spent || 0)}
                      </div>
                      <div className="text-sm text-gray-500">
                        {customer.tech_purchases?.length || 0} product{(customer.tech_purchases?.length || 0) !== 1 ? 's' : ''}
                      </div>
                      {customer.last_purchase_date && (
                        <div className="text-xs text-gray-400">
                          Last: {new Date(customer.last_purchase_date).toLocaleDateString()}
                        </div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        getCustomerTypeColor(customer.customer_type || 'regular')
                      }`}>
                        {(customer.customer_type || 'regular').charAt(0).toUpperCase() + (customer.customer_type || 'regular').slice(1)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        getStatusColor(customer.status || 'active')
                      }`}>
                        {(customer.status || 'active').charAt(0).toUpperCase() + (customer.status || 'active').slice(1)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex items-center space-x-2">
                        <Link
                          to={`/admin/customers/${customer._id}`}
                          className="text-blue-600 hover:text-blue-900 p-1"
                          title="View Customer Details"
                        >
                          <i className="fas fa-eye"></i>
                        </Link>
                        <button
                          onClick={() => {
                            setSelectedCustomer(customer);
                            setShowCustomerModal(true);
                          }}
                          className="text-green-600 hover:text-green-900 p-1"
                          title="Edit Customer"
                        >
                          <i className="fas fa-edit"></i>
                        </button>
                        <Link
                          to={`/admin/customers/${customer._id}/orders`}
                          className="text-purple-600 hover:text-purple-900 p-1"
                          title="View Orders"
                        >
                          <i className="fas fa-shopping-cart"></i>
                        </Link>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </AdminPageWrapper>
  );
};

export default CustomerManagementPage;
