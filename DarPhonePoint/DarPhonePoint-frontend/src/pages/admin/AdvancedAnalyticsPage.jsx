import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { safeApiRequest } from '../../api/apiClient';
import { formatPrice } from '../../utils/priceFormatter';
import { getCategoryConfig } from '../../config/productCategories';
import PageHeader from '../../components/ui/PageHeader';
import LoadingState from '../../components/ui/LoadingState';
import Alert from '../../components/ui/Alert';
import Button from '../../components/ui/Button';

/**
 * Advanced Analytics Page for Phone Point Dar
 * Comprehensive business intelligence and reporting
 */
const AdvancedAnalyticsPage = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [analytics, setAnalytics] = useState({
    salesTrends: [],
    categoryPerformance: [],
    topProducts: [],
    customerInsights: {},
    inventoryAnalysis: {},
    revenueBreakdown: {}
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [timeframe, setTimeframe] = useState('month');

  // Check admin access
  useEffect(() => {
    if (!user || user.role !== 'admin') {
      navigate('/login');
      return;
    }
  }, [user, navigate]);

  useEffect(() => {
    fetchAnalytics();
  }, [timeframe]);

  const fetchAnalytics = async () => {
    try {
      setLoading(true);
      setError('');

      const [
        salesTrendsResponse,
        categoryStatsResponse,
        topProductsResponse,
        inventoryAlertsResponse
      ] = await Promise.all([
        safeApiRequest(`/api/admin/dashboard/sales-trends?period=${timeframe}`),
        safeApiRequest('/api/admin/dashboard/category-stats'),
        safeApiRequest('/api/admin/dashboard/top-products?limit=10'),
        safeApiRequest('/api/admin/dashboard/inventory-alerts')
      ]);

      setAnalytics({
        salesTrends: salesTrendsResponse.data || [],
        categoryPerformance: categoryStatsResponse.data || [],
        topProducts: topProductsResponse.data || [],
        inventoryAnalysis: inventoryAlertsResponse.data || {}
      });

    } catch (err) {
      console.error('Error fetching analytics:', err);
      setError('Failed to load analytics data');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return <LoadingState text="Loading analytics..." />;
  }

  const { salesTrends, categoryPerformance, topProducts, inventoryAnalysis } = analytics;

  return (
    <div className="space-y-6">
      <PageHeader
        title="📊 Advanced Analytics"
        description="Comprehensive business intelligence for Phone Point Dar"
        breadcrumbs={[
          { name: 'Dashboard', href: '/admin' },
          { name: 'Analytics', href: '/admin/analytics' }
        ]}
        actions={
          <div className="flex space-x-3">
            <select
              value={timeframe}
              onChange={(e) => setTimeframe(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="week">Last Week</option>
              <option value="month">Last Month</option>
              <option value="year">Last Year</option>
            </select>
            <Button onClick={fetchAnalytics} className="bg-blue-600 hover:bg-blue-700">
              <i className="fas fa-sync-alt mr-2"></i>
              Refresh
            </Button>
          </div>
        }
      />

      {error && <Alert type="error" message={error} />}

      {/* Sales Performance Overview */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Sales Trends Chart */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <i className="fas fa-chart-line mr-2 text-blue-600"></i>
            Sales Trends ({timeframe})
          </h3>
          <div className="space-y-3">
            {salesTrends.length > 0 ? (
              salesTrends.map((trend, index) => (
                <div key={index} className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                  <div>
                    <p className="font-medium text-gray-900">
                      {trend._id.day ? `${trend._id.day}/${trend._id.month}` : `${trend._id.month}/${trend._id.year}`}
                    </p>
                    <p className="text-sm text-gray-600">{trend.orders} orders</p>
                  </div>
                  <div className="text-right">
                    <p className="font-bold text-green-600">{formatPrice(trend.revenue)}</p>
                    <p className="text-xs text-gray-500">Avg: {formatPrice(trend.averageOrderValue)}</p>
                  </div>
                </div>
              ))
            ) : (
              <p className="text-gray-500 text-center py-8">No sales data available</p>
            )}
          </div>
        </div>

        {/* Category Performance */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <i className="fas fa-chart-pie mr-2 text-purple-600"></i>
            Category Performance
          </h3>
          <div className="space-y-3">
            {categoryPerformance.map((category) => {
              const categoryConfig = getCategoryConfig(category.category);
              return (
                <div key={category.category} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center">
                    <span className="text-2xl mr-3">{categoryConfig.icon}</span>
                    <div>
                      <p className="font-medium text-gray-900">{categoryConfig.label}</p>
                      <p className="text-sm text-gray-600">{category.count} products</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-bold text-gray-900">{formatPrice(category.revenue)}</p>
                    <p className="text-xs text-gray-500">{category.unitsSold} sold</p>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>

      {/* Top Products Analysis */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          <i className="fas fa-star mr-2 text-yellow-600"></i>
          Top Performing Products
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {topProducts.map((product, index) => {
            const categoryConfig = getCategoryConfig(product.category);
            return (
              <div key={product._id} className="p-4 border border-gray-200 rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-2xl">{categoryConfig.icon}</span>
                  <span className="bg-blue-100 text-blue-800 text-xs font-medium px-2 py-1 rounded-full">
                    #{index + 1}
                  </span>
                </div>
                <h4 className="font-medium text-gray-900 mb-1">{product.name}</h4>
                <p className="text-sm text-gray-600 mb-2">{product.brand}</p>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium text-green-600">
                    {product.salesCount} sold
                  </span>
                  <span className="text-sm font-bold text-gray-900">
                    {formatPrice(product.revenue)}
                  </span>
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Inventory Analysis */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <i className="fas fa-exclamation-triangle mr-2 text-red-600"></i>
            Low Stock Items
          </h3>
          <div className="text-center">
            <div className="text-3xl font-bold text-red-600 mb-2">
              {inventoryAnalysis.lowStock?.length || 0}
            </div>
            <p className="text-gray-600">Items need restocking</p>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <i className="fas fa-times-circle mr-2 text-red-600"></i>
            Out of Stock
          </h3>
          <div className="text-center">
            <div className="text-3xl font-bold text-red-600 mb-2">
              {inventoryAnalysis.outOfStock?.length || 0}
            </div>
            <p className="text-gray-600">Items unavailable</p>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <i className="fas fa-boxes mr-2 text-blue-600"></i>
            Overstock Items
          </h3>
          <div className="text-center">
            <div className="text-3xl font-bold text-blue-600 mb-2">
              {inventoryAnalysis.overstock?.length || 0}
            </div>
            <p className="text-gray-600">Items overstocked</p>
          </div>
        </div>
      </div>

      {/* Export Options */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          <i className="fas fa-download mr-2 text-green-600"></i>
          Export Reports
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Button className="bg-green-600 hover:bg-green-700 text-white">
            <i className="fas fa-file-excel mr-2"></i>
            Sales Report
          </Button>
          <Button className="bg-blue-600 hover:bg-blue-700 text-white">
            <i className="fas fa-chart-bar mr-2"></i>
            Category Analysis
          </Button>
          <Button className="bg-purple-600 hover:bg-purple-700 text-white">
            <i className="fas fa-boxes mr-2"></i>
            Inventory Report
          </Button>
          <Button className="bg-orange-600 hover:bg-orange-700 text-white">
            <i className="fas fa-users mr-2"></i>
            Customer Report
          </Button>
        </div>
      </div>
    </div>
  );
};

export default AdvancedAnalyticsPage;
