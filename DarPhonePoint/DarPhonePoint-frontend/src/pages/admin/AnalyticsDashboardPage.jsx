import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import Alert from '../../components/ui/Alert';
import PageHeader from '../../components/ui/PageHeader';
import LoadingState from '../../components/ui/LoadingState';
import ErrorBoundary from '../../components/ui/ErrorBoundary';
import LineChart from '../../components/admin/charts/LineChart';
import Bar<PERSON>hart from '../../components/admin/charts/BarChart';
import AreaChart from '../../components/admin/charts/AreaChart';
import <PERSON><PERSON>hart from '../../components/admin/charts/PieChart';
import HeatmapChart from '../../components/admin/charts/HeatmapChart';
import ChartCard from '../../components/admin/ChartCard';
import StatCard from '../../components/admin/StatCard';
import { fetchAnalyticsData } from '../../services/analyticsService';
import analytics from '../../api/analytics';

// Phone Point Dar Analytics Dashboard
const PhonePointDarAnalyticsDashboard = () => {
  const [timeRange, setTimeRange] = useState('30d');
  const [phoneAnalyticsData, setPhoneAnalyticsData] = useState(null);
  const [heatmapData, setHeatmapData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [heatmapLoading, setHeatmapLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    const loadAnalyticsData = async () => {
      setLoading(true);
      try {
        const data = await fetchAnalyticsData(timeRange);
        setAnalyticsData(data);
        setLoading(false);
      } catch (err) {
        console.error('Error fetching analytics data:', err);
        setError('Failed to load analytics data. Please try again later.');
        setLoading(false);
      }
    };

    loadAnalyticsData();
  }, [timeRange]);

  useEffect(() => {
    const loadHeatmapData = async () => {
      setHeatmapLoading(true);
      try {
        const days = timeRange === '7d' ? 7 : timeRange === '90d' ? 90 : 30;
        const data = await analytics.getWeeklyActivityHeatmap(days);
        setHeatmapData(data);
        setHeatmapLoading(false);
      } catch (err) {
        console.error('Error fetching heatmap data:', err);
        // Don't set error for heatmap, just use fallback data
        setHeatmapLoading(false);
      }
    };

    loadHeatmapData();
  }, [timeRange]);

  const formatTZS = (amount) => {
    return new Intl.NumberFormat('sw-TZ', {
      style: 'currency',
      currency: 'TZS',
      minimumFractionDigits: 0
    }).format(amount);
  };

  if (loading) {
    return <LoadingState fullPage text="Loading Phone Point Dar analytics..." />;
  }

  return (
    <div className="bg-gray-50 py-12">
      <div className="container mx-auto px-4">
        <div className="max-w-7xl mx-auto">
          <PageHeader
            title="📱 Phone Point Dar - Analytics Dashboard"
            description="Track your Tanzania phone retail performance and market insights"
            breadcrumbs={[
              { label: 'Dashboard', path: '/admin' },
              { label: 'Phone Analytics', path: '/admin/analytics' }
            ]}
            actions={
              <div className="inline-flex rounded-md shadow-sm">
                <button
                  type="button"
                  className={`px-4 py-2 text-sm font-medium rounded-l-md ${timeRange === '7d'
                    ? 'bg-blue-600 text-white'
                    : 'bg-white text-gray-700 hover:bg-gray-50'
                    } border border-gray-300`}
                  onClick={() => setTimeRange('7d')}
                >
                  7 Days
                </button>
                <button
                  type="button"
                  className={`px-4 py-2 text-sm font-medium ${timeRange === '30d'
                    ? 'bg-blue-600 text-white'
                    : 'bg-white text-gray-700 hover:bg-gray-50'
                    } border-t border-b border-gray-300`}
                  onClick={() => setTimeRange('30d')}
                >
                  30 Days
                </button>
                <button
                  type="button"
                  className={`px-4 py-2 text-sm font-medium rounded-r-md ${timeRange === '90d'
                    ? 'bg-blue-600 text-white'
                    : 'bg-white text-gray-700 hover:bg-gray-50'
                    } border border-gray-300`}
                  onClick={() => setTimeRange('90d')}
                >
                  90 Days
                </button>
              </div>
            }
          />

          {error && (
            <Alert
              type="error"
              message={error}
              onClose={() => setError('')}
              className="mb-8"
            />
          )}

          {/* Phone Point Dar Summary Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
            <StatCard
              title="Phone Sales Revenue"
              value={formatTZS(phoneAnalyticsData.summary.totalRevenue)}
              icon={<i className="fas fa-money-bill-wave text-2xl"></i>}
              bgColor="bg-green-100"
              textColor="text-green-600"
            />

            <StatCard
              title="Phones Sold"
              value={phoneAnalyticsData.summary.phonesSold}
              icon={<i className="fas fa-mobile-alt text-2xl"></i>}
              bgColor="bg-blue-100"
              textColor="text-blue-600"
            />

            <StatCard
              title="Customers"
              value={phoneAnalyticsData.summary.totalCustomers}
              icon={<i className="fas fa-users text-2xl"></i>}
              bgColor="bg-purple-100"
              textColor="text-purple-600"
            />

            <StatCard
              title="IMEI Tracked"
              value={phoneAnalyticsData.summary.imeiTracked}
              icon={<i className="fas fa-barcode text-2xl"></i>}
              bgColor="bg-yellow-100"
              textColor="text-yellow-600"
            />

            <StatCard
              title="Warranty Claims"
              value={phoneAnalyticsData.summary.warrantyClaims}
              icon={<i className="fas fa-shield-alt text-2xl"></i>}
              bgColor="bg-red-100"
              textColor="text-red-600"
            />
          </div>

          {/* Charts */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
            {/* Revenue Chart */}
            <ErrorBoundary>
              <ChartCard
                title="Revenue"
                description="Revenue over time"
                actions={[
                  {
                    label: 'View Detailed Report',
                    onClick: () => console.log('View detailed revenue report')
                  },
                  {
                    label: 'Compare to Previous Period',
                    onClick: () => console.log('Compare revenue to previous period')
                  }
                ]}
              >
                <AreaChart
                  data={analyticsData.revenueData}
                  labels={analyticsData.dateLabels}
                  colors={['rgba(16, 185, 129, 0.5)']} // green
                  height={250}
                  showGrid={true}
                  options={{
                    yAxisTitle: 'Revenue ($)',
                    datasetLabels: ['Revenue']
                  }}
                />
              </ChartCard>
            </ErrorBoundary>

            {/* Orders Chart */}
            <ErrorBoundary>
              <ChartCard
                title="Orders"
                description="Orders over time"
                actions={[
                  {
                    label: 'View All Orders',
                    onClick: () => console.log('View all orders')
                  }
                ]}
              >
                <BarChart
                  data={analyticsData.ordersData}
                  labels={analyticsData.dateLabels}
                  colors={['#3B82F6']} // blue
                  height={250}
                  showGrid={true}
                  options={{
                    yAxisTitle: 'Orders',
                    datasetLabels: ['Orders']
                  }}
                />
              </ChartCard>
            </ErrorBoundary>

            {/* Users Chart */}
            <ErrorBoundary>
              <ChartCard
                title="New Users"
                description="User growth over time"
                actions={[
                  {
                    label: 'View User Details',
                    onClick: () => console.log('View user details')
                  }
                ]}
              >
                <LineChart
                  data={analyticsData.usersData}
                  labels={analyticsData.dateLabels}
                  color="#8B5CF6" // purple
                  height={250}
                  showGrid={true}
                  options={{
                    yAxisTitle: 'New Users',
                    datasetLabels: ['Users']
                  }}
                />
              </ChartCard>
            </ErrorBoundary>

            {/* Leads Chart */}
            <ErrorBoundary>
              <ChartCard
                title="New Leads"
                description="Lead acquisition over time"
                actions={[
                  {
                    label: 'View Lead Sources',
                    onClick: () => console.log('View lead sources')
                  }
                ]}
              >
                <LineChart
                  data={analyticsData.leadsData}
                  labels={analyticsData.dateLabels}
                  color="#F59E0B" // yellow
                  height={250}
                  showGrid={true}
                  options={{
                    yAxisTitle: 'New Leads',
                    datasetLabels: ['Leads']
                  }}
                />
              </ChartCard>
            </ErrorBoundary>
          </div>

          {/* Product Performance */}
          <div className="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 className="text-lg font-semibold mb-4">Product Performance</h2>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Product
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Sales
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Revenue
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Conversion Rate
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {analyticsData.productPerformance.map((product, index) => (
                    <tr key={index}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        {product.name}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {product.sales}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {product.revenue > 0 ? `$${typeof product.revenue === 'number' ? product.revenue.toFixed(2) : product.revenue}` : 'Free'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {product.conversionRate !== 'N/A' ? `${product.conversionRate}%` : 'N/A'}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          {/* Traffic Sources and Device Types */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <ErrorBoundary>
              <ChartCard
                title="Traffic Sources"
                description="Where your visitors come from"
              >
                <PieChart
                  data={analyticsData.trafficSources.data}
                  labels={analyticsData.trafficSources.labels}
                  colors={['#8B5CF6', '#3B82F6', '#10B981', '#F59E0B']}
                  size={220}
                />
              </ChartCard>
            </ErrorBoundary>

            <ErrorBoundary>
              <ChartCard
                title="Device Types"
                description="What devices your visitors use"
              >
                <PieChart
                  data={analyticsData.deviceTypes.data}
                  labels={analyticsData.deviceTypes.labels}
                  colors={['#8B5CF6', '#3B82F6', '#10B981']}
                  size={220}
                />
              </ChartCard>
            </ErrorBoundary>
          </div>

          {/* Weekly Activity Heatmap */}
          <div className="mt-8">
            <ErrorBoundary>
              <ChartCard
                title="Weekly Activity Heatmap"
                description={`User activity patterns by day and hour ${heatmapData ? `(${heatmapData.totalEvents} events)` : ''}`}
                actions={[
                  {
                    label: 'View Activity Details',
                    onClick: () => console.log('View activity details')
                  }
                ]}
              >
                {heatmapLoading ? (
                  <div className="flex items-center justify-center h-64">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
                    <span className="ml-2 text-gray-600">Loading heatmap...</span>
                  </div>
                ) : (
                  <HeatmapChart
                    data={heatmapData?.heatmapData || [
                      // Fallback data if API fails
                      [0, 0, 0, 0, 0, 0, 0],
                      [0, 0, 0, 0, 0, 0, 0],
                      [0, 0, 0, 0, 0, 0, 0],
                      [0, 0, 0, 0, 0, 0, 0],
                      [0, 0, 0, 0, 0, 0, 0],
                      [0, 0, 0, 0, 0, 0, 0],
                      [0, 0, 0, 0, 0, 0, 0],
                      [0, 0, 0, 0, 0, 0, 0],
                      [0, 0, 0, 0, 0, 0, 0],
                      [0, 0, 0, 0, 0, 0, 0],
                      [0, 0, 0, 0, 0, 0, 0],
                      [0, 0, 0, 0, 0, 0, 0],
                      [0, 0, 0, 0, 0, 0, 0],
                      [0, 0, 0, 0, 0, 0, 0],
                      [0, 0, 0, 0, 0, 0, 0],
                      [0, 0, 0, 0, 0, 0, 0],
                      [0, 0, 0, 0, 0, 0, 0],
                      [0, 0, 0, 0, 0, 0, 0],
                      [0, 0, 0, 0, 0, 0, 0],
                      [0, 0, 0, 0, 0, 0, 0],
                      [0, 0, 0, 0, 0, 0, 0],
                      [0, 0, 0, 0, 0, 0, 0],
                      [0, 0, 0, 0, 0, 0, 0],
                      [0, 0, 0, 0, 0, 0, 0]
                    ]}
                    xLabels={heatmapData?.labels?.days || ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']}
                    yLabels={heatmapData?.labels?.hours || [
                      '12AM', '1AM', '2AM', '3AM', '4AM', '5AM', '6AM', '7AM', '8AM', '9AM', '10AM', '11AM',
                      '12PM', '1PM', '2PM', '3PM', '4PM', '5PM', '6PM', '7PM', '8PM', '9PM', '10PM', '11PM'
                    ]}
                    colorScale="purple"
                    height={400}
                    options={{
                      xAxisTitle: 'Day of Week',
                      yAxisTitle: 'Time of Day'
                    }}
                  />
                )}
              </ChartCard>
            </ErrorBoundary>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PhonePointDarAnalyticsDashboard;
