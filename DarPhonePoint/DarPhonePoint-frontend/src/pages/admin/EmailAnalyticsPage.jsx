import React, { useState, useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { apiService } from '../../services/api';
import { formatDate } from '../../utils/dateUtils';

// Helper function for formatting numbers
const formatNumber = (num) => {
  return new Intl.NumberFormat().format(num);
};
import Button from '../../components/ui/Button';
import Card from '../../components/ui/Card';
import LoadingState from '../../components/ui/LoadingState';
import ErrorState from '../../components/ui/ErrorState';

/**
 * Email Analytics Page for Phone Point Dar
 * Provides detailed analytics and insights for email campaigns
 */
const EmailAnalyticsPage = () => {
  const { user } = useAuth();
  const [analytics, setAnalytics] = useState({
    overview: {
      totalSent: 0,
      totalDelivered: 0,
      totalOpened: 0,
      totalClicked: 0,
      totalBounced: 0,
      totalUnsubscribed: 0,
      deliveryRate: 0,
      openRate: 0,
      clickRate: 0,
      bounceRate: 0
    },
    trends: [],
    topTemplates: [],
    recentCampaigns: [],
    deviceStats: [],
    timeStats: []
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [timeframe, setTimeframe] = useState('30d');

  useEffect(() => {
    fetchAnalytics();
  }, [timeframe]);

  const fetchAnalytics = async () => {
    try {
      setLoading(true);
      // Mock data for demonstration
      const mockAnalytics = {
        overview: {
          totalSent: 15420,
          totalDelivered: 14890,
          totalOpened: 8934,
          totalClicked: 1247,
          totalBounced: 530,
          totalUnsubscribed: 89,
          deliveryRate: 96.6,
          openRate: 60.0,
          clickRate: 14.0,
          bounceRate: 3.4
        },
        trends: [
          { date: '2024-01-01', sent: 450, delivered: 435, opened: 261, clicked: 37 },
          { date: '2024-01-02', sent: 520, delivered: 502, opened: 301, clicked: 42 },
          { date: '2024-01-03', sent: 380, delivered: 367, opened: 220, clicked: 31 },
          { date: '2024-01-04', sent: 610, delivered: 589, opened: 353, clicked: 49 },
          { date: '2024-01-05', sent: 490, delivered: 473, opened: 284, clicked: 40 }
        ],
        topTemplates: [
          { name: 'Welcome Email', sent: 3420, openRate: 68.5, clickRate: 18.2 },
          { name: 'Order Confirmation', sent: 2890, openRate: 72.1, clickRate: 12.4 },
          { name: 'Payment Receipt', sent: 2654, openRate: 69.8, clickRate: 8.9 },
          { name: 'Re-engagement', sent: 1876, openRate: 45.2, clickRate: 22.1 }
        ],
        recentCampaigns: [
          { name: 'New Year Sale', sent: 5420, delivered: 5234, openRate: 58.2, clickRate: 16.8, date: '2024-01-01' },
          { name: 'Product Launch', sent: 3210, delivered: 3098, openRate: 62.4, clickRate: 19.3, date: '2023-12-28' },
          { name: 'Holiday Greetings', sent: 4567, delivered: 4401, openRate: 71.2, clickRate: 14.6, date: '2023-12-25' }
        ],
        deviceStats: [
          { device: 'Mobile', percentage: 68.4, opens: 6108 },
          { device: 'Desktop', percentage: 24.7, opens: 2207 },
          { device: 'Tablet', percentage: 6.9, opens: 619 }
        ],
        timeStats: [
          { hour: '09:00', opens: 892, clicks: 124 },
          { hour: '10:00', opens: 1034, clicks: 145 },
          { hour: '11:00', opens: 967, clicks: 135 },
          { hour: '14:00', opens: 823, clicks: 115 },
          { hour: '15:00', opens: 756, clicks: 106 }
        ]
      };
      
      setAnalytics(mockAnalytics);
    } catch (error) {
      console.error('Error fetching analytics:', error);
      setError('Failed to load email analytics');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return <LoadingState message="Loading email analytics..." />;
  }

  if (error) {
    return <ErrorState message={error} onRetry={fetchAnalytics} />;
  }

  const { overview, topTemplates, recentCampaigns, deviceStats, timeStats } = analytics;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-600 to-blue-800 rounded-lg p-6 text-white">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
          <div>
            <h1 className="text-2xl font-bold mb-2">📊 Email Analytics</h1>
            <p className="text-blue-100">Detailed insights for Phone Point Dar email campaigns</p>
          </div>
          <div className="flex flex-wrap gap-2 mt-4 md:mt-0">
            <select
              value={timeframe}
              onChange={(e) => setTimeframe(e.target.value)}
              className="px-3 py-2 bg-white text-gray-900 rounded-md text-sm"
            >
              <option value="7d">Last 7 days</option>
              <option value="30d">Last 30 days</option>
              <option value="90d">Last 90 days</option>
              <option value="1y">Last year</option>
            </select>
            <Button className="bg-white text-blue-600 hover:bg-gray-100">
              <i className="fas fa-download mr-2"></i>Export Report
            </Button>
          </div>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                <i className="fas fa-paper-plane text-blue-600"></i>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Total Sent</p>
              <p className="text-2xl font-bold text-gray-900">{formatNumber(overview.totalSent)}</p>
              <p className="text-sm text-green-600">↑ 12.5% from last period</p>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                <i className="fas fa-check-circle text-green-600"></i>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Delivery Rate</p>
              <p className="text-2xl font-bold text-gray-900">{overview.deliveryRate}%</p>
              <p className="text-sm text-green-600">↑ 2.1% from last period</p>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                <i className="fas fa-envelope-open text-purple-600"></i>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Open Rate</p>
              <p className="text-2xl font-bold text-gray-900">{overview.openRate}%</p>
              <p className="text-sm text-green-600">↑ 5.3% from last period</p>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
                <i className="fas fa-mouse-pointer text-orange-600"></i>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Click Rate</p>
              <p className="text-2xl font-bold text-gray-900">{overview.clickRate}%</p>
              <p className="text-sm text-green-600">↑ 8.7% from last period</p>
            </div>
          </div>
        </Card>
      </div>

      {/* Performance Overview */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <div className="p-6 border-b border-gray-200">
            <h3 className="text-lg font-semibold">Top Performing Templates</h3>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              {Array.isArray(topTemplates) && topTemplates.map((template, index) => (
                <div key={index} className="flex items-center justify-between py-3 border-b border-gray-100 last:border-b-0">
                  <div className="flex-1">
                    <p className="font-medium text-gray-900">{template.name}</p>
                    <p className="text-sm text-gray-500">{formatNumber(template.sent)} emails sent</p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-medium text-gray-900">
                      {template.openRate}% open | {template.clickRate}% click
                    </p>
                    <div className="w-24 bg-gray-200 rounded-full h-2 mt-1">
                      <div 
                        className="bg-blue-600 h-2 rounded-full" 
                        style={{ width: `${template.openRate}%` }}
                      ></div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </Card>

        <Card>
          <div className="p-6 border-b border-gray-200">
            <h3 className="text-lg font-semibold">Recent Campaigns</h3>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              {Array.isArray(recentCampaigns) && recentCampaigns.map((campaign, index) => (
                <div key={index} className="flex items-center justify-between py-3 border-b border-gray-100 last:border-b-0">
                  <div className="flex-1">
                    <p className="font-medium text-gray-900">{campaign.name}</p>
                    <p className="text-sm text-gray-500">
                      {formatNumber(campaign.sent)} sent • {formatDate(campaign.date)}
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-medium text-gray-900">
                      {campaign.openRate}% open
                    </p>
                    <p className="text-sm text-gray-500">
                      {campaign.clickRate}% click
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </Card>
      </div>

      {/* Device and Time Analytics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <div className="p-6 border-b border-gray-200">
            <h3 className="text-lg font-semibold">Device Breakdown</h3>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              {Array.isArray(deviceStats) && deviceStats.map((device, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex items-center">
                    <i className={`fas ${
                      device.device === 'Mobile' ? 'fa-mobile-alt' :
                      device.device === 'Desktop' ? 'fa-desktop' : 'fa-tablet-alt'
                    } text-gray-400 mr-3`}></i>
                    <span className="font-medium text-gray-900">{device.device}</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <span className="text-sm text-gray-500">{formatNumber(device.opens)} opens</span>
                    <span className="font-medium text-gray-900">{device.percentage}%</span>
                    <div className="w-16 bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-blue-600 h-2 rounded-full" 
                        style={{ width: `${device.percentage}%` }}
                      ></div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </Card>

        <Card>
          <div className="p-6 border-b border-gray-200">
            <h3 className="text-lg font-semibold">Best Sending Times</h3>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              {Array.isArray(timeStats) && timeStats.map((time, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex items-center">
                    <i className="fas fa-clock text-gray-400 mr-3"></i>
                    <span className="font-medium text-gray-900">{time.hour}</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <span className="text-sm text-gray-500">
                      {formatNumber(time.opens)} opens • {formatNumber(time.clicks)} clicks
                    </span>
                    <div className="w-16 bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-green-600 h-2 rounded-full" 
                        style={{ width: `${(time.opens / 1100) * 100}%` }}
                      ></div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </Card>
      </div>

      {/* Detailed Metrics */}
      <Card>
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold">Detailed Email Metrics</h3>
        </div>
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900 mb-1">{formatNumber(overview.totalDelivered)}</div>
              <div className="text-sm text-gray-500">Delivered</div>
              <div className="text-xs text-green-600 mt-1">{overview.deliveryRate}% delivery rate</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900 mb-1">{formatNumber(overview.totalOpened)}</div>
              <div className="text-sm text-gray-500">Opened</div>
              <div className="text-xs text-purple-600 mt-1">{overview.openRate}% open rate</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900 mb-1">{formatNumber(overview.totalClicked)}</div>
              <div className="text-sm text-gray-500">Clicked</div>
              <div className="text-xs text-orange-600 mt-1">{overview.clickRate}% click rate</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900 mb-1">{formatNumber(overview.totalBounced)}</div>
              <div className="text-sm text-gray-500">Bounced</div>
              <div className="text-xs text-red-600 mt-1">{overview.bounceRate}% bounce rate</div>
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default EmailAnalyticsPage;
