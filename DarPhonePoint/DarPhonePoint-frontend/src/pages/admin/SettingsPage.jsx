import React, { useState, useEffect } from 'react';
import PageHeader from '../../components/ui/PageHeader';
import Button from '../../components/ui/Button';
import Alert from '../../components/ui/Alert';
import LoadingState from '../../components/ui/LoadingState';
import { safeApiRequest } from '../../api/apiClient';

/**
 * Phone Point Dar Settings Page
 * Configure Tanzania phone retail business settings
 */
const PhonePointDarSettingsPage = () => {
  const [activeTab, setActiveTab] = useState('general');
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  // General Business Settings
  const [generalSettings, setGeneralSettings] = useState({
    businessName: 'Phone Point Dar',
    businessDescription: 'Premium Tech Products, Mobile Devices & Accessories in Tanzania',
    contactEmail: '<EMAIL>',
    supportPhone: '+255 123 456 789',
    whatsappNumber: '+255 123 456 789',
    businessAddress: 'Dar es Salaam, Tanzania',
    logoUrl: '',
    faviconUrl: '',
    timezone: 'Africa/Dar_es_Salaam',
    currency: 'TZS',
    language: 'en'
  });

  // Tanzania Payment Settings
  const [paymentSettings, setPaymentSettings] = useState({
    mpesaBusinessNumber: '',
    mpesaApiKey: '',
    mpesaApiSecret: '',
    tigoPesaBusinessNumber: '',
    tigoPesaApiKey: '',
    airtelMoneyBusinessNumber: '',
    airtelMoneyApiKey: '',
    bankTransferEnabled: true,
    cashOnDeliveryEnabled: true,
    defaultPaymentMethod: 'mpesa',
    paymentTimeout: 300, // 5 minutes
    enableTestMode: true
  });

  // Tanzania Shipping Settings
  const [shippingSettings, setShippingSettings] = useState({
    dhlEnabled: true,
    dhlApiKey: '',
    dhlAccountNumber: '',
    postaEnabled: true,
    postaApiKey: '',
    localCourierEnabled: true,
    localCourierName: 'Dar Express',
    localCourierPhone: '+255 123 456 789',
    freeShippingThreshold: 500000, // 500,000 TZS
    standardShippingCost: 15000, // 15,000 TZS
    expressShippingCost: 25000, // 25,000 TZS
    deliveryTimeStandard: '3-5 business days',
    deliveryTimeExpress: '1-2 business days'
  });

  // Tech Retail Settings
  const [retailSettings, setRetailSettings] = useState({
    lowStockThreshold: 5,
    enableImeiTracking: true,
    enableWarrantyTracking: true,
    defaultWarrantyPeriod: '1 year',
    enableTradeIn: true,
    tradeInDiscountPercent: 10,
    enableInstallments: false,
    maxInstallmentMonths: 12,
    requireImeiForPhones: true,
    autoGenerateSku: true,
    enableBulkDiscount: true,
    bulkDiscountThreshold: 10,
    enableAccessoriesBundling: true
  });

  // Notification Settings
  const [notificationSettings, setNotificationSettings] = useState({
    emailNotifications: true,
    smsNotifications: true,
    whatsappNotifications: true,
    lowStockAlerts: true,
    newOrderAlerts: true,
    paymentAlerts: true,
    shippingAlerts: true,
    warrantyExpiryAlerts: true,
    customerCommunication: true,
    adminEmail: '<EMAIL>',
    smsProvider: 'local',
    smsApiKey: ''
  });

  useEffect(() => {
    fetchSettings();
  }, []);

  const fetchSettings = async () => {
    setLoading(true);
    try {
      const response = await safeApiRequest({
        method: 'GET',
        url: '/settings'
      });

      if (response.data) {
        const settings = response.data;
        setGeneralSettings(prev => ({ ...prev, ...settings.general }));
        setPaymentSettings(prev => ({ ...prev, ...settings.payment }));
        setShippingSettings(prev => ({ ...prev, ...settings.shipping }));
        setRetailSettings(prev => ({ ...prev, ...settings.retail }));
        setNotificationSettings(prev => ({ ...prev, ...settings.notifications }));
      }
    } catch (err) {
      console.error('Error fetching settings:', err);
      setError('Failed to load settings');
    } finally {
      setLoading(false);
    }
  };

  const handleSaveSettings = async () => {
    setSubmitting(true);
    setError('');
    setSuccess('');

    try {
      const settingsData = {
        general: generalSettings,
        payment: paymentSettings,
        shipping: shippingSettings,
        retail: retailSettings,
        notifications: notificationSettings
      };

      await safeApiRequest({
        method: 'PATCH',
        url: '/settings?category=general',
        data: settingsData
      });

      setSuccess('Phone Point Dar settings updated successfully');
    } catch (err) {
      console.error('Error saving settings:', err);
      setError('Failed to save settings');
    } finally {
      setSubmitting(false);
    }
  };

  const formatTZS = (amount) => {
    return new Intl.NumberFormat('sw-TZ', {
      style: 'currency',
      currency: 'TZS',
      minimumFractionDigits: 0
    }).format(amount);
  };

  const tabs = [
    { id: 'general', name: 'General', icon: 'fas fa-cog' },
    { id: 'payment', name: 'Tanzania Payments', icon: 'fas fa-mobile-alt' },
    { id: 'shipping', name: 'Shipping', icon: 'fas fa-shipping-fast' },
    { id: 'retail', name: 'Tech Retail', icon: 'fas fa-mobile-alt' },
    { id: 'notifications', name: 'Notifications', icon: 'fas fa-bell' }
  ];

  if (loading) {
    return <LoadingState fullPage text="Loading Phone Point Dar settings..." />;
  }

  return (
    <div className="space-y-6">
      <PageHeader
        title="📱 Phone Point Dar Settings"
        description="Configure your Tanzania tech retail business settings"
        breadcrumbs={[
          { name: 'Dashboard', href: '/admin' },
          { name: 'Settings', href: '/admin/settings' }
        ]}
      />

      {error && (
        <Alert
          type="error"
          message={error}
          onClose={() => setError('')}
        />
      )}

      {success && (
        <Alert
          type="success"
          message={success}
          onClose={() => setSuccess('')}
        />
      )}

      {/* Settings Tabs */}
      <div className="bg-white rounded-lg shadow-md overflow-hidden">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6" aria-label="Tabs">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <i className={`${tab.icon} mr-2`}></i>
                {tab.name}
              </button>
            ))}
          </nav>
        </div>

        <div className="p-6">
          {/* General Settings Tab */}
          {activeTab === 'general' && (
            <div className="space-y-6">
              <h3 className="text-lg font-medium text-gray-900 flex items-center">
                <i className="fas fa-cog mr-2 text-blue-600"></i>
                General Business Settings
              </h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Business Name
                  </label>
                  <input
                    type="text"
                    value={generalSettings.businessName}
                    onChange={(e) => setGeneralSettings({ ...generalSettings, businessName: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Contact Email
                  </label>
                  <input
                    type="email"
                    value={generalSettings.contactEmail}
                    onChange={(e) => setGeneralSettings({ ...generalSettings, contactEmail: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Support Phone
                  </label>
                  <input
                    type="tel"
                    value={generalSettings.supportPhone}
                    onChange={(e) => setGeneralSettings({ ...generalSettings, supportPhone: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="+255 123 456 789"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    WhatsApp Number
                  </label>
                  <input
                    type="tel"
                    value={generalSettings.whatsappNumber}
                    onChange={(e) => setGeneralSettings({ ...generalSettings, whatsappNumber: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="+255 123 456 789"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Business Description
                </label>
                <textarea
                  value={generalSettings.businessDescription}
                  onChange={(e) => setGeneralSettings({ ...generalSettings, businessDescription: e.target.value })}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Business Address
                </label>
                <textarea
                  value={generalSettings.businessAddress}
                  onChange={(e) => setGeneralSettings({ ...generalSettings, businessAddress: e.target.value })}
                  rows={2}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Dar es Salaam, Tanzania"
                />
              </div>
            </div>
          )}

          {/* Tanzania Payment Settings Tab */}
          {activeTab === 'payment' && (
            <div className="space-y-6">
              <h3 className="text-lg font-medium text-gray-900 flex items-center">
                <i className="fas fa-mobile-alt mr-2 text-blue-600"></i>
                Tanzania Payment Methods
              </h3>

              {/* M-Pesa Settings */}
              <div className="bg-green-50 p-4 rounded-lg">
                <h4 className="font-medium text-green-900 mb-4 flex items-center">
                  <i className="fas fa-mobile-alt mr-2"></i>
                  M-Pesa Configuration
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Business Number
                    </label>
                    <input
                      type="text"
                      value={paymentSettings.mpesaBusinessNumber}
                      onChange={(e) => setPaymentSettings({ ...paymentSettings, mpesaBusinessNumber: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="123456"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      API Key
                    </label>
                    <input
                      type="password"
                      value={paymentSettings.mpesaApiKey}
                      onChange={(e) => setPaymentSettings({ ...paymentSettings, mpesaApiKey: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                </div>
              </div>

              {/* Tigo Pesa Settings */}
              <div className="bg-blue-50 p-4 rounded-lg">
                <h4 className="font-medium text-blue-900 mb-4 flex items-center">
                  <i className="fas fa-mobile-alt mr-2"></i>
                  Tigo Pesa Configuration
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Business Number
                    </label>
                    <input
                      type="text"
                      value={paymentSettings.tigoPesaBusinessNumber}
                      onChange={(e) => setPaymentSettings({ ...paymentSettings, tigoPesaBusinessNumber: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      API Key
                    </label>
                    <input
                      type="password"
                      value={paymentSettings.tigoPesaApiKey}
                      onChange={(e) => setPaymentSettings({ ...paymentSettings, tigoPesaApiKey: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                </div>
              </div>

              {/* Airtel Money Settings */}
              <div className="bg-red-50 p-4 rounded-lg">
                <h4 className="font-medium text-red-900 mb-4 flex items-center">
                  <i className="fas fa-mobile-alt mr-2"></i>
                  Airtel Money Configuration
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Business Number
                    </label>
                    <input
                      type="text"
                      value={paymentSettings.airtelMoneyBusinessNumber}
                      onChange={(e) => setPaymentSettings({ ...paymentSettings, airtelMoneyBusinessNumber: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      API Key
                    </label>
                    <input
                      type="password"
                      value={paymentSettings.airtelMoneyApiKey}
                      onChange={(e) => setPaymentSettings({ ...paymentSettings, airtelMoneyApiKey: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                </div>
              </div>

              {/* Other Payment Options */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    checked={paymentSettings.bankTransferEnabled}
                    onChange={(e) => setPaymentSettings({ ...paymentSettings, bankTransferEnabled: e.target.checked })}
                    className="mr-3 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label className="text-sm font-medium text-gray-700">
                    Enable Bank Transfer
                  </label>
                </div>
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    checked={paymentSettings.cashOnDeliveryEnabled}
                    onChange={(e) => setPaymentSettings({ ...paymentSettings, cashOnDeliveryEnabled: e.target.checked })}
                    className="mr-3 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label className="text-sm font-medium text-gray-700">
                    Enable Cash on Delivery
                  </label>
                </div>
              </div>
            </div>
          )}

          {/* Shipping Settings Tab */}
          {activeTab === 'shipping' && (
            <div className="space-y-6">
              <h3 className="text-lg font-medium text-gray-900 flex items-center">
                <i className="fas fa-shipping-fast mr-2 text-blue-600"></i>
                Tanzania Shipping Configuration
              </h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Free Shipping Threshold (TZS)
                  </label>
                  <input
                    type="number"
                    value={shippingSettings.freeShippingThreshold}
                    onChange={(e) => setShippingSettings({ ...shippingSettings, freeShippingThreshold: parseInt(e.target.value) || 0 })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="500000"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Current: {formatTZS(shippingSettings.freeShippingThreshold)}
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Standard Shipping Cost (TZS)
                  </label>
                  <input
                    type="number"
                    value={shippingSettings.standardShippingCost}
                    onChange={(e) => setShippingSettings({ ...shippingSettings, standardShippingCost: parseInt(e.target.value) || 0 })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="15000"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Current: {formatTZS(shippingSettings.standardShippingCost)}
                  </p>
                </div>
              </div>

              {/* Shipping Providers */}
              <div className="space-y-4">
                <h4 className="font-medium text-gray-900">Shipping Providers</h4>

                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      checked={shippingSettings.dhlEnabled}
                      onChange={(e) => setShippingSettings({ ...shippingSettings, dhlEnabled: e.target.checked })}
                      className="mr-3 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <div>
                      <p className="font-medium">DHL Tanzania</p>
                      <p className="text-sm text-gray-500">International and domestic express delivery</p>
                    </div>
                  </div>
                  <i className="fas fa-plane text-gray-400"></i>
                </div>

                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      checked={shippingSettings.postaEnabled}
                      onChange={(e) => setShippingSettings({ ...shippingSettings, postaEnabled: e.target.checked })}
                      className="mr-3 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <div>
                      <p className="font-medium">Posta Tanzania</p>
                      <p className="text-sm text-gray-500">National postal service</p>
                    </div>
                  </div>
                  <i className="fas fa-mail-bulk text-gray-400"></i>
                </div>

                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      checked={shippingSettings.localCourierEnabled}
                      onChange={(e) => setShippingSettings({ ...shippingSettings, localCourierEnabled: e.target.checked })}
                      className="mr-3 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <div>
                      <p className="font-medium">Local Courier</p>
                      <p className="text-sm text-gray-500">Same-day delivery in Dar es Salaam</p>
                    </div>
                  </div>
                  <i className="fas fa-motorcycle text-gray-400"></i>
                </div>
              </div>
            </div>
          )}

          {/* Tech Retail Settings Tab */}
          {activeTab === 'retail' && (
            <div className="space-y-6">
              <h3 className="text-lg font-medium text-gray-900 flex items-center">
                <i className="fas fa-mobile-alt mr-2 text-blue-600"></i>
                Tech Retail Configuration
              </h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Low Stock Threshold
                  </label>
                  <input
                    type="number"
                    value={retailSettings.lowStockThreshold}
                    onChange={(e) => setRetailSettings({ ...retailSettings, lowStockThreshold: parseInt(e.target.value) || 0 })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    min="1"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Default Warranty Period
                  </label>
                  <select
                    value={retailSettings.defaultWarrantyPeriod}
                    onChange={(e) => setRetailSettings({ ...retailSettings, defaultWarrantyPeriod: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="6 months">6 Months</option>
                    <option value="1 year">1 Year</option>
                    <option value="2 years">2 Years</option>
                    <option value="3 years">3 Years</option>
                  </select>
                </div>
              </div>

              <div className="space-y-4">
                <h4 className="font-medium text-gray-900">Tech Retail Features</h4>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      checked={retailSettings.enableImeiTracking}
                      onChange={(e) => setRetailSettings({ ...retailSettings, enableImeiTracking: e.target.checked })}
                      className="mr-3 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <label className="text-sm font-medium text-gray-700">
                      Enable IMEI Tracking
                    </label>
                  </div>

                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      checked={retailSettings.enableWarrantyTracking}
                      onChange={(e) => setRetailSettings({ ...retailSettings, enableWarrantyTracking: e.target.checked })}
                      className="mr-3 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <label className="text-sm font-medium text-gray-700">
                      Enable Warranty Tracking
                    </label>
                  </div>

                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      checked={retailSettings.enableTradeIn}
                      onChange={(e) => setRetailSettings({ ...retailSettings, enableTradeIn: e.target.checked })}
                      className="mr-3 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <label className="text-sm font-medium text-gray-700">
                      Enable Trade-in Program
                    </label>
                  </div>

                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      checked={retailSettings.requireImeiForSale}
                      onChange={(e) => setRetailSettings({ ...retailSettings, requireImeiForSale: e.target.checked })}
                      className="mr-3 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <label className="text-sm font-medium text-gray-700">
                      Require IMEI for Phone Sales
                    </label>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Notifications Settings Tab */}
          {activeTab === 'notifications' && (
            <div className="space-y-6">
              <h3 className="text-lg font-medium text-gray-900 flex items-center">
                <i className="fas fa-bell mr-2 text-blue-600"></i>
                Notification Settings
              </h3>

              <div className="space-y-4">
                <h4 className="font-medium text-gray-900">Alert Types</h4>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      checked={notificationSettings.lowStockAlerts}
                      onChange={(e) => setNotificationSettings({ ...notificationSettings, lowStockAlerts: e.target.checked })}
                      className="mr-3 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <label className="text-sm font-medium text-gray-700">
                      Low Stock Alerts
                    </label>
                  </div>

                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      checked={notificationSettings.newOrderAlerts}
                      onChange={(e) => setNotificationSettings({ ...notificationSettings, newOrderAlerts: e.target.checked })}
                      className="mr-3 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <label className="text-sm font-medium text-gray-700">
                      New Order Alerts
                    </label>
                  </div>

                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      checked={notificationSettings.paymentAlerts}
                      onChange={(e) => setNotificationSettings({ ...notificationSettings, paymentAlerts: e.target.checked })}
                      className="mr-3 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <label className="text-sm font-medium text-gray-700">
                      Payment Alerts
                    </label>
                  </div>

                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      checked={notificationSettings.warrantyExpiryAlerts}
                      onChange={(e) => setNotificationSettings({ ...notificationSettings, warrantyExpiryAlerts: e.target.checked })}
                      className="mr-3 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <label className="text-sm font-medium text-gray-700">
                      Warranty Expiry Alerts
                    </label>
                  </div>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Admin Email for Notifications
                </label>
                <input
                  type="email"
                  value={notificationSettings.adminEmail}
                  onChange={(e) => setNotificationSettings({ ...notificationSettings, adminEmail: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="<EMAIL>"
                />
              </div>
            </div>
          )}
        </div>

        {/* Save Button */}
        <div className="px-6 py-4 bg-gray-50 border-t flex justify-end space-x-3">
          <Button
            onClick={handleSaveSettings}
            disabled={submitting}
            className="bg-blue-600 hover:bg-blue-700"
          >
            {submitting ? (
              <>
                <i className="fas fa-spinner fa-spin mr-2"></i>
                Saving...
              </>
            ) : (
              <>
                <i className="fas fa-save mr-2"></i>
                Save Settings
              </>
            )}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default PhonePointDarSettingsPage;
