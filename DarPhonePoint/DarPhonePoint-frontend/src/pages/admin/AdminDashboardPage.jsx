import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import LoadingState from '../../components/ui/LoadingState';
import EnhancedDashboard from '../../components/admin/EnhancedDashboard';

/**
 * Admin Dashboard Page for Phone Point Dar
 * Provides comprehensive admin interface with enhanced analytics
 */
const AdminDashboardPage = () => {
  const { user } = useAuth();
  const navigate = useNavigate();

  // Check admin access
  useEffect(() => {
    if (!user || user.role !== 'admin') {
      navigate('/login');
      return;
    }
  }, [user, navigate]);

  // Loading state for auth check
  if (!user) {
    return <LoadingState message="Loading Phone Point Dar Admin Dashboard..." />;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-blue-100">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <EnhancedDashboard />
      </div>
    </div>
  );
};

export default AdminDashboardPage;
