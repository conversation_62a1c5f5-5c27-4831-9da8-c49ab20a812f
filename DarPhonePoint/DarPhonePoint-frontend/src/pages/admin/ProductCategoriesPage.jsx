import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import AdminPageWrapper, { QuickActionButton, AdminStatsCard } from '../../components/layout/AdminPageWrapper';
import Button from '../../components/ui/Button';
import Modal from '../../components/ui/Modal';
import { safeApiRequest } from '../../api/apiClient';

const ProductCategoriesPage = () => {
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [showModal, setShowModal] = useState(false);
  const [editingCategory, setEditingCategory] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    parent_category: '',
    is_active: true,
    sort_order: 0
  });

  useEffect(() => {
    fetchCategories();
  }, []);

  const fetchCategories = async () => {
    setLoading(true);
    try {
      // Since we don't have a dedicated categories endpoint, we'll use products to extract categories
      const response = await safeApiRequest({
        method: 'GET',
        url: '/products?limit=1000'
      });

      if (response.data && response.data.data) {
        // Extract unique categories from products
        const uniqueCategories = [...new Set(
          response.data.data
            .map(product => product.category)
            .filter(category => category)
        )];

        // Create category objects with stats
        const categoryStats = uniqueCategories.map((categoryName) => {
          const categoryProducts = response.data.data.filter(
            product => product.category === categoryName
          );

          return {
            _id: categoryName.toLowerCase().replace(/\s+/g, '-'),
            name: categoryName,
            description: `${categoryName} products for Phone Point Dar`,
            product_count: categoryProducts.length,
            total_value: categoryProducts.reduce((sum, product) => sum + (product.price || 0), 0),
            is_active: true,
            created_at: new Date().toISOString()
          };
        });

        setCategories(categoryStats);
      }
    } catch (err) {
      console.error('Error fetching categories:', err);
      setError('Failed to load product categories');
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      // For now, we'll simulate category management
      // In a real implementation, this would call a categories API endpoint
      
      if (editingCategory) {
        // Update existing category
        setCategories(prev => prev.map(cat => 
          cat._id === editingCategory._id 
            ? { ...cat, ...formData, updated_at: new Date().toISOString() }
            : cat
        ));
        setSuccess('Category updated successfully');
      } else {
        // Add new category
        const newCategory = {
          _id: formData.name.toLowerCase().replace(/\s+/g, '-'),
          ...formData,
          product_count: 0,
          total_value: 0,
          created_at: new Date().toISOString()
        };
        setCategories(prev => [...prev, newCategory]);
        setSuccess('Category created successfully');
      }

      setShowModal(false);
      setEditingCategory(null);
      setFormData({
        name: '',
        description: '',
        parent_category: '',
        is_active: true,
        sort_order: 0
      });
    } catch (err) {
      console.error('Error saving category:', err);
      setError('Failed to save category');
    }
  };

  const handleEdit = (category) => {
    setEditingCategory(category);
    setFormData({
      name: category.name,
      description: category.description,
      parent_category: category.parent_category || '',
      is_active: category.is_active,
      sort_order: category.sort_order || 0
    });
    setShowModal(true);
  };

  const handleDelete = async (categoryId) => {
    if (!window.confirm('Are you sure you want to delete this category?')) return;

    try {
      setCategories(prev => prev.filter(cat => cat._id !== categoryId));
      setSuccess('Category deleted successfully');
    } catch (err) {
      console.error('Error deleting category:', err);
      setError('Failed to delete category');
    }
  };

  const formatTZS = (amount) => {
    return new Intl.NumberFormat('sw-TZ', {
      style: 'currency',
      currency: 'TZS',
      minimumFractionDigits: 0
    }).format(amount);
  };

  // Calculate stats
  const totalCategories = categories.length;
  const activeCategories = categories.filter(cat => cat.is_active).length;
  const totalProducts = categories.reduce((sum, cat) => sum + cat.product_count, 0);
  const totalValue = categories.reduce((sum, cat) => sum + cat.total_value, 0);

  return (
    <AdminPageWrapper
      title="📦 Product Categories Management"
      description="Manage product categories for Phone Point Dar tech store"
      breadcrumbs={[
        { name: 'Dashboard', href: '/admin' },
        { name: 'Products', href: '/admin/products' },
        { name: 'Categories', href: '/admin/products/categories' }
      ]}
      actions={
        <QuickActionButton
          icon="fas fa-plus"
          onClick={() => setShowModal(true)}
        >
          Add Category
        </QuickActionButton>
      }
      loading={loading}
      error={error}
      success={success}
      onErrorClose={() => setError('')}
      onSuccessClose={() => setSuccess('')}
    >
      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        <AdminStatsCard
          title="Total Categories"
          value={totalCategories}
          icon="fas fa-tags"
          color="blue"
        />
        <AdminStatsCard
          title="Active Categories"
          value={activeCategories}
          icon="fas fa-check-circle"
          color="green"
        />
        <AdminStatsCard
          title="Total Products"
          value={totalProducts}
          icon="fas fa-mobile-alt"
          color="purple"
        />
        <AdminStatsCard
          title="Total Value"
          value={formatTZS(totalValue)}
          icon="fas fa-money-bill-wave"
          color="yellow"
        />
      </div>

      {/* Categories Table */}
      <div className="bg-white rounded-lg shadow-md overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Product Categories</h3>
        </div>
        
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Category
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Products
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Total Value
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {categories.map((category) => (
                <tr key={category._id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900">
                        {category.name}
                      </div>
                      <div className="text-sm text-gray-500">
                        {category.description}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {category.product_count}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {formatTZS(category.total_value)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      category.is_active 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {category.is_active ? 'Active' : 'Inactive'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                    <button
                      onClick={() => handleEdit(category)}
                      className="text-blue-600 hover:text-blue-900"
                    >
                      <i className="fas fa-edit"></i>
                    </button>
                    <button
                      onClick={() => handleDelete(category._id)}
                      className="text-red-600 hover:text-red-900"
                    >
                      <i className="fas fa-trash"></i>
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Add/Edit Category Modal */}
      <Modal
        isOpen={showModal}
        onClose={() => {
          setShowModal(false);
          setEditingCategory(null);
          setFormData({
            name: '',
            description: '',
            parent_category: '',
            is_active: true,
            sort_order: 0
          });
        }}
        title={editingCategory ? 'Edit Category' : 'Add New Category'}
      >
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Category Name
            </label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Description
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              rows="3"
            />
          </div>

          <div className="flex items-center">
            <input
              type="checkbox"
              id="is_active"
              checked={formData.is_active}
              onChange={(e) => setFormData({ ...formData, is_active: e.target.checked })}
              className="mr-2"
            />
            <label htmlFor="is_active" className="text-sm font-medium text-gray-700">
              Active Category
            </label>
          </div>

          <div className="flex justify-end space-x-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => setShowModal(false)}
            >
              Cancel
            </Button>
            <Button type="submit">
              {editingCategory ? 'Update' : 'Create'} Category
            </Button>
          </div>
        </form>
      </Modal>
    </AdminPageWrapper>
  );
};

export default ProductCategoriesPage;
