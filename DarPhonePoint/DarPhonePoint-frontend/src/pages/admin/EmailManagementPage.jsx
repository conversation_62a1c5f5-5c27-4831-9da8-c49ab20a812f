import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { apiService } from '../../services/api';
import { formatDate } from '../../utils/dateUtils';
import LoadingState from '../../components/ui/LoadingState';
import ErrorState from '../../components/ui/ErrorState';
import Button from '../../components/ui/Button';
import Card from '../../components/ui/Card';
import Alert from '../../components/ui/Alert';

/**
 * Email Management Dashboard for Phone Point Dar
 * Provides overview and quick access to all email operations
 */
const EmailManagementPage = () => {
  const { user } = useAuth();
  const [emailStats, setEmailStats] = useState({
    totalSent: 0,
    totalDelivered: 0,
    totalFailed: 0,
    deliveryRate: 0,
    recentEmails: [],
    templates: [],
    sequences: []
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    fetchEmailStats();
  }, []);

  const fetchEmailStats = async () => {
    try {
      setLoading(true);
      const response = await apiService.get('/admin/emails/stats');
      setEmailStats(response.data);
    } catch (error) {
      console.error('Error fetching email stats:', error);
      setError('Failed to load email statistics');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return <LoadingState message="Loading email management dashboard..." />;
  }

  if (error) {
    return <ErrorState message={error} onRetry={fetchEmailStats} />;
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-600 to-blue-800 rounded-lg p-6 text-white">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
          <div>
            <h1 className="text-2xl font-bold mb-2">📧 Email Management</h1>
            <p className="text-blue-100">Manage customer communications for Phone Point Dar</p>
          </div>
          <div className="flex flex-wrap gap-2 mt-4 md:mt-0">
            <Link to="/admin/emails/send">
              <Button className="bg-white text-blue-600 hover:bg-gray-100">
                <i className="fas fa-paper-plane mr-2"></i>Send Email
              </Button>
            </Link>
            <Link to="/admin/emails/templates">
              <Button className="bg-blue-500 hover:bg-blue-400 text-white">
                <i className="fas fa-file-alt mr-2"></i>Templates
              </Button>
            </Link>
          </div>
        </div>
      </div>

      {/* Email Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                <i className="fas fa-paper-plane text-blue-600"></i>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Total Sent</p>
              <p className="text-2xl font-bold text-gray-900">{emailStats.totalSent.toLocaleString()}</p>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                <i className="fas fa-check-circle text-green-600"></i>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Delivered</p>
              <p className="text-2xl font-bold text-gray-900">{emailStats.totalDelivered.toLocaleString()}</p>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                <i className="fas fa-exclamation-circle text-red-600"></i>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Failed</p>
              <p className="text-2xl font-bold text-gray-900">{emailStats.totalFailed.toLocaleString()}</p>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                <i className="fas fa-percentage text-purple-600"></i>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Delivery Rate</p>
              <p className="text-2xl font-bold text-gray-900">{emailStats.deliveryRate.toFixed(1)}%</p>
            </div>
          </div>
        </Card>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <Card className="p-6">
          <div className="flex items-center mb-4">
            <i className="fas fa-paper-plane text-blue-600 text-xl mr-3"></i>
            <h3 className="text-lg font-semibold">Send Email</h3>
          </div>
          <p className="text-gray-600 mb-4">Send emails to customers, groups, or individual recipients</p>
          <Link to="/admin/emails/send">
            <Button className="w-full">
              <i className="fas fa-paper-plane mr-2"></i>Compose Email
            </Button>
          </Link>
        </Card>

        <Card className="p-6">
          <div className="flex items-center mb-4">
            <i className="fas fa-file-alt text-green-600 text-xl mr-3"></i>
            <h3 className="text-lg font-semibold">Email Templates</h3>
          </div>
          <p className="text-gray-600 mb-4">Manage and customize email templates for Phone Point Dar</p>
          <Link to="/admin/emails/templates">
            <Button variant="outline" className="w-full">
              <i className="fas fa-file-alt mr-2"></i>Manage Templates
            </Button>
          </Link>
        </Card>

        <Card className="p-6">
          <div className="flex items-center mb-4">
            <i className="fas fa-list text-purple-600 text-xl mr-3"></i>
            <h3 className="text-lg font-semibold">Email Sequences</h3>
          </div>
          <p className="text-gray-600 mb-4">Set up automated email sequences for customer engagement</p>
          <Link to="/admin/emails/sequences">
            <Button variant="outline" className="w-full">
              <i className="fas fa-list mr-2"></i>Manage Sequences
            </Button>
          </Link>
        </Card>
      </div>

      {/* Recent Email Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <div className="p-6 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold">Recent Emails</h3>
              <Link to="/admin/emails/logs" className="text-blue-600 hover:text-blue-800 text-sm">
                View All
              </Link>
            </div>
          </div>
          <div className="p-6">
            {emailStats.recentEmails.length > 0 ? (
              <div className="space-y-4">
                {emailStats.recentEmails.slice(0, 5).map((email, index) => (
                  <div key={index} className="flex items-center justify-between py-2 border-b border-gray-100 last:border-b-0">
                    <div className="flex-1">
                      <p className="font-medium text-gray-900">{email.subject}</p>
                      <p className="text-sm text-gray-500">To: {email.recipient}</p>
                    </div>
                    <div className="text-right">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        email.status === 'delivered' ? 'bg-green-100 text-green-800' :
                        email.status === 'failed' ? 'bg-red-100 text-red-800' :
                        'bg-yellow-100 text-yellow-800'
                      }`}>
                        {email.status}
                      </span>
                      <p className="text-xs text-gray-500 mt-1">{formatDate(email.sentAt)}</p>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-gray-500 text-center py-8">No recent emails found</p>
            )}
          </div>
        </Card>

        <Card>
          <div className="p-6 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold">Email Templates</h3>
              <Link to="/admin/emails/templates" className="text-blue-600 hover:text-blue-800 text-sm">
                Manage All
              </Link>
            </div>
          </div>
          <div className="p-6">
            {emailStats.templates.length > 0 ? (
              <div className="space-y-3">
                {emailStats.templates.slice(0, 5).map((template, index) => (
                  <div key={index} className="flex items-center justify-between py-2">
                    <div>
                      <p className="font-medium text-gray-900">{template.name}</p>
                      <p className="text-sm text-gray-500">{template.description}</p>
                    </div>
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      template.isActive ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                    }`}>
                      {template.isActive ? 'Active' : 'Inactive'}
                    </span>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-gray-500 text-center py-8">No templates found</p>
            )}
          </div>
        </Card>
      </div>

      {/* Email Analytics Summary */}
      <Card>
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold">Email Performance</h3>
        </div>
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="text-3xl font-bold text-blue-600 mb-2">{emailStats.deliveryRate.toFixed(1)}%</div>
              <div className="text-sm text-gray-500">Delivery Rate</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-green-600 mb-2">
                {emailStats.totalSent > 0 ? ((emailStats.totalDelivered / emailStats.totalSent) * 100).toFixed(1) : 0}%
              </div>
              <div className="text-sm text-gray-500">Success Rate</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-purple-600 mb-2">{emailStats.templates.length}</div>
              <div className="text-sm text-gray-500">Active Templates</div>
            </div>
          </div>
          <div className="mt-6 text-center">
            <Link to="/admin/emails/analytics">
              <Button variant="outline">
                <i className="fas fa-chart-line mr-2"></i>View Detailed Analytics
              </Button>
            </Link>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default EmailManagementPage;
