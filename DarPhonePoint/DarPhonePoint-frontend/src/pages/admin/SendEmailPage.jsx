import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { apiService } from '../../services/api';
import Button from '../../components/ui/Button';
import Card from '../../components/ui/Card';
import Alert from '../../components/ui/Alert';
import FormField from '../../components/ui/FormField';
import LoadingState from '../../components/ui/LoadingState';

/**
 * Send Email Page for Phone Point Dar Admin
 * Allows admins to send emails to customers
 */
const SendEmailPage = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [templates, setTemplates] = useState([]);
  const [customers, setCustomers] = useState([]);

  const [emailData, setEmailData] = useState({
    recipients: '',
    recipientType: 'individual', // individual, group, all
    subject: '',
    message: '',
    template: '',
    priority: 'normal',
    sendNow: true,
    scheduledDate: '',
    scheduledTime: ''
  });

  useEffect(() => {
    fetchTemplates();
    fetchCustomers();
  }, []);

  const fetchTemplates = async () => {
    try {
      const response = await apiService.get('/admin/emails/templates');
      // Ensure we always have an array
      const templatesData = response?.data?.data || response?.data || [];
      setTemplates(Array.isArray(templatesData) ? templatesData : []);
    } catch (error) {
      console.error('Error fetching templates:', error);
      setTemplates([]); // Set empty array on error
    }
  };

  const fetchCustomers = async () => {
    try {
      const response = await apiService.get('/admin/customers?limit=100');
      // Ensure we always have an array
      const customersData = response?.data?.customers || response?.data?.data || response?.data || [];
      setCustomers(Array.isArray(customersData) ? customersData : []);
    } catch (error) {
      console.error('Error fetching customers:', error);
      setCustomers([]); // Set empty array on error
    }
  };

  const handleTemplateSelect = async (templateId) => {
    if (!templateId) {
      setEmailData(prev => ({ ...prev, template: '', subject: '', message: '' }));
      return;
    }

    try {
      const response = await apiService.get(`/admin/emails/templates/${templateId}`);
      const template = response.data;
      
      setEmailData(prev => ({
        ...prev,
        template: templateId,
        subject: template.subject || '',
        message: template.content || ''
      }));
    } catch (error) {
      console.error('Error fetching template:', error);
      setError('Failed to load template');
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    setSuccess('');

    try {
      // Validate required fields
      if (!emailData.recipients && emailData.recipientType === 'individual') {
        throw new Error('Please specify recipients');
      }
      if (!emailData.subject) {
        throw new Error('Subject is required');
      }
      if (!emailData.message) {
        throw new Error('Message content is required');
      }

      const payload = {
        ...emailData,
        recipients: emailData.recipientType === 'individual' 
          ? emailData.recipients.split(',').map(email => email.trim())
          : emailData.recipientType
      };

      const response = await apiService.post('/admin/emails/send', payload);
      
      setSuccess(`Email ${emailData.sendNow ? 'sent' : 'scheduled'} successfully!`);
      
      // Reset form
      setEmailData({
        recipients: '',
        recipientType: 'individual',
        subject: '',
        message: '',
        template: '',
        priority: 'normal',
        sendNow: true,
        scheduledDate: '',
        scheduledTime: ''
      });

      // Redirect to email logs after 2 seconds
      setTimeout(() => {
        navigate('/admin/emails/logs');
      }, 2000);

    } catch (error) {
      console.error('Error sending email:', error);
      setError(error.response?.data?.message || error.message || 'Failed to send email');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field, value) => {
    setEmailData(prev => ({ ...prev, [field]: value }));
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-600 to-blue-800 rounded-lg p-6 text-white">
        <h1 className="text-2xl font-bold mb-2">📧 Send Email</h1>
        <p className="text-blue-100">Send emails to Phone Point Dar customers</p>
      </div>

      {error && (
        <Alert
          type="error"
          message={error}
          onClose={() => setError('')}
        />
      )}

      {success && (
        <Alert
          type="success"
          message={success}
          onClose={() => setSuccess('')}
        />
      )}

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Recipients Section */}
        <Card>
          <div className="p-6 border-b border-gray-200">
            <h3 className="text-lg font-semibold">Recipients</h3>
          </div>
          <div className="p-6 space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Recipient Type
              </label>
              <select
                value={emailData.recipientType}
                onChange={(e) => handleInputChange('recipientType', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="individual">Individual Recipients</option>
                <option value="all_customers">All Customers</option>
                <option value="recent_customers">Recent Customers (30 days)</option>
                <option value="high_value_customers">High Value Customers</option>
              </select>
            </div>

            {emailData.recipientType === 'individual' && (
              <FormField
                label="Email Addresses"
                name="recipients"
                type="text"
                value={emailData.recipients}
                onChange={(e) => handleInputChange('recipients', e.target.value)}
                placeholder="<EMAIL>, <EMAIL>"
                helpText="Enter email addresses separated by commas"
                required
              />
            )}

            {emailData.recipientType !== 'individual' && (
              <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
                <p className="text-sm text-blue-800">
                  <i className="fas fa-info-circle mr-2"></i>
                  {emailData.recipientType === 'all_customers' && 'Email will be sent to all registered customers'}
                  {emailData.recipientType === 'recent_customers' && 'Email will be sent to customers who made purchases in the last 30 days'}
                  {emailData.recipientType === 'high_value_customers' && 'Email will be sent to customers with high purchase value'}
                </p>
              </div>
            )}
          </div>
        </Card>

        {/* Email Content Section */}
        <Card>
          <div className="p-6 border-b border-gray-200">
            <h3 className="text-lg font-semibold">Email Content</h3>
          </div>
          <div className="p-6 space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Use Template (Optional)
              </label>
              <select
                value={emailData.template}
                onChange={(e) => handleTemplateSelect(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">Select a template...</option>
                {Array.isArray(templates) && templates.map((template) => (
                  <option key={template._id} value={template._id}>
                    {template.name}
                  </option>
                ))}
              </select>
            </div>

            <FormField
              label="Subject"
              name="subject"
              type="text"
              value={emailData.subject}
              onChange={(e) => handleInputChange('subject', e.target.value)}
              placeholder="Enter email subject"
              required
            />

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Message Content
              </label>
              <textarea
                value={emailData.message}
                onChange={(e) => handleInputChange('message', e.target.value)}
                rows={12}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Enter your email message here..."
                required
              />
              <p className="text-sm text-gray-500 mt-1">
                You can use HTML formatting in your message
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Priority
              </label>
              <select
                value={emailData.priority}
                onChange={(e) => handleInputChange('priority', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="low">Low</option>
                <option value="normal">Normal</option>
                <option value="high">High</option>
              </select>
            </div>
          </div>
        </Card>

        {/* Scheduling Section */}
        <Card>
          <div className="p-6 border-b border-gray-200">
            <h3 className="text-lg font-semibold">Delivery Options</h3>
          </div>
          <div className="p-6 space-y-4">
            <div className="flex items-center space-x-4">
              <label className="flex items-center">
                <input
                  type="radio"
                  name="sendTiming"
                  checked={emailData.sendNow}
                  onChange={() => handleInputChange('sendNow', true)}
                  className="mr-2"
                />
                Send Now
              </label>
              <label className="flex items-center">
                <input
                  type="radio"
                  name="sendTiming"
                  checked={!emailData.sendNow}
                  onChange={() => handleInputChange('sendNow', false)}
                  className="mr-2"
                />
                Schedule for Later
              </label>
            </div>

            {!emailData.sendNow && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  label="Scheduled Date"
                  name="scheduledDate"
                  type="date"
                  value={emailData.scheduledDate}
                  onChange={(e) => handleInputChange('scheduledDate', e.target.value)}
                  required={!emailData.sendNow}
                />
                <FormField
                  label="Scheduled Time"
                  name="scheduledTime"
                  type="time"
                  value={emailData.scheduledTime}
                  onChange={(e) => handleInputChange('scheduledTime', e.target.value)}
                  required={!emailData.sendNow}
                />
              </div>
            )}
          </div>
        </Card>

        {/* Action Buttons */}
        <div className="flex justify-end space-x-4">
          <Button
            type="button"
            variant="outline"
            onClick={() => navigate('/admin/emails')}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            disabled={loading}
            className="bg-blue-600 hover:bg-blue-700"
          >
            {loading ? (
              <>
                <i className="fas fa-spinner fa-spin mr-2"></i>
                {emailData.sendNow ? 'Sending...' : 'Scheduling...'}
              </>
            ) : (
              <>
                <i className="fas fa-paper-plane mr-2"></i>
                {emailData.sendNow ? 'Send Email' : 'Schedule Email'}
              </>
            )}
          </Button>
        </div>
      </form>
    </div>
  );
};

export default SendEmailPage;
