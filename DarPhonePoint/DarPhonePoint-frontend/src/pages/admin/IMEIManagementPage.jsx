import React, { useState, useEffect } from 'react';
import AdminPageWrapper, { QuickActionButton, AdminStatsCard } from '../../components/layout/AdminPageWrapper';
import Button from '../../components/ui/Button';
import Modal from '../../components/ui/Modal';
import { safeApiRequest } from '../../api/apiClient';

const IMEIManagementPage = () => {
  const [imeiDevices, setImeiDevices] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [showModal, setShowModal] = useState(false);
  const [editingDevice, setEditingDevice] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [conditionFilter, setConditionFilter] = useState('all');
  const [formData, setFormData] = useState({
    imei: '',
    product_id: '',
    product_name: '',
    brand: '',
    model: '',
    condition: 'new',
    status: 'available',
    warranty_start: '',
    warranty_end: '',
    purchase_price: '',
    selling_price: '',
    supplier: '',
    notes: ''
  });

  useEffect(() => {
    fetchIMEIDevices();
  }, []);

  const fetchIMEIDevices = async () => {
    setLoading(true);
    try {
      // Since we don't have a dedicated IMEI endpoint, we'll simulate with inventory data
      const response = await safeApiRequest({
        method: 'GET',
        url: '/inventory'
      });

      if (response.data && response.data.data) {
        // Filter for devices that should have IMEI tracking
        const imeiDevices = response.data.data
          .filter(item => item.product && (
            item.product.category?.toLowerCase().includes('smartphone') ||
            item.product.category?.toLowerCase().includes('phone') ||
            item.product.category?.toLowerCase().includes('tablet')
          ))
          .map(item => ({
            _id: item._id,
            imei: generateMockIMEI(),
            product_id: item.product._id,
            product_name: item.product.name,
            brand: item.product.brand,
            model: item.product.model || 'Standard',
            condition: item.condition || 'new',
            status: item.quantity > 0 ? 'available' : 'sold',
            warranty_start: new Date().toISOString().split('T')[0],
            warranty_end: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
            purchase_price: item.product.cost_price || item.product.price * 0.7,
            selling_price: item.product.price,
            supplier: 'Official Distributor Tanzania',
            notes: `IMEI tracked device - ${item.product.name}`,
            created_at: item.created_at || new Date().toISOString(),
            updated_at: item.updated_at || new Date().toISOString()
          }));

        setImeiDevices(imeiDevices);
      }
    } catch (err) {
      console.error('Error fetching IMEI devices:', err);
      setError('Failed to load IMEI tracking data');
    } finally {
      setLoading(false);
    }
  };

  const generateMockIMEI = () => {
    // Generate a mock IMEI number (15 digits)
    return Array.from({ length: 15 }, () => Math.floor(Math.random() * 10)).join('');
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      // Validate IMEI format (15 digits)
      if (!/^\d{15}$/.test(formData.imei)) {
        setError('IMEI must be exactly 15 digits');
        return;
      }

      if (editingDevice) {
        setImeiDevices(prev => prev.map(device => 
          device._id === editingDevice._id 
            ? { ...device, ...formData, updated_at: new Date().toISOString() }
            : device
        ));
        setSuccess('IMEI device updated successfully');
      } else {
        const newDevice = {
          _id: Date.now().toString(),
          ...formData,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };
        setImeiDevices(prev => [...prev, newDevice]);
        setSuccess('IMEI device added successfully');
      }

      setShowModal(false);
      setEditingDevice(null);
      resetForm();
    } catch (err) {
      console.error('Error saving IMEI device:', err);
      setError('Failed to save IMEI device');
    }
  };

  const resetForm = () => {
    setFormData({
      imei: '',
      product_id: '',
      product_name: '',
      brand: '',
      model: '',
      condition: 'new',
      status: 'available',
      warranty_start: '',
      warranty_end: '',
      purchase_price: '',
      selling_price: '',
      supplier: '',
      notes: ''
    });
  };

  const handleEdit = (device) => {
    setEditingDevice(device);
    setFormData({
      imei: device.imei,
      product_id: device.product_id,
      product_name: device.product_name,
      brand: device.brand,
      model: device.model,
      condition: device.condition,
      status: device.status,
      warranty_start: device.warranty_start,
      warranty_end: device.warranty_end,
      purchase_price: device.purchase_price,
      selling_price: device.selling_price,
      supplier: device.supplier,
      notes: device.notes
    });
    setShowModal(true);
  };

  const handleDelete = async (deviceId) => {
    if (!window.confirm('Are you sure you want to delete this IMEI record?')) return;
    try {
      setImeiDevices(prev => prev.filter(device => device._id !== deviceId));
      setSuccess('IMEI device deleted successfully');
    } catch (err) {
      setError('Failed to delete IMEI device');
    }
  };

  const formatTZS = (amount) => {
    return new Intl.NumberFormat('sw-TZ', {
      style: 'currency',
      currency: 'TZS',
      minimumFractionDigits: 0
    }).format(amount);
  };

  // Filter devices based on search and filters
  const filteredDevices = imeiDevices.filter(device => {
    const matchesSearch = !searchTerm || 
      device.imei.includes(searchTerm) ||
      device.product_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      device.brand.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === 'all' || device.status === statusFilter;
    const matchesCondition = conditionFilter === 'all' || device.condition === conditionFilter;
    
    return matchesSearch && matchesStatus && matchesCondition;
  });

  // Calculate stats
  const totalDevices = imeiDevices.length;
  const availableDevices = imeiDevices.filter(d => d.status === 'available').length;
  const soldDevices = imeiDevices.filter(d => d.status === 'sold').length;
  const totalValue = imeiDevices.reduce((sum, d) => sum + (d.selling_price || 0), 0);

  return (
    <AdminPageWrapper
      title="📱 IMEI Management"
      description="Track and manage IMEI numbers for Phone Point Dar devices"
      breadcrumbs={[
        { name: 'Dashboard', href: '/admin' },
        { name: 'Inventory', href: '/admin/inventory' },
        { name: 'IMEI Management', href: '/admin/inventory/imei' }
      ]}
      actions={
        <QuickActionButton
          icon="fas fa-plus"
          onClick={() => setShowModal(true)}
        >
          Add IMEI Device
        </QuickActionButton>
      }
      loading={loading}
      error={error}
      success={success}
      onErrorClose={() => setError('')}
      onSuccessClose={() => setSuccess('')}
    >
      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        <AdminStatsCard
          title="Total Devices"
          value={totalDevices}
          icon="fas fa-mobile-alt"
          color="blue"
        />
        <AdminStatsCard
          title="Available"
          value={availableDevices}
          icon="fas fa-check-circle"
          color="green"
        />
        <AdminStatsCard
          title="Sold"
          value={soldDevices}
          icon="fas fa-shopping-cart"
          color="purple"
        />
        <AdminStatsCard
          title="Total Value"
          value={formatTZS(totalValue)}
          icon="fas fa-money-bill-wave"
          color="yellow"
        />
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg shadow-md p-6 mb-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Search IMEI/Product
            </label>
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="Search by IMEI or product name..."
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Status
            </label>
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">All Status</option>
              <option value="available">Available</option>
              <option value="sold">Sold</option>
              <option value="reserved">Reserved</option>
              <option value="defective">Defective</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Condition
            </label>
            <select
              value={conditionFilter}
              onChange={(e) => setConditionFilter(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">All Conditions</option>
              <option value="new">New</option>
              <option value="like_new">Like New</option>
              <option value="good">Good</option>
              <option value="fair">Fair</option>
              <option value="poor">Poor</option>
            </select>
          </div>

          <div className="flex items-end">
            <Button
              onClick={() => {
                setSearchTerm('');
                setStatusFilter('all');
                setConditionFilter('all');
              }}
              variant="outline"
              className="w-full"
            >
              <i className="fas fa-undo mr-2"></i>
              Reset Filters
            </Button>
          </div>
        </div>
      </div>

      {/* IMEI Devices Table */}
      <div className="bg-white rounded-lg shadow-md overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">
            IMEI Tracked Devices ({filteredDevices.length})
          </h3>
        </div>
        
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  IMEI
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Device
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Condition
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Price
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Warranty
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredDevices.map((device) => (
                <tr key={device._id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-mono text-gray-900">{device.imei}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900">
                        {device.product_name}
                      </div>
                      <div className="text-sm text-gray-500">
                        {device.brand} {device.model}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      device.condition === 'new' ? 'bg-green-100 text-green-800' :
                      device.condition === 'like_new' ? 'bg-blue-100 text-blue-800' :
                      device.condition === 'good' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-red-100 text-red-800'
                    }`}>
                      {device.condition.replace('_', ' ')}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      device.status === 'available' ? 'bg-green-100 text-green-800' :
                      device.status === 'sold' ? 'bg-gray-100 text-gray-800' :
                      device.status === 'reserved' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-red-100 text-red-800'
                    }`}>
                      {device.status}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {formatTZS(device.selling_price)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {device.warranty_end ? new Date(device.warranty_end).toLocaleDateString() : '-'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                    <button
                      onClick={() => handleEdit(device)}
                      className="text-blue-600 hover:text-blue-900"
                    >
                      <i className="fas fa-edit"></i>
                    </button>
                    <button
                      onClick={() => handleDelete(device._id)}
                      className="text-red-600 hover:text-red-900"
                    >
                      <i className="fas fa-trash"></i>
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Add/Edit IMEI Device Modal */}
      <Modal
        isOpen={showModal}
        onClose={() => {
          setShowModal(false);
          setEditingDevice(null);
          resetForm();
        }}
        title={editingDevice ? 'Edit IMEI Device' : 'Add New IMEI Device'}
      >
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                IMEI Number *
              </label>
              <input
                type="text"
                value={formData.imei}
                onChange={(e) => setFormData({ ...formData, imei: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono"
                placeholder="15-digit IMEI number"
                maxLength="15"
                pattern="\d{15}"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Product Name *
              </label>
              <input
                type="text"
                value={formData.product_name}
                onChange={(e) => setFormData({ ...formData, product_name: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Brand
              </label>
              <input
                type="text"
                value={formData.brand}
                onChange={(e) => setFormData({ ...formData, brand: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Model
              </label>
              <input
                type="text"
                value={formData.model}
                onChange={(e) => setFormData({ ...formData, model: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Condition
              </label>
              <select
                value={formData.condition}
                onChange={(e) => setFormData({ ...formData, condition: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="new">New</option>
                <option value="like_new">Like New</option>
                <option value="good">Good</option>
                <option value="fair">Fair</option>
                <option value="poor">Poor</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Status
              </label>
              <select
                value={formData.status}
                onChange={(e) => setFormData({ ...formData, status: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="available">Available</option>
                <option value="sold">Sold</option>
                <option value="reserved">Reserved</option>
                <option value="defective">Defective</option>
              </select>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Selling Price (TZS)
              </label>
              <input
                type="number"
                value={formData.selling_price}
                onChange={(e) => setFormData({ ...formData, selling_price: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                min="0"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Warranty End Date
              </label>
              <input
                type="date"
                value={formData.warranty_end}
                onChange={(e) => setFormData({ ...formData, warranty_end: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Notes
            </label>
            <textarea
              value={formData.notes}
              onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              rows="3"
              placeholder="Additional notes about this device..."
            />
          </div>

          <div className="flex justify-end space-x-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => setShowModal(false)}
            >
              Cancel
            </Button>
            <Button type="submit">
              {editingDevice ? 'Update' : 'Add'} Device
            </Button>
          </div>
        </form>
      </Modal>
    </AdminPageWrapper>
  );
};

export default IMEIManagementPage;
