import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import PageHeader from '../../components/ui/PageHeader';
import Alert from '../../components/ui/Alert';
import LoadingState from '../../components/ui/LoadingState';
import ErrorBoundary from '../../components/ui/ErrorBoundary';
import DynamicProductForm from '../../components/admin/DynamicProductForm';
import { getProductById } from '../../services/productService';

const ProductFormPage = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const isEditMode = !!id;

  const [product, setProduct] = useState(null);
  const [loading, setLoading] = useState(isEditMode);
  const [error, setError] = useState('');

  useEffect(() => {
    const fetchProduct = async () => {
      if (!isEditMode) return;

      try {
        // Get token from localStorage
        const token = localStorage.getItem('token');

        if (!token) {
          setError('You must be logged in to view this page.');
          setLoading(false);
          return;
        }

        // Get product from the API
        const productData = await getProductById(id);
        setProduct(productData);
        setLoading(false);
      } catch (err) {
        console.error('Error fetching product details:', err);
        setError('Failed to load product details. Please try again later.');
        setLoading(false);
      }
    };

    fetchProduct();
  }, [id, isEditMode]);

  if (loading) {
    return <LoadingState fullPage text="Loading product details..." />;
  }

  // Prepare breadcrumbs
  const breadcrumbs = [
    { label: 'Products', path: '/admin/products' },
    { label: isEditMode ? 'Edit Product' : 'Add Product', path: isEditMode ? `/admin/products/edit/${id}` : '/admin/products/new' }
  ];

  return (
    <div className="bg-gray-50 py-12">
      <div className="container mx-auto px-4">
        <div className="max-w-3xl mx-auto">
          <PageHeader
            title={isEditMode ? '📦 Edit Product' : '📦 Add New Product'}
            description={isEditMode ? 'Update product specifications and details' : 'Add a new product to Phone Point Dar catalog'}
            breadcrumbs={breadcrumbs}
          />

          {error && (
            <Alert
              type="error"
              message={error}
              onClose={() => setError('')}
              className="mb-8"
            />
          )}

          <ErrorBoundary>
            <DynamicProductForm product={product} />
          </ErrorBoundary>
        </div>
      </div>
    </div>
  );
};

export default ProductFormPage;
