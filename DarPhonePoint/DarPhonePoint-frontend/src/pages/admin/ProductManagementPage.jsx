import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import Button from '../../components/ui/Button';
import Alert from '../../components/ui/Alert';
import PageHeader from '../../components/ui/PageHeader';
import LoadingState from '../../components/ui/LoadingState';
import ErrorState from '../../components/ui/ErrorState';
import Modal from '../../components/ui/Modal';
import {
  getAdminProducts,
  deleteAdminProduct,
  bulkProductOperation,
  getProductFilters,
  searchAdminProducts
} from '../../services/adminProductService';

/**
 * Phone Point Dar Product Management Page
 * Manage tech product inventory with IMEI tracking and Tanzania market features
 */
const ProductManagementPage = () => {
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [filtersLoading, setFiltersLoading] = useState(true);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalPages: 1,
    totalItems: 0,
    itemsPerPage: 20
  });
  const [filters, setFilters] = useState({
    search: '',
    category: '',
    brand: '',
    status: '',
    sort: '-created_at'
  });
  const [selectedProducts, setSelectedProducts] = useState([]);
  const [showBulkModal, setShowBulkModal] = useState(false);
  const [bulkAction, setBulkAction] = useState('');
  const [availableFilters, setAvailableFilters] = useState({
    categories: [],
    brands: []
  });

  // TZS currency formatting for Tanzania market
  const formatTZS = (amount) => {
    return new Intl.NumberFormat('sw-TZ', {
      style: 'currency',
      currency: 'TZS',
      minimumFractionDigits: 0
    }).format(amount);
  };

  // Fetch products with current filters and pagination
  const fetchProducts = async () => {
    try {
      setLoading(true);
      setError('');

      const params = {
        page: pagination.currentPage,
        limit: pagination.itemsPerPage,
        ...filters
      };

      const response = await getAdminProducts(params);

      if (response && response.products) {
        setProducts(response.products);
        setPagination(prev => ({
          ...prev,
          ...response.pagination
        }));
      } else {
        setProducts([]);
      }
    } catch (err) {
      console.error('Error fetching products:', err);
      setError('Failed to load products. Please try again later.');
      setProducts([]);
    } finally {
      setLoading(false);
    }
  };

  // Fetch available filters
  const fetchFilters = async () => {
    try {
      setFiltersLoading(true);
      const response = await getProductFilters();
      if (response && response.data) {
        setAvailableFilters(response.data);
      } else if (response) {
        setAvailableFilters(response);
      } else {
        // Fallback to default empty arrays
        setAvailableFilters({
          categories: [],
          brands: []
        });
      }
    } catch (err) {
      console.error('Error fetching filters:', err);
      // Set fallback values on error
      setAvailableFilters({
        categories: [],
        brands: []
      });
    } finally {
      setFiltersLoading(false);
    }
  };

  useEffect(() => {
    fetchProducts();
  }, [pagination.currentPage, filters]);

  useEffect(() => {
    fetchFilters();
  }, []);

  // Handle single product deletion
  const handleDeleteProduct = async (productId) => {
    if (window.confirm('Are you sure you want to delete this product?')) {
      try {
        await deleteAdminProduct(productId);
        setSuccess('Product deleted successfully');
        fetchProducts(); // Refresh the list
      } catch (err) {
        console.error('Error deleting product:', err);
        setError('Failed to delete product. Please try again later.');
      }
    }
  };

  // Handle filter changes
  const handleFilterChange = (field, value) => {
    setFilters(prev => ({
      ...prev,
      [field]: value
    }));
    setPagination(prev => ({ ...prev, currentPage: 1 }));
  };

  // Handle search
  const handleSearch = async (searchQuery) => {
    try {
      setLoading(true);
      const response = await searchAdminProducts(searchQuery, filters);
      if (response && response.products) {
        setProducts(response.products);
        setPagination(prev => ({
          ...prev,
          ...response.pagination
        }));
      }
    } catch (err) {
      console.error('Error searching products:', err);
      setError('Search failed. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Handle product selection for bulk operations
  const handleProductSelect = (productId) => {
    setSelectedProducts(prev => {
      if (prev.includes(productId)) {
        return prev.filter(id => id !== productId);
      } else {
        return [...prev, productId];
      }
    });
  };

  // Handle select all products
  const handleSelectAll = () => {
    if (selectedProducts.length === products.length) {
      setSelectedProducts([]);
    } else {
      setSelectedProducts(products.map(p => p._id));
    }
  };

  // Handle bulk operations
  const handleBulkOperation = async () => {
    if (selectedProducts.length === 0) {
      setError('Please select products to perform bulk operation');
      return;
    }

    try {
      await bulkProductOperation(bulkAction, selectedProducts);
      setSuccess(`Bulk ${bulkAction} completed successfully`);
      setSelectedProducts([]);
      setShowBulkModal(false);
      fetchProducts(); // Refresh the list
    } catch (err) {
      console.error('Error performing bulk operation:', err);
      setError(`Failed to perform bulk ${bulkAction}. Please try again.`);
    }
  };

  // Handle page change
  const handlePageChange = (page) => {
    setPagination(prev => ({ ...prev, currentPage: page }));
  };

  if (loading) {
    return <LoadingState message="Loading products..." />;
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-600 to-blue-800 rounded-lg p-6 text-white">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
          <div>
            <h1 className="text-2xl font-bold mb-2">📱 Product Management</h1>
            <p className="text-blue-100">Manage Phone Point Dar inventory with IMEI tracking</p>
          </div>
          <div className="flex flex-wrap gap-2 mt-4 md:mt-0">
            <Link to="/admin/products/new">
              <Button className="bg-white text-blue-600 hover:bg-gray-100">
                <i className="fas fa-plus mr-2"></i>Add Product
              </Button>
            </Link>
            <Button className="bg-blue-500 hover:bg-blue-400 text-white">
              <i className="fas fa-upload mr-2"></i>Import
            </Button>
            <Button className="bg-blue-500 hover:bg-blue-400 text-white">
              <i className="fas fa-download mr-2"></i>Export
            </Button>
          </div>
        </div>
      </div>

      {/* Alerts */}
      {error && (
        <Alert
          type="error"
          message={error}
          onClose={() => setError('')}
        />
      )}

      {success && (
        <Alert
          type="success"
          message={success}
          onClose={() => setSuccess('')}
        />
      )}

      {/* Filters and Search */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Search</label>
            <input
              type="text"
              value={filters.search}
              onChange={(e) => handleFilterChange('search', e.target.value)}
              placeholder="Search products..."
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Category</label>
            <select
              value={filters.category}
              onChange={(e) => handleFilterChange('category', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              disabled={filtersLoading}
            >
              <option value="">All Categories</option>
              {!filtersLoading && availableFilters?.categories?.map(category => (
                <option key={category} value={category}>{category}</option>
              ))}
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Brand</label>
            <select
              value={filters.brand}
              onChange={(e) => handleFilterChange('brand', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              disabled={filtersLoading}
            >
              <option value="">All Brands</option>
              {!filtersLoading && availableFilters?.brands?.map(brand => (
                <option key={brand} value={brand}>{brand}</option>
              ))}
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Status</label>
            <select
              value={filters.status}
              onChange={(e) => handleFilterChange('status', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">All Status</option>
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
              <option value="draft">Draft</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Sort By</label>
            <select
              value={filters.sort}
              onChange={(e) => handleFilterChange('sort', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="-created_at">Newest First</option>
              <option value="created_at">Oldest First</option>
              <option value="name">Name A-Z</option>
              <option value="-name">Name Z-A</option>
              <option value="price">Price Low-High</option>
              <option value="-price">Price High-Low</option>
            </select>
          </div>
        </div>
      </div>

      {/* Bulk Actions */}
      {selectedProducts.length > 0 && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <span className="text-sm text-blue-800">
              {selectedProducts.length} product{selectedProducts.length > 1 ? 's' : ''} selected
            </span>
            <div className="flex space-x-2">
              <Button
                size="sm"
                variant="outline"
                onClick={() => {
                  setBulkAction('activate');
                  setShowBulkModal(true);
                }}
                className="text-green-600 border-green-300 hover:bg-green-50"
              >
                <i className="fas fa-check mr-1"></i>Activate
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => {
                  setBulkAction('deactivate');
                  setShowBulkModal(true);
                }}
                className="text-yellow-600 border-yellow-300 hover:bg-yellow-50"
              >
                <i className="fas fa-pause mr-1"></i>Deactivate
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => {
                  setBulkAction('delete');
                  setShowBulkModal(true);
                }}
                className="text-red-600 border-red-300 hover:bg-red-50"
              >
                <i className="fas fa-trash mr-1"></i>Delete
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Products Table */}
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  <input
                    type="checkbox"
                    checked={selectedProducts.length === products.length && products.length > 0}
                    onChange={handleSelectAll}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Product
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Brand & Category
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Price (TZS)
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Stock
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {products.length > 0 ? (
                products.map((product) => (
                  <tr key={product._id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <input
                        type="checkbox"
                        checked={selectedProducts.includes(product._id)}
                        onChange={() => handleProductSelect(product._id)}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-12 w-12">
                          {product.images && product.images.length > 0 ? (
                            <img
                              className="h-12 w-12 rounded-lg object-cover"
                              src={product.images[0]}
                              alt={product.name}
                            />
                          ) : (
                            <div className="h-12 w-12 bg-gradient-to-br from-blue-100 to-blue-200 rounded-lg flex items-center justify-center">
                              <i className="fas fa-mobile-alt text-blue-600 text-lg"></i>
                            </div>
                          )}
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900">{product.name}</div>
                          <div className="text-sm text-gray-500">SKU: {product.sku || 'N/A'}</div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        <div className="font-medium">{product.brand || 'Unknown Brand'}</div>
                        <div className="text-gray-500">{product.category || 'Uncategorized'}</div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900 font-medium">
                        {formatTZS(product.price || 0)}
                      </div>
                      {product.compare_at_price && product.compare_at_price > product.price && (
                        <div className="text-xs text-gray-500 line-through">
                          {formatTZS(product.compare_at_price)}
                        </div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        {product.track_inventory ? (
                          <>
                            <div className="font-medium">{product.stock_quantity || 0}</div>
                            {product.stock_quantity <= (product.low_stock_threshold || 5) && (
                              <div className="text-xs text-red-600">Low Stock</div>
                            )}
                          </>
                        ) : (
                          <span className="text-gray-500">N/A</span>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        product.status === 'active' ? 'bg-green-100 text-green-800' :
                        product.status === 'inactive' ? 'bg-red-100 text-red-800' :
                        'bg-yellow-100 text-yellow-800'
                      }`}>
                        {product.status || 'draft'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex justify-end space-x-2">
                        <Link
                          to={`/admin/products/${product._id}`}
                          className="text-blue-600 hover:text-blue-900"
                        >
                          <i className="fas fa-eye mr-1"></i>View
                        </Link>
                        <Link
                          to={`/admin/products/edit/${product._id}`}
                          className="text-indigo-600 hover:text-indigo-900"
                        >
                          <i className="fas fa-edit mr-1"></i>Edit
                        </Link>
                        <button
                          onClick={() => handleDeleteProduct(product._id)}
                          className="text-red-600 hover:text-red-900"
                        >
                          <i className="fas fa-trash mr-1"></i>Delete
                        </button>
                      </div>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan="7" className="px-6 py-12 text-center">
                    <div className="text-gray-500">
                      <i className="fas fa-box-open text-4xl mb-4"></i>
                      <p className="text-lg font-medium">No products found</p>
                      <p className="text-sm">Get started by adding your first product</p>
                    </div>
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        {pagination.totalPages > 1 && (
          <div className="px-6 py-4 border-t border-gray-200">
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-500">
                Showing {((pagination.currentPage - 1) * pagination.itemsPerPage) + 1} to{' '}
                {Math.min(pagination.currentPage * pagination.itemsPerPage, pagination.totalItems)} of{' '}
                {pagination.totalItems} results
              </div>
              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(pagination.currentPage - 1)}
                  disabled={pagination.currentPage === 1}
                >
                  <i className="fas fa-chevron-left mr-1"></i>Previous
                </Button>

                {/* Page numbers */}
                {Array.from({ length: Math.min(5, pagination.totalPages) }, (_, i) => {
                  const page = i + Math.max(1, pagination.currentPage - 2);
                  if (page <= pagination.totalPages) {
                    return (
                      <Button
                        key={page}
                        variant={page === pagination.currentPage ? "primary" : "outline"}
                        size="sm"
                        onClick={() => handlePageChange(page)}
                      >
                        {page}
                      </Button>
                    );
                  }
                  return null;
                })}

                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(pagination.currentPage + 1)}
                  disabled={pagination.currentPage === pagination.totalPages}
                >
                  Next<i className="fas fa-chevron-right ml-1"></i>
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Bulk Operation Modal */}
      {showBulkModal && (
        <Modal
          isOpen={showBulkModal}
          onClose={() => setShowBulkModal(false)}
          title={`Bulk ${bulkAction.charAt(0).toUpperCase() + bulkAction.slice(1)}`}
        >
          <div className="space-y-4">
            <p className="text-gray-600">
              Are you sure you want to {bulkAction} {selectedProducts.length} selected product{selectedProducts.length > 1 ? 's' : ''}?
            </p>

            {bulkAction === 'delete' && (
              <div className="bg-red-50 border border-red-200 rounded-md p-4">
                <p className="text-sm text-red-800">
                  <i className="fas fa-exclamation-triangle mr-2"></i>
                  This action will deactivate the selected products. They can be reactivated later.
                </p>
              </div>
            )}

            <div className="flex justify-end space-x-3">
              <Button
                variant="outline"
                onClick={() => setShowBulkModal(false)}
              >
                Cancel
              </Button>
              <Button
                onClick={handleBulkOperation}
                className={
                  bulkAction === 'delete' ? 'bg-red-600 hover:bg-red-700' :
                  bulkAction === 'activate' ? 'bg-green-600 hover:bg-green-700' :
                  'bg-yellow-600 hover:bg-yellow-700'
                }
              >
                {bulkAction === 'delete' ? 'Delete Products' :
                 bulkAction === 'activate' ? 'Activate Products' :
                 'Deactivate Products'}
              </Button>
            </div>
          </div>
        </Modal>
      )}
    </div>
  );
};

export default ProductManagementPage;
