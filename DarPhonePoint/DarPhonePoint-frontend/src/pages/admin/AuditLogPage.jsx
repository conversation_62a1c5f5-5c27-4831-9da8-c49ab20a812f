import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import Button from '../../components/ui/Button';
import Alert from '../../components/ui/Alert';
import PageHeader from '../../components/ui/PageHeader';
import LoadingState from '../../components/ui/LoadingState';
import FilterBar from '../../components/ui/FilterBar';
import ErrorBoundary from '../../components/ui/ErrorBoundary';
import Pagination from '../../components/ui/Pagination';
import { getAuditLogs } from '../../services/auditLogService';

/**
 * Phone Point Dar Audit Log Page
 * Track all phone retail operations and system activities
 */
const PhonePointDarAuditLogPage = () => {
  const [logs, setLogs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [filters, setFilters] = useState({
    action: '',
    resource_type: '',
    start_date: '',
    end_date: ''
  });

  // Action types for filtering
  const actionTypes = [
    { value: '', label: 'All Actions' },
    { value: 'create', label: 'Create' },
    { value: 'update', label: 'Update' },
    { value: 'delete', label: 'Delete' },
    { value: 'login', label: 'Login' },
    { value: 'logout', label: 'Logout' },
    { value: 'view', label: 'View' },
    { value: 'export', label: 'Export' },
    { value: 'import', label: 'Import' },
    { value: 'settings_change', label: 'Settings Change' },
    { value: 'password_reset', label: 'Password Reset' },
    { value: 'email_sent', label: 'Email Sent' },
    { value: 'payment_processed', label: 'Payment Processed' },
    { value: 'refund_processed', label: 'Refund Processed' },
    { value: 'other', label: 'Other' }
  ];

  // Resource types for filtering - Phone Point Dar specific
  const resourceTypes = [
    { value: '', label: 'All Resources' },
    { value: 'customer', label: 'Customer' },
    { value: 'phone', label: 'Phone/Device' },
    { value: 'phone_order', label: 'Phone Order' },
    { value: 'inventory', label: 'Phone Inventory' },
    { value: 'imei', label: 'IMEI Tracking' },
    { value: 'warranty', label: 'Warranty' },
    { value: 'user', label: 'User' },
    { value: 'settings', label: 'Settings' },
    { value: 'analytics', label: 'Analytics' },
    { value: 'auth', label: 'Authentication' },
    { value: 'payment', label: 'Payment' },
    { value: 'shipping', label: 'Shipping' },
    { value: 'supplier', label: 'Supplier' },
    { value: 'other', label: 'Other' }
  ];

  useEffect(() => {
    fetchLogs();
  }, [page, filters]);

  const fetchLogs = async () => {
    setLoading(true);
    setError('');

    try {
      // Prepare parameters for the API call
      const params = {
        page,
        limit: 50,
        ...Object.fromEntries(
          Object.entries(filters).filter(([_, value]) => value !== '')
        )
      };

      // Use the auditLogService to fetch logs
      const data = await getAuditLogs(params);

      // Check if we have valid data
      if (Array.isArray(data)) {
        // Direct array response
        setLogs(data);
        setTotalPages(1);
      } else if (data && data.data && Array.isArray(data.data)) {
        // Wrapped response
        setLogs(data.data);
        setTotalPages(data.pages || Math.ceil(data.total / params.limit) || 1);
      } else {
        // If the API returns an empty response or unexpected format
        console.warn('API returned unexpected data format:', data);
        setLogs([]);
        setTotalPages(1);
      }

      setLoading(false);
    } catch (err) {
      console.error('Error fetching audit logs:', err);
      setError('Failed to load audit logs. Please try again later.');
      setLoading(false);
    }
  };

  const handleFilterChange = (e) => {
    const { name, value } = e.target;
    setFilters(prev => ({ ...prev, [name]: value }));
    setPage(1); // Reset to first page when filters change
  };

  const handleClearFilters = () => {
    setFilters({
      action: '',
      resource_type: '',
      start_date: '',
      end_date: ''
    });
    setPage(1);
  };

  const formatDate = (dateString) => {
    try {
      const date = new Date(dateString);

      // Check if date is valid
      if (isNaN(date.getTime())) {
        return 'Invalid Date';
      }

      return new Intl.DateTimeFormat('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      }).format(date);
    } catch (error) {
      console.error('Error formatting date:', error);
      return 'Invalid Date';
    }
  };

  // Get action label
  const getActionLabel = (action) => {
    if (!action) return 'Unknown';
    const actionType = actionTypes.find(type => type.value === action);
    return actionType ? actionType.label : (typeof action === 'string' ? action : 'Unknown');
  };

  // Get resource type label
  const getResourceTypeLabel = (type) => {
    if (!type) return 'Unknown';
    const resourceType = resourceTypes.find(t => t.value === type);
    return resourceType ? resourceType.label : (typeof type === 'string' ? type : 'Unknown');
  };

  // Get action color
  const getActionColor = (action) => {
    switch (action) {
      case 'create':
        return 'bg-green-100 text-green-800';
      case 'update':
        return 'bg-blue-100 text-blue-800';
      case 'delete':
        return 'bg-red-100 text-red-800';
      case 'login':
      case 'logout':
        return 'bg-purple-100 text-purple-800';
      case 'view':
        return 'bg-gray-100 text-gray-800';
      case 'settings_change':
        return 'bg-yellow-100 text-yellow-800';
      case 'payment_processed':
      case 'refund_processed':
        return 'bg-indigo-100 text-indigo-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading && logs.length === 0) {
    return <LoadingState fullPage text="Loading audit logs..." />;
  }

  return (
    <div className="bg-gray-50 py-12">
      <div className="container mx-auto px-4">
        <div className="max-w-7xl mx-auto">
          <PageHeader
            title="📱 Phone Point Dar - Audit Logs"
            description="Track all phone retail operations and system activities in Tanzania"
            breadcrumbs={[
              { label: 'Dashboard', path: '/admin' },
              { label: 'Audit Logs', path: '/admin/audit-logs' }
            ]}
            actions={
              <Button
                variant="outline"
                onClick={fetchLogs}
              >
                <svg className="h-5 w-5 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
                Refresh
              </Button>
            }
          />

          {error && (
            <Alert
              type="error"
              message={error}
              onClose={() => setError('')}
              className="mb-8"
            />
          )}

          {/* Filters */}
          <ErrorBoundary>
            <FilterBar
              showSearch={false}
              filters={[
                {
                  name: 'action',
                  label: 'Action',
                  type: 'select',
                  options: actionTypes
                },
                {
                  name: 'resource_type',
                  label: 'Resource Type',
                  type: 'select',
                  options: resourceTypes
                },
                {
                  name: 'start_date',
                  label: 'Start Date',
                  type: 'date'
                },
                {
                  name: 'end_date',
                  label: 'End Date',
                  type: 'date'
                }
              ]}
              onFilterChange={(newFilters) => {
                setFilters(newFilters);
                setPage(1); // Reset to first page when filters change
              }}
            />
          </ErrorBoundary>

          {/* Logs Table */}
          <div className="bg-white rounded-lg shadow-md overflow-hidden">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Date & Time
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      User
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Action
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Resource
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Description
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {loading ? (
                    <tr>
                      <td colSpan="5" className="px-6 py-4 text-center">
                        <div className="flex justify-center">
                          <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-purple-500"></div>
                        </div>
                      </td>
                    </tr>
                  ) : logs.length === 0 ? (
                    <tr>
                      <td colSpan="5" className="px-6 py-4 text-center text-gray-500">
                        No logs found matching your criteria.
                      </td>
                    </tr>
                  ) : (
                    logs.map(log => (
                      <tr key={log._id || `log-${Math.random()}`} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {log.created_at ? formatDate(log.created_at) : 'N/A'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          {log.user ? (
                            <div className="flex items-center">
                              <div className="flex-shrink-0 h-8 w-8 bg-purple-100 rounded-full flex items-center justify-center">
                                <span className="text-sm font-medium text-purple-800">
                                  {log.user.name ? log.user.name.charAt(0).toUpperCase() : 'U'}
                                </span>
                              </div>
                              <div className="ml-4">
                                <div className="text-sm font-medium text-gray-900">
                                  {log.user.name || 'Unknown User'}
                                </div>
                                <div className="text-sm text-gray-500">
                                  {log.user.email || ''}
                                </div>
                              </div>
                            </div>
                          ) : (
                            <span className="text-sm text-gray-500">System</span>
                          )}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getActionColor(log.action || 'unknown')}`}>
                            {log.action ? getActionLabel(log.action) : 'Unknown'}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          <div className="text-sm text-gray-900">
                            {log.resource_type ? getResourceTypeLabel(log.resource_type) : 'N/A'}
                          </div>
                          {log.resource_id && (
                            <div className="text-xs text-gray-500">
                              ID: {log.resource_id}
                            </div>
                          )}
                        </td>
                        <td className="px-6 py-4 text-sm text-gray-500">
                          {log.description || 'No description available'}
                        </td>
                      </tr>
                    ))
                  )}
                </tbody>
              </table>
            </div>
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <Pagination
              currentPage={page}
              totalPages={totalPages}
              totalItems={logs.length}
              pageSize={50}
              onPageChange={setPage}
            />
          )}
        </div>
      </div>
    </div>
  );
};

export default PhonePointDarAuditLogPage;
