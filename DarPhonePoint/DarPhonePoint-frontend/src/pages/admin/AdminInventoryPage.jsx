import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { safeApiRequest } from '../../api/apiClient';
import PageHeader from '../../components/ui/PageHeader';
import LoadingState from '../../components/ui/LoadingState';
import Alert from '../../components/ui/Alert';
import Button from '../../components/ui/Button';

const PhoneInventoryPage = () => {
  const [phoneInventory, setPhoneInventory] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [filters, setFilters] = useState({
    search: '',
    brand: 'all',
    condition: 'all',
    warranty_status: 'all',
    stock_level: 'all'
  });
  const [showStockModal, setShowStockModal] = useState(false);
  const [showImeiModal, setShowImeiModal] = useState(false);
  const [selectedPhone, setSelectedPhone] = useState(null);
  const [stockAdjustment, setStockAdjustment] = useState({
    type: 'received_shipment',
    quantity: '',
    reason: '',
    supplier: '',
    cost_per_unit: '',
    notes: ''
  });
  const [imeiData, setImeiData] = useState({
    imei_numbers: [''],
    condition: 'new',
    warranty_expiry: ''
  });

  useEffect(() => {
    fetchPhoneInventory();
  }, [filters]);

  const fetchPhoneInventory = async () => {
    setLoading(true);
    try {
      // Convert filters to appropriate query parameters for the inventory endpoint
      const queryParams = new URLSearchParams();

      if (filters.search && filters.search !== '') {
        queryParams.append('search', filters.search);
      }
      if (filters.brand && filters.brand !== 'all') {
        queryParams.append('brand', filters.brand);
      }
      if (filters.stock_level && filters.stock_level !== 'all') {
        if (filters.stock_level === 'low') {
          queryParams.append('low_stock', 'true');
        } else if (filters.stock_level === 'out') {
          queryParams.append('out_of_stock', 'true');
        }
      }

      const response = await safeApiRequest({
        method: 'GET',
        url: `/inventory?${queryParams.toString()}`
      });

      if (response.data && response.data.data) {
        setPhoneInventory(response.data.data);
      }
    } catch (err) {
      console.error('Error fetching phone inventory:', err);
      setError('Failed to load phone inventory data');
    } finally {
      setLoading(false);
    }
  };

  const formatTZS = (amount) => {
    return new Intl.NumberFormat('sw-TZ', {
      style: 'currency',
      currency: 'TZS',
      minimumFractionDigits: 0
    }).format(amount);
  };

  const getStockLevelColor = (current, threshold) => {
    if (current === 0) return 'bg-red-100 text-red-800';
    if (current <= threshold) return 'bg-yellow-100 text-yellow-800';
    return 'bg-green-100 text-green-800';
  };

  const getConditionColor = (condition) => {
    switch (condition) {
      case 'new': return 'bg-green-100 text-green-800';
      case 'refurbished': return 'bg-blue-100 text-blue-800';
      case 'damaged': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const handleStockAdjustment = async () => {
    if (!selectedPhone || !stockAdjustment.quantity) return;

    try {
      await safeApiRequest({
        method: 'POST',
        url: `/inventory/phones/${selectedPhone._id}/adjust`,
        data: {
          ...stockAdjustment,
          cost_per_unit: stockAdjustment.cost_per_unit ? parseFloat(stockAdjustment.cost_per_unit) : null
        }
      });

      setShowStockModal(false);
      setSelectedPhone(null);
      setStockAdjustment({
        type: 'received_shipment',
        quantity: '',
        reason: '',
        supplier: '',
        cost_per_unit: '',
        notes: ''
      });
      setSuccess('Stock adjustment completed successfully');
      fetchPhoneInventory();
    } catch (err) {
      console.error('Error adjusting phone stock:', err);
      setError('Failed to adjust phone stock');
    }
  };

  const handleImeiUpdate = async () => {
    if (!selectedPhone || !imeiData.imei_numbers.filter(imei => imei.trim()).length) return;

    try {
      await safeApiRequest({
        method: 'POST',
        url: `/inventory/phones/${selectedPhone._id}/imei`,
        data: {
          ...imeiData,
          imei_numbers: imeiData.imei_numbers.filter(imei => imei.trim())
        }
      });

      setShowImeiModal(false);
      setSelectedPhone(null);
      setImeiData({
        imei_numbers: [''],
        condition: 'new',
        warranty_expiry: ''
      });
      setSuccess('IMEI numbers updated successfully');
      fetchPhoneInventory();
    } catch (err) {
      console.error('Error updating IMEI:', err);
      setError('Failed to update IMEI numbers');
    }
  };

  const addImeiField = () => {
    setImeiData({
      ...imeiData,
      imei_numbers: [...imeiData.imei_numbers, '']
    });
  };

  const removeImeiField = (index) => {
    const newImeiNumbers = imeiData.imei_numbers.filter((_, i) => i !== index);
    setImeiData({
      ...imeiData,
      imei_numbers: newImeiNumbers.length ? newImeiNumbers : ['']
    });
  };

  const updateImeiField = (index, value) => {
    const newImeiNumbers = [...imeiData.imei_numbers];
    newImeiNumbers[index] = value;
    setImeiData({
      ...imeiData,
      imei_numbers: newImeiNumbers
    });
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <LoadingState text="Loading phone inventory..." size="lg" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <PageHeader
        title="📱 Phone Point Dar - Inventory Management"
        description="Monitor and manage phone inventory with IMEI tracking and warranty status for Tanzania market"
        breadcrumbs={[
          { name: 'Dashboard', href: '/admin' },
          { name: 'Phone Inventory', href: '/admin/inventory' }
        ]}
        actions={
          <div className="flex space-x-3">
            <Link to="/admin/inventory/movements">
              <Button variant="outline" className="bg-blue-50 text-blue-700 border-blue-300 hover:bg-blue-100">
                <i className="fas fa-exchange-alt mr-2"></i>
                Stock Movements
              </Button>
            </Link>
            <Link to="/admin/inventory/alerts">
              <Button variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-300 hover:bg-yellow-100">
                <i className="fas fa-exclamation-triangle mr-2"></i>
                Low Stock Alerts
              </Button>
            </Link>
            <Button
              onClick={() => setShowStockModal(true)}
              className="bg-blue-600 hover:bg-blue-700"
            >
              <i className="fas fa-plus mr-2"></i>
              Adjust Stock
            </Button>
          </div>
        }
      />

      {error && (
        <Alert
          type="error"
          message={error}
          onClose={() => setError('')}
        />
      )}

      {success && (
        <Alert
          type="success"
          message={success}
          onClose={() => setSuccess('')}
        />
      )}

      {/* Phone Inventory Filters */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          <i className="fas fa-filter mr-2 text-blue-600"></i>
          Filter Phone Inventory
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Search Phones
            </label>
            <input
              type="text"
              placeholder="Search by phone name, brand, IMEI..."
              value={filters.search}
              onChange={(e) => setFilters({ ...filters, search: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Brand
            </label>
            <select
              value={filters.brand}
              onChange={(e) => setFilters({ ...filters, brand: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">All Brands</option>
              <option value="Apple">Apple</option>
              <option value="Samsung">Samsung</option>
              <option value="Tecno">Tecno</option>
              <option value="Infinix">Infinix</option>
              <option value="Huawei">Huawei</option>
              <option value="Xiaomi">Xiaomi</option>
              <option value="Oppo">Oppo</option>
              <option value="Vivo">Vivo</option>
              <option value="Nokia">Nokia</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Condition
            </label>
            <select
              value={filters.condition}
              onChange={(e) => setFilters({ ...filters, condition: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">All Conditions</option>
              <option value="new">New</option>
              <option value="refurbished">Refurbished</option>
              <option value="damaged">Damaged</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Stock Level
            </label>
            <select
              value={filters.stock_level}
              onChange={(e) => setFilters({ ...filters, stock_level: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">All Stock Levels</option>
              <option value="in_stock">In Stock</option>
              <option value="low_stock">Low Stock</option>
              <option value="out_of_stock">Out of Stock</option>
            </select>
          </div>
        </div>
      </div>

      {/* Phone Inventory Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center">
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <i className="fas fa-mobile-alt text-blue-600 text-xl"></i>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Total Phones</p>
              <p className="text-2xl font-bold text-gray-900">{phoneInventory.length}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center">
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
              <i className="fas fa-check-circle text-green-600 text-xl"></i>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">In Stock</p>
              <p className="text-2xl font-bold text-gray-900">
                {phoneInventory.filter(phone => (phone.stock_quantity || 0) > (phone.low_stock_threshold || 5)).length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center">
            <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
              <i className="fas fa-exclamation-triangle text-yellow-600 text-xl"></i>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Low Stock</p>
              <p className="text-2xl font-bold text-gray-900">
                {phoneInventory.filter(phone => {
                  const stock = phone.stock_quantity || 0;
                  const threshold = phone.low_stock_threshold || 5;
                  return stock > 0 && stock <= threshold;
                }).length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center">
            <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
              <i className="fas fa-times-circle text-red-600 text-xl"></i>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Out of Stock</p>
              <p className="text-2xl font-bold text-gray-900">
                {phoneInventory.filter(phone => (phone.stock_quantity || 0) === 0).length}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Phone Inventory Table */}
      <div className="bg-white rounded-lg shadow-md overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900 flex items-center">
            <i className="fas fa-mobile-alt mr-2 text-blue-600"></i>
            Phone Inventory ({phoneInventory.length})
          </h3>
        </div>

        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Phone Details
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Brand & Model
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Stock Level
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Condition
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Value (TZS)
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Warranty
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {phoneInventory.length === 0 ? (
                <tr>
                  <td colSpan="7" className="px-6 py-12 text-center">
                    <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                      <i className="fas fa-mobile-alt text-blue-600 text-2xl"></i>
                    </div>
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No phones in inventory</h3>
                    <p className="text-gray-500">Start by adding phones to your inventory</p>
                  </td>
                </tr>
              ) : (
                phoneInventory.map((phone) => (
                  <tr key={phone._id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center">
                          <i className="fas fa-mobile-alt text-blue-600 text-xl"></i>
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900">
                            {phone.name}
                          </div>
                          <div className="text-sm text-gray-500">
                            SKU: {phone.sku || 'N/A'} • {phone.storage_capacity || 'N/A'}
                          </div>
                          {phone.color_variants && phone.color_variants.length > 0 && (
                            <div className="text-xs text-gray-400 mt-1">
                              Colors: {phone.color_variants.slice(0, 3).join(', ')}
                              {phone.color_variants.length > 3 && ` +${phone.color_variants.length - 3} more`}
                            </div>
                          )}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">{phone.brand}</div>
                        <div className="text-sm text-gray-500">{phone.model || 'N/A'}</div>
                        {phone.operating_system && (
                          <div className="text-xs text-gray-400">{phone.operating_system}</div>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">
                        {phone.stock_quantity || 0}
                      </div>
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                        getStockLevelColor(phone.stock_quantity || 0, phone.low_stock_threshold || 5)
                      }`}>
                        {(phone.stock_quantity || 0) === 0 ? 'Out of Stock' :
                         (phone.stock_quantity || 0) <= (phone.low_stock_threshold || 5) ? 'Low Stock' : 'In Stock'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                        getConditionColor(phone.condition || 'new')
                      }`}>
                        {(phone.condition || 'new').charAt(0).toUpperCase() + (phone.condition || 'new').slice(1)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">
                        {formatTZS((phone.stock_quantity || 0) * (phone.cost_price || phone.price || 0))}
                      </div>
                      <div className="text-xs text-gray-500">
                        Unit: {formatTZS(phone.cost_price || phone.price || 0)}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        {phone.warranty_period || 'N/A'}
                      </div>
                      {phone.warranty_expiry && (
                        <div className="text-xs text-gray-500">
                          Expires: {new Date(phone.warranty_expiry).toLocaleDateString()}
                        </div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex items-center space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            setSelectedPhone(phone);
                            setShowStockModal(true);
                          }}
                          title="Adjust Stock"
                        >
                          <i className="fas fa-edit"></i>
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            setSelectedPhone(phone);
                            setShowImeiModal(true);
                          }}
                          title="Manage IMEI"
                          className="text-blue-600 hover:text-blue-800"
                        >
                          <i className="fas fa-barcode"></i>
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Phone Stock Adjustment Modal */}
      {showStockModal && selectedPhone && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                <i className="fas fa-mobile-alt mr-2 text-blue-600"></i>
                Adjust Stock: {selectedPhone.name}
              </h3>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Movement Type
                  </label>
                  <select
                    value={stockAdjustment.type}
                    onChange={(e) => setStockAdjustment({ ...stockAdjustment, type: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="received_shipment">Received Shipment</option>
                    <option value="sale">Sale</option>
                    <option value="return">Customer Return</option>
                    <option value="warranty_replacement">Warranty Replacement</option>
                    <option value="trade_in">Trade-in</option>
                    <option value="damaged">Damaged/Defective</option>
                    <option value="adjustment">Manual Adjustment</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Quantity
                  </label>
                  <input
                    type="number"
                    min="0"
                    value={stockAdjustment.quantity}
                    onChange={(e) => setStockAdjustment({ ...stockAdjustment, quantity: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Enter quantity"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Supplier (for shipments)
                  </label>
                  <input
                    type="text"
                    value={stockAdjustment.supplier}
                    onChange={(e) => setStockAdjustment({ ...stockAdjustment, supplier: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Supplier name"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Cost per Unit (TZS)
                  </label>
                  <input
                    type="number"
                    min="0"
                    value={stockAdjustment.cost_per_unit}
                    onChange={(e) => setStockAdjustment({ ...stockAdjustment, cost_per_unit: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Cost in TZS"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Reason/Notes
                  </label>
                  <textarea
                    value={stockAdjustment.notes}
                    onChange={(e) => setStockAdjustment({ ...stockAdjustment, notes: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    rows="3"
                    placeholder="Additional notes about this stock movement..."
                  />
                </div>
              </div>

              <div className="flex justify-end space-x-3 mt-6">
                <Button
                  variant="outline"
                  onClick={() => {
                    setShowStockModal(false);
                    setSelectedPhone(null);
                  }}
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleStockAdjustment}
                  disabled={!stockAdjustment.quantity}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  Apply Stock Movement
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* IMEI Management Modal */}
      {showImeiModal && selectedPhone && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                <i className="fas fa-barcode mr-2 text-blue-600"></i>
                Manage IMEI: {selectedPhone.name}
              </h3>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    IMEI Numbers
                  </label>
                  {imeiData.imei_numbers.map((imei, index) => (
                    <div key={index} className="flex items-center space-x-2 mb-2">
                      <input
                        type="text"
                        value={imei}
                        onChange={(e) => updateImeiField(index, e.target.value)}
                        className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="Enter IMEI number"
                        maxLength="15"
                      />
                      {imeiData.imei_numbers.length > 1 && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => removeImeiField(index)}
                          className="text-red-600 hover:text-red-800"
                        >
                          <i className="fas fa-trash"></i>
                        </Button>
                      )}
                    </div>
                  ))}
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={addImeiField}
                    className="mt-2"
                  >
                    <i className="fas fa-plus mr-2"></i>
                    Add IMEI
                  </Button>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Device Condition
                  </label>
                  <select
                    value={imeiData.condition}
                    onChange={(e) => setImeiData({ ...imeiData, condition: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="new">New</option>
                    <option value="refurbished">Refurbished</option>
                    <option value="damaged">Damaged</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Warranty Expiry Date
                  </label>
                  <input
                    type="date"
                    value={imeiData.warranty_expiry}
                    onChange={(e) => setImeiData({ ...imeiData, warranty_expiry: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </div>

              <div className="flex justify-end space-x-3 mt-6">
                <Button
                  variant="outline"
                  onClick={() => {
                    setShowImeiModal(false);
                    setSelectedPhone(null);
                  }}
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleImeiUpdate}
                  disabled={!imeiData.imei_numbers.filter(imei => imei.trim()).length}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  Update IMEI Data
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default PhoneInventoryPage;
