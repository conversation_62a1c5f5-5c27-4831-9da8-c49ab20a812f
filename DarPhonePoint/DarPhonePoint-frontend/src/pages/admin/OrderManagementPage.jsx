import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import Button from '../../components/ui/Button';
import Alert from '../../components/ui/Alert';
import PageHeader from '../../components/ui/PageHeader';
import LoadingState from '../../components/ui/LoadingState';
import FilterBar from '../../components/ui/FilterBar';
import ErrorBoundary from '../../components/ui/ErrorBoundary';
import Pagination from '../../components/ui/Pagination';
import { getAllOrders, updateOrderStatus, exportOrdersToCSV } from '../../services/orderService';

/**
 * Phone Point Dar Order Management Page
 * Manage tech product orders with Tanzania shipping and payment tracking
 */
const TechOrderManagementPage = () => {
  const [orders, setOrders] = useState([]);
  const [filteredOrders, setFilteredOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [dateFilter, setDateFilter] = useState('all');
  const [exportLoading, setExportLoading] = useState(false);

  // TZS currency formatting for Tanzania market
  const formatTZS = (amount) => {
    return new Intl.NumberFormat('sw-TZ', {
      style: 'currency',
      currency: 'TZS',
      minimumFractionDigits: 0
    }).format(amount);
  };
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  useEffect(() => {
    const fetchOrders = async () => {
      try {
        // Get token from localStorage
        const token = localStorage.getItem('token');

        if (!token) {
          setError('You must be logged in to view this page.');
          setLoading(false);
          return;
        }

        // Get real orders from the API
        const fetchedOrders = await getAllOrders();
        console.log('Fetched orders:', fetchedOrders);

        // Use only real orders from the database
        const realOrders = fetchedOrders || [];
        console.log(`Found ${realOrders.length} real orders in database`);

        // Process orders to ensure they have all required fields
        const processedOrders = realOrders.map(order => ({
          ...order,
          customer_email: order.customer_email || order.user?.email || 'Unknown',
          payment_status: order.payment_status || 'pending',
          created_at: order.created_at || new Date(),
          updated_at: order.updated_at || new Date()
        }));

        setOrders(processedOrders);
        setFilteredOrders(processedOrders);
        setLoading(false);
      } catch (err) {
        console.error('Error fetching orders:', err);
        setError('Failed to load orders. Please try again later.');
        setLoading(false);
      }
    };

    fetchOrders();
  }, []);

  useEffect(() => {
    // Apply filters whenever search term or filters change
    let result = [...orders];

    // Apply search filter
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      result = result.filter(order =>
        (order.user?.name && order.user.name.toLowerCase().includes(term)) ||
        (order.user?.email && order.user.email.toLowerCase().includes(term)) ||
        (order.customer_email && order.customer_email.toLowerCase().includes(term)) ||
        (order.transaction_id && order.transaction_id.toLowerCase().includes(term))
      );
    }

    // Apply status filter
    if (statusFilter !== 'all') {
      result = result.filter(order => order.payment_status === statusFilter);
    }

    // Apply date filter
    if (dateFilter !== 'all') {
      const now = new Date();
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

      if (dateFilter === 'today') {
        result = result.filter(order => new Date(order.created_at) >= today);
      } else if (dateFilter === 'week') {
        const weekAgo = new Date(today);
        weekAgo.setDate(weekAgo.getDate() - 7);
        result = result.filter(order => new Date(order.created_at) >= weekAgo);
      } else if (dateFilter === 'month') {
        const monthAgo = new Date(today);
        monthAgo.setMonth(monthAgo.getMonth() - 1);
        result = result.filter(order => new Date(order.created_at) >= monthAgo);
      }
    }

    setFilteredOrders(result);
    // Reset to first page when filters change
    setCurrentPage(1);
  }, [searchTerm, statusFilter, dateFilter, orders]);

  const handleExportCSV = async () => {
    setExportLoading(true);
    try {
      // Generate CSV content using the service
      const csvContent = exportOrdersToCSV(filteredOrders);

      // Create a blob and download link
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `orders_export_${new Date().toISOString().split('T')[0]}.csv`);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      setExportLoading(false);
    } catch (err) {
      console.error('Error exporting orders:', err);
      setError('Failed to export orders. Please try again later.');
      setExportLoading(false);
    }
  };

  const handleUpdateOrderStatus = async (orderId, newStatus) => {
    try {
      // Call the API to update the order status
      await updateOrderStatus(orderId, newStatus);

      // Update the UI after successful update
      const updatedOrders = orders.map(order =>
        order._id === orderId ? { ...order, payment_status: newStatus } : order
      );
      setOrders(updatedOrders);

      // Also update filtered orders
      setFilteredOrders(prevFiltered =>
        prevFiltered.map(order =>
          order._id === orderId ? { ...order, payment_status: newStatus } : order
        )
      );
    } catch (err) {
      console.error('Error updating order status:', err);
      setError('Failed to update order status. Please try again later.');
    }
  };

  if (loading) {
    return <LoadingState fullPage text="Loading orders..." />;
  }

  // Calculate total revenue
  const totalRevenue = filteredOrders.reduce((sum, order) => {
    if (order.payment_status === 'completed') {
      return sum + order.amount;
    }
    return sum;
  }, 0);

  return (
    <div className="bg-gray-50 py-12">
      <div className="container mx-auto px-4">
        <div className="max-w-7xl mx-auto">
          <PageHeader
            title="📱 Phone Point Dar - Order Management"
            description="Manage tech product orders with Tanzania shipping and payment tracking"
            breadcrumbs={[
              { label: 'Dashboard', path: '/admin' },
              { label: 'Phone Orders', path: '/admin/orders' }
            ]}
            actions={
              <Button
                onClick={handleExportCSV}
                isLoading={exportLoading}
              >
                <svg className="h-5 w-5 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                </svg>
                Export CSV
              </Button>
            }
          />

          {error && (
            <Alert
              type="error"
              message={error}
              onClose={() => setError('')}
              className="mb-8"
            />
          )}

          {/* Summary Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div className="bg-white rounded-lg shadow-md p-6">
              <div className="flex items-center">
                <div className="p-3 rounded-full bg-purple-100 text-purple-600 mr-4">
                  <svg className="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                  </svg>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Total Orders</p>
                  <p className="text-2xl font-bold">{filteredOrders.length}</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-md p-6">
              <div className="flex items-center">
                <div className="p-3 rounded-full bg-green-100 text-green-600 mr-4">
                  <svg className="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Total Revenue (TZS)</p>
                  <p className="text-2xl font-bold">{formatTZS(totalRevenue)}</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-md p-6">
              <div className="flex items-center">
                <div className="p-3 rounded-full bg-blue-100 text-blue-600 mr-4">
                  <svg className="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Completed Orders</p>
                  <p className="text-2xl font-bold">
                    {filteredOrders.filter(order => order.payment_status === 'completed').length}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-md p-6">
              <div className="flex items-center">
                <div className="p-3 rounded-full bg-yellow-100 text-yellow-600 mr-4">
                  <svg className="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Pending Orders</p>
                  <p className="text-2xl font-bold">
                    {filteredOrders.filter(order => order.payment_status === 'pending').length}
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Filters */}
          <ErrorBoundary>
            <FilterBar
              showSearch={true}
              searchPlaceholder="Search by name, email, or transaction ID"
              onSearch={setSearchTerm}
              filters={[
                {
                  name: 'status',
                  label: 'Status',
                  type: 'select',
                  options: [
                    { value: 'all', label: 'All Statuses' },
                    { value: 'pending', label: 'Pending' },
                    { value: 'completed', label: 'Completed' },
                    { value: 'failed', label: 'Failed' },
                    { value: 'refunded', label: 'Refunded' }
                  ]
                },
                {
                  name: 'dateRange',
                  label: 'Date Range',
                  type: 'select',
                  options: [
                    { value: 'all', label: 'All Time' },
                    { value: 'today', label: 'Today' },
                    { value: 'week', label: 'Last 7 Days' },
                    { value: 'month', label: 'Last 30 Days' }
                  ]
                }
              ]}
              onFilterChange={(filters) => {
                setStatusFilter(filters.status || 'all');
                setDateFilter(filters.dateRange || 'all');
              }}
            />
          </ErrorBoundary>

          {/* Orders Table */}
          <ErrorBoundary
            showDetails={false}
            fallbackAction={{
              label: "Refresh Data",
              onClick: () => window.location.reload()
            }}
          >
            <div className="bg-white rounded-lg shadow-md overflow-hidden">
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Order ID
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Customer
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Product
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Amount
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Date
                      </th>
                      <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {filteredOrders.length > 0 ? (
                      // Only show orders for the current page
                      filteredOrders
                        .slice((currentPage - 1) * pageSize, currentPage * pageSize)
                        .map((order) => (
                        <tr key={order._id}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {order._id}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center">
                              <div className="ml-4">
                                <div className="text-sm font-medium text-gray-900">{order.user?.name || 'Guest'}</div>
                                <div className="text-sm text-gray-500">{order.customer_email}</div>
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {order.product?.name}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-medium">
                            {formatTZS(order.amount)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <select
                              className={`text-xs font-medium rounded-full px-2.5 py-1 border-0 ${
                                order.payment_status === 'completed' ? 'bg-green-100 text-green-800' :
                                order.payment_status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                                order.payment_status === 'failed' ? 'bg-red-100 text-red-800' :
                                'bg-gray-100 text-gray-800'
                              }`}
                              value={order.payment_status}
                              onChange={(e) => handleUpdateOrderStatus(order._id, e.target.value)}
                            >
                              <option value="pending">Pending</option>
                              <option value="completed">Completed</option>
                              <option value="failed">Failed</option>
                              <option value="refunded">Refunded</option>
                            </select>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {new Date(order.created_at).toLocaleDateString()}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <div className="flex justify-end space-x-2">
                              <Link to={`/admin/orders/${order._id}`} className="text-indigo-600 hover:text-indigo-900">
                                View
                              </Link>
                              <a
                                href={`mailto:${order.customer_email}?subject=Phone Point Dar - Your order ${order._id}&body=Hello,\n\nThank you for your order with Phone Point Dar! We're processing your order and will keep you updated.\n\nOrder Details:\n- Order ID: ${order._id}\n- Status: ${order.status}\n- Payment: We accept M-Pesa, Tigo Pesa, and other mobile payments\n- Delivery: Available across Tanzania\n\nFor any questions, please contact us.\n\nBest regards,\nPhone Point Dar Team`}
                                className="text-blue-600 hover:text-blue-900"
                                title={`Send email to ${order.customer_email}`}
                              >
                                Email
                              </a>
                            </div>
                          </td>
                        </tr>
                      ))
                    ) : (
                      <tr>
                        <td colSpan="7" className="px-6 py-4 text-center text-gray-500">
                          <div className="py-8">
                            <svg className="mx-auto h-12 w-12 text-gray-400 mb-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                            </svg>
                            <h3 className="text-lg font-medium text-gray-900 mb-2">No orders yet</h3>
                            <p className="text-gray-500">
                              {searchTerm || statusFilter !== 'all' || dateFilter !== 'all'
                                ? 'No orders found matching your filters. Try adjusting your search criteria.'
                                : 'Orders will appear here once customers start making purchases.'
                              }
                            </p>
                          </div>
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>

              {/* Pagination */}
              <Pagination
                currentPage={currentPage}
                totalPages={Math.ceil(filteredOrders.length / pageSize)}
                totalItems={filteredOrders.length}
                pageSize={pageSize}
                onPageChange={setCurrentPage}
                onPageSizeChange={(newSize) => {
                  setPageSize(newSize);
                  setCurrentPage(1); // Reset to first page when changing page size
                }}
              />
            </div>
          </ErrorBoundary>
        </div>
      </div>
    </div>
  );
};

export default TechOrderManagementPage;
