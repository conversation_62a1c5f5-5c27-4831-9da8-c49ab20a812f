import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import Button from '../../components/ui/Button';
import Alert from '../../components/ui/Alert';
import PageHeader from '../../components/ui/PageHeader';
import LoadingState from '../../components/ui/LoadingState';
import ErrorState from '../../components/ui/ErrorState';
import Modal from '../../components/ui/Modal';
import {
  getAdminOrders,
  updateOrderStatus,
  bulkUpdateOrderStatus,
  getOrderFilters,
  searchAdminOrders,
  getOrderStats,
  exportOrders
} from '../../services/adminOrderService';

/**
 * Phone Point Dar Order Management Page
 * Manage tech product orders with Tanzania shipping and payment tracking
 */
const OrderManagementPage = () => {
  const [orders, setOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalPages: 1,
    totalItems: 0,
    itemsPerPage: 20
  });
  const [filters, setFilters] = useState({
    search: '',
    status: '',
    payment_status: '',
    date_from: '',
    date_to: '',
    sort: '-created_at'
  });
  const [selectedOrders, setSelectedOrders] = useState([]);
  const [showBulkModal, setShowBulkModal] = useState(false);
  const [bulkAction, setBulkAction] = useState('');
  const [availableFilters, setAvailableFilters] = useState({
    orderStatuses: [],
    paymentStatuses: [],
    paymentMethods: []
  });
  const [orderStats, setOrderStats] = useState({
    totalOrders: 0,
    pendingOrders: 0,
    completedOrders: 0,
    totalRevenue: 0
  });
  const [showOrderModal, setShowOrderModal] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState(null);
  const [statusUpdateLoading, setStatusUpdateLoading] = useState(false);

  // TZS currency formatting for Tanzania market
  const formatTZS = (amount) => {
    return new Intl.NumberFormat('sw-TZ', {
      style: 'currency',
      currency: 'TZS',
      minimumFractionDigits: 0
    }).format(amount);
  };

  // Fetch orders with current filters and pagination
  const fetchOrders = async () => {
    try {
      setLoading(true);
      setError('');

      const params = {
        page: pagination.currentPage,
        limit: pagination.itemsPerPage,
        ...filters
      };

      const response = await getAdminOrders(params);

      if (response && response.orders) {
        setOrders(response.orders);
        setPagination(prev => ({
          ...prev,
          ...response.pagination
        }));
      } else {
        setOrders([]);
      }
    } catch (err) {
      console.error('Error fetching orders:', err);
      setError('Failed to load orders. Please try again later.');
      setOrders([]);
    } finally {
      setLoading(false);
    }
  };

  // Fetch available filters
  const fetchFilters = async () => {
    try {
      const response = await getOrderFilters();
      if (response) {
        setAvailableFilters(response);
      }
    } catch (err) {
      console.error('Error fetching filters:', err);
    }
  };

  // Fetch order statistics
  const fetchOrderStats = async () => {
    try {
      const response = await getOrderStats('30d');
      if (response) {
        setOrderStats(response);
      }
    } catch (err) {
      console.error('Error fetching order stats:', err);
    }
  };

  useEffect(() => {
    fetchOrders();
  }, [pagination.currentPage, filters]);

  useEffect(() => {
    fetchFilters();
    fetchOrderStats();
  }, []);

  // Handle filter changes
  const handleFilterChange = (field, value) => {
    setFilters(prev => ({
      ...prev,
      [field]: value
    }));
    setPagination(prev => ({ ...prev, currentPage: 1 }));
  };

  // Handle search
  const handleSearch = async (searchQuery) => {
    try {
      setLoading(true);
      const response = await searchAdminOrders(searchQuery, filters);
      if (response && response.orders) {
        setOrders(response.orders);
        setPagination(prev => ({
          ...prev,
          ...response.pagination
        }));
      }
    } catch (err) {
      console.error('Error searching orders:', err);
      setError('Search failed. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Handle order selection for bulk operations
  const handleOrderSelect = (orderId) => {
    setSelectedOrders(prev => {
      if (prev.includes(orderId)) {
        return prev.filter(id => id !== orderId);
      } else {
        return [...prev, orderId];
      }
    });
  };

  // Handle select all orders
  const handleSelectAll = () => {
    if (selectedOrders.length === orders.length) {
      setSelectedOrders([]);
    } else {
      setSelectedOrders(orders.map(o => o._id));
    }
  };

  // Handle single order status update
  const handleStatusUpdate = async (orderId, statusData) => {
    try {
      setStatusUpdateLoading(true);
      await updateOrderStatus(orderId, statusData);
      setSuccess('Order status updated successfully');
      fetchOrders(); // Refresh the list
      setShowOrderModal(false);
    } catch (err) {
      console.error('Error updating order status:', err);
      setError('Failed to update order status. Please try again.');
    } finally {
      setStatusUpdateLoading(false);
    }
  };

  // Handle bulk operations
  const handleBulkOperation = async () => {
    if (selectedOrders.length === 0) {
      setError('Please select orders to perform bulk operation');
      return;
    }

    try {
      const statusData = {};
      if (bulkAction === 'confirm') {
        statusData.order_status = 'confirmed';
      } else if (bulkAction === 'ship') {
        statusData.order_status = 'shipped';
      } else if (bulkAction === 'deliver') {
        statusData.order_status = 'delivered';
      } else if (bulkAction === 'cancel') {
        statusData.order_status = 'cancelled';
      }

      await bulkUpdateOrderStatus(selectedOrders, statusData);
      setSuccess(`Bulk ${bulkAction} completed successfully`);
      setSelectedOrders([]);
      setShowBulkModal(false);
      fetchOrders(); // Refresh the list
    } catch (err) {
      console.error('Error performing bulk operation:', err);
      setError(`Failed to perform bulk ${bulkAction}. Please try again.`);
    }
  };

  // Handle page change
  const handlePageChange = (page) => {
    setPagination(prev => ({ ...prev, currentPage: page }));
  };

  // Handle order view
  const handleViewOrder = (order) => {
    setSelectedOrder(order);
    setShowOrderModal(true);
  };

  // Handle export
  const handleExport = async () => {
    try {
      const response = await exportOrders(filters);
      // Create download link
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `orders-${new Date().toISOString().split('T')[0]}.csv`);
      document.body.appendChild(link);
      link.click();
      link.remove();
      setSuccess('Orders exported successfully');
    } catch (err) {
      console.error('Error exporting orders:', err);
      setError('Failed to export orders. Please try again.');
    }
  };
  if (loading) {
    return <LoadingState message="Loading orders..." />;
  }

  return (
    <div className="space-y-6">
      {/* Header with Statistics */}
      <div className="bg-gradient-to-r from-blue-600 to-blue-800 rounded-lg p-6 text-white">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
          <div>
            <h1 className="text-2xl font-bold mb-2">📦 Order Management</h1>
            <p className="text-blue-100">Manage Phone Point Dar orders and shipments</p>
          </div>
          <div className="flex flex-wrap gap-2 mt-4 md:mt-0">
            <Button
              onClick={handleExport}
              className="bg-white text-blue-600 hover:bg-gray-100"
            >
              <i className="fas fa-download mr-2"></i>Export
            </Button>
            <Button className="bg-blue-500 hover:bg-blue-400 text-white">
              <i className="fas fa-plus mr-2"></i>New Order
            </Button>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-6">
          <div className="bg-blue-500 bg-opacity-50 rounded-lg p-4">
            <div className="text-2xl font-bold">{orderStats.totalOrders}</div>
            <div className="text-sm text-blue-100">Total Orders</div>
          </div>
          <div className="bg-blue-500 bg-opacity-50 rounded-lg p-4">
            <div className="text-2xl font-bold">{orderStats.pendingOrders}</div>
            <div className="text-sm text-blue-100">Pending</div>
          </div>
          <div className="bg-blue-500 bg-opacity-50 rounded-lg p-4">
            <div className="text-2xl font-bold">{orderStats.completedOrders}</div>
            <div className="text-sm text-blue-100">Completed</div>
          </div>
          <div className="bg-blue-500 bg-opacity-50 rounded-lg p-4">
            <div className="text-2xl font-bold">{formatTZS(orderStats.totalRevenue)}</div>
            <div className="text-sm text-blue-100">Revenue</div>
          </div>
        </div>
      </div>

      {/* Alerts */}
      {error && (
        <Alert
          type="error"
          message={error}
          onClose={() => setError('')}
        />
      )}

      {success && (
        <Alert
          type="success"
          message={success}
          onClose={() => setSuccess('')}
        />
      )}

      {/* Filters and Search */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="grid grid-cols-1 md:grid-cols-6 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Search</label>
            <input
              type="text"
              value={filters.search}
              onChange={(e) => handleFilterChange('search', e.target.value)}
              placeholder="Search orders..."
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Order Status</label>
            <select
              value={filters.status}
              onChange={(e) => handleFilterChange('status', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">All Statuses</option>
              {availableFilters.orderStatuses.map(status => (
                <option key={status} value={status}>{status}</option>
              ))}
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Payment Status</label>
            <select
              value={filters.payment_status}
              onChange={(e) => handleFilterChange('payment_status', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">All Payments</option>
              {availableFilters.paymentStatuses.map(status => (
                <option key={status} value={status}>{status}</option>
              ))}
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">From Date</label>
            <input
              type="date"
              value={filters.date_from}
              onChange={(e) => handleFilterChange('date_from', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">To Date</label>
            <input
              type="date"
              value={filters.date_to}
              onChange={(e) => handleFilterChange('date_to', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Sort By</label>
            <select
              value={filters.sort}
              onChange={(e) => handleFilterChange('sort', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="-created_at">Newest First</option>
              <option value="created_at">Oldest First</option>
              <option value="order_number">Order Number</option>
              <option value="-total_amount">Highest Value</option>
              <option value="total_amount">Lowest Value</option>
            </select>
          </div>
        </div>
      </div>

      {/* Bulk Actions */}
      {selectedOrders.length > 0 && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <span className="text-sm text-blue-800">
              {selectedOrders.length} order{selectedOrders.length > 1 ? 's' : ''} selected
            </span>
            <div className="flex space-x-2">
              <Button
                size="sm"
                variant="outline"
                onClick={() => {
                  setBulkAction('confirm');
                  setShowBulkModal(true);
                }}
                className="text-green-600 border-green-300 hover:bg-green-50"
              >
                <i className="fas fa-check mr-1"></i>Confirm
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => {
                  setBulkAction('ship');
                  setShowBulkModal(true);
                }}
                className="text-blue-600 border-blue-300 hover:bg-blue-50"
              >
                <i className="fas fa-shipping-fast mr-1"></i>Ship
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => {
                  setBulkAction('cancel');
                  setShowBulkModal(true);
                }}
                className="text-red-600 border-red-300 hover:bg-red-50"
              >
                <i className="fas fa-times mr-1"></i>Cancel
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Orders Table */}
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  <input
                    type="checkbox"
                    checked={selectedOrders.length === orders.length && orders.length > 0}
                    onChange={handleSelectAll}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Order
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Customer
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Products
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Amount (TZS)
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Payment
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Date
                </th>
                <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {orders.length > 0 ? (
                orders.map((order) => (
                  <tr key={order._id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <input
                        type="checkbox"
                        checked={selectedOrders.includes(order._id)}
                        onChange={() => handleOrderSelect(order._id)}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">
                        {order.order_number}
                      </div>
                      <div className="text-sm text-gray-500">
                        {new Date(order.created_at).toLocaleDateString()}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        <div className="font-medium">{order.customer_name}</div>
                        <div className="text-gray-500">{order.customer_email}</div>
                        {order.customer_phone && (
                          <div className="text-gray-500">{order.customer_phone}</div>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        {order.items && order.items.length > 0 ? (
                          <>
                            <div className="font-medium">
                              {order.items[0].name}
                              {order.items.length > 1 && ` +${order.items.length - 1} more`}
                            </div>
                            <div className="text-gray-500">
                              {order.items.reduce((total, item) => total + item.quantity, 0)} items
                            </div>
                          </>
                        ) : (
                          <span className="text-gray-500">No items</span>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">
                        {formatTZS(order.total_amount || order.amount || 0)}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        order.order_status === 'delivered' ? 'bg-green-100 text-green-800' :
                        order.order_status === 'shipped' ? 'bg-blue-100 text-blue-800' :
                        order.order_status === 'processing' ? 'bg-yellow-100 text-yellow-800' :
                        order.order_status === 'confirmed' ? 'bg-purple-100 text-purple-800' :
                        order.order_status === 'cancelled' ? 'bg-red-100 text-red-800' :
                        'bg-gray-100 text-gray-800'
                      }`}>
                        {order.order_status || 'pending'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        order.payment_status === 'completed' ? 'bg-green-100 text-green-800' :
                        order.payment_status === 'failed' ? 'bg-red-100 text-red-800' :
                        order.payment_status === 'refunded' ? 'bg-orange-100 text-orange-800' :
                        'bg-yellow-100 text-yellow-800'
                      }`}>
                        {order.payment_status || 'pending'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {new Date(order.created_at).toLocaleDateString('en-GB')}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex justify-end space-x-2">
                        <button
                          onClick={() => handleViewOrder(order)}
                          className="text-blue-600 hover:text-blue-900"
                        >
                          <i className="fas fa-eye mr-1"></i>View
                        </button>
                        <button
                          onClick={() => {
                            setSelectedOrder(order);
                            setShowOrderModal(true);
                          }}
                          className="text-indigo-600 hover:text-indigo-900"
                        >
                          <i className="fas fa-edit mr-1"></i>Update
                        </button>
                      </div>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan="9" className="px-6 py-12 text-center">
                    <div className="text-gray-500">
                      <i className="fas fa-shopping-cart text-4xl mb-4"></i>
                      <p className="text-lg font-medium">No orders found</p>
                      <p className="text-sm">Orders will appear here when customers make purchases</p>
                    </div>
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        {pagination.totalPages > 1 && (
          <div className="px-6 py-4 border-t border-gray-200">
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-500">
                Showing {((pagination.currentPage - 1) * pagination.itemsPerPage) + 1} to{' '}
                {Math.min(pagination.currentPage * pagination.itemsPerPage, pagination.totalItems)} of{' '}
                {pagination.totalItems} results
              </div>
              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(pagination.currentPage - 1)}
                  disabled={pagination.currentPage === 1}
                >
                  <i className="fas fa-chevron-left mr-1"></i>Previous
                </Button>

                {/* Page numbers */}
                {Array.from({ length: Math.min(5, pagination.totalPages) }, (_, i) => {
                  const page = i + Math.max(1, pagination.currentPage - 2);
                  if (page <= pagination.totalPages) {
                    return (
                      <Button
                        key={page}
                        variant={page === pagination.currentPage ? "primary" : "outline"}
                        size="sm"
                        onClick={() => handlePageChange(page)}
                      >
                        {page}
                      </Button>
                    );
                  }
                  return null;
                })}

                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(pagination.currentPage + 1)}
                  disabled={pagination.currentPage === pagination.totalPages}
                >
                  Next<i className="fas fa-chevron-right ml-1"></i>
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Bulk Operation Modal */}
      {showBulkModal && (
        <Modal
          isOpen={showBulkModal}
          onClose={() => setShowBulkModal(false)}
          title={`Bulk ${bulkAction.charAt(0).toUpperCase() + bulkAction.slice(1)} Orders`}
        >
          <div className="space-y-4">
            <p className="text-gray-600">
              Are you sure you want to {bulkAction} {selectedOrders.length} selected order{selectedOrders.length > 1 ? 's' : ''}?
            </p>

            {bulkAction === 'cancel' && (
              <div className="bg-red-50 border border-red-200 rounded-md p-4">
                <p className="text-sm text-red-800">
                  <i className="fas fa-exclamation-triangle mr-2"></i>
                  This action will cancel the selected orders and may trigger refunds.
                </p>
              </div>
            )}

            <div className="flex justify-end space-x-3">
              <Button
                variant="outline"
                onClick={() => setShowBulkModal(false)}
              >
                Cancel
              </Button>
              <Button
                onClick={handleBulkOperation}
                className={
                  bulkAction === 'cancel' ? 'bg-red-600 hover:bg-red-700' :
                  bulkAction === 'confirm' ? 'bg-green-600 hover:bg-green-700' :
                  'bg-blue-600 hover:bg-blue-700'
                }
              >
                {bulkAction === 'cancel' ? 'Cancel Orders' :
                 bulkAction === 'confirm' ? 'Confirm Orders' :
                 bulkAction === 'ship' ? 'Ship Orders' :
                 'Update Orders'}
              </Button>
            </div>
          </div>
        </Modal>
      )}

      {/* Order Details Modal */}
      {showOrderModal && selectedOrder && (
        <Modal
          isOpen={showOrderModal}
          onClose={() => setShowOrderModal(false)}
          title={`Order ${selectedOrder.order_number}`}
          size="lg"
        >
          <div className="space-y-6">
            {/* Order Info */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">Customer</label>
                <p className="text-sm text-gray-900">{selectedOrder.customer_name}</p>
                <p className="text-sm text-gray-500">{selectedOrder.customer_email}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Amount</label>
                <p className="text-sm text-gray-900">{formatTZS(selectedOrder.total_amount || selectedOrder.amount || 0)}</p>
              </div>
            </div>

            {/* Status Updates */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Order Status</label>
                <select
                  value={selectedOrder.order_status || 'pending'}
                  onChange={(e) => setSelectedOrder({...selectedOrder, order_status: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="pending">Pending</option>
                  <option value="confirmed">Confirmed</option>
                  <option value="processing">Processing</option>
                  <option value="shipped">Shipped</option>
                  <option value="delivered">Delivered</option>
                  <option value="cancelled">Cancelled</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Payment Status</label>
                <select
                  value={selectedOrder.payment_status || 'pending'}
                  onChange={(e) => setSelectedOrder({...selectedOrder, payment_status: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="pending">Pending</option>
                  <option value="completed">Completed</option>
                  <option value="failed">Failed</option>
                  <option value="refunded">Refunded</option>
                </select>
              </div>
            </div>

            {/* Internal Notes */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Internal Notes</label>
              <textarea
                value={selectedOrder.internal_notes || ''}
                onChange={(e) => setSelectedOrder({...selectedOrder, internal_notes: e.target.value})}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Add internal notes..."
              />
            </div>

            <div className="flex justify-end space-x-3">
              <Button
                variant="outline"
                onClick={() => setShowOrderModal(false)}
              >
                Cancel
              </Button>
              <Button
                onClick={() => handleStatusUpdate(selectedOrder._id, {
                  order_status: selectedOrder.order_status,
                  payment_status: selectedOrder.payment_status,
                  internal_notes: selectedOrder.internal_notes
                })}
                disabled={statusUpdateLoading}
              >
                {statusUpdateLoading ? 'Updating...' : 'Update Order'}
              </Button>
            </div>
          </div>
        </Modal>
      )}
    </div>
  );
};

export default OrderManagementPage;
