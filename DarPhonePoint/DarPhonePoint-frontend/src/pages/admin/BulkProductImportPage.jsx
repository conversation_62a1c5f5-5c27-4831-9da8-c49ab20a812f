import React, { useState, useRef } from 'react';
import AdminPageWrapper, { QuickActionButton, AdminStatsCard } from '../../components/layout/AdminPageWrapper';
import Button from '../../components/ui/Button';
import { safeApiRequest } from '../../api/apiClient';

const BulkProductImportPage = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [importResults, setImportResults] = useState(null);
  const [selectedFile, setSelectedFile] = useState(null);
  const [previewData, setPreviewData] = useState([]);
  const [validationErrors, setValidationErrors] = useState([]);
  const fileInputRef = useRef(null);

  const csvTemplate = `name,brand,category,price,description,storage,color,condition,warranty_months,stock_quantity
iPhone 14 Pro,Apple,Smartphones,2500000,Latest iPhone with Pro camera system,256GB,Deep Purple,New,12,10
Samsung Galaxy S23,Samsung,Smartphones,1800000,Flagship Android smartphone,128GB,Phantom Black,New,24,15
Xiaomi Redmi Note 12,Xiaomi,Smartphones,450000,Budget-friendly smartphone,64GB,Midnight Black,New,12,25
AirPods Pro 2,Apple,Accessories,650000,Wireless earbuds with ANC,N/A,White,New,12,20
Samsung Galaxy Buds2,Samsung,Accessories,280000,True wireless earbuds,N/A,Graphite,New,12,30`;

  const handleFileSelect = (event) => {
    const file = event.target.files[0];
    if (file) {
      if (file.type !== 'text/csv' && !file.name.endsWith('.csv')) {
        setError('Please select a valid CSV file');
        return;
      }
      
      setSelectedFile(file);
      setError('');
      
      // Read and preview CSV content
      const reader = new FileReader();
      reader.onload = (e) => {
        const csv = e.target.result;
        const lines = csv.split('\n');
        const headers = lines[0].split(',');
        
        // Parse first 5 rows for preview
        const preview = lines.slice(1, 6).map((line, index) => {
          const values = line.split(',');
          const row = {};
          headers.forEach((header, i) => {
            row[header.trim()] = values[i]?.trim() || '';
          });
          row.rowNumber = index + 2; // +2 because we skip header and start from row 2
          return row;
        }).filter(row => Object.values(row).some(val => val !== ''));
        
        setPreviewData(preview);
        validateData(preview);
      };
      reader.readAsText(file);
    }
  };

  const validateData = (data) => {
    const errors = [];
    const requiredFields = ['name', 'brand', 'category', 'price'];
    
    data.forEach((row) => {
      requiredFields.forEach((field) => {
        if (!row[field] || row[field].trim() === '') {
          errors.push(`Row ${row.rowNumber}: Missing required field '${field}'`);
        }
      });
      
      // Validate price
      if (row.price && isNaN(parseFloat(row.price))) {
        errors.push(`Row ${row.rowNumber}: Invalid price format`);
      }
      
      // Validate stock quantity
      if (row.stock_quantity && isNaN(parseInt(row.stock_quantity))) {
        errors.push(`Row ${row.rowNumber}: Invalid stock quantity format`);
      }
    });
    
    setValidationErrors(errors);
  };

  const handleImport = async () => {
    if (!selectedFile) {
      setError('Please select a CSV file to import');
      return;
    }
    
    if (validationErrors.length > 0) {
      setError('Please fix validation errors before importing');
      return;
    }
    
    setLoading(true);
    try {
      const formData = new FormData();
      formData.append('file', selectedFile);
      
      // Real bulk import API call
      const response = await safeApiRequest({
        method: 'POST',
        url: '/bulk/products/import',
        data: formData,
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });

      setImportResults(response.data);
      setSuccess(response.message || `Successfully imported ${response.data.successful_imports} products`);
      
      // Clear form
      setSelectedFile(null);
      setPreviewData([]);
      setValidationErrors([]);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
      
    } catch (err) {
      console.error('Error importing products:', err);
      setError('Failed to import products. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const downloadTemplate = () => {
    const blob = new Blob([csvTemplate], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'phone_point_dar_product_template.csv';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
  };

  const formatTZS = (amount) => {
    return new Intl.NumberFormat('sw-TZ', {
      style: 'currency',
      currency: 'TZS',
      minimumFractionDigits: 0
    }).format(amount);
  };

  return (
    <AdminPageWrapper
      title="📤 Bulk Product Import"
      description="Import multiple products to Phone Point Dar inventory using CSV files"
      breadcrumbs={[
        { name: 'Dashboard', href: '/admin' },
        { name: 'Products', href: '/admin/products' },
        { name: 'Bulk Import', href: '/admin/products/import' }
      ]}
      actions={
        <QuickActionButton
          icon="fas fa-download"
          variant="secondary"
          onClick={downloadTemplate}
        >
          Download Template
        </QuickActionButton>
      }
      loading={loading}
      error={error}
      success={success}
      onErrorClose={() => setError('')}
      onSuccessClose={() => setSuccess('')}
    >
      {/* Import Instructions */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6">
        <h3 className="text-lg font-semibold text-blue-900 mb-3">
          <i className="fas fa-info-circle mr-2"></i>
          Import Instructions
        </h3>
        <div className="text-blue-800 space-y-2">
          <p>• Download the CSV template and fill in your product data</p>
          <p>• Required fields: name, brand, category, price</p>
          <p>• Prices should be in Tanzanian Shillings (TZS)</p>
          <p>• Use standard categories: Smartphones, Accessories, Tablets, etc.</p>
          <p>• Maximum file size: 10MB</p>
        </div>
      </div>

      {/* File Upload Section */}
      <div className="bg-white rounded-lg shadow-md p-6 mb-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Upload CSV File</h3>
        
        <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
          <div className="space-y-4">
            <div className="mx-auto w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
              <i className="fas fa-file-csv text-blue-600 text-2xl"></i>
            </div>
            
            <div>
              <p className="text-lg font-medium text-gray-900">
                {selectedFile ? selectedFile.name : 'Select CSV file to import'}
              </p>
              <p className="text-gray-500">
                {selectedFile ? `${(selectedFile.size / 1024).toFixed(1)} KB` : 'or drag and drop here'}
              </p>
            </div>
            
            <div className="space-x-3">
              <Button
                onClick={() => fileInputRef.current?.click()}
                variant="outline"
              >
                <i className="fas fa-folder-open mr-2"></i>
                Choose File
              </Button>
              
              {selectedFile && (
                <Button
                  onClick={handleImport}
                  disabled={validationErrors.length > 0}
                >
                  <i className="fas fa-upload mr-2"></i>
                  Import Products
                </Button>
              )}
            </div>
          </div>
        </div>
        
        <input
          ref={fileInputRef}
          type="file"
          accept=".csv"
          onChange={handleFileSelect}
          className="hidden"
        />
      </div>

      {/* Validation Errors */}
      {validationErrors.length > 0 && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-6 mb-6">
          <h3 className="text-lg font-semibold text-red-900 mb-3">
            <i className="fas fa-exclamation-triangle mr-2"></i>
            Validation Errors ({validationErrors.length})
          </h3>
          <div className="max-h-40 overflow-y-auto">
            {validationErrors.map((error, index) => (
              <p key={index} className="text-red-800 text-sm mb-1">• {error}</p>
            ))}
          </div>
        </div>
      )}

      {/* Data Preview */}
      {previewData.length > 0 && (
        <div className="bg-white rounded-lg shadow-md overflow-hidden mb-6">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">Data Preview (First 5 rows)</h3>
          </div>
          
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Product Name
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Brand
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Category
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Price (TZS)
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Stock
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {previewData.map((row, index) => (
                  <tr key={index} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {row.name}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {row.brand}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {row.category}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {row.price ? formatTZS(parseFloat(row.price)) : '-'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {row.stock_quantity || '-'}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* Import Results */}
      {importResults && (
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Import Results</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <AdminStatsCard
              title="Total Rows"
              value={importResults.total_rows}
              icon="fas fa-list"
              color="blue"
            />
            <AdminStatsCard
              title="Successful"
              value={importResults.successful_imports}
              icon="fas fa-check-circle"
              color="green"
            />
            <AdminStatsCard
              title="Failed"
              value={importResults.failed_imports}
              icon="fas fa-times-circle"
              color="red"
            />
          </div>

          {importResults.errors && importResults.errors.length > 0 && (
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
              <h4 className="font-semibold text-yellow-900 mb-2">Import Errors:</h4>
              {importResults.errors.map((error, index) => (
                <p key={index} className="text-yellow-800 text-sm">• {error}</p>
              ))}
            </div>
          )}

          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Product Name
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Brand
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Price
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {importResults.imported_products?.map((product, index) => (
                  <tr key={index} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {product.name}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {product.brand}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {formatTZS(product.price)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                        Imported
                      </span>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}
    </AdminPageWrapper>
  );
};

export default BulkProductImportPage;
