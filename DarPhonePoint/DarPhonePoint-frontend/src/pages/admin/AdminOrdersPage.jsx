import React, { useState, useEffect } from 'react';
import { Link, useSearchParams } from 'react-router-dom';
import { safeApiRequest } from '../../api/apiClient';
import PageHeader from '../../components/ui/PageHeader';
import LoadingState from '../../components/ui/LoadingState';
import Alert from '../../components/ui/Alert';
import Button from '../../components/ui/Button';

const PhoneOrdersPage = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const [phoneOrders, setPhoneOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [selectedOrders, setSelectedOrders] = useState([]);
  const [showShippingModal, setShowShippingModal] = useState(false);
  const [showReturnModal, setShowReturnModal] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState(null);
  const [filters, setFilters] = useState({
    search: searchParams.get('search') || '',
    order_status: searchParams.get('status') || '',
    shipping_status: searchParams.get('shipping_status') || '',
    payment_method: searchParams.get('payment_method') || ''
  });
  const [shippingData, setShippingData] = useState({
    tracking_number: '',
    shipping_provider: '',
    estimated_delivery: '',
    notes: ''
  });
  const [returnData, setReturnData] = useState({
    reason: '',
    condition: '',
    refund_amount: '',
    notes: ''
  });
  const [pagination, setPagination] = useState({
    page: 1,
    pages: 1,
    total: 0
  });

  useEffect(() => {
    fetchPhoneOrders();
  }, [filters, pagination.page]);

  const fetchPhoneOrders = async () => {
    setLoading(true);
    try {
      const params = new URLSearchParams({
        page: pagination.page,
        limit: 20,
        ...filters
      });

      const response = await safeApiRequest({
        method: 'GET',
        url: `/orders/admin/all?${params.toString()}`
      });

      if (response.data && response.data.data) {
        setPhoneOrders(response.data.data);
        setPagination({
          page: response.data.page || 1,
          pages: response.data.pages || 1,
          total: response.data.total || 0
        });
      }
    } catch (err) {
      console.error('Error fetching phone orders:', err);
      setError('Failed to load phone orders');
    } finally {
      setLoading(false);
    }
  };

  const formatTZS = (amount) => {
    return new Intl.NumberFormat('sw-TZ', {
      style: 'currency',
      currency: 'TZS',
      minimumFractionDigits: 0
    }).format(amount);
  };

  const getOrderStatusColor = (status) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'processing': return 'bg-blue-100 text-blue-800';
      case 'shipped': return 'bg-purple-100 text-purple-800';
      case 'delivered': return 'bg-green-100 text-green-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getPaymentMethodIcon = (method) => {
    switch (method) {
      case 'mpesa': return 'fas fa-mobile-alt';
      case 'tigo_pesa': return 'fas fa-mobile-alt';
      case 'airtel_money': return 'fas fa-mobile-alt';
      case 'bank_transfer': return 'fas fa-university';
      case 'cash_on_delivery': return 'fas fa-money-bill-wave';
      default: return 'fas fa-credit-card';
    }
  };

  const handleFilterChange = (key, value) => {
    const newFilters = { ...filters, [key]: value };
    setFilters(newFilters);

    // Update URL params
    const newParams = new URLSearchParams();
    Object.entries(newFilters).forEach(([k, v]) => {
      if (v) newParams.set(k, v);
    });
    setSearchParams(newParams);
  };

  const handleShippingUpdate = async () => {
    if (!selectedOrder || !shippingData.tracking_number) return;

    try {
      await safeApiRequest({
        method: 'PUT',
        url: `/orders/${selectedOrder._id}/shipping`,
        data: {
          ...shippingData,
          order_status: 'shipped'
        }
      });

      setShowShippingModal(false);
      setSelectedOrder(null);
      setShippingData({
        tracking_number: '',
        shipping_provider: '',
        estimated_delivery: '',
        notes: ''
      });
      setSuccess('Order marked as shipped successfully');
      fetchPhoneOrders();
    } catch (err) {
      console.error('Error updating shipping:', err);
      setError('Failed to update shipping information');
    }
  };

  const handleReturnProcess = async () => {
    if (!selectedOrder || !returnData.reason) return;

    try {
      await safeApiRequest({
        method: 'POST',
        url: `/orders/${selectedOrder._id}/return`,
        data: {
          ...returnData,
          refund_amount: returnData.refund_amount ? parseFloat(returnData.refund_amount) : null
        }
      });

      setShowReturnModal(false);
      setSelectedOrder(null);
      setReturnData({
        reason: '',
        condition: '',
        refund_amount: '',
        notes: ''
      });
      setSuccess('Return processed successfully');
      fetchPhoneOrders();
    } catch (err) {
      console.error('Error processing return:', err);
      setError('Failed to process return');
    }
  };

  const handleQuickStatusUpdate = async (orderId, status) => {
    try {
      await safeApiRequest({
        method: 'PUT',
        url: `/orders/${orderId}/status`,
        data: { order_status: status }
      });

      setSuccess(`Order status updated to ${status}`);
      fetchPhoneOrders();
    } catch (err) {
      console.error('Error updating order status:', err);
      setError('Failed to update order status');
    }
  };

  const formatDate = (date) => {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusColor = (status, type = 'fulfillment') => {
    const colors = {
      fulfillment: {
        pending: 'bg-yellow-100 text-yellow-800',
        processing: 'bg-blue-100 text-blue-800',
        shipped: 'bg-purple-100 text-purple-800',
        delivered: 'bg-green-100 text-green-800',
        cancelled: 'bg-red-100 text-red-800'
      },
      payment: {
        pending: 'bg-yellow-100 text-yellow-800',
        completed: 'bg-green-100 text-green-800',
        failed: 'bg-red-100 text-red-800',
        refunded: 'bg-gray-100 text-gray-800'
      }
    };
    
    return colors[type]?.[status] || 'bg-gray-100 text-gray-800';
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <LoadingState text="Loading phone orders..." size="lg" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <PageHeader
        title="📱 Phone Point Dar - Order Management"
        description="Process phone orders and manage deliveries across Tanzania"
        breadcrumbs={[
          { name: 'Dashboard', href: '/admin' },
          { name: 'Phone Orders', href: '/admin/orders' }
        ]}
        actions={
          <div className="flex space-x-3">
            <Link to="/admin/orders/pending">
              <Button variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-300 hover:bg-yellow-100">
                <i className="fas fa-clock mr-2"></i>
                Pending Orders
              </Button>
            </Link>
            <Link to="/admin/orders/shipping">
              <Button variant="outline" className="bg-blue-50 text-blue-700 border-blue-300 hover:bg-blue-100">
                <i className="fas fa-shipping-fast mr-2"></i>
                Shipping Labels
              </Button>
            </Link>
            <Link to="/admin/analytics/orders">
              <Button className="bg-blue-600 hover:bg-blue-700">
                <i className="fas fa-chart-line mr-2"></i>
                Sales Analytics
              </Button>
            </Link>
          </div>
        }
      />

      {error && (
        <Alert
          type="error"
          message={error}
          onClose={() => setError('')}
        />
      )}

      {success && (
        <Alert
          type="success"
          message={success}
          onClose={() => setSuccess('')}
        />
      )}

      {/* Phone Order Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center">
            <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
              <i className="fas fa-clock text-yellow-600 text-xl"></i>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Pending Orders</p>
              <p className="text-2xl font-bold text-gray-900">
                {phoneOrders.filter(order => order.order_status === 'pending').length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center">
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <i className="fas fa-shipping-fast text-blue-600 text-xl"></i>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Shipped</p>
              <p className="text-2xl font-bold text-gray-900">
                {phoneOrders.filter(order => order.order_status === 'shipped').length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center">
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
              <i className="fas fa-check-circle text-green-600 text-xl"></i>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Delivered</p>
              <p className="text-2xl font-bold text-gray-900">
                {phoneOrders.filter(order => order.order_status === 'delivered').length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center">
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <i className="fas fa-money-bill-wave text-blue-600 text-xl"></i>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Total Revenue</p>
              <p className="text-2xl font-bold text-gray-900">
                {formatTZS(phoneOrders.reduce((sum, order) => sum + (order.total_amount || 0), 0))}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Phone Order Filters */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          <i className="fas fa-filter mr-2 text-blue-600"></i>
          Filter Phone Orders
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Search Orders
            </label>
            <input
              type="text"
              placeholder="Search by order ID, customer, phone..."
              value={filters.search}
              onChange={(e) => handleFilterChange('search', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Order Status
            </label>
            <select
              value={filters.order_status}
              onChange={(e) => handleFilterChange('order_status', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">All Statuses</option>
              <option value="pending">Pending</option>
              <option value="processing">Processing</option>
              <option value="shipped">Shipped</option>
              <option value="delivered">Delivered</option>
              <option value="cancelled">Cancelled</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Payment Method
            </label>
            <select
              value={filters.payment_method}
              onChange={(e) => handleFilterChange('payment_method', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">All Methods</option>
              <option value="mpesa">M-Pesa</option>
              <option value="tigo_pesa">Tigo Pesa</option>
              <option value="airtel_money">Airtel Money</option>
              <option value="bank_transfer">Bank Transfer</option>
              <option value="cash_on_delivery">Cash on Delivery</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Date Range
            </label>
            <select
              value={filters.date_range}
              onChange={(e) => handleFilterChange('date_range', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">All Time</option>
              <option value="today">Today</option>
              <option value="week">This Week</option>
              <option value="month">This Month</option>
            </select>
          </div>
        </div>
      </div>

      {/* Phone Orders Table */}
      <div className="bg-white rounded-lg shadow-md overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900 flex items-center">
            <i className="fas fa-shopping-cart mr-2 text-blue-600"></i>
            Phone Orders ({phoneOrders.length})
          </h3>
        </div>

        {phoneOrders.length === 0 ? (
          <div className="p-12 text-center">
            <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <i className="fas fa-shopping-cart text-blue-600 text-2xl"></i>
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">No phone orders found</h3>
            <p className="text-gray-500">
              {filters.search || filters.order_status || filters.payment_method
                ? "No orders match your current filters."
                : "No phone orders have been placed yet."}
            </p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Order Details
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Customer & Phone
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Amount (TZS)
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Payment
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {phoneOrders.map((order) => (
                  <tr key={order._id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          #{order.order_number || order._id.slice(-8)}
                        </div>
                        <div className="text-sm text-gray-500">
                          {formatDate(order.created_at)}
                        </div>
                        {order.tracking_number && (
                          <div className="text-xs text-blue-600">
                            Tracking: {order.tracking_number}
                          </div>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          {order.customer?.name || 'N/A'}
                        </div>
                        <div className="text-sm text-gray-500">
                          {order.customer?.email || order.customer?.phone}
                        </div>
                        {order.items && order.items.length > 0 && (
                          <div className="text-xs text-gray-400 mt-1">
                            {order.items[0].product?.name}
                            {order.items.length > 1 && ` +${order.items.length - 1} more`}
                          </div>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">
                        {formatTZS(order.total_amount || 0)}
                      </div>
                      {order.items && (
                        <div className="text-xs text-gray-500">
                          {order.items.length} item{order.items.length !== 1 ? 's' : ''}
                        </div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <i className={`${getPaymentMethodIcon(order.payment_method)} mr-2 text-gray-400`}></i>
                        <span className="text-sm text-gray-900 capitalize">
                          {(order.payment_method || 'N/A').replace('_', ' ')}
                        </span>
                      </div>
                      <div className={`text-xs px-2 py-1 rounded-full inline-block mt-1 ${
                        order.payment_status === 'completed' ? 'bg-green-100 text-green-800' :
                        order.payment_status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-red-100 text-red-800'
                      }`}>
                        {order.payment_status || 'Pending'}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        getOrderStatusColor(order.order_status || 'pending')
                      }`}>
                        {(order.order_status || 'pending').charAt(0).toUpperCase() + (order.order_status || 'pending').slice(1)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex items-center space-x-2">
                        {order.order_status === 'processing' && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              setSelectedOrder(order);
                              setShowShippingModal(true);
                            }}
                            title="Mark as Shipped"
                            className="text-blue-600 hover:text-blue-800"
                          >
                            <i className="fas fa-shipping-fast"></i>
                          </Button>
                        )}
                        {(order.order_status === 'delivered' || order.order_status === 'shipped') && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              setSelectedOrder(order);
                              setShowReturnModal(true);
                            }}
                            title="Process Return"
                            className="text-orange-600 hover:text-orange-800"
                          >
                            <i className="fas fa-undo"></i>
                          </Button>
                        )}
                        <Link
                          to={`/admin/orders/${order._id}`}
                          className="text-green-600 hover:text-green-900 p-1"
                          title="View Order Details"
                        >
                          <i className="fas fa-eye"></i>
                        </Link>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Pagination */}
      {pagination.pages > 1 && (
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center justify-between">
            <Button
              variant="outline"
              onClick={() => setPagination(prev => ({ ...prev, page: prev.page - 1 }))}
              disabled={pagination.page === 1}
            >
              Previous
            </Button>
            <span className="text-sm text-gray-700">
              Page {pagination.page} of {pagination.pages} ({pagination.total} total orders)
            </span>
            <Button
              variant="outline"
              onClick={() => setPagination(prev => ({ ...prev, page: prev.page + 1 }))}
              disabled={pagination.page === pagination.pages}
            >
              Next
            </Button>
          </div>
        </div>
      )}

      {/* Shipping Modal */}
      {showShippingModal && selectedOrder && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                <i className="fas fa-shipping-fast mr-2 text-blue-600"></i>
                Ship Order #{selectedOrder.order_number || selectedOrder._id.slice(-8)}
              </h3>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Tracking Number *
                  </label>
                  <input
                    type="text"
                    value={shippingData.tracking_number}
                    onChange={(e) => setShippingData({ ...shippingData, tracking_number: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Enter tracking number"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Shipping Provider
                  </label>
                  <select
                    value={shippingData.shipping_provider}
                    onChange={(e) => setShippingData({ ...shippingData, shipping_provider: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">Select Provider</option>
                    <option value="dhl">DHL Tanzania</option>
                    <option value="posta">Posta Tanzania</option>
                    <option value="fedex">FedEx</option>
                    <option value="local_courier">Local Courier</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Estimated Delivery
                  </label>
                  <input
                    type="date"
                    value={shippingData.estimated_delivery}
                    onChange={(e) => setShippingData({ ...shippingData, estimated_delivery: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Shipping Notes
                  </label>
                  <textarea
                    value={shippingData.notes}
                    onChange={(e) => setShippingData({ ...shippingData, notes: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    rows="3"
                    placeholder="Additional shipping notes..."
                  />
                </div>
              </div>

              <div className="flex justify-end space-x-3 mt-6">
                <Button
                  variant="outline"
                  onClick={() => {
                    setShowShippingModal(false);
                    setSelectedOrder(null);
                  }}
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleShippingUpdate}
                  disabled={!shippingData.tracking_number}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  Mark as Shipped
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Return Modal */}
      {showReturnModal && selectedOrder && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                <i className="fas fa-undo mr-2 text-orange-600"></i>
                Process Return for Order #{selectedOrder.order_number || selectedOrder._id.slice(-8)}
              </h3>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Return Reason *
                  </label>
                  <select
                    value={returnData.reason}
                    onChange={(e) => setReturnData({ ...returnData, reason: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">Select Reason</option>
                    <option value="defective">Defective Device</option>
                    <option value="wrong_item">Wrong Item Sent</option>
                    <option value="customer_change">Customer Changed Mind</option>
                    <option value="damaged_shipping">Damaged in Shipping</option>
                    <option value="warranty_claim">Warranty Claim</option>
                    <option value="other">Other</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Device Condition
                  </label>
                  <select
                    value={returnData.condition}
                    onChange={(e) => setReturnData({ ...returnData, condition: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">Select Condition</option>
                    <option value="new">Like New</option>
                    <option value="good">Good Condition</option>
                    <option value="fair">Fair Condition</option>
                    <option value="poor">Poor Condition</option>
                    <option value="damaged">Damaged</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Refund Amount (TZS)
                  </label>
                  <input
                    type="number"
                    value={returnData.refund_amount}
                    onChange={(e) => setReturnData({ ...returnData, refund_amount: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Enter refund amount"
                    min="0"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Return Notes
                  </label>
                  <textarea
                    value={returnData.notes}
                    onChange={(e) => setReturnData({ ...returnData, notes: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    rows="3"
                    placeholder="Additional notes about the return..."
                  />
                </div>
              </div>

              <div className="flex justify-end space-x-3 mt-6">
                <Button
                  variant="outline"
                  onClick={() => {
                    setShowReturnModal(false);
                    setSelectedOrder(null);
                  }}
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleReturnProcess}
                  disabled={!returnData.reason}
                  className="bg-orange-600 hover:bg-orange-700"
                >
                  Process Return
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default PhoneOrdersPage;
