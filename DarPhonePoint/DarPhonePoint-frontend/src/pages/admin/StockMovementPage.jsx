import React, { useState, useEffect } from 'react';
import AdminPageWrapper, { QuickActionButton, AdminStatsCard } from '../../components/layout/AdminPageWrapper';
import Button from '../../components/ui/Button';
import { safeApiRequest } from '../../api/apiClient';

const StockMovementPage = () => {
  const [movements, setMovements] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [typeFilter, setTypeFilter] = useState('all');
  const [dateFilter, setDateFilter] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(20);

  useEffect(() => {
    fetchStockMovements();
  }, []);

  const fetchStockMovements = async () => {
    setLoading(true);
    try {
      // Since we don't have a dedicated stock movements endpoint, we'll simulate with inventory data
      const response = await safeApiRequest({
        method: 'GET',
        url: '/inventory'
      });

      if (response.data && response.data.data) {
        // Generate mock stock movements based on inventory data
        const mockMovements = [];
        
        response.data.data.forEach(item => {
          if (item.product) {
            // Generate multiple movements per product
            const movementTypes = ['stock_in', 'stock_out', 'adjustment', 'transfer'];
            const reasons = {
              'stock_in': ['Purchase', 'Return', 'Transfer In'],
              'stock_out': ['Sale', 'Damage', 'Transfer Out'],
              'adjustment': ['Count Correction', 'System Adjustment'],
              'transfer': ['Warehouse Transfer', 'Store Transfer']
            };

            for (let i = 0; i < Math.floor(Math.random() * 5) + 1; i++) {
              const type = movementTypes[Math.floor(Math.random() * movementTypes.length)];
              const reason = reasons[type][Math.floor(Math.random() * reasons[type].length)];
              
              mockMovements.push({
                _id: `${item._id}-${i}`,
                product_id: item.product._id,
                product_name: item.product.name,
                brand: item.product.brand,
                category: item.product.category,
                movement_type: type,
                reason: reason,
                quantity_before: Math.floor(Math.random() * 50) + 10,
                quantity_change: type === 'stock_in' ? 
                  Math.floor(Math.random() * 20) + 1 : 
                  -(Math.floor(Math.random() * 10) + 1),
                quantity_after: 0, // Will be calculated
                unit_cost: item.product.cost_price || item.product.price * 0.7,
                total_value: 0, // Will be calculated
                reference_number: `REF-${Date.now()}-${i}`,
                notes: `${reason} for ${item.product.name}`,
                created_by: 'Admin User',
                created_at: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
                warehouse: 'Main Warehouse',
                supplier_customer: type === 'stock_in' ? 'Tanzania Mobile Distributors' : 
                                 type === 'stock_out' ? 'Customer Sale' : 'Internal'
              });
            }
          }
        });

        // Calculate quantity_after and total_value
        mockMovements.forEach(movement => {
          movement.quantity_after = movement.quantity_before + movement.quantity_change;
          movement.total_value = Math.abs(movement.quantity_change) * movement.unit_cost;
        });

        // Sort by date (newest first)
        mockMovements.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
        
        setMovements(mockMovements);
      }
    } catch (err) {
      console.error('Error fetching stock movements:', err);
      setError('Failed to load stock movement history');
    } finally {
      setLoading(false);
    }
  };

  const formatTZS = (amount) => {
    return new Intl.NumberFormat('sw-TZ', {
      style: 'currency',
      currency: 'TZS',
      minimumFractionDigits: 0
    }).format(amount);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-GB', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getMovementIcon = (type) => {
    switch (type) {
      case 'stock_in': return 'fas fa-arrow-up text-green-600';
      case 'stock_out': return 'fas fa-arrow-down text-red-600';
      case 'adjustment': return 'fas fa-edit text-blue-600';
      case 'transfer': return 'fas fa-exchange-alt text-purple-600';
      default: return 'fas fa-box text-gray-600';
    }
  };

  const getMovementColor = (type) => {
    switch (type) {
      case 'stock_in': return 'bg-green-100 text-green-800';
      case 'stock_out': return 'bg-red-100 text-red-800';
      case 'adjustment': return 'bg-blue-100 text-blue-800';
      case 'transfer': return 'bg-purple-100 text-purple-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  // Filter movements
  const filteredMovements = movements.filter(movement => {
    const matchesSearch = !searchTerm || 
      movement.product_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      movement.brand.toLowerCase().includes(searchTerm.toLowerCase()) ||
      movement.reference_number.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesType = typeFilter === 'all' || movement.movement_type === typeFilter;
    
    let matchesDate = true;
    if (dateFilter !== 'all') {
      const movementDate = new Date(movement.created_at);
      const now = new Date();
      
      switch (dateFilter) {
        case 'today':
          matchesDate = movementDate.toDateString() === now.toDateString();
          break;
        case 'week':
          const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          matchesDate = movementDate >= weekAgo;
          break;
        case 'month':
          const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
          matchesDate = movementDate >= monthAgo;
          break;
      }
    }
    
    return matchesSearch && matchesType && matchesDate;
  });

  // Pagination
  const totalPages = Math.ceil(filteredMovements.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const paginatedMovements = filteredMovements.slice(startIndex, startIndex + itemsPerPage);

  // Calculate stats
  const totalMovements = movements.length;
  const stockInMovements = movements.filter(m => m.movement_type === 'stock_in').length;
  const stockOutMovements = movements.filter(m => m.movement_type === 'stock_out').length;
  const totalValue = movements.reduce((sum, m) => sum + m.total_value, 0);

  return (
    <AdminPageWrapper
      title="📊 Stock Movement History"
      description="Track all inventory movements and changes for Phone Point Dar"
      breadcrumbs={[
        { name: 'Dashboard', href: '/admin' },
        { name: 'Inventory', href: '/admin/inventory' },
        { name: 'Stock Movements', href: '/admin/inventory/movements' }
      ]}
      actions={
        <QuickActionButton
          icon="fas fa-download"
          variant="secondary"
          onClick={() => {
            // Export functionality would go here
            alert('Export functionality coming soon!');
          }}
        >
          Export Report
        </QuickActionButton>
      }
      loading={loading}
      error={error}
      onErrorClose={() => setError('')}
    >
      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        <AdminStatsCard
          title="Total Movements"
          value={totalMovements}
          icon="fas fa-list"
          color="blue"
        />
        <AdminStatsCard
          title="Stock In"
          value={stockInMovements}
          icon="fas fa-arrow-up"
          color="green"
        />
        <AdminStatsCard
          title="Stock Out"
          value={stockOutMovements}
          icon="fas fa-arrow-down"
          color="red"
        />
        <AdminStatsCard
          title="Total Value"
          value={formatTZS(totalValue)}
          icon="fas fa-money-bill-wave"
          color="yellow"
        />
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg shadow-md p-6 mb-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Search
            </label>
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="Search product, brand, or reference..."
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Movement Type
            </label>
            <select
              value={typeFilter}
              onChange={(e) => setTypeFilter(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">All Types</option>
              <option value="stock_in">Stock In</option>
              <option value="stock_out">Stock Out</option>
              <option value="adjustment">Adjustment</option>
              <option value="transfer">Transfer</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Date Range
            </label>
            <select
              value={dateFilter}
              onChange={(e) => setDateFilter(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">All Time</option>
              <option value="today">Today</option>
              <option value="week">Last 7 Days</option>
              <option value="month">Last 30 Days</option>
            </select>
          </div>

          <div className="flex items-end">
            <Button
              onClick={() => {
                setSearchTerm('');
                setTypeFilter('all');
                setDateFilter('all');
                setCurrentPage(1);
              }}
              variant="outline"
              className="w-full"
            >
              <i className="fas fa-undo mr-2"></i>
              Reset Filters
            </Button>
          </div>
        </div>
      </div>

      {/* Stock Movements Table */}
      <div className="bg-white rounded-lg shadow-md overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">
            Stock Movements ({filteredMovements.length})
          </h3>
        </div>
        
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Date/Time
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Product
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Type
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Quantity Change
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Value
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Reference
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Reason
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {paginatedMovements.map((movement) => (
                <tr key={movement._id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {formatDate(movement.created_at)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900">
                        {movement.product_name}
                      </div>
                      <div className="text-sm text-gray-500">
                        {movement.brand} • {movement.category}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <i className={`${getMovementIcon(movement.movement_type)} mr-2`}></i>
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getMovementColor(movement.movement_type)}`}>
                        {movement.movement_type.replace('_', ' ')}
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">
                      <span className={movement.quantity_change > 0 ? 'text-green-600' : 'text-red-600'}>
                        {movement.quantity_change > 0 ? '+' : ''}{movement.quantity_change}
                      </span>
                    </div>
                    <div className="text-xs text-gray-500">
                      {movement.quantity_before} → {movement.quantity_after}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {formatTZS(movement.total_value)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-mono text-gray-900">
                    {movement.reference_number}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{movement.reason}</div>
                    <div className="text-xs text-gray-500">{movement.created_by}</div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="px-6 py-4 border-t border-gray-200 flex items-center justify-between">
            <div className="text-sm text-gray-700">
              Showing {startIndex + 1} to {Math.min(startIndex + itemsPerPage, filteredMovements.length)} of {filteredMovements.length} movements
            </div>
            <div className="flex space-x-2">
              <Button
                onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                disabled={currentPage === 1}
                variant="outline"
                size="sm"
              >
                Previous
              </Button>
              
              {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                const page = i + 1;
                return (
                  <Button
                    key={page}
                    onClick={() => setCurrentPage(page)}
                    variant={currentPage === page ? "primary" : "outline"}
                    size="sm"
                  >
                    {page}
                  </Button>
                );
              })}
              
              <Button
                onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                disabled={currentPage === totalPages}
                variant="outline"
                size="sm"
              >
                Next
              </Button>
            </div>
          </div>
        )}
      </div>
    </AdminPageWrapper>
  );
};

export default StockMovementPage;
