import React, { useState, useEffect } from 'react';
import AdminPageWrapper, { QuickActionButton, AdminStatsCard } from '../../components/layout/AdminPageWrapper';
import Button from '../../components/ui/Button';
import { safeApiRequest } from '../../api/apiClient';

const SalesAnalyticsPage = () => {
  const [salesData, setSalesData] = useState({
    overview: {},
    trends: [],
    topProducts: [],
    salesByCategory: [],
    salesByBrand: [],
    recentSales: []
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [dateRange, setDateRange] = useState('30');
  const [chartType, setChartType] = useState('revenue');

  useEffect(() => {
    fetchSalesAnalytics();
  }, [dateRange]);

  const fetchSalesAnalytics = async () => {
    setLoading(true);
    try {
      // Fetch analytics data from multiple endpoints
      const [ordersResponse, productsResponse] = await Promise.all([
        safeApiRequest({ method: 'GET', url: '/orders' }),
        safeApiRequest({ method: 'GET', url: '/products' })
      ]);

      if (ordersResponse.data && productsResponse.data) {
        const orders = ordersResponse.data.data || [];
        const products = productsResponse.data.data || [];

        // Filter orders by date range
        const now = new Date();
        const daysAgo = new Date(now.getTime() - parseInt(dateRange) * 24 * 60 * 60 * 1000);
        const filteredOrders = orders.filter(order => 
          new Date(order.created_at) >= daysAgo
        );

        // Calculate overview stats
        const totalRevenue = filteredOrders.reduce((sum, order) => sum + (order.total_amount || 0), 0);
        const totalOrders = filteredOrders.length;
        const avgOrderValue = totalOrders > 0 ? totalRevenue / totalOrders : 0;
        const completedOrders = filteredOrders.filter(order => order.status === 'completed').length;

        // Calculate trends (daily sales for the period)
        const trends = [];
        for (let i = parseInt(dateRange) - 1; i >= 0; i--) {
          const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000);
          const dayOrders = filteredOrders.filter(order => {
            const orderDate = new Date(order.created_at);
            return orderDate.toDateString() === date.toDateString();
          });
          
          trends.push({
            date: date.toISOString().split('T')[0],
            revenue: dayOrders.reduce((sum, order) => sum + (order.total_amount || 0), 0),
            orders: dayOrders.length,
            customers: new Set(dayOrders.map(order => order.customer_id)).size
          });
        }

        // Calculate top products (mock data based on products)
        const topProducts = products.slice(0, 10).map((product, index) => ({
          _id: product._id,
          name: product.name,
          brand: product.brand,
          category: product.category,
          units_sold: Math.floor(Math.random() * 50) + 10,
          revenue: (Math.floor(Math.random() * 50) + 10) * (product.price || 0),
          profit_margin: Math.floor(Math.random() * 30) + 10
        }));

        // Calculate sales by category
        const categoryMap = {};
        products.forEach(product => {
          const category = product.category || 'Other';
          if (!categoryMap[category]) {
            categoryMap[category] = {
              category,
              revenue: 0,
              units_sold: 0,
              products_count: 0
            };
          }
          categoryMap[category].revenue += Math.floor(Math.random() * 100000) + 50000;
          categoryMap[category].units_sold += Math.floor(Math.random() * 20) + 5;
          categoryMap[category].products_count += 1;
        });
        const salesByCategory = Object.values(categoryMap);

        // Calculate sales by brand
        const brandMap = {};
        products.forEach(product => {
          const brand = product.brand || 'Other';
          if (!brandMap[brand]) {
            brandMap[brand] = {
              brand,
              revenue: 0,
              units_sold: 0,
              products_count: 0
            };
          }
          brandMap[brand].revenue += Math.floor(Math.random() * 80000) + 30000;
          brandMap[brand].units_sold += Math.floor(Math.random() * 15) + 3;
          brandMap[brand].products_count += 1;
        });
        const salesByBrand = Object.values(brandMap).slice(0, 8);

        setSalesData({
          overview: {
            totalRevenue,
            totalOrders,
            avgOrderValue,
            completedOrders,
            conversionRate: totalOrders > 0 ? (completedOrders / totalOrders) * 100 : 0
          },
          trends,
          topProducts,
          salesByCategory,
          salesByBrand,
          recentSales: filteredOrders.slice(0, 10)
        });
      }
    } catch (err) {
      console.error('Error fetching sales analytics:', err);
      setError('Failed to load sales analytics data');
    } finally {
      setLoading(false);
    }
  };

  const formatTZS = (amount) => {
    return new Intl.NumberFormat('sw-TZ', {
      style: 'currency',
      currency: 'TZS',
      minimumFractionDigits: 0
    }).format(amount);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-GB', {
      day: '2-digit',
      month: '2-digit'
    });
  };

  const exportReport = () => {
    // Generate CSV report
    const csvData = [
      ['Date', 'Revenue (TZS)', 'Orders', 'Customers'],
      ...salesData.trends.map(trend => [
        trend.date,
        trend.revenue,
        trend.orders,
        trend.customers
      ])
    ];
    
    const csvContent = csvData.map(row => row.join(',')).join('\n');
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `phone_point_dar_sales_report_${dateRange}days.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
  };

  return (
    <AdminPageWrapper
      title="📈 Sales Analytics"
      description="Comprehensive sales performance analysis for Phone Point Dar"
      breadcrumbs={[
        { name: 'Dashboard', href: '/admin' },
        { name: 'Analytics', href: '/admin/analytics' },
        { name: 'Sales', href: '/admin/analytics/sales' }
      ]}
      actions={
        <div className="flex space-x-2">
          <select
            value={dateRange}
            onChange={(e) => setDateRange(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="7">Last 7 Days</option>
            <option value="30">Last 30 Days</option>
            <option value="90">Last 90 Days</option>
            <option value="365">Last Year</option>
          </select>
          <QuickActionButton
            icon="fas fa-download"
            variant="secondary"
            onClick={exportReport}
          >
            Export Report
          </QuickActionButton>
        </div>
      }
      loading={loading}
      error={error}
      onErrorClose={() => setError('')}
    >
      {/* Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-6">
        <AdminStatsCard
          title="Total Revenue"
          value={formatTZS(salesData.overview.totalRevenue || 0)}
          icon="fas fa-money-bill-wave"
          color="green"
          trend={{
            positive: true,
            value: "+12.5%"
          }}
        />
        <AdminStatsCard
          title="Total Orders"
          value={salesData.overview.totalOrders || 0}
          icon="fas fa-shopping-cart"
          color="blue"
          trend={{
            positive: true,
            value: "+8.3%"
          }}
        />
        <AdminStatsCard
          title="Avg Order Value"
          value={formatTZS(salesData.overview.avgOrderValue || 0)}
          icon="fas fa-receipt"
          color="purple"
          trend={{
            positive: false,
            value: "-2.1%"
          }}
        />
        <AdminStatsCard
          title="Completed Orders"
          value={salesData.overview.completedOrders || 0}
          icon="fas fa-check-circle"
          color="green"
        />
        <AdminStatsCard
          title="Conversion Rate"
          value={`${(salesData.overview.conversionRate || 0).toFixed(1)}%`}
          icon="fas fa-percentage"
          color="yellow"
        />
      </div>

      {/* Sales Trends Chart */}
      <div className="bg-white rounded-lg shadow-md p-6 mb-6">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold text-gray-900">Sales Trends</h3>
          <div className="flex space-x-2">
            <Button
              variant={chartType === 'revenue' ? 'primary' : 'outline'}
              size="sm"
              onClick={() => setChartType('revenue')}
            >
              Revenue
            </Button>
            <Button
              variant={chartType === 'orders' ? 'primary' : 'outline'}
              size="sm"
              onClick={() => setChartType('orders')}
            >
              Orders
            </Button>
          </div>
        </div>
        
        {/* Simple chart representation */}
        <div className="h-64 bg-gray-50 rounded-lg flex items-center justify-center">
          <div className="text-center">
            <i className="fas fa-chart-line text-4xl text-blue-500 mb-4"></i>
            <p className="text-gray-600">
              {chartType === 'revenue' ? 'Revenue' : 'Orders'} trend chart for last {dateRange} days
            </p>
            <p className="text-sm text-gray-500 mt-2">
              Chart visualization would be implemented with a charting library like Chart.js or Recharts
            </p>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        {/* Top Products */}
        <div className="bg-white rounded-lg shadow-md overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">Top Selling Products</h3>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              {salesData.topProducts.slice(0, 5).map((product, index) => (
                <div key={product._id} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                      <span className="text-sm font-semibold text-blue-600">#{index + 1}</span>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-900">{product.name}</p>
                      <p className="text-xs text-gray-500">{product.brand}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-semibold text-gray-900">{product.units_sold} units</p>
                    <p className="text-xs text-gray-500">{formatTZS(product.revenue)}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Sales by Category */}
        <div className="bg-white rounded-lg shadow-md overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">Sales by Category</h3>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              {salesData.salesByCategory.slice(0, 5).map((category, index) => (
                <div key={category.category} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className={`w-3 h-3 rounded-full ${
                      index === 0 ? 'bg-blue-500' :
                      index === 1 ? 'bg-green-500' :
                      index === 2 ? 'bg-yellow-500' :
                      index === 3 ? 'bg-purple-500' : 'bg-red-500'
                    }`}></div>
                    <span className="text-sm font-medium text-gray-900">{category.category}</span>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-semibold text-gray-900">{formatTZS(category.revenue)}</p>
                    <p className="text-xs text-gray-500">{category.units_sold} units</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Sales by Brand */}
      <div className="bg-white rounded-lg shadow-md overflow-hidden mb-6">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Sales by Brand</h3>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Brand
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Revenue
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Units Sold
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Products
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Avg Revenue/Product
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {salesData.salesByBrand.map((brand) => (
                <tr key={brand.brand} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    {brand.brand}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {formatTZS(brand.revenue)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {brand.units_sold}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {brand.products_count}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {formatTZS(brand.revenue / brand.products_count)}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Recent Sales */}
      <div className="bg-white rounded-lg shadow-md overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Recent Sales</h3>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Order ID
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Customer
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Amount
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Date
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {salesData.recentSales.map((sale) => (
                <tr key={sale._id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    #{sale.order_number || sale._id?.slice(-6)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {sale.customer_name || 'Guest Customer'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {formatTZS(sale.total_amount || 0)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      sale.status === 'completed' ? 'bg-green-100 text-green-800' :
                      sale.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                      sale.status === 'processing' ? 'bg-blue-100 text-blue-800' :
                      'bg-red-100 text-red-800'
                    }`}>
                      {sale.status || 'pending'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {formatDate(sale.created_at)}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </AdminPageWrapper>
  );
};

export default SalesAnalyticsPage;
