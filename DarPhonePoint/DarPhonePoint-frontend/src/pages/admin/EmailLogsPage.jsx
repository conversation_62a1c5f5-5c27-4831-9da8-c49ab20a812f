import React, { useState, useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { apiService } from '../../services/api';
import { formatDate } from '../../utils/dateUtils';
import Button from '../../components/ui/Button';
import Card from '../../components/ui/Card';
import LoadingState from '../../components/ui/LoadingState';
import ErrorState from '../../components/ui/ErrorState';
import Modal from '../../components/ui/Modal';
import Pagination from '../../components/ui/Pagination';

/**
 * Email Logs Page for Phone Point Dar
 * View and manage email delivery logs and history
 */
const EmailLogsPage = () => {
  const { user } = useAuth();
  const [logs, setLogs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [selectedLog, setSelectedLog] = useState(null);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [filters, setFilters] = useState({
    status: 'all',
    template: 'all',
    dateRange: '7d',
    search: ''
  });
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalPages: 1,
    totalItems: 0,
    itemsPerPage: 20
  });

  useEffect(() => {
    fetchLogs();
  }, [filters, pagination.currentPage]);

  const fetchLogs = async () => {
    try {
      setLoading(true);
      // Mock data for demonstration
      const mockLogs = Array.from({ length: 50 }, (_, index) => ({
        _id: `log_${index + 1}`,
        recipient: `customer${index + 1}@example.com`,
        subject: getRandomSubject(),
        template: getRandomTemplate(),
        status: getRandomStatus(),
        sentAt: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString(),
        deliveredAt: Math.random() > 0.1 ? new Date(Date.now() - Math.random() * 6 * 24 * 60 * 60 * 1000).toISOString() : null,
        openedAt: Math.random() > 0.4 ? new Date(Date.now() - Math.random() * 5 * 24 * 60 * 60 * 1000).toISOString() : null,
        clickedAt: Math.random() > 0.8 ? new Date(Date.now() - Math.random() * 4 * 24 * 60 * 60 * 1000).toISOString() : null,
        errorMessage: getRandomStatus() === 'failed' ? 'SMTP connection failed' : null,
        messageId: `msg_${Date.now()}_${index}`,
        trackingId: `track_${Date.now()}_${index}`
      }));

      // Apply filters
      let filteredLogs = mockLogs;
      if (filters.status !== 'all') {
        filteredLogs = filteredLogs.filter(log => log.status === filters.status);
      }
      if (filters.search) {
        filteredLogs = filteredLogs.filter(log => 
          log.recipient.toLowerCase().includes(filters.search.toLowerCase()) ||
          log.subject.toLowerCase().includes(filters.search.toLowerCase())
        );
      }

      // Pagination
      const startIndex = (pagination.currentPage - 1) * pagination.itemsPerPage;
      const endIndex = startIndex + pagination.itemsPerPage;
      const paginatedLogs = filteredLogs.slice(startIndex, endIndex);

      setLogs(paginatedLogs);
      setPagination(prev => ({
        ...prev,
        totalItems: filteredLogs.length,
        totalPages: Math.ceil(filteredLogs.length / pagination.itemsPerPage)
      }));
    } catch (error) {
      console.error('Error fetching logs:', error);
      setError('Failed to load email logs');
    } finally {
      setLoading(false);
    }
  };

  const getRandomSubject = () => {
    const subjects = [
      'Welcome to Phone Point Dar',
      'Order Confirmation #PPD-001',
      'Payment Receipt - Thank You',
      'We Miss You - Special Offer',
      'New Arrivals This Week',
      'Your Order Has Shipped'
    ];
    return subjects[Math.floor(Math.random() * subjects.length)];
  };

  const getRandomTemplate = () => {
    const templates = ['welcome', 'order-confirmation', 'payment-receipt', 're-engagement', 'newsletter', 'shipping'];
    return templates[Math.floor(Math.random() * templates.length)];
  };

  const getRandomStatus = () => {
    const statuses = ['delivered', 'delivered', 'delivered', 'delivered', 'pending', 'failed'];
    return statuses[Math.floor(Math.random() * statuses.length)];
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'delivered':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      case 'bounced':
        return 'bg-orange-100 text-orange-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const handleViewDetails = (log) => {
    setSelectedLog(log);
    setShowDetailModal(true);
  };

  const handleFilterChange = (field, value) => {
    setFilters(prev => ({ ...prev, [field]: value }));
    setPagination(prev => ({ ...prev, currentPage: 1 }));
  };

  const handlePageChange = (page) => {
    setPagination(prev => ({ ...prev, currentPage: page }));
  };

  if (loading) {
    return <LoadingState message="Loading email logs..." />;
  }

  if (error) {
    return <ErrorState message={error} onRetry={fetchLogs} />;
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-600 to-blue-800 rounded-lg p-6 text-white">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
          <div>
            <h1 className="text-2xl font-bold mb-2">📋 Email Logs</h1>
            <p className="text-blue-100">Track and monitor all email deliveries</p>
          </div>
          <div className="flex flex-wrap gap-2 mt-4 md:mt-0">
            <Button className="bg-white text-blue-600 hover:bg-gray-100">
              <i className="fas fa-download mr-2"></i>Export Logs
            </Button>
            <Button className="bg-blue-500 hover:bg-blue-400 text-white">
              <i className="fas fa-sync-alt mr-2"></i>Refresh
            </Button>
          </div>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Status</label>
              <select
                value={filters.status}
                onChange={(e) => handleFilterChange('status', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">All Status</option>
                <option value="delivered">Delivered</option>
                <option value="pending">Pending</option>
                <option value="failed">Failed</option>
                <option value="bounced">Bounced</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Template</label>
              <select
                value={filters.template}
                onChange={(e) => handleFilterChange('template', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">All Templates</option>
                <option value="welcome">Welcome</option>
                <option value="order-confirmation">Order Confirmation</option>
                <option value="payment-receipt">Payment Receipt</option>
                <option value="re-engagement">Re-engagement</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Date Range</label>
              <select
                value={filters.dateRange}
                onChange={(e) => handleFilterChange('dateRange', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="1d">Last 24 hours</option>
                <option value="7d">Last 7 days</option>
                <option value="30d">Last 30 days</option>
                <option value="90d">Last 90 days</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Search</label>
              <input
                type="text"
                value={filters.search}
                onChange={(e) => handleFilterChange('search', e.target.value)}
                placeholder="Search recipient or subject..."
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>
        </div>
      </Card>

      {/* Email Logs Table */}
      <Card>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Recipient
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Subject
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Sent At
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {Array.isArray(logs) && logs.map((log) => (
                <tr key={log._id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">{log.recipient}</div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm text-gray-900">{log.subject}</div>
                    <div className="text-sm text-gray-500">Template: {log.template}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(log.status)}`}>
                      {log.status}
                    </span>
                    {log.openedAt && (
                      <div className="text-xs text-green-600 mt-1">
                        <i className="fas fa-envelope-open mr-1"></i>Opened
                      </div>
                    )}
                    {log.clickedAt && (
                      <div className="text-xs text-blue-600 mt-1">
                        <i className="fas fa-mouse-pointer mr-1"></i>Clicked
                      </div>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {formatDate(log.sentAt)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleViewDetails(log)}
                    >
                      <i className="fas fa-eye mr-1"></i>Details
                    </Button>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    {log.status === 'failed' && (
                      <Button
                        size="sm"
                        variant="outline"
                        className="text-blue-600 border-blue-300 hover:bg-blue-50"
                      >
                        <i className="fas fa-redo mr-1"></i>Retry
                      </Button>
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        <div className="px-6 py-4 border-t border-gray-200">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-500">
              Showing {((pagination.currentPage - 1) * pagination.itemsPerPage) + 1} to{' '}
              {Math.min(pagination.currentPage * pagination.itemsPerPage, pagination.totalItems)} of{' '}
              {pagination.totalItems} results
            </div>
            <Pagination
              currentPage={pagination.currentPage}
              totalPages={pagination.totalPages}
              onPageChange={handlePageChange}
            />
          </div>
        </div>
      </Card>

      {/* Detail Modal */}
      {showDetailModal && selectedLog && (
        <Modal
          isOpen={showDetailModal}
          onClose={() => setShowDetailModal(false)}
          title="Email Log Details"
          size="lg"
        >
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Recipient:</label>
                <p className="text-sm bg-gray-50 p-2 rounded">{selectedLog.recipient}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Status:</label>
                <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(selectedLog.status)}`}>
                  {selectedLog.status}
                </span>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Template:</label>
                <p className="text-sm bg-gray-50 p-2 rounded">{selectedLog.template}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Message ID:</label>
                <p className="text-sm bg-gray-50 p-2 rounded font-mono">{selectedLog.messageId}</p>
              </div>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Subject:</label>
              <p className="text-sm bg-gray-50 p-2 rounded">{selectedLog.subject}</p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Sent At:</label>
                <p className="text-sm bg-gray-50 p-2 rounded">{formatDate(selectedLog.sentAt)}</p>
              </div>
              {selectedLog.deliveredAt && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Delivered At:</label>
                  <p className="text-sm bg-gray-50 p-2 rounded">{formatDate(selectedLog.deliveredAt)}</p>
                </div>
              )}
              {selectedLog.openedAt && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Opened At:</label>
                  <p className="text-sm bg-gray-50 p-2 rounded">{formatDate(selectedLog.openedAt)}</p>
                </div>
              )}
              {selectedLog.clickedAt && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Clicked At:</label>
                  <p className="text-sm bg-gray-50 p-2 rounded">{formatDate(selectedLog.clickedAt)}</p>
                </div>
              )}
            </div>

            {selectedLog.errorMessage && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Error Message:</label>
                <p className="text-sm bg-red-50 text-red-800 p-2 rounded">{selectedLog.errorMessage}</p>
              </div>
            )}

            <div className="flex justify-end space-x-3">
              <Button variant="outline" onClick={() => setShowDetailModal(false)}>
                Close
              </Button>
              {selectedLog.status === 'failed' && (
                <Button>
                  <i className="fas fa-redo mr-2"></i>Retry Send
                </Button>
              )}
            </div>
          </div>
        </Modal>
      )}
    </div>
  );
};

export default EmailLogsPage;
