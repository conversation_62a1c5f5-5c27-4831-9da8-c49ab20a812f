import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { apiService } from '../../services/api';
import { formatDate } from '../../utils/dateUtils';
import Button from '../../components/ui/Button';
import Card from '../../components/ui/Card';
import Alert from '../../components/ui/Alert';
import LoadingState from '../../components/ui/LoadingState';
import Modal from '../../components/ui/Modal';
import FormField from '../../components/ui/FormField';

/**
 * Email Templates Management Page for Phone Point Dar
 * Manage email templates used throughout the system
 */
const EmailTemplatesPage = () => {
  const { user } = useAuth();
  const [templates, setTemplates] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [showPreviewModal, setShowPreviewModal] = useState(false);
  const [previewTemplate, setPreviewTemplate] = useState(null);
  const [showEditModal, setShowEditModal] = useState(false);
  const [editingTemplate, setEditingTemplate] = useState(null);

  useEffect(() => {
    fetchTemplates();
  }, []);

  const fetchTemplates = async () => {
    try {
      setLoading(true);
      // For now, we'll show the built-in templates
      const builtInTemplates = [
        {
          _id: 'email-verification',
          name: 'Email Verification',
          description: 'Template for email address verification',
          subject: 'Verify Your Email - Phone Point Dar',
          type: 'system',
          isActive: true,
          lastModified: new Date().toISOString(),
          usage: 'Account verification emails'
        },
        {
          _id: 'order-confirmation',
          name: 'Order Confirmation',
          description: 'Template for order confirmation emails',
          subject: 'Order Confirmed - Phone Point Dar',
          type: 'transactional',
          isActive: true,
          lastModified: new Date().toISOString(),
          usage: 'Order confirmation emails'
        },
        {
          _id: 'payment-receipt',
          name: 'Payment Receipt',
          description: 'Template for payment confirmation emails',
          subject: 'Payment Receipt - Phone Point Dar',
          type: 'transactional',
          isActive: true,
          lastModified: new Date().toISOString(),
          usage: 'Payment confirmation emails'
        },
        {
          _id: 'welcome',
          name: 'Welcome Email',
          description: 'Template for welcoming new customers',
          subject: 'Welcome to Phone Point Dar',
          type: 'marketing',
          isActive: true,
          lastModified: new Date().toISOString(),
          usage: 'New customer welcome emails'
        },
        {
          _id: 'password-reset',
          name: 'Password Reset',
          description: 'Template for password reset emails',
          subject: 'Reset Your Password - Phone Point Dar',
          type: 'system',
          isActive: true,
          lastModified: new Date().toISOString(),
          usage: 'Password reset emails'
        },
        {
          _id: 're-engagement',
          name: 'Re-engagement',
          description: 'Template for re-engaging inactive customers',
          subject: 'We Miss You - Phone Point Dar',
          type: 'marketing',
          isActive: true,
          lastModified: new Date().toISOString(),
          usage: 'Customer re-engagement campaigns'
        }
      ];
      
      setTemplates(builtInTemplates);
    } catch (error) {
      console.error('Error fetching templates:', error);
      setError('Failed to load email templates');
    } finally {
      setLoading(false);
    }
  };

  const handlePreview = async (template) => {
    setPreviewTemplate(template);
    setShowPreviewModal(true);
  };

  const handleEdit = (template) => {
    setEditingTemplate({ ...template });
    setShowEditModal(true);
  };

  const handleToggleActive = async (templateId, currentStatus) => {
    try {
      // In a real implementation, this would call the API
      setTemplates(prev => prev.map(template => 
        template._id === templateId 
          ? { ...template, isActive: !currentStatus }
          : template
      ));
      setSuccess(`Template ${!currentStatus ? 'activated' : 'deactivated'} successfully`);
    } catch (error) {
      setError('Failed to update template status');
    }
  };

  const getTemplateTypeColor = (type) => {
    switch (type) {
      case 'system':
        return 'bg-blue-100 text-blue-800';
      case 'transactional':
        return 'bg-green-100 text-green-800';
      case 'marketing':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return <LoadingState message="Loading email templates..." />;
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-600 to-blue-800 rounded-lg p-6 text-white">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
          <div>
            <h1 className="text-2xl font-bold mb-2">📄 Email Templates</h1>
            <p className="text-blue-100">Manage email templates for Phone Point Dar</p>
          </div>
          <div className="flex flex-wrap gap-2 mt-4 md:mt-0">
            <Button className="bg-white text-blue-600 hover:bg-gray-100">
              <i className="fas fa-plus mr-2"></i>Create Template
            </Button>
            <Button className="bg-blue-500 hover:bg-blue-400 text-white">
              <i className="fas fa-download mr-2"></i>Export Templates
            </Button>
          </div>
        </div>
      </div>

      {error && (
        <Alert
          type="error"
          message={error}
          onClose={() => setError('')}
        />
      )}

      {success && (
        <Alert
          type="success"
          message={success}
          onClose={() => setSuccess('')}
        />
      )}

      {/* Templates Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {templates.map((template) => (
          <Card key={template._id} className="hover:shadow-lg transition-shadow">
            <div className="p-6">
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <h3 className="text-lg font-semibold text-gray-900 mb-1">
                    {template.name}
                  </h3>
                  <p className="text-sm text-gray-600 mb-2">
                    {template.description}
                  </p>
                </div>
                <div className="flex items-center space-x-2">
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getTemplateTypeColor(template.type)}`}>
                    {template.type}
                  </span>
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                    template.isActive ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                  }`}>
                    {template.isActive ? 'Active' : 'Inactive'}
                  </span>
                </div>
              </div>

              <div className="mb-4">
                <p className="text-sm font-medium text-gray-700 mb-1">Subject:</p>
                <p className="text-sm text-gray-600">{template.subject}</p>
              </div>

              <div className="mb-4">
                <p className="text-sm font-medium text-gray-700 mb-1">Usage:</p>
                <p className="text-sm text-gray-600">{template.usage}</p>
              </div>

              <div className="mb-4">
                <p className="text-xs text-gray-500">
                  Last modified: {formatDate(template.lastModified)}
                </p>
              </div>

              <div className="flex space-x-2">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => handlePreview(template)}
                  className="flex-1"
                >
                  <i className="fas fa-eye mr-1"></i>Preview
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => handleEdit(template)}
                  className="flex-1"
                >
                  <i className="fas fa-edit mr-1"></i>Edit
                </Button>
                <Button
                  size="sm"
                  variant={template.isActive ? "outline" : "primary"}
                  onClick={() => handleToggleActive(template._id, template.isActive)}
                  className={template.isActive ? "text-red-600 border-red-300 hover:bg-red-50" : ""}
                >
                  <i className={`fas ${template.isActive ? 'fa-pause' : 'fa-play'} mr-1`}></i>
                  {template.isActive ? 'Disable' : 'Enable'}
                </Button>
              </div>
            </div>
          </Card>
        ))}
      </div>

      {/* Template Statistics */}
      <Card>
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold">Template Statistics</h3>
        </div>
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="text-center">
              <div className="text-3xl font-bold text-blue-600 mb-2">{templates.length}</div>
              <div className="text-sm text-gray-500">Total Templates</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-green-600 mb-2">
                {templates.filter(t => t.isActive).length}
              </div>
              <div className="text-sm text-gray-500">Active Templates</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-purple-600 mb-2">
                {templates.filter(t => t.type === 'marketing').length}
              </div>
              <div className="text-sm text-gray-500">Marketing Templates</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-orange-600 mb-2">
                {templates.filter(t => t.type === 'transactional').length}
              </div>
              <div className="text-sm text-gray-500">Transactional Templates</div>
            </div>
          </div>
        </div>
      </Card>

      {/* Preview Modal */}
      {showPreviewModal && previewTemplate && (
        <Modal
          isOpen={showPreviewModal}
          onClose={() => setShowPreviewModal(false)}
          title={`Preview: ${previewTemplate.name}`}
          size="lg"
        >
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Subject:</label>
              <p className="text-sm bg-gray-50 p-2 rounded">{previewTemplate.subject}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Template Type:</label>
              <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getTemplateTypeColor(previewTemplate.type)}`}>
                {previewTemplate.type}
              </span>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Description:</label>
              <p className="text-sm text-gray-600">{previewTemplate.description}</p>
            </div>
            <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
              <p className="text-sm text-blue-800">
                <i className="fas fa-info-circle mr-2"></i>
                This template is currently being used for: {previewTemplate.usage}
              </p>
            </div>
          </div>
        </Modal>
      )}

      {/* Edit Modal */}
      {showEditModal && editingTemplate && (
        <Modal
          isOpen={showEditModal}
          onClose={() => setShowEditModal(false)}
          title={`Edit Template: ${editingTemplate.name}`}
          size="lg"
        >
          <div className="space-y-4">
            <FormField
              label="Template Name"
              name="name"
              type="text"
              value={editingTemplate.name}
              onChange={(e) => setEditingTemplate(prev => ({ ...prev, name: e.target.value }))}
            />
            <FormField
              label="Description"
              name="description"
              type="text"
              value={editingTemplate.description}
              onChange={(e) => setEditingTemplate(prev => ({ ...prev, description: e.target.value }))}
            />
            <FormField
              label="Subject"
              name="subject"
              type="text"
              value={editingTemplate.subject}
              onChange={(e) => setEditingTemplate(prev => ({ ...prev, subject: e.target.value }))}
            />
            <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
              <p className="text-sm text-yellow-800">
                <i className="fas fa-exclamation-triangle mr-2"></i>
                Template content editing requires direct file system access. Contact your system administrator to modify template content.
              </p>
            </div>
            <div className="flex justify-end space-x-3">
              <Button variant="outline" onClick={() => setShowEditModal(false)}>
                Cancel
              </Button>
              <Button onClick={() => setShowEditModal(false)}>
                Save Changes
              </Button>
            </div>
          </div>
        </Modal>
      )}
    </div>
  );
};

export default EmailTemplatesPage;
