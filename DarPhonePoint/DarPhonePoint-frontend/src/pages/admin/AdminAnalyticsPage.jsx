import React, { useState, useEffect } from 'react';
import { safeApiRequest } from '../../api/apiClient';
import PageHeader from '../../components/ui/PageHeader';
import LoadingState from '../../components/ui/LoadingState';
import Alert from '../../components/ui/Alert';

const PhoneSalesAnalyticsPage = () => {
  const [phoneAnalytics, setPhoneAnalytics] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [period, setPeriod] = useState('30d');
  const [activeTab, setActiveTab] = useState('sales');

  useEffect(() => {
    fetchPhoneAnalytics();
  }, [period]);

  const fetchPhoneAnalytics = async () => {
    setLoading(true);
    try {
      const [dashboardResponse, statsResponse, overviewResponse, categoryStatsResponse, topProductsResponse] = await Promise.all([
        safeApiRequest({
          method: 'GET',
          url: `/analytics/dashboard-stats?timeRange=${period}`
        }),
        safeApiRequest({
          method: 'GET',
          url: `/analytics/dashboard`
        }),
        safeApiRequest({
          method: 'GET',
          url: `/admin/dashboard/overview`
        }),
        safeApiRequest({
          method: 'GET',
          url: `/admin/dashboard/category-stats`
        }),
        safeApiRequest({
          method: 'GET',
          url: `/admin/dashboard/top-products`
        })
      ]);

      setPhoneAnalytics({
        sales: {
          totalRevenue: overviewResponse.data?.data?.totalRevenue || 0,
          totalOrders: overviewResponse.data?.data?.totalOrders || 0,
          todayRevenue: overviewResponse.data?.data?.todayRevenue || 0,
          todayOrders: overviewResponse.data?.data?.todayOrders || 0,
          pendingOrders: overviewResponse.data?.data?.pendingOrders || 0
        },
        orders: overviewResponse.data?.data || {},
        inventory: {
          totalProducts: overviewResponse.data?.data?.totalProducts || 0,
          lowStockItems: overviewResponse.data?.data?.lowStockItems || 0
        },
        brands: categoryStatsResponse.data?.data || [],
        warranty: {
          totalClaims: 0,
          pendingClaims: 0,
          resolvedClaims: 0
        },
        topProducts: topProductsResponse.data?.data || [],
        dashboardStats: dashboardResponse.data?.data || {},
        generalStats: statsResponse.data?.data || {}
      });
    } catch (err) {
      console.error('Error fetching phone analytics:', err);
      setError('Failed to load phone sales analytics data');
    } finally {
      setLoading(false);
    }
  };

  const formatTZS = (price) => {
    return new Intl.NumberFormat('sw-TZ', {
      style: 'currency',
      currency: 'TZS',
      minimumFractionDigits: 0
    }).format(price || 0);
  };

  const formatPercentage = (value) => {
    return `${(value || 0).toFixed(1)}%`;
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <LoadingState text="Loading phone sales analytics..." size="lg" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <PageHeader
        title="📱 Phone Point Dar - Sales Analytics"
        description="Monitor phone sales performance and Tanzania market insights"
        breadcrumbs={[
          { name: 'Dashboard', href: '/admin' },
          { name: 'Phone Sales Analytics', href: '/admin/analytics' }
        ]}
        actions={
          <div className="flex space-x-3">
            <select
              value={period}
              onChange={(e) => setPeriod(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="7d">Last 7 Days</option>
              <option value="30d">Last 30 Days</option>
              <option value="90d">Last 90 Days</option>
              <option value="1y">Last Year</option>
            </select>
          </div>
        }
      />

      {error && (
        <Alert
          type="error"
          message={error}
          onClose={() => setError('')}
        />
      )}

      {/* Tab Navigation */}
      <div className="bg-white rounded-lg shadow-md">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            {[
              { id: 'sales', label: 'Phone Sales Performance', icon: 'fas fa-mobile-alt' },
              { id: 'brands', label: 'Brand Analytics', icon: 'fas fa-tags' },
              { id: 'inventory', label: 'Phone Inventory', icon: 'fas fa-warehouse' },
              { id: 'warranty', label: 'Warranty & Returns', icon: 'fas fa-shield-alt' },
              { id: 'customers', label: 'Customer Insights', icon: 'fas fa-users' }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <i className={`${tab.icon} mr-2`}></i>
                {tab.label}
              </button>
            ))}
          </nav>
        </div>

        <div className="p-6">
          {/* Phone Sales Performance Tab */}
          {activeTab === 'sales' && phoneAnalytics?.sales && (
            <div className="space-y-6">
              {/* Key Phone Sales Metrics */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div className="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg p-6 text-white">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-blue-100">Phone Sales Revenue (TZS)</p>
                      <p className="text-3xl font-bold">{formatTZS(phoneAnalytics.sales.totalRevenue)}</p>
                    </div>
                    <i className="fas fa-mobile-alt text-3xl text-blue-200"></i>
                  </div>
                </div>

                <div className="bg-gradient-to-r from-green-500 to-green-600 rounded-lg p-6 text-white">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-green-100">Phones Sold</p>
                      <p className="text-3xl font-bold">{phoneAnalytics.sales.totalPhonesSold || 0}</p>
                    </div>
                    <i className="fas fa-shopping-cart text-3xl text-green-200"></i>
                  </div>
                </div>

                <div className="bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg p-6 text-white">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-purple-100">Avg Phone Price (TZS)</p>
                      <p className="text-3xl font-bold">{formatTZS(phoneAnalytics.sales.averagePhonePrice)}</p>
                    </div>
                    <i className="fas fa-money-bill-wave text-3xl text-purple-200"></i>
                  </div>
                </div>

                <div className="bg-gradient-to-r from-orange-500 to-orange-600 rounded-lg p-6 text-white">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-orange-100">IMEI Tracked</p>
                      <p className="text-3xl font-bold">{phoneAnalytics.inventory.imeiTracked || 0}</p>
                    </div>
                    <i className="fas fa-barcode text-3xl text-orange-200"></i>
                  </div>
                </div>
              </div>

              {/* Top Selling Phones */}
              <div className="bg-gray-50 rounded-lg p-6">
                <h3 className="text-lg font-semibold mb-4 flex items-center">
                  <i className="fas fa-mobile-alt mr-2 text-blue-600"></i>
                  Top Selling Phones in Tanzania
                </h3>
                <div className="space-y-3">
                  {phoneAnalytics.sales.topPhones?.slice(0, 5).map((phone, index) => (
                    <div key={phone._id} className="flex items-center justify-between bg-white p-4 rounded-lg">
                      <div className="flex items-center space-x-3">
                        <span className="flex items-center justify-center w-8 h-8 bg-blue-100 text-blue-600 rounded-full text-sm font-medium">
                          {index + 1}
                        </span>
                        <div>
                          <p className="font-medium">{phone.name}</p>
                          <p className="text-sm text-gray-500">{phone.brand} • {phone.storage} • {phone.color}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-medium">{phone.totalSold} sold</p>
                        <p className="text-sm text-gray-500">{formatTZS(phone.revenue)}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* Brand Analytics Tab */}
          {activeTab === 'brands' && phoneAnalytics?.brands && (
            <div className="space-y-6">
              {/* Brand Performance */}
              <div className="bg-gray-50 rounded-lg p-6">
                <h3 className="text-lg font-semibold mb-4 flex items-center">
                  <i className="fas fa-tags mr-2 text-blue-600"></i>
                  Phone Brand Performance in Tanzania
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {phoneAnalytics.brands.topBrands?.map((brand, index) => (
                    <div key={brand.name} className="bg-white rounded-lg p-4 border">
                      <div className="flex items-center justify-between mb-3">
                        <h4 className="font-medium text-gray-900">{brand.name}</h4>
                        <span className="text-sm text-gray-500">#{index + 1}</span>
                      </div>
                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <span className="text-sm text-gray-600">Units Sold:</span>
                          <span className="font-medium">{brand.unitsSold}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm text-gray-600">Revenue:</span>
                          <span className="font-medium">{formatTZS(brand.revenue)}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm text-gray-600">Market Share:</span>
                          <span className="font-medium">{formatPercentage(brand.marketShare)}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm text-gray-600">Avg Price:</span>
                          <span className="font-medium">{formatTZS(brand.averagePrice)}</span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Tanzania Market Insights */}
              <div className="bg-white rounded-lg p-6 border">
                <h3 className="text-lg font-semibold mb-4 flex items-center">
                  <i className="fas fa-globe-africa mr-2 text-green-600"></i>
                  Tanzania Mobile Market Insights
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="font-medium mb-3">Popular Price Ranges (TZS)</h4>
                    <div className="space-y-2">
                      {phoneAnalytics.brands.priceRanges?.map((range) => (
                        <div key={range.range} className="flex justify-between items-center">
                          <span className="text-sm">{range.range}</span>
                          <div className="flex items-center space-x-2">
                            <div className="w-20 bg-gray-200 rounded-full h-2">
                              <div
                                className="bg-blue-600 h-2 rounded-full"
                                style={{ width: `${range.percentage}%` }}
                              ></div>
                            </div>
                            <span className="text-sm font-medium">{formatPercentage(range.percentage)}</span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                  <div>
                    <h4 className="font-medium mb-3">Popular Features</h4>
                    <div className="space-y-2">
                      {phoneAnalytics.brands.popularFeatures?.map((feature) => (
                        <div key={feature.name} className="flex justify-between items-center">
                          <span className="text-sm">{feature.name}</span>
                          <span className="text-sm font-medium">{formatPercentage(feature.demand)}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Phone Inventory Tab */}
          {activeTab === 'inventory' && phoneAnalytics?.inventory && (
            <div className="space-y-6">
              {/* Phone Inventory Overview */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div className="bg-white border border-gray-200 rounded-lg p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-gray-500">Total Phones in Stock</p>
                      <p className="text-3xl font-bold text-gray-900">{phoneAnalytics.inventory.totalPhones || 0}</p>
                    </div>
                    <i className="fas fa-mobile-alt text-3xl text-blue-500"></i>
                  </div>
                </div>

                <div className="bg-white border border-gray-200 rounded-lg p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-gray-500">IMEI Tracked</p>
                      <p className="text-3xl font-bold text-green-600">{phoneAnalytics.inventory.imeiTracked || 0}</p>
                    </div>
                    <i className="fas fa-barcode text-3xl text-green-500"></i>
                  </div>
                </div>

                <div className="bg-white border border-gray-200 rounded-lg p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-gray-500">Low Stock Phones</p>
                      <p className="text-3xl font-bold text-red-600">{phoneAnalytics.inventory.lowStockPhones || 0}</p>
                    </div>
                    <i className="fas fa-exclamation-triangle text-3xl text-red-500"></i>
                  </div>
                </div>

                <div className="bg-white border border-gray-200 rounded-lg p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-gray-500">Out of Stock</p>
                      <p className="text-3xl font-bold text-gray-600">{phoneAnalytics.inventory.outOfStockPhones || 0}</p>
                    </div>
                    <i className="fas fa-times-circle text-3xl text-gray-500"></i>
                  </div>
                </div>
              </div>

              {/* Inventory Value */}
              <div className="bg-gradient-to-r from-green-500 to-blue-600 rounded-lg p-6 text-white">
                <h3 className="text-lg font-semibold mb-2 flex items-center">
                  <i className="fas fa-money-bill-wave mr-2"></i>
                  Total Inventory Value
                </h3>
                <p className="text-3xl font-bold">{formatTZS(phoneAnalytics.inventory.totalValue || 0)}</p>
                <p className="text-sm opacity-90 mt-1">Current market value of all phones in stock</p>
              </div>

              {/* Phone Inventory Turnover */}
              <div className="bg-gray-50 rounded-lg p-6">
                <h3 className="text-lg font-semibold mb-4 flex items-center">
                  <i className="fas fa-sync-alt mr-2 text-blue-600"></i>
                  Phone Inventory Turnover
                </h3>
                <div className="space-y-3">
                  {phoneAnalytics.inventory.turnoverData?.slice(0, 10).map((phone, index) => (
                    <div key={phone._id} className="flex items-center justify-between bg-white p-4 rounded-lg">
                      <div className="flex items-center space-x-3">
                        <div>
                          <p className="font-medium">{phone.phoneName}</p>
                          <p className="text-sm text-gray-500">{phone.brand} • {phone.storage}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-medium">Turnover: {phone.turnoverRate?.toFixed(1)}x</p>
                        <p className="text-sm text-gray-500">{phone.currentStock} in stock</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* Warranty & Returns Tab */}
          {activeTab === 'warranty' && phoneAnalytics?.warranty && (
            <div className="space-y-6">
              {/* Warranty Overview */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div className="bg-white border border-gray-200 rounded-lg p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-gray-500">Active Warranties</p>
                      <p className="text-3xl font-bold text-green-600">{phoneAnalytics.warranty.activeWarranties || 0}</p>
                    </div>
                    <i className="fas fa-shield-alt text-3xl text-green-500"></i>
                  </div>
                </div>

                <div className="bg-white border border-gray-200 rounded-lg p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-gray-500">Warranty Claims</p>
                      <p className="text-3xl font-bold text-orange-600">{phoneAnalytics.warranty.totalClaims || 0}</p>
                    </div>
                    <i className="fas fa-exclamation-circle text-3xl text-orange-500"></i>
                  </div>
                </div>

                <div className="bg-white border border-gray-200 rounded-lg p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-gray-500">Return Rate</p>
                      <p className="text-3xl font-bold text-red-600">{formatPercentage(phoneAnalytics.warranty.returnRate)}</p>
                    </div>
                    <i className="fas fa-undo text-3xl text-red-500"></i>
                  </div>
                </div>

                <div className="bg-white border border-gray-200 rounded-lg p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-gray-500">Expiring Soon</p>
                      <p className="text-3xl font-bold text-yellow-600">{phoneAnalytics.warranty.expiringSoon || 0}</p>
                    </div>
                    <i className="fas fa-clock text-3xl text-yellow-500"></i>
                  </div>
                </div>
              </div>

              {/* Warranty Claims by Brand */}
              <div className="bg-gray-50 rounded-lg p-6">
                <h3 className="text-lg font-semibold mb-4 flex items-center">
                  <i className="fas fa-chart-bar mr-2 text-blue-600"></i>
                  Warranty Claims by Phone Brand
                </h3>
                <div className="space-y-3">
                  {phoneAnalytics.warranty.claimsByBrand?.map((brand) => (
                    <div key={brand.name} className="flex items-center justify-between bg-white p-4 rounded-lg">
                      <div className="flex items-center space-x-3">
                        <div>
                          <p className="font-medium">{brand.name}</p>
                          <p className="text-sm text-gray-500">Total phones sold: {brand.totalSold}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-medium">{brand.claims} claims</p>
                        <p className="text-sm text-gray-500">{formatPercentage(brand.claimRate)} claim rate</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* Customer Insights Tab */}
          {activeTab === 'customers' && phoneAnalytics?.sales && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div className="bg-white border border-gray-200 rounded-lg p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-gray-500">Total Phone Customers</p>
                      <p className="text-3xl font-bold text-gray-900">{phoneAnalytics.sales.totalCustomers || 0}</p>
                    </div>
                    <i className="fas fa-users text-3xl text-blue-500"></i>
                  </div>
                </div>

                <div className="bg-white border border-gray-200 rounded-lg p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-gray-500">New Customers</p>
                      <p className="text-3xl font-bold text-green-600">{phoneAnalytics.sales.newCustomers || 0}</p>
                    </div>
                    <i className="fas fa-user-plus text-3xl text-green-500"></i>
                  </div>
                </div>

                <div className="bg-white border border-gray-200 rounded-lg p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-gray-500">Repeat Customers</p>
                      <p className="text-3xl font-bold text-purple-600">{phoneAnalytics.sales.repeatCustomers || 0}</p>
                    </div>
                    <i className="fas fa-sync-alt text-3xl text-purple-500"></i>
                  </div>
                </div>

                <div className="bg-white border border-gray-200 rounded-lg p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-gray-500">Avg Customer Value (TZS)</p>
                      <p className="text-3xl font-bold text-purple-600">{formatTZS(phoneAnalytics.sales.avgCustomerValue)}</p>
                    </div>
                    <i className="fas fa-money-bill-wave text-3xl text-purple-500"></i>
                  </div>
                </div>
              </div>

              {/* Tanzania Customer Behavior */}
              <div className="bg-gray-50 rounded-lg p-6">
                <h3 className="text-lg font-semibold mb-4 flex items-center">
                  <i className="fas fa-chart-pie mr-2 text-blue-600"></i>
                  Tanzania Phone Customer Behavior
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="bg-white p-4 rounded-lg">
                    <h4 className="font-medium mb-3">Popular Phone Brands</h4>
                    <div className="space-y-2">
                      {phoneAnalytics.sales.popularBrands?.slice(0, 5).map((brand, index) => (
                        <div key={brand._id} className="flex justify-between">
                          <span>{brand.name}</span>
                          <span className="font-medium">{brand.phonesSold} phones sold</span>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className="bg-white p-4 rounded-lg">
                    <h4 className="font-medium mb-3">Payment Methods</h4>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span>M-Pesa</span>
                        <span className="font-medium">{formatPercentage(phoneAnalytics.sales.paymentMethods?.mpesa || 0)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Tigo Pesa</span>
                        <span className="font-medium">{formatPercentage(phoneAnalytics.sales.paymentMethods?.tigoPesa || 0)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Cash on Delivery</span>
                        <span className="font-medium">{formatPercentage(phoneAnalytics.sales.paymentMethods?.cod || 0)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Bank Transfer</span>
                        <span className="font-medium">{formatPercentage(phoneAnalytics.sales.paymentMethods?.bank || 0)}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default PhoneSalesAnalyticsPage;
