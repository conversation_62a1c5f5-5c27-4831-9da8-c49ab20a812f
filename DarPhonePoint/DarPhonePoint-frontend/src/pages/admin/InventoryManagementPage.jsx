import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import PageHeader from '../../components/ui/PageHeader';
import InventoryManager from '../../components/admin/InventoryManager';
import BulkOperations from '../../components/admin/BulkOperations';
import LoadingState from '../../components/ui/LoadingState';
import Alert from '../../components/ui/Alert';

/**
 * Inventory Management Page for Phone Point Dar
 * Comprehensive inventory tracking and management interface
 */
const InventoryManagementPage = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [selectedItems, setSelectedItems] = useState([]);
  const [error, setError] = useState('');

  // Check admin access
  useEffect(() => {
    if (!user || user.role !== 'admin') {
      navigate('/login');
      return;
    }
  }, [user, navigate]);

  const handleItemSelection = (itemId, isSelected) => {
    if (isSelected) {
      setSelectedItems(prev => [...prev, itemId]);
    } else {
      setSelectedItems(prev => prev.filter(id => id !== itemId));
    }
  };

  const handleBulkSuccess = () => {
    setSelectedItems([]);
    // Refresh inventory data
    window.location.reload();
  };

  const handleClearSelection = () => {
    setSelectedItems([]);
  };

  // Loading state for auth check
  if (!user) {
    return <LoadingState message="Loading inventory management..." />;
  }

  return (
    <div className="space-y-6">
      <PageHeader
        title="📦 Inventory Management"
        description="Comprehensive inventory tracking and stock management for Phone Point Dar"
        breadcrumbs={[
          { name: 'Dashboard', href: '/admin' },
          { name: 'Inventory', href: '/admin/inventory' }
        ]}
      />

      {error && (
        <Alert
          type="error"
          message={error}
          onClose={() => setError('')}
        />
      )}

      {/* Bulk Operations */}
      {selectedItems.length > 0 && (
        <BulkOperations
          selectedItems={selectedItems}
          itemType="products"
          onSuccess={handleBulkSuccess}
          onCancel={handleClearSelection}
        />
      )}

      {/* Inventory Manager */}
      <InventoryManager
        onItemSelect={handleItemSelection}
        selectedItems={selectedItems}
      />
    </div>
  );
};

export default InventoryManagementPage;
