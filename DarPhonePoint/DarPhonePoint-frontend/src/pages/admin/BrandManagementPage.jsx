import React, { useState, useEffect } from 'react';
import AdminPageWrapper, { QuickActionButton, AdminStatsCard } from '../../components/layout/AdminPageWrapper';
import Button from '../../components/ui/Button';
import Modal from '../../components/ui/Modal';
import { safeApiRequest } from '../../api/apiClient';

const BrandManagementPage = () => {
  const [brands, setBrands] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [showModal, setShowModal] = useState(false);
  const [editingBrand, setEditingBrand] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    logo_url: '',
    website: '',
    distributor_info: '',
    is_active: true,
    country_of_origin: '',
    warranty_period: 12
  });

  useEffect(() => {
    fetchBrands();
  }, []);

  const fetchBrands = async () => {
    setLoading(true);
    try {
      const response = await safeApiRequest({
        method: 'GET',
        url: '/products?limit=1000'
      });

      if (response.data && response.data.data) {
        // Extract unique brands from products
        const uniqueBrands = [...new Set(
          response.data.data
            .map(product => product.brand)
            .filter(brand => brand)
        )];

        // Create brand objects with stats
        const brandStats = uniqueBrands.map((brandName) => {
          const brandProducts = response.data.data.filter(
            product => product.brand === brandName
          );
          
          return {
            _id: brandName.toLowerCase().replace(/\s+/g, '-'),
            name: brandName,
            description: `${brandName} mobile devices and accessories`,
            logo_url: `/images/brands/${brandName.toLowerCase()}.png`,
            website: `https://www.${brandName.toLowerCase()}.com`,
            distributor_info: `Official ${brandName} distributor in Tanzania`,
            product_count: brandProducts.length,
            total_value: brandProducts.reduce((sum, product) => sum + (product.price || 0), 0),
            avg_price: brandProducts.length > 0 
              ? brandProducts.reduce((sum, product) => sum + (product.price || 0), 0) / brandProducts.length 
              : 0,
            is_active: true,
            country_of_origin: getBrandOrigin(brandName),
            warranty_period: 12,
            created_at: new Date().toISOString()
          };
        });

        setBrands(brandStats);
      }
    } catch (err) {
      console.error('Error fetching brands:', err);
      setError('Failed to load brand data');
    } finally {
      setLoading(false);
    }
  };

  const getBrandOrigin = (brandName) => {
    const origins = {
      'Apple': 'USA',
      'Samsung': 'South Korea',
      'Huawei': 'China',
      'Xiaomi': 'China',
      'Oppo': 'China',
      'Vivo': 'China',
      'OnePlus': 'China',
      'Google': 'USA',
      'Sony': 'Japan',
      'Nokia': 'Finland',
      'Motorola': 'USA',
      'Realme': 'China',
      'Tecno': 'China',
      'Infinix': 'China',
      'Itel': 'China'
    };
    return origins[brandName] || 'Unknown';
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      if (editingBrand) {
        setBrands(prev => prev.map(brand => 
          brand._id === editingBrand._id 
            ? { ...brand, ...formData, updated_at: new Date().toISOString() }
            : brand
        ));
        setSuccess('Brand updated successfully');
      } else {
        const newBrand = {
          _id: formData.name.toLowerCase().replace(/\s+/g, '-'),
          ...formData,
          product_count: 0,
          total_value: 0,
          avg_price: 0,
          created_at: new Date().toISOString()
        };
        setBrands(prev => [...prev, newBrand]);
        setSuccess('Brand created successfully');
      }

      setShowModal(false);
      setEditingBrand(null);
      setFormData({
        name: '',
        description: '',
        logo_url: '',
        website: '',
        distributor_info: '',
        is_active: true,
        country_of_origin: '',
        warranty_period: 12
      });
    } catch (err) {
      console.error('Error saving brand:', err);
      setError('Failed to save brand');
    }
  };

  const handleEdit = (brand) => {
    setEditingBrand(brand);
    setFormData({
      name: brand.name,
      description: brand.description,
      logo_url: brand.logo_url || '',
      website: brand.website || '',
      distributor_info: brand.distributor_info || '',
      is_active: brand.is_active,
      country_of_origin: brand.country_of_origin || '',
      warranty_period: brand.warranty_period || 12
    });
    setShowModal(true);
  };

  const handleDelete = async (brandId) => {
    if (!window.confirm('Are you sure you want to delete this brand?')) return;
    try {
      setBrands(prev => prev.filter(brand => brand._id !== brandId));
      setSuccess('Brand deleted successfully');
    } catch (err) {
      setError('Failed to delete brand');
    }
  };

  const formatTZS = (amount) => {
    return new Intl.NumberFormat('sw-TZ', {
      style: 'currency',
      currency: 'TZS',
      minimumFractionDigits: 0
    }).format(amount);
  };

  const totalBrands = brands.length;
  const activeBrands = brands.filter(brand => brand.is_active).length;
  const totalProducts = brands.reduce((sum, brand) => sum + brand.product_count, 0);
  const totalValue = brands.reduce((sum, brand) => sum + brand.total_value, 0);

  return (
    <AdminPageWrapper
      title="🏷️ Brand Management"
      description="Manage mobile device brands and distributors for Phone Point Dar"
      breadcrumbs={[
        { name: 'Dashboard', href: '/admin' },
        { name: 'Products', href: '/admin/products' },
        { name: 'Brands', href: '/admin/products/brands' }
      ]}
      actions={
        <QuickActionButton
          icon="fas fa-plus"
          onClick={() => setShowModal(true)}
        >
          Add Brand
        </QuickActionButton>
      }
      loading={loading}
      error={error}
      success={success}
      onErrorClose={() => setError('')}
      onSuccessClose={() => setSuccess('')}
    >
      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        <AdminStatsCard
          title="Total Brands"
          value={totalBrands}
          icon="fas fa-tags"
          color="blue"
        />
        <AdminStatsCard
          title="Active Brands"
          value={activeBrands}
          icon="fas fa-check-circle"
          color="green"
        />
        <AdminStatsCard
          title="Total Products"
          value={totalProducts}
          icon="fas fa-mobile-alt"
          color="purple"
        />
        <AdminStatsCard
          title="Total Value"
          value={formatTZS(totalValue)}
          icon="fas fa-money-bill-wave"
          color="yellow"
        />
      </div>

      {/* Brands Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
        {brands.map((brand) => (
          <div key={brand._id} className="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                  <i className="fas fa-mobile-alt text-blue-600 text-xl"></i>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">{brand.name}</h3>
                  <p className="text-sm text-gray-500">{brand.country_of_origin}</p>
                </div>
              </div>
              <span className={`px-2 py-1 text-xs font-semibold rounded-full ${
                brand.is_active 
                  ? 'bg-green-100 text-green-800' 
                  : 'bg-red-100 text-red-800'
              }`}>
                {brand.is_active ? 'Active' : 'Inactive'}
              </span>
            </div>

            <div className="space-y-2 mb-4">
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Products:</span>
                <span className="text-sm font-medium">{brand.product_count}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Total Value:</span>
                <span className="text-sm font-medium">{formatTZS(brand.total_value)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Avg Price:</span>
                <span className="text-sm font-medium">{formatTZS(brand.avg_price)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Warranty:</span>
                <span className="text-sm font-medium">{brand.warranty_period} months</span>
              </div>
            </div>

            <div className="flex justify-between items-center pt-4 border-t border-gray-200">
              <button
                onClick={() => handleEdit(brand)}
                className="text-blue-600 hover:text-blue-800 text-sm font-medium"
              >
                <i className="fas fa-edit mr-1"></i>
                Edit
              </button>
              <button
                onClick={() => handleDelete(brand._id)}
                className="text-red-600 hover:text-red-800 text-sm font-medium"
              >
                <i className="fas fa-trash mr-1"></i>
                Delete
              </button>
            </div>
          </div>
        ))}
      </div>

      {/* Add/Edit Brand Modal */}
      <Modal
        isOpen={showModal}
        onClose={() => {
          setShowModal(false);
          setEditingBrand(null);
          setFormData({
            name: '',
            description: '',
            logo_url: '',
            website: '',
            distributor_info: '',
            is_active: true,
            country_of_origin: '',
            warranty_period: 12
          });
        }}
        title={editingBrand ? 'Edit Brand' : 'Add New Brand'}
      >
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Brand Name
              </label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Country of Origin
              </label>
              <input
                type="text"
                value={formData.country_of_origin}
                onChange={(e) => setFormData({ ...formData, country_of_origin: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Description
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              rows="3"
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Website
              </label>
              <input
                type="url"
                value={formData.website}
                onChange={(e) => setFormData({ ...formData, website: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Warranty Period (months)
              </label>
              <input
                type="number"
                value={formData.warranty_period}
                onChange={(e) => setFormData({ ...formData, warranty_period: parseInt(e.target.value) })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                min="1"
                max="60"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Distributor Information
            </label>
            <textarea
              value={formData.distributor_info}
              onChange={(e) => setFormData({ ...formData, distributor_info: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              rows="2"
              placeholder="Official distributor contact and information for Tanzania"
            />
          </div>

          <div className="flex items-center">
            <input
              type="checkbox"
              id="is_active"
              checked={formData.is_active}
              onChange={(e) => setFormData({ ...formData, is_active: e.target.checked })}
              className="mr-2"
            />
            <label htmlFor="is_active" className="text-sm font-medium text-gray-700">
              Active Brand
            </label>
          </div>

          <div className="flex justify-end space-x-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => setShowModal(false)}
            >
              Cancel
            </Button>
            <Button type="submit">
              {editingBrand ? 'Update' : 'Create'} Brand
            </Button>
          </div>
        </form>
      </Modal>
    </AdminPageWrapper>
  );
};

export default BrandManagementPage;
