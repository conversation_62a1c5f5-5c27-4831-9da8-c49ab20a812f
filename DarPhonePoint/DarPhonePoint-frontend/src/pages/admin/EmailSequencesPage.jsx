import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { formatDate } from '../../utils/dateUtils';
import Button from '../../components/common/Button';
import Card from '../../components/common/Card';
import Alert from '../../components/common/Alert';
import LoadingState from '../../components/common/LoadingState';

/**
 * Email Sequences Page for Phone Point Dar
 * Manage automated email sequences and campaigns
 */
const EmailSequencesPage = () => {
  const { user } = useAuth();
  const [sequences, setSequences] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  useEffect(() => {
    fetchSequences();
  }, []);

  const fetchSequences = async () => {
    try {
      setLoading(true);
      // Mock data for demonstration
      const mockSequences = [
        {
          _id: 'seq_1',
          name: 'Welcome Series',
          description: 'Onboarding sequence for new customers',
          trigger: 'user_registration',
          isActive: true,
          totalEmails: 3,
          subscribers: 1247,
          openRate: 68.5,
          clickRate: 18.2,
          createdAt: '2024-01-01T00:00:00Z',
          emails: [
            { subject: 'Welcome to Phone Point Dar', delay: 0, template: 'welcome' },
            { subject: 'Explore Our Mobile Collection', delay: 3, template: 'product-showcase' },
            { subject: 'Special Offer Just for You', delay: 7, template: 'first-purchase-discount' }
          ]
        },
        {
          _id: 'seq_2',
          name: 'Abandoned Cart Recovery',
          description: 'Recover abandoned shopping carts',
          trigger: 'cart_abandonment',
          isActive: true,
          totalEmails: 2,
          subscribers: 892,
          openRate: 45.2,
          clickRate: 22.1,
          createdAt: '2024-01-05T00:00:00Z',
          emails: [
            { subject: 'You Left Something Behind', delay: 1, template: 'cart-reminder' },
            { subject: 'Last Chance - Complete Your Purchase', delay: 3, template: 'cart-final-reminder' }
          ]
        },
        {
          _id: 'seq_3',
          name: 'Post-Purchase Follow-up',
          description: 'Follow up after successful purchases',
          trigger: 'purchase_completed',
          isActive: true,
          totalEmails: 4,
          subscribers: 2156,
          openRate: 72.8,
          clickRate: 15.6,
          createdAt: '2024-01-10T00:00:00Z',
          emails: [
            { subject: 'Thank You for Your Purchase', delay: 0, template: 'purchase-thank-you' },
            { subject: 'How to Get the Most from Your Device', delay: 2, template: 'device-tips' },
            { subject: 'Rate Your Experience', delay: 7, template: 'review-request' },
            { subject: 'Accessories for Your Device', delay: 14, template: 'accessory-recommendations' }
          ]
        },
        {
          _id: 'seq_4',
          name: 'Re-engagement Campaign',
          description: 'Win back inactive customers',
          trigger: 'inactive_customer',
          isActive: false,
          totalEmails: 2,
          subscribers: 567,
          openRate: 38.4,
          clickRate: 12.8,
          createdAt: '2024-01-15T00:00:00Z',
          emails: [
            { subject: 'We Miss You at Phone Point Dar', delay: 0, template: 're-engagement' },
            { subject: 'Special Comeback Offer', delay: 7, template: 'comeback-offer' }
          ]
        }
      ];
      
      setSequences(mockSequences);
    } catch (error) {
      console.error('Error fetching sequences:', error);
      setError('Failed to load email sequences');
    } finally {
      setLoading(false);
    }
  };

  const handleToggleSequence = async (sequenceId, currentStatus) => {
    try {
      setSequences(prev => prev.map(seq => 
        seq._id === sequenceId 
          ? { ...seq, isActive: !currentStatus }
          : seq
      ));
      setSuccess(`Sequence ${!currentStatus ? 'activated' : 'deactivated'} successfully`);
    } catch (error) {
      setError('Failed to update sequence status');
    }
  };

  const getTriggerLabel = (trigger) => {
    const triggers = {
      'user_registration': 'User Registration',
      'cart_abandonment': 'Cart Abandonment',
      'purchase_completed': 'Purchase Completed',
      'inactive_customer': 'Inactive Customer'
    };
    return triggers[trigger] || trigger;
  };

  const getTriggerColor = (trigger) => {
    const colors = {
      'user_registration': 'bg-green-100 text-green-800',
      'cart_abandonment': 'bg-orange-100 text-orange-800',
      'purchase_completed': 'bg-blue-100 text-blue-800',
      'inactive_customer': 'bg-purple-100 text-purple-800'
    };
    return colors[trigger] || 'bg-gray-100 text-gray-800';
  };

  if (loading) {
    return <LoadingState message="Loading email sequences..." />;
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-600 to-blue-800 rounded-lg p-6 text-white">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
          <div>
            <h1 className="text-2xl font-bold mb-2">🔄 Email Sequences</h1>
            <p className="text-blue-100">Automated email campaigns for Phone Point Dar</p>
          </div>
          <div className="flex flex-wrap gap-2 mt-4 md:mt-0">
            <Button className="bg-white text-blue-600 hover:bg-gray-100">
              <i className="fas fa-plus mr-2"></i>Create Sequence
            </Button>
            <Button className="bg-blue-500 hover:bg-blue-400 text-white">
              <i className="fas fa-cog mr-2"></i>Automation Settings
            </Button>
          </div>
        </div>
      </div>

      {error && (
        <Alert
          type="error"
          message={error}
          onClose={() => setError('')}
        />
      )}

      {success && (
        <Alert
          type="success"
          message={success}
          onClose={() => setSuccess('')}
        />
      )}

      {/* Sequences Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                <i className="fas fa-list text-blue-600"></i>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Total Sequences</p>
              <p className="text-2xl font-bold text-gray-900">{sequences.length}</p>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                <i className="fas fa-play text-green-600"></i>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Active Sequences</p>
              <p className="text-2xl font-bold text-gray-900">{sequences.filter(s => s.isActive).length}</p>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                <i className="fas fa-users text-purple-600"></i>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Total Subscribers</p>
              <p className="text-2xl font-bold text-gray-900">
                {sequences.reduce((sum, seq) => sum + seq.subscribers, 0).toLocaleString()}
              </p>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
                <i className="fas fa-percentage text-orange-600"></i>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Avg. Open Rate</p>
              <p className="text-2xl font-bold text-gray-900">
                {(sequences.reduce((sum, seq) => sum + seq.openRate, 0) / sequences.length).toFixed(1)}%
              </p>
            </div>
          </div>
        </Card>
      </div>

      {/* Sequences List */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {sequences.map((sequence) => (
          <Card key={sequence._id} className="hover:shadow-lg transition-shadow">
            <div className="p-6">
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <h3 className="text-lg font-semibold text-gray-900 mb-1">
                    {sequence.name}
                  </h3>
                  <p className="text-sm text-gray-600 mb-2">
                    {sequence.description}
                  </p>
                </div>
                <div className="flex items-center space-x-2">
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getTriggerColor(sequence.trigger)}`}>
                    {getTriggerLabel(sequence.trigger)}
                  </span>
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                    sequence.isActive ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                  }`}>
                    {sequence.isActive ? 'Active' : 'Inactive'}
                  </span>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4 mb-4">
                <div>
                  <p className="text-sm font-medium text-gray-700">Emails in Sequence</p>
                  <p className="text-lg font-semibold text-gray-900">{sequence.totalEmails}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-700">Subscribers</p>
                  <p className="text-lg font-semibold text-gray-900">{sequence.subscribers.toLocaleString()}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-700">Open Rate</p>
                  <p className="text-lg font-semibold text-gray-900">{sequence.openRate}%</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-700">Click Rate</p>
                  <p className="text-lg font-semibold text-gray-900">{sequence.clickRate}%</p>
                </div>
              </div>

              <div className="mb-4">
                <p className="text-sm font-medium text-gray-700 mb-2">Email Flow:</p>
                <div className="space-y-2">
                  {sequence.emails.slice(0, 2).map((email, index) => (
                    <div key={index} className="flex items-center text-sm text-gray-600">
                      <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                        <span className="text-xs font-medium text-blue-600">{index + 1}</span>
                      </div>
                      <span className="flex-1">{email.subject}</span>
                      <span className="text-xs text-gray-500">
                        {email.delay === 0 ? 'Immediate' : `+${email.delay} days`}
                      </span>
                    </div>
                  ))}
                  {sequence.emails.length > 2 && (
                    <div className="text-xs text-gray-500 ml-9">
                      +{sequence.emails.length - 2} more emails...
                    </div>
                  )}
                </div>
              </div>

              <div className="mb-4">
                <p className="text-xs text-gray-500">
                  Created: {formatDate(sequence.createdAt)}
                </p>
              </div>

              <div className="flex space-x-2">
                <Button
                  size="sm"
                  variant="outline"
                  className="flex-1"
                >
                  <i className="fas fa-edit mr-1"></i>Edit
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  className="flex-1"
                >
                  <i className="fas fa-chart-line mr-1"></i>Analytics
                </Button>
                <Button
                  size="sm"
                  variant={sequence.isActive ? "outline" : "primary"}
                  onClick={() => handleToggleSequence(sequence._id, sequence.isActive)}
                  className={sequence.isActive ? "text-red-600 border-red-300 hover:bg-red-50" : ""}
                >
                  <i className={`fas ${sequence.isActive ? 'fa-pause' : 'fa-play'} mr-1`}></i>
                  {sequence.isActive ? 'Pause' : 'Start'}
                </Button>
              </div>
            </div>
          </Card>
        ))}
      </div>

      {/* Quick Actions */}
      <Card>
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold">Quick Actions</h3>
        </div>
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <i className="fas fa-plus text-blue-600 text-xl"></i>
              </div>
              <h4 className="font-medium text-gray-900 mb-2">Create New Sequence</h4>
              <p className="text-sm text-gray-600 mb-4">Set up automated email campaigns for different customer journeys</p>
              <Button size="sm" className="w-full">
                <i className="fas fa-plus mr-2"></i>Create Sequence
              </Button>
            </div>
            <div className="text-center">
              <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <i className="fas fa-copy text-green-600 text-xl"></i>
              </div>
              <h4 className="font-medium text-gray-900 mb-2">Import Templates</h4>
              <p className="text-sm text-gray-600 mb-4">Import pre-built email sequence templates for common scenarios</p>
              <Button size="sm" variant="outline" className="w-full">
                <i className="fas fa-download mr-2"></i>Browse Templates
              </Button>
            </div>
            <div className="text-center">
              <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <i className="fas fa-cog text-purple-600 text-xl"></i>
              </div>
              <h4 className="font-medium text-gray-900 mb-2">Automation Settings</h4>
              <p className="text-sm text-gray-600 mb-4">Configure global automation rules and triggers</p>
              <Button size="sm" variant="outline" className="w-full">
                <i className="fas fa-cog mr-2"></i>Settings
              </Button>
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default EmailSequencesPage;
