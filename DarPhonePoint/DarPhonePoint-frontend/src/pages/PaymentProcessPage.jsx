import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { useLoading } from '../contexts/LoadingContext';
import { get, post } from '../api/unifiedApiClient';
import { formatPrice } from '../utils/priceFormatter';
import Button from '../components/ui/Button';
import LoadingState from '../components/ui/LoadingState';
import Alert from '../components/ui/Alert';
import { useDeviceDetect } from '../utils/mobileOptimization';

const PaymentProcessPage = () => {
  const { orderId } = useParams();
  const { user } = useAuth();
  const { setLoading, isLoading } = useLoading();
  const { isMobile } = useDeviceDetect();
  const navigate = useNavigate();

  const [order, setOrder] = useState(null);
  const [paymentData, setPaymentData] = useState(null);
  const [error, setError] = useState('');
  const [paymentStatus, setPaymentStatus] = useState('pending');
  const [checkingPayment, setCheckingPayment] = useState(false);

  useEffect(() => {
    if (orderId) {
      fetchOrder();
    }
  }, [orderId]);

  useEffect(() => {
    let interval;
    if (paymentData && paymentStatus === 'pending') {
      // Check payment status every 10 seconds
      interval = setInterval(() => {
        checkPaymentStatus();
      }, 10000);
    }
    
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [paymentData, paymentStatus]);

  const fetchOrder = async () => {
    setLoading('order', true);
    try {
      const response = await safeApiRequest({
        method: 'GET',
        url: `/orders/${orderId}`
      });
      setOrder(response.data);
      
      // If order already paid, redirect to success
      if (response.data.payment_status === 'completed') {
        navigate('/payment/success');
        return;
      }
      
      // Initialize payment if not already done
      if (!response.data.transaction_id) {
        await initializePayment(response.data);
      } else {
        // Check existing payment status
        await checkPaymentStatus();
      }
    } catch (error) {
      setError('Failed to load order');
      console.error('Order fetch error:', error);
    } finally {
      setLoading('order', false);
    }
  };

  const initializePayment = async (orderData) => {
    try {
      const response = await safeApiRequest({
        method: 'POST',
        url: '/payments',
        data: {
          orderId: orderData._id,
          paymentMethod: orderData.payment_method || 'cash_on_delivery',
          phone: orderData.customer_phone
        }
      });
      
      setPaymentData(response.data);
      setPaymentStatus(response.data.status);
    } catch (error) {
      setError('Failed to initialize payment');
      console.error('Payment initialization error:', error);
    }
  };

  const checkPaymentStatus = async () => {
    if (!paymentData) return;
    
    setCheckingPayment(true);
    try {
      const response = await safeApiRequest({
        method: 'GET',
        url: `/payments/${paymentData.payment_id}/verify`
      });
      
      setPaymentStatus(response.data.status);
      
      if (response.data.status === 'completed') {
        navigate('/payment/success');
      } else if (response.data.status === 'failed') {
        setError('Payment failed. Please try again or contact support.');
      }
    } catch (error) {
      console.error('Payment verification error:', error);
    } finally {
      setCheckingPayment(false);
    }
  };

  const handleRetryPayment = () => {
    setError('');
    setPaymentData(null);
    setPaymentStatus('pending');
    fetchOrder();
  };

  if (isLoading('order')) {
    return <LoadingState message="Loading payment details..." />;
  }

  if (!order) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Payment Processing</h1>
          <p className="text-gray-600 mb-4">Order not found</p>
          <Button onClick={() => navigate('/orders')}>
            View Orders
          </Button>
        </div>
      </div>
    );
  }

  const getPaymentMethodIcon = (method) => {
    switch (method) {
      case 'mpesa': return 'fas fa-mobile-alt text-green-600';
      case 'tigo_pesa': return 'fas fa-mobile-alt text-blue-600';
      case 'airtel_money': return 'fas fa-mobile-alt text-red-600';
      case 'bank_transfer': return 'fas fa-university text-gray-600';
      case 'cash_on_delivery': return 'fas fa-money-bill-wave text-yellow-600';
      default: return 'fas fa-credit-card text-gray-600';
    }
  };

  const getPaymentMethodName = (method) => {
    switch (method) {
      case 'mpesa': return 'M-Pesa';
      case 'tigo_pesa': return 'Tigo Pesa';
      case 'airtel_money': return 'Airtel Money';
      case 'bank_transfer': return 'Bank Transfer';
      case 'cash_on_delivery': return 'Cash on Delivery';
      default: return method;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto">
          <div className="text-center mb-8">
            <h1 className="text-2xl font-bold mb-2">Payment Processing</h1>
            <p className="text-gray-600">Order #{order.order_number}</p>
          </div>

          {error && (
            <Alert
              type="error"
              message={error}
              onClose={() => setError('')}
              className="mb-6"
            />
          )}

          {/* Payment Status */}
          <div className="bg-white rounded-lg shadow-sm border p-6 mb-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-semibold">Payment Status</h2>
              {checkingPayment && (
                <i className="fas fa-spinner fa-spin text-blue-500"></i>
              )}
            </div>
            
            <div className="flex items-center mb-4">
              <div className={`w-12 h-12 rounded-full flex items-center justify-center mr-4 ${
                paymentStatus === 'completed' ? 'bg-green-100' :
                paymentStatus === 'failed' ? 'bg-red-100' :
                'bg-yellow-100'
              }`}>
                <i className={`${
                  paymentStatus === 'completed' ? 'fas fa-check text-green-600' :
                  paymentStatus === 'failed' ? 'fas fa-times text-red-600' :
                  'fas fa-clock text-yellow-600'
                }`}></i>
              </div>
              
              <div>
                <p className="font-medium">
                  {paymentStatus === 'completed' ? 'Payment Completed' :
                   paymentStatus === 'failed' ? 'Payment Failed' :
                   'Payment Pending'}
                </p>
                <p className="text-sm text-gray-600">
                  {paymentStatus === 'completed' ? 'Your payment has been processed successfully' :
                   paymentStatus === 'failed' ? 'There was an issue processing your payment' :
                   'Waiting for payment confirmation'}
                </p>
              </div>
            </div>
          </div>

          {/* Payment Details */}
          {paymentData && (
            <div className="bg-white rounded-lg shadow-sm border p-6 mb-6">
              <h2 className="text-lg font-semibold mb-4">Payment Details</h2>
              
              <div className="space-y-4">
                <div className="flex justify-between">
                  <span className="text-gray-600">Payment Method:</span>
                  <div className="flex items-center">
                    <i className={`${getPaymentMethodIcon(paymentData.payment_method)} mr-2`}></i>
                    <span className="font-medium">{getPaymentMethodName(paymentData.payment_method)}</span>
                  </div>
                </div>
                
                <div className="flex justify-between">
                  <span className="text-gray-600">Amount:</span>
                  <span className="font-semibold text-blue-600">{formatPrice(paymentData.amount)}</span>
                </div>
                
                <div className="flex justify-between">
                  <span className="text-gray-600">Transaction ID:</span>
                  <span className="font-mono text-sm">{paymentData.payment_id}</span>
                </div>
                
                {paymentData.expires_at && (
                  <div className="flex justify-between">
                    <span className="text-gray-600">Expires:</span>
                    <span className="text-sm">{new Date(paymentData.expires_at).toLocaleString()}</span>
                  </div>
                )}
              </div>
              
              {/* Payment Instructions */}
              {paymentData.instructions && (
                <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                  <h3 className="font-medium text-blue-800 mb-2">Payment Instructions</h3>
                  <p className="text-sm text-blue-700">{paymentData.instructions.message}</p>
                  
                  {paymentData.instructions.bank_name && (
                    <div className="mt-3 space-y-1 text-sm">
                      <p><strong>Bank:</strong> {paymentData.instructions.bank_name}</p>
                      <p><strong>Account Name:</strong> {paymentData.instructions.account_name}</p>
                      <p><strong>Account Number:</strong> {paymentData.instructions.account_number}</p>
                      <p><strong>Reference:</strong> {paymentData.instructions.reference}</p>
                    </div>
                  )}
                </div>
              )}
            </div>
          )}

          {/* Action Buttons */}
          <div className="text-center space-y-4">
            {paymentStatus === 'failed' && (
              <Button onClick={handleRetryPayment} className="mr-4">
                <i className="fas fa-redo mr-2"></i>
                Retry Payment
              </Button>
            )}
            
            <Button
              variant="outline"
              onClick={() => navigate('/orders')}
            >
              View All Orders
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PaymentProcessPage;
