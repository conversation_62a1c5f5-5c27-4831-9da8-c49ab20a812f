import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { safeApiRequest } from '../api/apiClient';
import Button from '../components/ui/Button';
import Alert from '../components/ui/Alert';

const DashboardPage = () => {
  const { user } = useAuth();
  const [orders, setOrders] = useState([]);
  const [recentOrders, setRecentOrders] = useState([]);
  const [wishlist, setWishlist] = useState([]);
  const [deviceWarranties, setDeviceWarranties] = useState([]);
  const [loyaltyPoints, setLoyaltyPoints] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  // Helper functions for Phone Point Dar
  const getOrderStatus = (order) => {
    if (!order) return 'unknown';
    if (order.status) return String(order.status).toLowerCase();
    if (order.payment_status) {
      return order.payment_status === 'completed' ? 'delivered' : 'processing';
    }
    return 'pending';
  };

  const formatTanzanianPrice = (price) => {
    return new Intl.NumberFormat('sw-TZ', {
      style: 'currency',
      currency: 'TZS',
      minimumFractionDigits: 0
    }).format(price);
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'delivered': return 'bg-green-100 text-green-800';
      case 'shipped': return 'bg-blue-100 text-blue-800';
      case 'processing': return 'bg-yellow-100 text-yellow-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        // Fetch orders - use admin endpoint if user is admin
        const ordersEndpoint = user?.role === 'admin' ? '/orders/admin/all' : '/orders';
        const ordersResponse = await safeApiRequest({
          method: 'GET',
          url: ordersEndpoint
        });

        let ordersData = [];
        if (ordersResponse.data && ordersResponse.data.data) {
          ordersData = ordersResponse.data.data;
        } else if (ordersResponse.data) {
          ordersData = ordersResponse.data;
        }

        if (!Array.isArray(ordersData)) {
          ordersData = [];
        }

        ordersData.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
        setOrders(ordersData);
        setRecentOrders(ordersData.slice(0, 3));

        // Fetch real wishlist data
        try {
          const wishlistResponse = await safeApiRequest({
            method: 'GET',
            url: '/wishlist'
          });

          // Extract wishlist items from API response
          const apiResponse = wishlistResponse.data || {};
          const wishlistData = apiResponse.data || { items: [] };
          const wishlistItems = (wishlistData.items || []).map(item => ({
            id: item._id,
            name: item.product?.name || 'Unknown Product',
            price: item.product?.price || 0,
            image: item.product?.primary_image?.url || item.product?.images?.[0]?.url || '/api/placeholder/300/300',
            brand: item.product?.brand || 'Unknown Brand'
          }));

          setWishlist(wishlistItems);
        } catch (wishlistError) {
          console.error('Error fetching wishlist:', wishlistError);
          setWishlist([]); // Set empty array on error
        }

        // Calculate loyalty points based on orders (10 points per 1000 TZS spent)
        const totalSpent = ordersData.reduce((sum, order) => sum + (order.total_amount || order.amount || order.total || 0), 0);
        const calculatedPoints = Math.floor(totalSpent / 1000) * 10;
        setLoyaltyPoints(calculatedPoints);

        // Mock device warranties (replace with real API when warranty system is implemented)
        setDeviceWarranties([
          {
            id: 1,
            device: 'iPhone 15 Pro',
            purchaseDate: '2024-01-15',
            warrantyExpiry: '2025-01-15',
            status: 'active',
            serialNumber: 'F2LX8K9M2Q'
          }
        ]);

      } catch (error) {
        console.error('Error fetching dashboard data:', error);
        setError('Failed to load dashboard data');
      } finally {
        setLoading(false);
      }
    };

    if (user) {
      fetchDashboardData();
    } else {
      setLoading(false);
    }
  }, [user]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading your Phone Point Dar dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-600 to-blue-800 text-white">
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold">Karibu, {user?.name}! 📱</h1>
              <p className="text-blue-100 mt-2">Welcome to your Phone Point Dar dashboard</p>
            </div>
            <div className="text-right">
              <p className="text-sm text-blue-100">Customer since</p>
              <p className="font-semibold">{new Date(user?.created_at).toLocaleDateString('sw-TZ')}</p>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        {error && (
          <Alert
            type="error"
            message={error}
            onClose={() => setError('')}
            className="mb-8"
          />
        )}

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-blue-100 text-blue-600 mr-4">
                <i className="fas fa-mobile-alt text-xl"></i>
              </div>
              <div>
                <p className="text-sm text-gray-500">Total Orders</p>
                <p className="text-2xl font-bold text-blue-600">{orders.length}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-green-100 text-green-600 mr-4">
                <i className="fas fa-heart text-xl"></i>
              </div>
              <div>
                <p className="text-sm text-gray-500">Wishlist Items</p>
                <p className="text-2xl font-bold text-green-600">{wishlist.length}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-purple-100 text-purple-600 mr-4">
                <i className="fas fa-shield-alt text-xl"></i>
              </div>
              <div>
                <p className="text-sm text-gray-500">Active Warranties</p>
                <p className="text-2xl font-bold text-purple-600">{deviceWarranties.length}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-orange-100 text-orange-600 mr-4">
                <i className="fas fa-star text-xl"></i>
              </div>
              <div>
                <p className="text-sm text-gray-500">Loyalty Points</p>
                <p className="text-2xl font-bold text-orange-600">{loyaltyPoints.toLocaleString()}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Recent Orders */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-lg shadow-md overflow-hidden">
              <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                <h2 className="text-xl font-bold text-gray-800">Recent Phone Orders</h2>
                <Link to="/orders">
                  <Button variant="outline" size="sm">View All</Button>
                </Link>
              </div>
              <div className="p-6">
                {recentOrders.length > 0 ? (
                  <div className="space-y-4">
                    {recentOrders.map((order) => (
                      <div key={order._id} className="border border-gray-200 rounded-lg p-4">
                        <div className="flex justify-between items-start mb-3">
                          <div>
                            <h3 className="font-semibold text-gray-800">
                              {order.product?.name || 'Phone Order'}
                            </h3>
                            <p className="text-sm text-gray-500">
                              Order #{order._id?.slice(-8)} • {new Date(order.created_at).toLocaleDateString('sw-TZ')}
                            </p>
                          </div>
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(getOrderStatus(order))}`}>
                            {getOrderStatus(order).charAt(0).toUpperCase() + getOrderStatus(order).slice(1)}
                          </span>
                        </div>
                        <div className="flex justify-between items-center">
                          <p className="text-lg font-bold text-blue-600">
                            {formatTanzanianPrice(order.total_amount || order.amount || order.total || 0)}
                          </p>
                          <div className="flex space-x-2">
                            <Link to={`/orders/${order._id}`}>
                              <Button variant="outline" size="sm">Track Order</Button>
                            </Link>
                            {getOrderStatus(order) === 'delivered' && (
                              <Button variant="primary" size="sm">
                                <i className="fas fa-star mr-1"></i>
                                Review
                              </Button>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <i className="fas fa-mobile-alt text-gray-400 text-4xl mb-4"></i>
                    <p className="text-gray-500 mb-4">You haven't ordered any phones yet.</p>
                    <Link to="/products">
                      <Button variant="primary">
                        <i className="fas fa-shopping-cart mr-2"></i>
                        Shop Phones
                      </Button>
                    </Link>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Quick Actions */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-semibold mb-4 text-gray-800">Quick Actions</h3>
              <div className="space-y-3">
                <Link to="/products" className="block">
                  <Button variant="outline" className="w-full justify-start">
                    <i className="fas fa-mobile-alt mr-3"></i>
                    Browse Phones
                  </Button>
                </Link>
                <Link to="/orders" className="block">
                  <Button variant="outline" className="w-full justify-start">
                    <i className="fas fa-truck mr-3"></i>
                    Track Orders
                  </Button>
                </Link>
                <Link to="/profile" className="block">
                  <Button variant="outline" className="w-full justify-start">
                    <i className="fas fa-user mr-3"></i>
                    Update Profile
                  </Button>
                </Link>
                <Button variant="outline" className="w-full justify-start">
                  <i className="fas fa-headset mr-3"></i>
                  Contact Support
                </Button>
              </div>
            </div>

            {/* Wishlist Preview */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-semibold text-gray-800">My Wishlist</h3>
                <Link to="/wishlist" className="text-blue-600 hover:text-blue-800 text-sm">
                  View All
                </Link>
              </div>
              {wishlist.length > 0 ? (
                <div className="space-y-3">
                  {wishlist.slice(0, 2).map((item) => (
                    <div key={item.id} className="flex items-center space-x-3">
                      <div className="w-12 h-12 bg-gray-200 rounded-lg overflow-hidden">
                        <img
                          src={item.image}
                          alt={item.name}
                          className="w-full h-full object-cover"
                          onError={(e) => {
                            e.target.style.display = 'none';
                            e.target.nextSibling.style.display = 'flex';
                          }}
                        />
                        <div className="w-full h-full bg-gray-200 rounded-lg flex items-center justify-center" style={{display: 'none'}}>
                          <i className="fas fa-mobile-alt text-gray-500"></i>
                        </div>
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-800 truncate">{item.name}</p>
                        <p className="text-xs text-gray-500">{item.brand}</p>
                        <p className="text-sm text-blue-600">{formatTanzanianPrice(item.price)}</p>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-4">
                  <i className="fas fa-heart text-gray-300 text-2xl mb-2"></i>
                  <p className="text-gray-500 text-sm">No items in wishlist</p>
                  <Link to="/products" className="text-blue-600 hover:text-blue-800 text-xs">
                    Browse phones to add favorites
                  </Link>
                </div>
              )}
            </div>

            {/* Device Warranties */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-semibold mb-4 text-gray-800">Device Warranties</h3>
              {deviceWarranties.length > 0 ? (
                <div className="space-y-3">
                  {deviceWarranties.map((warranty) => (
                    <div key={warranty.id} className="border border-gray-200 rounded-lg p-3">
                      <div className="flex justify-between items-start mb-2">
                        <h4 className="font-medium text-gray-800">{warranty.device}</h4>
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                          Active
                        </span>
                      </div>
                      <p className="text-xs text-gray-500 mb-1">Serial: {warranty.serialNumber}</p>
                      <p className="text-xs text-gray-500">
                        Expires: {new Date(warranty.warrantyExpiry).toLocaleDateString('sw-TZ')}
                      </p>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-500 text-sm">No active warranties</p>
              )}
            </div>
          </div>
        </div>

        {/* Phone Point Dar Special Features */}
        <div className="mt-12 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* Trade-In Program */}
          <div className="bg-gradient-to-br from-green-500 to-green-600 rounded-lg shadow-md p-6 text-white">
            <div className="flex items-center mb-4">
              <i className="fas fa-recycle text-2xl mr-3"></i>
              <h3 className="text-xl font-semibold">Trade-In Program</h3>
            </div>
            <p className="text-green-100 mb-4">
              Get instant value for your old phone when buying a new one.
            </p>
            <Link to="/trade-in">
              <Button variant="outline" className="border-white text-white hover:bg-white hover:text-green-600">
                Check Trade Value
              </Button>
            </Link>
          </div>

          {/* Phone Comparison */}
          <div className="bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg shadow-md p-6 text-white">
            <div className="flex items-center mb-4">
              <i className="fas fa-balance-scale text-2xl mr-3"></i>
              <h3 className="text-xl font-semibold">Compare Phones</h3>
            </div>
            <p className="text-purple-100 mb-4">
              Compare specs, prices, and features side by side.
            </p>
            <Link to="/comparison">
              <Button variant="outline" className="border-white text-white hover:bg-white hover:text-purple-600">
                Start Comparing
              </Button>
            </Link>
          </div>

          {/* Local Support */}
          <div className="bg-gradient-to-br from-orange-500 to-orange-600 rounded-lg shadow-md p-6 text-white">
            <div className="flex items-center mb-4">
              <i className="fas fa-map-marker-alt text-2xl mr-3"></i>
              <h3 className="text-xl font-semibold">Dar es Salaam Support</h3>
            </div>
            <p className="text-orange-100 mb-4">
              Visit our store in Kariakoo for repairs and support.
            </p>
            <Link to="/store-locator">
              <Button variant="outline" className="border-white text-white hover:bg-white hover:text-orange-600">
                Find Store
              </Button>
            </Link>
          </div>
        </div>

        {/* Loyalty Program Banner */}
        <div className="mt-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg shadow-md p-8 text-white text-center">
          <div className="max-w-2xl mx-auto">
            <h2 className="text-2xl font-bold mb-4">
              <i className="fas fa-star mr-2"></i>
              Phone Point Dar Loyalty Program
            </h2>
            <p className="text-blue-100 mb-6">
              Earn points with every purchase and unlock exclusive discounts on the latest phones and accessories.
            </p>
            <div className="flex justify-center space-x-4">
              <Link to="/loyalty">
                <Button variant="outline" className="border-white text-white hover:bg-white hover:text-blue-600">
                  Learn More
                </Button>
              </Link>
              <Link to="/loyalty">
                <Button className="bg-white text-blue-600 hover:bg-gray-100">
                  Join Now
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DashboardPage;
