import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import Input from '../components/ui/Input';
import PasswordInput from '../components/ui/PasswordInput';
import Button from '../components/ui/Button';
import Alert from '../components/ui/Alert';

const RegisterPage = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    password: '',
    confirmPassword: '',
  });
  const [formErrors, setFormErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const { register, error, setError, user, isAdmin } = useAuth();
  const navigate = useNavigate();

  // Redirect if user is already logged in
  useEffect(() => {
    if (user) {
      // Redirect admin users to admin dashboard, regular users to customer dashboard
      const redirectPath = isAdmin() ? '/admin/dashboard' : '/dashboard';
      navigate(redirectPath);
    }
  }, [user, navigate, isAdmin]);

  // Clear any auth errors when component mounts
  useEffect(() => {
    setError('');
  }, [setError]);

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });

    // Clear field error when user types
    if (formErrors[e.target.name]) {
      setFormErrors({
        ...formErrors,
        [e.target.name]: '',
      });
    }
  };

  const validateForm = () => {
    const errors = {};

    if (!formData.name) {
      errors.name = 'Name is required';
    }

    if (!formData.email) {
      errors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      errors.email = 'Email is invalid';
    }

    if (!formData.password) {
      errors.password = 'Password is required';
    } else if (formData.password.length < 8) {
      errors.password = 'Password must be at least 8 characters';
    } else if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(formData.password)) {
      errors.password = 'Password must contain at least one uppercase letter, one lowercase letter, and one number';
    }

    if (formData.password !== formData.confirmPassword) {
      errors.confirmPassword = 'Passwords do not match';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    // Remove confirmPassword before sending to API
    const { confirmPassword, ...registerData } = formData;

    const result = await register(registerData);

    setIsSubmitting(false);

    if (result.success) {
      if (result.requiresVerification) {
        // Redirect to a verification pending page
        navigate('/verification-sent', {
          state: {
            email: formData.email,
            message: result.message
          }
        });
      } else {
        // Old flow - direct login (for backward compatibility)
        navigate('/dashboard');
      }
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-blue-100 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <div className="flex justify-center">
          <Link to="/" className="flex items-center group">
            <i className="fas fa-mobile-alt mr-3 text-blue-600 text-3xl group-hover:text-blue-700 transition-colors"></i>
            <span className="text-2xl font-bold text-gray-900 group-hover:text-blue-700 transition-colors">Phone Point Dar</span>
          </Link>
        </div>
        <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
          Join Phone Point Dar! 🇹🇿
        </h2>
        <p className="mt-2 text-center text-sm text-gray-600">
          Create your account to access exclusive deals on mobile devices
        </p>
        <p className="mt-1 text-center text-sm text-blue-600">
          Already have an account?{' '}
          <Link to="/login" className="font-medium text-blue-600 hover:text-blue-500 underline">
            Sign in here
          </Link>
        </p>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-white py-8 px-4 shadow-xl border border-blue-100 sm:rounded-xl sm:px-10">
          {error && (
            <Alert
              type="error"
              message={error}
              onClose={() => setError('')}
              className="mb-6"
            />
          )}

          <form className="space-y-6" onSubmit={handleSubmit}>
            <Input
              label="Full name"
              id="name"
              name="name"
              type="text"
              autoComplete="name"
              value={formData.name}
              onChange={handleChange}
              error={formErrors.name}
              required
            />

            <Input
              label="Email address"
              id="email"
              name="email"
              type="email"
              autoComplete="email"
              value={formData.email}
              onChange={handleChange}
              error={formErrors.email}
              required
            />

            <PasswordInput
              label="Password"
              id="password"
              name="password"
              autoComplete="new-password"
              value={formData.password}
              onChange={handleChange}
              error={formErrors.password}
              showStrengthIndicator={true}
              showRequirements={true}
              required
            />

            <PasswordInput
              label="Confirm password"
              id="confirmPassword"
              name="confirmPassword"
              autoComplete="new-password"
              value={formData.confirmPassword}
              onChange={handleChange}
              error={formErrors.confirmPassword}
              showStrengthIndicator={false}
              showRequirements={false}
              required
            />

            <div className="flex items-center">
              <input
                id="terms"
                name="terms"
                type="checkbox"
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                required
              />
              <label htmlFor="terms" className="ml-2 block text-sm text-gray-900">
                I agree to the{' '}
                <a href="#" className="font-medium text-blue-600 hover:text-blue-500">
                  Terms of Service
                </a>{' '}
                and{' '}
                <a href="#" className="font-medium text-blue-600 hover:text-blue-500">
                  Privacy Policy
                </a>
              </label>
            </div>

            <div>
              <Button
                type="submit"
                fullWidth
                size="lg"
                isLoading={isSubmitting}
              >
                Create account
              </Button>
            </div>
          </form>

          <div className="mt-6">
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-gray-300" />
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="px-2 bg-white text-gray-500">Member Benefits</span>
              </div>
            </div>

            <div className="mt-6 space-y-3">
              <div className="text-center">
                <div className="grid grid-cols-2 gap-3 text-xs text-gray-600">
                  <div className="flex items-center justify-center">
                    <i className="fas fa-heart text-red-500 mr-1"></i>
                    Wishlist & Favorites
                  </div>
                  <div className="flex items-center justify-center">
                    <i className="fas fa-truck text-blue-500 mr-1"></i>
                    Order Tracking
                  </div>
                  <div className="flex items-center justify-center">
                    <i className="fas fa-percent text-green-500 mr-1"></i>
                    Exclusive Deals
                  </div>
                  <div className="flex items-center justify-center">
                    <i className="fas fa-shield-alt text-purple-500 mr-1"></i>
                    Warranty Support
                  </div>
                </div>
              </div>
              <Link
                to="/products"
                className="w-full inline-flex justify-center items-center py-2 px-4 border border-blue-300 rounded-md shadow-sm bg-blue-50 text-sm font-medium text-blue-700 hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
              >
                <i className="fas fa-mobile-alt mr-2"></i>
                Browse Phones First
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RegisterPage;
