import React, { useState, useEffect } from 'react';
import { Link, useParams, useNavigate } from 'react-router-dom';
import { safeApiRequest } from '../api/apiClient';
import Button from '../components/ui/Button';
import Alert from '../components/ui/Alert';

const OrderDetailPage = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [order, setOrder] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  // Helper functions
  const getOrderStatus = (order) => {
    if (!order) return 'unknown';
    if (order.status) return order.status;
    return order.payment_status === 'completed' ? 'completed' : 'processing';
  };

  const getOrderAmount = (order) => {
    if (!order) return 0;
    return order.amount || order.total || 0;
  };

  useEffect(() => {
    const fetchOrder = async () => {
      try {
        const response = await safeApiRequest({
          method: 'GET',
          url: `/orders/${id}`
        });
        console.log('Order detail response:', response);

        // Handle nested data structure
        const orderData = response.data && response.data.data
          ? response.data.data
          : response.data;

        setOrder(orderData);
        setLoading(false);
      } catch (err) {
        console.error('Error fetching order details:', err);
        setError('Failed to load order details. Please try again later.');
        setLoading(false);
      }
    };

    fetchOrder();
  }, [id]);

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen bg-gray-50">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-gray-50 py-12">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto">
            <Alert
              type="error"
              message={error}
              onClose={() => setError('')}
              className="mb-8"
            />
            <div className="flex justify-center">
              <Button onClick={() => navigate('/orders')} variant="outline">
                Back to Orders
              </Button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!order) {
    return (
      <div className="bg-gray-50 py-12">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto text-center">
            <h2 className="text-2xl font-bold mb-4">Order Not Found</h2>
            <p className="text-gray-600 mb-6">The order you're looking for doesn't exist or you don't have permission to view it.</p>
            <Button onClick={() => navigate('/orders')} variant="outline">
              Back to Orders
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-gray-50 py-12">
      <div className="container mx-auto px-4">
        <div className="max-w-3xl mx-auto">
          <div className="flex justify-between items-center mb-8">
            <div>
              <h1 className="text-3xl font-bold mb-2">Order Details</h1>
              <p className="text-gray-600">Order #{order._id.substring(0, 8)}</p>
            </div>
            <Button onClick={() => navigate('/orders')} variant="outline">
              Back to Orders
            </Button>
          </div>

          <div className="bg-white rounded-lg shadow-md overflow-hidden mb-8">
            <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
              <h2 className="text-xl font-bold">Order Summary</h2>
            </div>
            <div className="p-6">
              <div className="grid grid-cols-2 gap-6 mb-6">
                <div>
                  <p className="text-sm text-gray-500 mb-1">Order Date</p>
                  <p className="font-medium">{new Date(order.created_at).toLocaleDateString()}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500 mb-1">Order Status</p>
                  {(() => {
                    const status = getOrderStatus(order);
                    return (
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        status === 'completed'
                          ? 'bg-green-100 text-green-800'
                          : status === 'processing'
                            ? 'bg-yellow-100 text-yellow-800'
                            : status === 'cancelled'
                              ? 'bg-red-100 text-red-800'
                              : 'bg-gray-100 text-gray-800'
                      }`}>
                        {status === 'completed' ? 'Completed' :
                         status === 'processing' ? 'Processing' :
                         status === 'cancelled' ? 'Cancelled' :
                         status}
                      </span>
                    );
                  })()}
                </div>
                <div>
                  <p className="text-sm text-gray-500 mb-1">Payment Status</p>
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                    order.payment_status === 'completed'
                      ? 'bg-green-100 text-green-800'
                      : 'bg-yellow-100 text-yellow-800'
                  }`}>
                    {order.payment_status === 'completed' ? 'Paid' : 'Pending'}
                  </span>
                </div>
                <div>
                  <p className="text-sm text-gray-500 mb-1">Total Amount</p>
                  <p className="text-xl font-bold">${getOrderAmount(order).toFixed(2)}</p>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-md overflow-hidden mb-8">
            <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
              <h2 className="text-xl font-bold">Product Details</h2>
            </div>
            <div className="p-6">
              {order.product ? (
                <div className="flex items-center">
                  <div className="flex-shrink-0 h-20 w-20 bg-gray-200 rounded-md overflow-hidden">
                    <img
                      src={`https://via.placeholder.com/80?text=${encodeURIComponent((order.product?.name || 'NA').substring(0, 2))}`}
                      alt={order.product?.name || 'Product'}
                      className="h-20 w-20 object-cover"
                    />
                  </div>
                  <div className="ml-6 flex-1">
                    <h3 className="text-lg font-bold">{order.product?.name || 'Product Unavailable'}</h3>
                    <p className="text-gray-500 mb-2">{order.product?.product_type || 'Unknown'}</p>
                    <p className="text-gray-700">{order.product?.description?.substring(0, 100) || 'No description available'}...</p>
                  </div>
                  <div className="ml-6">
                    <p className="text-lg font-bold">${order.product?.price?.toFixed(2) || '0.00'}</p>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8">
                  <p className="text-gray-500">Product information is no longer available.</p>
                </div>
              )}

              {getOrderStatus(order) === 'delivered' && (
                <div className="mt-6 flex justify-end space-x-3">
                  <Button variant="outline">
                    <i className="fas fa-star mr-2"></i>
                    Write Review
                  </Button>
                  <Button variant="outline">
                    <i className="fas fa-undo mr-2"></i>
                    Return/Exchange
                  </Button>
                  <Button variant="primary">
                    <i className="fas fa-download mr-2"></i>
                    Download Invoice
                  </Button>
                </div>
              )}
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-md overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
              <h2 className="text-xl font-bold">Customer Information</h2>
            </div>
            <div className="p-6">
              <div className="grid grid-cols-2 gap-6">
                <div>
                  <p className="text-sm text-gray-500 mb-1">Name</p>
                  <p className="font-medium">{order.customer_name || 'N/A'}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500 mb-1">Email</p>
                  <p className="font-medium">{order.customer_email}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OrderDetailPage;
