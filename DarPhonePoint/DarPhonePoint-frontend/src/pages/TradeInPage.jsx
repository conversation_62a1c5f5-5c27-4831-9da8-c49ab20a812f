import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import Button from '../components/ui/Button';
import Input from '../components/ui/Input';
import Alert from '../components/ui/Alert';
import { formatPrice } from '../utils/priceFormatter';

const TradeInPage = () => {
  const { user } = useAuth();
  const [step, setStep] = useState(1);
  const [deviceInfo, setDeviceInfo] = useState({
    brand: '',
    model: '',
    storage: '',
    condition: '',
    imei: '',
    accessories: []
  });
  const [valuation, setValuation] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  // Tanzania popular phone brands and models
  const phoneDatabase = {
    'Apple': {
      models: ['iPhone 15 Pro Max', 'iPhone 15 Pro', 'iPhone 15', 'iPhone 14 Pro Max', 'iPhone 14 Pro', 'iPhone 14', 'iPhone 13 Pro Max', 'iPhone 13 Pro', 'iPhone 13', 'iPhone 12 Pro Max', 'iPhone 12 Pro', 'iPhone 12'],
      storage: ['64GB', '128GB', '256GB', '512GB', '1TB']
    },
    'Samsung': {
      models: ['Galaxy S24 Ultra', 'Galaxy S24+', 'Galaxy S24', 'Galaxy S23 Ultra', 'Galaxy S23+', 'Galaxy S23', 'Galaxy A54', 'Galaxy A34', 'Galaxy A24', 'Galaxy Note 20 Ultra'],
      storage: ['128GB', '256GB', '512GB', '1TB']
    },
    'Xiaomi': {
      models: ['Redmi Note 13 Pro', 'Redmi Note 13', 'Redmi Note 12 Pro', 'Redmi Note 12', 'Mi 13 Ultra', 'Mi 13 Pro', 'Mi 13', 'Poco X5 Pro', 'Poco F5'],
      storage: ['64GB', '128GB', '256GB', '512GB']
    },
    'Oppo': {
      models: ['Reno 10 Pro', 'Reno 10', 'Find X6 Pro', 'Find X6', 'A98', 'A78', 'A58'],
      storage: ['128GB', '256GB', '512GB']
    },
    'Tecno': {
      models: ['Phantom V Fold', 'Phantom V Flip', 'Camon 20 Pro', 'Camon 20', 'Spark 10 Pro', 'Spark 10', 'Pova 5 Pro'],
      storage: ['64GB', '128GB', '256GB']
    },
    'Infinix': {
      models: ['Zero 30', 'Note 30 Pro', 'Note 30', 'Hot 30', 'Smart 8 Pro', 'Smart 8'],
      storage: ['64GB', '128GB', '256GB']
    }
  };

  const conditions = [
    { value: 'excellent', label: 'Excellent', description: 'Like new, no scratches or damage', multiplier: 1.0 },
    { value: 'good', label: 'Good', description: 'Minor scratches, fully functional', multiplier: 0.85 },
    { value: 'fair', label: 'Fair', description: 'Visible wear, some scratches, works well', multiplier: 0.70 },
    { value: 'poor', label: 'Poor', description: 'Heavy wear, cracks, but still works', multiplier: 0.50 },
    { value: 'broken', label: 'Broken', description: 'Damaged, not working properly', multiplier: 0.25 }
  ];

  const accessories = [
    { id: 'charger', label: 'Original Charger', value: 15000 },
    { id: 'box', label: 'Original Box', value: 10000 },
    { id: 'earphones', label: 'Original Earphones', value: 25000 },
    { id: 'case', label: 'Phone Case', value: 5000 },
    { id: 'screen_protector', label: 'Screen Protector', value: 3000 }
  ];

  // Base values for different phone models (in TZS)
  const getBaseValue = (brand, model, storage) => {
    const baseValues = {
      'Apple': {
        'iPhone 15 Pro Max': { '128GB': 2800000, '256GB': 3200000, '512GB': 3800000, '1TB': 4400000 },
        'iPhone 15 Pro': { '128GB': 2400000, '256GB': 2800000, '512GB': 3400000, '1TB': 4000000 },
        'iPhone 15': { '128GB': 2000000, '256GB': 2300000, '512GB': 2800000 },
        'iPhone 14 Pro Max': { '128GB': 2200000, '256GB': 2600000, '512GB': 3200000, '1TB': 3800000 },
        'iPhone 14 Pro': { '128GB': 1900000, '256GB': 2300000, '512GB': 2900000, '1TB': 3500000 },
        'iPhone 14': { '128GB': 1600000, '256GB': 1900000, '512GB': 2400000 },
        'iPhone 13 Pro Max': { '128GB': 1800000, '256GB': 2200000, '512GB': 2800000, '1TB': 3400000 },
        'iPhone 13 Pro': { '128GB': 1500000, '256GB': 1900000, '512GB': 2500000, '1TB': 3100000 },
        'iPhone 13': { '128GB': 1200000, '256GB': 1500000, '512GB': 2000000 },
        'iPhone 12 Pro Max': { '128GB': 1400000, '256GB': 1800000, '512GB': 2400000 },
        'iPhone 12 Pro': { '128GB': 1200000, '256GB': 1600000, '512GB': 2200000 },
        'iPhone 12': { '128GB': 1000000, '256GB': 1300000, '512GB': 1800000 }
      },
      'Samsung': {
        'Galaxy S24 Ultra': { '256GB': 2600000, '512GB': 3000000, '1TB': 3600000 },
        'Galaxy S24+': { '256GB': 2200000, '512GB': 2600000 },
        'Galaxy S24': { '128GB': 1800000, '256GB': 2100000, '512GB': 2500000 },
        'Galaxy S23 Ultra': { '256GB': 2200000, '512GB': 2600000, '1TB': 3200000 },
        'Galaxy S23+': { '256GB': 1800000, '512GB': 2200000 },
        'Galaxy S23': { '128GB': 1500000, '256GB': 1800000, '512GB': 2200000 },
        'Galaxy A54': { '128GB': 650000, '256GB': 750000 },
        'Galaxy A34': { '128GB': 550000, '256GB': 650000 },
        'Galaxy A24': { '128GB': 450000, '256GB': 550000 },
        'Galaxy Note 20 Ultra': { '256GB': 1400000, '512GB': 1800000 }
      },
      'Xiaomi': {
        'Redmi Note 13 Pro': { '128GB': 520000, '256GB': 620000 },
        'Redmi Note 13': { '128GB': 420000, '256GB': 520000 },
        'Redmi Note 12 Pro': { '128GB': 450000, '256GB': 550000 },
        'Redmi Note 12': { '128GB': 350000, '256GB': 450000 },
        'Mi 13 Ultra': { '256GB': 1800000, '512GB': 2200000 },
        'Mi 13 Pro': { '256GB': 1500000, '512GB': 1900000 },
        'Mi 13': { '128GB': 1200000, '256GB': 1500000 },
        'Poco X5 Pro': { '128GB': 650000, '256GB': 750000 },
        'Poco F5': { '256GB': 850000, '512GB': 1050000 }
      },
      'Oppo': {
        'Reno 10 Pro': { '256GB': 950000, '512GB': 1150000 },
        'Reno 10': { '128GB': 750000, '256GB': 850000 },
        'Find X6 Pro': { '256GB': 1600000, '512GB': 2000000 },
        'Find X6': { '256GB': 1300000, '512GB': 1700000 },
        'A98': { '256GB': 650000, '512GB': 750000 },
        'A78': { '128GB': 450000, '256GB': 550000 },
        'A58': { '128GB': 350000, '256GB': 450000 }
      },
      'Tecno': {
        'Phantom V Fold': { '256GB': 2200000, '512GB': 2600000 },
        'Phantom V Flip': { '256GB': 1400000, '512GB': 1800000 },
        'Camon 20 Pro': { '256GB': 550000, '512GB': 650000 },
        'Camon 20': { '128GB': 450000, '256GB': 550000 },
        'Spark 10 Pro': { '128GB': 320000, '256GB': 420000 },
        'Spark 10': { '64GB': 250000, '128GB': 320000 },
        'Pova 5 Pro': { '256GB': 480000, '512GB': 580000 }
      },
      'Infinix': {
        'Zero 30': { '256GB': 650000, '512GB': 750000 },
        'Note 30 Pro': { '256GB': 520000, '512GB': 620000 },
        'Note 30': { '128GB': 420000, '256GB': 520000 },
        'Hot 30': { '128GB': 320000, '256GB': 420000 },
        'Smart 8 Pro': { '128GB': 280000, '256GB': 350000 },
        'Smart 8': { '64GB': 220000, '128GB': 280000 }
      }
    };

    return baseValues[brand]?.[model]?.[storage] || 0;
  };

  const calculateTradeValue = () => {
    const baseValue = getBaseValue(deviceInfo.brand, deviceInfo.model, deviceInfo.storage);
    if (!baseValue) return 0;

    const condition = conditions.find(c => c.value === deviceInfo.condition);
    const conditionMultiplier = condition ? condition.multiplier : 0.5;

    const accessoryValue = deviceInfo.accessories.reduce((sum, accId) => {
      const accessory = accessories.find(a => a.id === accId);
      return sum + (accessory ? accessory.value : 0);
    }, 0);

    // Trade-in value is typically 60-70% of current market value
    const tradeMultiplier = 0.65;
    const finalValue = (baseValue * conditionMultiplier * tradeMultiplier) + accessoryValue;

    return Math.round(finalValue);
  };

  const handleInputChange = (field, value) => {
    setDeviceInfo(prev => ({
      ...prev,
      [field]: value
    }));

    // Reset dependent fields when parent changes
    if (field === 'brand') {
      setDeviceInfo(prev => ({
        ...prev,
        model: '',
        storage: ''
      }));
    } else if (field === 'model') {
      setDeviceInfo(prev => ({
        ...prev,
        storage: ''
      }));
    }
  };

  const handleAccessoryChange = (accessoryId, checked) => {
    setDeviceInfo(prev => ({
      ...prev,
      accessories: checked 
        ? [...prev.accessories, accessoryId]
        : prev.accessories.filter(id => id !== accessoryId)
    }));
  };

  const handleGetValuation = () => {
    if (!deviceInfo.brand || !deviceInfo.model || !deviceInfo.storage || !deviceInfo.condition) {
      setError('Please fill in all required fields');
      return;
    }

    setLoading(true);
    
    // Simulate API call
    setTimeout(() => {
      const value = calculateTradeValue();
      setValuation({
        estimatedValue: value,
        deviceInfo: { ...deviceInfo },
        validUntil: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
        quoteId: 'TI' + Date.now().toString().slice(-8)
      });
      setStep(2);
      setLoading(false);
    }, 1500);
  };

  const handleStartTradeIn = () => {
    if (!user) {
      setError('Please log in to proceed with trade-in');
      return;
    }
    setStep(3);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-gradient-to-r from-green-600 to-green-800 text-white">
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold">Trade-In Program 🔄</h1>
              <p className="text-green-100 mt-2">Get instant value for your old phone</p>
            </div>
            <div className="text-right">
              <Link to="/dashboard">
                <Button className="bg-white text-green-600 hover:bg-gray-100">
                  <i className="fas fa-arrow-left mr-2"></i>
                  Back to Dashboard
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        {error && (
          <Alert 
            type="error" 
            message={error} 
            onClose={() => setError('')}
            className="mb-6"
          />
        )}

        {/* Progress Steps */}
        <div className="mb-8">
          <div className="flex items-center justify-center space-x-4">
            <div className={`flex items-center ${step >= 1 ? 'text-green-600' : 'text-gray-400'}`}>
              <div className={`w-8 h-8 rounded-full flex items-center justify-center ${step >= 1 ? 'bg-green-600 text-white' : 'bg-gray-300'}`}>
                1
              </div>
              <span className="ml-2 font-medium">Device Info</span>
            </div>
            <div className="w-16 h-1 bg-gray-300"></div>
            <div className={`flex items-center ${step >= 2 ? 'text-green-600' : 'text-gray-400'}`}>
              <div className={`w-8 h-8 rounded-full flex items-center justify-center ${step >= 2 ? 'bg-green-600 text-white' : 'bg-gray-300'}`}>
                2
              </div>
              <span className="ml-2 font-medium">Valuation</span>
            </div>
            <div className="w-16 h-1 bg-gray-300"></div>
            <div className={`flex items-center ${step >= 3 ? 'text-green-600' : 'text-gray-400'}`}>
              <div className={`w-8 h-8 rounded-full flex items-center justify-center ${step >= 3 ? 'bg-green-600 text-white' : 'bg-gray-300'}`}>
                3
              </div>
              <span className="ml-2 font-medium">Complete</span>
            </div>
          </div>
        </div>

        {/* Step 1: Device Information */}
        {step === 1 && (
          <div className="max-w-2xl mx-auto">
            <div className="bg-white rounded-lg shadow-md p-8">
              <h2 className="text-2xl font-bold mb-6 text-center">Tell us about your device</h2>
              
              <div className="space-y-6">
                {/* Brand Selection */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Phone Brand *
                  </label>
                  <select
                    value={deviceInfo.brand}
                    onChange={(e) => handleInputChange('brand', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                  >
                    <option value="">Select brand</option>
                    {Object.keys(phoneDatabase).map(brand => (
                      <option key={brand} value={brand}>{brand}</option>
                    ))}
                  </select>
                </div>

                {/* Model Selection */}
                {deviceInfo.brand && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Phone Model *
                    </label>
                    <select
                      value={deviceInfo.model}
                      onChange={(e) => handleInputChange('model', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                    >
                      <option value="">Select model</option>
                      {phoneDatabase[deviceInfo.brand].models.map(model => (
                        <option key={model} value={model}>{model}</option>
                      ))}
                    </select>
                  </div>
                )}

                {/* Storage Selection */}
                {deviceInfo.model && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Storage Capacity *
                    </label>
                    <select
                      value={deviceInfo.storage}
                      onChange={(e) => handleInputChange('storage', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                    >
                      <option value="">Select storage</option>
                      {phoneDatabase[deviceInfo.brand].storage.map(storage => (
                        <option key={storage} value={storage}>{storage}</option>
                      ))}
                    </select>
                  </div>
                )}

                {/* Condition Assessment */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Device Condition *
                  </label>
                  <div className="space-y-3">
                    {conditions.map(condition => (
                      <label key={condition.value} className="flex items-start space-x-3 cursor-pointer">
                        <input
                          type="radio"
                          name="condition"
                          value={condition.value}
                          checked={deviceInfo.condition === condition.value}
                          onChange={(e) => handleInputChange('condition', e.target.value)}
                          className="mt-1 text-green-600 focus:ring-green-500"
                        />
                        <div>
                          <div className="font-medium">{condition.label}</div>
                          <div className="text-sm text-gray-500">{condition.description}</div>
                        </div>
                      </label>
                    ))}
                  </div>
                </div>

                {/* IMEI */}
                <div>
                  <Input
                    label="IMEI Number (Optional)"
                    placeholder="Enter 15-digit IMEI"
                    value={deviceInfo.imei}
                    onChange={(e) => handleInputChange('imei', e.target.value)}
                    maxLength={15}
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Dial *#06# to find your IMEI number
                  </p>
                </div>

                {/* Accessories */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Included Accessories (increases value)
                  </label>
                  <div className="space-y-2">
                    {accessories.map(accessory => (
                      <label key={accessory.id} className="flex items-center space-x-3 cursor-pointer">
                        <input
                          type="checkbox"
                          checked={deviceInfo.accessories.includes(accessory.id)}
                          onChange={(e) => handleAccessoryChange(accessory.id, e.target.checked)}
                          className="text-green-600 focus:ring-green-500"
                        />
                        <span className="flex-1">{accessory.label}</span>
                        <span className="text-sm text-green-600">+{formatPrice(accessory.value)}</span>
                      </label>
                    ))}
                  </div>
                </div>

                <Button
                  onClick={handleGetValuation}
                  disabled={loading || !deviceInfo.brand || !deviceInfo.model || !deviceInfo.storage || !deviceInfo.condition}
                  className="w-full bg-green-600 hover:bg-green-700"
                >
                  {loading ? (
                    <>
                      <i className="fas fa-spinner fa-spin mr-2"></i>
                      Calculating Value...
                    </>
                  ) : (
                    <>
                      <i className="fas fa-calculator mr-2"></i>
                      Get Trade-In Value
                    </>
                  )}
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Step 2: Valuation Results */}
        {step === 2 && valuation && (
          <div className="max-w-2xl mx-auto">
            <div className="bg-white rounded-lg shadow-md p-8">
              <div className="text-center mb-8">
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <i className="fas fa-check text-green-600 text-2xl"></i>
                </div>
                <h2 className="text-2xl font-bold mb-2">Your Trade-In Value</h2>
                <div className="text-4xl font-bold text-green-600 mb-2">
                  {formatPrice(valuation.estimatedValue)}
                </div>
                <p className="text-gray-600">
                  Quote valid until {valuation.validUntil.toLocaleDateString('sw-TZ')}
                </p>
                <p className="text-sm text-gray-500">
                  Quote ID: {valuation.quoteId}
                </p>
              </div>

              {/* Device Summary */}
              <div className="bg-gray-50 rounded-lg p-6 mb-6">
                <h3 className="font-semibold mb-4">Device Summary</h3>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-500">Brand:</span>
                    <span className="ml-2 font-medium">{valuation.deviceInfo.brand}</span>
                  </div>
                  <div>
                    <span className="text-gray-500">Model:</span>
                    <span className="ml-2 font-medium">{valuation.deviceInfo.model}</span>
                  </div>
                  <div>
                    <span className="text-gray-500">Storage:</span>
                    <span className="ml-2 font-medium">{valuation.deviceInfo.storage}</span>
                  </div>
                  <div>
                    <span className="text-gray-500">Condition:</span>
                    <span className="ml-2 font-medium capitalize">{valuation.deviceInfo.condition}</span>
                  </div>
                </div>
                
                {valuation.deviceInfo.accessories.length > 0 && (
                  <div className="mt-4">
                    <span className="text-gray-500">Accessories:</span>
                    <div className="mt-1">
                      {valuation.deviceInfo.accessories.map(accId => {
                        const accessory = accessories.find(a => a.id === accId);
                        return accessory ? (
                          <span key={accId} className="inline-block bg-green-100 text-green-800 text-xs px-2 py-1 rounded mr-2 mb-1">
                            {accessory.label}
                          </span>
                        ) : null;
                      })}
                    </div>
                  </div>
                )}
              </div>

              {/* Next Steps */}
              <div className="space-y-4">
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <h4 className="font-semibold text-blue-800 mb-2">How it works:</h4>
                  <ol className="text-sm text-blue-700 space-y-1">
                    <li>1. Bring your device to our Kariakoo store</li>
                    <li>2. Our technician will verify the condition</li>
                    <li>3. Get instant credit towards your new phone purchase</li>
                    <li>4. Pay only the difference for your upgrade</li>
                  </ol>
                </div>

                <div className="flex space-x-4">
                  <Button
                    onClick={() => setStep(1)}
                    variant="outline"
                    className="flex-1"
                  >
                    <i className="fas fa-arrow-left mr-2"></i>
                    Back
                  </Button>
                  <Button
                    onClick={handleStartTradeIn}
                    className="flex-1 bg-green-600 hover:bg-green-700"
                  >
                    <i className="fas fa-arrow-right mr-2"></i>
                    Start Trade-In
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Step 3: Complete Trade-In */}
        {step === 3 && (
          <div className="max-w-2xl mx-auto">
            <div className="bg-white rounded-lg shadow-md p-8 text-center">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <i className="fas fa-handshake text-green-600 text-2xl"></i>
              </div>
              <h2 className="text-2xl font-bold mb-4">Trade-In Request Submitted!</h2>
              <p className="text-gray-600 mb-6">
                Your trade-in quote has been saved. Visit our store in Kariakoo to complete the process.
              </p>

              <div className="bg-gray-50 rounded-lg p-6 mb-6">
                <h3 className="font-semibold mb-4">What to bring:</h3>
                <ul className="text-left text-sm space-y-2">
                  <li className="flex items-center">
                    <i className="fas fa-check text-green-600 mr-2"></i>
                    Your device and selected accessories
                  </li>
                  <li className="flex items-center">
                    <i className="fas fa-check text-green-600 mr-2"></i>
                    Valid ID (National ID or Passport)
                  </li>
                  <li className="flex items-center">
                    <i className="fas fa-check text-green-600 mr-2"></i>
                    Quote ID: {valuation?.quoteId}
                  </li>
                  <li className="flex items-center">
                    <i className="fas fa-check text-green-600 mr-2"></i>
                    Original purchase receipt (if available)
                  </li>
                </ul>
              </div>

              <div className="space-y-4">
                <Link to="/store-locator">
                  <Button className="w-full bg-green-600 hover:bg-green-700">
                    <i className="fas fa-map-marker-alt mr-2"></i>
                    Find Our Store
                  </Button>
                </Link>
                <Link to="/products">
                  <Button variant="outline" className="w-full">
                    <i className="fas fa-mobile-alt mr-2"></i>
                    Browse New Phones
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default TradeInPage;
