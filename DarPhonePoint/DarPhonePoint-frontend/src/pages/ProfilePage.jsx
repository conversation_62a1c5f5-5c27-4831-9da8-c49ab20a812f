import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import Button from '../components/ui/Button';
import Input from '../components/ui/Input';
import PasswordInput from '../components/ui/PasswordInput';
import Alert from '../components/ui/Alert';

const ProfilePage = () => {
  const { user, updateProfile, error: authError, setError: setAuthError } = useAuth();

  const [formData, setFormData] = useState({
    name: user?.name || '',
    email: user?.email || '',
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
  });

  const [formErrors, setFormErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [success, setSuccess] = useState(false);

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });

    // Clear field error when user types
    if (formErrors[e.target.name]) {
      setFormErrors({
        ...formErrors,
        [e.target.name]: '',
      });
    }

    // Clear success message when user makes changes
    if (success) {
      setSuccess(false);
    }
  };

  const validateForm = () => {
    const errors = {};

    if (!formData.name) {
      errors.name = 'Name is required';
    }

    if (!formData.email) {
      errors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      errors.email = 'Email is invalid';
    }

    // Only validate password fields if the user is trying to change password
    if (formData.newPassword || formData.confirmPassword || formData.currentPassword) {
      if (!formData.currentPassword) {
        errors.currentPassword = 'Current password is required to change password';
      }

      if (formData.newPassword && formData.newPassword.length < 8) {
        errors.newPassword = 'New password must be at least 8 characters';
      } else if (formData.newPassword && !/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(formData.newPassword)) {
        errors.newPassword = 'Password must contain at least one uppercase letter, one lowercase letter, and one number';
      }

      if (formData.newPassword !== formData.confirmPassword) {
        errors.confirmPassword = 'Passwords do not match';
      }
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    setAuthError('');

    // Prepare data for API
    const updateData = {
      name: formData.name,
      email: formData.email,
    };

    // Only include password fields if the user is changing password
    if (formData.currentPassword && formData.newPassword) {
      updateData.currentPassword = formData.currentPassword;
      updateData.newPassword = formData.newPassword;
    }

    const success = await updateProfile(updateData);

    setIsSubmitting(false);

    if (success) {
      setSuccess(true);
      // Clear password fields
      setFormData({
        ...formData,
        currentPassword: '',
        newPassword: '',
        confirmPassword: '',
      });
    }
  };

  return (
    <div className="bg-gray-50 py-12">
      <div className="container mx-auto px-4">
        <div className="max-w-3xl mx-auto">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
            <div>
              <h1 className="text-3xl font-bold mb-2">Your Profile</h1>
              <p className="text-gray-600">Update your account information</p>
            </div>
            <div className="mt-4 md:mt-0">
              <Link to="/dashboard">
                <Button variant="outline">
                  Back to Dashboard
                </Button>
              </Link>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-md overflow-hidden">
            <div className="p-6">
              {success && (
                <Alert
                  type="success"
                  message="Your profile has been updated successfully."
                  onClose={() => setSuccess(false)}
                  className="mb-6"
                />
              )}

              {authError && (
                <Alert
                  type="error"
                  message={authError}
                  onClose={() => setAuthError('')}
                  className="mb-6"
                />
              )}

              <form onSubmit={handleSubmit}>
                <div className="mb-6">
                  <h2 className="text-lg font-medium mb-4">Personal Information</h2>
                  <Input
                    label="Full Name"
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleChange}
                    error={formErrors.name}
                    required
                  />

                  <Input
                    label="Email Address"
                    id="email"
                    name="email"
                    type="email"
                    value={formData.email}
                    onChange={handleChange}
                    error={formErrors.email}
                    required
                  />
                </div>

                <div className="mb-6">
                  <h2 className="text-lg font-medium mb-4">Change Password</h2>
                  <p className="text-sm text-gray-500 mb-4">
                    Leave these fields blank if you don't want to change your password.
                  </p>

                  <PasswordInput
                    label="Current Password"
                    id="currentPassword"
                    name="currentPassword"
                    value={formData.currentPassword}
                    onChange={handleChange}
                    error={formErrors.currentPassword}
                    showStrengthIndicator={false}
                    showRequirements={false}
                  />

                  <PasswordInput
                    label="New Password"
                    id="newPassword"
                    name="newPassword"
                    value={formData.newPassword}
                    onChange={handleChange}
                    error={formErrors.newPassword}
                    showStrengthIndicator={true}
                    showRequirements={true}
                  />

                  <PasswordInput
                    label="Confirm New Password"
                    id="confirmPassword"
                    name="confirmPassword"
                    value={formData.confirmPassword}
                    onChange={handleChange}
                    error={formErrors.confirmPassword}
                    showStrengthIndicator={false}
                    showRequirements={false}
                  />
                </div>

                <div className="flex justify-end">
                  <Button
                    type="submit"
                    isLoading={isSubmitting}
                  >
                    Save Changes
                  </Button>
                </div>
              </form>
            </div>
          </div>

          <div className="mt-8 bg-white rounded-lg shadow-md overflow-hidden">
            <div className="p-6">
              <h2 className="text-lg font-medium mb-4">Account Settings</h2>

              <div className="mb-6">
                <h3 className="text-md font-medium mb-2">Email Notifications</h3>
                <div className="flex items-center">
                  <input
                    id="marketing-emails"
                    name="marketing-emails"
                    type="checkbox"
                    className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
                    defaultChecked
                  />
                  <label htmlFor="marketing-emails" className="ml-2 block text-sm text-gray-900">
                    Receive marketing emails and newsletters
                  </label>
                </div>
                <div className="flex items-center mt-2">
                  <input
                    id="product-updates"
                    name="product-updates"
                    type="checkbox"
                    className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
                    defaultChecked
                  />
                  <label htmlFor="product-updates" className="ml-2 block text-sm text-gray-900">
                    Receive product updates and announcements
                  </label>
                </div>
              </div>

              <div className="border-t border-gray-200 pt-6">
                <h3 className="text-md font-medium text-red-600 mb-2">Danger Zone</h3>
                <p className="text-sm text-gray-500 mb-4">
                  Once you delete your account, there is no going back. Please be certain.
                </p>
                <Button
                  variant="danger"
                  onClick={() => alert('This feature is not implemented in the demo.')}
                >
                  Delete Account
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProfilePage;
