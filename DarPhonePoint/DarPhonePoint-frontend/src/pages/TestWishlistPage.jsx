import React, { useState, useEffect } from 'react';
import { 
  addToGuestWishlist, 
  removeFromGuestWishlist, 
  getGuestWishlist,
  clearGuestWishlist 
} from '../services/wishlistService';
import Button from '../components/ui/Button';

const TestWishlistPage = () => {
  const [wishlist, setWishlist] = useState({ items: [] });
  const [error, setError] = useState('');

  // Test product
  const testProduct = {
    _id: '68775e53849a0097534ea98f',
    name: 'iPhone 15 Pro Max',
    brand: 'Apple',
    price: 2800000,
    image: '/api/placeholder/300/300',
    in_stock: true
  };

  useEffect(() => {
    loadWishlist();
  }, []);

  const loadWishlist = () => {
    try {
      const guestWishlist = getGuestWishlist();
      setWishlist(guestWishlist);
      setError('');
    } catch (err) {
      setError('Failed to load wishlist: ' + err.message);
    }
  };

  const handleAddToWishlist = () => {
    try {
      addToGuestWishlist(testProduct);
      loadWishlist();
      setError('');
    } catch (err) {
      setError('Failed to add to wishlist: ' + err.message);
    }
  };

  const handleRemoveFromWishlist = () => {
    try {
      removeFromGuestWishlist(testProduct._id);
      loadWishlist();
      setError('');
    } catch (err) {
      setError('Failed to remove from wishlist: ' + err.message);
    }
  };

  const handleClearWishlist = () => {
    try {
      clearGuestWishlist();
      loadWishlist();
      setError('');
    } catch (err) {
      setError('Failed to clear wishlist: ' + err.message);
    }
  };

  const isInWishlist = wishlist.items.some(item => item._id === testProduct._id);

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-6">Wishlist Test Page</h1>
      
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}

      <div className="bg-white rounded-lg shadow-md p-6 mb-6">
        <h2 className="text-xl font-semibold mb-4">Test Product</h2>
        <div className="flex items-center gap-4 mb-4">
          <img
            src={testProduct.image}
            alt={testProduct.name}
            className="w-20 h-20 object-cover rounded"
          />
          <div>
            <h3 className="font-semibold">{testProduct.name}</h3>
            <p className="text-gray-600">{testProduct.brand}</p>
            <p className="text-blue-600 font-bold">TZS {testProduct.price.toLocaleString()}</p>
          </div>
        </div>
        
        <div className="flex gap-2">
          <Button
            onClick={handleAddToWishlist}
            disabled={isInWishlist}
            className="bg-red-500 hover:bg-red-600"
          >
            <i className="fas fa-heart mr-2"></i>
            {isInWishlist ? 'Already in Wishlist' : 'Add to Wishlist'}
          </Button>
          
          <Button
            onClick={handleRemoveFromWishlist}
            disabled={!isInWishlist}
            variant="outline"
          >
            <i className="fas fa-trash mr-2"></i>
            Remove from Wishlist
          </Button>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold">Current Wishlist ({wishlist.items.length} items)</h2>
          <Button
            onClick={handleClearWishlist}
            disabled={wishlist.items.length === 0}
            variant="outline"
            className="text-red-600 border-red-300"
          >
            Clear All
          </Button>
        </div>
        
        {wishlist.items.length === 0 ? (
          <p className="text-gray-500 text-center py-8">No items in wishlist</p>
        ) : (
          <div className="space-y-4">
            {wishlist.items.map((item) => (
              <div key={item._id} className="flex items-center gap-4 p-4 border rounded">
                <img
                  src={item.image}
                  alt={item.name}
                  className="w-16 h-16 object-cover rounded"
                />
                <div className="flex-1">
                  <h3 className="font-semibold">{item.name}</h3>
                  <p className="text-gray-600">{item.brand}</p>
                  <p className="text-blue-600 font-bold">TZS {item.price.toLocaleString()}</p>
                </div>
                <Button
                  onClick={() => {
                    try {
                      removeFromGuestWishlist(item._id);
                      loadWishlist();
                    } catch (err) {
                      setError('Failed to remove item: ' + err.message);
                    }
                  }}
                  variant="outline"
                  size="sm"
                  className="text-red-600"
                >
                  <i className="fas fa-times"></i>
                </Button>
              </div>
            ))}
          </div>
        )}
      </div>

      <div className="mt-6 bg-gray-100 rounded-lg p-4">
        <h3 className="font-semibold mb-2">Debug Info:</h3>
        <pre className="text-sm text-gray-700 overflow-auto">
          {JSON.stringify(wishlist, null, 2)}
        </pre>
      </div>
    </div>
  );
};

export default TestWishlistPage;
