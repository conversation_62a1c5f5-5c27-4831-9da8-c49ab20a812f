import React, { useState, useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';
import axios from 'axios';
import { API_BASE_URL } from '../config';

/**
 * Unsubscribe Page
 * Allows users to unsubscribe from email sequences
 */
const UnsubscribePage = () => {
  const [searchParams] = useSearchParams();
  const token = searchParams.get('token');
  const email = searchParams.get('email');
  const sequenceId = searchParams.get('sequence_id');

  const [loading, setLoading] = useState(true);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState('');
  const [showFeedbackForm, setShowFeedbackForm] = useState(false);
  const [reason, setReason] = useState('');
  const [feedback, setFeedback] = useState('');
  const [sequenceName, setSequenceName] = useState('');

  useEffect(() => {
    const unsubscribe = async () => {
      try {
        setLoading(true);

        if (!token || !email || !sequenceId) {
          setError('Invalid unsubscribe link. Please check your email and try again.');
          setLoading(false);
          return;
        }

        const response = await axios.get(`${API_BASE_URL}/unsubscribe/${token}?email=${encodeURIComponent(email)}&sequence_id=${sequenceId}`);

        setSuccess(true);
        setShowFeedbackForm(true);

        // Try to get sequence name
        try {
          const sequenceResponse = await axios.get(`${API_BASE_URL}/email-sequences/${sequenceId}`);
          setSequenceName(sequenceResponse.data.data.name);
        } catch (err) {
          console.error('Error fetching sequence name:', err);
        }

        setLoading(false);
      } catch (err) {
        console.error('Error unsubscribing:', err);
        setError(err.response?.data?.message || 'An error occurred while unsubscribing. Please try again later.');
        setLoading(false);
      }
    };

    unsubscribe();
  }, [token, email, sequenceId]);

  const handleSubmitFeedback = async (e) => {
    e.preventDefault();

    try {
      await axios.post(`${API_BASE_URL}/unsubscribe/feedback`, {
        email,
        sequence_id: sequenceId,
        reason,
        feedback
      });

      setShowFeedbackForm(false);
      setSuccess(true);
    } catch (err) {
      console.error('Error submitting feedback:', err);
      setError('An error occurred while submitting your feedback. Please try again later.');
    }
  };

  const handleUnsubscribeAll = async () => {
    try {
      const globalToken = token; // In a real implementation, we would generate a new token

      await axios.post(`${API_BASE_URL}/unsubscribe/all`, {
        email,
        token: globalToken
      });

      setShowFeedbackForm(false);
      setSuccess(true);
    } catch (err) {
      console.error('Error unsubscribing from all sequences:', err);
      setError('An error occurred while unsubscribing from all sequences. Please try again later.');
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
        <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
          <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
            <div className="flex justify-center">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500"></div>
            </div>
            <p className="mt-4 text-center text-sm text-gray-600">
              Processing your unsubscribe request...
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <img
          className="mx-auto h-12 w-auto"
          src="/logo.png"
          alt="AIXcelerate"
        />
        <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
          Email Preferences
        </h2>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
          {error ? (
            <div className="rounded-md bg-red-50 p-4 mb-4">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-red-800">Error</h3>
                  <div className="mt-2 text-sm text-red-700">
                    <p>{error}</p>
                  </div>
                </div>
              </div>
            </div>
          ) : success && !showFeedbackForm ? (
            <div className="rounded-md bg-green-50 p-4 mb-4">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-green-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-green-800">Success</h3>
                  <div className="mt-2 text-sm text-green-700">
                    <p>You have been successfully unsubscribed. Thank you for your feedback!</p>
                  </div>
                </div>
              </div>
            </div>
          ) : null}

          {showFeedbackForm ? (
            <div>
              <p className="text-center text-sm text-gray-600 mb-6">
                You have been unsubscribed from {sequenceName || 'our email sequence'}.
              </p>

              <form onSubmit={handleSubmitFeedback} className="space-y-6">
                <div>
                  <label htmlFor="reason" className="block text-sm font-medium text-gray-700">
                    Why are you unsubscribing?
                  </label>
                  <select
                    id="reason"
                    name="reason"
                    value={reason}
                    onChange={(e) => setReason(e.target.value)}
                    className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-purple-500 focus:border-purple-500 sm:text-sm rounded-md"
                  >
                    <option value="">Select a reason</option>
                    <option value="not_interested">I'm not interested in this content</option>
                    <option value="too_many_emails">I receive too many emails</option>
                    <option value="content_not_relevant">The content is not relevant to me</option>
                    <option value="other">Other</option>
                  </select>
                </div>

                <div>
                  <label htmlFor="feedback" className="block text-sm font-medium text-gray-700">
                    Additional feedback (optional)
                  </label>
                  <textarea
                    id="feedback"
                    name="feedback"
                    rows="3"
                    value={feedback}
                    onChange={(e) => setFeedback(e.target.value)}
                    className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-purple-500 focus:border-purple-500 sm:text-sm"
                  ></textarea>
                </div>

                <div className="flex flex-col space-y-4">
                  <button
                    type="submit"
                    className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500"
                  >
                    Submit Feedback
                  </button>

                  <button
                    type="button"
                    onClick={handleUnsubscribeAll}
                    className="w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500"
                  >
                    Unsubscribe from all emails
                  </button>
                </div>
              </form>
            </div>
          ) : success ? (
            <div className="text-center">
              <p className="text-sm text-gray-600 mb-6">
                You can resubscribe at any time by updating your preferences in your account settings.
              </p>
              <a
                href="/"
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500"
              >
                Return to Homepage
              </a>
            </div>
          ) : null}
        </div>
      </div>
    </div>
  );
};

export default UnsubscribePage;
