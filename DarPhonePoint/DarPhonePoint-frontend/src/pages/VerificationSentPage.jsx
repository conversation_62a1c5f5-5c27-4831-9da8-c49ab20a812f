import React, { useState } from 'react';
import { useLocation, Link, useNavigate } from 'react-router-dom';
import { EnvelopeIcon, ArrowPathIcon } from '@heroicons/react/24/outline';
import { toast } from 'react-hot-toast';
import Button from '../components/ui/Button';

const VerificationSentPage = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const [isResending, setIsResending] = useState(false);

  const email = location.state?.email || '';
  const message = location.state?.message || 'Please check your email to verify your account.';

  const resendVerification = async () => {
    if (!email) {
      toast.error('Email address not available. Please register again.');
      return;
    }

    try {
      setIsResending(true);
      
      const response = await fetch('/api/auth/resend-verification', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      });

      const data = await response.json();

      if (data.success) {
        toast.success('Verification email sent! Please check your inbox.');
      } else {
        toast.error(data.message || 'Failed to resend verification email');
      }
    } catch (error) {
      console.error('Resend error:', error);
      toast.error('Failed to resend verification email');
    } finally {
      setIsResending(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <div className="text-center">
          <h1 className="text-3xl font-bold text-indigo-600">AIXcelerate</h1>
          <p className="mt-2 text-sm text-gray-600">Account Registration</p>
        </div>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
          <div className="text-center">
            <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100 mb-4">
              <EnvelopeIcon className="h-8 w-8 text-green-600" />
            </div>
            
            <h2 className="text-2xl font-bold text-gray-900 mb-2">
              Check Your Email
            </h2>
            
            <p className="text-gray-600 mb-6">
              {message}
            </p>

            {email && (
              <div className="bg-gray-50 rounded-lg p-4 mb-6">
                <p className="text-sm text-gray-600 mb-1">Verification email sent to:</p>
                <p className="font-medium text-gray-900">{email}</p>
              </div>
            )}

            <div className="space-y-4">
              <div className="text-sm text-gray-600">
                <p className="mb-2">
                  <strong>What's next?</strong>
                </p>
                <ol className="text-left space-y-1 list-decimal list-inside">
                  <li>Check your email inbox (and spam folder)</li>
                  <li>Click the verification link in the email</li>
                  <li>Return here to log in to your account</li>
                </ol>
              </div>

              <div className="pt-4 space-y-3">
                {email && (
                  <Button
                    onClick={resendVerification}
                    disabled={isResending}
                    variant="outline"
                    fullWidth
                  >
                    {isResending ? (
                      <>
                        <ArrowPathIcon className="animate-spin -ml-1 mr-2 h-4 w-4" />
                        Sending...
                      </>
                    ) : (
                      'Resend Verification Email'
                    )}
                  </Button>
                )}

                <Link to="/login">
                  <Button variant="primary" fullWidth>
                    Continue to Login
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="mt-8 text-center space-y-2">
        <p className="text-sm text-gray-600">
          Didn't receive the email? Check your spam folder or{' '}
          {email && (
            <button
              onClick={resendVerification}
              disabled={isResending}
              className="text-indigo-600 hover:text-indigo-500 font-medium"
            >
              resend verification email
            </button>
          )}
        </p>
        
        <p className="text-xs text-gray-500">
          Need help? Contact our support team at{' '}
          <a href="mailto:<EMAIL>" className="text-indigo-600 hover:text-indigo-500">
            <EMAIL>
          </a>
        </p>
      </div>
    </div>
  );
};

export default VerificationSentPage;
