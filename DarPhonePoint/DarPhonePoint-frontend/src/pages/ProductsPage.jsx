import React, { useState, useEffect } from 'react';
import { Link, useNavigate, useSearchParams } from 'react-router-dom';
import { products as productsApi } from '../api';
import { get } from '../api/unifiedApiClient';
import Button from '../components/ui/Button';
import Alert from '../components/ui/Alert';
import LoadingState from '../components/ui/LoadingState';
import useApiRequest from '../hooks/useApiRequest';
import { useAuth } from '../contexts/AuthContext';
import { useLanguage } from '../contexts/LanguageContext';
import ProductComparison from '../components/product/ProductComparison';
import {
  addToGuestWishlist,
  isInGuestWishlist,
  removeFromGuestWishlist,
  getGuestWishlistCount
} from '../services/wishlistService';

const ProductsPage = () => {
  const [productsList, setProductsList] = useState([]);
  const [searchParams, setSearchParams] = useSearchParams();
  const [filters, setFilters] = useState({
    category: searchParams.get('category') || '',
    brand: searchParams.get('brand') || '',
    minPrice: searchParams.get('minPrice') || '',
    maxPrice: searchParams.get('maxPrice') || '',
    search: searchParams.get('search') || ''
  });
  const [sortBy, setSortBy] = useState(searchParams.get('sort') || 'name');
  const [viewMode, setViewMode] = useState('grid'); // grid or list
  const [compareProducts, setCompareProducts] = useState([]);
  const [showComparison, setShowComparison] = useState(false);
  const [wishlistItems, setWishlistItems] = useState(new Set());
  const [wishlistLoading, setWishlistLoading] = useState({});

  const navigate = useNavigate();
  const { user } = useAuth();
  const { t } = useLanguage();

  // Use our new API request hook
  const {
    data: productsData,
    error,
    isLoading,
    request: fetchProducts
  } = useApiRequest({
    loadingKey: 'products-page',
    cacheTime: 30 * 1000, // Cache for 30 seconds (shorter cache for dynamic filtering)
    onSuccess: (data) => {
      if (data && data.data) {
        setProductsList(data.data);
      } else {
        setProductsList(data || []);
      }
    },
    onError: (err) => {
      console.error('Error fetching products:', err);
    }
  });

  // Fetch products when filters change (with debouncing)
  useEffect(() => {
    // Add a small delay to prevent too many rapid API calls
    const timeoutId = setTimeout(() => {
      const queryParams = new URLSearchParams();

      Object.entries(filters).forEach(([key, value]) => {
        // Only add non-empty values to query params
        if (value && value.trim() !== '') {
          queryParams.append(key, value);
        }
      });

      if (sortBy && sortBy !== 'name') {
        queryParams.append('sort', sortBy);
      }

      // Request all products (increase limit)
      queryParams.append('limit', '100');

      const queryString = queryParams.toString();
      const url = `/products?${queryString}`;

      fetchProducts({
        method: 'GET',
        url: url
      });

      // Update URL without causing navigation
      setSearchParams(queryParams);
    }, 300); // 300ms delay

    return () => clearTimeout(timeoutId);
  }, [filters, sortBy]); // Removed fetchProducts from dependency array

  // Load wishlist items
  useEffect(() => {
    loadWishlistItems();
  }, [user]);

  const loadWishlistItems = async () => {
    try {
      if (user) {
        // Load user wishlist from server
        const response = await get('/wishlist');

        // Extract the actual wishlist data from response.data.data
        const apiResponse = response.data || {};
        const wishlistData = apiResponse.data || { items: [] };

        const wishlistProductIds = (wishlistData.items || []).map(item =>
          item.product?._id || item.product || item._id
        ).filter(Boolean);
        setWishlistItems(new Set(wishlistProductIds));
      } else {
        // Load guest wishlist from localStorage
        try {
          const guestWishlist = JSON.parse(localStorage.getItem('phonePointDarWishlist') || '{"items":[]}');
          const wishlistProductIds = (guestWishlist.items || []).map(item => item._id).filter(Boolean);
          setWishlistItems(new Set(wishlistProductIds));
        } catch (parseError) {
          console.error('Error parsing guest wishlist:', parseError);
          setWishlistItems(new Set());
        }
      }
    } catch (error) {
      console.error('Error loading wishlist:', error);
      setWishlistItems(new Set());
    }
  };

  // Handle filter changes
  const handleFilterChange = (filterType, value) => {
    setFilters(prev => ({
      ...prev,
      [filterType]: value
    }));
  };

  // Clear all filters
  const clearFilters = () => {
    setFilters({
      category: '',
      brand: '',
      minPrice: '',
      maxPrice: '',
      search: ''
    });
    setSortBy('name');
  };

  // Handle wishlist toggle
  const handleWishlistToggle = async (product) => {
    const productId = product._id;
    const isInWishlist = wishlistItems.has(productId); // Move outside try block
    setWishlistLoading(prev => ({ ...prev, [productId]: true }));

    try {

      if (user) {
        // Authenticated user - use API
        if (isInWishlist) {
          const response = await safeApiRequest({
            method: 'DELETE',
            url: `/wishlist/${productId}`
          });

          setWishlistItems(prev => {
            const newSet = new Set(prev);
            newSet.delete(productId);
            return newSet;
          });
        } else {
          const response = await safeApiRequest({
            method: 'POST',
            url: '/wishlist',
            data: { productId }
          });

          setWishlistItems(prev => new Set([...prev, productId]));
        }
      } else {
        // Guest user - use localStorage
        try {
          if (isInWishlist) {
            removeFromGuestWishlist(productId);
            setWishlistItems(prev => {
              const newSet = new Set(prev);
              newSet.delete(productId);
              return newSet;
            });
          } else {
            addToGuestWishlist(product);
            setWishlistItems(prev => new Set([...prev, productId]));
          }
        } catch (localStorageError) {
          console.error('Error with guest wishlist:', localStorageError);
          console.error('LocalStorage error details:', {
            error: localStorageError.message,
            productId,
            isInWishlist,
            storageAvailable: typeof(Storage) !== "undefined"
          });
          alert('Unable to save to wishlist. Please check your browser settings and ensure cookies/storage are enabled.');
        }
      }
    } catch (error) {
      console.error('Error toggling wishlist:', error);
      console.error('Error details:', {
        message: error.message,
        response: error.response?.data,
        status: error.response?.status,
        user: !!user,
        productId,
        isInWishlist
      });

      // More specific error messages
      if (error.response?.status === 401) {
        alert('Please log in to save items to your wishlist.');
      } else if (error.response?.status === 404) {
        alert('Product not found. Please refresh the page and try again.');
      } else if (error.response?.status === 400) {
        alert(`Invalid request: ${error.response?.data?.message || 'Please check the product data and try again.'}`);
      } else {
        alert(`Failed to update wishlist: ${error.response?.data?.message || error.message}`);
      }
    } finally {
      setWishlistLoading(prev => ({ ...prev, [productId]: false }));
    }
  };

  // Handle product comparison
  const handleCompareToggle = (product) => {
    const productId = product._id;
    const isInComparison = compareProducts.some(p => p._id === productId);

    if (isInComparison) {
      // Remove from comparison
      setCompareProducts(prev => prev.filter(p => p._id !== productId));
    } else {
      // Add to comparison (max 3 products)
      if (compareProducts.length < 3) {
        setCompareProducts(prev => [...prev, product]);
      } else {
        // Show message that max 3 products can be compared
        alert('You can compare up to 3 products at a time. Please remove one to add another.');
      }
    }
  };

  // Show comparison modal
  const handleShowComparison = () => {
    if (compareProducts.length >= 2) {
      setShowComparison(true);
    } else {
      alert('Please select at least 2 products to compare.');
    }
  };

  // Clear comparison
  const handleClearComparison = () => {
    setCompareProducts([]);
  };

  // Format price in TZS
  const formatPrice = (price) => {
    return new Intl.NumberFormat('sw-TZ', {
      style: 'currency',
      currency: 'TZS',
      minimumFractionDigits: 0
    }).format(price);
  };



  // Render product card for grid view
  const renderProductCard = (product) => (
    <div key={product._id} className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300">
      {/* Product Image */}
      <div className="relative">
        <img
          src={product.primary_image?.url || '/api/placeholder/300/300'}
          alt={product.name}
          className="w-full h-64 object-cover"
        />
        {product.is_featured && (
          <span className="absolute top-2 left-2 bg-red-500 text-white px-2 py-1 text-xs font-semibold rounded">
            Featured
          </span>
        )}
        {!product.in_stock && (
          <span className="absolute top-2 right-2 bg-gray-500 text-white px-2 py-1 text-xs font-semibold rounded">
            Out of Stock
          </span>
        )}
      </div>

      <div className="p-4">
        {/* Brand and Category */}
        <div className="flex justify-between items-center mb-2">
          <span className="text-sm text-blue-600 font-medium">{product.brand}</span>
          <span className="text-xs text-gray-500 capitalize">{product.category}</span>
        </div>

        {/* Product Name */}
        <h3 className="text-lg font-semibold mb-2 line-clamp-2">{product.name}</h3>

        {/* Short Description */}
        <p className="text-gray-600 text-sm mb-3 line-clamp-2">
          {product.short_description || product.description?.substring(0, 100)}
        </p>

        {/* Key Specifications */}
        {product.specifications && product.specifications.length > 0 && (
          <div className="mb-3">
            <div className="flex flex-wrap gap-1">
              {product.specifications.slice(0, 3).map((spec, index) => (
                <span
                  key={index}
                  className="bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded"
                >
                  {spec.value}
                </span>
              ))}
            </div>
          </div>
        )}

        {/* Price */}
        <div className="mb-4">
          {product.price_range && product.price_range.min !== product.price_range.max ? (
            <div>
              <span className="text-lg font-bold text-blue-600">
                {formatPrice(product.price_range.min)} - {formatPrice(product.price_range.max)}
              </span>
            </div>
          ) : (
            <div className="flex items-center gap-2">
              <span className="text-xl font-bold text-blue-600">
                {formatPrice(product.price)}
              </span>
              {product.compare_at_price && product.compare_at_price > product.price && (
                <span className="text-sm text-gray-500 line-through">
                  {formatPrice(product.compare_at_price)}
                </span>
              )}
            </div>
          )}
        </div>

        {/* Quick Action Buttons */}
        <div className="flex gap-1 mb-3">
          {/* Wishlist Button */}
          <button
            onClick={() => handleWishlistToggle(product)}
            disabled={wishlistLoading[product._id]}
            className={`p-2 rounded-md transition-colors ${
              wishlistItems.has(product._id)
                ? 'bg-red-100 text-red-600 hover:bg-red-200'
                : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
            }`}
            title={wishlistItems.has(product._id) ? 'Remove from wishlist' : 'Add to wishlist'}
          >
            {wishlistLoading[product._id] ? (
              <i className="fas fa-spinner fa-spin text-sm"></i>
            ) : (
              <i className={`fas fa-heart text-sm ${
                wishlistItems.has(product._id) ? 'text-red-600' : 'text-gray-600'
              }`}></i>
            )}
          </button>

          {/* Compare Button */}
          <button
            onClick={() => handleCompareToggle(product)}
            className={`p-2 rounded-md transition-colors ${
              compareProducts.some(p => p._id === product._id)
                ? 'bg-blue-100 text-blue-600 hover:bg-blue-200'
                : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
            }`}
            title={compareProducts.some(p => p._id === product._id) ? 'Remove from comparison' : 'Add to comparison'}
          >
            <i className={`fas fa-balance-scale text-sm ${
              compareProducts.some(p => p._id === product._id) ? 'text-blue-600' : 'text-gray-600'
            }`}></i>
          </button>
        </div>

        {/* Main Action Buttons */}
        <div>
          <Link to={`/products/${product._id}`} className="block">
            <Button variant="outline" size="sm" fullWidth>
              View Details
            </Button>
          </Link>
        </div>
      </div>
    </div>
  );

  // Render product row for list view
  const renderProductRow = (product) => (
    <div key={product._id} className="bg-white rounded-lg shadow-md p-4 flex items-center gap-4 hover:shadow-lg transition-shadow duration-300">
      {/* Product Image */}
      <div className="relative flex-shrink-0">
        <img
          src={product.primary_image?.url || '/api/placeholder/100/100'}
          alt={product.name}
          className="w-20 h-20 object-cover rounded"
        />
      </div>

      {/* Product Info */}
      <div className="flex-1 min-w-0">
        <div className="flex justify-between items-start mb-2">
          <div>
            <span className="text-sm text-blue-600 font-medium">{product.brand}</span>
            <h3 className="text-lg font-semibold truncate">{product.name}</h3>
          </div>
          <div className="text-right">
            <div className="text-xl font-bold text-blue-600">
              {formatPrice(product.price)}
            </div>
            {product.compare_at_price && product.compare_at_price > product.price && (
              <div className="text-sm text-gray-500 line-through">
                {formatPrice(product.compare_at_price)}
              </div>
            )}
          </div>
        </div>

        <p className="text-gray-600 text-sm mb-2 line-clamp-1">
          {product.short_description || product.description?.substring(0, 150)}
        </p>

        {/* Key specs in list view */}
        {product.specifications && product.specifications.length > 0 && (
          <div className="flex flex-wrap gap-2 mb-2">
            {product.specifications.slice(0, 4).map((spec, index) => (
              <span
                key={index}
                className="bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded"
              >
                {spec.name}: {spec.value}
              </span>
            ))}
          </div>
        )}
      </div>

      {/* Action Buttons */}
      <div className="flex flex-col gap-2 flex-shrink-0">
        {/* Quick Actions */}
        <div className="flex gap-1">
          {/* Wishlist Button */}
          <button
            onClick={() => handleWishlistToggle(product)}
            disabled={wishlistLoading[product._id]}
            className={`p-2 rounded-md transition-colors ${
              wishlistItems.has(product._id)
                ? 'bg-red-100 text-red-600 hover:bg-red-200'
                : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
            }`}
            title={wishlistItems.has(product._id) ? 'Remove from wishlist' : 'Add to wishlist'}
          >
            {wishlistLoading[product._id] ? (
              <i className="fas fa-spinner fa-spin text-sm"></i>
            ) : (
              <i className={`fas fa-heart text-sm ${
                wishlistItems.has(product._id) ? 'text-red-600' : 'text-gray-600'
              }`}></i>
            )}
          </button>

          {/* Compare Button */}
          <button
            onClick={() => handleCompareToggle(product)}
            className={`p-2 rounded-md transition-colors ${
              compareProducts.some(p => p._id === product._id)
                ? 'bg-blue-100 text-blue-600 hover:bg-blue-200'
                : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
            }`}
            title={compareProducts.some(p => p._id === product._id) ? 'Remove from comparison' : 'Add to comparison'}
          >
            <i className={`fas fa-balance-scale text-sm ${
              compareProducts.some(p => p._id === product._id) ? 'text-blue-600' : 'text-gray-600'
            }`}></i>
          </button>
        </div>

        {/* Main Actions */}
        <Link to={`/products/${product._id}`}>
          <Button variant="outline" size="sm" fullWidth>
            View Details
          </Button>
        </Link>
        <Button
          onClick={() => handleAddToCart(product)}
          disabled={!product.in_stock}
          size="sm"
          className="bg-blue-600 hover:bg-blue-700 w-full"
        >
          {product.in_stock ? 'Add to Cart' : 'Out of Stock'}
        </Button>
      </div>
    </div>
  );

  return (
    <div className="bg-gray-50 min-h-screen">
      {/* Header */}
      <div className="bg-white shadow-sm">
        <div className="container mx-auto px-4 py-6">
          <div className="text-center mb-6">
            <h1 className="text-3xl md:text-4xl font-bold mb-2 flex items-center justify-center">
              <i className="fas fa-mobile-alt mr-3 text-blue-600"></i>
              📱 Phone Point Dar
            </h1>
            <p className="text-lg text-gray-600">
              Discover the latest smartphones, tablets, accessories, and tech gadgets in Tanzania
            </p>
            <p className="text-sm text-blue-600 mt-2">
              🇹🇿 Serving Dar es Salaam with quality tech products • M-Pesa & Mobile Money Accepted
            </p>
          </div>

          {/* Search Bar */}
          <div className="max-w-2xl mx-auto mb-6">
            <div className="relative">
              <input
                type="text"
                placeholder="Search for phones, brands, or features..."
                value={filters.search}
                onChange={(e) => handleFilterChange('search', e.target.value)}
                className="w-full px-4 py-3 pl-12 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
              <i className="fas fa-search absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        <div className="flex flex-col lg:flex-row gap-8">
          {/* Sidebar Filters */}
          <div className="lg:w-1/4">
            <div className="bg-white rounded-lg shadow-md p-6 sticky top-4">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-semibold">Filters</h3>
                <button
                  onClick={clearFilters}
                  className="text-blue-600 hover:text-blue-800 text-sm"
                >
                  Clear All
                </button>
              </div>

              {/* Category Filter */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Category
                </label>
                <select
                  value={filters.category}
                  onChange={(e) => handleFilterChange('category', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">All Categories</option>
                  <option value="smartphone">Smartphones</option>
                  <option value="tablet">Tablets</option>
                  <option value="smartwatch">Smartwatches</option>
                  <option value="earbuds">Earbuds</option>
                  <option value="headphones">Headphones</option>
                  <option value="case">Cases</option>
                  <option value="charger">Chargers</option>
                  <option value="cable">Cables</option>
                  <option value="power_bank">Power Banks</option>
                </select>
              </div>

              {/* Brand Filter */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Brand
                </label>
                <select
                  value={filters.brand}
                  onChange={(e) => handleFilterChange('brand', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">All Brands</option>
                  <option value="Apple">Apple</option>
                  <option value="Samsung">Samsung</option>
                  <option value="Google">Google</option>
                  <option value="OnePlus">OnePlus</option>
                  <option value="Xiaomi">Xiaomi</option>
                  <option value="Huawei">Huawei</option>
                  <option value="Oppo">Oppo</option>
                  <option value="Vivo">Vivo</option>
                  <option value="Realme">Realme</option>
                  <option value="Nokia">Nokia</option>
                </select>
              </div>

              {/* Price Range Filter */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Price Range (TZS)
                </label>
                <div className="grid grid-cols-2 gap-2">
                  <input
                    type="number"
                    placeholder="Min"
                    value={filters.minPrice}
                    onChange={(e) => handleFilterChange('minPrice', e.target.value)}
                    className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                  <input
                    type="number"
                    placeholder="Max"
                    value={filters.maxPrice}
                    onChange={(e) => handleFilterChange('maxPrice', e.target.value)}
                    className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </div>

              {/* Quick Price Filters */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Quick Price Filters
                </label>
                <div className="space-y-2">
                  {[
                    { label: 'Under 500K', min: '', max: '500000' },
                    { label: '500K - 1M', min: '500000', max: '1000000' },
                    { label: '1M - 2M', min: '1000000', max: '2000000' },
                    { label: '2M - 3M', min: '2000000', max: '3000000' },
                    { label: 'Over 3M', min: '3000000', max: '' }
                  ].map((range) => (
                    <button
                      key={range.label}
                      onClick={() => {
                        // Update both price filters at once to avoid race conditions
                        setFilters(prev => ({
                          ...prev,
                          minPrice: range.min,
                          maxPrice: range.max
                        }));
                      }}
                      className="w-full text-left px-3 py-2 text-sm bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
                    >
                      {range.label}
                    </button>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="lg:w-3/4">
            {error && (
              <Alert
                type="error"
                message={error.message || 'Failed to load products. Please try again.'}
                className="mb-6"
              />
            )}

            {/* Toolbar */}
            <div className="bg-white rounded-lg shadow-md p-4 mb-6">
              <div className="flex flex-col sm:flex-row justify-between items-center gap-4">
                <div className="flex items-center gap-4">
                  <span className="text-sm text-gray-600">
                    {productsList.length} products found
                  </span>

                  {/* Comparison Counter */}
                  {compareProducts.length > 0 && (
                    <div className="flex items-center gap-2 text-sm text-blue-600">
                      <i className="fas fa-balance-scale"></i>
                      <span>{compareProducts.length} selected for comparison</span>
                      {compareProducts.length >= 2 && (
                        <button
                          onClick={handleShowComparison}
                          className="text-blue-600 hover:text-blue-800 font-medium"
                        >
                          Compare Now
                        </button>
                      )}
                    </div>
                  )}
                </div>

                <div className="flex items-center gap-4">
                  {/* Sort Dropdown */}
                  <select
                    value={sortBy}
                    onChange={(e) => setSortBy(e.target.value)}
                    className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="name">Sort by Name</option>
                    <option value="price">Price: Low to High</option>
                    <option value="-price">Price: High to Low</option>
                    <option value="-created_at">Newest First</option>
                    <option value="brand">Brand</option>
                  </select>

                  {/* View Mode Toggle */}
                  <div className="flex border border-gray-300 rounded-md">
                    <button
                      onClick={() => setViewMode('grid')}
                      className={`px-3 py-2 ${viewMode === 'grid'
                        ? 'bg-blue-600 text-white'
                        : 'bg-white text-gray-700 hover:bg-gray-50'
                      }`}
                    >
                      <i className="fas fa-th"></i>
                    </button>
                    <button
                      onClick={() => setViewMode('list')}
                      className={`px-3 py-2 ${viewMode === 'list'
                        ? 'bg-blue-600 text-white'
                        : 'bg-white text-gray-700 hover:bg-gray-50'
                      }`}
                    >
                      <i className="fas fa-list"></i>
                    </button>
                  </div>
                </div>
              </div>
            </div>

            {/* Products Grid/List */}
            {isLoading ? (
              <LoadingState
                loadingKey="products-page"
                text="Loading products..."
                size="lg"
                type="spinner"
              />
            ) : productsList.length > 0 ? (
              <div className={viewMode === 'grid'
                ? 'grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6'
                : 'space-y-4'
              }>
                {productsList.map(viewMode === 'grid' ? renderProductCard : renderProductRow)}
              </div>
            ) : (
              <div className="text-center py-12 bg-white rounded-lg shadow-md">
                <i className="fas fa-mobile-alt text-blue-400 text-6xl mb-4"></i>
                <h3 className="text-xl font-semibold text-gray-600 mb-2">No tech products found</h3>
                <p className="text-gray-500 mb-4">
                  We're constantly updating our inventory with the latest phones and tech gadgets in Tanzania
                </p>
                <p className="text-sm text-gray-500 mb-4">
                  Try adjusting your filters or search terms, or check back soon!
                </p>
                <Button onClick={clearFilters} variant="outline" className="bg-blue-50 text-blue-700 border-blue-300 hover:bg-blue-100">
                  <i className="fas fa-filter-circle-xmark mr-2"></i>
                  Clear Filters
                </Button>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Floating Comparison Button */}
      {compareProducts.length > 0 && (
        <div className="fixed bottom-6 right-6 z-40">
          <div className="bg-white rounded-lg shadow-lg border p-4 max-w-sm">
            <div className="flex items-center justify-between mb-3">
              <h4 className="font-semibold text-gray-800">
                <i className="fas fa-balance-scale text-blue-600 mr-2"></i>
                Compare ({compareProducts.length})
              </h4>
              <button
                onClick={handleClearComparison}
                className="text-gray-400 hover:text-gray-600"
                title="Clear comparison"
              >
                <i className="fas fa-times"></i>
              </button>
            </div>

            <div className="space-y-2 mb-3">
              {compareProducts.map((product) => (
                <div key={product._id} className="flex items-center gap-2 text-sm">
                  <img
                    src={product.primary_image?.url || '/api/placeholder/30/30'}
                    alt={product.name}
                    className="w-8 h-8 object-cover rounded"
                  />
                  <span className="flex-1 truncate">{product.name}</span>
                  <button
                    onClick={() => handleCompareToggle(product)}
                    className="text-red-500 hover:text-red-700"
                    title="Remove from comparison"
                  >
                    <i className="fas fa-times text-xs"></i>
                  </button>
                </div>
              ))}
            </div>

            <div className="flex gap-2">
              <Button
                onClick={handleShowComparison}
                disabled={compareProducts.length < 2}
                size="sm"
                className="flex-1"
              >
                <i className="fas fa-balance-scale mr-2"></i>
                Compare
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Product Comparison Modal */}
      {showComparison && (
        <ProductComparison
          productIds={compareProducts.map(p => p._id)}
          onClose={() => setShowComparison(false)}
        />
      )}
    </div>
  );
};

export default ProductsPage;
