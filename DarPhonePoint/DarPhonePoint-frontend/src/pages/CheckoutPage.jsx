import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { useLoading } from '../contexts/LoadingContext';
import { get, post } from '../api/unifiedApiClient';
import { formatPrice } from '../utils/priceFormatter';
import Button from '../components/ui/Button';
import LoadingState from '../components/ui/LoadingState';
import Alert from '../components/ui/Alert';
import { useDeviceDetect } from '../utils/mobileOptimization';
import { getGuestCart, clearGuestCart } from '../services/guestCartService';
import CheckoutSteps from '../components/checkout/CheckoutSteps';
import CustomerInfoStep from '../components/checkout/CustomerInfoStep';
import ShippingAddressStep from '../components/checkout/ShippingAddressStep';
import PaymentMethodStep from '../components/checkout/PaymentMethodStep';
import OrderReviewStep from '../components/checkout/OrderReviewStep';

const CHECKOUT_STEPS = [
  { id: 'customer', title: 'Customer Info', icon: 'fas fa-user' },
  { id: 'shipping', title: 'Shipping Address', icon: 'fas fa-truck' },
  { id: 'payment', title: 'Payment Method', icon: 'fas fa-credit-card' },
  { id: 'review', title: 'Review Order', icon: 'fas fa-check-circle' }
];

const CheckoutPage = () => {
  const { user } = useAuth();
  const { setLoading, isLoading } = useLoading();
  const { isMobile, isTablet } = useDeviceDetect();
  const navigate = useNavigate();

  const [currentStep, setCurrentStep] = useState('customer');
  const [cart, setCart] = useState(null);
  const [error, setError] = useState('');
  const [checkoutData, setCheckoutData] = useState({
    customer: {
      email: user?.email || '',
      firstName: user?.firstName || '',
      lastName: user?.lastName || '',
      phone: user?.phone || '',
      isGuest: !user
    },
    shipping: {
      street_address: '',
      city: '',
      state_province: '',
      postal_code: '',
      country: 'Tanzania'
    },
    payment: {
      method: '',
      phone: '', // For mobile money
      details: {}
    }
  });

  useEffect(() => {
    fetchCart();
  }, []);

  const fetchCart = async () => {
    setLoading('checkout', true);
    try {
      // For guest users, get cart from guest cart service
      if (!user) {
        const guestCart = getGuestCart();
        if (!guestCart.items || guestCart.items.length === 0) {
          navigate('/cart');
          return;
        }

        setCart(guestCart);
        setLoading('checkout', false);
        return;
      }

      // For authenticated users, fetch from API
      const response = await safeApiRequest({
        method: 'GET',
        url: '/cart'
      });

      if (!response.data || response.data.items.length === 0) {
        navigate('/cart');
        return;
      }

      setCart(response.data);
    } catch (error) {
      setError('Failed to load cart');
      console.error('Cart fetch error:', error);
    } finally {
      setLoading('checkout', false);
    }
  };

  const updateCheckoutData = (step, data) => {
    setCheckoutData(prev => ({
      ...prev,
      [step]: { ...prev[step], ...data }
    }));
  };

  const goToNextStep = () => {
    if (!canProceedToNext()) {
      return;
    }

    const currentIndex = CHECKOUT_STEPS.findIndex(step => step.id === currentStep);
    if (currentIndex < CHECKOUT_STEPS.length - 1) {
      setCurrentStep(CHECKOUT_STEPS[currentIndex + 1].id);
    }
  };

  const goToPreviousStep = () => {
    const currentIndex = CHECKOUT_STEPS.findIndex(step => step.id === currentStep);
    if (currentIndex > 0) {
      setCurrentStep(CHECKOUT_STEPS[currentIndex - 1].id);
    }
  };

  const canProceedToNext = () => {
    switch (currentStep) {
      case 'customer':
        return !!(checkoutData.customer.email &&
                 checkoutData.customer.firstName &&
                 checkoutData.customer.lastName &&
                 checkoutData.customer.phone);
      case 'shipping':
        return !!(checkoutData.shipping.street_address &&
                 checkoutData.shipping.city &&
                 checkoutData.shipping.state_province);
      case 'payment':
        return !!checkoutData.payment.method;
      default:
        return true;
    }
  };

  const submitOrder = async () => {
    setLoading('order', true);
    try {
      // Transform shipping address to match backend schema
      const shippingAddress = {
        first_name: checkoutData.customer.firstName,
        last_name: checkoutData.customer.lastName,
        address_line_1: checkoutData.shipping.street_address,
        city: checkoutData.shipping.city,
        state: checkoutData.shipping.state_province,
        postal_code: checkoutData.shipping.postal_code || '00000',
        country: checkoutData.shipping.country || 'Tanzania',
        phone: checkoutData.customer.phone
      };

      // Only include address_line_2 if it has a value
      if (checkoutData.shipping.address_line_2 && checkoutData.shipping.address_line_2.trim()) {
        shippingAddress.address_line_2 = checkoutData.shipping.address_line_2.trim();
      }

      const orderData = {
        customer_email: checkoutData.customer.email,
        customer_name: `${checkoutData.customer.firstName} ${checkoutData.customer.lastName}`,
        customer_phone: checkoutData.customer.phone,
        shipping_address: shippingAddress,
        payment_method: checkoutData.payment.method,
        payment_details: checkoutData.payment.details,
        is_guest: !user,
        use_cart: true // Always use cart for both authenticated and guest users
      };

      // Add guest-specific fields for guest checkout
      if (!user) {
        orderData.guest_email = checkoutData.customer.email;
        orderData.guest_name = `${checkoutData.customer.firstName} ${checkoutData.customer.lastName}`;
      }

      // Include cart items in the order request for both guest and authenticated users
      // The backend needs cart_items when use_cart is true
      if (cart && cart.items) {
        orderData.cart_items = cart.items.map(item => ({
          productId: item.product?._id || item.productId || item.product,
          variantSku: item.variant_sku,
          quantity: item.quantity,
          price: item.price,
          imei: item.imei,
          warranty_option: item.warranty_option,
          warranty_price: item.warranty_price,
          warranty_duration: item.warranty_duration,
          trade_in_device: item.trade_in_device,
          trade_in_value: item.trade_in_value,
          accessories: item.accessories,
          device_condition: item.device_condition
        }));
      }

      // If user wants to create an account during guest checkout
      if (!user && checkoutData.customer.createAccount) {
        orderData.create_account = true;
        orderData.password = checkoutData.customer.password;
      }

      // Debug: Log the order data being sent
      console.log('Order data being sent:', JSON.stringify(orderData, null, 2));
      console.log('Customer data:', checkoutData.customer);
      console.log('Cart data:', cart);

      const response = await safeApiRequest({
        method: 'POST',
        url: '/orders',
        data: orderData
      });

      console.log('Order creation response:', response);

      // Clear cart after successful order creation
      if (!user) {
        // For guest users, clear guest cart
        clearGuestCart();
      } else {
        // For authenticated users, clear server cart
        try {
          await safeApiRequest({
            method: 'DELETE',
            url: '/cart'
          });
        } catch (cartError) {
          console.error('Failed to clear cart:', cartError);
          // Don't fail the order process if cart clearing fails
        }
      }

      // Update header cart count
      if (window.refreshCartCount) {
        window.refreshCartCount();
      }

      // Redirect to payment processing or success page
      const orderId = response.data.order_id || response.data._id;
      console.log('Order ID for redirect:', orderId);
      navigate(`/payment/process/${orderId}`);
    } catch (error) {
      setError('Failed to create order. Please try again.');
      console.error('Order creation error:', error);
    } finally {
      setLoading('order', false);
    }
  };

  if (isLoading('checkout')) {
    return <LoadingState message="Loading checkout..." />;
  }

  if (!cart) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Checkout</h1>
          <p className="text-gray-600 mb-4">Your cart is empty</p>
          <Button onClick={() => navigate('/products')}>
            Continue Shopping
          </Button>
        </div>
      </div>
    );
  }

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 'customer':
        return (
          <CustomerInfoStep
            data={checkoutData.customer}
            onUpdate={(data) => updateCheckoutData('customer', data)}
            isGuest={!user}
          />
        );
      case 'shipping':
        return (
          <ShippingAddressStep
            data={checkoutData.shipping}
            onUpdate={(data) => updateCheckoutData('shipping', data)}
          />
        );
      case 'payment':
        return (
          <PaymentMethodStep
            data={checkoutData.payment}
            onUpdate={(data) => updateCheckoutData('payment', data)}
            total={cart.total}
          />
        );
      case 'review':
        return (
          <OrderReviewStep
            cart={cart}
            checkoutData={checkoutData}
            onSubmit={submitOrder}
            isSubmitting={isLoading('order')}
          />
        );
      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-2xl font-bold mb-8 text-center">Checkout</h1>

          {error && (
            <Alert
              type="error"
              message={error}
              onClose={() => setError('')}
              className="mb-6"
            />
          )}

          {/* Progress Steps */}
          <CheckoutSteps
            steps={CHECKOUT_STEPS}
            currentStep={currentStep}
            onStepClick={setCurrentStep}
            isMobile={isMobile}
          />

          <div className={`grid gap-8 mt-8 ${isMobile ? 'grid-cols-1' : 'lg:grid-cols-3'}`}>
            {/* Main Content */}
            <div className="lg:col-span-2">
              <div className="bg-white rounded-lg shadow-sm border p-6">
                {renderCurrentStep()}

                {/* Navigation Buttons */}
                <div className="flex justify-between mt-8 pt-6 border-t">
                  <Button
                    variant="outline"
                    onClick={goToPreviousStep}
                    disabled={currentStep === 'customer'}
                  >
                    <i className="fas fa-arrow-left mr-2"></i>
                    Previous
                  </Button>

                  {currentStep !== 'review' ? (
                    <Button
                      onClick={goToNextStep}
                      disabled={!canProceedToNext()}
                    >
                      Next
                      <i className="fas fa-arrow-right ml-2"></i>
                    </Button>
                  ) : (
                    <Button
                      onClick={submitOrder}
                      disabled={isLoading('order')}
                      className="bg-green-600 hover:bg-green-700"
                    >
                      {isLoading('order') ? (
                        <>
                          <i className="fas fa-spinner fa-spin mr-2"></i>
                          Processing...
                        </>
                      ) : (
                        <>
                          <i className="fas fa-check mr-2"></i>
                          Place Order
                        </>
                      )}
                    </Button>
                  )}
                </div>
              </div>
            </div>

            {/* Order Summary Sidebar */}
            <div className="lg:col-span-1">
              <OrderSummary cart={cart} />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

const OrderSummary = ({ cart }) => {
  return (
    <div className="bg-white rounded-lg shadow-sm border sticky top-4">
      <div className="p-6">
        <h3 className="text-lg font-semibold mb-4">Order Summary</h3>
        
        {/* Cart Items */}
        <div className="space-y-3 mb-4 max-h-60 overflow-y-auto">
          {cart.items.map((item) => {
            const itemId = item._id || item.id;
            const product = item.product || item;
            const productImage = product.image_url || product.primary_image?.url || product.images?.[0]?.url || item.image || '/placeholder-phone.jpg';
            const productName = product.name || item.name;

            return (
              <div key={itemId} className="flex gap-3">
                <img
                  src={productImage}
                  alt={productName}
                  className="w-12 h-12 object-cover rounded"
                />
                <div className="flex-grow">
                  <p className="text-sm font-medium">{productName}</p>
                  <p className="text-xs text-gray-500">Qty: {item.quantity}</p>
                </div>
                <div className="text-sm font-medium">
                  {formatPrice(item.price * item.quantity)}
                </div>
              </div>
            );
          })}
        </div>

        {/* Totals */}
        <div className="space-y-2 pt-4 border-t">
          <div className="flex justify-between text-sm">
            <span>Subtotal</span>
            <span>{formatPrice(cart.subtotal)}</span>
          </div>
          <div className="flex justify-between text-sm">
            <span>Shipping</span>
            <span>{cart.shipping_cost > 0 ? formatPrice(cart.shipping_cost) : 'TBD'}</span>
          </div>
          <div className="flex justify-between text-sm">
            <span>Tax</span>
            <span>{formatPrice(cart.tax_amount)}</span>
          </div>
          <div className="flex justify-between font-semibold text-lg pt-2 border-t">
            <span>Total</span>
            <span className="text-blue-600">{formatPrice(cart.total)}</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CheckoutPage;
