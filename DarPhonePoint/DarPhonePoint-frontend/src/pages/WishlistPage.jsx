import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { useLanguage } from '../contexts/LanguageContext';
import { safeApiRequest } from '../api/apiClient';
import { 
  getGuestWishlist, 
  removeFromGuestWishlist,
  clearGuestWishlist 
} from '../services/wishlistService';
import { formatPrice } from '../utils/priceFormatter';
import Button from '../components/ui/Button';
import Alert from '../components/ui/Alert';
import LoadingState from '../components/ui/LoadingState';

const WishlistPage = () => {
  const [wishlist, setWishlist] = useState({ items: [] });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [actionLoading, setActionLoading] = useState({});

  const { user } = useAuth();
  const { t } = useLanguage();
  const navigate = useNavigate();

  // Safe getter for wishlist items
  const getWishlistItems = () => {
    return wishlist?.items || [];
  };

  useEffect(() => {
    loadWishlist();
  }, [user]);

  const loadWishlist = async () => {
    try {
      setLoading(true);
      setError('');

      if (user) {
        // Load authenticated user's wishlist
        const response = await safeApiRequest({
          method: 'GET',
          url: '/wishlist'
        });

        // Extract the actual wishlist data from response.data.data
        const apiResponse = response.data || {};
        const wishlistData = apiResponse.data || { items: [] };

        const processedWishlist = {
          items: wishlistData.items || [],
          ...wishlistData
        };

        setWishlist(processedWishlist);
      } else {
        // Load guest wishlist
        const guestWishlist = getGuestWishlist();

        // Ensure guest wishlist has proper structure
        const processedWishlist = {
          items: guestWishlist?.items || [],
          ...guestWishlist
        };

        setWishlist(processedWishlist);
      }
    } catch (err) {
      console.error('Error loading wishlist:', err);
      setError('Failed to load wishlist. Please try again.');
      // Set empty wishlist on error
      setWishlist({ items: [] });
    } finally {
      setLoading(false);
    }
  };

  const handleRemoveItem = async (productId) => {
    setActionLoading(prev => ({ ...prev, [productId]: true }));

    try {
      if (user) {
        // Remove from authenticated user's wishlist
        await safeApiRequest({
          method: 'DELETE',
          url: `/wishlist/${productId}`
        });
      } else {
        // Remove from guest wishlist
        removeFromGuestWishlist(productId);
      }

      // Update local state
      setWishlist(prev => ({
        ...prev,
        items: (prev?.items || []).filter(item =>
          (item.product?._id || item._id) !== productId
        )
      }));
    } catch (err) {
      console.error('Error removing item:', err);
      setError('Failed to remove item from wishlist.');
    } finally {
      setActionLoading(prev => ({ ...prev, [productId]: false }));
    }
  };

  const handleClearWishlist = async () => {
    if (!window.confirm('Are you sure you want to clear your entire wishlist?')) {
      return;
    }

    try {
      setLoading(true);

      if (user) {
        // Clear authenticated user's wishlist
        await safeApiRequest({
          method: 'DELETE',
          url: '/wishlist'
        });
      } else {
        // Clear guest wishlist
        clearGuestWishlist();
      }

      setWishlist({ items: [] });
    } catch (err) {
      console.error('Error clearing wishlist:', err);
      setError('Failed to clear wishlist.');
    } finally {
      setLoading(false);
    }
  };

  const handleMoveToCart = async (productIds) => {
    if (!user) {
      // For guest users, redirect to product page to add to cart
      if (productIds.length === 1) {
        navigate(`/products/${productIds[0]}`);
      }
      return;
    }

    try {
      setLoading(true);
      
      await safeApiRequest({
        method: 'POST',
        url: '/wishlist/move-to-cart',
        data: { productIds }
      });

      // Reload wishlist to reflect changes
      await loadWishlist();
      
      // Show success message
      alert(`${productIds.length} item(s) moved to cart successfully!`);
    } catch (err) {
      console.error('Error moving to cart:', err);
      setError('Failed to move items to cart.');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="bg-gray-50 min-h-screen">
        <div className="container mx-auto px-4 py-8">
          <LoadingState message="Loading your wishlist..." />
        </div>
      </div>
    );
  }

  return (
    <div className="bg-gray-50 min-h-screen">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-800 mb-2">
            <i className="fas fa-heart text-red-500 mr-3"></i>
            My Wishlist
          </h1>
          <p className="text-gray-600">
            Save your favorite phones and accessories for later
          </p>
        </div>

        {error && (
          <Alert 
            type="error" 
            message={error} 
            onClose={() => setError('')}
            className="mb-6"
          />
        )}

        {/* Wishlist Content */}
        {getWishlistItems().length === 0 ? (
          <div className="bg-white rounded-lg shadow-md p-12 text-center">
            <i className="fas fa-heart text-gray-300 text-6xl mb-4"></i>
            <h3 className="text-xl font-semibold text-gray-600 mb-2">
              Your wishlist is empty
            </h3>
            <p className="text-gray-500 mb-6">
              Start adding products you love to your wishlist
            </p>
            <Link to="/products">
              <Button>
                <i className="fas fa-shopping-bag mr-2"></i>
                Browse Products
              </Button>
            </Link>
          </div>
        ) : (
          <>
            {/* Wishlist Actions */}
            <div className="bg-white rounded-lg shadow-md p-4 mb-6">
              <div className="flex flex-col sm:flex-row justify-between items-center gap-4">
                <div className="flex items-center gap-4">
                  <span className="text-sm text-gray-600">
                    {getWishlistItems().length} item(s) in your wishlist
                  </span>
                </div>

                <div className="flex gap-2">
                  {user && getWishlistItems().length > 0 && (
                    <Button
                      onClick={() => handleMoveToCart(getWishlistItems().map(item => item.product?._id || item._id))}
                      variant="outline"
                      size="sm"
                    >
                      <i className="fas fa-shopping-cart mr-2"></i>
                      Move All to Cart
                    </Button>
                  )}

                  {getWishlistItems().length > 0 && (
                    <Button
                      onClick={handleClearWishlist}
                      variant="outline"
                      size="sm"
                      className="text-red-600 border-red-300 hover:bg-red-50"
                    >
                      <i className="fas fa-trash mr-2"></i>
                      Clear Wishlist
                    </Button>
                  )}
                </div>
              </div>
            </div>

            {/* Wishlist Items */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {getWishlistItems().map((item) => {
                const product = item.product || item; // Handle both user and guest wishlist formats
                const productId = product._id;
                
                return (
                  <div key={productId} className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
                    {/* Product Image */}
                    <div className="relative">
                      <img
                        src={product.primary_image?.url || product.image || '/api/placeholder/300/300'}
                        alt={product.name}
                        className="w-full h-48 object-cover"
                      />
                      <button
                        onClick={() => handleRemoveItem(productId)}
                        disabled={actionLoading[productId]}
                        className="absolute top-2 right-2 bg-white rounded-full p-2 shadow-md hover:bg-red-50 transition-colors"
                        title="Remove from wishlist"
                      >
                        {actionLoading[productId] ? (
                          <i className="fas fa-spinner fa-spin text-gray-600"></i>
                        ) : (
                          <i className="fas fa-heart text-red-500"></i>
                        )}
                      </button>
                    </div>

                    {/* Product Info */}
                    <div className="p-4">
                      <div className="mb-2">
                        <span className="text-sm text-blue-600 font-medium">{product.brand}</span>
                        <h3 className="text-lg font-semibold line-clamp-2">{product.name}</h3>
                      </div>

                      <div className="mb-4">
                        <span className="text-xl font-bold text-blue-600">
                          {formatPrice(product.price)}
                        </span>
                      </div>

                      {/* Stock Status */}
                      <div className="mb-4">
                        <span className={`inline-block px-2 py-1 rounded-full text-xs font-medium ${
                          product.in_stock 
                            ? 'bg-green-100 text-green-800' 
                            : 'bg-red-100 text-red-800'
                        }`}>
                          {product.in_stock ? 'In Stock' : 'Out of Stock'}
                        </span>
                      </div>

                      {/* Actions */}
                      <div className="flex gap-2">
                        <Link to={`/products/${productId}`} className="flex-1">
                          <Button variant="outline" size="sm" fullWidth>
                            View Details
                          </Button>
                        </Link>
                        
                        {user ? (
                          <Button
                            onClick={() => handleMoveToCart([productId])}
                            disabled={!product.in_stock}
                            size="sm"
                            className="flex-1"
                          >
                            <i className="fas fa-shopping-cart mr-1"></i>
                            Add to Cart
                          </Button>
                        ) : (
                          <Link to={`/products/${productId}`} className="flex-1">
                            <Button
                              disabled={!product.in_stock}
                              size="sm"
                              fullWidth
                            >
                              <i className="fas fa-shopping-cart mr-1"></i>
                              Add to Cart
                            </Button>
                          </Link>
                        )}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default WishlistPage;
