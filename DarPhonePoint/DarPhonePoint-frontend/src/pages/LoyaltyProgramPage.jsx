import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { safeApiRequest } from '../api/apiClient';
import Button from '../components/ui/Button';
import Alert from '../components/ui/Alert';
import LoadingState from '../components/ui/LoadingState';
import { formatPrice } from '../utils/priceFormatter';

const LoyaltyProgramPage = () => {
  const { user } = useAuth();
  const [loyaltyData, setLoyaltyData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [joinLoading, setJoinLoading] = useState(false);

  // Loyalty program tiers
  const tiers = [
    {
      name: 'Bronze',
      minPoints: 0,
      maxPoints: 999,
      color: 'from-amber-600 to-amber-800',
      icon: '🥉',
      benefits: [
        '1 point per 1,000 TZS spent',
        '5% discount on accessories',
        'Birthday month special offer',
        'Free SIM card registration'
      ],
      pointsMultiplier: 1
    },
    {
      name: 'Silver',
      minPoints: 1000,
      maxPoints: 4999,
      color: 'from-gray-400 to-gray-600',
      icon: '🥈',
      benefits: [
        '1.5 points per 1,000 TZS spent',
        '10% discount on accessories',
        '5% discount on phones',
        'Priority customer support',
        'Free screen protector installation',
        'Extended warranty options'
      ],
      pointsMultiplier: 1.5
    },
    {
      name: 'Gold',
      minPoints: 5000,
      maxPoints: 14999,
      color: 'from-yellow-400 to-yellow-600',
      icon: '🥇',
      benefits: [
        '2 points per 1,000 TZS spent',
        '15% discount on accessories',
        '10% discount on phones',
        'VIP customer support',
        'Free phone setup service',
        'Exclusive early access to new phones',
        'Free annual phone cleaning'
      ],
      pointsMultiplier: 2
    },
    {
      name: 'Platinum',
      minPoints: 15000,
      maxPoints: Infinity,
      color: 'from-purple-400 to-purple-600',
      icon: '💎',
      benefits: [
        '3 points per 1,000 TZS spent',
        '20% discount on accessories',
        '15% discount on phones',
        'Dedicated account manager',
        'Free home delivery',
        'Complimentary phone insurance',
        'Exclusive VIP events',
        'Priority trade-in valuations'
      ],
      pointsMultiplier: 3
    }
  ];

  // Rewards catalog
  const rewards = [
    {
      id: 1,
      name: 'Phone Case',
      description: 'Premium protective case for your phone',
      points: 500,
      value: 25000,
      category: 'accessories',
      image: '/api/placeholder/150/150',
      inStock: true
    },
    {
      id: 2,
      name: 'Screen Protector',
      description: 'Tempered glass screen protector',
      points: 300,
      value: 15000,
      category: 'accessories',
      image: '/api/placeholder/150/150',
      inStock: true
    },
    {
      id: 3,
      name: 'Wireless Earbuds',
      description: 'Bluetooth wireless earbuds',
      points: 2000,
      value: 120000,
      category: 'accessories',
      image: '/api/placeholder/150/150',
      inStock: true
    },
    {
      id: 4,
      name: 'Power Bank 10,000mAh',
      description: 'Portable power bank with fast charging',
      points: 1500,
      value: 85000,
      category: 'accessories',
      image: '/api/placeholder/150/150',
      inStock: true
    },
    {
      id: 5,
      name: 'TZS 50,000 Discount',
      description: 'Discount voucher for your next phone purchase',
      points: 2500,
      value: 50000,
      category: 'voucher',
      image: '/api/placeholder/150/150',
      inStock: true
    },
    {
      id: 6,
      name: 'TZS 100,000 Discount',
      description: 'Discount voucher for premium phones',
      points: 4500,
      value: 100000,
      category: 'voucher',
      image: '/api/placeholder/150/150',
      inStock: true
    },
    {
      id: 7,
      name: 'Free Phone Repair',
      description: 'One-time free repair service (screen/battery)',
      points: 3000,
      value: 150000,
      category: 'service',
      image: '/api/placeholder/150/150',
      inStock: true
    },
    {
      id: 8,
      name: 'Premium Phone Setup',
      description: 'Complete phone setup and data transfer service',
      points: 800,
      value: 40000,
      category: 'service',
      image: '/api/placeholder/150/150',
      inStock: true
    }
  ];

  useEffect(() => {
    if (user) {
      loadLoyaltyData();
    } else {
      setLoading(false);
    }
  }, [user]);

  const loadLoyaltyData = async () => {
    try {
      // Simulate loyalty data loading (replace with real API)
      const ordersResponse = await safeApiRequest({
        method: 'GET',
        url: '/orders'
      });

      const orders = ordersResponse.data?.data || [];
      const totalSpent = orders.reduce((sum, order) => sum + (order.amount || order.total || 0), 0);
      const points = Math.floor(totalSpent / 1000) * 10; // 10 points per 1000 TZS

      const currentTier = tiers.find(tier => 
        points >= tier.minPoints && points <= tier.maxPoints
      ) || tiers[0];

      const nextTier = tiers.find(tier => tier.minPoints > points);

      setLoyaltyData({
        points,
        totalSpent,
        currentTier,
        nextTier,
        pointsToNextTier: nextTier ? nextTier.minPoints - points : 0,
        memberSince: user.createdAt || new Date().toISOString(),
        rewardsRedeemed: 0, // This would come from API
        isActive: true
      });
    } catch (err) {
      console.error('Error loading loyalty data:', err);
      setError('Failed to load loyalty program data');
    } finally {
      setLoading(false);
    }
  };

  const joinLoyaltyProgram = async () => {
    if (!user) {
      setError('Please log in to join the loyalty program');
      return;
    }

    setJoinLoading(true);
    try {
      // Simulate joining loyalty program
      setTimeout(() => {
        setLoyaltyData({
          points: 0,
          totalSpent: 0,
          currentTier: tiers[0],
          nextTier: tiers[1],
          pointsToNextTier: tiers[1].minPoints,
          memberSince: new Date().toISOString(),
          rewardsRedeemed: 0,
          isActive: true
        });
        setJoinLoading(false);
      }, 1500);
    } catch (err) {
      setError('Failed to join loyalty program');
      setJoinLoading(false);
    }
  };

  const redeemReward = async (reward) => {
    if (!loyaltyData || loyaltyData.points < reward.points) {
      setError('Insufficient points to redeem this reward');
      return;
    }

    try {
      // Simulate reward redemption
      setLoyaltyData(prev => ({
        ...prev,
        points: prev.points - reward.points,
        rewardsRedeemed: prev.rewardsRedeemed + 1
      }));

      alert(`Successfully redeemed ${reward.name}! You will receive instructions via SMS.`);
    } catch (err) {
      setError('Failed to redeem reward');
    }
  };

  if (loading) {
    return <LoadingState message="Loading loyalty program..." />;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-gradient-to-r from-orange-600 to-orange-800 text-white">
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold">Loyalty Program 🏆</h1>
              <p className="text-orange-100 mt-2">Earn points and unlock exclusive rewards</p>
            </div>
            <div className="text-right">
              <Link to="/dashboard">
                <Button className="bg-white text-orange-600 hover:bg-gray-100">
                  <i className="fas fa-arrow-left mr-2"></i>
                  Back to Dashboard
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        {error && (
          <Alert 
            type="error" 
            message={error} 
            onClose={() => setError('')}
            className="mb-6"
          />
        )}

        {!user ? (
          /* Not Logged In */
          <div className="max-w-4xl mx-auto">
            <div className="bg-white rounded-lg shadow-md p-8 text-center mb-8">
              <i className="fas fa-user-plus text-orange-600 text-6xl mb-4"></i>
              <h2 className="text-2xl font-bold mb-4">Join Phone Point Dar Loyalty Program</h2>
              <p className="text-gray-600 mb-6">
                Sign up to start earning points with every purchase and unlock exclusive rewards!
              </p>
              <div className="flex justify-center space-x-4">
                <Link to="/login">
                  <Button className="bg-orange-600 hover:bg-orange-700">
                    <i className="fas fa-sign-in-alt mr-2"></i>
                    Login
                  </Button>
                </Link>
                <Link to="/register">
                  <Button variant="outline">
                    <i className="fas fa-user-plus mr-2"></i>
                    Register
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        ) : !loyaltyData?.isActive ? (
          /* Join Program */
          <div className="max-w-4xl mx-auto">
            <div className="bg-white rounded-lg shadow-md p-8 text-center mb-8">
              <i className="fas fa-star text-orange-600 text-6xl mb-4"></i>
              <h2 className="text-2xl font-bold mb-4">Welcome to Phone Point Dar Loyalty!</h2>
              <p className="text-gray-600 mb-6">
                Join our loyalty program to start earning points with every purchase.
              </p>
              <Button
                onClick={joinLoyaltyProgram}
                disabled={joinLoading}
                className="bg-orange-600 hover:bg-orange-700"
              >
                {joinLoading ? (
                  <>
                    <i className="fas fa-spinner fa-spin mr-2"></i>
                    Joining...
                  </>
                ) : (
                  <>
                    <i className="fas fa-star mr-2"></i>
                    Join Loyalty Program
                  </>
                )}
              </Button>
            </div>
          </div>
        ) : (
          /* Member Dashboard */
          <div className="max-w-6xl mx-auto">
            {/* Member Status */}
            <div className="bg-white rounded-lg shadow-md p-8 mb-8">
              <div className="flex items-center justify-between mb-6">
                <div>
                  <h2 className="text-2xl font-bold">Welcome back, {user.name}!</h2>
                  <p className="text-gray-600">
                    Member since {new Date(loyaltyData.memberSince).toLocaleDateString('sw-TZ')}
                  </p>
                </div>
                <div className={`bg-gradient-to-r ${loyaltyData.currentTier.color} text-white px-6 py-3 rounded-lg text-center`}>
                  <div className="text-2xl mb-1">{loyaltyData.currentTier.icon}</div>
                  <div className="font-bold">{loyaltyData.currentTier.name}</div>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center">
                  <div className="text-3xl font-bold text-orange-600">{loyaltyData.points.toLocaleString()}</div>
                  <div className="text-gray-600">Available Points</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-green-600">{formatPrice(loyaltyData.totalSpent)}</div>
                  <div className="text-gray-600">Total Spent</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-blue-600">{loyaltyData.rewardsRedeemed}</div>
                  <div className="text-gray-600">Rewards Redeemed</div>
                </div>
              </div>

              {loyaltyData.nextTier && (
                <div className="mt-6">
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-sm text-gray-600">Progress to {loyaltyData.nextTier.name}</span>
                    <span className="text-sm text-gray-600">
                      {loyaltyData.pointsToNextTier} points needed
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-orange-600 h-2 rounded-full"
                      style={{
                        width: `${Math.min(100, (loyaltyData.points / loyaltyData.nextTier.minPoints) * 100)}%`
                      }}
                    ></div>
                  </div>
                </div>
              )}
            </div>

            {/* Tier Benefits */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
              {/* Current Tier Benefits */}
              <div className="bg-white rounded-lg shadow-md p-6">
                <h3 className="text-xl font-bold mb-4">
                  Your {loyaltyData.currentTier.name} Benefits
                </h3>
                <div className="space-y-3">
                  {loyaltyData.currentTier.benefits.map((benefit, index) => (
                    <div key={index} className="flex items-center">
                      <i className="fas fa-check text-green-500 mr-3"></i>
                      <span className="text-gray-700">{benefit}</span>
                    </div>
                  ))}
                </div>
              </div>

              {/* Next Tier Preview */}
              {loyaltyData.nextTier && (
                <div className="bg-gray-50 rounded-lg shadow-md p-6">
                  <h3 className="text-xl font-bold mb-4">
                    Unlock {loyaltyData.nextTier.name} Benefits
                  </h3>
                  <div className="space-y-3">
                    {loyaltyData.nextTier.benefits.slice(0, 4).map((benefit, index) => (
                      <div key={index} className="flex items-center">
                        <i className="fas fa-lock text-gray-400 mr-3"></i>
                        <span className="text-gray-600">{benefit}</span>
                      </div>
                    ))}
                  </div>
                  <div className="mt-4 text-sm text-gray-500">
                    Spend {formatPrice(loyaltyData.pointsToNextTier * 100)} more to unlock!
                  </div>
                </div>
              )}
            </div>

            {/* Rewards Catalog */}
            <div className="bg-white rounded-lg shadow-md p-8">
              <h2 className="text-2xl font-bold mb-6">Redeem Rewards</h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                {rewards.map((reward) => (
                  <div key={reward.id} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                    <img
                      src={reward.image}
                      alt={reward.name}
                      className="w-full h-32 object-cover rounded mb-3"
                    />
                    <h4 className="font-semibold mb-2">{reward.name}</h4>
                    <p className="text-sm text-gray-600 mb-3">{reward.description}</p>
                    
                    <div className="flex justify-between items-center mb-3">
                      <span className="text-lg font-bold text-orange-600">
                        {reward.points.toLocaleString()} pts
                      </span>
                      <span className="text-sm text-gray-500">
                        Value: {formatPrice(reward.value)}
                      </span>
                    </div>

                    <Button
                      onClick={() => redeemReward(reward)}
                      disabled={loyaltyData.points < reward.points || !reward.inStock}
                      size="sm"
                      className={`w-full ${
                        loyaltyData.points >= reward.points && reward.inStock
                          ? 'bg-orange-600 hover:bg-orange-700'
                          : 'bg-gray-300 cursor-not-allowed'
                      }`}
                    >
                      {loyaltyData.points >= reward.points && reward.inStock ? (
                        <>
                          <i className="fas fa-gift mr-2"></i>
                          Redeem
                        </>
                      ) : loyaltyData.points < reward.points ? (
                        <>
                          <i className="fas fa-lock mr-2"></i>
                          Need {(reward.points - loyaltyData.points).toLocaleString()} more
                        </>
                      ) : (
                        <>
                          <i className="fas fa-times mr-2"></i>
                          Out of Stock
                        </>
                      )}
                    </Button>
                  </div>
                ))}
              </div>
            </div>

            {/* How to Earn Points */}
            <div className="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
              <h3 className="font-semibold text-blue-800 mb-4">
                <i className="fas fa-info-circle mr-2"></i>
                How to Earn More Points
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-blue-700">
                <div>
                  <h4 className="font-medium mb-2">Purchase Activities:</h4>
                  <ul className="space-y-1">
                    <li>• Buy phones: {loyaltyData.currentTier.pointsMultiplier} pts per 1,000 TZS</li>
                    <li>• Buy accessories: {loyaltyData.currentTier.pointsMultiplier} pts per 1,000 TZS</li>
                    <li>• Trade-in bonus: +50 pts per trade</li>
                    <li>• Referral bonus: +200 pts per friend</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-medium mb-2">Special Bonuses:</h4>
                  <ul className="space-y-1">
                    <li>• Birthday month: Double points</li>
                    <li>• Review products: +25 pts per review</li>
                    <li>• Social media share: +10 pts</li>
                    <li>• Newsletter signup: +50 pts</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Program Overview for All Users */}
        <div className="mt-12">
          <h2 className="text-2xl font-bold text-center mb-8">Loyalty Program Tiers</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {tiers.map((tier, index) => (
              <div
                key={tier.name}
                className={`bg-white rounded-lg shadow-md overflow-hidden ${
                  loyaltyData?.currentTier.name === tier.name ? 'ring-2 ring-orange-500' : ''
                }`}
              >
                <div className={`bg-gradient-to-r ${tier.color} text-white p-4 text-center`}>
                  <div className="text-3xl mb-2">{tier.icon}</div>
                  <h3 className="text-xl font-bold">{tier.name}</h3>
                  <p className="text-sm opacity-90">
                    {tier.minPoints === 0 ? '0' : tier.minPoints.toLocaleString()}
                    {tier.maxPoints === Infinity ? '+' : ` - ${tier.maxPoints.toLocaleString()}`} points
                  </p>
                </div>
                <div className="p-4">
                  <div className="space-y-2">
                    {tier.benefits.slice(0, 3).map((benefit, benefitIndex) => (
                      <div key={benefitIndex} className="flex items-start text-sm">
                        <i className="fas fa-check text-green-500 mr-2 mt-0.5"></i>
                        <span>{benefit}</span>
                      </div>
                    ))}
                    {tier.benefits.length > 3 && (
                      <div className="text-xs text-gray-500">
                        +{tier.benefits.length - 3} more benefits
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoyaltyProgramPage;
