import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import PasswordInput from '../components/ui/PasswordInput';
import PasswordStrengthIndicator from '../components/ui/PasswordStrengthIndicator';
import Button from '../components/ui/Button';

/**
 * Password Strength Demo Page
 * Showcases the password strength indicator functionality
 */
const PasswordStrengthDemo = () => {
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');

  const handlePasswordChange = (e) => {
    setPassword(e.target.value);
  };

  const handleConfirmPasswordChange = (e) => {
    setConfirmPassword(e.target.value);
  };

  // Example passwords for testing
  const examplePasswords = [
    { label: 'Very Weak', value: '123' },
    { label: 'Weak', value: 'password' },
    { label: 'Medium', value: 'Password1' },
    { label: 'Strong', value: 'Password123!' },
    { label: 'Very Strong', value: 'MySecure@Password2024!' }
  ];

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">
              Password Strength Indicator Demo
            </h1>
            <p className="text-lg text-gray-600 mb-6">
              Experience our enhanced password security features
            </p>
            <Link to="/" className="text-purple-600 hover:text-purple-500">
              ← Back to Home
            </Link>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Interactive Demo */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h2 className="text-2xl font-semibold mb-6">Interactive Demo</h2>
              
              <div className="space-y-6">
                <PasswordInput
                  label="Try typing a password"
                  value={password}
                  onChange={handlePasswordChange}
                  showStrengthIndicator={true}
                  showRequirements={true}
                  placeholder="Start typing to see the strength indicator..."
                />

                <PasswordInput
                  label="Confirm Password"
                  value={confirmPassword}
                  onChange={handleConfirmPasswordChange}
                  showStrengthIndicator={false}
                  showRequirements={false}
                  placeholder="Confirm your password..."
                  error={password && confirmPassword && password !== confirmPassword ? 'Passwords do not match' : ''}
                />

                {/* Quick Test Buttons */}
                <div className="border-t pt-6">
                  <h3 className="text-lg font-medium mb-4">Quick Test Examples:</h3>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                    {examplePasswords.map((example, index) => (
                      <Button
                        key={index}
                        variant="outline"
                        size="sm"
                        onClick={() => setPassword(example.value)}
                        className="text-left justify-start"
                      >
                        <span className="font-medium">{example.label}:</span>
                        <span className="ml-2 text-gray-600 font-mono text-sm">
                          {example.value}
                        </span>
                      </Button>
                    ))}
                  </div>
                </div>
              </div>
            </div>

            {/* Features Overview */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h2 className="text-2xl font-semibold mb-6">Features</h2>
              
              <div className="space-y-6">
                {/* Real-time Feedback */}
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0 w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                    <svg className="w-5 h-5 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="font-medium text-gray-900">Real-time Feedback</h3>
                    <p className="text-gray-600 text-sm">
                      Instant visual feedback as you type your password
                    </p>
                  </div>
                </div>

                {/* Visual Progress Bar */}
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                    <svg className="w-5 h-5 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="font-medium text-gray-900">Visual Progress</h3>
                    <p className="text-gray-600 text-sm">
                      Color-coded progress bar and strength dots
                    </p>
                  </div>
                </div>

                {/* Requirements Checklist */}
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0 w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                    <svg className="w-5 h-5 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="font-medium text-gray-900">Requirements Checklist</h3>
                    <p className="text-gray-600 text-sm">
                      Clear checklist of password requirements
                    </p>
                  </div>
                </div>

                {/* Security Tips */}
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0 w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                    <svg className="w-5 h-5 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="font-medium text-gray-900">Security Tips</h3>
                    <p className="text-gray-600 text-sm">
                      Contextual tips to improve password security
                    </p>
                  </div>
                </div>

                {/* Show/Hide Toggle */}
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0 w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                    <svg className="w-5 h-5 text-gray-600" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                      <path fillRule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="font-medium text-gray-900">Show/Hide Toggle</h3>
                    <p className="text-gray-600 text-sm">
                      Toggle password visibility for easier typing
                    </p>
                  </div>
                </div>
              </div>

              {/* Implementation Note */}
              <div className="mt-8 p-4 bg-gray-50 rounded-lg">
                <h4 className="font-medium text-gray-900 mb-2">Implementation</h4>
                <p className="text-sm text-gray-600">
                  This password strength indicator is now integrated into the registration 
                  and profile pages, providing users with immediate feedback to create 
                  more secure passwords.
                </p>
              </div>
            </div>
          </div>

          {/* Navigation */}
          <div className="mt-12 text-center">
            <div className="space-x-4">
              <Link to="/register">
                <Button>
                  Try Registration Form
                </Button>
              </Link>
              <Link to="/profile">
                <Button variant="outline">
                  Try Profile Page
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PasswordStrengthDemo;
