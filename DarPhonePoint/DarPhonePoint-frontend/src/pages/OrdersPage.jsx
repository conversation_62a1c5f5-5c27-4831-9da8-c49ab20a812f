import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { safeApiRequest } from '../api/apiClient';
import Button from '../components/ui/Button';
import Alert from '../components/ui/Alert';

const OrdersPage = () => {
  const [orders, setOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [activeFilter, setActiveFilter] = useState('all');

  // Phone Point Dar specific helper functions
  const formatTanzanianPrice = (price) => {
    return new Intl.NumberFormat('sw-TZ', {
      style: 'currency',
      currency: 'TZS',
      minimumFractionDigits: 0
    }).format(price);
  };

  const getOrderStatus = (order) => {
    if (!order) return 'unknown';
    if (order.order_status) return String(order.order_status).toLowerCase();
    if (order.status) return String(order.status).toLowerCase();
    if (order.payment_status) {
      return order.payment_status === 'completed' ? 'delivered' : 'processing';
    }
    return 'pending';
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'delivered': return 'bg-green-100 text-green-800 border-green-200';
      case 'shipped': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'processing': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'pending': return 'bg-gray-100 text-gray-800 border-gray-200';
      case 'cancelled': return 'bg-red-100 text-red-800 border-red-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'delivered': return 'fas fa-check-circle';
      case 'shipped': return 'fas fa-truck';
      case 'processing': return 'fas fa-cog fa-spin';
      case 'pending': return 'fas fa-clock';
      case 'cancelled': return 'fas fa-times-circle';
      default: return 'fas fa-question-circle';
    }
  };

  useEffect(() => {
    const fetchOrders = async () => {
      try {
        const response = await safeApiRequest({
          method: 'GET',
          url: `/orders?_t=${Date.now()}`
        });

        let ordersData = [];
        if (response.data && response.data.data) {
          ordersData = response.data.data;
        } else if (response.data) {
          ordersData = response.data;
        }

        if (!Array.isArray(ordersData)) {
          ordersData = [];
        }

        // Sort orders by date (newest first)
        ordersData.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
        setOrders(ordersData);
        setLoading(false);
      } catch (err) {
        console.error('Error fetching orders:', err);
        setError('Failed to load your phone orders. Please try again later.');
        setLoading(false);
      }
    };

    fetchOrders();
  }, []);

  // Filter orders based on active filter
  const filteredOrders = orders.filter(order => {
    if (activeFilter === 'all') return true;
    return getOrderStatus(order) === activeFilter;
  });

  const orderCounts = {
    all: orders.length,
    pending: orders.filter(o => getOrderStatus(o) === 'pending').length,
    processing: orders.filter(o => getOrderStatus(o) === 'processing').length,
    shipped: orders.filter(o => getOrderStatus(o) === 'shipped').length,
    delivered: orders.filter(o => getOrderStatus(o) === 'delivered').length,
    cancelled: orders.filter(o => getOrderStatus(o) === 'cancelled').length,
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading your Phone Point Dar orders...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-600 to-blue-800 text-white">
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold">My Phone Orders 📱</h1>
              <p className="text-blue-100 mt-2">Track your Phone Point Dar purchases</p>
            </div>
            <div className="text-right">
              <Link to="/products">
                <Button className="bg-white text-blue-600 hover:bg-gray-100">
                  <i className="fas fa-shopping-cart mr-2"></i>
                  Continue Shopping
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        <div className="max-w-6xl mx-auto">

          {error && (
            <Alert
              type="error"
              message={error}
              onClose={() => setError('')}
              className="mb-8"
            />
          )}

          {/* Order Status Filter Tabs */}
          <div className="mb-8">
            <div className="bg-white rounded-lg shadow-md p-6">
              <h2 className="text-lg font-semibold mb-4 text-gray-800">Filter Orders</h2>
              <div className="flex flex-wrap gap-2">
                {[
                  { key: 'all', label: 'All Orders', icon: 'fas fa-list' },
                  { key: 'pending', label: 'Pending', icon: 'fas fa-clock' },
                  { key: 'processing', label: 'Processing', icon: 'fas fa-cog' },
                  { key: 'shipped', label: 'Shipped', icon: 'fas fa-truck' },
                  { key: 'delivered', label: 'Delivered', icon: 'fas fa-check-circle' },
                  { key: 'cancelled', label: 'Cancelled', icon: 'fas fa-times-circle' }
                ].map((filter) => (
                  <button
                    key={filter.key}
                    type="button"
                    className={`flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-colors ${
                      activeFilter === filter.key
                        ? 'bg-blue-600 text-white'
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }`}
                    onClick={() => setActiveFilter(filter.key)}
                  >
                    <i className={`${filter.icon} mr-2`}></i>
                    {filter.label}
                    <span className="ml-2 px-2 py-1 text-xs rounded-full bg-white bg-opacity-20">
                      {orderCounts[filter.key]}
                    </span>
                  </button>
                ))}
              </div>
            </div>
          </div>

          {filteredOrders.length > 0 ? (
            <div className="space-y-6">
              {filteredOrders.map((order) => (
                <div key={order._id} className="bg-white rounded-lg shadow-md overflow-hidden">
                  <div className="p-6">
                    {/* Order Header */}
                    <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-4">
                      <div className="flex items-center space-x-4">
                        <div className="flex-shrink-0">
                          <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                            <i className="fas fa-mobile-alt text-blue-600 text-xl"></i>
                          </div>
                        </div>
                        <div>
                          <h3 className="text-lg font-semibold text-gray-800">
                            {order.items?.[0]?.product?.name || order.items?.[0]?.name || 'Phone Order'}
                          </h3>
                          <p className="text-sm text-gray-500">
                            Order #{order.order_number || order._id?.slice(-8)} • {new Date(order.created_at).toLocaleDateString('sw-TZ')}
                          </p>
                        </div>
                      </div>
                      <div className="mt-4 md:mt-0 flex items-center space-x-4">
                        <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${getStatusColor(getOrderStatus(order))}`}>
                          <i className={`${getStatusIcon(getOrderStatus(order))} mr-2`}></i>
                          {getOrderStatus(order).charAt(0).toUpperCase() + getOrderStatus(order).slice(1)}
                        </span>
                        <div className="text-right">
                          <p className="text-lg font-bold text-blue-600">
                            {formatTanzanianPrice(order.total_amount || order.amount || order.total || 0)}
                          </p>
                        </div>
                      </div>
                    </div>

                    {/* Order Details */}
                    <div className="border-t border-gray-200 pt-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-4">
                        {/* Device Details */}
                        <div>
                          <h4 className="text-sm font-medium text-gray-500 mb-2">Device Details</h4>
                          <div className="space-y-1">
                            <p className="text-sm text-gray-800">
                              <span className="font-medium">Model:</span> {order.items?.[0]?.product?.name || order.items?.[0]?.name || 'N/A'}
                            </p>
                            <p className="text-sm text-gray-800">
                              <span className="font-medium">Brand:</span> {order.items?.[0]?.product?.brand || 'N/A'}
                            </p>
                            {order.variant && (
                              <>
                                <p className="text-sm text-gray-800">
                                  <span className="font-medium">Storage:</span> {order.variant.storage || 'N/A'}
                                </p>
                                <p className="text-sm text-gray-800">
                                  <span className="font-medium">Color:</span> {order.variant.color || 'N/A'}
                                </p>
                              </>
                            )}
                          </div>
                        </div>

                        {/* Shipping Details */}
                        <div>
                          <h4 className="text-sm font-medium text-gray-500 mb-2">Shipping Address</h4>
                          <div className="space-y-1">
                            <p className="text-sm text-gray-800">
                              {order.shipping_address?.first_name} {order.shipping_address?.last_name}
                            </p>
                            <p className="text-sm text-gray-800">
                              {order.shipping_address?.address_line_1}
                            </p>
                            <p className="text-sm text-gray-800">
                              {order.shipping_address?.city}, {order.shipping_address?.state}
                            </p>
                            <p className="text-sm text-gray-800">
                              {order.shipping_address?.country}
                            </p>
                          </div>
                        </div>

                        {/* Payment & Warranty */}
                        <div>
                          <h4 className="text-sm font-medium text-gray-500 mb-2">Payment & Warranty</h4>
                          <div className="space-y-1">
                            <p className="text-sm text-gray-800">
                              <span className="font-medium">Payment:</span>
                              <span className={`ml-1 px-2 py-1 text-xs rounded-full ${
                                order.payment_status === 'completed'
                                  ? 'bg-green-100 text-green-800'
                                  : 'bg-yellow-100 text-yellow-800'
                              }`}>
                                {order.payment_status === 'completed' ? 'Paid' : 'Pending'}
                              </span>
                            </p>
                            <p className="text-sm text-gray-800">
                              <span className="font-medium">Warranty:</span> 1 Year
                            </p>
                            {order.imei && (
                              <p className="text-sm text-gray-800">
                                <span className="font-medium">IMEI:</span> {order.imei}
                              </p>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Action Buttons */}
                    <div className="border-t border-gray-200 pt-4">
                      <div className="flex flex-wrap gap-3">
                        <Button variant="outline" size="sm">
                          <i className="fas fa-truck mr-2"></i>
                          Track Order
                        </Button>

                        {getOrderStatus(order) === 'delivered' && (
                          <>
                            <Button variant="outline" size="sm">
                              <i className="fas fa-star mr-2"></i>
                              Write Review
                            </Button>
                            <Button variant="outline" size="sm">
                              <i className="fas fa-undo mr-2"></i>
                              Return/Exchange
                            </Button>
                          </>
                        )}

                        <Button variant="outline" size="sm">
                          <i className="fas fa-headset mr-2"></i>
                          Contact Support
                        </Button>

                        <Button variant="outline" size="sm">
                          <i className="fas fa-download mr-2"></i>
                          Download Invoice
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="bg-white rounded-lg shadow-md p-12 text-center">
              <div className="max-w-md mx-auto">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <i className="fas fa-mobile-alt text-blue-600 text-2xl"></i>
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">No phone orders found</h3>
                <p className="text-gray-500 mb-6">
                  {activeFilter === 'all'
                    ? "You haven't ordered any phones from Phone Point Dar yet."
                    : `No orders with status "${activeFilter}" found.`}
                </p>
                <Link to="/products">
                  <Button className="bg-blue-600 hover:bg-blue-700">
                    <i className="fas fa-shopping-cart mr-2"></i>
                    Shop Phones Now
                  </Button>
                </Link>
              </div>
            </div>
          )}

          {/* Support Section */}
          <div className="mt-12 bg-gradient-to-r from-blue-600 to-blue-800 rounded-lg shadow-md p-8 text-white text-center">
            <div className="max-w-2xl mx-auto">
              <h2 className="text-2xl font-bold mb-4">
                <i className="fas fa-headset mr-2"></i>
                Need Help with Your Order?
              </h2>
              <p className="text-blue-100 mb-6">
                Our Phone Point Dar support team is here to help with order tracking, returns, warranty claims, and technical support.
              </p>
              <div className="flex flex-col sm:flex-row justify-center space-y-3 sm:space-y-0 sm:space-x-4">
                <Button className="bg-white text-blue-600 hover:bg-gray-100">
                  <i className="fas fa-phone mr-2"></i>
                  Call Support
                </Button>
                <Button variant="outline" className="border-white text-white hover:bg-white hover:text-blue-600">
                  <i className="fas fa-envelope mr-2"></i>
                  Email Us
                </Button>
                <Button variant="outline" className="border-white text-white hover:bg-white hover:text-blue-600">
                  <i className="fas fa-map-marker-alt mr-2"></i>
                  Visit Store
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OrdersPage;
