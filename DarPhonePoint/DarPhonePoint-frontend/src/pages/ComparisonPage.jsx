import React, { useState, useEffect } from 'react';
import { Link, useSearchParams } from 'react-router-dom';
import { safeApiRequest } from '../api/apiClient';
import Button from '../components/ui/Button';
import Alert from '../components/ui/Alert';
import LoadingState from '../components/ui/LoadingState';
import { formatPrice } from '../utils/priceFormatter';

const ComparisonPage = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const [products, setProducts] = useState([]);
  const [comparisonProducts, setComparisonProducts] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [searchResults, setSearchResults] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchLoading, setSearchLoading] = useState(false);
  const [error, setError] = useState('');

  useEffect(() => {
    loadProducts();
    loadComparisonFromParams();
  }, []);

  const loadProducts = async () => {
    try {
      const response = await safeApiRequest({
        method: 'GET',
        url: '/products?limit=50&category=smartphone'
      });

      let productsData = [];
      if (response.data && response.data.data) {
        productsData = response.data.data;
      } else if (response.data) {
        productsData = response.data;
      }

      setProducts(Array.isArray(productsData) ? productsData : []);
    } catch (err) {
      console.error('Error loading products:', err);
      setError('Failed to load products for comparison');
    } finally {
      setLoading(false);
    }
  };

  const loadComparisonFromParams = () => {
    const productIds = searchParams.get('products');
    if (productIds) {
      const ids = productIds.split(',').slice(0, 3); // Max 3 products
      loadComparisonProducts(ids);
    }
  };

  const loadComparisonProducts = async (productIds) => {
    try {
      const productPromises = productIds.map(id =>
        safeApiRequest({
          method: 'GET',
          url: `/products/${id}`
        })
      );

      const responses = await Promise.all(productPromises);
      const loadedProducts = responses.map(response =>
        response.data?.data || response.data
      ).filter(Boolean);

      setComparisonProducts(loadedProducts);
    } catch (err) {
      console.error('Error loading comparison products:', err);
      setError('Failed to load products for comparison');
    }
  };

  const searchProducts = async (term) => {
    if (!term.trim()) {
      setSearchResults([]);
      return;
    }

    setSearchLoading(true);
    try {
      const response = await safeApiRequest({
        method: 'GET',
        url: `/search/suggestions?q=${encodeURIComponent(term)}&limit=10`
      });

      let results = [];
      if (response.data && response.data.data) {
        results = response.data.data;
      } else if (response.data) {
        results = response.data;
      }

      setSearchResults(Array.isArray(results) ? results : []);
    } catch (err) {
      console.error('Error searching products:', err);
      setSearchResults([]);
    } finally {
      setSearchLoading(false);
    }
  };

  const addToComparison = async (product) => {
    if (comparisonProducts.length >= 3) {
      setError('You can compare up to 3 products at a time');
      return;
    }

    if (comparisonProducts.find(p => p._id === product._id)) {
      setError('This product is already in comparison');
      return;
    }

    try {
      // Fetch complete product data including specifications
      const response = await safeApiRequest({
        method: 'GET',
        url: `/products/${product._id}`
      });

      const fullProduct = response.data?.data || response.data;

      const newComparison = [...comparisonProducts, fullProduct];
      setComparisonProducts(newComparison);
      updateUrlParams(newComparison);
      setSearchTerm('');
      setSearchResults([]);
      setError('');
    } catch (err) {
      console.error('Error fetching product details:', err);
      setError('Failed to add product to comparison');
    }
  };

  const removeFromComparison = (productId) => {
    const newComparison = comparisonProducts.filter(p => p._id !== productId);
    setComparisonProducts(newComparison);
    updateUrlParams(newComparison);
  };

  const updateUrlParams = (products) => {
    if (products.length > 0) {
      setSearchParams({ products: products.map(p => p._id).join(',') });
    } else {
      setSearchParams({});
    }
  };

  const clearComparison = () => {
    setComparisonProducts([]);
    setSearchParams({});
  };

  const getSpecValue = (product, spec) => {
    const specs = product.specifications || [];

    // Helper function to find spec by name or category
    const findSpec = (searchTerms) => {
      for (const term of searchTerms) {
        const found = specs.find(s =>
          s.name.toLowerCase().includes(term.toLowerCase()) ||
          s.category.toLowerCase().includes(term.toLowerCase())
        );
        if (found) {
          return found.value;
        }
      }
      return null;
    };

    switch (spec) {
      case 'display':
        return findSpec(['display size', 'screen size', 'display']) || 'N/A';
      case 'processor':
        return findSpec(['processor', 'chipset', 'cpu', 'soc']) || 'N/A';
      case 'ram':
        return findSpec(['ram', 'memory']) || 'N/A';
      case 'storage':
        return findSpec(['storage', 'internal storage', 'capacity']) || 'N/A';
      case 'camera':
        return findSpec(['main camera', 'rear camera', 'primary camera', 'camera']) || 'N/A';
      case 'battery':
        return findSpec(['battery', 'battery capacity']) || 'N/A';
      case 'os':
        return findSpec(['operating system', 'os', 'software']) || 'N/A';
      case 'network':
        return findSpec(['network', 'connectivity', '5g', '4g', 'lte']) || 'N/A';
      case 'weight':
        return findSpec(['weight']) || (product.weight ? `${product.weight}g` : 'N/A');
      case 'dimensions':
        return findSpec(['dimensions', 'size']) ||
               (product.dimensions ? `${product.dimensions.length}×${product.dimensions.width}×${product.dimensions.height}mm` : 'N/A');
      default:
        return 'N/A';
    }
  };

  const comparisonSpecs = [
    { key: 'price', label: 'Price', type: 'price' },
    { key: 'display', label: 'Display Size' },
    { key: 'processor', label: 'Processor' },
    { key: 'ram', label: 'RAM' },
    { key: 'storage', label: 'Storage' },
    { key: 'camera', label: 'Main Camera' },
    { key: 'battery', label: 'Battery' },
    { key: 'os', label: 'Operating System' },
    { key: 'network', label: 'Network' },
    { key: 'weight', label: 'Weight' },
    { key: 'dimensions', label: 'Dimensions' }
  ];

  if (loading) {
    return <LoadingState message="Loading comparison tool..." />;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-gradient-to-r from-purple-600 to-purple-800 text-white">
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold">Phone Comparison ⚖️</h1>
              <p className="text-purple-100 mt-2">Compare specs, prices, and features side by side</p>
            </div>
            <div className="text-right">
              <Link to="/dashboard">
                <Button className="bg-white text-purple-600 hover:bg-gray-100">
                  <i className="fas fa-arrow-left mr-2"></i>
                  Back to Dashboard
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        {error && (
          <Alert 
            type="error" 
            message={error} 
            onClose={() => setError('')}
            className="mb-6"
          />
        )}

        {/* Search and Add Products */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-8">
          <h2 className="text-xl font-bold mb-4">Add Products to Compare</h2>
          
          <div className="relative">
            <div className="flex">
              <input
                type="text"
                placeholder="Search for phones to compare..."
                value={searchTerm}
                onChange={(e) => {
                  setSearchTerm(e.target.value);
                  searchProducts(e.target.value);
                }}
                className="flex-1 px-4 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-purple-500"
              />
              <Button 
                onClick={() => searchProducts(searchTerm)}
                disabled={searchLoading}
                className="bg-purple-600 hover:bg-purple-700 rounded-l-none"
              >
                {searchLoading ? (
                  <i className="fas fa-spinner fa-spin"></i>
                ) : (
                  <i className="fas fa-search"></i>
                )}
              </Button>
            </div>

            {/* Search Results */}
            {searchResults.length > 0 && (
              <div className="absolute top-full left-0 right-0 bg-white border border-gray-300 rounded-md shadow-lg z-10 max-h-60 overflow-y-auto">
                {searchResults.map((product) => (
                  <div
                    key={product._id}
                    onClick={() => addToComparison(product)}
                    className="flex items-center p-3 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0"
                  >
                    <img
                      src={product.primary_image?.url || product.images?.[0]?.url || '/api/placeholder/50/50'}
                      alt={product.name}
                      className="w-12 h-12 object-cover rounded mr-3"
                    />
                    <div className="flex-1">
                      <h4 className="font-medium">{product.name}</h4>
                      <p className="text-sm text-gray-500">{product.brand}</p>
                      <p className="text-sm text-purple-600 font-semibold">{formatPrice(product.price)}</p>
                    </div>
                    <Button size="sm" variant="outline">
                      <i className="fas fa-plus mr-1"></i>
                      Add
                    </Button>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Popular Phones Quick Add */}
          <div className="mt-6">
            <h3 className="font-semibold mb-3">Popular Phones</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-3">
              {products.slice(0, 6).map((product) => (
                <div
                  key={product._id}
                  onClick={() => addToComparison(product)}
                  className="border border-gray-200 rounded-lg p-3 hover:border-purple-500 cursor-pointer transition-colors"
                >
                  <img
                    src={product.primary_image?.url || product.images?.[0]?.url || '/api/placeholder/100/100'}
                    alt={product.name}
                    className="w-full h-20 object-cover rounded mb-2"
                  />
                  <h4 className="text-xs font-medium truncate">{product.name}</h4>
                  <p className="text-xs text-purple-600">{formatPrice(product.price)}</p>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Comparison Table */}
        {comparisonProducts.length > 0 ? (
          <div className="bg-white rounded-lg shadow-md overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
              <h2 className="text-xl font-bold">
                Comparing {comparisonProducts.length} Phone{comparisonProducts.length > 1 ? 's' : ''}
              </h2>
              <Button
                onClick={clearComparison}
                variant="outline"
                size="sm"
                className="text-red-600 border-red-300 hover:bg-red-50"
              >
                <i className="fas fa-trash mr-2"></i>
                Clear All
              </Button>
            </div>

            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="bg-gray-50">
                    <th className="px-6 py-4 text-left font-medium text-gray-700 w-48">Specification</th>
                    {comparisonProducts.map((product) => (
                      <th key={product._id} className="px-6 py-4 text-center min-w-64">
                        <div className="relative">
                          <button
                            onClick={() => removeFromComparison(product._id)}
                            className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full text-xs hover:bg-red-600"
                            title="Remove from comparison"
                          >
                            ×
                          </button>
                          <img
                            src={product.primary_image?.url || product.images?.[0]?.url || '/api/placeholder/150/150'}
                            alt={product.name}
                            className="w-24 h-24 object-cover rounded mx-auto mb-3"
                          />
                          <h3 className="font-semibold text-sm mb-1">{product.name}</h3>
                          <p className="text-xs text-gray-500 mb-2">{product.brand}</p>
                          <Link to={`/products/${product._id}`}>
                            <Button size="sm" variant="outline">
                              View Details
                            </Button>
                          </Link>
                        </div>
                      </th>
                    ))}
                  </tr>
                </thead>
                <tbody>
                  {comparisonSpecs.map((spec, index) => (
                    <tr key={spec.key} className={index % 2 === 0 ? 'bg-gray-50' : 'bg-white'}>
                      <td className="px-6 py-4 font-medium text-gray-700">{spec.label}</td>
                      {comparisonProducts.map((product) => (
                        <td key={product._id} className="px-6 py-4 text-center">
                          {spec.type === 'price' ? (
                            <span className="text-lg font-bold text-purple-600">
                              {formatPrice(product.price)}
                            </span>
                          ) : (
                            <span className={`text-sm ${getSpecValue(product, spec.key) === 'N/A' ? 'text-gray-400 italic' : ''}`}>
                              {getSpecValue(product, spec.key)}
                            </span>
                          )}
                        </td>
                      ))}
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Action Buttons */}
            <div className="px-6 py-4 border-t border-gray-200 bg-gray-50">
              <div className="flex justify-center space-x-4">
                {comparisonProducts.map((product) => (
                  <Link key={product._id} to={`/products/${product._id}`}>
                    <Button className="bg-purple-600 hover:bg-purple-700">
                      <i className="fas fa-shopping-cart mr-2"></i>
                      Buy {product.brand}
                    </Button>
                  </Link>
                ))}
              </div>
            </div>
          </div>
        ) : (
          <div className="bg-white rounded-lg shadow-md p-12 text-center">
            <i className="fas fa-balance-scale text-gray-400 text-6xl mb-4"></i>
            <h3 className="text-xl font-semibold text-gray-600 mb-2">
              No phones selected for comparison
            </h3>
            <p className="text-gray-500 mb-6">
              Search and add phones above to start comparing their features
            </p>
            <Link to="/products">
              <Button className="bg-purple-600 hover:bg-purple-700">
                <i className="fas fa-mobile-alt mr-2"></i>
                Browse All Phones
              </Button>
            </Link>
          </div>
        )}

        {/* Comparison Tips */}
        <div className="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
          <h3 className="font-semibold text-blue-800 mb-3">
            <i className="fas fa-lightbulb mr-2"></i>
            Comparison Tips
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-blue-700">
            <div>
              <h4 className="font-medium mb-2">What to Compare:</h4>
              <ul className="space-y-1">
                <li>• Display size and quality</li>
                <li>• Camera specifications</li>
                <li>• Battery life and charging</li>
                <li>• Performance and RAM</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium mb-2">For Tanzania Users:</h4>
              <ul className="space-y-1">
                <li>• Check network compatibility</li>
                <li>• Consider battery life for power outages</li>
                <li>• Dual SIM support for multiple networks</li>
                <li>• Storage for offline content</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ComparisonPage;
