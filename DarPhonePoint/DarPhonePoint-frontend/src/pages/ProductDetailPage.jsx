import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { safeApiRequest } from '../api/apiClient';
import Button from '../components/ui/Button';
import Alert from '../components/ui/Alert';
import LoadingState from '../components/ui/LoadingState';
import ProductRecommendations from '../components/ProductRecommendations';
import ProductSelector from '../components/product/ProductSelector';
import { formatPrice } from '../utils/priceFormatter';
import { addToGuestCart } from '../services/guestCartService';
import {
  addToGuestWishlist,
  removeFromGuestWishlist,
  isInGuestWishlist
} from '../services/wishlistService';

const ProductDetailPage = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { user } = useAuth();

  const [product, setProduct] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [selectedVariant, setSelectedVariant] = useState(null);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [quantity, setQuantity] = useState(1);
  const [selectedPhone, setSelectedPhone] = useState(null);
  const [addingToCart, setAddingToCart] = useState(false);
  const [addToCartSuccess, setAddToCartSuccess] = useState(false);
  const [activeTab, setActiveTab] = useState('specifications'); // specifications, reviews, shipping
  const [isInWishlist, setIsInWishlist] = useState(false);
  const [wishlistLoading, setWishlistLoading] = useState(false);

  useEffect(() => {
    let isMounted = true;
    const abortController = new AbortController();

    const fetchProduct = async () => {
      try {
        const response = await safeApiRequest({
          method: 'GET',
          url: `/products/${id}`,
          signal: abortController.signal
        });

        if (!isMounted) return;

        const productData = response.data && response.data.data
          ? response.data.data
          : response.data;

        setProduct(productData);

        // Set default variant if product has variants
        if (productData.variants && productData.variants.length > 0) {
          const activeVariants = productData.variants.filter(v => v.is_active);
          if (activeVariants.length > 0) {
            setSelectedVariant(activeVariants[0]);
          }
        }

        setLoading(false);
      } catch (err) {
        if (err.name === 'AbortError') {
          return; // Request was cancelled
        }
        if (isMounted) {
          console.error('Error fetching product details:', err);
          setError('Failed to load product details. Please try again later.');
          setLoading(false);
        }
      }
    };

    fetchProduct();

    return () => {
      isMounted = false;
      abortController.abort();
    };
  }, [id]);

  // Check if product is in wishlist
  useEffect(() => {
    if (!product) return;

    const checkWishlistStatus = async () => {
      try {
        if (user) {
          // Check authenticated user's wishlist
          const response = await safeApiRequest({
            method: 'GET',
            url: '/wishlist'
          });
          const wishlist = response.data?.data || response.data;
          const isInList = wishlist?.items?.some(item =>
            (item.product?._id || item.product) === product._id
          );
          setIsInWishlist(isInList || false);
        } else {
          // Check guest wishlist
          setIsInWishlist(isInGuestWishlist(product._id));
        }
      } catch (err) {
        console.error('Error checking wishlist status:', err);
        setIsInWishlist(false);
      }
    };

    checkWishlistStatus();
  }, [product, user]);

  // Format price in TZS
  const formatPrice = (price) => {
    return new Intl.NumberFormat('sw-TZ', {
      style: 'currency',
      currency: 'TZS',
      minimumFractionDigits: 0
    }).format(price);
  };

  // Get current price based on selected variant or base price
  const getCurrentPrice = () => {
    if (selectedVariant) {
      return selectedVariant.price;
    }
    return product?.price || 0;
  };

  // Get current compare at price
  const getCompareAtPrice = () => {
    if (selectedVariant && selectedVariant.compare_at_price) {
      return selectedVariant.compare_at_price;
    }
    return product?.compare_at_price;
  };

  // Get current stock
  const getCurrentStock = () => {
    if (selectedVariant) {
      return selectedVariant.stock_quantity;
    }
    return product?.stock_quantity || 0;
  };

  // Check if product is in stock
  const isInStock = () => {
    if (!product?.track_inventory) return true;
    return getCurrentStock() > 0;
  };

  // Get product images
  const getProductImages = () => {
    if (selectedVariant && selectedVariant.images && selectedVariant.images.length > 0) {
      return selectedVariant.images;
    }
    return product?.images || [];
  };

  // Handle add to cart
  const handleAddToCart = async () => {
    if (!isInStock()) {
      setError('This product is currently out of stock.');
      return;
    }

    if (quantity > getCurrentStock()) {
      setError(`Only ${getCurrentStock()} items available in stock.`);
      return;
    }

    setAddingToCart(true);
    setError('');

    try {
      const cartData = {
        productId: product._id,
        quantity: quantity,
        name: product.name,
        brand: product.brand,
        model: product.model,
        category: product.category,
        price: selectedVariant ? selectedVariant.price : product.price,
        image: product.primary_image?.url || product.images?.[0]?.url || product.images?.[0],
        product: product // Include full product object for compatibility
      };

      if (selectedVariant) {
        cartData.variantSku = selectedVariant.sku;
        cartData.variantName = selectedVariant.name;
      }

      if (selectedPhone) {
        cartData.imei = selectedPhone.imei;
        cartData.condition = selectedPhone.condition;
        cartData.warrantyMonths = selectedPhone.warranty_months;
      }

      if (user) {
        // Authenticated user - use API
        const response = await safeApiRequest({
          method: 'POST',
          url: '/cart',
          data: {
            productId: cartData.productId,
            quantity: cartData.quantity,
            variantSku: cartData.variantSku,
            imei: cartData.imei
          }
        });
      } else {
        // Guest user - use local storage
        addToGuestCart(cartData);
      }

      setAddToCartSuccess(true);
      setTimeout(() => setAddToCartSuccess(false), 3000);

      // Refresh cart count in header
      if (window.refreshCartCount) {
        window.refreshCartCount();
      }

    } catch (error) {
      console.error('Add to cart error:', error);
      setError(error.response?.data?.message || 'Failed to add item to cart. Please try again.');
    } finally {
      setAddingToCart(false);
    }
  };

  // Handle wishlist toggle
  const handleWishlistToggle = async () => {
    if (!product) return;

    setWishlistLoading(true);
    try {
      if (isInWishlist) {
        // Remove from wishlist
        if (user) {
          await safeApiRequest({
            method: 'DELETE',
            url: `/wishlist/${product._id}`
          });
        } else {
          removeFromGuestWishlist(product._id);
        }
        setIsInWishlist(false);
      } else {
        // Add to wishlist
        if (user) {
          await safeApiRequest({
            method: 'POST',
            url: '/wishlist',
            data: { productId: product._id }
          });
        } else {
          addToGuestWishlist(product);
        }
        setIsInWishlist(true);
      }
    } catch (error) {
      console.error('Wishlist error:', error);
      setError(error.response?.data?.message || 'Failed to update wishlist. Please try again.');
    } finally {
      setWishlistLoading(false);
    }
  };

  // Handle variant selection
  const handleVariantChange = (variant) => {
    setSelectedVariant(variant);
    setQuantity(1); // Reset quantity when variant changes
    setSelectedImageIndex(0); // Reset to first image
    setSelectedPhone(null); // Reset selected phone when variant changes
  };

  // Handle quantity change
  const handleQuantityChange = (newQuantity) => {
    const maxQuantity = getCurrentStock();
    if (newQuantity >= 1 && newQuantity <= maxQuantity) {
      setQuantity(newQuantity);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <LoadingState
          text="Loading product details..."
          size="lg"
          type="spinner"
        />
      </div>
    );
  }

  if (error && !product) {
    return (
      <div className="container mx-auto px-4 py-12">
        <Alert
          type="error"
          message={error}
          className="max-w-3xl mx-auto"
        />
        <div className="text-center mt-6">
          <Link to="/products">
            <Button variant="outline">
              ← Back to Products
            </Button>
          </Link>
        </div>
      </div>
    );
  }

  if (!product) {
    return (
      <div className="container mx-auto px-4 py-12 text-center">
        <h2 className="text-2xl font-bold mb-4">Product Not Found</h2>
        <p className="mb-6">The product you're looking for doesn't exist or has been removed.</p>
        <Link to="/products">
          <Button>Browse All Products</Button>
        </Link>
      </div>
    );
  }

  const productImages = getProductImages();
  const currentPrice = getCurrentPrice();
  const compareAtPrice = getCompareAtPrice();

  return (
    <div className="bg-gray-50 min-h-screen">
      {/* Breadcrumb */}
      <div className="bg-white border-b">
        <div className="container mx-auto px-4 py-3">
          <nav className="flex items-center space-x-2 text-sm">
            <Link to="/" className="text-blue-600 hover:text-blue-800">Home</Link>
            <span className="text-gray-400">/</span>
            <Link to="/products" className="text-blue-600 hover:text-blue-800">Products</Link>
            <span className="text-gray-400">/</span>
            <span className="text-gray-600 capitalize">{product.category}</span>
            <span className="text-gray-400">/</span>
            <span className="text-gray-900 font-medium">{product.name}</span>
          </nav>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Product Images */}
          <div className="space-y-4">
            {/* Main Image */}
            <div className="bg-white rounded-lg shadow-md overflow-hidden">
              <div className="aspect-square relative">
                <img
                  src={productImages[selectedImageIndex]?.url || '/api/placeholder/600/600'}
                  alt={productImages[selectedImageIndex]?.alt_text || product.name}
                  className="w-full h-full object-cover"
                />
                {product.is_featured && (
                  <span className="absolute top-4 left-4 bg-red-500 text-white px-3 py-1 text-sm font-semibold rounded-full">
                    Featured
                  </span>
                )}
                {!isInStock() && (
                  <span className="absolute top-4 right-4 bg-gray-500 text-white px-3 py-1 text-sm font-semibold rounded-full">
                    Out of Stock
                  </span>
                )}
              </div>
            </div>

            {/* Thumbnail Images */}
            {productImages.length > 1 && (
              <div className="grid grid-cols-4 gap-2">
                {productImages.map((image, index) => (
                  <button
                    key={index}
                    onClick={() => setSelectedImageIndex(index)}
                    className={`aspect-square rounded-lg overflow-hidden border-2 transition-colors ${
                      selectedImageIndex === index
                        ? 'border-blue-500'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <img
                      src={image.url}
                      alt={image.alt_text || `${product.name} ${index + 1}`}
                      className="w-full h-full object-cover"
                    />
                  </button>
                ))}
              </div>
            )}
          </div>

            {/* Product Info */}
          <div className="space-y-6">
            {/* Product Header */}
            <div className="bg-white rounded-lg shadow-md p-6">
              {/* Brand and Category */}
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-2">
                  <span className="text-blue-600 font-semibold text-lg">{product.brand}</span>
                  <span className="text-gray-400">•</span>
                  <span className="text-gray-600 capitalize">{product.category}</span>
                </div>
                {product.is_featured && (
                  <span className="bg-red-100 text-red-800 px-3 py-1 text-sm font-semibold rounded-full">
                    Featured
                  </span>
                )}
              </div>

              {/* Product Name */}
              <h1 className="text-3xl font-bold mb-4 text-gray-900">{product.name}</h1>

              {/* Short Description */}
              {product.short_description && (
                <p className="text-lg text-gray-600 mb-4">{product.short_description}</p>
              )}

              {/* Price */}
              <div className="mb-6">
                <div className="flex items-center space-x-3">
                  <span className="text-3xl font-bold text-blue-600">
                    {formatPrice(currentPrice)}
                  </span>
                  {compareAtPrice && compareAtPrice > currentPrice && (
                    <span className="text-xl text-gray-500 line-through">
                      {formatPrice(compareAtPrice)}
                    </span>
                  )}
                  {compareAtPrice && compareAtPrice > currentPrice && (
                    <span className="bg-red-100 text-red-800 px-2 py-1 text-sm font-semibold rounded">
                      Save {formatPrice(compareAtPrice - currentPrice)}
                    </span>
                  )}
                </div>
              </div>

              {/* Product Selection and Add to Cart */}
              <div className="mb-6">
                <ProductSelector
                  product={product}
                  selectedVariant={selectedVariant}
                  onVariantSelect={handleVariantChange}
                  selectedPhone={selectedPhone}
                  onPhoneSelect={setSelectedPhone}
                  quantity={quantity}
                  onQuantityChange={handleQuantityChange}
                  onAddToCart={handleAddToCart}
                  addingToCart={addingToCart}
                  addToCartSuccess={addToCartSuccess}
                  error={error}
                />
              </div>

              {/* Wishlist Button */}
              <div className="mb-6">
                <Button
                  onClick={handleWishlistToggle}
                  variant="outline"
                  fullWidth
                  size="lg"
                  disabled={wishlistLoading}
                  className={isInWishlist ? 'border-red-500 text-red-500 hover:bg-red-50' : 'border-gray-300 hover:bg-gray-50'}
                >
                  <i className={`fas fa-heart mr-2 ${isInWishlist ? 'text-red-500' : 'text-gray-400'}`}></i>
                  {wishlistLoading ? 'Updating...' : (isInWishlist ? 'Remove from Wishlist' : 'Add to Wishlist')}
                </Button>
              </div>

              {/* Quick Info */}
              <div className="mt-6 grid grid-cols-2 gap-4 text-sm">
                <div className="flex items-center text-gray-600">
                  <i className="fas fa-shield-alt mr-2 text-green-500"></i>
                  Official Warranty
                </div>
                <div className="flex items-center text-gray-600">
                  <i className="fas fa-truck mr-2 text-blue-500"></i>
                  Free Delivery*
                </div>
                <div className="flex items-center text-gray-600">
                  <i className="fas fa-undo mr-2 text-orange-500"></i>
                  30-Day Returns
                </div>
                <div className="flex items-center text-gray-600">
                  <i className="fas fa-headset mr-2 text-purple-500"></i>
                  24/7 Support
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Product Details Tabs */}
        <div className="mt-12">
          <div className="bg-white rounded-lg shadow-md overflow-hidden">
            {/* Tab Navigation */}
            <div className="border-b border-gray-200">
              <nav className="flex space-x-8 px-6">
                {[
                  { id: 'specifications', label: 'Specifications', icon: 'fas fa-list' },
                  { id: 'description', label: 'Description', icon: 'fas fa-info-circle' },
                  { id: 'shipping', label: 'Shipping & Returns', icon: 'fas fa-truck' }
                ].map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                      activeTab === tab.id
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    <i className={`${tab.icon} mr-2`}></i>
                    {tab.label}
                  </button>
                ))}
              </nav>
            </div>

            {/* Tab Content */}
            <div className="p-6">
              {activeTab === 'specifications' && (
                <div>
                  <h3 className="text-lg font-semibold mb-4">Technical Specifications</h3>
                  {product.specifications && product.specifications.length > 0 ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      {Object.entries(
                        product.specifications.reduce((acc, spec) => {
                          if (!acc[spec.category]) acc[spec.category] = [];
                          acc[spec.category].push(spec);
                          return acc;
                        }, {})
                      ).map(([category, specs]) => (
                        <div key={category} className="border border-gray-200 rounded-lg p-4">
                          <h4 className="font-semibold text-gray-900 mb-3 capitalize">
                            {category.replace('_', ' ')}
                          </h4>
                          <dl className="space-y-2">
                            {specs.map((spec, index) => (
                              <div key={index} className="flex justify-between">
                                <dt className="text-sm text-gray-600">{spec.name}:</dt>
                                <dd className="text-sm font-medium text-gray-900">{spec.value}</dd>
                              </div>
                            ))}
                          </dl>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-gray-600">No specifications available for this product.</p>
                  )}
                </div>
              )}

              {activeTab === 'description' && (
                <div>
                  <h3 className="text-lg font-semibold mb-4">Product Description</h3>
                  <div className="prose max-w-none">
                    <p className="text-gray-700 leading-relaxed">
                      {product.description}
                    </p>

                    {product.features && product.features.length > 0 && (
                      <div className="mt-6">
                        <h4 className="font-semibold text-gray-900 mb-3">Key Features:</h4>
                        <ul className="space-y-2">
                          {product.features.map((feature, index) => (
                            <li key={index} className="flex items-start">
                              <i className="fas fa-check text-green-500 mr-2 mt-1"></i>
                              <span className="text-gray-700">{feature}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {activeTab === 'shipping' && (
                <div>
                  <h3 className="text-lg font-semibold mb-4">Shipping & Returns</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-3">Shipping Information</h4>
                      <ul className="space-y-2 text-sm text-gray-700">
                        <li className="flex items-start">
                          <i className="fas fa-truck text-blue-500 mr-2 mt-1"></i>
                          <span>Free delivery within Dar es Salaam for orders over 500,000 TZS</span>
                        </li>
                        <li className="flex items-start">
                          <i className="fas fa-clock text-blue-500 mr-2 mt-1"></i>
                          <span>Standard delivery: 2-3 business days</span>
                        </li>
                        <li className="flex items-start">
                          <i className="fas fa-bolt text-blue-500 mr-2 mt-1"></i>
                          <span>Express delivery: 1-2 business days (additional cost)</span>
                        </li>
                        <li className="flex items-start">
                          <i className="fas fa-store text-blue-500 mr-2 mt-1"></i>
                          <span>Store pickup available at our Kariakoo location</span>
                        </li>
                      </ul>
                    </div>

                    <div>
                      <h4 className="font-semibold text-gray-900 mb-3">Returns & Warranty</h4>
                      <ul className="space-y-2 text-sm text-gray-700">
                        <li className="flex items-start">
                          <i className="fas fa-undo text-green-500 mr-2 mt-1"></i>
                          <span>30-day return policy for unopened items</span>
                        </li>
                        <li className="flex items-start">
                          <i className="fas fa-shield-alt text-green-500 mr-2 mt-1"></i>
                          <span>Official manufacturer warranty included</span>
                        </li>
                        <li className="flex items-start">
                          <i className="fas fa-tools text-green-500 mr-2 mt-1"></i>
                          <span>Free device setup and data transfer service</span>
                        </li>
                        <li className="flex items-start">
                          <i className="fas fa-headset text-green-500 mr-2 mt-1"></i>
                          <span>24/7 customer support available</span>
                        </li>
                      </ul>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Related Products */}
        <div className="mt-16">
          <ProductRecommendations
            productId={product._id}
            type="related"
            title="You might also like"
            limit={8}
          />
        </div>
      </div>
    </div>
  );
};

export default ProductDetailPage;
