import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { useLoading } from '../contexts/LoadingContext';
import { get, post, put, del } from '../api/unifiedApiClient';
import { formatPrice } from '../utils/priceFormatter';
import Button from '../components/ui/Button';
import LoadingState from '../components/ui/LoadingState';
import Alert from '../components/ui/Alert';
import { useDeviceDetect } from '../utils/mobileOptimization';
import {
  getGuestCart,
  updateGuestCartItem,
  removeFromGuestCart,
  formatCartForDisplay,
  transferGuestCartToUser
} from '../services/guestCartService';

const CartPage = () => {
  const { user } = useAuth();
  const { setLoading, isLoading } = useLoading();
  const { isMobile, isTablet } = useDeviceDetect();
  const navigate = useNavigate();

  const [cart, setCart] = useState(null);
  const [error, setError] = useState('');
  const [updating, setUpdating] = useState({});

  useEffect(() => {
    let isMounted = true;
    const abortController = new AbortController();

    const fetchCart = async () => {
      setLoading('cart', true);
      try {
        if (user) {
          // For authenticated users, fetch from API
          const response = await safeApiRequest({
            method: 'GET',
            url: '/cart',
            signal: abortController.signal
          });
          if (isMounted) {
            setCart(response.data);
          }
        } else {
          // For guest users, get from localStorage
          const guestCart = getGuestCart();
          if (isMounted) {
            setCart(formatCartForDisplay(guestCart));
          }
        }
      } catch (error) {
        if (error.name === 'AbortError') {
          return; // Request was cancelled
        }
        if (isMounted) {
          setError('Failed to load cart');
          console.error('Cart fetch error:', error);
        }
      } finally {
        if (isMounted) {
          setLoading('cart', false);
        }
      }
    };

    fetchCart();

    return () => {
      isMounted = false;
      abortController.abort();
    };
  }, [user, setLoading]);

  const fetchCart = async () => {
    setLoading('cart', true);
    try {
      if (user) {
        // For authenticated users, fetch from API
        const response = await safeApiRequest({
          method: 'GET',
          url: '/cart'
        });
        setCart(response.data);
      } else {
        // For guest users, get from localStorage
        const guestCart = getGuestCart();
        setCart(formatCartForDisplay(guestCart));
      }
    } catch (error) {
      setError('Failed to load cart');
      console.error('Cart fetch error:', error);
    } finally {
      setLoading('cart', false);
    }
  };

  const updateQuantity = async (itemId, newQuantity) => {
    if (newQuantity < 1) return;

    setUpdating(prev => ({ ...prev, [itemId]: true }));
    try {
      if (user) {
        // For authenticated users, update via API
        const response = await safeApiRequest({
          method: 'PATCH',
          url: `/cart/${itemId}`,
          data: { quantity: newQuantity }
        });
        setCart(response.data);
      } else {
        // For guest users, update localStorage
        const updatedCart = updateGuestCartItem(itemId, newQuantity);
        setCart(formatCartForDisplay(updatedCart));
      }

      // Update header cart count
      if (window.refreshCartCount) {
        window.refreshCartCount();
      }
    } catch (error) {
      setError('Failed to update quantity');
    } finally {
      setUpdating(prev => ({ ...prev, [itemId]: false }));
    }
  };

  const removeItem = async (itemId) => {
    setUpdating(prev => ({ ...prev, [itemId]: true }));
    try {
      if (user) {
        // For authenticated users, remove via API
        const response = await safeApiRequest({
          method: 'DELETE',
          url: `/cart/${itemId}`
        });
        setCart(response.data);
      } else {
        // For guest users, remove from localStorage
        const updatedCart = removeFromGuestCart(itemId);
        setCart(formatCartForDisplay(updatedCart));
      }

      // Update header cart count
      if (window.refreshCartCount) {
        window.refreshCartCount();
      }
    } catch (error) {
      setError('Failed to remove item');
    } finally {
      setUpdating(prev => ({ ...prev, [itemId]: false }));
    }
  };

  const proceedToCheckout = () => {
    if (!cart || cart.items.length === 0) return;
    navigate('/checkout');
  };

  // Remove the user check - guest users should be able to view their cart

  if (isLoading('cart')) {
    return <LoadingState message="Loading your cart..." />;
  }

  if (!cart || cart.items.length === 0) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Shopping Cart</h1>
          <div className="bg-gray-50 rounded-lg p-8">
            <i className="fas fa-shopping-cart text-4xl text-gray-400 mb-4"></i>
            <p className="text-gray-600 mb-4">Your cart is empty</p>
            <p className="text-sm text-gray-500 mb-6">
              {user ?
                "Add some products to get started!" :
                "Browse our products and add items to your cart. No account required!"
              }
            </p>
            <Link to="/products">
              <Button>Continue Shopping</Button>
            </Link>
            {!user && (
              <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <p className="text-sm text-blue-800 mb-2">
                  <i className="fas fa-info-circle mr-2"></i>
                  Shopping as a guest? No problem!
                </p>
                <p className="text-xs text-blue-600">
                  You can complete your purchase without creating an account.
                  Your cart is saved locally on this device.
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold mb-6">Shopping Cart</h1>

      {/* Guest User Information */}
      {!user && cart && cart.items.length > 0 && (
        <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
          <div className="flex items-start">
            <i className="fas fa-shopping-cart text-green-600 mt-1 mr-3"></i>
            <div>
              <h3 className="text-sm font-medium text-green-800 mb-1">
                Shopping as Guest
              </h3>
              <p className="text-sm text-green-700 mb-2">
                You can complete your purchase without creating an account. Your cart is saved on this device.
              </p>
              <div className="flex flex-wrap gap-2 text-xs">
                <span className="inline-flex items-center px-2 py-1 bg-green-100 text-green-800 rounded">
                  <i className="fas fa-check mr-1"></i>
                  No registration required
                </span>
                <span className="inline-flex items-center px-2 py-1 bg-green-100 text-green-800 rounded">
                  <i className="fas fa-truck mr-1"></i>
                  Fast checkout
                </span>
                <span className="inline-flex items-center px-2 py-1 bg-green-100 text-green-800 rounded">
                  <i className="fas fa-shield-alt mr-1"></i>
                  Secure payment
                </span>
              </div>
            </div>
          </div>
        </div>
      )}

      {error && (
        <Alert
          type="error"
          message={error}
          onClose={() => setError('')}
          className="mb-6"
        />
      )}

      <div className={`grid gap-8 ${isMobile ? 'grid-cols-1' : 'lg:grid-cols-3'}`}>
        {/* Cart Items */}
        <div className="lg:col-span-2">
          <div className="bg-white rounded-lg shadow-sm border">
            <div className="p-6 border-b">
              <h2 className="text-lg font-semibold">Cart Items ({cart.item_count})</h2>
            </div>
            
            <div className="divide-y">
              {cart.items.map((item) => {
                const itemId = item._id || item.id;
                return (
                  <CartItem
                    key={itemId}
                    item={item}
                    onUpdateQuantity={updateQuantity}
                    onRemove={removeItem}
                    updating={updating[itemId]}
                    isMobile={isMobile}
                  />
                );
              })}
            </div>
          </div>
        </div>

        {/* Cart Summary */}
        <div className="lg:col-span-1">
          <CartSummary
            cart={cart}
            onProceedToCheckout={proceedToCheckout}
            isMobile={isMobile}
          />
        </div>
      </div>
    </div>
  );
};

const CartItem = ({ item, onUpdateQuantity, onRemove, updating, isMobile }) => {
  // Handle both authenticated user cart items (item.product) and guest cart items (item directly)
  const product = item.product || item;
  const productName = product.name || item.name;
  const productImage = product.image_url || product.primary_image?.url || product.images?.[0]?.url || item.image || '/placeholder-phone.jpg';
  const productPrice = item.price || product.price;
  const itemId = item._id || item.id;

  return (
    <div className={`p-4 ${isMobile ? 'p-3' : 'p-6'}`}>
      <div className={`flex gap-4 ${isMobile ? 'flex-col' : 'flex-row'}`}>
        {/* Product Image */}
        <div className={`${isMobile ? 'w-20 h-20 mx-auto' : 'w-24 h-24'} flex-shrink-0`}>
          <img
            src={productImage}
            alt={productName}
            className="w-full h-full object-cover rounded-lg"
          />
        </div>

        {/* Product Details */}
        <div className="flex-grow">
          <div className={`flex justify-between items-start mb-2 ${isMobile ? 'flex-col gap-2' : ''}`}>
            <div className={isMobile ? 'text-center w-full' : ''}>
              <h3 className={`font-semibold text-gray-900 ${isMobile ? 'text-lg' : ''}`}>
                {productName}
              </h3>
              <p className="text-sm text-gray-600">{product.brand || item.brand || ''} {product.model || item.model || ''}</p>
              {item.variant_sku && (
                <p className="text-xs text-gray-500">SKU: {item.variant_sku}</p>
              )}

              {/* Phone-specific details */}
              {item.imei && (
                <p className="text-xs text-blue-600 font-mono">IMEI: {item.imei}</p>
              )}
              {item.warranty_option && item.warranty_option !== 'none' && (
                <p className="text-xs text-green-600">
                  <i className="fas fa-shield-alt mr-1"></i>
                  {item.warranty_duration} months warranty
                </p>
              )}
              {item.trade_in_value > 0 && (
                <p className="text-xs text-orange-600">
                  <i className="fas fa-exchange-alt mr-1"></i>
                  Trade-in: -{formatPrice(item.trade_in_value)}
                </p>
              )}
            </div>

            <button
              onClick={() => onRemove(itemId)}
              disabled={updating}
              className={`text-red-500 hover:text-red-700 p-2 rounded-full hover:bg-red-50 ${
                isMobile ? 'self-center' : ''
              }`}
            >
              <i className={`fas fa-trash ${isMobile ? 'text-lg' : 'text-sm'}`}></i>
            </button>
          </div>

          {/* Price and Quantity */}
          <div className={`flex items-center justify-between ${isMobile ? 'flex-col gap-3' : ''}`}>
            <div className={`flex items-center gap-3 ${isMobile ? 'flex-col' : ''}`}>
              <span className={`font-semibold text-blue-600 ${isMobile ? 'text-lg' : ''}`}>
                {formatPrice(productPrice)}
                {item.warranty_price > 0 && (
                  <span className="text-sm text-gray-500 ml-1">
                    + {formatPrice(item.warranty_price)} warranty
                  </span>
                )}
              </span>

              {/* Quantity Controls - Enhanced for mobile */}
              <div className={`flex items-center border rounded-lg ${isMobile ? 'scale-110' : ''}`}>
                <button
                  onClick={() => onUpdateQuantity(itemId, item.quantity - 1)}
                  disabled={updating || item.quantity <= 1 || item.imei} // Can't change quantity for IMEI items
                  className={`px-4 py-2 hover:bg-gray-50 disabled:opacity-50 ${
                    isMobile ? 'px-5 py-3' : ''
                  }`}
                >
                  <i className="fas fa-minus text-xs"></i>
                </button>
                <span className={`px-4 py-2 border-x min-w-[3rem] text-center ${
                  isMobile ? 'px-5 py-3 min-w-[4rem] text-lg' : ''
                }`}>
                  {item.quantity}
                </span>
                <button
                  onClick={() => onUpdateQuantity(itemId, item.quantity + 1)}
                  disabled={updating || item.imei} // Can't change quantity for IMEI items
                  className={`px-4 py-2 hover:bg-gray-50 disabled:opacity-50 ${
                    isMobile ? 'px-5 py-3' : ''
                  }`}
                >
                  <i className="fas fa-plus text-xs"></i>
                </button>
              </div>

              {item.imei && (
                <p className="text-xs text-gray-500 italic">
                  Quantity fixed for IMEI items
                </p>
              )}
            </div>

            <div className={`text-right ${isMobile ? 'text-center' : ''}`}>
              <p className={`font-semibold ${isMobile ? 'text-xl text-blue-600' : ''}`}>
                {formatPrice((item.price + (item.warranty_price || 0)) * item.quantity - (item.trade_in_value || 0))}
              </p>
              {(item.warranty_price > 0 || item.trade_in_value > 0) && (
                <p className="text-xs text-gray-500">
                  Base: {formatPrice(item.price * item.quantity)}
                  {item.warranty_price > 0 && ` + ${formatPrice(item.warranty_price)}`}
                  {item.trade_in_value > 0 && ` - ${formatPrice(item.trade_in_value)}`}
                </p>
              )}
            </div>
          </div>

          {/* Accessories */}
          {item.accessories && item.accessories.length > 0 && (
            <div className="mt-3 pt-3 border-t">
              <p className="text-sm font-medium text-gray-700 mb-2">Accessories:</p>
              <div className="space-y-1">
                {item.accessories.map((accessory, index) => (
                  <div key={index} className="flex justify-between text-sm">
                    <span>{accessory.name} x{accessory.quantity}</span>
                    <span>{formatPrice(accessory.price * accessory.quantity)}</span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

const CartSummary = ({ cart, onProceedToCheckout, isMobile }) => {
  return (
    <div className="bg-white rounded-lg shadow-sm border sticky top-4">
      <div className="p-6">
        <h2 className="text-lg font-semibold mb-4">Order Summary</h2>
        
        <div className="space-y-3 mb-6">
          <div className="flex justify-between">
            <span>Subtotal</span>
            <span>{formatPrice(cart.subtotal)}</span>
          </div>
          <div className="flex justify-between">
            <span>Shipping</span>
            <span>{cart.shipping_cost > 0 ? formatPrice(cart.shipping_cost) : 'Calculated at checkout'}</span>
          </div>
          <div className="flex justify-between">
            <span>Tax</span>
            <span>{formatPrice(cart.tax_amount)}</span>
          </div>
          <div className="border-t pt-3">
            <div className="flex justify-between font-semibold text-lg">
              <span>Total</span>
              <span className="text-blue-600">{formatPrice(cart.total)}</span>
            </div>
          </div>
        </div>

        <Button
          onClick={onProceedToCheckout}
          fullWidth
          className="mb-4"
        >
          Proceed to Checkout
        </Button>

        <Link to="/products">
          <Button variant="outline" fullWidth>
            Continue Shopping
          </Button>
        </Link>
      </div>
    </div>
  );
};

export default CartPage;
