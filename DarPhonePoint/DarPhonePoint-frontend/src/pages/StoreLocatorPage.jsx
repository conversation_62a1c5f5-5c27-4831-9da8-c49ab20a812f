import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import Button from '../components/ui/Button';
import Alert from '../components/ui/Alert';

const StoreLocatorPage = () => {
  const [selectedStore, setSelectedStore] = useState(null);
  const [contactForm, setContactForm] = useState({
    name: '',
    phone: '',
    email: '',
    service: '',
    message: '',
    preferredTime: ''
  });
  const [formSubmitted, setFormSubmitted] = useState(false);

  // Phone Point Dar store locations in Dar es Salaam
  const stores = [
    {
      id: 1,
      name: 'Phone Point Dar - Kariakoo Main Store',
      address: 'Msimbazi Street, Kariakoo Market, Dar es Salaam',
      coordinates: { lat: -6.8235, lng: 39.2695 },
      phone: '+255 22 218 0000',
      whatsapp: '+255 754 123 456',
      email: '<EMAIL>',
      hours: {
        weekdays: '8:00 AM - 8:00 PM',
        saturday: '8:00 AM - 9:00 PM',
        sunday: '9:00 AM - 6:00 PM'
      },
      services: [
        'Phone Sales & Accessories',
        'Phone Repairs & Screen Replacement',
        'Trade-In Program',
        'Warranty Services',
        'Technical Support',
        'SIM Card Registration',
        'Mobile Money Services'
      ],
      features: [
        'Air Conditioned',
        'Free WiFi',
        'Parking Available',
        'Wheelchair Accessible',
        'Security Guard',
        'CCTV Monitored'
      ],
      languages: ['Swahili', 'English', 'Arabic'],
      manager: 'Amina Hassan',
      established: '2020',
      isMain: true
    },
    {
      id: 2,
      name: 'Phone Point Dar - Mlimani City',
      address: 'Mlimani City Mall, Sam Nujoma Road, Dar es Salaam',
      coordinates: { lat: -6.7735, lng: 39.2695 },
      phone: '+255 22 277 0000',
      whatsapp: '+255 754 123 457',
      email: '<EMAIL>',
      hours: {
        weekdays: '10:00 AM - 10:00 PM',
        saturday: '10:00 AM - 10:00 PM',
        sunday: '10:00 AM - 8:00 PM'
      },
      services: [
        'Phone Sales & Accessories',
        'Quick Repairs',
        'Warranty Services',
        'Technical Support',
        'Product Demonstrations'
      ],
      features: [
        'Mall Location',
        'Food Court Nearby',
        'Free Parking',
        'Air Conditioned',
        'Free WiFi'
      ],
      languages: ['Swahili', 'English'],
      manager: 'John Mwalimu',
      established: '2022',
      isMain: false
    },
    {
      id: 3,
      name: 'Phone Point Dar - Sinza Service Center',
      address: 'Sinza Mori, Near Sinza Hospital, Dar es Salaam',
      coordinates: { lat: -6.7924, lng: 39.2083 },
      phone: '+255 22 260 0000',
      whatsapp: '+255 754 123 458',
      email: '<EMAIL>',
      hours: {
        weekdays: '8:00 AM - 6:00 PM',
        saturday: '8:00 AM - 4:00 PM',
        sunday: 'Closed'
      },
      services: [
        'Phone Repairs & Diagnostics',
        'Screen Replacement',
        'Battery Replacement',
        'Water Damage Repair',
        'Software Updates',
        'Data Recovery'
      ],
      features: [
        'Specialized Repair Center',
        'Certified Technicians',
        'Genuine Parts Only',
        'Express Service Available',
        'Pickup & Delivery'
      ],
      languages: ['Swahili', 'English'],
      manager: 'Grace Kimaro',
      established: '2021',
      isMain: false,
      isServiceCenter: true
    }
  ];

  const services = [
    { value: 'repair', label: 'Phone Repair' },
    { value: 'purchase', label: 'Purchase Inquiry' },
    { value: 'trade-in', label: 'Trade-In Valuation' },
    { value: 'warranty', label: 'Warranty Claim' },
    { value: 'support', label: 'Technical Support' },
    { value: 'other', label: 'Other' }
  ];

  const handleContactSubmit = (e) => {
    e.preventDefault();
    // Simulate form submission
    setFormSubmitted(true);
    setTimeout(() => {
      setFormSubmitted(false);
      setContactForm({
        name: '',
        phone: '',
        email: '',
        service: '',
        message: '',
        preferredTime: ''
      });
    }, 3000);
  };

  const handleInputChange = (field, value) => {
    setContactForm(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const openInMaps = (store) => {
    const query = encodeURIComponent(store.address);
    const mapsUrl = `https://www.google.com/maps/search/?api=1&query=${query}`;
    window.open(mapsUrl, '_blank');
  };

  const callStore = (phone) => {
    window.location.href = `tel:${phone}`;
  };

  const openWhatsApp = (whatsapp, storeName) => {
    const message = encodeURIComponent(`Hello! I'm interested in visiting ${storeName}. Can you help me with more information?`);
    const whatsappUrl = `https://wa.me/${whatsapp.replace(/[^0-9]/g, '')}?text=${message}`;
    window.open(whatsappUrl, '_blank');
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-600 to-blue-800 text-white">
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold">Store Locator 📍</h1>
              <p className="text-blue-100 mt-2">Find Phone Point Dar stores in Dar es Salaam</p>
            </div>
            <div className="text-right">
              <Link to="/dashboard">
                <Button className="bg-white text-blue-600 hover:bg-gray-100">
                  <i className="fas fa-arrow-left mr-2"></i>
                  Back to Dashboard
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        {/* Store Locations */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
          {/* Store List */}
          <div>
            <h2 className="text-2xl font-bold mb-6">Our Locations</h2>
            <div className="space-y-6">
              {stores.map((store) => (
                <div
                  key={store.id}
                  className={`bg-white rounded-lg shadow-md p-6 cursor-pointer transition-all ${
                    selectedStore?.id === store.id ? 'ring-2 ring-blue-500' : 'hover:shadow-lg'
                  }`}
                  onClick={() => setSelectedStore(store)}
                >
                  <div className="flex justify-between items-start mb-4">
                    <div>
                      <h3 className="text-lg font-bold text-gray-800">
                        {store.name}
                        {store.isMain && (
                          <span className="ml-2 bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
                            Main Store
                          </span>
                        )}
                        {store.isServiceCenter && (
                          <span className="ml-2 bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">
                            Service Center
                          </span>
                        )}
                      </h3>
                      <p className="text-gray-600 text-sm mt-1">{store.address}</p>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                      <h4 className="font-semibold text-sm text-gray-700 mb-2">Contact</h4>
                      <p className="text-sm text-gray-600">📞 {store.phone}</p>
                      <p className="text-sm text-gray-600">📱 {store.whatsapp}</p>
                      <p className="text-sm text-gray-600">✉️ {store.email}</p>
                    </div>
                    <div>
                      <h4 className="font-semibold text-sm text-gray-700 mb-2">Hours</h4>
                      <p className="text-sm text-gray-600">Mon-Fri: {store.hours.weekdays}</p>
                      <p className="text-sm text-gray-600">Saturday: {store.hours.saturday}</p>
                      <p className="text-sm text-gray-600">Sunday: {store.hours.sunday}</p>
                    </div>
                  </div>

                  <div className="flex flex-wrap gap-2 mb-4">
                    {store.services.slice(0, 3).map((service, index) => (
                      <span
                        key={index}
                        className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full"
                      >
                        {service}
                      </span>
                    ))}
                    {store.services.length > 3 && (
                      <span className="text-xs text-gray-500">
                        +{store.services.length - 3} more
                      </span>
                    )}
                  </div>

                  <div className="flex space-x-2">
                    <Button
                      onClick={(e) => {
                        e.stopPropagation();
                        openInMaps(store);
                      }}
                      size="sm"
                      variant="outline"
                    >
                      <i className="fas fa-map-marker-alt mr-1"></i>
                      Directions
                    </Button>
                    <Button
                      onClick={(e) => {
                        e.stopPropagation();
                        callStore(store.phone);
                      }}
                      size="sm"
                      variant="outline"
                    >
                      <i className="fas fa-phone mr-1"></i>
                      Call
                    </Button>
                    <Button
                      onClick={(e) => {
                        e.stopPropagation();
                        openWhatsApp(store.whatsapp, store.name);
                      }}
                      size="sm"
                      className="bg-green-600 hover:bg-green-700"
                    >
                      <i className="fab fa-whatsapp mr-1"></i>
                      WhatsApp
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Store Details */}
          <div>
            {selectedStore ? (
              <div className="bg-white rounded-lg shadow-md p-6 sticky top-4">
                <h2 className="text-xl font-bold mb-4">{selectedStore.name}</h2>
                
                {/* Store Image Placeholder */}
                <div className="w-full h-48 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg mb-6 flex items-center justify-center">
                  <div className="text-center text-white">
                    <i className="fas fa-store text-4xl mb-2"></i>
                    <p className="text-sm">Store Photo</p>
                  </div>
                </div>

                {/* Store Info */}
                <div className="space-y-4">
                  <div>
                    <h4 className="font-semibold text-gray-700 mb-2">Services Offered</h4>
                    <div className="grid grid-cols-1 gap-1">
                      {selectedStore.services.map((service, index) => (
                        <div key={index} className="flex items-center text-sm text-gray-600">
                          <i className="fas fa-check text-green-500 mr-2"></i>
                          {service}
                        </div>
                      ))}
                    </div>
                  </div>

                  <div>
                    <h4 className="font-semibold text-gray-700 mb-2">Store Features</h4>
                    <div className="flex flex-wrap gap-2">
                      {selectedStore.features.map((feature, index) => (
                        <span
                          key={index}
                          className="bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded"
                        >
                          {feature}
                        </span>
                      ))}
                    </div>
                  </div>

                  <div>
                    <h4 className="font-semibold text-gray-700 mb-2">Additional Info</h4>
                    <div className="text-sm text-gray-600 space-y-1">
                      <p><strong>Manager:</strong> {selectedStore.manager}</p>
                      <p><strong>Established:</strong> {selectedStore.established}</p>
                      <p><strong>Languages:</strong> {selectedStore.languages.join(', ')}</p>
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              <div className="bg-white rounded-lg shadow-md p-12 text-center">
                <i className="fas fa-map-marker-alt text-gray-400 text-4xl mb-4"></i>
                <h3 className="text-lg font-semibold text-gray-600 mb-2">
                  Select a store to view details
                </h3>
                <p className="text-gray-500">
                  Click on any store location to see more information
                </p>
              </div>
            )}
          </div>
        </div>

        {/* Contact Form */}
        <div className="bg-white rounded-lg shadow-md p-8">
          <h2 className="text-2xl font-bold mb-6">Contact Our Support Team</h2>
          
          {formSubmitted ? (
            <Alert
              type="success"
              message="Your message has been sent! We'll get back to you within 24 hours."
              className="mb-6"
            />
          ) : (
            <form onSubmit={handleContactSubmit} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Full Name *
                  </label>
                  <input
                    type="text"
                    required
                    value={contactForm.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Enter your full name"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Phone Number *
                  </label>
                  <input
                    type="tel"
                    required
                    value={contactForm.phone}
                    onChange={(e) => handleInputChange('phone', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="+255 7XX XXX XXX"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Email Address
                  </label>
                  <input
                    type="email"
                    value={contactForm.email}
                    onChange={(e) => handleInputChange('email', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="<EMAIL>"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Service Needed *
                  </label>
                  <select
                    required
                    value={contactForm.service}
                    onChange={(e) => handleInputChange('service', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">Select service</option>
                    {services.map((service) => (
                      <option key={service.value} value={service.value}>
                        {service.label}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Preferred Contact Time
                </label>
                <select
                  value={contactForm.preferredTime}
                  onChange={(e) => handleInputChange('preferredTime', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Any time</option>
                  <option value="morning">Morning (8AM - 12PM)</option>
                  <option value="afternoon">Afternoon (12PM - 5PM)</option>
                  <option value="evening">Evening (5PM - 8PM)</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Message *
                </label>
                <textarea
                  required
                  rows={4}
                  value={contactForm.message}
                  onChange={(e) => handleInputChange('message', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Please describe how we can help you..."
                ></textarea>
              </div>

              <Button
                type="submit"
                className="w-full bg-blue-600 hover:bg-blue-700"
              >
                <i className="fas fa-paper-plane mr-2"></i>
                Send Message
              </Button>
            </form>
          )}
        </div>

        {/* Emergency Contact */}
        <div className="mt-8 bg-red-50 border border-red-200 rounded-lg p-6">
          <h3 className="font-semibold text-red-800 mb-3">
            <i className="fas fa-exclamation-triangle mr-2"></i>
            Emergency Support
          </h3>
          <p className="text-red-700 mb-4">
            For urgent technical issues or security concerns, contact our 24/7 emergency line:
          </p>
          <div className="flex flex-col sm:flex-row gap-4">
            <Button
              onClick={() => callStore('+255 754 000 911')}
              className="bg-red-600 hover:bg-red-700"
            >
              <i className="fas fa-phone mr-2"></i>
              Call Emergency: +255 754 000 911
            </Button>
            <Button
              onClick={() => openWhatsApp('+255 754 000 911', 'Emergency Support')}
              className="bg-green-600 hover:bg-green-700"
            >
              <i className="fab fa-whatsapp mr-2"></i>
              WhatsApp Emergency
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default StoreLocatorPage;
