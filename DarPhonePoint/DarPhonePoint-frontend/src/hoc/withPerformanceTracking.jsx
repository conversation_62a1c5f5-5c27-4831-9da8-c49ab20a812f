import React, { useEffect, useRef } from 'react';
import { startTiming, endTiming, recordRenderTime } from '../utils/performanceMonitor';

/**
 * Higher-order component that tracks component performance
 * 
 * @param {React.Component} Component - Component to track
 * @param {Object} options - Tracking options
 * @param {Boolean} options.trackMounts - Whether to track component mounts
 * @param {Boolean} options.trackRenders - Whether to track component renders
 * @param {Boolean} options.trackUnmounts - Whether to track component unmounts
 * @returns {React.Component} Wrapped component with performance tracking
 */
const withPerformanceTracking = (
  Component,
  { trackMounts = true, trackRenders = true, trackUnmounts = true } = {}
) => {
  // Get display name for the component
  const displayName = Component.displayName || Component.name || 'Component';
  
  // Create a wrapped component
  const WrappedComponent = (props) => {
    // Refs to track render time
    const renderStartTimeRef = useRef(0);
    const mountTimeRef = useRef(null);
    
    // Track component mount
    useEffect(() => {
      if (trackMounts) {
        const mountTiming = startTiming(displayName, 'page');
        mountTimeRef.current = mountTiming;
      }
      
      // Track component unmount
      return () => {
        if (trackUnmounts && mountTimeRef.current) {
          endTiming(mountTimeRef.current, true, { unmounted: true });
        }
      };
    }, []);
    
    // Track render start time
    if (trackRenders) {
      renderStartTimeRef.current = performance.now();
    }
    
    // After render, record the render time
    useEffect(() => {
      if (trackRenders) {
        const renderTime = performance.now() - renderStartTimeRef.current;
        recordRenderTime(displayName, renderTime);
      }
    });
    
    // Render the original component
    return <Component {...props} />;
  };
  
  // Set display name for the wrapped component
  WrappedComponent.displayName = `withPerformanceTracking(${displayName})`;
  
  return WrappedComponent;
};

export default withPerformanceTracking;
