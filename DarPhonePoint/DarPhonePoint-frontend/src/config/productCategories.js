/**
 * Product categories configuration for Phone Point Dar
 * Defines categories, brands, and form fields for different product types
 */

export const PRODUCT_CATEGORIES = {
  // Mobile & Tablets
  smartphone: {
    label: 'Smartphone',
    icon: '📱',
    brands: ['Apple', 'Samsung', 'Huawei', 'Xiaomi', 'Oppo', 'Vivo', 'Realme', 'OnePlus', 'Google', 'Sony', 'Nokia', 'Tecno', 'Infinix', 'Itel', 'Redmi', 'Honor', 'Motorola', 'Nothing'],
    fields: [
      { name: 'operating_system', label: 'Operating System', type: 'select', options: ['Android', 'iOS'], required: true },
      { name: 'storage_capacity', label: 'Storage', type: 'select', options: ['64GB', '128GB', '256GB', '512GB', '1TB'], required: true },
      { name: 'ram', label: 'RAM', type: 'select', options: ['4GB', '6GB', '8GB', '12GB', '16GB'], required: true },
      { name: 'screen_size', label: 'Screen Size', type: 'text', placeholder: 'e.g., 6.1 inches', required: true },
      { name: 'camera_specs', label: 'Main Camera', type: 'text', placeholder: 'e.g., 48MP Triple Camera', required: true },
      { name: 'battery_capacity', label: 'Battery', type: 'text', placeholder: 'e.g., 4000mAh', required: true },
      { name: 'network_support', label: 'Network', type: 'select', options: ['4G LTE', '5G'], required: false },
      { name: 'color_options', label: 'Available Colors', type: 'text', placeholder: 'e.g., Black, White, Blue', required: false }
    ]
  },

  tablet: {
    label: 'Tablet',
    icon: '📱',
    brands: ['Apple', 'Samsung', 'Huawei', 'Xiaomi', 'Lenovo', 'Microsoft'],
    fields: [
      { name: 'operating_system', label: 'Operating System', type: 'select', options: ['Android', 'iOS', 'Windows'], required: true },
      { name: 'storage_capacity', label: 'Storage', type: 'select', options: ['64GB', '128GB', '256GB', '512GB', '1TB'], required: true },
      { name: 'ram', label: 'RAM', type: 'select', options: ['4GB', '6GB', '8GB', '12GB', '16GB'], required: true },
      { name: 'screen_size', label: 'Screen Size', type: 'text', placeholder: 'e.g., 10.9 inches', required: true },
      { name: 'display_resolution', label: 'Resolution', type: 'text', placeholder: 'e.g., 2360 x 1640', required: false },
      { name: 'cellular_support', label: 'Cellular', type: 'select', options: ['Wi-Fi Only', 'Wi-Fi + Cellular'], required: false }
    ]
  },

  smartwatch: {
    label: 'Smartwatch',
    icon: '⌚',
    brands: ['Apple', 'Samsung', 'Huawei', 'Xiaomi', 'Garmin', 'Fitbit'],
    fields: [
      { name: 'operating_system', label: 'Operating System', type: 'select', options: ['watchOS', 'Wear OS', 'Tizen', 'Proprietary'], required: true },
      { name: 'display_size', label: 'Display Size', type: 'text', placeholder: 'e.g., 45mm', required: true },
      { name: 'battery_life', label: 'Battery Life', type: 'text', placeholder: 'e.g., 18 hours', required: true },
      { name: 'water_resistance', label: 'Water Resistance', type: 'text', placeholder: 'e.g., 50m', required: true },
      { name: 'health_sensors', label: 'Health Sensors', type: 'text', placeholder: 'e.g., Heart Rate, SpO2, ECG', required: false },
      { name: 'gps_support', label: 'GPS', type: 'select', options: ['Yes', 'No'], required: false }
    ]
  },

  // Computers
  laptop: {
    label: 'Laptop',
    icon: '💻',
    brands: ['Dell', 'HP', 'Lenovo', 'ASUS', 'Acer', 'MSI', 'Razer', 'Apple', 'Huawei', 'Infinix'],
    fields: [
      { name: 'processor', label: 'Processor', type: 'text', placeholder: 'e.g., Intel Core i7-13700H', required: true },
      { name: 'ram', label: 'RAM', type: 'select', options: ['8GB', '16GB', '32GB', '64GB'], required: true },
      { name: 'storage_type', label: 'Storage Type', type: 'select', options: ['SSD', 'HDD', 'Hybrid'], required: true },
      { name: 'storage_capacity', label: 'Storage Capacity', type: 'select', options: ['256GB', '512GB', '1TB', '2TB'], required: true },
      { name: 'screen_size', label: 'Screen Size', type: 'select', options: ['13.3"', '14"', '15.6"', '17.3"'], required: true },
      { name: 'graphics_card', label: 'Graphics Card', type: 'text', placeholder: 'e.g., NVIDIA RTX 4060', required: true },
      { name: 'operating_system', label: 'Operating System', type: 'select', options: ['Windows 11', 'macOS', 'Linux', 'FreeDOS'], required: true },
      { name: 'battery_life', label: 'Battery Life', type: 'text', placeholder: 'e.g., 8 hours', required: false },
      { name: 'weight', label: 'Weight', type: 'text', placeholder: 'e.g., 2.3kg', required: false }
    ]
  },

  desktop: {
    label: 'Desktop Computer',
    icon: '🖥️',
    brands: ['Dell', 'HP', 'Lenovo', 'ASUS', 'MSI', 'Custom Build'],
    fields: [
      { name: 'processor', label: 'Processor', type: 'text', placeholder: 'e.g., Intel Core i7-13700K', required: true },
      { name: 'ram', label: 'RAM', type: 'select', options: ['8GB', '16GB', '32GB', '64GB', '128GB'], required: true },
      { name: 'storage_type', label: 'Storage Type', type: 'select', options: ['SSD', 'HDD', 'Hybrid'], required: true },
      { name: 'storage_capacity', label: 'Storage Capacity', type: 'select', options: ['512GB', '1TB', '2TB', '4TB'], required: true },
      { name: 'graphics_card', label: 'Graphics Card', type: 'text', placeholder: 'e.g., NVIDIA RTX 4070', required: true },
      { name: 'motherboard', label: 'Motherboard', type: 'text', placeholder: 'e.g., ASUS ROG Strix B650', required: false },
      { name: 'power_supply', label: 'Power Supply', type: 'text', placeholder: 'e.g., 750W 80+ Gold', required: false }
    ]
  },

  // Gaming
  gaming_console: {
    label: 'Gaming Console',
    icon: '🎮',
    brands: ['PlayStation', 'Xbox', 'Nintendo'],
    fields: [
      { name: 'platform', label: 'Platform', type: 'select', options: ['PlayStation 5', 'Xbox Series X', 'Xbox Series S', 'Nintendo Switch'], required: true },
      { name: 'storage_capacity', label: 'Storage', type: 'select', options: ['512GB', '825GB', '1TB'], required: true },
      { name: 'max_resolution', label: 'Max Resolution', type: 'select', options: ['1080p', '1440p', '4K UHD'], required: true },
      { name: 'max_framerate', label: 'Max Frame Rate', type: 'select', options: ['60fps', '120fps'], required: false },
      { name: 'backwards_compatibility', label: 'Backwards Compatible', type: 'text', placeholder: 'e.g., PS4 Games', required: false },
      { name: 'controller_included', label: 'Controller Included', type: 'select', options: ['Yes', 'No'], required: false }
    ]
  },

  // Audio/Visual
  camera: {
    label: 'Camera',
    icon: '📷',
    brands: ['Canon', 'Nikon', 'Sony', 'Fujifilm', 'Olympus', 'Panasonic', 'GoPro', 'DJI'],
    fields: [
      { name: 'sensor_type', label: 'Sensor Type', type: 'select', options: ['Full Frame', 'APS-C', 'Micro Four Thirds', 'Action Camera'], required: true },
      { name: 'megapixels', label: 'Megapixels', type: 'text', placeholder: 'e.g., 24.2MP', required: true },
      { name: 'lens_mount', label: 'Lens Mount', type: 'text', placeholder: 'e.g., Canon EF', required: false },
      { name: 'video_recording', label: 'Video Recording', type: 'select', options: ['1080p', '4K 30fps', '4K 60fps', '8K'], required: true },
      { name: 'image_stabilization', label: 'Image Stabilization', type: 'select', options: ['Yes', 'No'], required: false },
      { name: 'weather_sealing', label: 'Weather Sealed', type: 'select', options: ['Yes', 'No'], required: false }
    ]
  },

  monitor: {
    label: 'Monitor',
    icon: '🖥️',
    brands: ['Samsung', 'LG', 'ASUS', 'Dell', 'HP', 'MSI', 'Acer'],
    fields: [
      { name: 'screen_size', label: 'Screen Size', type: 'select', options: ['21.5"', '24"', '27"', '32"', '34"', '49"'], required: true },
      { name: 'resolution', label: 'Resolution', type: 'select', options: ['1080p', '1440p', '4K UHD', '5K'], required: true },
      { name: 'refresh_rate', label: 'Refresh Rate', type: 'select', options: ['60Hz', '75Hz', '144Hz', '165Hz', '240Hz'], required: true },
      { name: 'panel_type', label: 'Panel Type', type: 'select', options: ['IPS', 'VA', 'TN', 'OLED'], required: true },
      { name: 'response_time', label: 'Response Time', type: 'text', placeholder: 'e.g., 1ms', required: false },
      { name: 'color_accuracy', label: 'Color Accuracy', type: 'text', placeholder: 'e.g., 99% sRGB', required: false }
    ]
  },

  // Networking
  router: {
    label: 'Router',
    icon: '📡',
    brands: ['TP-Link', 'Netgear', 'Linksys', 'D-Link', 'ASUS', 'Ubiquiti'],
    fields: [
      { name: 'wifi_standard', label: 'Wi-Fi Standard', type: 'select', options: ['Wi-Fi 5 (802.11ac)', 'Wi-Fi 6 (802.11ax)', 'Wi-Fi 6E'], required: true },
      { name: 'max_speed', label: 'Max Speed', type: 'text', placeholder: 'e.g., 5400 Mbps', required: true },
      { name: 'frequency_bands', label: 'Frequency Bands', type: 'select', options: ['Single Band (2.4GHz)', 'Dual Band', 'Tri Band'], required: true },
      { name: 'ethernet_ports', label: 'Ethernet Ports', type: 'text', placeholder: 'e.g., 4 x Gigabit', required: true },
      { name: 'coverage_range', label: 'Coverage Range', type: 'text', placeholder: 'e.g., 200m', required: false },
      { name: 'antenna_count', label: 'Antennas', type: 'text', placeholder: 'e.g., 6 external', required: false }
    ]
  },

  // Storage
  external_drive: {
    label: 'External Drive',
    icon: '💾',
    brands: ['SanDisk', 'Western Digital', 'Seagate', 'Kingston', 'Samsung', 'Transcend'],
    fields: [
      { name: 'capacity', label: 'Capacity', type: 'select', options: ['500GB', '1TB', '2TB', '4TB', '8TB'], required: true },
      { name: 'interface', label: 'Interface', type: 'select', options: ['USB 3.0', 'USB 3.1', 'USB-C', 'Thunderbolt'], required: true },
      { name: 'drive_type', label: 'Drive Type', type: 'select', options: ['SSD', 'HDD'], required: true },
      { name: 'transfer_speed', label: 'Transfer Speed', type: 'text', placeholder: 'e.g., 540 MB/s', required: false },
      { name: 'encryption', label: 'Hardware Encryption', type: 'select', options: ['Yes', 'No'], required: false }
    ]
  },

  // Audio
  headphones: {
    label: 'Headphones',
    icon: '🎧',
    brands: ['Bose', 'Sony', 'JBL', 'Beats', 'Sennheiser', 'Audio-Technica', 'Skullcandy'],
    fields: [
      { name: 'type', label: 'Type', type: 'select', options: ['Over-ear', 'On-ear', 'In-ear'], required: true },
      { name: 'connectivity', label: 'Connectivity', type: 'select', options: ['Wired', 'Wireless', 'Both'], required: true },
      { name: 'noise_cancellation', label: 'Noise Cancellation', type: 'select', options: ['Active', 'Passive', 'None'], required: false },
      { name: 'battery_life', label: 'Battery Life', type: 'text', placeholder: 'e.g., 30 hours', required: false },
      { name: 'driver_size', label: 'Driver Size', type: 'text', placeholder: 'e.g., 40mm', required: false }
    ]
  },

  // Accessories & Others
  charger: {
    label: 'Charger',
    icon: '🔌',
    brands: ['Anker', 'Belkin', 'Samsung', 'Apple', 'Xiaomi', 'Baseus', 'Ugreen'],
    fields: [
      { name: 'power_output', label: 'Power Output', type: 'text', placeholder: 'e.g., 65W', required: true },
      { name: 'connector_type', label: 'Connector Type', type: 'select', options: ['USB-C', 'Lightning', 'Micro USB', 'Wireless'], required: true },
      { name: 'fast_charging', label: 'Fast Charging', type: 'select', options: ['Yes', 'No'], required: false },
      { name: 'cable_included', label: 'Cable Included', type: 'select', options: ['Yes', 'No'], required: false }
    ]
  },

  other: {
    label: 'Other',
    icon: '📦',
    brands: ['Generic', 'Various'],
    fields: [
      { name: 'product_type', label: 'Product Type', type: 'text', placeholder: 'e.g., Phone Stand', required: true },
      { name: 'material', label: 'Material', type: 'text', placeholder: 'e.g., Aluminum', required: false },
      { name: 'color', label: 'Color', type: 'text', placeholder: 'e.g., Black', required: false },
      { name: 'dimensions', label: 'Dimensions', type: 'text', placeholder: 'e.g., 10 x 5 x 2 cm', required: false }
    ]
  }
};

/**
 * Get category configuration
 */
export const getCategoryConfig = (category) => {
  return PRODUCT_CATEGORIES[category] || PRODUCT_CATEGORIES.other;
};

/**
 * Get all category options for select dropdown
 */
export const getCategoryOptions = () => {
  return Object.entries(PRODUCT_CATEGORIES).map(([value, config]) => ({
    value,
    label: `${config.icon} ${config.label}`
  }));
};

/**
 * Get brands for a specific category
 */
export const getBrandsForCategory = (category) => {
  const config = getCategoryConfig(category);
  return config.brands.map(brand => ({ value: brand, label: brand }));
};

/**
 * Get form fields for a specific category
 */
export const getFieldsForCategory = (category) => {
  const config = getCategoryConfig(category);
  return config.fields || [];
};
