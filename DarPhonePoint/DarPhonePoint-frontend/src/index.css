@import url("https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap");

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Accessibility utilities */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Focus styles for better accessibility */
.focus-visible:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .bg-gray-50 {
    background-color: white;
  }

  .text-gray-600 {
    color: black;
  }

  .border-gray-300 {
    border-color: black;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

body {
  font-family: "Poppins", sans-serif;
  color: #333;
  line-height: 1.6;
}

.gradient-bg {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
}

.bg-gradient-primary {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
}

.gradient-text {
  background: linear-gradient(90deg, #6366f1, #8b5cf6);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

.feature-card {
  transition: transform 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-5px);
}

.pricing-card {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.pricing-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.check-icon {
  color: #10b981;
}

.testimonial-card {
  position: relative;
}

.testimonial-card::before {
  content: '"';
  font-size: 5rem;
  color: rgba(139, 92, 246, 0.1);
  position: absolute;
  top: -0.5rem;
  left: 1rem;
  font-family: serif;
  font-weight: bold;
}

.value-item {
  transition: transform 0.3s ease;
}

.value-item:hover {
  transform: scale(1.02);
}

.logo-mark {
  font-weight: bold;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  color: white;
  border-radius: 12px;
  padding: 2px 8px;
  display: inline-block;
  position: relative;
  top: -2px;
  margin-right: 3px;
}
