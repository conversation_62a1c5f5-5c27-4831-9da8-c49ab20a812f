import React, { createContext, useContext, useState, useCallback } from 'react';

const LoadingContext = createContext();

export const useLoading = () => {
  const context = useContext(LoadingContext);
  if (!context) {
    throw new Error('useLoading must be used within a LoadingProvider');
  }
  return context;
};

export const LoadingProvider = ({ children }) => {
  const [loadingStates, setLoadingStates] = useState({});
  const [globalLoading, setGlobalLoading] = useState(false);
  const [loadingQueue, setLoadingQueue] = useState(new Set());

  const setLoading = useCallback((key, isLoading, options = {}) => {
    setLoadingStates(prev => ({
      ...prev,
      [key]: isLoading
    }));

    // Manage global loading queue
    setLoadingQueue(prev => {
      const newQueue = new Set(prev);
      if (isLoading) {
        newQueue.add(key);
      } else {
        newQueue.delete(key);
      }
      return newQueue;
    });

    // Auto-manage global loading state
    if (options.global) {
      setGlobalLoading(isLoading);
    }
  }, []);

  const isLoading = useCallback((key) => {
    return loadingStates[key] || false;
  }, [loadingStates]);

  const isAnyLoading = useCallback(() => {
    return loadingQueue.size > 0;
  }, [loadingQueue]);

  const setGlobalLoadingState = useCallback((isLoading) => {
    setGlobalLoading(isLoading);
  }, []);

  const clearAllLoading = useCallback(() => {
    setLoadingStates({});
    setLoadingQueue(new Set());
    setGlobalLoading(false);
  }, []);

  const value = {
    setLoading,
    isLoading,
    isAnyLoading,
    globalLoading,
    setGlobalLoadingState,
    clearAllLoading,
    loadingStates,
    loadingQueue: Array.from(loadingQueue)
  };

  return (
    <LoadingContext.Provider value={value}>
      {children}
    </LoadingContext.Provider>
  );
};

export default LoadingContext;
