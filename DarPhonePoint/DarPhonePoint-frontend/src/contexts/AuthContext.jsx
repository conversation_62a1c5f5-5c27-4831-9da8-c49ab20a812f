import React, { createContext, useContext, useState, useEffect } from 'react';
import { get, post } from '../api/unifiedApiClient';

const AuthContext = createContext();

export const useAuth = () => {
  return useContext(AuthContext);
};

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(false); // Start with false for testing
  const [error, setError] = useState(null);

  useEffect(() => {
    // Simple initialization - just set user to null for guest access
    console.log('🔍 AuthContext: Initializing auth context...');
    setUser(null);
    setError(null);
    console.log('🔍 AuthContext: Auth context initialized');
  }, []); // Empty dependency array - only run on mount

  const login = async (credentials) => {
    setLoading(true);
    setError(null);
    try {
      const response = await post('/auth/login', credentials);

      // Backend returns { success: true, token: "...", user: {...} }
      const responseData = response.data || response;
      if (responseData && responseData.success && responseData.token) {
        localStorage.setItem('token', responseData.token);
        setUser(responseData.user);
        setLoading(false);
        return true;
      } else {
        // Handle case where token is missing (e.g., login failed)
        setError(responseData?.message || 'Login failed');
        setLoading(false);
        return false;
      }
    } catch (error) {
      const errorMessage = error.message || 'Login failed';

      // Handle email verification required error
      if (errorMessage.includes('verification')) {
        setError({
          message: errorMessage,
          requiresVerification: true,
          email: credentials.email
        });
      } else {
        setError(errorMessage);
      }

      setLoading(false);
      return false;
    }
  };

  const register = async (userData) => {
    try {
      setError(null);
      setLoading(true);

      const response = await post('/auth/register', userData);

      // Backend returns { success: true, token: "..." } OR { success: true, requiresVerification: true, message: "..." }
      const responseData = response.data || response;

      if (responseData && responseData.success) {
        setLoading(false);

        if (responseData.requiresVerification) {
          // New email verification flow
          return {
            success: true,
            requiresVerification: true,
            message: responseData.message
          };
        } else if (responseData.token) {
          // Old flow - direct login (for backward compatibility)
          localStorage.setItem('token', responseData.token);

          // After registration, fetch user data
          try {
            const userResponse = await get('/auth/me');
            const userData = userResponse.data?.data || userResponse.data;
            setUser(userData);
          } catch (userErr) {
            // Create minimal user object if fetch fails
            setUser({
              name: userData.name,
              email: userData.email,
              user_type: 'free'
            });
          }

          return { success: true };
        }
      } else {
        setError(responseData?.message || 'Registration failed');
        setLoading(false);
        return { success: false };
      }
    } catch (err) {
      setError(err.response?.data?.message || 'Registration failed. Please try again.');
      setLoading(false);
      return { success: false };
    }
  };

  const logout = async () => {
    try {
      // Call logout API
      await post('/auth/logout', {});
    } catch (err) {
      // Logout error - continue with cleanup
    } finally {
      // Remove token from localStorage
      localStorage.removeItem('token');

      // Clear current user
      setUser(null);
    }
  };

  const updateProfile = async (userData) => {
    try {
      setError(null);

      const response = await post('/auth/profile', userData);

      // Backend returns { success: true, user: {...} }
      const responseData = response.data || response;
      if (responseData && responseData.success && responseData.user) {
        setUser(responseData.user);
        return true;
      } else {
        setError(responseData?.message || 'Profile update failed');
        return false;
      }
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to update profile. Please try again.');
      return false;
    }
  };

  const isAdmin = () => {
    return user && user.role === 'admin';
  };

  const value = {
    user,
    loading,
    error,
    setError,
    login,
    register,
    logout,
    updateProfile,
    isAdmin
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export default AuthContext;
