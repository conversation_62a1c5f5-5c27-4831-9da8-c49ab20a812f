import React, { createContext, useContext, useState, useEffect } from 'react';

const LanguageContext = createContext();

export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (!context) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};

// Translation dictionaries
const translations = {
  en: {
    // Navigation
    'nav.home': 'Home',
    'nav.products': 'Products',
    'nav.cart': 'Cart',
    'nav.orders': 'My Orders',
    'nav.login': 'Login',
    'nav.register': 'Register',
    'nav.logout': 'Logout',
    'nav.admin': 'Admin',

    // Product Page
    'products.title': 'Mobile Phones & Accessories',
    'products.search': 'Search phones, accessories...',
    'products.filter.category': 'Category',
    'products.filter.brand': 'Brand',
    'products.filter.price': 'Price Range (TZS)',
    'products.filter.clear': 'Clear Filters',
    'products.sort.name': 'Name',
    'products.sort.price_low': 'Price: Low to High',
    'products.sort.price_high': 'Price: High to Low',
    'products.sort.newest': 'Newest First',
    'products.in_stock': 'In Stock',
    'products.out_of_stock': 'Out of Stock',
    'products.add_to_cart': 'Add to Cart',
    'products.compare': 'Compare',
    'products.wishlist': 'Add to Wishlist',

    // Cart
    'cart.title': 'Shopping Cart',
    'cart.empty': 'Your cart is empty',
    'cart.continue_shopping': 'Continue Shopping',
    'cart.proceed_checkout': 'Proceed to Checkout',
    'cart.subtotal': 'Subtotal',
    'cart.shipping': 'Shipping',
    'cart.tax': 'Tax',
    'cart.total': 'Total',
    'cart.remove': 'Remove',
    'cart.update': 'Update',

    // Checkout
    'checkout.title': 'Checkout',
    'checkout.customer_info': 'Customer Information',
    'checkout.shipping_address': 'Shipping Address',
    'checkout.payment_method': 'Payment Method',
    'checkout.review_order': 'Review Order',
    'checkout.place_order': 'Place Order',
    'checkout.order_notes': 'Order Notes (Optional)',
    'checkout.terms': 'I agree to the Terms and Conditions',

    // Payment Methods
    'payment.mpesa': 'M-Pesa',
    'payment.tigo_pesa': 'Tigo Pesa',
    'payment.airtel_money': 'Airtel Money',
    'payment.bank_transfer': 'Bank Transfer',
    'payment.cash_on_delivery': 'Cash on Delivery',

    // Orders
    'orders.title': 'My Orders',
    'orders.empty': 'No orders found',
    'orders.status.pending': 'Pending',
    'orders.status.processing': 'Processing',
    'orders.status.shipped': 'Shipped',
    'orders.status.delivered': 'Delivered',
    'orders.status.cancelled': 'Cancelled',
    'orders.reorder': 'Reorder Items',
    'orders.cancel': 'Cancel Order',
    'orders.track': 'Track Order',

    // Common
    'common.loading': 'Loading...',
    'common.error': 'Error',
    'common.success': 'Success',
    'common.save': 'Save',
    'common.cancel': 'Cancel',
    'common.close': 'Close',
    'common.edit': 'Edit',
    'common.delete': 'Delete',
    'common.view': 'View',
    'common.back': 'Back',
    'common.next': 'Next',
    'common.previous': 'Previous',
    'common.search': 'Search',
    'common.filter': 'Filter',
    'common.sort': 'Sort',
    'common.currency': 'TZS',
    'common.free': 'Free'
  },

  sw: {
    // Navigation
    'nav.home': 'Nyumbani',
    'nav.products': 'Bidhaa',
    'nav.cart': 'Kikapu',
    'nav.orders': 'Maagizo Yangu',
    'nav.login': 'Ingia',
    'nav.register': 'Jisajili',
    'nav.logout': 'Toka',
    'nav.admin': 'Msimamizi',

    // Product Page
    'products.title': 'Simu za Mkononi na Vifaa',
    'products.search': 'Tafuta simu, vifaa...',
    'products.filter.category': 'Aina',
    'products.filter.brand': 'Chapa',
    'products.filter.price': 'Bei (TZS)',
    'products.filter.clear': 'Futa Vichujio',
    'products.sort.name': 'Jina',
    'products.sort.price_low': 'Bei: Chini hadi Juu',
    'products.sort.price_high': 'Bei: Juu hadi Chini',
    'products.sort.newest': 'Mpya Zaidi',
    'products.in_stock': 'Inapatikana',
    'products.out_of_stock': 'Haipatikani',
    'products.add_to_cart': 'Weka Kikupuni',
    'products.compare': 'Linganisha',
    'products.wishlist': 'Weka Orodhani',

    // Cart
    'cart.title': 'Kikapu cha Ununuzi',
    'cart.empty': 'Kikapu chako ni tupu',
    'cart.continue_shopping': 'Endelea Kununua',
    'cart.proceed_checkout': 'Endelea na Malipo',
    'cart.subtotal': 'Jumla Ndogo',
    'cart.shipping': 'Usafirishaji',
    'cart.tax': 'Kodi',
    'cart.total': 'Jumla',
    'cart.remove': 'Ondoa',
    'cart.update': 'Sasisha',

    // Checkout
    'checkout.title': 'Malipo',
    'checkout.customer_info': 'Taarifa za Mteja',
    'checkout.shipping_address': 'Anwani ya Usafirishaji',
    'checkout.payment_method': 'Njia ya Malipo',
    'checkout.review_order': 'Kagua Agizo',
    'checkout.place_order': 'Weka Agizo',
    'checkout.order_notes': 'Maelezo ya Agizo (Si Lazima)',
    'checkout.terms': 'Nakubali Masharti na Hali',

    // Payment Methods
    'payment.mpesa': 'M-Pesa',
    'payment.tigo_pesa': 'Tigo Pesa',
    'payment.airtel_money': 'Airtel Money',
    'payment.bank_transfer': 'Uhamishaji wa Benki',
    'payment.cash_on_delivery': 'Malipo Wakati wa Uwasilishaji',

    // Orders
    'orders.title': 'Maagizo Yangu',
    'orders.empty': 'Hakuna maagizo',
    'orders.status.pending': 'Inasubiri',
    'orders.status.processing': 'Inachakatwa',
    'orders.status.shipped': 'Imetumwa',
    'orders.status.delivered': 'Imewasilishwa',
    'orders.status.cancelled': 'Imeghairiwa',
    'orders.reorder': 'Agiza Tena',
    'orders.cancel': 'Ghairi Agizo',
    'orders.track': 'Fuatilia Agizo',

    // Common
    'common.loading': 'Inapakia...',
    'common.error': 'Hitilafu',
    'common.success': 'Mafanikio',
    'common.save': 'Hifadhi',
    'common.cancel': 'Ghairi',
    'common.close': 'Funga',
    'common.edit': 'Hariri',
    'common.delete': 'Futa',
    'common.view': 'Ona',
    'common.back': 'Rudi',
    'common.next': 'Ifuatayo',
    'common.previous': 'Iliyotangulia',
    'common.search': 'Tafuta',
    'common.filter': 'Chuja',
    'common.sort': 'Panga',
    'common.currency': 'TZS',
    'common.free': 'Bure'
  }
};

export const LanguageProvider = ({ children }) => {
  const [language, setLanguage] = useState(() => {
    // Get saved language from localStorage or default to English
    return localStorage.getItem('phonePointDarLanguage') || 'en';
  });

  useEffect(() => {
    // Save language preference to localStorage
    localStorage.setItem('phonePointDarLanguage', language);
    
    // Update document language attribute
    document.documentElement.lang = language;
  }, [language]);

  const t = (key, fallback = key) => {
    return translations[language]?.[key] || translations.en[key] || fallback;
  };

  const changeLanguage = (newLanguage) => {
    if (translations[newLanguage]) {
      setLanguage(newLanguage);
    }
  };

  const value = {
    language,
    changeLanguage,
    t,
    availableLanguages: [
      { code: 'en', name: 'English', flag: '🇺🇸' },
      { code: 'sw', name: 'Kiswahili', flag: '🇹🇿' }
    ]
  };

  return (
    <LanguageContext.Provider value={value}>
      {children}
    </LanguageContext.Provider>
  );
};
