import { apiRequest } from '../api/apiClient';

/**
 * Service for managing leads
 * Uses centralized API client with proper authentication and error handling
 */

/**
 * Get all leads
 * @returns {Promise<Array>} Array of leads
 */
export const getAllLeads = async () => {
  const response = await apiRequest('get', '/leads');
  return response.data || response;
};

/**
 * Get lead by ID
 * @param {String} id - Lead ID
 * @returns {Promise<Object>} Lead object
 */
export const getLeadById = async (id) => {
  const response = await apiRequest('get', `/leads/${id}`);
  return response.data || response;
};

/**
 * Create a new lead
 * @param {Object} leadData - Lead data
 * @returns {Promise<Object>} Created lead object
 */
export const createLead = async (leadData) => {
  const response = await apiRequest('post', '/leads', leadData);
  return response.data || response;
};

/**
 * Update a lead
 * @param {String} id - Lead ID
 * @param {Object} leadData - Updated lead data
 * @returns {Promise<Object>} Updated lead object
 */
export const updateLead = async (id, leadData) => {
  const response = await apiRequest('put', `/leads/${id}`, leadData);
  return response.data || response;
};

/**
 * Update lead status
 * @param {String} id - Lead ID
 * @param {String} status - New status
 * @returns {Promise<Object>} Updated lead object
 */
export const updateLeadStatus = async (id, status) => {
  const response = await apiRequest('put', `/leads/${id}/status`, { status });
  return response.data || response;
};

/**
 * Delete a lead
 * @param {String} id - Lead ID
 * @returns {Promise<Boolean>} Success status
 */
export const deleteLead = async (id) => {
  await apiRequest('delete', `/leads/${id}`);
  return true;
};

/**
 * Export leads as CSV
 * @param {Array} leads - Leads to export
 * @returns {String} CSV content
 */
export const exportLeadsToCSV = (leads) => {
  // Create CSV content
  const headers = ['Name', 'Email', 'Source', 'Status', 'Created At'];
  const rows = leads.map(lead => [
    lead.name || '',
    lead.email || '',
    lead.source || '',
    lead.status || '',
    new Date(lead.created_at).toLocaleString()
  ]);

  const csvContent = [
    headers.join(','),
    ...rows.map(row => row.join(','))
  ].join('\n');

  return csvContent;
};

export default {
  getAllLeads,
  getLeadById,
  createLead,
  updateLead,
  updateLeadStatus,
  deleteLead,
  exportLeadsToCSV
};
