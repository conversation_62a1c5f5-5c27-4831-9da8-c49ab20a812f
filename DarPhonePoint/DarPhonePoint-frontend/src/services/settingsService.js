import { safeApiRequest } from '../api/apiClient';

/**
 * Service for managing system settings
 * Uses centralized API client with proper authentication and error handling
 */

/**
 * Get all settings
 * @returns {Promise<Object>} Settings object
 */
export const getSettings = async () => {
  const response = await safeApiRequest({
    method: 'GET',
    url: '/settings'
  });
  return response.data?.data || response.data || response;
};

/**
 * Update settings
 * @param {String} category - Settings category (general, email, payment, storage, system)
 * @param {Object} data - Settings data to update
 * @returns {Promise<Object>} Updated settings object
 */
export const updateSettings = async (category, data) => {
  const response = await safeApiRequest({
    method: 'PATCH',
    url: `/settings?category=${category}`,
    data
  });
  return response.data?.data || response.data || response;
};

/**
 * Reset settings to default
 * @returns {Promise<Object>} Default settings object
 */
export const resetSettings = async () => {
  const response = await safeApiRequest({
    method: 'POST',
    url: '/settings/reset'
  });
  return response.data?.data || response.data || response;
};

export default {
  getSettings,
  updateSettings,
  resetSettings
};
