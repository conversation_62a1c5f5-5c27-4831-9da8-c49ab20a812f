/**
 * Wishlist Service
 * Handles wishlist functionality for both authenticated and guest users
 */

const GUEST_WISHLIST_KEY = 'phonePointDarWishlist';

/**
 * Get guest wishlist from localStorage
 */
export const getGuestWishlist = () => {
  try {
    const wishlistData = localStorage.getItem(GUEST_WISHLIST_KEY);
    if (!wishlistData) {
      return { items: [], created_at: new Date().toISOString() };
    }
    return JSON.parse(wishlistData);
  } catch (error) {
    console.error('Error loading guest wishlist:', error);
    return { items: [], created_at: new Date().toISOString() };
  }
};

/**
 * Save guest wishlist to localStorage
 */
export const saveGuestWishlist = (wishlist) => {
  try {
    localStorage.setItem(GUEST_WISHLIST_KEY, JSON.stringify({
      ...wishlist,
      updated_at: new Date().toISOString()
    }));
  } catch (error) {
    console.error('Error saving guest wishlist:', error);
    throw new Error('Failed to save wishlist');
  }
};

/**
 * Add product to guest wishlist
 */
export const addToGuestWishlist = (product) => {
  const wishlist = getGuestWishlist();
  
  // Check if product already exists
  const existingIndex = wishlist.items.findIndex(item => item._id === product._id);
  if (existingIndex !== -1) {
    throw new Error('Product is already in your wishlist');
  }
  
  // Add product with minimal data needed
  wishlist.items.push({
    _id: product._id,
    name: product.name,
    brand: product.brand,
    price: product.price,
    image: product.primary_image?.url || product.images?.[0]?.url,
    in_stock: product.in_stock,
    added_at: new Date().toISOString()
  });
  
  saveGuestWishlist(wishlist);
  return wishlist;
};

/**
 * Remove product from guest wishlist
 */
export const removeFromGuestWishlist = (productId) => {
  const wishlist = getGuestWishlist();
  wishlist.items = wishlist.items.filter(item => item._id !== productId);
  saveGuestWishlist(wishlist);
  return wishlist;
};

/**
 * Check if product is in guest wishlist
 */
export const isInGuestWishlist = (productId) => {
  const wishlist = getGuestWishlist();
  return wishlist.items.some(item => item._id === productId);
};

/**
 * Clear guest wishlist
 */
export const clearGuestWishlist = () => {
  localStorage.removeItem(GUEST_WISHLIST_KEY);
};

/**
 * Get wishlist count
 */
export const getGuestWishlistCount = () => {
  const wishlist = getGuestWishlist();
  return wishlist.items.length;
};

/**
 * Transfer guest wishlist to user account
 */
export const transferGuestWishlistToUser = async (apiCall) => {
  const guestWishlist = getGuestWishlist();
  
  if (guestWishlist.items.length === 0) {
    return { items: [] };
  }
  
  try {
    // Transfer each item to user wishlist
    const transferPromises = guestWishlist.items.map(item => 
      apiCall({
        method: 'POST',
        url: '/wishlist',
        data: { productId: item._id }
      })
    );
    
    await Promise.all(transferPromises);
    
    // Clear guest wishlist after successful transfer
    clearGuestWishlist();
    
    // Return updated user wishlist
    const response = await apiCall({
      method: 'GET',
      url: '/wishlist'
    });
    
    return response.data;
  } catch (error) {
    console.error('Error transferring guest wishlist:', error);
    throw new Error('Failed to transfer wishlist items');
  }
};

/**
 * Format wishlist for display
 */
export const formatWishlistForDisplay = (wishlist) => {
  return {
    ...wishlist,
    items: wishlist.items.map(item => ({
      ...item,
      // Ensure consistent structure for display
      product: item.product || {
        _id: item._id,
        name: item.name,
        brand: item.brand,
        price: item.price,
        primary_image: { url: item.image }
      }
    }))
  };
};
