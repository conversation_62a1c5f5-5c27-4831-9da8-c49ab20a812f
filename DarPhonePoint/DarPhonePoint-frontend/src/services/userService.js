import { apiRequest } from '../api/apiClient';

/**
 * Service for managing users
 * Uses centralized API client with proper authentication and error handling
 */

/**
 * Get all users
 * @returns {Promise<Array>} Array of users
 */
export const getAllUsers = async () => {
  const response = await apiRequest('get', '/users');
  return response.data || response;
};

/**
 * Get user by ID
 * @param {String} id - User ID
 * @returns {Promise<Object>} User object
 */
export const getUserById = async (id) => {
  const response = await apiRequest('get', `/users/${id}`);
  return response.data || response;
};

/**
 * Create a new user
 * @param {Object} userData - User data
 * @returns {Promise<Object>} Created user object
 */
export const createUser = async (userData) => {
  const response = await apiRequest('post', '/users', userData);
  return response.data || response;
};

/**
 * Update a user
 * @param {String} id - User ID
 * @param {Object} userData - Updated user data
 * @returns {Promise<Object>} Updated user object
 */
export const updateUser = async (id, userData) => {
  const response = await apiRequest('put', `/users/${id}`, userData);
  return response.data || response;
};

/**
 * Delete a user
 * @param {String} id - User ID
 * @returns {Promise<Boolean>} Success status
 */
export const deleteUser = async (id) => {
  await apiRequest('delete', `/users/${id}`);
  return true;
};

export default {
  getAllUsers,
  getUserById,
  createUser,
  updateUser,
  deleteUser
};
