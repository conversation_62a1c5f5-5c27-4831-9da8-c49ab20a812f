import { apiRequest } from '../api/apiClient';

/**
 * Service for managing audit logs
 * Uses centralized API client with proper authentication and error handling
 */

/**
 * Get all audit logs with pagination and filtering
 * @param {Object} params - Query parameters
 * @param {Number} params.page - Page number
 * @param {Number} params.limit - Number of logs per page
 * @param {String} params.user - Filter by user ID
 * @param {String} params.action - Filter by action type
 * @param {String} params.resource_type - Filter by resource type
 * @param {String} params.resource_id - Filter by resource ID
 * @param {String} params.start_date - Filter by start date
 * @param {String} params.end_date - Filter by end date
 * @returns {Promise<Object>} Audit logs with pagination info
 */
export const getAuditLogs = async (params = {}) => {
  try {
    // Build query string from params
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== '' && value !== null && value !== undefined) {
        queryParams.append(key, value);
      }
    });

    const queryString = queryParams.toString();
    const url = queryString ? `/audit-logs?${queryString}` : '/audit-logs';

    const response = await apiRequest('get', url);
    return response.data || response;
  } catch (error) {
    console.error('Error fetching audit logs:', error);

    // Return empty data structure on error
    return {
      data: [],
      total: 0,
      pages: 1,
      page: 1
    };
  }
};

/**
 * Get recent audit logs
 * @param {Number} limit - Number of logs to return
 * @returns {Promise<Object>} Recent audit logs
 */
export const getRecentLogs = async (limit = 100) => {
  try {
    const response = await apiRequest('get', `/audit-logs/recent?limit=${limit}`);
    return response.data || response;
  } catch (error) {
    console.error('Error fetching recent audit logs:', error);
    return { data: [], total: 0 };
  }
};

/**
 * Get audit logs for a specific resource
 * @param {String} type - Resource type
 * @param {String} id - Resource ID
 * @param {Number} limit - Number of logs to return
 * @returns {Promise<Object>} Resource audit logs
 */
export const getResourceLogs = async (type, id, limit = 100) => {
  try {
    const response = await apiRequest('get', `/audit-logs/resource/${type}/${id}?limit=${limit}`);
    return response.data || response;
  } catch (error) {
    console.error(`Error fetching audit logs for ${type} ${id}:`, error);
    return { data: [], total: 0 };
  }
};

/**
 * Get audit logs for a specific user
 * @param {String} id - User ID
 * @param {Number} limit - Number of logs to return
 * @returns {Promise<Object>} User audit logs
 */
export const getUserLogs = async (id, limit = 100) => {
  try {
    const response = await apiRequest('get', `/audit-logs/user/${id}?limit=${limit}`);
    return response.data || response;
  } catch (error) {
    console.error(`Error fetching audit logs for user ${id}:`, error);
    return { data: [], total: 0 };
  }
};

export default {
  getAuditLogs,
  getRecentLogs,
  getResourceLogs,
  getUserLogs
};
