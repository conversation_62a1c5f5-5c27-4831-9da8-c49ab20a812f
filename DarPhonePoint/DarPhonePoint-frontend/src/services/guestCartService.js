/**
 * Guest Cart Service
 * Handles cart functionality for non-authenticated users using localStorage
 */

const GUEST_CART_KEY = 'phonePointDarGuestCart';

/**
 * Get guest cart from localStorage
 * @returns {Object} Cart object
 */
export const getGuestCart = () => {
  try {
    const cartData = localStorage.getItem(GUEST_CART_KEY);
    if (!cartData) {
      return createEmptyCart();
    }
    
    const cart = JSON.parse(cartData);
    
    // Ensure cart has required structure
    if (!cart.items || !Array.isArray(cart.items)) {
      return createEmptyCart();
    }
    
    // Recalculate totals to ensure consistency
    return recalculateCart(cart);
  } catch (error) {
    console.error('Error loading guest cart:', error);
    return createEmptyCart();
  }
};

/**
 * Save guest cart to localStorage
 * @param {Object} cart - Cart object to save
 */
export const saveGuestCart = (cart) => {
  try {
    const cartToSave = recalculateCart(cart);
    localStorage.setItem(GUEST_CART_KEY, JSON.stringify(cartToSave));
    return cartToSave;
  } catch (error) {
    console.error('Error saving guest cart:', error);
    throw new Error('Failed to save cart');
  }
};

/**
 * Add item to guest cart
 * @param {Object} item - Item to add
 * @returns {Object} Updated cart
 */
export const addToGuestCart = (item) => {
  const cart = getGuestCart();
  
  // For items with IMEI, each is unique
  if (item.imei) {
    // Check if IMEI already exists
    const existingItem = cart.items.find(cartItem => cartItem.imei === item.imei);
    if (existingItem) {
      throw new Error('This IMEI is already in your cart');
    }
    
    cart.items.push({
      ...item,
      id: generateItemId(),
      added_at: new Date().toISOString()
    });
  } else {
    // For non-IMEI items, check if same product+variant exists
    const itemProductId = item.productId || item.product?._id;
    const existingItemIndex = cart.items.findIndex(cartItem => {
      const cartItemProductId = cartItem.productId || cartItem.product?._id;
      return cartItemProductId === itemProductId &&
             cartItem.variant_sku === item.variant_sku &&
             !cartItem.imei;
    });
    
    if (existingItemIndex > -1) {
      // Update quantity
      cart.items[existingItemIndex].quantity += item.quantity || 1;
    } else {
      // Add new item
      cart.items.push({
        ...item,
        id: generateItemId(),
        added_at: new Date().toISOString()
      });
    }
  }
  
  return saveGuestCart(cart);
};

/**
 * Update item quantity in guest cart
 * @param {String} itemId - Item ID to update
 * @param {Number} quantity - New quantity
 * @returns {Object} Updated cart
 */
export const updateGuestCartItem = (itemId, quantity) => {
  const cart = getGuestCart();
  const itemIndex = cart.items.findIndex(item => item.id === itemId);
  
  if (itemIndex === -1) {
    throw new Error('Item not found in cart');
  }
  
  if (quantity <= 0) {
    // Remove item if quantity is 0 or less
    cart.items.splice(itemIndex, 1);
  } else {
    // Update quantity
    cart.items[itemIndex].quantity = quantity;
  }
  
  return saveGuestCart(cart);
};

/**
 * Remove item from guest cart
 * @param {String} itemId - Item ID to remove
 * @returns {Object} Updated cart
 */
export const removeFromGuestCart = (itemId) => {
  const cart = getGuestCart();
  cart.items = cart.items.filter(item => item.id !== itemId);
  return saveGuestCart(cart);
};

/**
 * Clear guest cart
 * @returns {Object} Empty cart
 */
export const clearGuestCart = () => {
  const emptyCart = createEmptyCart();
  localStorage.removeItem(GUEST_CART_KEY);
  return emptyCart;
};

/**
 * Get guest cart item count
 * @returns {Number} Total number of items
 */
export const getGuestCartItemCount = () => {
  const cart = getGuestCart();
  return cart.items.reduce((total, item) => total + (item.quantity || 1), 0);
};

/**
 * Transfer guest cart to user account
 * @param {Function} apiCall - Function to make API calls
 * @returns {Promise<Object>} Updated user cart
 */
export const transferGuestCartToUser = async (apiCall) => {
  const guestCart = getGuestCart();
  
  if (guestCart.items.length === 0) {
    return { items: [], item_count: 0, subtotal: 0, total: 0 };
  }
  
  try {
    // Transfer each item to user cart
    const transferPromises = guestCart.items.map(item => 
      apiCall({
        method: 'POST',
        url: '/cart',
        data: {
          productId: item.product._id,
          variantSku: item.variant_sku,
          quantity: item.quantity,
          imei: item.imei,
          warrantyOption: item.warranty_option,
          tradeInDeviceId: item.trade_in_device,
          accessories: item.accessories,
          deviceCondition: item.device_condition
        }
      })
    );
    
    await Promise.all(transferPromises);
    
    // Clear guest cart after successful transfer
    clearGuestCart();
    
    // Return updated user cart
    const response = await apiCall({
      method: 'GET',
      url: '/cart'
    });
    
    return response.data;
  } catch (error) {
    console.error('Error transferring guest cart:', error);
    throw new Error('Failed to transfer cart items');
  }
};

/**
 * Create empty cart structure
 * @returns {Object} Empty cart
 */
const createEmptyCart = () => ({
  items: [],
  item_count: 0,
  subtotal: 0,
  tax_amount: 0,
  shipping_cost: 0,
  total: 0,
  currency: 'TZS',
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString()
});

/**
 * Recalculate cart totals
 * @param {Object} cart - Cart to recalculate
 * @returns {Object} Cart with updated totals
 */
const recalculateCart = (cart) => {
  let subtotal = 0;
  let itemCount = 0;
  
  cart.items.forEach(item => {
    const itemTotal = (item.price + (item.warranty_price || 0)) * (item.quantity || 1);
    const tradeInDiscount = item.trade_in_value || 0;
    const accessoriesTotal = (item.accessories || []).reduce((acc, accessory) => 
      acc + (accessory.price * accessory.quantity), 0
    );
    
    subtotal += itemTotal + accessoriesTotal - tradeInDiscount;
    itemCount += item.quantity || 1;
  });
  
  // Calculate tax (18% VAT in Tanzania)
  const taxAmount = subtotal * 0.18;
  
  // Calculate shipping (free for orders over 100,000 TZS)
  const shippingCost = subtotal >= 100000 ? 0 : 5000;
  
  const total = subtotal + taxAmount + shippingCost;
  
  return {
    ...cart,
    item_count: itemCount,
    subtotal: Math.round(subtotal),
    tax_amount: Math.round(taxAmount),
    shipping_cost: shippingCost,
    total: Math.round(total),
    updated_at: new Date().toISOString()
  };
};

/**
 * Generate unique item ID
 * @returns {String} Unique ID
 */
const generateItemId = () => {
  return `guest_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

/**
 * Validate cart item structure
 * @param {Object} item - Item to validate
 * @returns {Boolean} Is valid
 */
export const validateCartItem = (item) => {
  return (
    item &&
    item.product &&
    item.product._id &&
    item.product.name &&
    typeof item.price === 'number' &&
    typeof item.quantity === 'number' &&
    item.quantity > 0
  );
};

/**
 * Format cart for display
 * @param {Object} cart - Cart to format
 * @returns {Object} Formatted cart
 */
export const formatCartForDisplay = (cart) => {
  return {
    ...cart,
    items: cart.items.map(item => ({
      ...item,
      // Ensure all required fields are present
      _id: item.id,
      quantity: item.quantity || 1,
      warranty_price: item.warranty_price || 0,
      trade_in_value: item.trade_in_value || 0,
      accessories: item.accessories || []
    }))
  };
};
