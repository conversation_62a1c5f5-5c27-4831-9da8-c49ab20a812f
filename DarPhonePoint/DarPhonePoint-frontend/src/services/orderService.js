import { apiRequest } from '../api/apiClient';

/**
 * Service for managing orders
 * Uses centralized API client with proper authentication and error handling
 */

/**
 * Get all orders (admin only)
 * @returns {Promise<Array>} Array of orders
 */
export const getAllOrders = async () => {
  const response = await apiRequest('get', '/orders/admin/all');
  return response.data || response;
};

/**
 * Get order by ID
 * @param {String} id - Order ID
 * @returns {Promise<Object>} Order object
 */
export const getOrderById = async (id) => {
  const response = await apiRequest('get', `/orders/${id}`);
  return response.data || response;
};

/**
 * Update order status (admin only)
 * @param {String} id - Order ID
 * @param {String} status - New status
 * @returns {Promise<Object>} Updated order object
 */
export const updateOrderStatus = async (id, status) => {
  const response = await apiRequest('put', `/orders/admin/${id}/status`, { status });
  return response.data || response;
};

/**
 * Get orders by date range
 * @param {Date} startDate - Start date
 * @param {Date} endDate - End date
 * @returns {Promise<Array>} Array of orders
 */
export const getOrdersByDateRange = async (startDate, endDate) => {
  // Format dates for API
  const formattedStartDate = startDate.toISOString();
  const formattedEndDate = endDate.toISOString();

  const response = await apiRequest('get', `/orders/date-range?start=${formattedStartDate}&end=${formattedEndDate}`);
  return response.data || response;
};

/**
 * Export orders as CSV
 * @param {Array} orders - Orders to export
 * @returns {String} CSV content
 */
export const exportOrdersToCSV = (orders) => {
  // Create CSV content
  const headers = ['Order ID', 'Customer', 'Email', 'Product', 'Amount', 'Status', 'Date'];
  const rows = orders.map(order => [
    order._id,
    order.user?.name || 'Guest',
    order.customer_email,
    order.product?.name,
    `$${order.amount}`,
    order.payment_status,
    new Date(order.created_at).toLocaleDateString()
  ]);

  const csvContent = [
    headers.join(','),
    ...rows.map(row => row.join(','))
  ].join('\n');

  return csvContent;
};

export default {
  getAllOrders,
  getOrderById,
  updateOrderStatus,
  getOrdersByDateRange,
  exportOrdersToCSV
};
