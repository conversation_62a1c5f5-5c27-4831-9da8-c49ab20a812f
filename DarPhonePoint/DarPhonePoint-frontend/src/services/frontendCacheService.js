/**
 * Frontend Cache Service for Phone Point Dar
 * Provides client-side caching with automatic cleanup and TTL support
 */

class FrontendCacheService {
  constructor() {
    this.cache = new Map();
    this.timers = new Map();
    this.stats = {
      hits: 0,
      misses: 0,
      sets: 0,
      deletes: 0
    };

    // Default TTL values (in milliseconds)
    this.defaultTTL = {
      products: 30 * 60 * 1000,      // 30 minutes
      productList: 15 * 60 * 1000,   // 15 minutes
      user: 20 * 60 * 1000,          // 20 minutes
      cart: 5 * 60 * 1000,           // 5 minutes
      inventory: 10 * 60 * 1000,     // 10 minutes
      analytics: 15 * 60 * 1000,     // 15 minutes
      session: 60 * 60 * 1000        // 1 hour
    };

    // Clean up expired entries every 5 minutes
    this.cleanupInterval = setInterval(() => {
      this.cleanup();
    }, 5 * 60 * 1000);

    console.log('Frontend cache service initialized');
  }

  /**
   * Generate cache key with namespace
   * @param {string} namespace - Cache namespace
   * @param {string} key - Cache key
   * @returns {string} Full cache key
   */
  generateKey(namespace, key) {
    return `phonepoint:${namespace}:${key}`;
  }

  /**
   * Set value in cache
   * @param {string} namespace - Cache namespace
   * @param {string} key - Cache key
   * @param {any} value - Value to cache
   * @param {number} ttl - Time to live in milliseconds (optional)
   * @returns {boolean} Success status
   */
  set(namespace, key, value, ttl = null) {
    try {
      const fullKey = this.generateKey(namespace, key);
      const expiresAt = Date.now() + (ttl || this.defaultTTL[namespace] || this.defaultTTL.session);
      
      // Clear existing timer if any
      if (this.timers.has(fullKey)) {
        clearTimeout(this.timers.get(fullKey));
      }

      // Store value with expiration
      this.cache.set(fullKey, {
        value,
        expiresAt,
        createdAt: Date.now()
      });

      // Set expiration timer
      const timer = setTimeout(() => {
        this.delete(namespace, key);
      }, expiresAt - Date.now());

      this.timers.set(fullKey, timer);
      this.stats.sets++;

      console.debug(`Cache SET [${namespace}]: ${key}`);
      return true;
    } catch (error) {
      console.error('Cache SET error:', error);
      return false;
    }
  }

  /**
   * Get value from cache
   * @param {string} namespace - Cache namespace
   * @param {string} key - Cache key
   * @returns {any|null} Cached value or null if not found/expired
   */
  get(namespace, key) {
    try {
      const fullKey = this.generateKey(namespace, key);
      const entry = this.cache.get(fullKey);

      if (!entry) {
        this.stats.misses++;
        console.debug(`Cache MISS [${namespace}]: ${key}`);
        return null;
      }

      // Check if expired
      if (Date.now() > entry.expiresAt) {
        this.delete(namespace, key);
        this.stats.misses++;
        console.debug(`Cache EXPIRED [${namespace}]: ${key}`);
        return null;
      }

      this.stats.hits++;
      console.debug(`Cache HIT [${namespace}]: ${key}`);
      return entry.value;
    } catch (error) {
      console.error('Cache GET error:', error);
      return null;
    }
  }

  /**
   * Delete value from cache
   * @param {string} namespace - Cache namespace
   * @param {string} key - Cache key
   * @returns {boolean} Success status
   */
  delete(namespace, key) {
    try {
      const fullKey = this.generateKey(namespace, key);
      
      // Clear timer
      if (this.timers.has(fullKey)) {
        clearTimeout(this.timers.get(fullKey));
        this.timers.delete(fullKey);
      }

      // Delete from cache
      const deleted = this.cache.delete(fullKey);
      
      if (deleted) {
        this.stats.deletes++;
        console.debug(`Cache DELETE [${namespace}]: ${key}`);
      }

      return deleted;
    } catch (error) {
      console.error('Cache DELETE error:', error);
      return false;
    }
  }

  /**
   * Clear all entries in a namespace
   * @param {string} namespace - Cache namespace to clear
   * @returns {number} Number of entries cleared
   */
  clearNamespace(namespace) {
    try {
      const prefix = `phonepoint:${namespace}:`;
      let cleared = 0;

      for (const [key] of this.cache) {
        if (key.startsWith(prefix)) {
          const shortKey = key.replace(prefix, '');
          if (this.delete(namespace, shortKey)) {
            cleared++;
          }
        }
      }

      console.log(`Cache CLEARED namespace [${namespace}]: ${cleared} entries`);
      return cleared;
    } catch (error) {
      console.error('Cache CLEAR NAMESPACE error:', error);
      return 0;
    }
  }

  /**
   * Clear all cache
   * @returns {boolean} Success status
   */
  clearAll() {
    try {
      // Clear all timers
      for (const timer of this.timers.values()) {
        clearTimeout(timer);
      }

      this.cache.clear();
      this.timers.clear();
      
      console.log('All frontend cache cleared');
      return true;
    } catch (error) {
      console.error('Cache CLEAR ALL error:', error);
      return false;
    }
  }

  /**
   * Clean up expired entries
   */
  cleanup() {
    try {
      const now = Date.now();
      let cleaned = 0;

      for (const [key, entry] of this.cache) {
        if (now > entry.expiresAt) {
          const [, namespace, ...keyParts] = key.split(':');
          const shortKey = keyParts.join(':');
          if (this.delete(namespace, shortKey)) {
            cleaned++;
          }
        }
      }

      if (cleaned > 0) {
        console.log(`Cache cleanup: removed ${cleaned} expired entries`);
      }
    } catch (error) {
      console.error('Cache cleanup error:', error);
    }
  }

  /**
   * Get cache statistics
   * @returns {Object} Cache statistics
   */
  getStats() {
    return {
      ...this.stats,
      size: this.cache.size,
      hitRate: this.stats.hits / (this.stats.hits + this.stats.misses) || 0,
      namespaces: this.getNamespaceStats()
    };
  }

  /**
   * Get statistics by namespace
   * @returns {Object} Namespace statistics
   */
  getNamespaceStats() {
    const stats = {};
    
    for (const [key] of this.cache) {
      const [, namespace] = key.split(':');
      if (namespace) {
        stats[namespace] = (stats[namespace] || 0) + 1;
      }
    }

    return stats;
  }

  /**
   * Phone Point Dar specific cache methods
   */

  /**
   * Cache product data
   * @param {string} productId - Product ID
   * @param {Object} product - Product data
   * @param {number} ttl - Custom TTL (optional)
   */
  cacheProduct(productId, product, ttl = null) {
    return this.set('products', productId, product, ttl);
  }

  /**
   * Get cached product
   * @param {string} productId - Product ID
   * @returns {Object|null} Product data
   */
  getCachedProduct(productId) {
    return this.get('products', productId);
  }

  /**
   * Cache product list
   * @param {string} queryHash - Hash of query parameters
   * @param {Object} products - Product list data
   */
  cacheProductList(queryHash, products) {
    return this.set('productList', queryHash, products);
  }

  /**
   * Get cached product list
   * @param {string} queryHash - Hash of query parameters
   * @returns {Object|null} Product list data
   */
  getCachedProductList(queryHash) {
    return this.get('productList', queryHash);
  }

  /**
   * Cache user data
   * @param {string} userId - User ID
   * @param {Object} user - User data
   */
  cacheUser(userId, user) {
    return this.set('user', userId, user);
  }

  /**
   * Get cached user
   * @param {string} userId - User ID
   * @returns {Object|null} User data
   */
  getCachedUser(userId) {
    return this.get('user', userId);
  }

  /**
   * Cache cart data
   * @param {string} cartId - Cart ID (user ID or session ID)
   * @param {Object} cart - Cart data
   */
  cacheCart(cartId, cart) {
    return this.set('cart', cartId, cart);
  }

  /**
   * Get cached cart
   * @param {string} cartId - Cart ID
   * @returns {Object|null} Cart data
   */
  getCachedCart(cartId) {
    return this.get('cart', cartId);
  }

  /**
   * Invalidate product-related caches
   * @param {string} productId - Product ID
   */
  invalidateProduct(productId) {
    this.delete('products', productId);
    this.clearNamespace('productList'); // Clear all product lists
    console.log(`Invalidated frontend caches for product: ${productId}`);
  }

  /**
   * Invalidate user-related caches
   * @param {string} userId - User ID
   */
  invalidateUser(userId) {
    this.delete('user', userId);
    this.delete('cart', userId);
    console.log(`Invalidated frontend caches for user: ${userId}`);
  }

  /**
   * Generate hash from object for cache keys
   * @param {Object} obj - Object to hash
   * @returns {string} Hash string
   */
  generateHash(obj) {
    const str = JSON.stringify(obj, Object.keys(obj).sort());
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(36);
  }

  /**
   * Destroy cache service (cleanup)
   */
  destroy() {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
    this.clearAll();
    console.log('Frontend cache service destroyed');
  }
}

// Create singleton instance
const frontendCacheService = new FrontendCacheService();

// Cleanup on page unload
if (typeof window !== 'undefined') {
  window.addEventListener('beforeunload', () => {
    frontendCacheService.destroy();
  });
}

export default frontendCacheService;
