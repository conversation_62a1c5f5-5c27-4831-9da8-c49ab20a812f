import { apiRequest } from '../api/apiClient';

/**
 * Service for managing products
 * Uses centralized API client with proper authentication and error handling
 */

/**
 * Get all products
 * @returns {Promise<Array>} Array of products
 */
export const getAllProducts = async () => {
  const response = await apiRequest('get', '/products');
  return response.data || response;
};

/**
 * Get product by ID
 * @param {String} id - Product ID
 * @returns {Promise<Object>} Product object
 */
export const getProductById = async (id) => {
  const response = await apiRequest('get', `/products/${id}`);
  return response.data || response;
};

/**
 * Create a new product
 * @param {Object} productData - Product data
 * @returns {Promise<Object>} Created product object
 */
export const createProduct = async (productData) => {
  const response = await apiRequest('post', '/products', productData);
  return response.data || response;
};

/**
 * Update a product
 * @param {String} id - Product ID
 * @param {Object} productData - Updated product data
 * @returns {Promise<Object>} Updated product object
 */
export const updateProduct = async (id, productData) => {
  const response = await apiRequest('put', `/products/${id}`, productData);
  return response.data || response;
};

/**
 * Delete a product
 * @param {String} id - Product ID
 * @returns {Promise<Boolean>} Success status
 */
export const deleteProduct = async (id) => {
  await apiRequest('delete', `/products/${id}`);
  return true;
};

export default {
  getAllProducts,
  getProductById,
  createProduct,
  updateProduct,
  deleteProduct
};
