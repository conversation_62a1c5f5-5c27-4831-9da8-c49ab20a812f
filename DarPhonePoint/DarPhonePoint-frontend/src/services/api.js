/**
 * API Service for Phone Point Dar
 * Provides a simplified interface for API calls used by admin components
 */

import { apiClient } from '../api/apiClient';

/**
 * Simplified API service that wraps the existing apiClient
 * This provides compatibility for components expecting an apiService
 */
export const apiService = {
  /**
   * GET request
   * @param {string} url - API endpoint
   * @param {object} config - Request configuration
   * @returns {Promise} Response data
   */
  async get(url, config = {}) {
    try {
      const response = await apiClient({
        method: 'GET',
        url,
        ...config
      });
      return response;
    } catch (error) {
      console.error('API GET error:', error);
      throw error;
    }
  },

  /**
   * POST request
   * @param {string} url - API endpoint
   * @param {object} data - Request data
   * @param {object} config - Request configuration
   * @returns {Promise} Response data
   */
  async post(url, data = {}, config = {}) {
    try {
      const response = await apiClient({
        method: 'POST',
        url,
        data,
        ...config
      });
      return response;
    } catch (error) {
      console.error('API POST error:', error);
      throw error;
    }
  },

  /**
   * PUT request
   * @param {string} url - API endpoint
   * @param {object} data - Request data
   * @param {object} config - Request configuration
   * @returns {Promise} Response data
   */
  async put(url, data = {}, config = {}) {
    try {
      const response = await apiClient({
        method: 'PUT',
        url,
        data,
        ...config
      });
      return response;
    } catch (error) {
      console.error('API PUT error:', error);
      throw error;
    }
  },

  /**
   * PATCH request
   * @param {string} url - API endpoint
   * @param {object} data - Request data
   * @param {object} config - Request configuration
   * @returns {Promise} Response data
   */
  async patch(url, data = {}, config = {}) {
    try {
      const response = await apiClient({
        method: 'PATCH',
        url,
        data,
        ...config
      });
      return response;
    } catch (error) {
      console.error('API PATCH error:', error);
      throw error;
    }
  },

  /**
   * DELETE request
   * @param {string} url - API endpoint
   * @param {object} config - Request configuration
   * @returns {Promise} Response data
   */
  async delete(url, config = {}) {
    try {
      const response = await apiClient({
        method: 'DELETE',
        url,
        ...config
      });
      return response;
    } catch (error) {
      console.error('API DELETE error:', error);
      throw error;
    }
  },

  /**
   * Generic request method
   * @param {object} config - Full request configuration
   * @returns {Promise} Response data
   */
  async request(config) {
    try {
      const response = await apiClient(config);
      return response;
    } catch (error) {
      console.error('API request error:', error);
      throw error;
    }
  }
};

// Export as default for easier importing
export default apiService;
