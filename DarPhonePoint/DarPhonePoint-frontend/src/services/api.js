/**
 * API Service for Phone Point Dar
 * Provides a simplified interface for API calls used by admin components
 */

import { get, post, put, patch, del } from '../api/unifiedApiClient';

/**
 * Simplified API service that wraps the existing unifiedApiClient
 * This provides compatibility for components expecting an apiService
 */
export const apiService = {
  /**
   * GET request
   * @param {string} url - API endpoint
   * @param {object} config - Request configuration
   * @returns {Promise} Response data
   */
  async get(url, config = {}) {
    try {
      const response = await get(url, config);
      return response;
    } catch (error) {
      console.error('API GET error:', error);
      throw error;
    }
  },

  /**
   * POST request
   * @param {string} url - API endpoint
   * @param {object} data - Request data
   * @param {object} config - Request configuration
   * @returns {Promise} Response data
   */
  async post(url, data = {}, config = {}) {
    try {
      const response = await post(url, data, config);
      return response;
    } catch (error) {
      console.error('API POST error:', error);
      throw error;
    }
  },

  /**
   * PUT request
   * @param {string} url - API endpoint
   * @param {object} data - Request data
   * @param {object} config - Request configuration
   * @returns {Promise} Response data
   */
  async put(url, data = {}, config = {}) {
    try {
      const response = await put(url, data, config);
      return response;
    } catch (error) {
      console.error('API PUT error:', error);
      throw error;
    }
  },

  /**
   * PATCH request
   * @param {string} url - API endpoint
   * @param {object} data - Request data
   * @param {object} config - Request configuration
   * @returns {Promise} Response data
   */
  async patch(url, data = {}, config = {}) {
    try {
      const response = await patch(url, data, config);
      return response;
    } catch (error) {
      console.error('API PATCH error:', error);
      throw error;
    }
  },

  /**
   * DELETE request
   * @param {string} url - API endpoint
   * @param {object} config - Request configuration
   * @returns {Promise} Response data
   */
  async delete(url, config = {}) {
    try {
      const response = await del(url, config);
      return response;
    } catch (error) {
      console.error('API DELETE error:', error);
      throw error;
    }
  },

  /**
   * Generic request method
   * @param {object} config - Full request configuration
   * @returns {Promise} Response data
   */
  async request(config) {
    try {
      // For generic requests, use the get method as fallback
      const response = await get(config.url, config);
      return response;
    } catch (error) {
      console.error('API request error:', error);
      throw error;
    }
  }
};

// Export as default for easier importing
export default apiService;
