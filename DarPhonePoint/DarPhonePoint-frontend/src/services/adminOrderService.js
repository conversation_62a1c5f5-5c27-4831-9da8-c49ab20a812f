/**
 * Admin Order Service for Phone Point Dar
 * Handles all admin order management operations
 */

import { apiService } from './api';

/**
 * Get all orders for admin management
 * @param {Object} params - Query parameters
 * @returns {Promise<Object>} Orders with pagination
 */
export const getAdminOrders = async (params = {}) => {
  try {
    const queryString = new URLSearchParams(params).toString();
    const url = `/admin/orders${queryString ? `?${queryString}` : ''}`;
    const response = await apiService.get(url);
    return response.data;
  } catch (error) {
    console.error('Error fetching admin orders:', error);
    throw error;
  }
};

/**
 * Get single order for admin viewing
 * @param {String} id - Order ID
 * @returns {Promise<Object>} Order object
 */
export const getAdminOrder = async (id) => {
  try {
    const response = await apiService.get(`/admin/orders/${id}`);
    return response.data;
  } catch (error) {
    console.error('Error fetching admin order:', error);
    throw error;
  }
};

/**
 * Update order status
 * @param {String} id - Order ID
 * @param {Object} statusData - Status update data
 * @returns {Promise<Object>} Updated order object
 */
export const updateOrderStatus = async (id, statusData) => {
  try {
    const response = await apiService.put(`/admin/orders/${id}/status`, statusData);
    return response.data;
  } catch (error) {
    console.error('Error updating order status:', error);
    throw error;
  }
};

/**
 * Bulk update order statuses
 * @param {Array} orderIds - Array of order IDs
 * @param {Object} statusData - Status update data
 * @returns {Promise<Object>} Update result
 */
export const bulkUpdateOrderStatus = async (orderIds, statusData) => {
  try {
    const payload = {
      orderIds,
      ...statusData
    };
    
    const response = await apiService.post('/admin/orders/bulk-status', payload);
    return response.data;
  } catch (error) {
    console.error('Error performing bulk order status update:', error);
    throw error;
  }
};

/**
 * Get order statistics for admin dashboard
 * @param {String} period - Time period (7d, 30d, 90d, 1y)
 * @returns {Promise<Object>} Order statistics
 */
export const getOrderStats = async (period = '30d') => {
  try {
    const response = await apiService.get(`/admin/orders/stats?period=${period}`);
    return response.data;
  } catch (error) {
    console.error('Error fetching order stats:', error);
    throw error;
  }
};

/**
 * Get order filters (statuses, payment methods)
 * @returns {Promise<Object>} Filter options
 */
export const getOrderFilters = async () => {
  try {
    const response = await apiService.get('/admin/orders/filters');
    return response.data;
  } catch (error) {
    console.error('Error fetching order filters:', error);
    throw error;
  }
};

/**
 * Search orders for admin
 * @param {String} query - Search query
 * @param {Object} filters - Additional filters
 * @returns {Promise<Object>} Search results
 */
export const searchAdminOrders = async (query, filters = {}) => {
  try {
    const params = {
      search: query,
      ...filters
    };
    
    return await getAdminOrders(params);
  } catch (error) {
    console.error('Error searching orders:', error);
    throw error;
  }
};

/**
 * Export orders to CSV
 * @param {Object} filters - Export filters
 * @returns {Promise<Blob>} CSV file blob
 */
export const exportOrders = async (filters = {}) => {
  try {
    const queryString = new URLSearchParams(filters).toString();
    const url = `/admin/orders/export${queryString ? `?${queryString}` : ''}`;
    
    const response = await apiService.get(url, {
      responseType: 'blob'
    });
    
    return response;
  } catch (error) {
    console.error('Error exporting orders:', error);
    throw error;
  }
};

/**
 * Get order timeline/history
 * @param {String} orderId - Order ID
 * @returns {Promise<Array>} Order timeline events
 */
export const getOrderTimeline = async (orderId) => {
  try {
    const response = await apiService.get(`/admin/orders/${orderId}/timeline`);
    return response.data;
  } catch (error) {
    console.error('Error fetching order timeline:', error);
    throw error;
  }
};

/**
 * Add note to order
 * @param {String} orderId - Order ID
 * @param {String} note - Note content
 * @param {Boolean} isInternal - Whether note is internal only
 * @returns {Promise<Object>} Updated order
 */
export const addOrderNote = async (orderId, note, isInternal = false) => {
  try {
    const response = await apiService.post(`/admin/orders/${orderId}/notes`, {
      note,
      isInternal
    });
    return response.data;
  } catch (error) {
    console.error('Error adding order note:', error);
    throw error;
  }
};

/**
 * Process refund for order
 * @param {String} orderId - Order ID
 * @param {Object} refundData - Refund details
 * @returns {Promise<Object>} Refund result
 */
export const processRefund = async (orderId, refundData) => {
  try {
    const response = await apiService.post(`/admin/orders/${orderId}/refund`, refundData);
    return response.data;
  } catch (error) {
    console.error('Error processing refund:', error);
    throw error;
  }
};

/**
 * Get orders by customer
 * @param {String} customerId - Customer ID
 * @returns {Promise<Array>} Customer orders
 */
export const getCustomerOrders = async (customerId) => {
  try {
    const response = await apiService.get(`/admin/customers/${customerId}/orders`);
    return response.data;
  } catch (error) {
    console.error('Error fetching customer orders:', error);
    throw error;
  }
};

/**
 * Get pending orders requiring attention
 * @returns {Promise<Array>} Pending orders
 */
export const getPendingOrders = async () => {
  try {
    const response = await apiService.get('/admin/orders/pending');
    return response.data;
  } catch (error) {
    console.error('Error fetching pending orders:', error);
    throw error;
  }
};

/**
 * Update order shipping information
 * @param {String} orderId - Order ID
 * @param {Object} shippingData - Shipping update data
 * @returns {Promise<Object>} Updated order
 */
export const updateOrderShipping = async (orderId, shippingData) => {
  try {
    const response = await apiService.put(`/admin/orders/${orderId}/shipping`, shippingData);
    return response.data;
  } catch (error) {
    console.error('Error updating order shipping:', error);
    throw error;
  }
};

/**
 * Send order notification to customer
 * @param {String} orderId - Order ID
 * @param {String} type - Notification type
 * @returns {Promise<Object>} Send result
 */
export const sendOrderNotification = async (orderId, type) => {
  try {
    const response = await apiService.post(`/admin/orders/${orderId}/notify`, { type });
    return response.data;
  } catch (error) {
    console.error('Error sending order notification:', error);
    throw error;
  }
};

// Export all functions as default object
export default {
  getAdminOrders,
  getAdminOrder,
  updateOrderStatus,
  bulkUpdateOrderStatus,
  getOrderStats,
  getOrderFilters,
  searchAdminOrders,
  exportOrders,
  getOrderTimeline,
  addOrderNote,
  processRefund,
  getCustomerOrders,
  getPendingOrders,
  updateOrderShipping,
  sendOrderNotification
};
