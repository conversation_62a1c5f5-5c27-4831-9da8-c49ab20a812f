/**
 * Admin Product Service for Phone Point Dar
 * Handles all admin product management operations
 */

import { apiService } from './api';

/**
 * Get all products for admin management
 * @param {Object} params - Query parameters
 * @returns {Promise<Object>} Products with pagination
 */
export const getAdminProducts = async (params = {}) => {
  try {
    const queryString = new URLSearchParams(params).toString();
    const url = `/admin/products${queryString ? `?${queryString}` : ''}`;
    const response = await apiService.get(url);

    // Ensure consistent data format
    if (response && response.data) {
      return {
        products: response.data.products || response.data,
        pagination: response.data.pagination || {
          currentPage: 1,
          totalPages: 1,
          totalItems: response.data.products?.length || 0,
          itemsPerPage: 20
        }
      };
    }

    return { products: [], pagination: { currentPage: 1, totalPages: 1, totalItems: 0, itemsPerPage: 20 } };
  } catch (error) {
    console.error('Error fetching admin products:', error);
    throw error;
  }
};

/**
 * Get single product for admin editing
 * @param {String} id - Product ID
 * @returns {Promise<Object>} Product object
 */
export const getAdminProduct = async (id) => {
  try {
    const response = await apiService.get(`/admin/products/${id}`);
    return response.data;
  } catch (error) {
    console.error('Error fetching admin product:', error);
    throw error;
  }
};

/**
 * Create a new product
 * @param {Object} productData - Product data
 * @returns {Promise<Object>} Created product object
 */
export const createAdminProduct = async (productData) => {
  try {
    const response = await apiService.post('/admin/products', productData);
    return response.data;
  } catch (error) {
    console.error('Error creating product:', error);
    throw error;
  }
};

/**
 * Update a product
 * @param {String} id - Product ID
 * @param {Object} productData - Updated product data
 * @returns {Promise<Object>} Updated product object
 */
export const updateAdminProduct = async (id, productData) => {
  try {
    const response = await apiService.put(`/admin/products/${id}`, productData);
    return response.data;
  } catch (error) {
    console.error('Error updating product:', error);
    throw error;
  }
};

/**
 * Delete a product (soft delete)
 * @param {String} id - Product ID
 * @returns {Promise<Boolean>} Success status
 */
export const deleteAdminProduct = async (id) => {
  try {
    await apiService.delete(`/admin/products/${id}`);
    return true;
  } catch (error) {
    console.error('Error deleting product:', error);
    throw error;
  }
};

/**
 * Bulk operations for products
 * @param {String} action - Action to perform (delete, activate, deactivate, update)
 * @param {Array} productIds - Array of product IDs
 * @param {Object} updateData - Data for bulk update (optional)
 * @returns {Promise<Object>} Operation result
 */
export const bulkProductOperation = async (action, productIds, updateData = null) => {
  try {
    const payload = {
      action,
      productIds,
      ...(updateData && { updateData })
    };
    
    const response = await apiService.post('/admin/products/bulk', payload);
    return response.data;
  } catch (error) {
    console.error('Error performing bulk operation:', error);
    throw error;
  }
};

/**
 * Get product filters (categories, brands)
 * @returns {Promise<Object>} Filter options
 */
export const getProductFilters = async () => {
  try {
    const response = await apiService.get('/admin/products/filters');
    return response.data;
  } catch (error) {
    console.error('Error fetching product filters:', error);
    throw error;
  }
};

/**
 * Upload product image
 * @param {File} file - Image file
 * @param {String} productId - Product ID (optional)
 * @returns {Promise<Object>} Upload result
 */
export const uploadProductImage = async (file, productId = null) => {
  try {
    const formData = new FormData();
    formData.append('image', file);
    if (productId) {
      formData.append('productId', productId);
    }

    const response = await apiService.post('/admin/products/upload-image', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
    return response.data;
  } catch (error) {
    console.error('Error uploading product image:', error);
    throw error;
  }
};

/**
 * Search products for admin
 * @param {String} query - Search query
 * @param {Object} filters - Additional filters
 * @returns {Promise<Object>} Search results
 */
export const searchAdminProducts = async (query, filters = {}) => {
  try {
    const params = {
      search: query,
      ...filters
    };
    
    return await getAdminProducts(params);
  } catch (error) {
    console.error('Error searching products:', error);
    throw error;
  }
};

/**
 * Get product statistics for admin dashboard
 * @returns {Promise<Object>} Product statistics
 */
export const getProductStats = async () => {
  try {
    const response = await apiService.get('/admin/products/stats');
    return response.data;
  } catch (error) {
    console.error('Error fetching product stats:', error);
    throw error;
  }
};

/**
 * Export products to CSV
 * @param {Object} filters - Export filters
 * @returns {Promise<Blob>} CSV file blob
 */
export const exportProducts = async (filters = {}) => {
  try {
    const queryString = new URLSearchParams(filters).toString();
    const url = `/admin/products/export${queryString ? `?${queryString}` : ''}`;
    
    const response = await apiService.get(url, {
      responseType: 'blob'
    });
    
    return response;
  } catch (error) {
    console.error('Error exporting products:', error);
    throw error;
  }
};

/**
 * Import products from CSV
 * @param {File} file - CSV file
 * @returns {Promise<Object>} Import result
 */
export const importProducts = async (file) => {
  try {
    const formData = new FormData();
    formData.append('file', file);

    const response = await apiService.post('/admin/products/import', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
    
    return response.data;
  } catch (error) {
    console.error('Error importing products:', error);
    throw error;
  }
};

/**
 * Get low stock products
 * @param {Number} threshold - Stock threshold
 * @returns {Promise<Array>} Low stock products
 */
export const getLowStockProducts = async (threshold = 10) => {
  try {
    const response = await apiService.get(`/admin/products/low-stock?threshold=${threshold}`);
    return response.data;
  } catch (error) {
    console.error('Error fetching low stock products:', error);
    throw error;
  }
};

/**
 * Update product inventory
 * @param {String} productId - Product ID
 * @param {Object} inventoryData - Inventory update data
 * @returns {Promise<Object>} Updated inventory
 */
export const updateProductInventory = async (productId, inventoryData) => {
  try {
    const response = await apiService.put(`/admin/products/${productId}/inventory`, inventoryData);
    return response.data;
  } catch (error) {
    console.error('Error updating product inventory:', error);
    throw error;
  }
};

// Export all functions as default object
export default {
  getAdminProducts,
  getAdminProduct,
  createAdminProduct,
  updateAdminProduct,
  deleteAdminProduct,
  bulkProductOperation,
  getProductFilters,
  uploadProductImage,
  searchAdminProducts,
  getProductStats,
  exportProducts,
  importProducts,
  getLowStockProducts,
  updateProductInventory
};
