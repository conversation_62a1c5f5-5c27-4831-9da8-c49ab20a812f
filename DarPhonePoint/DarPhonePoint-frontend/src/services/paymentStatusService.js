import { safeApiRequest } from '../api/apiClient';

class PaymentStatusService {
  constructor() {
    this.pollingIntervals = new Map();
    this.maxAttempts = 30; // 5 minutes with 10-second intervals
  }

  /**
   * Start polling for payment status
   * @param {string} paymentId - Payment ID to poll
   * @param {Function} onSuccess - Callback for successful payment
   * @param {Function} onError - Callback for failed payment
   * @param {Function} onTimeout - Callback for timeout
   */
  startPolling(paymentId, onSuccess, onError, onTimeout) {
    if (this.pollingIntervals.has(paymentId)) {
      return;
    }

    let attempts = 0;

    const poll = async () => {
      try {
        const response = await safeApiRequest({
          method: 'GET',
          url: `/payments/${paymentId}/status`
        });
        const status = response.data?.status || response.status;

        if (status === 'completed') {
          this.stopPolling(paymentId);
          onSuccess(response.data);
        } else if (status === 'failed' || status === 'cancelled') {
          this.stopPolling(paymentId);
          onError(response.data);
        } else if (attempts >= this.maxAttempts) {
          this.stopPolling(paymentId);
          onTimeout();
        }

        attempts++;
      } catch (error) {
        this.stopPolling(paymentId);
        onError(error);
      }
    };

    // Start polling immediately
    poll();

    // Then poll every 10 seconds
    const intervalId = setInterval(poll, 10000);
    this.pollingIntervals.set(paymentId, intervalId);
  }

  /**
   * Stop polling for payment status
   * @param {string} paymentId - Payment ID to stop polling
   */
  stopPolling(paymentId) {
    const intervalId = this.pollingIntervals.get(paymentId);
    if (intervalId) {
      clearInterval(intervalId);
      this.pollingIntervals.delete(paymentId);
    }
  }

  /**
   * Stop all polling
   */
  stopAllPolling() {
    this.pollingIntervals.forEach((intervalId) => {
      clearInterval(intervalId);
    });
    this.pollingIntervals.clear();
  }
}

export default new PaymentStatusService();