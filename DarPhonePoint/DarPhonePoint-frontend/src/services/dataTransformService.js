/**
 * Data Transformation Service for Phone Point Dar Frontend
 * Standardizes data format between backend API and frontend components
 */

class DataTransformService {
  /**
   * Transform product data from API response
   * @param {Object} rawProduct - Raw product data from API
   * @returns {Object} Transformed product data
   */
  static transformProduct(rawProduct) {
    if (!rawProduct) return null;

    return {
      id: rawProduct._id || rawProduct.id,
      name: rawProduct.name || '',
      slug: rawProduct.slug || '',
      description: rawProduct.description || '',
      shortDescription: rawProduct.short_description || '',
      
      // Pricing
      price: rawProduct.price || 0,
      compareAtPrice: rawProduct.compare_at_price || null,
      costPrice: rawProduct.cost_price || null,
      formattedPrice: rawProduct.formattedPrice || this.formatTZSPrice(rawProduct.price),
      formattedComparePrice: rawProduct.formattedComparePrice || 
        (rawProduct.compare_at_price ? this.formatTZSPrice(rawProduct.compare_at_price) : null),
      
      // Product details
      brand: rawProduct.brand || '',
      model: rawProduct.model || '',
      category: rawProduct.category || '',
      subcategory: rawProduct.subcategory || '',
      
      // Stock information
      stockQuantity: rawProduct.stock_quantity || 0,
      lowStockThreshold: rawProduct.low_stock_threshold || 5,
      isInStock: (rawProduct.stock_quantity || 0) > 0,
      isLowStock: rawProduct.is_low_stock || false,
      stockStatus: this.getStockStatus(rawProduct.stock_quantity, rawProduct.low_stock_threshold),
      
      // Media
      images: this.transformImages(rawProduct.images || []),
      primaryImage: this.getPrimaryImage(rawProduct.images || []),
      
      // Specifications
      specifications: this.transformSpecifications(rawProduct.specifications || []),
      
      // Variants
      variants: this.transformVariants(rawProduct.variants || []),
      
      // Phone-specific fields
      storage: rawProduct.storage || null,
      color: rawProduct.color || null,
      memory: rawProduct.memory || null,
      
      // Metadata
      isActive: rawProduct.is_active !== false,
      isFeatured: rawProduct.is_featured || false,
      createdAt: rawProduct.created_at || rawProduct.createdAt,
      updatedAt: rawProduct.updated_at || rawProduct.updatedAt,
      
      // SEO
      metaTitle: rawProduct.meta_title || rawProduct.name,
      metaDescription: rawProduct.meta_description || rawProduct.short_description,
      
      // Additional computed fields
      hasVariants: (rawProduct.variants || []).length > 0,
      isPhone: ['smartphone', 'tablet'].includes(rawProduct.category),
      isAccessory: ['earbuds', 'headphones', 'charger', 'cable', 'case', 'screen_protector', 'power_bank'].includes(rawProduct.category)
    };
  }

  /**
   * Transform order data from API response
   * @param {Object} rawOrder - Raw order data from API
   * @returns {Object} Transformed order data
   */
  static transformOrder(rawOrder) {
    if (!rawOrder) return null;

    return {
      id: rawOrder._id || rawOrder.id,
      orderNumber: rawOrder.order_number || '',
      
      // Customer information
      customerName: rawOrder.customer_name || '',
      customerEmail: rawOrder.customer_email || '',
      customerPhone: rawOrder.customer_phone || '',
      isGuest: rawOrder.is_guest || false,
      
      // Order status
      orderStatus: rawOrder.order_status || 'pending',
      paymentStatus: rawOrder.payment_status || 'pending',
      fulfillmentStatus: rawOrder.fulfillment_status || 'unfulfilled',
      
      // Pricing
      subtotal: rawOrder.subtotal || 0,
      shippingCost: rawOrder.shipping_cost || 0,
      taxAmount: rawOrder.tax_amount || 0,
      discountAmount: rawOrder.discount_amount || 0,
      totalAmount: rawOrder.total_amount || 0,
      
      // Formatted pricing
      formattedSubtotal: rawOrder.formattedSubtotal || this.formatTZSPrice(rawOrder.subtotal),
      formattedShipping: rawOrder.formattedShipping || this.formatTZSPrice(rawOrder.shipping_cost),
      formattedTax: rawOrder.formattedTax || this.formatTZSPrice(rawOrder.tax_amount),
      formattedDiscount: rawOrder.formattedDiscount || this.formatTZSPrice(rawOrder.discount_amount),
      formattedTotal: rawOrder.formattedTotal || this.formatTZSPrice(rawOrder.total_amount),
      
      // Items
      items: this.transformOrderItems(rawOrder.items || []),
      
      // Addresses
      shippingAddress: this.transformAddress(rawOrder.shipping_address),
      billingAddress: this.transformAddress(rawOrder.billing_address),
      
      // Payment and shipping
      paymentMethod: rawOrder.payment_method || '',
      shippingMethod: rawOrder.shipping_method || '',
      
      // Metadata
      createdAt: rawOrder.created_at || rawOrder.createdAt,
      updatedAt: rawOrder.updated_at || rawOrder.updatedAt,
      
      // Computed fields
      itemCount: (rawOrder.items || []).length,
      statusColor: this.getOrderStatusColor(rawOrder.order_status),
      canCancel: this.canCancelOrder(rawOrder.order_status),
      canReturn: this.canReturnOrder(rawOrder.order_status, rawOrder.created_at)
    };
  }

  /**
   * Transform cart data from API response
   * @param {Object} rawCart - Raw cart data from API
   * @returns {Object} Transformed cart data
   */
  static transformCart(rawCart) {
    if (!rawCart) return { items: [], total: 0, itemCount: 0 };

    return {
      id: rawCart._id || rawCart.id,
      userId: rawCart.user || null,
      sessionId: rawCart.session_id || null,
      
      // Items
      items: this.transformCartItems(rawCart.items || []),
      
      // Totals
      subtotal: rawCart.subtotal || 0,
      total: rawCart.total || 0,
      formattedSubtotal: rawCart.formattedSubtotal || this.formatTZSPrice(rawCart.subtotal),
      formattedTotal: rawCart.formattedTotal || this.formatTZSPrice(rawCart.total),
      
      // Metadata
      itemCount: (rawCart.items || []).length,
      totalQuantity: (rawCart.items || []).reduce((sum, item) => sum + (item.quantity || 0), 0),
      isEmpty: (rawCart.items || []).length === 0,
      
      // Timestamps
      createdAt: rawCart.created_at || rawCart.createdAt,
      updatedAt: rawCart.updated_at || rawCart.updatedAt
    };
  }

  /**
   * Transform user data from API response
   * @param {Object} rawUser - Raw user data from API
   * @returns {Object} Transformed user data
   */
  static transformUser(rawUser) {
    if (!rawUser) return null;

    return {
      id: rawUser._id || rawUser.id,
      name: rawUser.name || '',
      email: rawUser.email || '',
      phone: rawUser.phone || '',
      role: rawUser.role || 'user',
      userType: rawUser.user_type || 'free',
      
      // Profile
      dateOfBirth: rawUser.date_of_birth || null,
      avatar: rawUser.avatar || null,
      
      // Addresses
      shippingAddresses: this.transformAddresses(rawUser.shipping_addresses || []),
      defaultShippingAddress: this.getDefaultAddress(rawUser.shipping_addresses || []),
      
      // Preferences
      preferences: rawUser.preferences || {},
      
      // Status
      isActive: rawUser.is_active !== false,
      isEmailVerified: rawUser.isEmailVerified || false,
      
      // Metadata
      createdAt: rawUser.created_at || rawUser.createdAt,
      lastLogin: rawUser.last_login || rawUser.lastLogin,
      
      // Computed fields
      isAdmin: rawUser.role === 'admin',
      isPremium: rawUser.user_type === 'premium',
      displayName: rawUser.name || rawUser.email?.split('@')[0] || 'User'
    };
  }

  /**
   * Helper methods
   */

  static formatTZSPrice(amount) {
    if (typeof amount !== 'number') return 'TZS 0';
    
    return new Intl.NumberFormat('sw-TZ', {
      style: 'currency',
      currency: 'TZS',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  }

  static getStockStatus(quantity, threshold = 5) {
    if (!quantity || quantity <= 0) return 'out_of_stock';
    if (quantity <= threshold) return 'low_stock';
    return 'in_stock';
  }

  static transformImages(images) {
    return images.map(img => ({
      url: img.url || img.src || '',
      altText: img.alt_text || img.alt || '',
      isPrimary: img.is_primary || false,
      order: img.order || 0
    })).sort((a, b) => a.order - b.order);
  }

  static getPrimaryImage(images) {
    const transformed = this.transformImages(images);
    return transformed.find(img => img.isPrimary) || transformed[0] || null;
  }

  static transformSpecifications(specs) {
    if (Array.isArray(specs)) {
      return specs.reduce((acc, spec) => {
        acc[spec.name] = spec.value;
        return acc;
      }, {});
    }
    return specs || {};
  }

  static transformVariants(variants) {
    return variants.map(variant => ({
      id: variant._id || variant.id,
      sku: variant.sku || '',
      price: variant.price || 0,
      compareAtPrice: variant.compare_at_price || null,
      formattedPrice: this.formatTZSPrice(variant.price),
      stockQuantity: variant.stock_quantity || 0,
      isActive: variant.is_active !== false,
      color: variant.color || null,
      storage: variant.storage || null,
      memory: variant.memory || null,
      images: this.transformImages(variant.images || [])
    }));
  }

  static transformOrderItems(items) {
    return items.map(item => ({
      id: item._id || item.id,
      productId: item.product?._id || item.product,
      productName: item.name || item.product?.name || '',
      quantity: item.quantity || 1,
      price: item.price || 0,
      formattedPrice: this.formatTZSPrice(item.price),
      formattedTotal: this.formatTZSPrice((item.price || 0) * (item.quantity || 1)),
      sku: item.sku || '',
      variantSku: item.variant_sku || null,
      imei: item.imei || null,
      warrantyOption: item.warranty_option || 'standard'
    }));
  }

  static transformCartItems(items) {
    return items.map(item => ({
      id: item._id || item.id,
      product: this.transformProduct(item.product),
      quantity: item.quantity || 1,
      price: item.price || 0,
      formattedPrice: this.formatTZSPrice(item.price),
      formattedTotal: this.formatTZSPrice((item.price || 0) * (item.quantity || 1)),
      variantSku: item.variant_sku || null,
      imei: item.imei || null,
      warrantyOption: item.warranty_option || 'standard'
    }));
  }

  static transformAddress(address) {
    if (!address) return null;

    return {
      firstName: address.first_name || '',
      lastName: address.last_name || '',
      company: address.company || '',
      addressLine1: address.address_line_1 || '',
      addressLine2: address.address_line_2 || '',
      city: address.city || '',
      state: address.state || '',
      postalCode: address.postal_code || '',
      country: address.country || 'Tanzania',
      phone: address.phone || '',
      isDefault: address.is_default || false
    };
  }

  static transformAddresses(addresses) {
    return addresses.map(addr => this.transformAddress(addr));
  }

  static getDefaultAddress(addresses) {
    return addresses.find(addr => addr.is_default) || addresses[0] || null;
  }

  static getOrderStatusColor(status) {
    const colors = {
      pending: 'yellow',
      confirmed: 'blue',
      processing: 'purple',
      shipped: 'indigo',
      delivered: 'green',
      cancelled: 'red',
      refunded: 'gray'
    };
    return colors[status] || 'gray';
  }

  static canCancelOrder(status) {
    return ['pending', 'confirmed'].includes(status);
  }

  static canReturnOrder(status, createdAt) {
    if (status !== 'delivered') return false;
    
    const deliveryDate = new Date(createdAt);
    const now = new Date();
    const daysSinceDelivery = (now - deliveryDate) / (1000 * 60 * 60 * 24);
    
    return daysSinceDelivery <= 30; // 30-day return policy
  }
}

export default DataTransformService;
