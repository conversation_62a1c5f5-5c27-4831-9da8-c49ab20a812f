/**
 * Global Error Handling Service for Phone Point Dar
 * Provides centralized error handling, logging, and user notifications
 */

import { toast } from 'react-hot-toast';

class ErrorHandlingService {
  constructor() {
    this.errorQueue = [];
    this.isOnline = navigator.onLine;
    this.setupGlobalHandlers();
  }

  /**
   * Set up global error handlers
   */
  setupGlobalHandlers() {
    // Handle unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      console.error('Unhandled promise rejection:', event.reason);
      this.handleError(event.reason, 'unhandled_promise');
      event.preventDefault();
    });

    // Handle global JavaScript errors
    window.addEventListener('error', (event) => {
      console.error('Global JavaScript error:', event.error);
      this.handleError(event.error, 'javascript_error');
    });

    // Monitor online/offline status
    window.addEventListener('online', () => {
      this.isOnline = true;
      this.processErrorQueue();
    });

    window.addEventListener('offline', () => {
      this.isOnline = false;
      this.showOfflineMessage();
    });
  }

  /**
   * Main error handling method
   * @param {Error|string} error - Error object or message
   * @param {string} context - Error context
   * @param {Object} metadata - Additional metadata
   */
  handleError(error, context = 'unknown', metadata = {}) {
    const errorInfo = this.normalizeError(error, context, metadata);
    
    // Log error
    this.logError(errorInfo);
    
    // Show user notification
    this.showUserNotification(errorInfo);
    
    // Report error (if online)
    if (this.isOnline) {
      this.reportError(errorInfo);
    } else {
      this.queueError(errorInfo);
    }

    return errorInfo.id;
  }

  /**
   * Normalize error into consistent format
   * @param {Error|string} error - Error object or message
   * @param {string} context - Error context
   * @param {Object} metadata - Additional metadata
   * @returns {Object} Normalized error object
   */
  normalizeError(error, context, metadata) {
    const errorId = `PPD_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    let message = 'An unexpected error occurred';
    let stack = null;
    let type = 'unknown';

    if (error instanceof Error) {
      message = error.message;
      stack = error.stack;
      type = error.name;
    } else if (typeof error === 'string') {
      message = error;
      type = 'string_error';
    } else if (error && error.message) {
      message = error.message;
      type = error.type || 'api_error';
    }

    return {
      id: errorId,
      message,
      stack,
      type,
      context,
      metadata,
      timestamp: new Date().toISOString(),
      url: window.location.href,
      userAgent: navigator.userAgent,
      userId: this.getCurrentUserId(),
      sessionId: this.getSessionId()
    };
  }

  /**
   * Handle API errors specifically
   * @param {Object} apiError - API error response
   * @param {string} endpoint - API endpoint
   * @param {Object} requestData - Request data
   */
  handleApiError(apiError, endpoint, requestData = {}) {
    const context = `api_${endpoint}`;
    const metadata = {
      endpoint,
      requestData: this.sanitizeRequestData(requestData),
      status: apiError.status,
      statusText: apiError.statusText
    };

    // Determine error type and message
    let userMessage = 'Something went wrong. Please try again.';
    let errorType = 'api_error';

    if (apiError.status === 401) {
      userMessage = 'Your session has expired. Please log in again.';
      errorType = 'auth_error';
      this.handleAuthError();
    } else if (apiError.status === 403) {
      userMessage = 'You don\'t have permission to perform this action.';
      errorType = 'permission_error';
    } else if (apiError.status === 404) {
      userMessage = 'The requested resource was not found.';
      errorType = 'not_found_error';
    } else if (apiError.status === 422) {
      userMessage = 'Please check your input and try again.';
      errorType = 'validation_error';
    } else if (apiError.status >= 500) {
      userMessage = 'Server error. Our team has been notified.';
      errorType = 'server_error';
    } else if (!navigator.onLine) {
      userMessage = 'No internet connection. Please check your network.';
      errorType = 'network_error';
    }

    const errorInfo = this.normalizeError(
      { message: userMessage, type: errorType, ...apiError },
      context,
      metadata
    );

    this.logError(errorInfo);
    this.showUserNotification(errorInfo);
    
    if (this.isOnline && errorType !== 'network_error') {
      this.reportError(errorInfo);
    }

    return errorInfo;
  }

  /**
   * Handle authentication errors
   */
  handleAuthError() {
    // Clear auth data
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    
    // Redirect to login after a short delay
    setTimeout(() => {
      window.location.href = '/login';
    }, 2000);
  }

  /**
   * Show user-friendly notification
   * @param {Object} errorInfo - Error information
   */
  showUserNotification(errorInfo) {
    const { type, message, context } = errorInfo;

    // Don't show notifications for certain contexts
    if (context === 'background_sync' || context === 'analytics') {
      return;
    }

    let toastOptions = {
      duration: 5000,
      position: 'top-right'
    };

    switch (type) {
      case 'auth_error':
        toast.error('🔐 Session expired. Redirecting to login...', toastOptions);
        break;
      case 'network_error':
        toast.error('🌐 No internet connection', {
          ...toastOptions,
          duration: 8000
        });
        break;
      case 'validation_error':
        toast.error('⚠️ ' + message, toastOptions);
        break;
      case 'server_error':
        toast.error('🔧 Server error. We\'re working on it!', toastOptions);
        break;
      case 'permission_error':
        toast.error('🚫 ' + message, toastOptions);
        break;
      default:
        // Only show generic errors for user-initiated actions
        if (context.includes('user_action') || context.includes('form_submit')) {
          toast.error('❌ ' + message, toastOptions);
        }
    }
  }

  /**
   * Show offline message
   */
  showOfflineMessage() {
    toast.error('📱 You\'re offline. Some features may not work.', {
      duration: 8000,
      position: 'top-center'
    });
  }

  /**
   * Log error to console and localStorage
   * @param {Object} errorInfo - Error information
   */
  logError(errorInfo) {
    // Console log
    console.group(`🔴 Phone Point Dar Error [${errorInfo.type}]`);
    console.error('Message:', errorInfo.message);
    console.error('Context:', errorInfo.context);
    console.error('ID:', errorInfo.id);
    if (errorInfo.stack) {
      console.error('Stack:', errorInfo.stack);
    }
    if (Object.keys(errorInfo.metadata).length > 0) {
      console.error('Metadata:', errorInfo.metadata);
    }
    console.groupEnd();

    // Store in localStorage for debugging
    try {
      const existingErrors = JSON.parse(localStorage.getItem('phonepoint_errors') || '[]');
      existingErrors.push(errorInfo);
      
      // Keep only last 20 errors
      if (existingErrors.length > 20) {
        existingErrors.splice(0, existingErrors.length - 20);
      }
      
      localStorage.setItem('phonepoint_errors', JSON.stringify(existingErrors));
    } catch (e) {
      console.error('Failed to store error in localStorage:', e);
    }
  }

  /**
   * Report error to monitoring service
   * @param {Object} errorInfo - Error information
   */
  async reportError(errorInfo) {
    try {
      // In a real app, send to error monitoring service
      // For now, we'll just log it
      console.log('📊 Reporting error to monitoring service:', errorInfo.id);
      
      // You could send to services like:
      // - Sentry
      // - LogRocket
      // - Bugsnag
      // - Custom error tracking endpoint
      
    } catch (reportingError) {
      console.error('Failed to report error:', reportingError);
    }
  }

  /**
   * Queue error for later reporting when online
   * @param {Object} errorInfo - Error information
   */
  queueError(errorInfo) {
    this.errorQueue.push(errorInfo);
    
    // Limit queue size
    if (this.errorQueue.length > 50) {
      this.errorQueue.shift();
    }
  }

  /**
   * Process queued errors when back online
   */
  async processErrorQueue() {
    if (this.errorQueue.length === 0) return;

    toast.success('📶 Back online! Syncing error reports...', {
      duration: 3000
    });

    const errors = [...this.errorQueue];
    this.errorQueue = [];

    for (const error of errors) {
      await this.reportError(error);
    }
  }

  /**
   * Utility methods
   */
  getCurrentUserId() {
    try {
      const user = JSON.parse(localStorage.getItem('user') || '{}');
      return user.id || 'anonymous';
    } catch {
      return 'anonymous';
    }
  }

  getSessionId() {
    let sessionId = sessionStorage.getItem('sessionId');
    if (!sessionId) {
      sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      sessionStorage.setItem('sessionId', sessionId);
    }
    return sessionId;
  }

  sanitizeRequestData(data) {
    // Remove sensitive data from request logs
    const sensitiveFields = ['password', 'token', 'credit_card', 'ssn', 'phone'];
    const sanitized = { ...data };
    
    sensitiveFields.forEach(field => {
      if (sanitized[field]) {
        sanitized[field] = '[REDACTED]';
      }
    });
    
    return sanitized;
  }

  /**
   * Get error statistics
   */
  getErrorStats() {
    try {
      const errors = JSON.parse(localStorage.getItem('phonepoint_errors') || '[]');
      const stats = {
        total: errors.length,
        byType: {},
        byContext: {},
        recent: errors.slice(-5)
      };

      errors.forEach(error => {
        stats.byType[error.type] = (stats.byType[error.type] || 0) + 1;
        stats.byContext[error.context] = (stats.byContext[error.context] || 0) + 1;
      });

      return stats;
    } catch {
      return { total: 0, byType: {}, byContext: {}, recent: [] };
    }
  }

  /**
   * Clear error logs
   */
  clearErrorLogs() {
    localStorage.removeItem('phonepoint_errors');
    this.errorQueue = [];
    console.log('🧹 Error logs cleared');
  }
}

// Create singleton instance
const errorHandlingService = new ErrorHandlingService();

export default errorHandlingService;
