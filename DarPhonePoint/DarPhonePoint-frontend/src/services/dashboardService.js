import { safeApiRequest } from '../api/apiClient';

/**
 * Phone Point Dar Dashboard Service
 * Fetches real-time statistics for phone retail business
 */

/**
 * Get Phone Point Dar dashboard stats
 * @returns {Promise<Object>} Dashboard stats for phone retail business
 */
export const getDashboardStats = async () => {
  try {
    // Fetch Phone Point Dar dashboard statistics from the new admin endpoint
    const response = await safeApiRequest({
      method: 'GET',
      url: '/admin/dashboard/stats'
    });

    // Extract data from response
    const data = response.data?.data || response.data || response;

    // Return the stats in the expected format
    return {
      totalCustomers: data.totalCustomers || 0,
      totalOrders: data.totalOrders || 0,
      totalProducts: data.totalProducts || 0,
      monthlyRevenue: data.monthlyRevenue || 0,
      averageOrderValue: data.averageOrderValue || 0,
      pendingOrders: data.pendingOrders || 0,
      lowStockItems: data.lowStockItems || 0,
      recentOrders: data.recentOrders || [],
      topProducts: data.topProducts || [],
      recentLeads: [] // Phone Point Dar doesn't use leads, but keeping for compatibility
    };
  } catch (error) {
    console.error('Error fetching Phone Point Dar dashboard stats:', error);

    // Return default values on error
    return {
      totalCustomers: 0,
      totalOrders: 0,
      totalProducts: 0,
      monthlyRevenue: 0,
      averageOrderValue: 0,
      pendingOrders: 0,
      lowStockItems: 0,
      recentOrders: [],
      topProducts: [],
      recentLeads: []
    };
  }
};

export default {
  getDashboardStats
};
