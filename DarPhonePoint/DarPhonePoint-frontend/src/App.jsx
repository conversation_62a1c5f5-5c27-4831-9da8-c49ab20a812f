import React, { useEffect, Suspense, lazy } from 'react';
import { Routes, Route, useLocation } from 'react-router-dom';
import { useAuth } from './contexts/AuthContext';
import { useLoading } from './contexts/LoadingContext';
import { LanguageProvider } from './contexts/LanguageContext';
import { trackPageView } from './utils/analytics';
import { recordPageLoad } from './utils/performanceMonitor';
import LoadingState from './components/ui/LoadingState';
import ErrorBoundary from './components/ErrorBoundary';

// Layout Components
import Header from './components/layout/Header';
import Footer from './components/layout/Footer';
import AdminLayout from './components/layout/AdminLayout';

// Protected Route Components
import ProtectedRoute from './components/auth/ProtectedRoute';
import AdminRoute from './components/auth/AdminRoute';

// Eagerly loaded pages (critical for initial render)
import HomePage from './pages/HomePage';
import NotFoundPage from './pages/NotFoundPage';

// Lazily loaded pages (loaded on demand)
const LoginPage = lazy(() => import('./pages/LoginPage'));
const RegisterPage = lazy(() => import('./pages/RegisterPage'));
const VerifyEmailPage = lazy(() => import('./pages/VerifyEmailPage'));
const VerificationSentPage = lazy(() => import('./pages/VerificationSentPage'));
const WarrantyPage = lazy(() => import('./pages/WarrantyPage'));
const ReturnsPage = lazy(() => import('./pages/ReturnsPage'));
const ForgotPassword = lazy(() => import('./pages/auth/ForgotPassword'));
const ResetPassword = lazy(() => import('./pages/auth/ResetPassword'));
const ProductsPage = lazy(() => import('./pages/ProductsPage'));
const ProductDetailPage = lazy(() => import('./pages/ProductDetailPage'));
// Cart and checkout pages for Phone Point Dar
const CartPage = lazy(() => import('./pages/CartPage'));
const CheckoutPage = lazy(() => import('./pages/CheckoutPage'));
const PaymentProcessPage = lazy(() => import('./pages/PaymentProcessPage'));
const PaymentSuccessPage = lazy(() => import('./pages/PaymentSuccessPage'));
const PaymentCancelPage = lazy(() => import('./pages/PaymentCancelPage'));
const DashboardPage = lazy(() => import('./pages/DashboardPage'));
const OrdersPage = lazy(() => import('./pages/OrdersPage'));
const OrderDetailPage = lazy(() => import('./pages/OrderDetailPage'));
const WishlistPage = lazy(() => import('./pages/WishlistPage'));
const TestWishlistPage = lazy(() => import('./pages/TestWishlistPage'));
const TradeInPage = lazy(() => import('./pages/TradeInPage'));
const ComparisonPage = lazy(() => import('./pages/ComparisonPage'));
const StoreLocatorPage = lazy(() => import('./pages/StoreLocatorPage'));
const LoyaltyProgramPage = lazy(() => import('./pages/LoyaltyProgramPage'));

const ProfilePage = lazy(() => import('./pages/ProfilePage'));
const PasswordStrengthDemo = lazy(() => import('./pages/PasswordStrengthDemo'));

// Admin pages for Phone Point Dar
const AdminDashboardPage = lazy(() => import('./pages/admin/AdminDashboardPage'));
const AdminProductsPage = lazy(() => import('./pages/admin/AdminProductsPage'));
const AdminInventoryPage = lazy(() => import('./pages/admin/AdminInventoryPage'));
const AdminOrdersPage = lazy(() => import('./pages/admin/AdminOrdersPage'));
const AdminAnalyticsPage = lazy(() => import('./pages/admin/AdminAnalyticsPage'));
const ProductCategoriesPage = lazy(() => import('./pages/admin/ProductCategoriesPage'));
const BrandManagementPage = lazy(() => import('./pages/admin/BrandManagementPage'));
const BulkProductImportPage = lazy(() => import('./pages/admin/BulkProductImportPage'));
const IMEIManagementPage = lazy(() => import('./pages/admin/IMEIManagementPage'));
const StockMovementPage = lazy(() => import('./pages/admin/StockMovementPage'));
const SalesAnalyticsPage = lazy(() => import('./pages/admin/SalesAnalyticsPage'));
const ProductFormPage = lazy(() => import('./pages/admin/ProductFormPage'));
const CustomerManagementPage = lazy(() => import('./pages/admin/CustomerManagementPage'));
const SettingsPage = lazy(() => import('./pages/admin/SettingsPage'));
const AuditLogPage = lazy(() => import('./pages/admin/AuditLogPage'));
const UnsubscribePage = lazy(() => import('./pages/UnsubscribePage'));

// Admin components
const PerformanceMonitor = lazy(() => import('./components/admin/PerformanceMonitor'));

function App() {
  const { loading } = useAuth();
  const { setLoading } = useLoading();
  const location = useLocation();

  // Track page views when location changes (only after auth is resolved)
  useEffect(() => {
    // Only track page views after auth loading is complete
    if (!loading) {
      const pageName = location.pathname === '/' ? 'home' : location.pathname.substring(1);

      // Add a small delay to ensure the page is fully loaded
      const timeoutId = setTimeout(() => {
        trackPageView(pageName);
        recordPageLoad(pageName);
      }, 100);

      return () => clearTimeout(timeoutId);
    }
  }, [location.pathname, loading]); // Only depend on pathname, not full location object

  // Set global loading state for auth
  useEffect(() => {
    setLoading('auth', loading, { global: true });
  }, [loading, setLoading]);

  if (loading) {
    return (
      <LoadingState
        fullPage={true}
        text="Initializing application..."
        size="lg"
        type="spinner"
      />
    );
  }

  // Loading fallback component for lazy-loaded routes
  const LoadingFallback = () => (
    <LoadingState
      text="Loading page..."
      size="md"
      type="dots"
    />
  );

  return (
    <ErrorBoundary>
      <LanguageProvider>
        <div className="flex flex-col min-h-screen">
          <Header />
          <main className="flex-grow">
          <Suspense fallback={<LoadingFallback />}>
          <Routes>
            {/* Public Routes */}
            <Route path="/" element={<HomePage />} />
            <Route path="/login" element={<LoginPage />} />
            <Route path="/register" element={<RegisterPage />} />
            <Route path="/verify-email" element={<VerifyEmailPage />} />
            <Route path="/verification-sent" element={<VerificationSentPage />} />
            <Route path="/forgot-password" element={<ForgotPassword />} />
            <Route path="/reset-password/:token" element={<ResetPassword />} />
            <Route path="/products" element={<ProductsPage />} />
            <Route path="/products/:id" element={<ProductDetailPage />} />
            <Route path="/password-demo" element={<PasswordStrengthDemo />} />
            <Route path="/unsubscribe" element={<UnsubscribePage />} />

            {/* Cart and Checkout Routes - Allow guest access */}
            <Route path="/cart" element={<CartPage />} />
            <Route path="/checkout" element={<CheckoutPage />} />
            <Route path="/payment/process/:orderId" element={<PaymentProcessPage />} />
            <Route path="/payment/success" element={<PaymentSuccessPage />} />
            <Route path="/payment/cancel" element={<PaymentCancelPage />} />
            <Route path="/dashboard" element={
              <ProtectedRoute>
                <DashboardPage />
              </ProtectedRoute>
            } />
            <Route path="/orders" element={
              <ProtectedRoute>
                <OrdersPage />
              </ProtectedRoute>
            } />
            <Route path="/orders/:id" element={
              <ProtectedRoute>
                <OrderDetailPage />
              </ProtectedRoute>
            } />

            {/* Wishlist Route - Allow guest access */}
            <Route path="/wishlist" element={<WishlistPage />} />

            {/* Test Wishlist Route - Development only */}
            <Route path="/test-wishlist" element={<TestWishlistPage />} />

            {/* Dashboard Feature Routes */}
            <Route path="/trade-in" element={<TradeInPage />} />
            <Route path="/comparison" element={<ComparisonPage />} />
            <Route path="/store-locator" element={<StoreLocatorPage />} />
            <Route path="/loyalty" element={<LoyaltyProgramPage />} />
            <Route path="/warranty" element={<WarrantyPage />} />
            <Route path="/returns" element={<ReturnsPage />} />

            <Route path="/profile" element={
              <ProtectedRoute>
                <ProfilePage />
              </ProtectedRoute>
            } />

            {/* Admin Routes for Phone Point Dar */}
            <Route path="/admin" element={
              <AdminRoute>
                <AdminDashboardPage />
              </AdminRoute>
            } />

            {/* Product Management Routes */}
            <Route path="/admin/products" element={
              <AdminRoute>
                <AdminProductsPage />
              </AdminRoute>
            } />
            <Route path="/admin/products/new" element={
              <AdminRoute>
                <ProductFormPage />
              </AdminRoute>
            } />
            <Route path="/admin/products/:id/edit" element={
              <AdminRoute>
                <ProductFormPage />
              </AdminRoute>
            } />
            <Route path="/admin/products/categories" element={
              <AdminRoute>
                <ProductCategoriesPage />
              </AdminRoute>
            } />
            <Route path="/admin/products/brands" element={
              <AdminRoute>
                <BrandManagementPage />
              </AdminRoute>
            } />
            <Route path="/admin/products/import" element={
              <AdminRoute>
                <BulkProductImportPage />
              </AdminRoute>
            } />

            {/* Inventory Management Routes */}
            <Route path="/admin/inventory" element={
              <AdminRoute>
                <AdminInventoryPage />
              </AdminRoute>
            } />
            <Route path="/admin/inventory/alerts" element={
              <AdminRoute>
                <AdminInventoryPage />
              </AdminRoute>
            } />
            <Route path="/admin/inventory/adjustments" element={
              <AdminRoute>
                <AdminInventoryPage />
              </AdminRoute>
            } />
            <Route path="/admin/inventory/history" element={
              <AdminRoute>
                <AdminInventoryPage />
              </AdminRoute>
            } />
            <Route path="/admin/inventory/imei" element={
              <AdminRoute>
                <IMEIManagementPage />
              </AdminRoute>
            } />
            <Route path="/admin/inventory/movements" element={
              <AdminRoute>
                <StockMovementPage />
              </AdminRoute>
            } />

            {/* Order Management Routes */}
            <Route path="/admin/orders" element={
              <AdminRoute>
                <AdminOrdersPage />
              </AdminRoute>
            } />
            <Route path="/admin/orders/:id" element={
              <AdminRoute>
                <AdminOrdersPage />
              </AdminRoute>
            } />

            {/* Customer Management */}
            <Route path="/admin/customers" element={
              <AdminRoute>
                <CustomerManagementPage />
              </AdminRoute>
            } />

            {/* Analytics Routes */}
            <Route path="/admin/analytics" element={
              <AdminRoute>
                <AdminAnalyticsPage />
              </AdminRoute>
            } />
            <Route path="/admin/analytics/sales" element={
              <AdminRoute>
                <SalesAnalyticsPage />
              </AdminRoute>
            } />
            <Route path="/admin/analytics/inventory" element={
              <AdminRoute>
                <AdminAnalyticsPage />
              </AdminRoute>
            } />
            <Route path="/admin/analytics/customers" element={
              <AdminRoute>
                <AdminAnalyticsPage />
              </AdminRoute>
            } />
            <Route path="/admin/analytics/financial" element={
              <AdminRoute>
                <AdminAnalyticsPage />
              </AdminRoute>
            } />

            {/* Settings and Logs */}
            <Route path="/admin/settings" element={
              <AdminRoute>
                <SettingsPage />
              </AdminRoute>
            } />
            <Route path="/admin/audit-logs" element={
              <AdminRoute>
                <AuditLogPage />
              </AdminRoute>
            } />

            {/* 404 Route */}
            <Route path="*" element={<NotFoundPage />} />
          </Routes>
        </Suspense>
      </main>
      <Footer />

      {/* Performance Monitor (only visible in admin routes) */}
      {location.pathname.startsWith('/admin') && (
        <Suspense fallback={null}>
          <PerformanceMonitor />
        </Suspense>
      )}
        </div>
      </LanguageProvider>
    </ErrorBoundary>
  );
}

export default App;
