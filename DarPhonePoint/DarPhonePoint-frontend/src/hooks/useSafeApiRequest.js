import { useEffect, useRef, useCallback, useState } from 'react';
import { safeApiRequest } from '../api/apiClient';

/**
 * Custom hook for making safe API requests that automatically handle cleanup
 * Prevents memory leaks by cancelling requests when component unmounts
 */
export const useSafeApiRequest = () => {
  const abortControllerRef = useRef(null);
  const isMountedRef = useRef(true);

  // Create a new abort controller for each request
  const createAbortController = useCallback(() => {
    // Cancel previous request if it exists
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    
    // Create new controller
    abortControllerRef.current = new AbortController();
    return abortControllerRef.current;
  }, []);

  // Safe API request wrapper
  const request = useCallback(async (config) => {
    const controller = createAbortController();
    
    try {
      const response = await safeApiRequest({
        ...config,
        signal: controller.signal
      });
      
      // Only return response if component is still mounted
      if (isMountedRef.current) {
        return response;
      }
      
      return null;
    } catch (error) {
      // Don't throw AbortError if component is unmounted
      if (error.name === 'AbortError' && !isMountedRef.current) {
        return null;
      }
      throw error;
    }
  }, [createAbortController]);

  // Cleanup on unmount
  useEffect(() => {
    isMountedRef.current = true;
    
    return () => {
      isMountedRef.current = false;
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  return {
    request,
    isMounted: () => isMountedRef.current,
    abort: () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    }
  };
};

/**
 * Custom hook for fetching data with loading and error states
 * Automatically handles cleanup and prevents memory leaks
 */
export const useFetch = (url, options = {}) => {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const { request, isMounted } = useSafeApiRequest();

  const fetchData = useCallback(async () => {
    if (!url) return;

    setLoading(true);
    setError(null);

    try {
      const response = await request({
        method: 'GET',
        url,
        ...options
      });

      if (isMounted() && response) {
        setData(response.data);
      }
    } catch (err) {
      if (isMounted()) {
        setError(err.message || 'An error occurred');
        console.error('Fetch error:', err);
      }
    } finally {
      if (isMounted()) {
        setLoading(false);
      }
    }
  }, [url, request, isMounted, options]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  return {
    data,
    loading,
    error,
    refetch: fetchData
  };
};

/**
 * Custom hook for handling async operations with loading states
 * Prevents memory leaks and provides consistent error handling
 */
export const useAsyncOperation = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const { request, isMounted } = useSafeApiRequest();

  const execute = useCallback(async (operation) => {
    setLoading(true);
    setError(null);

    try {
      const result = await operation(request);
      
      if (isMounted()) {
        setLoading(false);
        return result;
      }
      
      return null;
    } catch (err) {
      if (isMounted()) {
        setError(err.message || 'An error occurred');
        setLoading(false);
      }
      throw err;
    }
  }, [request, isMounted]);

  return {
    loading,
    error,
    execute,
    clearError: () => setError(null)
  };
};

/**
 * Custom hook for debounced API requests
 * Useful for search functionality to prevent excessive API calls
 */
export const useDebouncedApiRequest = (delay = 300) => {
  const timeoutRef = useRef(null);
  const { request, isMounted } = useSafeApiRequest();

  const debouncedRequest = useCallback((config) => {
    return new Promise((resolve, reject) => {
      // Clear previous timeout
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }

      // Set new timeout
      timeoutRef.current = setTimeout(async () => {
        try {
          const response = await request(config);
          if (isMounted()) {
            resolve(response);
          }
        } catch (error) {
          if (isMounted()) {
            reject(error);
          }
        }
      }, delay);
    });
  }, [request, isMounted, delay]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return debouncedRequest;
};

/**
 * Custom hook for polling data at regular intervals
 * Automatically handles cleanup and prevents memory leaks
 */
export const usePolling = (url, interval = 5000, options = {}) => {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const { request, isMounted } = useSafeApiRequest();
  const intervalRef = useRef(null);

  const fetchData = useCallback(async () => {
    try {
      const response = await request({
        method: 'GET',
        url,
        ...options
      });

      if (isMounted() && response) {
        setData(response.data);
        setError(null);
      }
    } catch (err) {
      if (isMounted()) {
        setError(err.message || 'An error occurred');
        console.error('Polling error:', err);
      }
    } finally {
      if (isMounted()) {
        setLoading(false);
      }
    }
  }, [url, request, isMounted, options]);

  const startPolling = useCallback(() => {
    fetchData(); // Initial fetch
    
    intervalRef.current = setInterval(() => {
      if (isMounted()) {
        fetchData();
      }
    }, interval);
  }, [fetchData, interval, isMounted]);

  const stopPolling = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
  }, []);

  useEffect(() => {
    if (url) {
      startPolling();
    }

    return () => {
      stopPolling();
    };
  }, [url, startPolling, stopPolling]);

  return {
    data,
    loading,
    error,
    refetch: fetchData,
    startPolling,
    stopPolling
  };
};

export default useSafeApiRequest;
