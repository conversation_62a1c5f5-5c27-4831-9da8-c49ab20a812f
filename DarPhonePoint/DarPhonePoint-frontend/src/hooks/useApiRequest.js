import { useState, useCallback, useRef, useEffect } from 'react';
import { apiRequest } from '../api/unifiedApiClient';
import { useLoading } from '../contexts/LoadingContext';

/**
 * Custom hook for making API requests with integrated loading states
 * Prevents duplicate requests and manages loading states automatically
 *
 * @param {Object} options - Configuration options
 * @returns {Object} Request state and functions
 */
const useApiRequest = (options = {}) => {
  const {
    loadingKey = null,
    preventDuplicates = true,
    cacheTime = 5 * 60 * 1000, // 5 minutes default
    retryAttempts = 3,
    retryDelay = 1000,
    onSuccess = null,
    onError = null
  } = options;

  const { setLoading } = useLoading();
  const [data, setData] = useState(null);
  const [error, setError] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [lastRequestTime, setLastRequestTime] = useState(null);

  // Track ongoing requests to prevent duplicates
  const ongoingRequests = useRef(new Map());
  const cache = useRef(new Map());
  const retryTimeouts = useRef(new Map());

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      // Clear any ongoing timeouts
      retryTimeouts.current.forEach(timeout => clearTimeout(timeout));
      retryTimeouts.current.clear();
    };
  }, []);

  /**
   * Generate cache key from request config
   */
  const generateCacheKey = useCallback((config) => {
    const { method = 'GET', url, data: requestData, params } = config;
    return `${method.toUpperCase()}:${url}:${JSON.stringify(params || {})}:${JSON.stringify(requestData || {})}`;
  }, []);

  /**
   * Check if cached data is still valid
   */
  const isCacheValid = useCallback((cacheEntry) => {
    if (!cacheEntry) return false;
    return Date.now() - cacheEntry.timestamp < cacheTime;
  }, [cacheTime]);

  /**
   * Execute API request with retry logic
   */
  const executeRequest = useCallback(async (config, attempt = 1) => {
    try {
      const response = await apiRequest(config);
      return response;
    } catch (error) {
      if (attempt < retryAttempts && error.code === 'NETWORK_ERROR') {
        // Retry with exponential backoff
        const delay = retryDelay * Math.pow(2, attempt - 1);

        return new Promise((resolve, reject) => {
          const timeoutId = setTimeout(async () => {
            try {
              const result = await executeRequest(config, attempt + 1);
              resolve(result);
            } catch (retryError) {
              reject(retryError);
            }
          }, delay);

          retryTimeouts.current.set(config, timeoutId);
        });
      }
      throw error;
    }
  }, [retryAttempts, retryDelay]);

  /**
   * Make API request
   */
  const request = useCallback(async (config) => {
    const cacheKey = generateCacheKey(config);
    const requestKey = loadingKey || cacheKey;

    // Check cache first for GET requests
    if (config.method?.toUpperCase() === 'GET' || !config.method) {
      const cachedData = cache.current.get(cacheKey);
      if (isCacheValid(cachedData)) {
        setData(cachedData.data);
        setError(null);
        return cachedData.data;
      }
    }

    // Prevent duplicate requests
    if (preventDuplicates && ongoingRequests.current.has(cacheKey)) {
      return ongoingRequests.current.get(cacheKey);
    }

    // Start loading
    setIsLoading(true);
    setError(null);
    if (loadingKey) {
      setLoading(loadingKey, true);
    }

    const requestPromise = (async () => {
      try {
        const response = await executeRequest(config);
        const responseData = response.data?.data || response.data || response;

        // Cache GET requests
        if (config.method?.toUpperCase() === 'GET' || !config.method) {
          cache.current.set(cacheKey, {
            data: responseData,
            timestamp: Date.now()
          });
        }

        setData(responseData);
        setError(null);
        setLastRequestTime(Date.now());

        // Call success callback
        if (onSuccess) {
          onSuccess(responseData, response);
        }

        return responseData;
      } catch (err) {
        console.error('API Request Error:', err);
        const errorMessage = err.response?.data?.message || err.message || 'Request failed';

        setError(errorMessage);
        setData(null);

        // Call error callback
        if (onError) {
          onError(err);
        }

        throw err;
      } finally {
        setIsLoading(false);
        if (loadingKey) {
          setLoading(loadingKey, false);
        }
        ongoingRequests.current.delete(cacheKey);
      }
    })();

    // Track ongoing request
    if (preventDuplicates) {
      ongoingRequests.current.set(cacheKey, requestPromise);
    }

    return requestPromise;
  }, [
    generateCacheKey,
    loadingKey,
    preventDuplicates,
    isCacheValid,
    executeRequest,
    setLoading,
    onSuccess,
    onError
  ]);

  /**
   * Clear cache for specific key or all cache
   */
  const clearCache = useCallback((key = null) => {
    if (key) {
      cache.current.delete(key);
    } else {
      cache.current.clear();
    }
  }, []);

  /**
   * Reset request state
   */
  const reset = useCallback(() => {
    setData(null);
    setError(null);
    setIsLoading(false);
    setLastRequestTime(null);
  }, []);

  return {
    data,
    error,
    isLoading,
    lastRequestTime,
    request,
    clearCache,
    reset
  };
};

export default useApiRequest;
