import { useCallback } from 'react';
import { startTiming, endTiming } from '../utils/performanceMonitor';

/**
 * Hook for tracking performance of operations
 * 
 * @returns {Object} Performance tracking functions
 */
const usePerformanceTracking = () => {
  /**
   * Track an async operation
   * 
   * @param {Function} fn - Async function to track
   * @param {String} name - Name of the operation
   * @param {String} category - Category of the operation (api, page, render, resource)
   * @returns {Function} Wrapped function that tracks performance
   */
  const trackAsync = useCallback((fn, name, category = 'api') => {
    return async (...args) => {
      const timing = startTiming(name, category);
      try {
        const result = await fn(...args);
        endTiming(timing, true, { args });
        return result;
      } catch (error) {
        endTiming(timing, false, { args, error: error.message });
        throw error;
      }
    };
  }, []);
  
  /**
   * Track a synchronous operation
   * 
   * @param {Function} fn - Synchronous function to track
   * @param {String} name - Name of the operation
   * @param {String} category - Category of the operation (api, page, render, resource)
   * @returns {Function} Wrapped function that tracks performance
   */
  const trackSync = useCallback((fn, name, category = 'render') => {
    return (...args) => {
      const timing = startTiming(name, category);
      try {
        const result = fn(...args);
        endTiming(timing, true, { args });
        return result;
      } catch (error) {
        endTiming(timing, false, { args, error: error.message });
        throw error;
      }
    };
  }, []);
  
  return {
    trackAsync,
    trackSync
  };
};

export default usePerformanceTracking;
