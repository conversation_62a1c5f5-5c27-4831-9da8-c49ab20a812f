import { useState, useEffect } from 'react';

/**
 * Custom hook for form validation
 * 
 * @param {Object} initialValues - Initial form values
 * @param {Function} validate - Validation function that returns errors object
 * @param {Function} onSubmit - Function to call when form is submitted and valid
 * @returns {Object} Form state and handlers
 */
const useFormValidation = (initialValues, validate, onSubmit) => {
  const [values, setValues] = useState(initialValues);
  const [errors, setErrors] = useState({});
  const [touched, setTouched] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isValid, setIsValid] = useState(false);

  // Validate form when values or touched fields change
  useEffect(() => {
    const validationErrors = validate(values);
    const touchedErrors = Object.keys(touched).reduce((acc, key) => {
      if (touched[key] && validationErrors[key]) {
        acc[key] = validationErrors[key];
      }
      return acc;
    }, {});
    
    setErrors(touchedErrors);
    setIsValid(Object.keys(validationErrors).length === 0);
  }, [values, touched, validate]);

  // Handle form submission
  useEffect(() => {
    if (isSubmitting && isValid) {
      onSubmit(values);
      setIsSubmitting(false);
    } else if (isSubmitting) {
      // Mark all fields as touched to show all errors
      const allTouched = Object.keys(values).reduce((acc, key) => {
        acc[key] = true;
        return acc;
      }, {});
      setTouched(allTouched);
      setIsSubmitting(false);
    }
  }, [isSubmitting, isValid, onSubmit, values]);

  // Handle input change
  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    
    // Handle different input types
    const inputValue = type === 'checkbox' ? checked : value;
    
    setValues({
      ...values,
      [name]: inputValue
    });
  };

  // Handle input blur (mark field as touched)
  const handleBlur = (e) => {
    const { name } = e.target;
    setTouched({
      ...touched,
      [name]: true
    });
  };

  // Handle form submission
  const handleSubmit = (e) => {
    if (e) e.preventDefault();
    setIsSubmitting(true);
  };

  // Reset form to initial values
  const resetForm = () => {
    setValues(initialValues);
    setErrors({});
    setTouched({});
    setIsSubmitting(false);
  };

  // Set a specific field value
  const setFieldValue = (field, value) => {
    setValues({
      ...values,
      [field]: value
    });
  };

  // Set a specific field as touched
  const setFieldTouched = (field, isTouched = true) => {
    setTouched({
      ...touched,
      [field]: isTouched
    });
  };

  return {
    values,
    errors,
    touched,
    isSubmitting,
    isValid,
    handleChange,
    handleBlur,
    handleSubmit,
    resetForm,
    setFieldValue,
    setFieldTouched
  };
};

export default useFormValidation;
