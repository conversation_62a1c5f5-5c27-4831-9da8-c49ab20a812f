import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { safeApiRequest } from '../../api/apiClient';
import { addToGuestCart } from '../../services/guestCartService';
import Button from '../ui/Button';
import Alert from '../ui/Alert';

const ReorderButton = ({ order, className = '' }) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const { user } = useAuth();
  const navigate = useNavigate();

  const handleReorder = async () => {
    try {
      setLoading(true);
      setError('');
      setSuccess('');

      // Check if all items are still available
      const availabilityChecks = await Promise.all(
        order.items.map(async (item) => {
          try {
            const response = await safeApiRequest({
              method: 'GET',
              url: `/products/${item.product._id || item.product}`
            });
            
            const product = response.data?.data || response.data;
            return {
              ...item,
              product: product,
              available: product.in_stock && product.is_active,
              currentPrice: product.price
            };
          } catch (err) {
            return {
              ...item,
              available: false,
              error: 'Product not found'
            };
          }
        })
      );

      const unavailableItems = availabilityChecks.filter(item => !item.available);
      const availableItems = availabilityChecks.filter(item => item.available);

      if (unavailableItems.length > 0) {
        setError(`${unavailableItems.length} item(s) from your previous order are no longer available. Only available items will be added to cart.`);
      }

      if (availableItems.length === 0) {
        setError('None of the items from this order are currently available.');
        return;
      }

      // Add available items to cart
      for (const item of availableItems) {
        const cartItem = {
          productId: item.product._id,
          variantSku: item.variant_sku,
          quantity: item.quantity,
          price: item.currentPrice,
          warranty_option: item.warranty_option || 'standard',
          warranty_price: item.warranty_price || 0,
          warranty_duration: item.warranty_duration || 12,
          device_condition: item.device_condition || 'new'
        };

        if (user) {
          // Add to authenticated user's cart
          await safeApiRequest({
            method: 'POST',
            url: '/cart',
            data: cartItem
          });
        } else {
          // Add to guest cart
          const productForCart = {
            ...item.product,
            _id: item.product._id,
            name: item.product.name,
            price: item.currentPrice,
            primary_image: item.product.primary_image || { url: item.product.images?.[0]?.url }
          };
          
          addToGuestCart({
            ...cartItem,
            product: productForCart,
            name: item.product.name,
            sku: item.variant_sku || item.product.sku
          });
        }
      }

      // Update header cart count
      if (window.refreshCartCount) {
        window.refreshCartCount();
      }

      setSuccess(`${availableItems.length} item(s) added to cart successfully!`);
      
      // Navigate to cart after a short delay
      setTimeout(() => {
        navigate('/cart');
      }, 1500);

    } catch (err) {
      console.error('Reorder error:', err);
      setError('Failed to add items to cart. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className={className}>
      {error && (
        <Alert type="error" message={error} onClose={() => setError('')} className="mb-3" />
      )}
      
      {success && (
        <Alert type="success" message={success} onClose={() => setSuccess('')} className="mb-3" />
      )}
      
      <Button
        onClick={handleReorder}
        disabled={loading}
        variant="outline"
        size="sm"
        className="w-full"
      >
        {loading ? (
          <>
            <i className="fas fa-spinner fa-spin mr-2"></i>
            Adding to Cart...
          </>
        ) : (
          <>
            <i className="fas fa-redo mr-2"></i>
            Reorder Items
          </>
        )}
      </Button>
      
      <p className="text-xs text-gray-500 mt-2 text-center">
        Available items will be added to your cart at current prices
      </p>
    </div>
  );
};

export default ReorderButton;
