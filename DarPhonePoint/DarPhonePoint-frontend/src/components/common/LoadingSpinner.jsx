import React from 'react';

/**
 * Loading spinner component
 * @returns {JSX.Element} Loading spinner
 */
const LoadingSpinner = ({ size = 'medium', color = 'purple' }) => {
  // Size classes
  const sizeClasses = {
    small: 'h-6 w-6',
    medium: 'h-12 w-12',
    large: 'h-16 w-16'
  };

  // Color classes
  const colorClasses = {
    purple: 'border-purple-500',
    blue: 'border-blue-500',
    green: 'border-green-500',
    red: 'border-red-500',
    gray: 'border-gray-500'
  };

  return (
    <div className="flex justify-center items-center min-h-[200px]">
      <div 
        className={`animate-spin rounded-full ${sizeClasses[size]} border-t-2 border-b-2 ${colorClasses[color]}`}
      ></div>
    </div>
  );
};

export default LoadingSpinner;
