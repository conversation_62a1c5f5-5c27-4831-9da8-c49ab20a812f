import React, { useState, useEffect } from 'react';
import { FaChartLine, FaServer, FaDesktop, FaClock, FaDownload } from 'react-icons/fa';
import Button from '../ui/Button';
import { getMetrics, clearMetrics, reportMetrics } from '../../utils/performanceMonitor';

/**
 * PerformanceMonitor Component
 * Displays performance metrics for the application
 */
const PerformanceMonitor = () => {
  const [activeTab, setActiveTab] = useState('api');
  const [metrics, setMetrics] = useState({});
  const [isOpen, setIsOpen] = useState(false);
  
  // Update metrics every second when open
  useEffect(() => {
    if (!isOpen) return;
    
    const updateMetrics = () => {
      setMetrics(getMetrics());
    };
    
    updateMetrics();
    const interval = setInterval(updateMetrics, 1000);
    
    return () => clearInterval(interval);
  }, [isOpen]);
  
  // Toggle the monitor
  const toggleMonitor = () => {
    setIsOpen(!isOpen);
  };
  
  // Clear metrics for the active tab
  const handleClearMetrics = () => {
    clearMetrics(activeTab);
    setMetrics(getMetrics());
  };
  
  // Report metrics for the active tab
  const handleReportMetrics = async () => {
    await reportMetrics(activeTab);
    setMetrics(getMetrics());
  };
  
  // Get the icon for a tab
  const getTabIcon = (tab) => {
    switch (tab) {
      case 'api':
        return <FaServer className="w-4 h-4" />;
      case 'page':
        return <FaDesktop className="w-4 h-4" />;
      case 'render':
        return <FaClock className="w-4 h-4" />;
      case 'resource':
        return <FaDownload className="w-4 h-4" />;
      default:
        return <FaChartLine className="w-4 h-4" />;
    }
  };
  
  // Calculate average duration for a metric
  const calculateAverage = (metricArray) => {
    if (!metricArray || metricArray.length === 0) return 0;
    const sum = metricArray.reduce((acc, metric) => acc + metric.duration, 0);
    return sum / metricArray.length;
  };
  
  // Calculate success rate for a metric
  const calculateSuccessRate = (metricArray) => {
    if (!metricArray || metricArray.length === 0) return 100;
    const successCount = metricArray.filter(metric => metric.success).length;
    return (successCount / metricArray.length) * 100;
  };
  
  // Render metrics for the active tab
  const renderMetrics = () => {
    let tabMetrics = {};
    switch (activeTab) {
      case 'api':
        tabMetrics = metrics.apiCalls || {};
        break;
      case 'page':
        tabMetrics = metrics.pageLoads || {};
        break;
      case 'render':
        tabMetrics = metrics.renderTimes || {};
        break;
      case 'resource':
        tabMetrics = metrics.resourceLoads || {};
        break;
      default:
        tabMetrics = {};
    }

    const metricNames = Object.keys(tabMetrics);

    if (metricNames.length === 0) {
      return (
        <div className="p-4 text-center text-gray-500">
          No metrics available for this category.
        </div>
      );
    }
    
    return (
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Name
              </th>
              <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Count
              </th>
              <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Avg. Duration
              </th>
              <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Success Rate
              </th>
              <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Last Call
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {metricNames.map(name => {
              const metricArray = tabMetrics[name];
              const avgDuration = calculateAverage(metricArray);
              const successRate = calculateSuccessRate(metricArray);
              const lastCall = metricArray[metricArray.length - 1];
              
              return (
                <tr key={name}>
                  <td className="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-900">
                    {name}
                  </td>
                  <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                    {metricArray.length}
                  </td>
                  <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                    {avgDuration.toFixed(2)}ms
                  </td>
                  <td className="px-4 py-3 whitespace-nowrap text-sm">
                    <div className="flex items-center">
                      <div className="w-16 bg-gray-200 rounded-full h-2.5">
                        <div 
                          className={`h-2.5 rounded-full ${
                            successRate >= 90 ? 'bg-green-500' :
                            successRate >= 75 ? 'bg-yellow-500' :
                            'bg-red-500'
                          }`}
                          style={{ width: `${successRate}%` }}
                        ></div>
                      </div>
                      <span className="ml-2 text-gray-500">{successRate.toFixed(0)}%</span>
                    </div>
                  </td>
                  <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                    {lastCall ? new Date(lastCall.timestamp).toLocaleTimeString() : 'N/A'}
                  </td>
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>
    );
  };
  
  // If closed, just show the toggle button
  if (!isOpen) {
    return (
      <div className="fixed bottom-4 right-4 z-50">
        <button
          onClick={toggleMonitor}
          className="bg-purple-600 text-white p-3 rounded-full shadow-lg hover:bg-purple-700 transition-colors"
          title="Performance Monitor"
        >
          <FaChartLine className="w-5 h-5" />
        </button>
      </div>
    );
  }
  
  // Render the full monitor
  return (
    <div className="fixed bottom-4 right-4 w-full max-w-3xl z-50">
      <div className="bg-white rounded-lg shadow-xl border border-gray-200 overflow-hidden">
        {/* Header */}
        <div className="bg-purple-600 text-white px-4 py-3 flex justify-between items-center">
          <h3 className="text-lg font-medium">Performance Monitor</h3>
          <button
            onClick={toggleMonitor}
            className="text-white hover:text-gray-200"
          >
            <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        
        {/* Tabs */}
        <div className="bg-gray-100 px-4 py-2 border-b border-gray-200">
          <div className="flex space-x-4">
            {['api', 'page', 'render', 'resource'].map(tab => (
              <button
                key={tab}
                className={`flex items-center px-3 py-2 text-sm font-medium rounded-md ${
                  activeTab === tab
                    ? 'bg-white text-purple-700 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                }`}
                onClick={() => setActiveTab(tab)}
              >
                <span className="mr-2">{getTabIcon(tab)}</span>
                {tab.charAt(0).toUpperCase() + tab.slice(1)}
              </button>
            ))}
          </div>
        </div>
        
        {/* Content */}
        <div className="max-h-96 overflow-y-auto">
          {renderMetrics()}
        </div>
        
        {/* Footer */}
        <div className="bg-gray-50 px-4 py-3 flex justify-between">
          <Button
            variant="outline"
            onClick={handleClearMetrics}
          >
            Clear Metrics
          </Button>
          <Button
            onClick={handleReportMetrics}
          >
            Report Metrics
          </Button>
        </div>
      </div>
    </div>
  );
};

export default PerformanceMonitor;
