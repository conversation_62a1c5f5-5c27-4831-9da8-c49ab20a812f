import React from 'react';
import PropTypes from 'prop-types';

/**
 * StatCard component for displaying a statistic with an icon
 *
 * @param {Object} props - Component props
 * @param {string} props.title - Card title
 * @param {string|number} props.value - Statistic value
 * @param {React.ReactNode} props.icon - Icon component
 * @param {string} props.bgColor - Background color for the icon
 * @param {string} props.textColor - Text color for the icon
 * @param {string} props.trend - Trend direction ('up', 'down', or null)
 * @param {number} props.trendValue - Trend percentage value
 * @param {string} props.trendLabel - Trend label text
 * @param {string} props.className - Additional CSS classes
 */
const StatCard = ({
  title,
  value,
  icon,
  bgColor = 'bg-blue-100',
  textColor = 'text-blue-600',
  trend = null,
  trendValue = 0,
  trendLabel = 'vs. previous period',
  className = ''
}) => {
  // Determine trend styling
  let trendColor = '';
  let trendIcon = null;

  if (trend === 'up') {
    trendColor = 'text-green-600';
    trendIcon = (
      <svg className="h-3 w-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 10l7-7m0 0l7 7m-7-7v18" />
      </svg>
    );
  } else if (trend === 'down') {
    trendColor = 'text-red-600';
    trendIcon = (
      <svg className="h-3 w-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
      </svg>
    );
  }

  return (
    <div className={`bg-white rounded-lg shadow-md p-6 ${className}`}>
      <div className="flex items-center">
        <div className={`p-3 rounded-full ${bgColor} ${textColor} mr-4`}>
          {icon}
        </div>
        <div>
          <p className="text-sm text-gray-500">{title}</p>
          <p className="text-2xl font-bold">{value}</p>

          {trend && (
            <div className={`flex items-center mt-1 text-xs ${trendColor}`}>
              {trendIcon}
              <span className="ml-1 font-medium">{trendValue}%</span>
              <span className="ml-1 text-gray-500">{trendLabel}</span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

StatCard.propTypes = {
  title: PropTypes.string.isRequired,
  value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
  icon: PropTypes.node.isRequired,
  bgColor: PropTypes.string,
  textColor: PropTypes.string,
  trend: PropTypes.oneOf(['up', 'down', null]),
  trendValue: PropTypes.number,
  trendLabel: PropTypes.string,
  className: PropTypes.string
};

export default StatCard;
