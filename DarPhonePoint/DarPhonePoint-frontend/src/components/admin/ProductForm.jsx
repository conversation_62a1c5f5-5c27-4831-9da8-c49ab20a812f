import React from 'react';
import { useNavigate } from 'react-router-dom';
import Button from '../ui/Button';
import <PERSON><PERSON>ield from '../ui/FormField';
import Alert from '../ui/Alert';
import useFormValidation from '../../hooks/useFormValidation';
import { productSchema, validateWithSchema } from '../../utils/validationSchemas';
import { createProduct, updateProduct } from '../../services/productService';
import { get, post, put } from '../../api/unifiedApiClient';

/**
 * ProductForm Component
 * Form for creating and editing products
 *
 * @param {Object} product - Product data for editing (null for new product)
 */
const ProductForm = ({ product = null }) => {
  const navigate = useNavigate();
  const isEditMode = !!product;

  // Initial form values
  const initialValues = {
    name: product?.name || '',
    description: product?.description || '',
    price: product?.price || 0,
    product_type: product?.product_type || 'premium',
    is_active: product?.is_active !== undefined ? product?.is_active : true,
    slug: product?.slug || '',
    file_path: product?.file_path || '',
    features: product?.features || [],

  };

  // PDF generation state
  const [htmlContent, setHtmlContent] = React.useState('');
  const [generatePDF, setGeneratePDF] = React.useState(false);
  const [pdfGenerating, setPdfGenerating] = React.useState(false);
  const [htmlTemplates, setHtmlTemplates] = React.useState([]);
  const [selectedTemplate, setSelectedTemplate] = React.useState('');
  const [generatedPdfUrl, setGeneratedPdfUrl] = React.useState('');

  // Features management state
  const [newFeature, setNewFeature] = React.useState('');

  // Features management functions
  const addFeature = () => {
    if (newFeature.trim() && !values.features.includes(newFeature.trim())) {
      const updatedFeatures = [...values.features, newFeature.trim()];
      handleChange({
        target: {
          name: 'features',
          value: updatedFeatures
        }
      });
      setNewFeature('');
    }
  };

  const removeFeature = (index) => {
    const updatedFeatures = values.features.filter((_, i) => i !== index);
    handleChange({
      target: {
        name: 'features',
        value: updatedFeatures
      }
    });
  };

  const handleFeatureKeyPress = (e) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      addFeature();
    }
  };

  // Form submission handler
  const handleSubmit = async (values) => {
    try {
      // Auto-generate file path if not provided
      if (!values.file_path && values.slug) {
        values.file_path = `/uploads/products/${values.slug}.pdf`;
      }

      let productData;
      if (isEditMode) {
        productData = await updateProduct(product._id, values);
        setStatus({ type: 'success', message: 'Product updated successfully!' });
      } else {
        productData = await createProduct(values);
        setStatus({ type: 'success', message: 'Product created successfully!' });
      }

      // Generate PDF if requested and HTML content is provided
      if (generatePDF && htmlContent.trim()) {
        await handlePDFGeneration(values.slug || values.name, htmlContent);
      } else if (!isEditMode && !generatePDF) {
        // For new products without PDF generation, create a placeholder PDF
        const placeholderHTML = `
          <html>
            <head>
              <title>${values.name}</title>
              <style>
                body { font-family: Arial, sans-serif; padding: 40px; line-height: 1.6; color: #333; }
                h1 { color: #2563eb; border-bottom: 2px solid #2563eb; padding-bottom: 10px; }
                .placeholder { background: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0; }
              </style>
            </head>
            <body>
              <h1>${values.name}</h1>
              <div class="placeholder">
                <p><strong>This is a placeholder PDF for ${values.name}.</strong></p>
                <p>Please update this content with the actual product information using the PDF Management section.</p>
              </div>
              <h2>Product Details:</h2>
              <p><strong>Description:</strong> ${values.description || 'No description provided'}</p>
              <p><strong>Price:</strong> $${values.price}</p>
              <p><strong>Type:</strong> ${values.product_type}</p>
              ${values.features && values.features.length > 0 ? `
                <h2>Features:</h2>
                <ul>
                  ${values.features.map(feature => `<li>${feature}</li>`).join('')}
                </ul>
              ` : ''}
            </body>
          </html>
        `;

        setStatus({ type: 'info', message: 'Creating placeholder PDF...' });
        await handlePDFGeneration(values.slug || values.name, placeholderHTML);
      }

      if (!isEditMode) {
        resetForm();
        setHtmlContent('');
        setGeneratePDF(false);
      }

      // Navigate back to product list after a short delay
      setTimeout(() => {
        navigate('/admin/products');
      }, 1500);
    } catch (error) {
      // Error is already handled by the UI state
      setStatus({
        type: 'error',
        message: `Failed to ${isEditMode ? 'update' : 'create'} product. Please try again.`
      });
    }
  };

  // Form validation (memoized to prevent infinite re-renders)
  const validate = React.useCallback((values) => {
    return validateWithSchema(productSchema, values);
  }, []);

  // Use form validation hook
  const {
    values,
    errors,
    touched,
    handleChange,
    handleBlur,
    handleSubmit: submitForm,
    isSubmitting,
    isValid,
    resetForm
  } = useFormValidation(initialValues, validate, handleSubmit);

  // Form status state
  const [status, setStatus] = React.useState(null);

  // PDF generation handler
  const handlePDFGeneration = async (productName, content) => {
    try {
      setPdfGenerating(true);
      setStatus({ type: 'info', message: 'Generating PDF...' });

      // Use the product's slug to match the database file_path
      const slugName = values.slug || productName || values.name || 'preview';

      const response = await apiRequest('post', '/admin/pdf/generate', {
        productName: slugName,
        htmlContent: content
      });

      if (response.success) {
        setStatus({ type: 'success', message: 'PDF generated successfully!' });
        // Add cache-busting parameter to ensure updated PDFs are loaded
        const cacheBuster = Date.now();
        setGeneratedPdfUrl(`http://localhost:5001${response.filePath}?v=${cacheBuster}`);
      } else {
        setStatus({ type: 'error', message: 'Failed to generate PDF' });
      }
    } catch (error) {
      // Error is already handled by the UI state
      setStatus({ type: 'error', message: 'Error generating PDF: ' + error.message });
    } finally {
      setPdfGenerating(false);
    }
  };

  // Load HTML templates from backend
  React.useEffect(() => {
    const loadTemplates = async () => {
      try {
        const response = await apiRequest('get', '/admin/templates');
        if (response.success) {
          const templates = response.templates.map(template => ({
            value: template.id,
            label: template.name
          }));
          templates.push({ value: 'custom', label: 'Custom HTML Content' });
          setHtmlTemplates(templates);
        }
      } catch (error) {
        // Error is handled by the UI state
        // Fallback to static templates
        const fallbackTemplates = [
          { value: 'ai_prompts_lead_magnet', label: 'AI Prompts Lead Magnet Template' },
          { value: 'ai_productivity_master_guide', label: 'AI Productivity Master Guide Template' },
          { value: 'ai_business_revolution', label: 'AI Business Revolution Template' },
          { value: 'custom', label: 'Custom HTML Content' }
        ];
        setHtmlTemplates(fallbackTemplates);
      }
    };
    loadTemplates();
  }, []);

  // Load template content when template is selected
  React.useEffect(() => {
    const loadTemplateContent = async () => {
      if (selectedTemplate && selectedTemplate !== 'custom') {
        try {
          const response = await apiRequest('get', `/admin/templates/${selectedTemplate}`);
          if (response.success) {
            setHtmlContent(response.template.content);
          }
        } catch (error) {
          // Error is handled by the UI state
          setStatus({ type: 'error', message: 'Failed to load template content' });
        }
      } else if (selectedTemplate === 'custom') {
        setHtmlContent('');
      }
    };
    loadTemplateContent();
  }, [selectedTemplate]);

  // Auto-generate file path when slug changes
  React.useEffect(() => {
    if (values.slug && !isEditMode) {
      const event = {
        target: {
          name: 'file_path',
          value: `/uploads/products/${values.slug}.pdf`
        }
      };
      handleChange(event);
    }
  }, [values.slug, isEditMode]); // Removed handleChange from dependencies

  // Generate slug from name
  const generateSlug = () => {
    if (!values.name) return;

    const slug = values.name
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/(^-|-$)/g, '');

    // Update the slug field
    const event = {
      target: {
        name: 'slug',
        value: slug
      }
    };
    handleChange(event);
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      {status && (
        <Alert
          type={status.type}
          message={status.message}
          onClose={() => setStatus(null)}
          className="mb-6"
        />
      )}

      <form onSubmit={submitForm}>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Product Name */}
          <FormField
            type="text"
            name="name"
            label="Product Name"
            value={values.name}
            onChange={handleChange}
            onBlur={handleBlur}
            error={errors.name}
            touched={touched.name}
            required
            className="md:col-span-2"
          />

          {/* Product Slug */}
          <div className="md:col-span-2">
            <div className="flex items-end space-x-2">
              <div className="flex-grow">
                <FormField
                  type="text"
                  name="slug"
                  label="Slug"
                  value={values.slug}
                  onChange={handleChange}
                  onBlur={handleBlur}
                  error={errors.slug}
                  touched={touched.slug}
                  helpText="URL-friendly version of the product name"
                  required
                />
              </div>
              <div className="mb-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={generateSlug}
                  disabled={!values.name}
                >
                  Generate
                </Button>
              </div>
            </div>
          </div>

          {/* Product Description */}
          <FormField
            type="textarea"
            name="description"
            label="Description"
            value={values.description}
            onChange={handleChange}
            onBlur={handleBlur}
            error={errors.description}
            touched={touched.description}
            required
            className="md:col-span-2"
          />

          {/* Product Features */}
          <div className="md:col-span-2">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Product Features
            </label>

            {/* Add Feature Input */}
            <div className="flex gap-2 mb-3">
              <input
                type="text"
                value={newFeature}
                onChange={(e) => setNewFeature(e.target.value)}
                onKeyPress={handleFeatureKeyPress}
                placeholder="Enter a product feature..."
                className="flex-1 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500"
              />
              <Button
                type="button"
                variant="outline"
                onClick={addFeature}
                disabled={!newFeature.trim()}
              >
                Add Feature
              </Button>
            </div>

            {/* Features List */}
            {values.features && values.features.length > 0 && (
              <div className="space-y-2">
                <p className="text-sm text-gray-600">Current Features:</p>
                <div className="space-y-1">
                  {values.features.map((feature, index) => (
                    <div
                      key={index}
                      className="flex items-center justify-between bg-gray-50 px-3 py-2 rounded-md"
                    >
                      <span className="text-sm text-gray-700">• {feature}</span>
                      <button
                        type="button"
                        onClick={() => removeFeature(index)}
                        className="text-red-500 hover:text-red-700 text-sm font-medium"
                      >
                        Remove
                      </button>
                    </div>
                  ))}
                </div>
              </div>
            )}

            <p className="mt-1 text-sm text-gray-500">
              Add key features and benefits of your product. Press Enter or click "Add Feature" to add each feature.
            </p>
          </div>

          {/* Product Price */}
          <FormField
            type="number"
            name="price"
            label="Price"
            value={values.price}
            onChange={handleChange}
            onBlur={handleBlur}
            error={errors.price}
            touched={touched.price}
            required
            min="0"
            step="0.01"
          />

          {/* Product Type */}
          <FormField
            type="select"
            name="product_type"
            label="Product Type"
            value={values.product_type}
            onChange={handleChange}
            onBlur={handleBlur}
            error={errors.product_type}
            touched={touched.product_type}
            options={[
              { value: 'lead_magnet', label: 'Lead Magnet' },
              { value: 'premium', label: 'Premium' },
              { value: 'basic', label: 'Basic' }
            ]}
            required
          />

          {/* File Path */}
          <FormField
            type="text"
            name="file_path"
            label="File Path"
            value={values.file_path}
            onChange={handleChange}
            onBlur={handleBlur}
            error={errors.file_path}
            touched={touched.file_path}
            helpText="Path to the product file (e.g., /uploads/products/my-product.pdf)"
            placeholder="/uploads/products/product-name.pdf"
            required
            className="md:col-span-2"
          />



          {/* Product Status */}
          <div className="md:col-span-2">
            <FormField
              type="checkbox"
              name="is_active"
              label="Active"
              checked={values.is_active}
              onChange={handleChange}
              onBlur={handleBlur}
              error={errors.is_active}
              touched={touched.is_active}
              helpText="Inactive products won't be visible to customers"
            />
          </div>
        </div>

        {/* PDF Generation Section */}
        <div className="mt-8 border-t border-gray-200 pt-8">
          <h3 className="text-lg font-medium text-gray-900 mb-4">📄 PDF Generation</h3>

          {/* Generate PDF Checkbox */}
          <div className="mb-4">
            <FormField
              type="checkbox"
              name="generate_pdf"
              label="Generate custom PDF for this product"
              checked={generatePDF}
              onChange={(e) => setGeneratePDF(e.target.checked)}
              helpText="Check this to create a custom PDF with your own content. If unchecked, a placeholder PDF will be automatically created."
            />
          </div>

          {generatePDF && (
            <div className="space-y-4">
              {/* Template Selection */}
              <FormField
                type="select"
                name="html_template"
                label="HTML Template"
                value={selectedTemplate}
                onChange={(e) => setSelectedTemplate(e.target.value)}
                options={htmlTemplates}
                helpText="Choose a pre-built template or select 'Custom' to write your own HTML"
              />

              {/* HTML Content Editor */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  HTML Content
                </label>
                <textarea
                  rows={12}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500"
                  placeholder="<html><head><title>Your Product</title></head><body>...</body></html>"
                  value={htmlContent}
                  onChange={(e) => setHtmlContent(e.target.value)}
                />
                <p className="mt-1 text-sm text-gray-500">
                  Enter your HTML content here. The system will automatically apply professional styling and formatting.
                </p>
              </div>

              {/* PDF Preview Button */}
              {htmlContent.trim() && (
                <div className="flex justify-start space-x-3">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => handlePDFGeneration(values.slug || values.name || 'preview', htmlContent)}
                    disabled={pdfGenerating || !htmlContent.trim()}
                    isLoading={pdfGenerating}
                  >
                    {pdfGenerating ? 'Generating Preview...' : 'Preview PDF'}
                  </Button>

                  {generatedPdfUrl && (
                    <Button
                      type="button"
                      variant="primary"
                      onClick={() => window.open(generatedPdfUrl, '_blank')}
                    >
                      View Generated PDF
                    </Button>
                  )}
                </div>
              )}

              {/* PDF Preview Display */}
              {generatedPdfUrl && (
                <div className="mt-4 p-4 bg-gray-50 rounded-lg">
                  <h4 className="text-sm font-medium text-gray-900 mb-2">PDF Preview</h4>
                  <div className="bg-white rounded border" style={{ height: '400px' }}>
                    <iframe
                      src={generatedPdfUrl}
                      style={{
                        width: '100%',
                        height: '100%',
                        border: 'none'
                      }}
                      title="PDF Preview"
                    />
                  </div>
                  <p className="text-xs text-gray-500 mt-2">
                    Preview of the generated PDF. This will be saved as the product file.
                  </p>
                </div>
              )}
            </div>
          )}
        </div>

        <div className="mt-6 flex justify-end space-x-3">
          <Button
            type="button"
            variant="outline"
            onClick={() => navigate('/admin/products')}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            disabled={isSubmitting || !isValid}
            isLoading={isSubmitting}
          >
            {isEditMode ? 'Update Product' : 'Create Product'}
          </Button>
        </div>
      </form>
    </div>
  );
};

export default ProductForm;
