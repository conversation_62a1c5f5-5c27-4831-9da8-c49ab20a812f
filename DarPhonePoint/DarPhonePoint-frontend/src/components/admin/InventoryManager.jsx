import React, { useState, useEffect } from 'react';
import { safeApiRequest } from '../../api/apiClient';
import { formatPrice } from '../../utils/priceFormatter';
import { getCategoryConfig } from '../../config/productCategories';
import LoadingState from '../ui/LoadingState';
import Alert from '../ui/Alert';
import Button from '../ui/Button';
import FormField from '../ui/FormField';
import Modal from '../ui/Modal';

/**
 * Advanced Inventory Manager Component
 * Provides comprehensive inventory tracking and management
 */
const InventoryManager = () => {
  const [inventory, setInventory] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [filters, setFilters] = useState({
    category: '',
    status: 'all',
    stockLevel: 'all'
  });
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [showStockModal, setShowStockModal] = useState(false);
  const [stockAdjustment, setStockAdjustment] = useState({
    type: 'add',
    quantity: 0,
    reason: ''
  });

  useEffect(() => {
    fetchInventory();
  }, [filters]);

  const fetchInventory = async () => {
    try {
      setLoading(true);
      setError('');

      const queryParams = new URLSearchParams();
      if (filters.category) queryParams.append('category', filters.category);
      if (filters.status !== 'all') queryParams.append('status', filters.status);
      if (filters.stockLevel !== 'all') queryParams.append('stockLevel', filters.stockLevel);

      const response = await safeApiRequest(`/api/admin/inventory?${queryParams}`);
      setInventory(response.data?.data || []);
    } catch (err) {
      setError('Failed to load inventory data');
      console.error('Inventory fetch error:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleStockAdjustment = async () => {
    try {
      setLoading(true);
      
      const response = await safeApiRequest(`/api/admin/products/${selectedProduct._id}/adjust-stock`, {
        method: 'POST',
        data: stockAdjustment
      });

      // Update local inventory
      setInventory(prev => prev.map(item => 
        item._id === selectedProduct._id 
          ? { ...item, stock_quantity: response.data.newStock }
          : item
      ));

      setShowStockModal(false);
      setStockAdjustment({ type: 'add', quantity: 0, reason: '' });
    } catch (err) {
      setError('Failed to adjust stock');
    } finally {
      setLoading(false);
    }
  };

  const getStockStatus = (product) => {
    if (product.stock_quantity === 0) return { status: 'out-of-stock', color: 'red', label: 'Out of Stock' };
    if (product.stock_quantity <= product.low_stock_threshold) return { status: 'low-stock', color: 'yellow', label: 'Low Stock' };
    if (product.stock_quantity > 50) return { status: 'overstock', color: 'blue', label: 'High Stock' };
    return { status: 'normal', color: 'green', label: 'Normal' };
  };

  const getInventoryValue = () => {
    return inventory.reduce((total, product) => {
      return total + (product.price * product.stock_quantity);
    }, 0);
  };

  const getStockAlerts = () => {
    return {
      outOfStock: inventory.filter(p => p.stock_quantity === 0).length,
      lowStock: inventory.filter(p => p.stock_quantity > 0 && p.stock_quantity <= p.low_stock_threshold).length,
      overstock: inventory.filter(p => p.stock_quantity > 50).length
    };
  };

  if (loading && inventory.length === 0) {
    return <LoadingState text="Loading inventory..." />;
  }

  const alerts = getStockAlerts();
  const totalValue = getInventoryValue();

  return (
    <div className="space-y-6">
      {/* Inventory Overview */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <h2 className="text-xl font-bold text-gray-900 mb-4 flex items-center">
          <i className="fas fa-warehouse mr-2 text-blue-600"></i>
          Inventory Overview
        </h2>
        
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <div className="bg-blue-50 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-blue-600">Total Products</p>
                <p className="text-2xl font-bold text-blue-900">{inventory.length}</p>
              </div>
              <i className="fas fa-boxes text-blue-600 text-2xl"></i>
            </div>
          </div>
          
          <div className="bg-green-50 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-green-600">Inventory Value</p>
                <p className="text-2xl font-bold text-green-900">{formatPrice(totalValue)}</p>
              </div>
              <i className="fas fa-dollar-sign text-green-600 text-2xl"></i>
            </div>
          </div>
          
          <div className="bg-yellow-50 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-yellow-600">Low Stock</p>
                <p className="text-2xl font-bold text-yellow-900">{alerts.lowStock}</p>
              </div>
              <i className="fas fa-exclamation-triangle text-yellow-600 text-2xl"></i>
            </div>
          </div>
          
          <div className="bg-red-50 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-red-600">Out of Stock</p>
                <p className="text-2xl font-bold text-red-900">{alerts.outOfStock}</p>
              </div>
              <i className="fas fa-times-circle text-red-600 text-2xl"></i>
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <FormField
            label="Category"
            name="category"
            type="select"
            value={filters.category}
            onChange={(e) => setFilters({ ...filters, category: e.target.value })}
            options={[
              { value: '', label: 'All Categories' },
              { value: 'smartphone', label: '📱 Smartphones' },
              { value: 'laptop', label: '💻 Laptops' },
              { value: 'gaming_console', label: '🎮 Gaming Consoles' },
              { value: 'camera', label: '📷 Cameras' },
              { value: 'router', label: '📡 Routers' }
            ]}
          />
          
          <FormField
            label="Status"
            name="status"
            type="select"
            value={filters.status}
            onChange={(e) => setFilters({ ...filters, status: e.target.value })}
            options={[
              { value: 'all', label: 'All Status' },
              { value: 'active', label: 'Active' },
              { value: 'inactive', label: 'Inactive' },
              { value: 'draft', label: 'Draft' }
            ]}
          />
          
          <FormField
            label="Stock Level"
            name="stockLevel"
            type="select"
            value={filters.stockLevel}
            onChange={(e) => setFilters({ ...filters, stockLevel: e.target.value })}
            options={[
              { value: 'all', label: 'All Stock Levels' },
              { value: 'out-of-stock', label: 'Out of Stock' },
              { value: 'low-stock', label: 'Low Stock' },
              { value: 'normal', label: 'Normal Stock' },
              { value: 'overstock', label: 'High Stock' }
            ]}
          />
        </div>
      </div>

      {error && <Alert type="error" message={error} />}

      {/* Inventory Table */}
      <div className="bg-white rounded-lg shadow-md overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">Product Inventory</h3>
        </div>
        
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Product
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Category
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Stock
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Value
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {inventory.map((product) => {
                const stockStatus = getStockStatus(product);
                const categoryConfig = getCategoryConfig(product.category);
                
                return (
                  <tr key={product._id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <span className="text-2xl mr-3">{categoryConfig.icon}</span>
                        <div>
                          <div className="text-sm font-medium text-gray-900">{product.name}</div>
                          <div className="text-sm text-gray-500">{product.brand} • {product.sku}</div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="text-sm text-gray-900">{categoryConfig.label}</span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        {product.stock_quantity} units
                      </div>
                      <div className="text-xs text-gray-500">
                        Threshold: {product.low_stock_threshold}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {formatPrice(product.price * product.stock_quantity)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-${stockStatus.color}-100 text-${stockStatus.color}-800`}>
                        {stockStatus.label}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <Button
                        onClick={() => {
                          setSelectedProduct(product);
                          setShowStockModal(true);
                        }}
                        className="text-blue-600 hover:text-blue-900 mr-2"
                        variant="link"
                      >
                        <i className="fas fa-edit mr-1"></i>
                        Adjust Stock
                      </Button>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </div>

      {/* Stock Adjustment Modal */}
      <Modal
        isOpen={showStockModal}
        onClose={() => setShowStockModal(false)}
        title="Adjust Stock Level"
      >
        {selectedProduct && (
          <div className="space-y-4">
            <div className="bg-gray-50 rounded-lg p-4">
              <h4 className="font-medium text-gray-900">{selectedProduct.name}</h4>
              <p className="text-sm text-gray-600">Current Stock: {selectedProduct.stock_quantity} units</p>
            </div>
            
            <FormField
              label="Adjustment Type"
              name="type"
              type="select"
              value={stockAdjustment.type}
              onChange={(e) => setStockAdjustment({ ...stockAdjustment, type: e.target.value })}
              options={[
                { value: 'add', label: 'Add Stock' },
                { value: 'subtract', label: 'Remove Stock' },
                { value: 'set', label: 'Set New Level' }
              ]}
            />
            
            <FormField
              label="Quantity"
              name="quantity"
              type="number"
              value={stockAdjustment.quantity}
              onChange={(e) => setStockAdjustment({ ...stockAdjustment, quantity: parseInt(e.target.value) })}
              min="0"
              required
            />
            
            <FormField
              label="Reason"
              name="reason"
              type="select"
              value={stockAdjustment.reason}
              onChange={(e) => setStockAdjustment({ ...stockAdjustment, reason: e.target.value })}
              options={[
                { value: 'restock', label: 'New Stock Arrival' },
                { value: 'sale', label: 'Manual Sale' },
                { value: 'damage', label: 'Damaged Items' },
                { value: 'theft', label: 'Theft/Loss' },
                { value: 'return', label: 'Customer Return' },
                { value: 'correction', label: 'Inventory Correction' }
              ]}
              required
            />
            
            <div className="flex justify-end space-x-3 pt-4 border-t">
              <Button
                variant="secondary"
                onClick={() => setShowStockModal(false)}
              >
                Cancel
              </Button>
              <Button
                onClick={handleStockAdjustment}
                disabled={loading || !stockAdjustment.quantity || !stockAdjustment.reason}
              >
                {loading ? 'Updating...' : 'Update Stock'}
              </Button>
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default InventoryManager;
