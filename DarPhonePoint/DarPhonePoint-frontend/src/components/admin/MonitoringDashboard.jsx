import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  <PERSON><PERSON><PERSON>,
  Bar
} from 'recharts';
import monitoringService from '../../services/monitoringService';
import { Card, CardContent, Typography, Grid, Button, Alert } from '@mui/material';
import { Refresh as RefreshIcon } from '@mui/icons-material';

const MonitoringDashboard = () => {
  const [metrics, setMetrics] = useState(null);
  const [performance, setPerformance] = useState(null);
  const [error, setError] = useState(null);
  const [loading, setLoading] = useState(true);

  const fetchData = async () => {
    try {
      setLoading(true);
      setError(null);
      const [metricsData, performanceData] = await Promise.all([
        monitoringService.getMetrics(),
        monitoringService.getPerformanceReport()
      ]);
      setMetrics(metricsData.data);
      setPerformance(performanceData.data);
    } catch (err) {
      setError('Failed to fetch monitoring data');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const handleReset = async () => {
    try {
      await monitoringService.resetMetrics();
      fetchData();
    } catch (err) {
      setError('Failed to reset metrics');
      console.error(err);
    }
  };

  useEffect(() => {
    fetchData();
    const interval = setInterval(fetchData, 30000); // Refresh every 30 seconds
    return () => clearInterval(interval);
  }, []);

  if (loading) {
    return <Typography>Loading monitoring data...</Typography>;
  }

  if (error) {
    return <Alert severity="error">{error}</Alert>;
  }

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <Typography variant="h4" component="h1">
          System Monitoring Dashboard
        </Typography>
        <Button
          variant="contained"
          startIcon={<RefreshIcon />}
          onClick={handleReset}
        >
          Reset Metrics
        </Button>
      </div>

      <Grid container spacing={3}>
        {/* Request Statistics */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Request Statistics
              </Typography>
              <div className="grid grid-cols-3 gap-4">
                <div>
                  <Typography variant="subtitle2">Total Requests</Typography>
                  <Typography variant="h4">{metrics?.requests.total || 0}</Typography>
                </div>
                <div>
                  <Typography variant="subtitle2">Success Rate</Typography>
                  <Typography variant="h4">
                    {((metrics?.requests.success / metrics?.requests.total) * 100 || 0).toFixed(1)}%
                  </Typography>
                </div>
                <div>
                  <Typography variant="subtitle2">Error Rate</Typography>
                  <Typography variant="h4">
                    {((metrics?.requests.error / metrics?.requests.total) * 100 || 0).toFixed(1)}%
                  </Typography>
                </div>
              </div>
            </CardContent>
          </Card>
        </Grid>

        {/* System Performance */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                System Performance
              </Typography>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Typography variant="subtitle2">Memory Usage</Typography>
                  <Typography variant="h4">
                    {((metrics?.performance.memoryUsage[0]?.used / metrics?.performance.memoryUsage[0]?.total) * 100 || 0).toFixed(1)}%
                  </Typography>
                </div>
                <div>
                  <Typography variant="subtitle2">CPU Load</Typography>
                  <Typography variant="h4">
                    {(metrics?.performance.cpuUsage[0]?.loadAvg[0] || 0).toFixed(2)}
                  </Typography>
                </div>
              </div>
            </CardContent>
          </Card>
        </Grid>

        {/* Response Time Chart */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Response Time Trend
              </Typography>
              <div style={{ height: 300 }}>
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={metrics?.performance.responseTime}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="timestamp" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Line
                      type="monotone"
                      dataKey="responseTime"
                      stroke="#8884d8"
                      name="Response Time (ms)"
                    />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </Grid>

        {/* Cache Performance */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Cache Performance
              </Typography>
              <div style={{ height: 300 }}>
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={[
                    { name: 'Hits', value: metrics?.cache.hits || 0 },
                    { name: 'Misses', value: metrics?.cache.misses || 0 }
                  ]}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Bar dataKey="value" fill="#82ca9d" />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </Grid>

        {/* Database Performance */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Database Performance
              </Typography>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Typography variant="subtitle2">Total Operations</Typography>
                  <Typography variant="h4">{metrics?.database.operations || 0}</Typography>
                </div>
                <div>
                  <Typography variant="subtitle2">Slow Queries</Typography>
                  <Typography variant="h4">{metrics?.database.slowQueries.length || 0}</Typography>
                </div>
              </div>
              {metrics?.database.slowQueries.length > 0 && (
                <div className="mt-4">
                  <Typography variant="subtitle2" gutterBottom>
                    Recent Slow Queries
                  </Typography>
                  <div className="max-h-40 overflow-y-auto">
                    {metrics.database.slowQueries.slice(0, 5).map((query, index) => (
                      <div key={index} className="text-sm mb-2">
                        <Typography variant="body2">
                          {query.operation} - {query.duration}ms
                        </Typography>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </div>
  );
};

export default MonitoringDashboard; 