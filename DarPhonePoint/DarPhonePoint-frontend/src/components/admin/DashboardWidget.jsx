import React, { useState } from 'react';
import PropTypes from 'prop-types';

/**
 * DashboardWidget component for creating customizable dashboard widgets
 * 
 * @param {Object} props - Component props
 * @param {string} props.title - Widget title
 * @param {React.ReactNode} props.children - Widget content
 * @param {string} props.className - Additional CSS classes
 * @param {boolean} props.collapsible - Whether the widget can be collapsed
 * @param {boolean} props.defaultCollapsed - Whether the widget is collapsed by default
 * @param {boolean} props.refreshable - Whether the widget can be refreshed
 * @param {function} props.onRefresh - Function to call when the refresh button is clicked
 * @param {boolean} props.loading - Whether the widget is loading
 * @param {boolean} props.error - Whether the widget has an error
 * @param {string} props.errorMessage - Error message to display
 */
const DashboardWidget = ({
  title,
  children,
  className = '',
  collapsible = false,
  defaultCollapsed = false,
  refreshable = false,
  onRefresh = () => {},
  loading = false,
  error = false,
  errorMessage = 'An error occurred while loading data.'
}) => {
  const [collapsed, setCollapsed] = useState(defaultCollapsed);
  const [isRefreshing, setIsRefreshing] = useState(false);

  const handleRefresh = async () => {
    if (isRefreshing) return;
    
    setIsRefreshing(true);
    try {
      await onRefresh();
    } finally {
      setIsRefreshing(false);
    }
  };

  return (
    <div className={`bg-white rounded-lg shadow-md overflow-hidden ${className}`}>
      {/* Widget Header */}
      <div className="flex justify-between items-center px-6 py-4 border-b border-gray-200">
        <h3 className="text-lg font-medium text-gray-800">{title}</h3>
        <div className="flex space-x-2">
          {refreshable && (
            <button
              onClick={handleRefresh}
              className="text-gray-400 hover:text-gray-600 focus:outline-none"
              disabled={isRefreshing}
              aria-label="Refresh"
            >
              <svg
                className={`h-5 w-5 ${isRefreshing ? 'animate-spin' : ''}`}
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                />
              </svg>
            </button>
          )}
          {collapsible && (
            <button
              onClick={() => setCollapsed(!collapsed)}
              className="text-gray-400 hover:text-gray-600 focus:outline-none"
              aria-label={collapsed ? 'Expand' : 'Collapse'}
            >
              <svg
                className="h-5 w-5"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d={
                    collapsed
                      ? 'M19 9l-7 7-7-7'
                      : 'M5 15l7-7 7 7'
                  }
                />
              </svg>
            </button>
          )}
        </div>
      </div>

      {/* Widget Content */}
      {!collapsed && (
        <div className="p-6">
          {loading ? (
            <div className="flex justify-center items-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-purple-500"></div>
            </div>
          ) : error ? (
            <div className="bg-red-50 text-red-600 p-4 rounded-md">
              <div className="flex">
                <svg
                  className="h-5 w-5 mr-2"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
                <span>{errorMessage}</span>
              </div>
            </div>
          ) : (
            children
          )}
        </div>
      )}
    </div>
  );
};

DashboardWidget.propTypes = {
  title: PropTypes.string.isRequired,
  children: PropTypes.node.isRequired,
  className: PropTypes.string,
  collapsible: PropTypes.bool,
  defaultCollapsed: PropTypes.bool,
  refreshable: PropTypes.bool,
  onRefresh: PropTypes.func,
  loading: PropTypes.bool,
  error: PropTypes.bool,
  errorMessage: PropTypes.string
};

export default DashboardWidget;
