import React, { useState } from 'react';
import { FaDownload, FaExpand, FaCompress, FaEllipsisV } from 'react-icons/fa';

/**
 * ChartCard Component
 * A wrapper for charts with consistent styling and functionality
 * 
 * @param {React.ReactNode} children - Chart component to display
 * @param {String} title - Card title
 * @param {String} description - Card description
 * @param {Array} actions - Additional actions for the card
 * @param {Boolean} allowDownload - Whether to allow downloading the chart
 * @param {Boolean} allowFullscreen - Whether to allow fullscreen mode
 * @param {String} className - Additional CSS classes
 */
const ChartCard = ({
  children,
  title,
  description,
  actions = [],
  allowDownload = true,
  allowFullscreen = true,
  className = ''
}) => {
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [showMenu, setShowMenu] = useState(false);
  
  // Handle download chart
  const handleDownload = () => {
    // Find canvas element within the chart
    const canvas = document.querySelector(`#chart-${title.replace(/\s+/g, '-').toLowerCase()} canvas`);
    
    if (!canvas) return;
    
    // Create a temporary link element
    const link = document.createElement('a');
    link.download = `${title.replace(/\s+/g, '-').toLowerCase()}-${new Date().toISOString().split('T')[0]}.png`;
    link.href = canvas.toDataURL('image/png');
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };
  
  // Toggle fullscreen mode
  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };
  
  // Toggle action menu
  const toggleMenu = () => {
    setShowMenu(!showMenu);
  };
  
  return (
    <div 
      id={`chart-${title.replace(/\s+/g, '-').toLowerCase()}`}
      className={`bg-white rounded-lg shadow-md overflow-hidden ${
        isFullscreen 
          ? 'fixed inset-0 z-50 flex flex-col' 
          : className
      }`}
    >
      {/* Card Header */}
      <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
        <div>
          <h2 className="text-lg font-semibold text-gray-900">{title}</h2>
          {description && (
            <p className="text-sm text-gray-500 mt-1">{description}</p>
          )}
        </div>
        
        <div className="flex items-center space-x-2">
          {/* Download button */}
          {allowDownload && (
            <button
              onClick={handleDownload}
              className="p-1 text-gray-500 hover:text-gray-700 rounded-full hover:bg-gray-100"
              title="Download chart"
            >
              <FaDownload className="w-4 h-4" />
            </button>
          )}
          
          {/* Fullscreen button */}
          {allowFullscreen && (
            <button
              onClick={toggleFullscreen}
              className="p-1 text-gray-500 hover:text-gray-700 rounded-full hover:bg-gray-100"
              title={isFullscreen ? 'Exit fullscreen' : 'View fullscreen'}
            >
              {isFullscreen ? (
                <FaCompress className="w-4 h-4" />
              ) : (
                <FaExpand className="w-4 h-4" />
              )}
            </button>
          )}
          
          {/* Additional actions */}
          {actions.length > 0 && (
            <div className="relative">
              <button
                onClick={toggleMenu}
                className="p-1 text-gray-500 hover:text-gray-700 rounded-full hover:bg-gray-100"
                title="More options"
              >
                <FaEllipsisV className="w-4 h-4" />
              </button>
              
              {/* Dropdown menu */}
              {showMenu && (
                <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10">
                  <div className="py-1">
                    {actions.map((action, index) => (
                      <button
                        key={index}
                        onClick={() => {
                          action.onClick();
                          setShowMenu(false);
                        }}
                        className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >
                        {action.icon && (
                          <span className="mr-2">{action.icon}</span>
                        )}
                        {action.label}
                      </button>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
      
      {/* Card Content */}
      <div className={`p-6 ${isFullscreen ? 'flex-grow' : ''}`}>
        {children}
      </div>
    </div>
  );
};

export default ChartCard;
