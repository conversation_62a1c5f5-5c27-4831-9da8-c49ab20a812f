import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import Button from '../ui/Button';
import Form<PERSON>ield from '../ui/FormField';
import Alert from '../ui/Alert';
import { 
  getCategoryConfig, 
  getCategoryOptions, 
  getBrandsForCategory, 
  getFieldsForCategory 
} from '../../config/productCategories';
import { createProduct, updateProduct } from '../../services/productService';

/**
 * DynamicProductForm Component
 * Intelligent form that adapts based on selected product category
 */
const DynamicProductForm = ({ product = null }) => {
  const navigate = useNavigate();
  const isEditMode = !!product;

  // Form state
  const [formData, setFormData] = useState({
    // Basic product info
    name: product?.name || '',
    brand: product?.brand || '',
    model: product?.model || '',
    category: product?.category || 'smartphone',
    subcategory: product?.subcategory || '',
    
    // Pricing
    price: product?.price || 0,
    cost_price: product?.cost_price || 0,
    
    // Description
    description: product?.description || '',
    short_description: product?.short_description || '',
    
    // Inventory
    stock_quantity: product?.stock_quantity || 0,
    low_stock_threshold: product?.low_stock_threshold || 5,
    sku: product?.sku || '',
    
    // Status
    status: product?.status || 'active',
    is_featured: product?.is_featured || false,
    
    // Dynamic specifications (will be populated based on category)
    specifications: product?.specifications || {}
  });

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  // Get current category configuration
  const categoryConfig = getCategoryConfig(formData.category);
  const categoryFields = getFieldsForCategory(formData.category);
  const brandOptions = getBrandsForCategory(formData.category);

  // Handle form field changes
  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    
    if (name === 'category') {
      // When category changes, reset specifications and brand
      setFormData(prev => ({
        ...prev,
        category: value,
        brand: '',
        specifications: {}
      }));
    } else if (name.startsWith('spec_')) {
      // Handle specification fields
      const specName = name.replace('spec_', '');
      setFormData(prev => ({
        ...prev,
        specifications: {
          ...prev.specifications,
          [specName]: value
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: type === 'checkbox' ? checked : value
      }));
    }
  };

  // Generate SKU automatically
  const generateSKU = () => {
    const brand = formData.brand.toUpperCase().replace(/\s+/g, '');
    const model = formData.model.toUpperCase().replace(/\s+/g, '-');
    const category = formData.category.toUpperCase();
    const timestamp = Date.now().toString().slice(-4);
    
    return `${brand}-${model}-${category}-${timestamp}`;
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    setSuccess('');

    try {
      // Auto-generate SKU if not provided
      const finalFormData = {
        ...formData,
        sku: formData.sku || generateSKU(),
        // Convert specifications object to array format expected by backend
        specifications: Object.entries(formData.specifications).map(([name, value]) => ({
          name,
          value,
          category: 'general',
          unit: '',
          is_key_spec: true
        }))
      };

      let response;
      if (isEditMode) {
        response = await updateProduct(product._id, finalFormData);
      } else {
        response = await createProduct(finalFormData);
      }

      setSuccess(`Product ${isEditMode ? 'updated' : 'created'} successfully!`);
      
      // Redirect after success
      setTimeout(() => {
        navigate('/admin/products');
      }, 2000);

    } catch (err) {
      console.error('Error saving product:', err);
      setError(err.response?.data?.message || `Failed to ${isEditMode ? 'update' : 'create'} product`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6 bg-white rounded-lg shadow-lg">
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          {categoryConfig.icon} {isEditMode ? 'Edit' : 'Add New'} {categoryConfig.label}
        </h2>
        <p className="text-gray-600">
          {isEditMode ? 'Update product information' : 'Add a new product to your inventory'}
        </p>
      </div>

      {error && <Alert type="error" message={error} className="mb-4" />}
      {success && <Alert type="success" message={success} className="mb-4" />}

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Basic Product Information */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Basic Information</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField
              label="Product Name"
              name="name"
              type="text"
              value={formData.name}
              onChange={handleChange}
              placeholder="e.g., iPhone 15 Pro"
              required
            />

            <FormField
              label="Category"
              name="category"
              type="select"
              value={formData.category}
              onChange={handleChange}
              options={getCategoryOptions()}
              required
            />

            <FormField
              label="Brand"
              name="brand"
              type="select"
              value={formData.brand}
              onChange={handleChange}
              options={brandOptions}
              placeholder="Select brand"
              required
            />

            <FormField
              label="Model"
              name="model"
              type="text"
              value={formData.model}
              onChange={handleChange}
              placeholder="e.g., 15 Pro Max"
              required
            />

            <FormField
              label="SKU"
              name="sku"
              type="text"
              value={formData.sku}
              onChange={handleChange}
              placeholder="Auto-generated if empty"
            />

            <FormField
              label="Subcategory"
              name="subcategory"
              type="text"
              value={formData.subcategory}
              onChange={handleChange}
              placeholder="Optional subcategory"
            />
          </div>
        </div>

        {/* Category-Specific Specifications */}
        {categoryFields.length > 0 && (
          <div className="bg-blue-50 p-4 rounded-lg">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              {categoryConfig.label} Specifications
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {categoryFields.map((field) => (
                <FormField
                  key={field.name}
                  label={field.label}
                  name={`spec_${field.name}`}
                  type={field.type}
                  value={formData.specifications[field.name] || ''}
                  onChange={handleChange}
                  options={field.options?.map(opt => ({ value: opt, label: opt }))}
                  placeholder={field.placeholder}
                  required={field.required}
                />
              ))}
            </div>
          </div>
        )}

        {/* Pricing & Inventory */}
        <div className="bg-green-50 p-4 rounded-lg">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Pricing & Inventory</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField
              label="Selling Price (TZS)"
              name="price"
              type="number"
              value={formData.price}
              onChange={handleChange}
              placeholder="0"
              required
            />

            <FormField
              label="Cost Price (TZS)"
              name="cost_price"
              type="number"
              value={formData.cost_price}
              onChange={handleChange}
              placeholder="0"
            />

            <FormField
              label="Stock Quantity"
              name="stock_quantity"
              type="number"
              value={formData.stock_quantity}
              onChange={handleChange}
              placeholder="0"
              required
            />

            <FormField
              label="Low Stock Threshold"
              name="low_stock_threshold"
              type="number"
              value={formData.low_stock_threshold}
              onChange={handleChange}
              placeholder="5"
            />
          </div>
        </div>

        {/* Description */}
        <div className="bg-yellow-50 p-4 rounded-lg">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Description</h3>
          <div className="space-y-4">
            <FormField
              label="Short Description"
              name="short_description"
              type="text"
              value={formData.short_description}
              onChange={handleChange}
              placeholder="Brief product description"
            />

            <FormField
              label="Full Description"
              name="description"
              type="textarea"
              value={formData.description}
              onChange={handleChange}
              placeholder="Detailed product description"
              rows={4}
            />
          </div>
        </div>

        {/* Status & Settings */}
        <div className="bg-purple-50 p-4 rounded-lg">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Status & Settings</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField
              label="Status"
              name="status"
              type="select"
              value={formData.status}
              onChange={handleChange}
              options={[
                { value: 'active', label: 'Active' },
                { value: 'inactive', label: 'Inactive' },
                { value: 'draft', label: 'Draft' }
              ]}
              required
            />

            <div className="flex items-center">
              <input
                type="checkbox"
                id="is_featured"
                name="is_featured"
                checked={formData.is_featured}
                onChange={handleChange}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="is_featured" className="ml-2 block text-sm text-gray-900">
                Featured Product
              </label>
            </div>
          </div>
        </div>

        {/* Form Actions */}
        <div className="flex justify-end space-x-4 pt-6 border-t">
          <Button
            type="button"
            variant="secondary"
            onClick={() => navigate('/admin/products')}
            disabled={loading}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            variant="primary"
            disabled={loading}
          >
            {loading ? 'Saving...' : (isEditMode ? 'Update Product' : 'Create Product')}
          </Button>
        </div>
      </form>
    </div>
  );
};

export default DynamicProductForm;
