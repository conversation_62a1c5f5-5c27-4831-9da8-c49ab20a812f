import React, { useState, useEffect } from 'react';
import { get, post, put } from '../../api/unifiedApiClient';
import { formatPrice } from '../../utils/priceFormatter';
import { getCategoryConfig } from '../../config/productCategories';
import LoadingState from '../ui/LoadingState';
import Alert from '../ui/Alert';
import Button from '../ui/Button';
import FormField from '../ui/FormField';
import Modal from '../ui/Modal';

/**
 * Serial Number Manager Component
 * Comprehensive serial number tracking and management
 */
const SerialNumberManager = () => {
  const [serialNumbers, setSerialNumbers] = useState([]);
  const [products, setProducts] = useState([]);
  const [suppliers, setSuppliers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [filters, setFilters] = useState({
    product: '',
    status: 'available',
    condition: '',
    supplier: ''
  });
  const [showAddModal, setShowAddModal] = useState(false);
  const [newSerial, setNewSerial] = useState({
    product: '',
    serialNumber: '',
    imei: '',
    macAddress: '',
    condition: 'new',
    supplier: '',
    purchaseDate: '',
    purchasePrice: '',
    warrantyStartDate: '',
    location: {
      warehouse: 'main',
      shelf: '',
      bin: ''
    },
    notes: ''
  });

  useEffect(() => {
    fetchData();
  }, [filters]);

  const fetchData = async () => {
    try {
      setLoading(true);
      setError('');

      const [serialResponse, productsResponse, suppliersResponse] = await Promise.all([
        safeApiRequest(`/api/admin/dashboard/serial-numbers?${new URLSearchParams(filters)}`),
        safeApiRequest('/api/products?limit=1000'),
        safeApiRequest('/api/admin/dashboard/suppliers?status=active')
      ]);

      setSerialNumbers(serialResponse.data?.serialNumbers || []);
      setProducts(productsResponse.data?.data || []);
      setSuppliers(suppliersResponse.data?.suppliers || []);
    } catch (err) {
      setError('Failed to load serial number data');
      console.error('Serial number fetch error:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleAddSerial = async () => {
    try {
      setLoading(true);
      
      const response = await safeApiRequest('/api/admin/dashboard/serial-numbers', {
        method: 'POST',
        data: newSerial
      });

      setSerialNumbers(prev => [response.data, ...prev]);
      setShowAddModal(false);
      setNewSerial({
        product: '',
        serialNumber: '',
        imei: '',
        macAddress: '',
        condition: 'new',
        supplier: '',
        purchaseDate: '',
        purchasePrice: '',
        warrantyStartDate: '',
        location: {
          warehouse: 'main',
          shelf: '',
          bin: ''
        },
        notes: ''
      });
    } catch (err) {
      setError('Failed to add serial number');
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'available':
        return 'bg-green-100 text-green-800';
      case 'sold':
        return 'bg-blue-100 text-blue-800';
      case 'reserved':
        return 'bg-yellow-100 text-yellow-800';
      case 'defective':
        return 'bg-red-100 text-red-800';
      case 'returned':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getConditionColor = (condition) => {
    switch (condition) {
      case 'new':
        return 'bg-green-100 text-green-800';
      case 'open_box':
        return 'bg-blue-100 text-blue-800';
      case 'refurbished':
        return 'bg-yellow-100 text-yellow-800';
      case 'used_excellent':
        return 'bg-orange-100 text-orange-800';
      case 'defective':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading && serialNumbers.length === 0) {
    return <LoadingState text="Loading serial numbers..." />;
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold text-gray-900 flex items-center">
            <i className="fas fa-barcode mr-2 text-blue-600"></i>
            Serial Number Management
          </h2>
          <Button
            onClick={() => setShowAddModal(true)}
            className="bg-blue-600 hover:bg-blue-700"
          >
            <i className="fas fa-plus mr-2"></i>
            Add Serial Number
          </Button>
        </div>

        {/* Filters */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <FormField
            label="Product"
            name="product"
            type="select"
            value={filters.product}
            onChange={(e) => setFilters({ ...filters, product: e.target.value })}
            options={[
              { value: '', label: 'All Products' },
              ...products.map(p => ({ value: p._id, label: `${p.name} (${p.brand})` }))
            ]}
          />
          
          <FormField
            label="Status"
            name="status"
            type="select"
            value={filters.status}
            onChange={(e) => setFilters({ ...filters, status: e.target.value })}
            options={[
              { value: 'all', label: 'All Status' },
              { value: 'available', label: 'Available' },
              { value: 'sold', label: 'Sold' },
              { value: 'reserved', label: 'Reserved' },
              { value: 'defective', label: 'Defective' },
              { value: 'returned', label: 'Returned' }
            ]}
          />
          
          <FormField
            label="Condition"
            name="condition"
            type="select"
            value={filters.condition}
            onChange={(e) => setFilters({ ...filters, condition: e.target.value })}
            options={[
              { value: '', label: 'All Conditions' },
              { value: 'new', label: 'New' },
              { value: 'open_box', label: 'Open Box' },
              { value: 'refurbished', label: 'Refurbished' },
              { value: 'used_excellent', label: 'Used - Excellent' },
              { value: 'used_good', label: 'Used - Good' },
              { value: 'defective', label: 'Defective' }
            ]}
          />
          
          <FormField
            label="Supplier"
            name="supplier"
            type="select"
            value={filters.supplier}
            onChange={(e) => setFilters({ ...filters, supplier: e.target.value })}
            options={[
              { value: '', label: 'All Suppliers' },
              ...suppliers.map(s => ({ value: s._id, label: s.name }))
            ]}
          />
        </div>
      </div>

      {error && <Alert type="error" message={error} />}

      {/* Serial Numbers Table */}
      <div className="bg-white rounded-lg shadow-md overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">
            Serial Numbers ({serialNumbers.length})
          </h3>
        </div>
        
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Product
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Serial/IMEI
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Condition
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Location
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Warranty
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {serialNumbers.map((serial) => {
                const categoryConfig = getCategoryConfig(serial.product?.category);
                const warrantyDays = serial.warrantyEndDate 
                  ? Math.ceil((new Date(serial.warrantyEndDate) - new Date()) / (1000 * 60 * 60 * 24))
                  : 0;
                
                return (
                  <tr key={serial._id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <span className="text-2xl mr-3">{categoryConfig.icon}</span>
                        <div>
                          <div className="text-sm font-medium text-gray-900">
                            {serial.product?.name}
                          </div>
                          <div className="text-sm text-gray-500">
                            {serial.product?.brand} • {serial.product?.model}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">
                        {serial.serialNumber}
                      </div>
                      {serial.imei && (
                        <div className="text-sm text-gray-500">
                          IMEI: {serial.imei}
                        </div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(serial.status)}`}>
                        {serial.status.replace('_', ' ')}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getConditionColor(serial.condition)}`}>
                        {serial.condition.replace('_', ' ')}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      <div>{serial.location?.warehouse || 'N/A'}</div>
                      {serial.location?.shelf && (
                        <div className="text-xs text-gray-500">
                          Shelf: {serial.location.shelf}
                        </div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {warrantyDays > 0 ? (
                        <div className={`${warrantyDays <= 30 ? 'text-red-600' : 'text-green-600'}`}>
                          {warrantyDays} days left
                        </div>
                      ) : (
                        <div className="text-red-600">Expired</div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex space-x-2">
                        <Button
                          variant="link"
                          className="text-blue-600 hover:text-blue-900"
                        >
                          <i className="fas fa-edit"></i>
                        </Button>
                        <Button
                          variant="link"
                          className="text-green-600 hover:text-green-900"
                        >
                          <i className="fas fa-eye"></i>
                        </Button>
                      </div>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </div>

      {/* Add Serial Number Modal */}
      <Modal
        isOpen={showAddModal}
        onClose={() => setShowAddModal(false)}
        title="Add New Serial Number"
        size="lg"
      >
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField
              label="Product"
              name="product"
              type="select"
              value={newSerial.product}
              onChange={(e) => setNewSerial({ ...newSerial, product: e.target.value })}
              options={[
                { value: '', label: 'Select Product' },
                ...products.map(p => ({ value: p._id, label: `${p.name} (${p.brand})` }))
              ]}
              required
            />
            
            <FormField
              label="Serial Number"
              name="serialNumber"
              type="text"
              value={newSerial.serialNumber}
              onChange={(e) => setNewSerial({ ...newSerial, serialNumber: e.target.value })}
              placeholder="Enter serial number"
              required
            />
            
            <FormField
              label="IMEI (for mobile devices)"
              name="imei"
              type="text"
              value={newSerial.imei}
              onChange={(e) => setNewSerial({ ...newSerial, imei: e.target.value })}
              placeholder="15-digit IMEI"
              maxLength="15"
            />
            
            <FormField
              label="Condition"
              name="condition"
              type="select"
              value={newSerial.condition}
              onChange={(e) => setNewSerial({ ...newSerial, condition: e.target.value })}
              options={[
                { value: 'new', label: 'New' },
                { value: 'open_box', label: 'Open Box' },
                { value: 'refurbished', label: 'Refurbished' },
                { value: 'used_excellent', label: 'Used - Excellent' },
                { value: 'used_good', label: 'Used - Good' },
                { value: 'defective', label: 'Defective' }
              ]}
            />
            
            <FormField
              label="Supplier"
              name="supplier"
              type="select"
              value={newSerial.supplier}
              onChange={(e) => setNewSerial({ ...newSerial, supplier: e.target.value })}
              options={[
                { value: '', label: 'Select Supplier' },
                ...suppliers.map(s => ({ value: s._id, label: s.name }))
              ]}
            />
            
            <FormField
              label="Purchase Price (TZS)"
              name="purchasePrice"
              type="number"
              value={newSerial.purchasePrice}
              onChange={(e) => setNewSerial({ ...newSerial, purchasePrice: e.target.value })}
              placeholder="0"
              min="0"
            />
          </div>
          
          <FormField
            label="Notes"
            name="notes"
            type="textarea"
            value={newSerial.notes}
            onChange={(e) => setNewSerial({ ...newSerial, notes: e.target.value })}
            placeholder="Additional notes..."
            rows={3}
          />
          
          <div className="flex justify-end space-x-3 pt-4 border-t">
            <Button
              variant="secondary"
              onClick={() => setShowAddModal(false)}
            >
              Cancel
            </Button>
            <Button
              onClick={handleAddSerial}
              disabled={!newSerial.product || !newSerial.serialNumber}
            >
              Add Serial Number
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default SerialNumberManager;
