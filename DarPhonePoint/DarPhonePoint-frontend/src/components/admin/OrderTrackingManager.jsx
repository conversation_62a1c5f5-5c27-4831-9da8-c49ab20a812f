import React, { useState, useEffect } from 'react';
import { safeApiRequest } from '../../api/apiClient';
import Button from '../ui/Button';
import Alert from '../ui/Alert';
import LoadingState from '../ui/LoadingState';

const OrderTrackingManager = ({ orderId, currentTracking, onUpdate }) => {
  const [tracking, setTracking] = useState(currentTracking || {});
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  // Tanzania courier services
  const courierServices = [
    { id: 'dhl_tanzania', name: 'DHL Tanzania', website: 'https://www.dhl.com/tz-en' },
    { id: 'posta_tanzania', name: 'Posta Tanzania', website: 'https://www.posta.co.tz' },
    { id: 'fedex_tanzania', name: 'FedEx Tanzania', website: 'https://www.fedex.com/tz' },
    { id: 'ups_tanzania', name: 'UPS Tanzania', website: 'https://www.ups.com/tz' },
    { id: 'local_courier', name: 'Local Courier Service', website: '' },
    { id: 'self_delivery', name: 'Self Delivery', website: '' }
  ];

  // Tracking statuses
  const trackingStatuses = [
    { id: 'pending', name: 'Order Pending', description: 'Order received and being prepared' },
    { id: 'processing', name: 'Processing', description: 'Order is being processed and packed' },
    { id: 'ready_for_pickup', name: 'Ready for Pickup', description: 'Order ready for courier pickup' },
    { id: 'picked_up', name: 'Picked Up', description: 'Package picked up by courier' },
    { id: 'in_transit', name: 'In Transit', description: 'Package is on the way to destination' },
    { id: 'out_for_delivery', name: 'Out for Delivery', description: 'Package is out for final delivery' },
    { id: 'delivered', name: 'Delivered', description: 'Package has been delivered' },
    { id: 'failed_delivery', name: 'Failed Delivery', description: 'Delivery attempt failed' },
    { id: 'returned', name: 'Returned', description: 'Package returned to sender' }
  ];

  useEffect(() => {
    if (orderId && !currentTracking) {
      fetchTrackingInfo();
    }
  }, [orderId]);

  const fetchTrackingInfo = async () => {
    try {
      setLoading(true);
      const response = await safeApiRequest({
        method: 'GET',
        url: `/admin/orders/${orderId}/tracking`
      });
      
      setTracking(response.data?.tracking || {});
    } catch (err) {
      console.error('Error fetching tracking info:', err);
      setError('Failed to load tracking information');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field, value) => {
    setTracking(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const addTrackingUpdate = () => {
    const newUpdate = {
      id: Date.now().toString(),
      status: 'processing',
      message: '',
      location: '',
      timestamp: new Date().toISOString(),
      is_new: true
    };

    setTracking(prev => ({
      ...prev,
      updates: [...(prev.updates || []), newUpdate]
    }));
  };

  const updateTrackingUpdate = (updateId, field, value) => {
    setTracking(prev => ({
      ...prev,
      updates: prev.updates.map(update =>
        update.id === updateId ? { ...update, [field]: value } : update
      )
    }));
  };

  const removeTrackingUpdate = (updateId) => {
    setTracking(prev => ({
      ...prev,
      updates: prev.updates.filter(update => update.id !== updateId)
    }));
  };

  const saveTracking = async () => {
    try {
      setSaving(true);
      setError('');
      
      const trackingData = {
        ...tracking,
        updated_at: new Date().toISOString(),
        updates: tracking.updates?.map(update => ({
          ...update,
          timestamp: update.timestamp || new Date().toISOString()
        })) || []
      };

      await safeApiRequest({
        method: 'PUT',
        url: `/admin/orders/${orderId}/tracking`,
        data: { tracking: trackingData }
      });

      setSuccess('Tracking information updated successfully');
      if (onUpdate) {
        onUpdate(trackingData);
      }
    } catch (err) {
      console.error('Error saving tracking:', err);
      setError('Failed to save tracking information');
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return <LoadingState message="Loading tracking information..." />;
  }

  return (
    <div className="space-y-6">
      {error && (
        <Alert type="error" message={error} onClose={() => setError('')} />
      )}
      
      {success && (
        <Alert type="success" message={success} onClose={() => setSuccess('')} />
      )}

      {/* Basic Tracking Info */}
      <div className="bg-white rounded-lg border p-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">
          <i className="fas fa-shipping-fast text-blue-600 mr-2"></i>
          Shipping Information
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Courier Service
            </label>
            <select
              value={tracking.courier_service || ''}
              onChange={(e) => handleInputChange('courier_service', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">Select Courier Service</option>
              {courierServices.map(courier => (
                <option key={courier.id} value={courier.id}>
                  {courier.name}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Tracking Number
            </label>
            <input
              type="text"
              value={tracking.tracking_number || ''}
              onChange={(e) => handleInputChange('tracking_number', e.target.value)}
              placeholder="Enter tracking number"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Current Status
            </label>
            <select
              value={tracking.current_status || 'pending'}
              onChange={(e) => handleInputChange('current_status', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              {trackingStatuses.map(status => (
                <option key={status.id} value={status.id}>
                  {status.name}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Estimated Delivery
            </label>
            <input
              type="date"
              value={tracking.estimated_delivery ? tracking.estimated_delivery.split('T')[0] : ''}
              onChange={(e) => handleInputChange('estimated_delivery', e.target.value ? new Date(e.target.value).toISOString() : '')}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>

        <div className="mt-4">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Tracking URL (Optional)
          </label>
          <input
            type="url"
            value={tracking.tracking_url || ''}
            onChange={(e) => handleInputChange('tracking_url', e.target.value)}
            placeholder="https://courier-website.com/track/123456"
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
      </div>

      {/* Tracking Updates */}
      <div className="bg-white rounded-lg border p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-800">
            <i className="fas fa-route text-blue-600 mr-2"></i>
            Tracking Updates
          </h3>
          <Button onClick={addTrackingUpdate} size="sm">
            <i className="fas fa-plus mr-2"></i>
            Add Update
          </Button>
        </div>

        <div className="space-y-4">
          {tracking.updates?.map((update, index) => (
            <div key={update.id} className="border border-gray-200 rounded-lg p-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Status
                  </label>
                  <select
                    value={update.status}
                    onChange={(e) => updateTrackingUpdate(update.id, 'status', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    {trackingStatuses.map(status => (
                      <option key={status.id} value={status.id}>
                        {status.name}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Location
                  </label>
                  <input
                    type="text"
                    value={update.location || ''}
                    onChange={(e) => updateTrackingUpdate(update.id, 'location', e.target.value)}
                    placeholder="e.g., Dar es Salaam Sorting Center"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Date & Time
                  </label>
                  <input
                    type="datetime-local"
                    value={update.timestamp ? new Date(update.timestamp).toISOString().slice(0, 16) : ''}
                    onChange={(e) => updateTrackingUpdate(update.id, 'timestamp', e.target.value ? new Date(e.target.value).toISOString() : '')}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </div>

              <div className="mt-3">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Message
                </label>
                <div className="flex space-x-2">
                  <textarea
                    value={update.message || ''}
                    onChange={(e) => updateTrackingUpdate(update.id, 'message', e.target.value)}
                    placeholder="Describe what happened at this stage..."
                    rows={2}
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                  <Button
                    onClick={() => removeTrackingUpdate(update.id)}
                    variant="outline"
                    size="sm"
                    className="text-red-600 hover:text-red-700"
                  >
                    <i className="fas fa-trash"></i>
                  </Button>
                </div>
              </div>
            </div>
          ))}

          {(!tracking.updates || tracking.updates.length === 0) && (
            <div className="text-center py-8 text-gray-500">
              <i className="fas fa-route text-4xl mb-4"></i>
              <p>No tracking updates yet. Add the first update to start tracking.</p>
            </div>
          )}
        </div>
      </div>

      {/* Save Button */}
      <div className="flex justify-end">
        <Button
          onClick={saveTracking}
          disabled={saving}
          className="px-6"
        >
          {saving ? (
            <>
              <i className="fas fa-spinner fa-spin mr-2"></i>
              Saving...
            </>
          ) : (
            <>
              <i className="fas fa-save mr-2"></i>
              Save Tracking Info
            </>
          )}
        </Button>
      </div>
    </div>
  );
};

export default OrderTrackingManager;
