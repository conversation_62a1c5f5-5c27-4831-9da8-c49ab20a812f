import React, { useEffect, useRef } from 'react';
import Chart from 'chart.js/auto';

/**
 * Bar<PERSON><PERSON> Component
 * Renders a bar chart using Chart.js
 * 
 * @param {Array} data - Array of data points
 * @param {Array} labels - Array of labels for data points
 * @param {String} title - Chart title
 * @param {Array} colors - Array of colors for bars
 * @param {Number} height - Chart height in pixels
 * @param {Boolean} horizontal - Whether to display bars horizontally
 * @param {Boolean} stacked - Whether to stack bars
 * @param {Boolean} showGrid - Whether to show grid lines
 * @param {Object} options - Additional Chart.js options
 */
const BarChart = ({
  data,
  labels,
  title,
  colors = ['#8B5CF6', '#3B82F6', '#10B981', '#F59E0B', '#EF4444'],
  height = 300,
  horizontal = false,
  stacked = false,
  showGrid = true,
  options = {}
}) => {
  const chartRef = useRef(null);
  const chartInstance = useRef(null);

  useEffect(() => {
    // Destroy existing chart if it exists
    if (chartInstance.current) {
      chartInstance.current.destroy();
    }

    // Return early if no data
    if (!data || !labels || data.length === 0 || labels.length === 0) {
      return;
    }

    const ctx = chartRef.current.getContext('2d');

    // Prepare datasets
    let datasets = [];
    
    // If data is a 2D array, create multiple datasets
    if (Array.isArray(data[0])) {
      datasets = data.map((dataSet, index) => ({
        label: options.datasetLabels?.[index] || `Dataset ${index + 1}`,
        data: dataSet,
        backgroundColor: colors[index % colors.length],
        borderColor: colors[index % colors.length],
        borderWidth: 1
      }));
    } else {
      // Single dataset
      datasets = [{
        label: options.datasetLabels?.[0] || title,
        data: data,
        backgroundColor: Array.isArray(colors) ? colors : [colors],
        borderColor: Array.isArray(colors) ? colors : [colors],
        borderWidth: 1
      }];
    }

    // Create chart
    chartInstance.current = new Chart(ctx, {
      type: horizontal ? 'horizontalBar' : 'bar',
      data: {
        labels: labels,
        datasets: datasets
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            display: datasets.length > 1 || options.showLegend,
            position: 'top',
          },
          title: {
            display: !!title,
            text: title
          },
          tooltip: {
            mode: 'index',
            intersect: false,
          }
        },
        scales: {
          x: {
            stacked: stacked,
            grid: {
              display: showGrid
            },
            title: {
              display: !!options.xAxisTitle,
              text: options.xAxisTitle
            }
          },
          y: {
            stacked: stacked,
            grid: {
              display: showGrid
            },
            title: {
              display: !!options.yAxisTitle,
              text: options.yAxisTitle
            },
            beginAtZero: true
          }
        },
        ...options
      }
    });

    // Cleanup function
    return () => {
      if (chartInstance.current) {
        chartInstance.current.destroy();
      }
    };
  }, [data, labels, title, colors, height, horizontal, stacked, showGrid, options]);

  return (
    <div style={{ height: `${height}px`, width: '100%' }}>
      <canvas ref={chartRef}></canvas>
    </div>
  );
};

export default BarChart;
