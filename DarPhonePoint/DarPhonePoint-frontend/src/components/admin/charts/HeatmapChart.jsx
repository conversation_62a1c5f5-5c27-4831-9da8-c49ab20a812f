import React, { useEffect, useRef } from 'react';

/**
 * HeatmapChart Component
 * Renders a heatmap chart using SVG
 *
 * @param {Array} data - 2D array of data points (24 hours x 7 days)
 * @param {Array} xLabels - Array of labels for x-axis (days)
 * @param {Array} yLabels - Array of labels for y-axis (hours)
 * @param {String} title - Chart title
 * @param {String} colorScale - Color scale for heatmap ('purple', 'blue', 'green', 'red')
 * @param {Number} height - Chart height in pixels
 * @param {Object} options - Additional options
 */
const HeatmapChart = ({
  data,
  xLabels,
  yLabels,
  title,
  colorScale = 'purple',
  height = 400,
  options = {}
}) => {
  const containerRef = useRef(null);

  // Define color scales
  const colorScales = {
    purple: ['#F8FAFC', '#F1F5F9', '#E2E8F0', '#CBD5E1', '#94A3B8', '#64748B', '#475569', '#334155', '#1E293B'],
    blue: ['#F0F9FF', '#E0F2FE', '#BAE6FD', '#7DD3FC', '#38BDF8', '#0EA5E9', '#0284C7', '#0369A1', '#075985'],
    green: ['#F0FDF4', '#DCFCE7', '#BBF7D0', '#86EFAC', '#4ADE80', '#22C55E', '#16A34A', '#15803D', '#166534'],
    red: ['#FEF2F2', '#FEE2E2', '#FECACA', '#FCA5A5', '#F87171', '#EF4444', '#DC2626', '#B91C1C', '#991B1B']
  };

  // Calculate dimensions and colors
  const getHeatmapData = () => {
    if (!data || !xLabels || !yLabels || data.length === 0) {
      return { processedData: [], minValue: 0, maxValue: 0 };
    }

    // Find min and max values for color scaling
    let minValue = Number.MAX_VALUE;
    let maxValue = Number.MIN_VALUE;

    data.forEach(row => {
      row.forEach(value => {
        if (value < minValue) minValue = value;
        if (value > maxValue) maxValue = value;
      });
    });

    // If all values are 0, set a reasonable max for color scaling
    if (maxValue === 0) {
      maxValue = 1;
    }

    const processedData = data.map((row, yIndex) =>
      row.map((value, xIndex) => ({
        x: xIndex,
        y: yIndex,
        value,
        day: xLabels[xIndex],
        hour: yLabels[yIndex]
      }))
    );

    return { processedData, minValue, maxValue };
  };

  const getColor = (value, minValue, maxValue) => {
    const colorSet = colorScales[colorScale] || colorScales.purple;

    if (value === 0) {
      return colorSet[0]; // Lightest color for zero values
    }

    const normalizedValue = (value - minValue) / (maxValue - minValue);
    const colorIndex = Math.min(Math.floor(normalizedValue * (colorSet.length - 1)) + 1, colorSet.length - 1);
    return colorSet[colorIndex];
  };

  const { processedData, minValue, maxValue } = getHeatmapData();

  // SVG dimensions
  const margin = { top: 40, right: 40, bottom: 60, left: 80 };
  const cellWidth = 40;
  const cellHeight = 16;
  const svgWidth = margin.left + margin.right + (xLabels?.length || 7) * cellWidth;
  const svgHeight = height;
  const chartHeight = svgHeight - margin.top - margin.bottom;

  return (
    <div ref={containerRef} style={{ width: '100%', height: `${height}px` }}>
      {!data || data.length === 0 ? (
        <div className="flex items-center justify-center h-full text-gray-500">
          No data available
        </div>
      ) : (
        <svg width="100%" height="100%" viewBox={`0 0 ${svgWidth} ${svgHeight}`}>
          {/* Title */}
          {title && (
            <text
              x={svgWidth / 2}
              y={20}
              textAnchor="middle"
              className="text-sm font-medium fill-gray-700"
            >
              {title}
            </text>
          )}

          {/* Y-axis labels (hours) */}
          {yLabels?.map((hour, index) => (
            <text
              key={`hour-${index}`}
              x={margin.left - 10}
              y={margin.top + index * cellHeight + cellHeight / 2}
              textAnchor="end"
              dominantBaseline="middle"
              className="text-xs fill-gray-600"
            >
              {hour}
            </text>
          ))}

          {/* X-axis labels (days) */}
          {xLabels?.map((day, index) => (
            <text
              key={`day-${index}`}
              x={margin.left + index * cellWidth + cellWidth / 2}
              y={svgHeight - margin.bottom + 20}
              textAnchor="middle"
              dominantBaseline="middle"
              className="text-xs fill-gray-600"
            >
              {day}
            </text>
          ))}

          {/* Heatmap cells */}
          {processedData.map((row, yIndex) =>
            row.map((cell, xIndex) => (
              <g key={`cell-${yIndex}-${xIndex}`}>
                <rect
                  x={margin.left + xIndex * cellWidth}
                  y={margin.top + yIndex * cellHeight}
                  width={cellWidth - 1}
                  height={cellHeight - 1}
                  fill={getColor(cell.value, minValue, maxValue)}
                  stroke="#fff"
                  strokeWidth="1"
                  className="hover:stroke-gray-400 hover:stroke-2 cursor-pointer"
                >
                  <title>
                    {`${cell.hour} on ${cell.day}: ${cell.value} events`}
                  </title>
                </rect>
              </g>
            ))
          )}

          {/* Axis titles */}
          {options.xAxisTitle && (
            <text
              x={svgWidth / 2}
              y={svgHeight - 10}
              textAnchor="middle"
              className="text-sm fill-gray-600"
            >
              {options.xAxisTitle}
            </text>
          )}

          {options.yAxisTitle && (
            <text
              x={15}
              y={svgHeight / 2}
              textAnchor="middle"
              transform={`rotate(-90, 15, ${svgHeight / 2})`}
              className="text-sm fill-gray-600"
            >
              {options.yAxisTitle}
            </text>
          )}

          {/* Legend */}
          <g transform={`translate(${svgWidth - margin.right - 100}, ${margin.top})`}>
            <text x="0" y="-5" className="text-xs fill-gray-600">Activity Level</text>
            {colorScales[colorScale].slice(0, 5).map((color, index) => (
              <g key={`legend-${index}`} transform={`translate(${index * 15}, 0)`}>
                <rect
                  width="12"
                  height="12"
                  fill={color}
                  stroke="#fff"
                  strokeWidth="1"
                />
                {index === 0 && (
                  <text x="0" y="25" className="text-xs fill-gray-500">Low</text>
                )}
                {index === 4 && (
                  <text x="-5" y="25" className="text-xs fill-gray-500">High</text>
                )}
              </g>
            ))}
          </g>
        </svg>
      )}
    </div>
  );
};

export default HeatmapChart;
