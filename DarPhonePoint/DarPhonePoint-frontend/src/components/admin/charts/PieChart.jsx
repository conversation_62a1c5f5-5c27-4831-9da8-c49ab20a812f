import React from 'react';
import PropTypes from 'prop-types';

/**
 * Enhanced SVG pie chart component with hover effects and animations
 *
 * @param {Object} props - Component props
 * @param {Array} props.data - Array of numeric values to plot
 * @param {Array} props.labels - Array of labels for each segment
 * @param {Array} props.colors - Array of colors for each segment
 * @param {string} props.title - Chart title
 * @param {boolean} props.showPercentage - Whether to show percentage values
 * @param {boolean} props.showLegend - Whether to show the legend
 * @param {number} props.size - Size of the chart in pixels
 * @param {number} props.innerRadius - Inner radius for donut chart (0 for pie chart)
 */
const PieChart = ({
  data,
  labels,
  colors = ['#8B5CF6', '#3B82F6', '#10B981', '#F59E0B', '#EF4444'],
  title = '',
  showPercentage = true,
  showLegend = true,
  size = 200,
  innerRadius = 0
}) => {
  // Handle empty data
  if (!data || data.length === 0) {
    return (
      <div className="flex flex-col h-full justify-center items-center bg-gray-50 rounded-lg p-4">
        <p className="text-gray-500 text-sm">No data available</p>
      </div>
    );
  }

  const total = data.reduce((sum, value) => sum + value, 0);
  let startAngle = 0;

  // Calculate percentages
  const percentages = data.map(value => Math.round((value / total) * 100));

  return (
    <div className="h-full">
      {title && <h3 className="text-sm font-medium text-gray-500 mb-2">{title}</h3>}
      <div className="flex items-center">
        <div className="relative" style={{ width: `${size}px`, height: `${size}px` }}>
          <svg viewBox="0 0 100 100" className="w-full h-full">
            {/* Segments */}
            {data.map((value, index) => {
              const percentage = value / total;
              const endAngle = startAngle + percentage * 360;

              // Calculate the SVG arc path
              const outerRadius = 50;
              const startRad = (startAngle * Math.PI) / 180;
              const endRad = (endAngle * Math.PI) / 180;

              const x1 = 50 + outerRadius * Math.cos(startRad);
              const y1 = 50 + outerRadius * Math.sin(startRad);

              const x2 = 50 + outerRadius * Math.cos(endRad);
              const y2 = 50 + outerRadius * Math.sin(endRad);

              const largeArcFlag = percentage > 0.5 ? 1 : 0;

              let pathData;

              if (innerRadius > 0) {
                // Donut chart path
                const innerR = innerRadius / 2; // Scale to viewBox
                const x3 = 50 + innerR * Math.cos(endRad);
                const y3 = 50 + innerR * Math.sin(endRad);
                const x4 = 50 + innerR * Math.cos(startRad);
                const y4 = 50 + innerR * Math.sin(startRad);

                pathData = [
                  `M ${x1} ${y1}`,
                  `A ${outerRadius} ${outerRadius} 0 ${largeArcFlag} 1 ${x2} ${y2}`,
                  `L ${x3} ${y3}`,
                  `A ${innerR} ${innerR} 0 ${largeArcFlag} 0 ${x4} ${y4}`,
                  `Z`
                ].join(' ');
              } else {
                // Regular pie chart path
                pathData = [
                  `M 50 50`,
                  `L ${x1} ${y1}`,
                  `A ${outerRadius} ${outerRadius} 0 ${largeArcFlag} 1 ${x2} ${y2}`,
                  `Z`
                ].join(' ');
              }

              startAngle = endAngle;

              return (
                <path
                  key={index}
                  d={pathData}
                  fill={colors[index % colors.length]}
                  stroke="#fff"
                  strokeWidth="1"
                  className="transition-opacity duration-200 hover:opacity-80"
                >
                  <title>{labels[index]}: {percentages[index]}%</title>
                </path>
              );
            })}

            {/* Percentage labels */}
            {showPercentage && data.length <= 5 && data.map((value, index) => {
              if (percentages[index] < 5) return null; // Don't show tiny segments

              const percentage = value / total;
              const halfAngle = startAngle + (percentage * 360) / 2;
              startAngle += percentage * 360;

              // Position the text in the middle of the segment
              const radius = innerRadius > 0 ? (50 + innerRadius / 4) : 30;
              const x = 50 + radius * Math.cos((halfAngle * Math.PI) / 180);
              const y = 50 + radius * Math.sin((halfAngle * Math.PI) / 180);

              return (
                <text
                  key={`text-${index}`}
                  x={x}
                  y={y}
                  textAnchor="middle"
                  dominantBaseline="middle"
                  fill="#fff"
                  fontSize="10"
                  fontWeight="bold"
                >
                  {percentages[index]}%
                </text>
              );
            })}
          </svg>
        </div>

        {/* Legend */}
        {showLegend && (
          <div className="ml-4 flex-1">
            {labels.map((label, index) => (
              <div key={index} className="flex items-center mb-1">
                <div
                  className="w-3 h-3 rounded-full mr-2"
                  style={{ backgroundColor: colors[index % colors.length] }}
                ></div>
                <span className="text-xs text-gray-600">
                  {label}: {percentages[index]}%
                </span>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

PieChart.propTypes = {
  data: PropTypes.arrayOf(PropTypes.number).isRequired,
  labels: PropTypes.arrayOf(PropTypes.string).isRequired,
  colors: PropTypes.arrayOf(PropTypes.string),
  title: PropTypes.string,
  showPercentage: PropTypes.bool,
  showLegend: PropTypes.bool,
  size: PropTypes.number,
  innerRadius: PropTypes.number
};



export default PieChart;
