import React from 'react';
import PropTypes from 'prop-types';

/**
 * LineChart component for displaying time series data
 *
 * @param {Object} props - Component props
 * @param {Array} props.data - Array of numeric data points
 * @param {Array} props.labels - Array of labels for the x-axis
 * @param {string} props.title - Chart title
 * @param {string} props.color - Line color (hex code)
 * @param {number} props.height - Chart height in pixels
 * @param {boolean} props.showGrid - Whether to show grid lines
 * @param {boolean} props.showArea - Whether to show area under the line
 * @param {boolean} props.showPoints - Whether to show data points
 */
const LineChart = ({
  data,
  labels,
  title,
  color = '#6366F1',
  height = 200,
  showGrid = false,
  showArea = true,
  showPoints = true
}) => {
  // Handle empty data
  if (!data || data.length === 0) {
    return (
      <div className="flex flex-col h-full justify-center items-center bg-gray-50 rounded-lg p-4">
        <p className="text-gray-500 text-sm">No data available</p>
      </div>
    );
  }

  // Ensure we have a non-zero range for the chart
  const maxData = Math.max(...data);
  const maxValue = maxData > 0 ? maxData * 1.1 : 10; // Add 10% padding or use 10 if all zeros
  const minValue = Math.min(0, ...data) * 1.1; // Include zero or go below if negative
  const width = 100;
  const chartHeight = height;

  // Generate points for the polyline
  const points = data.map((value, index) => {
    const x = (index / (data.length - 1)) * width;
    const y = chartHeight - ((value - minValue) / (maxValue - minValue)) * chartHeight;
    return `${x},${y}`;
  }).join(' ');

  // Generate area path if needed
  const areaPath = showArea ? `
    M 0,${chartHeight}
    L ${points}
    L ${width},${chartHeight}
    Z
  ` : '';

  // Generate grid lines
  const gridLines = showGrid ? (
    <>
      {[0.25, 0.5, 0.75].map((position, i) => (
        <line
          key={i}
          x1="0"
          y1={chartHeight * position}
          x2={width}
          y2={chartHeight * position}
          stroke="#E5E7EB"
          strokeWidth="0.5"
          strokeDasharray="2,2"
        />
      ))}
      {labels.map((_, i) => {
        if (i === 0 || i === labels.length - 1) return null;
        const x = (i / (labels.length - 1)) * width;
        return (
          <line
            key={i}
            x1={x}
            y1="0"
            x2={x}
            y2={chartHeight}
            stroke="#E5E7EB"
            strokeWidth="0.5"
            strokeDasharray="2,2"
          />
        );
      })}
    </>
  ) : null;

  // Generate data points
  const dataPoints = showPoints ? data.map((value, index) => {
    const x = (index / (data.length - 1)) * width;
    const y = chartHeight - ((value - minValue) / (maxValue - minValue)) * chartHeight;
    return (
      <circle
        key={index}
        cx={x}
        cy={y}
        r="1.5"
        fill="white"
        stroke={color}
        strokeWidth="1.5"
      />
    );
  }) : null;

  // Generate y-axis labels
  const yAxisLabels = [];
  const numYLabels = 5;
  for (let i = 0; i < numYLabels; i++) {
    const value = Math.round(maxValue * (1 - i / (numYLabels - 1)));
    yAxisLabels.push(value);
  }

  return (
    <div className="h-full">
      {title && <h3 className="text-sm font-medium text-gray-500 mb-1">{title}</h3>}
      <div className="flex">
        {/* Y-axis labels */}
        <div className="flex flex-col justify-between text-xs text-gray-400 pr-2">
          {yAxisLabels.map((label, index) => (
            <span key={index}>{label}</span>
          ))}
        </div>

        {/* Chart */}
        <div className="flex-1">
          <div className="relative" style={{ height: `${height}px` }}>
            <svg viewBox={`0 0 ${width} ${chartHeight}`} className="w-full h-full">
              {/* Grid lines */}
              {gridLines}

              {/* Horizontal grid lines for y-axis */}
              {yAxisLabels.map((_, index) => {
                const y = (index / (numYLabels - 1)) * chartHeight;
                return (
                  <line
                    key={`y-grid-${index}`}
                    x1="0"
                    y1={y}
                    x2={width}
                    y2={y}
                    stroke="#E5E7EB"
                    strokeWidth="0.5"
                    strokeDasharray="2,2"
                  />
                );
              })}

              {/* Area under the line */}
              {showArea && (
                <path
                  d={areaPath}
                  fill={color}
                  fillOpacity="0.1"
                />
              )}

              {/* Line */}
              <polyline
                points={points}
                fill="none"
                stroke={color}
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />

              {/* Data points */}
              {dataPoints}
            </svg>
          </div>

          {/* X-axis labels */}
          {labels && labels.length > 0 && (
            <div className="flex justify-between text-xs text-gray-400 mt-1">
              {labels.map((label, index) => (
                <span key={index}>{label}</span>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

LineChart.propTypes = {
  data: PropTypes.arrayOf(PropTypes.number).isRequired,
  labels: PropTypes.arrayOf(PropTypes.string).isRequired,
  title: PropTypes.string,
  color: PropTypes.string,
  height: PropTypes.number,
  showGrid: PropTypes.bool,
  showArea: PropTypes.bool,
  showPoints: PropTypes.bool
};

export default LineChart;
