import React, { useEffect, useRef } from 'react';
import Chart from 'chart.js/auto';

/**
 * AreaChart Component
 * Renders an area chart using Chart.js
 * 
 * @param {Array} data - Array of data points
 * @param {Array} labels - Array of labels for data points
 * @param {String} title - Chart title
 * @param {Array} colors - Array of colors for areas
 * @param {Number} height - Chart height in pixels
 * @param {Boolean} stacked - Whether to stack areas
 * @param {Boolean} showGrid - Whether to show grid lines
 * @param {Object} options - Additional Chart.js options
 */
const AreaChart = ({
  data,
  labels,
  title,
  colors = ['rgba(139, 92, 246, 0.5)', 'rgba(59, 130, 246, 0.5)', 'rgba(16, 185, 129, 0.5)'],
  height = 300,
  stacked = false,
  showGrid = true,
  options = {}
}) => {
  const chartRef = useRef(null);
  const chartInstance = useRef(null);

  useEffect(() => {
    // Destroy existing chart if it exists
    if (chartInstance.current) {
      chartInstance.current.destroy();
    }

    // Return early if no data
    if (!data || !labels || data.length === 0 || labels.length === 0) {
      return;
    }

    const ctx = chartRef.current.getContext('2d');

    // Prepare datasets
    let datasets = [];
    
    // If data is a 2D array, create multiple datasets
    if (Array.isArray(data[0])) {
      datasets = data.map((dataSet, index) => {
        const color = colors[index % colors.length];
        const borderColor = color.replace('0.5', '1');
        
        return {
          label: options.datasetLabels?.[index] || `Dataset ${index + 1}`,
          data: dataSet,
          backgroundColor: color,
          borderColor: borderColor,
          borderWidth: 1,
          fill: true,
          tension: 0.4
        };
      });
    } else {
      // Single dataset
      const color = colors[0];
      const borderColor = color.replace('0.5', '1');
      
      datasets = [{
        label: options.datasetLabels?.[0] || title,
        data: data,
        backgroundColor: color,
        borderColor: borderColor,
        borderWidth: 1,
        fill: true,
        tension: 0.4
      }];
    }

    // Create chart
    chartInstance.current = new Chart(ctx, {
      type: 'line', // Line chart with fill = true creates an area chart
      data: {
        labels: labels,
        datasets: datasets
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            display: datasets.length > 1 || options.showLegend,
            position: 'top',
          },
          title: {
            display: !!title,
            text: title
          },
          tooltip: {
            mode: 'index',
            intersect: false,
          }
        },
        scales: {
          x: {
            grid: {
              display: showGrid
            },
            title: {
              display: !!options.xAxisTitle,
              text: options.xAxisTitle
            }
          },
          y: {
            stacked: stacked,
            grid: {
              display: showGrid
            },
            title: {
              display: !!options.yAxisTitle,
              text: options.yAxisTitle
            },
            beginAtZero: true
          }
        },
        elements: {
          line: {
            tension: 0.4 // Smooth curves
          }
        },
        ...options
      }
    });

    // Cleanup function
    return () => {
      if (chartInstance.current) {
        chartInstance.current.destroy();
      }
    };
  }, [data, labels, title, colors, height, stacked, showGrid, options]);

  return (
    <div style={{ height: `${height}px`, width: '100%' }}>
      <canvas ref={chartRef}></canvas>
    </div>
  );
};

export default AreaChart;
