import React, { useState, useEffect } from 'react';
import { safeApiRequest } from '../../api/apiClient';
import { getCategoryOptions } from '../../config/productCategories';
import LoadingState from '../ui/LoadingState';
import Alert from '../ui/Alert';
import Button from '../ui/Button';
import FormField from '../ui/FormField';
import Modal from '../ui/Modal';

/**
 * Supplier Manager Component
 * Comprehensive supplier management and relationship tracking
 */
const SupplierManager = () => {
  const [suppliers, setSuppliers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [filters, setFilters] = useState({
    search: '',
    status: 'active',
    category: '',
    isPreferred: ''
  });
  const [showAddModal, setShowAddModal] = useState(false);
  const [newSupplier, setNewSupplier] = useState({
    name: '',
    contactInfo: {
      email: '',
      phone: '',
      website: '',
      contactPerson: {
        name: '',
        title: '',
        email: '',
        phone: ''
      }
    },
    address: {
      street: '',
      city: '',
      state: '',
      country: 'Tanzania',
      postalCode: ''
    },
    businessInfo: {
      businessType: 'distributor',
      registrationNumber: '',
      taxId: ''
    },
    categories: [],
    brands: [],
    paymentTerms: {
      creditDays: 30,
      paymentMethods: ['bank_transfer'],
      currency: 'TZS'
    },
    notes: ''
  });

  useEffect(() => {
    fetchSuppliers();
  }, [filters]);

  const fetchSuppliers = async () => {
    try {
      setLoading(true);
      setError('');

      const queryParams = new URLSearchParams();
      Object.entries(filters).forEach(([key, value]) => {
        if (value) queryParams.append(key, value);
      });

      const response = await safeApiRequest(`/api/admin/dashboard/suppliers?${queryParams}`);
      setSuppliers(response.data?.suppliers || []);
    } catch (err) {
      setError('Failed to load suppliers');
      console.error('Suppliers fetch error:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleAddSupplier = async () => {
    try {
      setLoading(true);
      
      const response = await safeApiRequest('/api/admin/dashboard/suppliers', {
        method: 'POST',
        data: newSupplier
      });

      setSuppliers(prev => [response.data, ...prev]);
      setShowAddModal(false);
      resetNewSupplier();
    } catch (err) {
      setError('Failed to add supplier');
    } finally {
      setLoading(false);
    }
  };

  const resetNewSupplier = () => {
    setNewSupplier({
      name: '',
      contactInfo: {
        email: '',
        phone: '',
        website: '',
        contactPerson: {
          name: '',
          title: '',
          email: '',
          phone: ''
        }
      },
      address: {
        street: '',
        city: '',
        state: '',
        country: 'Tanzania',
        postalCode: ''
      },
      businessInfo: {
        businessType: 'distributor',
        registrationNumber: '',
        taxId: ''
      },
      categories: [],
      brands: [],
      paymentTerms: {
        creditDays: 30,
        paymentMethods: ['bank_transfer'],
        currency: 'TZS'
      },
      notes: ''
    });
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'inactive':
        return 'bg-gray-100 text-gray-800';
      case 'suspended':
        return 'bg-red-100 text-red-800';
      case 'pending_approval':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getRatingStars = (rating) => {
    const stars = [];
    for (let i = 1; i <= 5; i++) {
      stars.push(
        <i
          key={i}
          className={`fas fa-star ${i <= rating ? 'text-yellow-400' : 'text-gray-300'}`}
        />
      );
    }
    return stars;
  };

  if (loading && suppliers.length === 0) {
    return <LoadingState text="Loading suppliers..." />;
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold text-gray-900 flex items-center">
            <i className="fas fa-truck mr-2 text-blue-600"></i>
            Supplier Management
          </h2>
          <Button
            onClick={() => setShowAddModal(true)}
            className="bg-blue-600 hover:bg-blue-700"
          >
            <i className="fas fa-plus mr-2"></i>
            Add Supplier
          </Button>
        </div>

        {/* Filters */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <FormField
            label="Search"
            name="search"
            type="text"
            value={filters.search}
            onChange={(e) => setFilters({ ...filters, search: e.target.value })}
            placeholder="Search suppliers..."
          />
          
          <FormField
            label="Status"
            name="status"
            type="select"
            value={filters.status}
            onChange={(e) => setFilters({ ...filters, status: e.target.value })}
            options={[
              { value: 'all', label: 'All Status' },
              { value: 'active', label: 'Active' },
              { value: 'inactive', label: 'Inactive' },
              { value: 'suspended', label: 'Suspended' },
              { value: 'pending_approval', label: 'Pending Approval' }
            ]}
          />
          
          <FormField
            label="Category"
            name="category"
            type="select"
            value={filters.category}
            onChange={(e) => setFilters({ ...filters, category: e.target.value })}
            options={[
              { value: '', label: 'All Categories' },
              ...getCategoryOptions()
            ]}
          />
          
          <FormField
            label="Preferred"
            name="isPreferred"
            type="select"
            value={filters.isPreferred}
            onChange={(e) => setFilters({ ...filters, isPreferred: e.target.value })}
            options={[
              { value: '', label: 'All Suppliers' },
              { value: 'true', label: 'Preferred Only' },
              { value: 'false', label: 'Non-Preferred' }
            ]}
          />
        </div>
      </div>

      {error && <Alert type="error" message={error} />}

      {/* Suppliers Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {suppliers.map((supplier) => (
          <div key={supplier._id} className="bg-white rounded-lg shadow-md p-6">
            <div className="flex justify-between items-start mb-4">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 flex items-center">
                  {supplier.name}
                  {supplier.isPreferred && (
                    <i className="fas fa-star text-yellow-400 ml-2" title="Preferred Supplier"></i>
                  )}
                </h3>
                <p className="text-sm text-gray-600">{supplier.code}</p>
              </div>
              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(supplier.status)}`}>
                {supplier.status.replace('_', ' ')}
              </span>
            </div>

            {/* Contact Info */}
            <div className="mb-4">
              <div className="text-sm text-gray-600">
                {supplier.contactInfo?.email && (
                  <div className="flex items-center mb-1">
                    <i className="fas fa-envelope mr-2 text-gray-400"></i>
                    {supplier.contactInfo.email}
                  </div>
                )}
                {supplier.contactInfo?.phone && (
                  <div className="flex items-center mb-1">
                    <i className="fas fa-phone mr-2 text-gray-400"></i>
                    {supplier.contactInfo.phone}
                  </div>
                )}
                {supplier.address?.city && (
                  <div className="flex items-center">
                    <i className="fas fa-map-marker-alt mr-2 text-gray-400"></i>
                    {supplier.address.city}, {supplier.address.country}
                  </div>
                )}
              </div>
            </div>

            {/* Performance Metrics */}
            <div className="mb-4">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-gray-700">Rating</span>
                <div className="flex items-center">
                  {getRatingStars(supplier.performance?.rating || 0)}
                  <span className="ml-2 text-sm text-gray-600">
                    ({(supplier.performance?.rating || 0).toFixed(1)})
                  </span>
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-2 text-sm">
                <div>
                  <span className="text-gray-600">Orders:</span>
                  <span className="ml-1 font-medium">{supplier.performance?.totalOrders || 0}</span>
                </div>
                <div>
                  <span className="text-gray-600">On-time:</span>
                  <span className="ml-1 font-medium">{(supplier.performance?.onTimeDeliveryRate || 0).toFixed(0)}%</span>
                </div>
              </div>
            </div>

            {/* Categories */}
            {supplier.categories && supplier.categories.length > 0 && (
              <div className="mb-4">
                <p className="text-sm font-medium text-gray-700 mb-2">Categories</p>
                <div className="flex flex-wrap gap-1">
                  {supplier.categories.slice(0, 3).map((category) => (
                    <span
                      key={category}
                      className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                    >
                      {category.replace('_', ' ')}
                    </span>
                  ))}
                  {supplier.categories.length > 3 && (
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                      +{supplier.categories.length - 3} more
                    </span>
                  )}
                </div>
              </div>
            )}

            {/* Actions */}
            <div className="flex justify-between items-center pt-4 border-t border-gray-200">
              <div className="flex space-x-2">
                <Button
                  variant="link"
                  className="text-blue-600 hover:text-blue-900 p-1"
                  title="Edit Supplier"
                >
                  <i className="fas fa-edit"></i>
                </Button>
                <Button
                  variant="link"
                  className="text-green-600 hover:text-green-900 p-1"
                  title="View Details"
                >
                  <i className="fas fa-eye"></i>
                </Button>
                <Button
                  variant="link"
                  className="text-purple-600 hover:text-purple-900 p-1"
                  title="Contact"
                >
                  <i className="fas fa-envelope"></i>
                </Button>
              </div>
              
              <div className="text-xs text-gray-500">
                {supplier.performance?.lastOrderDate 
                  ? `Last order: ${new Date(supplier.performance.lastOrderDate).toLocaleDateString()}`
                  : 'No orders yet'
                }
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Add Supplier Modal */}
      <Modal
        isOpen={showAddModal}
        onClose={() => setShowAddModal(false)}
        title="Add New Supplier"
        size="xl"
      >
        <div className="space-y-6">
          {/* Basic Information */}
          <div>
            <h4 className="text-lg font-medium text-gray-900 mb-4">Basic Information</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                label="Supplier Name"
                name="name"
                type="text"
                value={newSupplier.name}
                onChange={(e) => setNewSupplier({ ...newSupplier, name: e.target.value })}
                placeholder="Enter supplier name"
                required
              />
              
              <FormField
                label="Business Type"
                name="businessType"
                type="select"
                value={newSupplier.businessInfo.businessType}
                onChange={(e) => setNewSupplier({
                  ...newSupplier,
                  businessInfo: { ...newSupplier.businessInfo, businessType: e.target.value }
                })}
                options={[
                  { value: 'manufacturer', label: 'Manufacturer' },
                  { value: 'distributor', label: 'Distributor' },
                  { value: 'wholesaler', label: 'Wholesaler' },
                  { value: 'retailer', label: 'Retailer' },
                  { value: 'importer', label: 'Importer' },
                  { value: 'other', label: 'Other' }
                ]}
              />
            </div>
          </div>

          {/* Contact Information */}
          <div>
            <h4 className="text-lg font-medium text-gray-900 mb-4">Contact Information</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                label="Email"
                name="email"
                type="email"
                value={newSupplier.contactInfo.email}
                onChange={(e) => setNewSupplier({
                  ...newSupplier,
                  contactInfo: { ...newSupplier.contactInfo, email: e.target.value }
                })}
                placeholder="<EMAIL>"
              />
              
              <FormField
                label="Phone"
                name="phone"
                type="text"
                value={newSupplier.contactInfo.phone}
                onChange={(e) => setNewSupplier({
                  ...newSupplier,
                  contactInfo: { ...newSupplier.contactInfo, phone: e.target.value }
                })}
                placeholder="+255 XXX XXX XXX"
              />
            </div>
          </div>

          {/* Categories */}
          <div>
            <h4 className="text-lg font-medium text-gray-900 mb-4">Product Categories</h4>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
              {getCategoryOptions().map((category) => (
                <label key={category.value} className="flex items-center">
                  <input
                    type="checkbox"
                    checked={newSupplier.categories.includes(category.value)}
                    onChange={(e) => {
                      if (e.target.checked) {
                        setNewSupplier({
                          ...newSupplier,
                          categories: [...newSupplier.categories, category.value]
                        });
                      } else {
                        setNewSupplier({
                          ...newSupplier,
                          categories: newSupplier.categories.filter(c => c !== category.value)
                        });
                      }
                    }}
                    className="mr-2"
                  />
                  <span className="text-sm">{category.label}</span>
                </label>
              ))}
            </div>
          </div>

          <div className="flex justify-end space-x-3 pt-4 border-t">
            <Button
              variant="secondary"
              onClick={() => setShowAddModal(false)}
            >
              Cancel
            </Button>
            <Button
              onClick={handleAddSupplier}
              disabled={!newSupplier.name}
            >
              Add Supplier
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default SupplierManager;
