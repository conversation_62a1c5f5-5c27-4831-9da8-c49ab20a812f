import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { safeApiRequest } from '../../api/apiClient';
import { formatPrice } from '../../utils/priceFormatter';
import { getCategoryConfig } from '../../config/productCategories';
import LoadingState from '../ui/LoadingState';
import Alert from '../ui/Alert';
import Button from '../ui/Button';

/**
 * Enhanced Admin Dashboard Component
 * Provides comprehensive analytics and quick actions for Phone Point Dar
 */
const EnhancedDashboard = () => {
  const [dashboardData, setDashboardData] = useState({
    overview: {
      totalRevenue: 0,
      totalOrders: 0,
      totalProducts: 0,
      totalCustomers: 0,
      todayRevenue: 0,
      todayOrders: 0,
      pendingOrders: 0,
      lowStockItems: 0
    },
    categoryStats: [],
    recentOrders: [],
    topProducts: [],
    lowStockProducts: [],
    salesTrends: []
  });
  
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [selectedTimeframe, setSelectedTimeframe] = useState('today');

  useEffect(() => {
    fetchDashboardData();
  }, [selectedTimeframe]);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      setError('');

      // Fetch dashboard data in parallel
      const [
        overviewResponse,
        categoryStatsResponse,
        recentOrdersResponse,
        topProductsResponse,
        lowStockResponse
      ] = await Promise.all([
        safeApiRequest('/api/admin/dashboard/overview'),
        safeApiRequest('/api/admin/dashboard/category-stats'),
        safeApiRequest('/api/orders/admin/all?limit=5&sort=-createdAt'),
        safeApiRequest('/api/admin/dashboard/top-products'),
        safeApiRequest('/api/products?stock=low&limit=5')
      ]);

      setDashboardData({
        overview: overviewResponse.data || {},
        categoryStats: categoryStatsResponse.data || [],
        recentOrders: recentOrdersResponse.data?.data || [],
        topProducts: topProductsResponse.data || [],
        lowStockProducts: lowStockResponse.data?.data || []
      });

    } catch (err) {
      console.error('Error fetching dashboard data:', err);
      setError('Failed to load dashboard data. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return <LoadingState text="Loading dashboard..." />;
  }

  if (error) {
    return <Alert type="error" message={error} />;
  }

  const { overview, categoryStats, recentOrders, topProducts, lowStockProducts } = dashboardData;

  return (
    <div className="space-y-6">
      {/* Header with Quick Actions */}
      <div className="bg-gradient-to-r from-blue-600 to-blue-800 rounded-lg p-6 text-white">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
          <div>
            <h1 className="text-2xl font-bold mb-2">📦 Phone Point Dar Dashboard</h1>
            <p className="text-blue-100">Comprehensive tech store management</p>
          </div>
          <div className="flex flex-wrap gap-2 mt-4 md:mt-0">
            <Link to="/admin/products/new">
              <Button className="bg-white text-blue-600 hover:bg-gray-100">
                <i className="fas fa-plus mr-2"></i>Add Product
              </Button>
            </Link>
            <Link to="/admin/orders">
              <Button className="bg-blue-500 hover:bg-blue-400 text-white">
                <i className="fas fa-shopping-cart mr-2"></i>Orders
              </Button>
            </Link>
            <Button 
              onClick={fetchDashboardData}
              className="bg-blue-500 hover:bg-blue-400 text-white"
            >
              <i className="fas fa-sync-alt mr-2"></i>Refresh
            </Button>
          </div>
        </div>
      </div>

      {/* Key Metrics Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <MetricCard
          title="Today's Revenue"
          value={formatPrice(overview.todayRevenue)}
          icon="💰"
          color="green"
          subtitle={`${overview.todayOrders} orders today`}
        />
        <MetricCard
          title="Total Revenue"
          value={formatPrice(overview.totalRevenue)}
          icon="📈"
          color="blue"
          subtitle="All time"
        />
        <MetricCard
          title="Pending Orders"
          value={overview.pendingOrders}
          icon="⏳"
          color="yellow"
          subtitle="Needs attention"
          actionLink="/admin/orders?status=pending"
        />
        <MetricCard
          title="Low Stock Items"
          value={overview.lowStockItems}
          icon="⚠️"
          color="red"
          subtitle="Reorder needed"
          actionLink="/admin/inventory?filter=low-stock"
        />
      </div>

      {/* Category Performance */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          <i className="fas fa-chart-pie mr-2 text-blue-600"></i>
          Category Performance
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {Array.isArray(categoryStats) && categoryStats.length > 0 ? (
            categoryStats.map((category) => {
              const categoryConfig = getCategoryConfig(category.category);
              return (
                <CategoryCard
                  key={category.category}
                  category={category}
                  config={categoryConfig}
                />
              );
            })
          ) : (
            <div className="col-span-full text-center py-8 text-gray-500">
              <i className="fas fa-chart-pie text-4xl mb-4"></i>
              <p>No category data available</p>
            </div>
          )}
        </div>
      </div>

      {/* Recent Activity & Quick Actions */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Orders */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-semibold text-gray-900 flex items-center">
              <i className="fas fa-shopping-cart mr-2 text-green-600"></i>
              Recent Orders
            </h3>
            <Link to="/admin/orders" className="text-blue-600 hover:text-blue-800 text-sm">
              View All →
            </Link>
          </div>
          <div className="space-y-3">
            {recentOrders.length > 0 ? (
              recentOrders.map((order) => (
                <OrderCard key={order._id} order={order} />
              ))
            ) : (
              <p className="text-gray-500 text-center py-4">No recent orders</p>
            )}
          </div>
        </div>

        {/* Top Products */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-semibold text-gray-900 flex items-center">
              <i className="fas fa-star mr-2 text-yellow-600"></i>
              Top Products
            </h3>
            <Link to="/admin/products" className="text-blue-600 hover:text-blue-800 text-sm">
              View All →
            </Link>
          </div>
          <div className="space-y-3">
            {topProducts.length > 0 ? (
              topProducts.map((product) => (
                <ProductCard key={product._id} product={product} />
              ))
            ) : (
              <p className="text-gray-500 text-center py-4">No product data</p>
            )}
          </div>
        </div>
      </div>

      {/* Low Stock Alert */}
      {lowStockProducts.length > 0 && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-red-900 mb-4 flex items-center">
            <i className="fas fa-exclamation-triangle mr-2 text-red-600"></i>
            Low Stock Alert ({lowStockProducts.length} items)
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {lowStockProducts.map((product) => (
              <LowStockCard key={product._id} product={product} />
            ))}
          </div>
          <div className="mt-4">
            <Link to="/admin/inventory?filter=low-stock">
              <Button className="bg-red-600 hover:bg-red-700 text-white">
                <i className="fas fa-boxes mr-2"></i>
                Manage Inventory
              </Button>
            </Link>
          </div>
        </div>
      )}
    </div>
  );
};

// Metric Card Component
const MetricCard = ({ title, value, icon, color, subtitle, actionLink }) => {
  const colorClasses = {
    green: 'bg-green-50 border-green-200 text-green-800',
    blue: 'bg-blue-50 border-blue-200 text-blue-800',
    yellow: 'bg-yellow-50 border-yellow-200 text-yellow-800',
    red: 'bg-red-50 border-red-200 text-red-800'
  };

  const content = (
    <div className={`p-6 rounded-lg border ${colorClasses[color]} ${actionLink ? 'hover:shadow-md transition-shadow cursor-pointer' : ''}`}>
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium opacity-75">{title}</p>
          <p className="text-2xl font-bold">{value}</p>
          {subtitle && <p className="text-xs opacity-60 mt-1">{subtitle}</p>}
        </div>
        <div className="text-3xl">{icon}</div>
      </div>
    </div>
  );

  return actionLink ? <Link to={actionLink}>{content}</Link> : content;
};

// Category Card Component
const CategoryCard = ({ category, config }) => (
  <div className="bg-gray-50 rounded-lg p-4">
    <div className="flex items-center justify-between mb-2">
      <span className="text-2xl">{config.icon}</span>
      <span className="text-sm font-medium text-gray-600">{category.count} items</span>
    </div>
    <h4 className="font-medium text-gray-900">{config.label}</h4>
    <p className="text-sm text-gray-600">{formatPrice(category.revenue)} revenue</p>
    <div className="mt-2 bg-gray-200 rounded-full h-2">
      <div 
        className="bg-blue-600 h-2 rounded-full" 
        style={{ width: `${Math.min(category.percentage || 0, 100)}%` }}
      ></div>
    </div>
  </div>
);

// Order Card Component
const OrderCard = ({ order }) => (
  <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
    <div>
      <p className="font-medium text-gray-900">#{order.orderNumber}</p>
      <p className="text-sm text-gray-600">{order.customer?.name || 'Guest'}</p>
    </div>
    <div className="text-right">
      <p className="font-medium text-gray-900">{formatPrice(order.total)}</p>
      <span className={`inline-block px-2 py-1 text-xs rounded-full ${
        order.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
        order.status === 'processing' ? 'bg-blue-100 text-blue-800' :
        order.status === 'shipped' ? 'bg-green-100 text-green-800' :
        'bg-gray-100 text-gray-800'
      }`}>
        {order.status}
      </span>
    </div>
  </div>
);

// Product Card Component
const ProductCard = ({ product }) => {
  const categoryConfig = getCategoryConfig(product.category);
  return (
    <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
      <div className="flex items-center">
        <span className="text-xl mr-3">{categoryConfig.icon}</span>
        <div>
          <p className="font-medium text-gray-900">{product.name}</p>
          <p className="text-sm text-gray-600">{product.brand}</p>
        </div>
      </div>
      <div className="text-right">
        <p className="font-medium text-gray-900">{formatPrice(product.price)}</p>
        <p className="text-sm text-gray-600">{product.salesCount || 0} sold</p>
      </div>
    </div>
  );
};

// Low Stock Card Component
const LowStockCard = ({ product }) => {
  const categoryConfig = getCategoryConfig(product.category);
  return (
    <div className="bg-white rounded-lg p-4 border border-red-200">
      <div className="flex items-center justify-between mb-2">
        <span className="text-xl">{categoryConfig.icon}</span>
        <span className="text-sm font-medium text-red-600">
          {product.stock_quantity} left
        </span>
      </div>
      <h4 className="font-medium text-gray-900 mb-1">{product.name}</h4>
      <p className="text-sm text-gray-600">{product.brand} • {product.sku}</p>
      <div className="mt-2">
        <Link 
          to={`/admin/products/${product._id}/edit`}
          className="text-blue-600 hover:text-blue-800 text-sm"
        >
          Update Stock →
        </Link>
      </div>
    </div>
  );
};

export default EnhancedDashboard;
