# Admin Components

This directory contains components used in the admin interface of the AIXcelerate platform.

## Chart Components

### LineChart

`LineChart` is a reusable component for displaying time series data.

#### Props

- `data` (array, required): Array of numeric data points
- `labels` (array, required): Array of labels for the x-axis
- `title` (string): Chart title
- `color` (string): Line color (hex code)
- `height` (number): Chart height in pixels
- `showGrid` (boolean): Whether to show grid lines
- `showArea` (boolean): Whether to show area under the line
- `showPoints` (boolean): Whether to show data points

#### Example

```jsx
<LineChart
  data={[10, 25, 15, 30, 20, 35, 45]}
  labels={['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']}
  title="Weekly Revenue"
  color="#10B981"
  height={200}
  showGrid={true}
  showArea={true}
  showPoints={true}
/>
```

### PieChart

`PieChart` is a reusable component for displaying proportional data.

#### Props

- `data` (array, required): Array of numeric values to plot
- `labels` (array, required): Array of labels for each segment
- `colors` (array): Array of colors for each segment
- `title` (string): Chart title
- `showPercentage` (boolean): Whether to show percentage values
- `showLegend` (boolean): Whether to show the legend
- `size` (number): Size of the chart in pixels
- `innerRadius` (number): Inner radius for donut chart (0 for pie chart)

#### Example

```jsx
<PieChart
  data={[40, 30, 20, 10]}
  labels={['Direct', 'Organic Search', 'Social Media', 'Referral']}
  colors={['#8B5CF6', '#3B82F6', '#10B981', '#F59E0B']}
  title="Traffic Sources"
  showPercentage={true}
  showLegend={true}
  size={120}
  innerRadius={0}
/>
```

## StatCard

`StatCard` is a component for displaying a statistic with an icon.

#### Props

- `title` (string, required): Card title
- `value` (string|number, required): Statistic value
- `icon` (node, required): Icon component
- `bgColor` (string): Background color for the icon
- `textColor` (string): Text color for the icon
- `trend` (string): Trend direction ('up', 'down', or null)
- `trendValue` (number): Trend percentage value
- `trendLabel` (string): Trend label text
- `className` (string): Additional CSS classes

#### Example

```jsx
<StatCard
  title="Revenue"
  value="$12,345"
  icon={<RevenueIcon />}
  bgColor="bg-green-100"
  textColor="text-green-600"
  trend="up"
  trendValue={12}
  trendLabel="vs. last month"
/>
```

## DataTable

`DataTable` is a component for displaying tabular data with sorting, filtering, and pagination.

#### Props

- `data` (array, required): Array of data objects
- `columns` (array, required): Array of column configuration objects
- `title` (string): Table title
- `perPage` (number): Number of rows per page
- `searchable` (boolean): Whether to enable search functionality
- `sortable` (boolean): Whether to enable sorting functionality
- `pagination` (boolean): Whether to enable pagination
- `actions` (array): Array of action configuration objects

#### Example

```jsx
<DataTable
  data={products}
  columns={[
    { key: 'name', label: 'Product Name' },
    { key: 'price', label: 'Price', format: (value) => `$${value}` },
    { key: 'status', label: 'Status' }
  ]}
  title="Products"
  perPage={10}
  searchable={true}
  sortable={true}
  pagination={true}
  actions={[
    { label: 'Edit', onClick: (item) => handleEdit(item) },
    { label: 'Delete', onClick: (item) => handleDelete(item) }
  ]}
/>
```

## FormBuilder

`FormBuilder` is a component for dynamically building forms based on a configuration object.

#### Props

- `fields` (array, required): Array of field configuration objects
- `values` (object): Initial form values
- `onSubmit` (function, required): Function to call on form submission
- `submitText` (string): Text for the submit button
- `cancelText` (string): Text for the cancel button
- `onCancel` (function): Function to call on cancel

#### Example

```jsx
<FormBuilder
  fields={[
    { name: 'name', label: 'Product Name', type: 'text', required: true },
    { name: 'price', label: 'Price', type: 'number', required: true },
    { name: 'description', label: 'Description', type: 'textarea' },
    { name: 'category', label: 'Category', type: 'select', options: categories }
  ]}
  values={product}
  onSubmit={handleSubmit}
  submitText="Save Product"
  cancelText="Cancel"
  onCancel={handleCancel}
/>
```

## Usage Guidelines

1. Always provide required props to components
2. Use consistent styling across the admin interface
3. Prefer reusable components over one-off implementations
4. Follow the naming conventions established in this directory
5. Document any new components added to this directory
