import React, { useState } from 'react';
import { post, put, del } from '../../api/unifiedApiClient';
import Button from '../ui/Button';
import Alert from '../ui/Alert';
import Modal from '../ui/Modal';
import FormField from '../ui/FormField';
import { getCategoryOptions } from '../../config/productCategories';

/**
 * Bulk Operations Component
 * Provides bulk actions for product and order management
 */
const BulkOperations = ({ selectedItems, itemType, onSuccess, onCancel }) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [showModal, setShowModal] = useState(false);
  const [operation, setOperation] = useState('');
  const [operationData, setOperationData] = useState({});

  const bulkOperations = {
    products: [
      { id: 'update-status', label: 'Update Status', icon: 'fas fa-toggle-on' },
      { id: 'update-category', label: 'Update Category', icon: 'fas fa-tags' },
      { id: 'update-pricing', label: 'Update Pricing', icon: 'fas fa-dollar-sign' },
      { id: 'update-stock', label: 'Update Stock', icon: 'fas fa-boxes' },
      { id: 'export', label: 'Export Data', icon: 'fas fa-download' },
      { id: 'delete', label: 'Delete Items', icon: 'fas fa-trash', danger: true }
    ],
    orders: [
      { id: 'update-status', label: 'Update Status', icon: 'fas fa-shipping-fast' },
      { id: 'mark-shipped', label: 'Mark as Shipped', icon: 'fas fa-truck' },
      { id: 'send-notification', label: 'Send Notification', icon: 'fas fa-bell' },
      { id: 'export', label: 'Export Orders', icon: 'fas fa-download' },
      { id: 'cancel', label: 'Cancel Orders', icon: 'fas fa-times', danger: true }
    ]
  };

  const handleOperationClick = (operationId) => {
    setOperation(operationId);
    setOperationData({});
    setError('');
    setSuccess('');
    
    if (operationId === 'export') {
      handleExport();
    } else {
      setShowModal(true);
    }
  };

  const handleExport = async () => {
    try {
      setLoading(true);
      const endpoint = itemType === 'products' ? '/api/admin/products/export' : '/api/admin/orders/export';
      
      const response = await safeApiRequest(endpoint, {
        method: 'POST',
        data: { ids: selectedItems }
      });

      // Create download link
      const blob = new Blob([response.data], { type: 'text/csv' });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${itemType}-export-${new Date().toISOString().split('T')[0]}.csv`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      setSuccess(`Successfully exported ${selectedItems.length} ${itemType}`);
      onSuccess?.();
    } catch (err) {
      setError(`Failed to export ${itemType}: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  const handleBulkOperation = async () => {
    try {
      setLoading(true);
      setError('');

      let endpoint = '';
      let data = {};

      switch (operation) {
        case 'update-status':
          if (itemType === 'products') {
            endpoint = '/bulk/products/update-status';
            data = { product_ids: selectedItems, status: operationData.status };
          } else if (itemType === 'orders') {
            endpoint = '/bulk/orders/update-status';
            data = { order_ids: selectedItems, status: operationData.status };
          }
          break;
        case 'update-pricing':
          endpoint = '/bulk/products/update-pricing';
          data = {
            product_ids: selectedItems,
            pricing_action: operationData.pricing_action,
            value: operationData.value
          };
          break;
        case 'delete':
          if (itemType === 'products') {
            endpoint = '/bulk/products';
            data = { product_ids: selectedItems };
          }
          break;
        default:
          throw new Error('Invalid operation');
      }

      const method = operation === 'delete' ? 'DELETE' : 'POST';
      const response = await safeApiRequest({
        method,
        url: endpoint,
        data
      });

      setSuccess(`Successfully updated ${response.data.updated || selectedItems.length} ${itemType}`);
      setShowModal(false);
      onSuccess?.();
    } catch (err) {
      setError(`Failed to perform bulk operation: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  const renderOperationForm = () => {
    switch (operation) {
      case 'update-status':
        return (
          <FormField
            label={`New ${itemType === 'products' ? 'Product' : 'Order'} Status`}
            name="status"
            type="select"
            value={operationData.status || ''}
            onChange={(e) => setOperationData({ ...operationData, status: e.target.value })}
            options={
              itemType === 'products' 
                ? [
                    { value: 'active', label: 'Active' },
                    { value: 'inactive', label: 'Inactive' },
                    { value: 'draft', label: 'Draft' }
                  ]
                : [
                    { value: 'pending', label: 'Pending' },
                    { value: 'processing', label: 'Processing' },
                    { value: 'shipped', label: 'Shipped' },
                    { value: 'delivered', label: 'Delivered' },
                    { value: 'cancelled', label: 'Cancelled' }
                  ]
            }
            required
          />
        );

      case 'update-category':
        return (
          <FormField
            label="New Category"
            name="category"
            type="select"
            value={operationData.category || ''}
            onChange={(e) => setOperationData({ ...operationData, category: e.target.value })}
            options={getCategoryOptions()}
            required
          />
        );

      case 'update-pricing':
        return (
          <div className="space-y-4">
            <FormField
              label="Price Adjustment Type"
              name="adjustmentType"
              type="select"
              value={operationData.adjustmentType || ''}
              onChange={(e) => setOperationData({ ...operationData, adjustmentType: e.target.value })}
              options={[
                { value: 'percentage', label: 'Percentage Change' },
                { value: 'fixed', label: 'Fixed Amount Change' },
                { value: 'set', label: 'Set New Price' }
              ]}
              required
            />
            <FormField
              label={
                operationData.adjustmentType === 'percentage' ? 'Percentage (%)' :
                operationData.adjustmentType === 'fixed' ? 'Amount (TZS)' :
                'New Price (TZS)'
              }
              name="value"
              type="number"
              value={operationData.value || ''}
              onChange={(e) => setOperationData({ ...operationData, value: parseFloat(e.target.value) })}
              placeholder="Enter value"
              required
            />
          </div>
        );

      case 'update-stock':
        return (
          <div className="space-y-4">
            <FormField
              label="Stock Adjustment Type"
              name="adjustmentType"
              type="select"
              value={operationData.adjustmentType || ''}
              onChange={(e) => setOperationData({ ...operationData, adjustmentType: e.target.value })}
              options={[
                { value: 'add', label: 'Add to Current Stock' },
                { value: 'subtract', label: 'Subtract from Current Stock' },
                { value: 'set', label: 'Set New Stock Level' }
              ]}
              required
            />
            <FormField
              label="Quantity"
              name="quantity"
              type="number"
              value={operationData.quantity || ''}
              onChange={(e) => setOperationData({ ...operationData, quantity: parseInt(e.target.value) })}
              placeholder="Enter quantity"
              min="0"
              required
            />
          </div>
        );

      case 'send-notification':
        return (
          <div className="space-y-4">
            <FormField
              label="Notification Type"
              name="notificationType"
              type="select"
              value={operationData.notificationType || ''}
              onChange={(e) => setOperationData({ ...operationData, notificationType: e.target.value })}
              options={[
                { value: 'order-update', label: 'Order Status Update' },
                { value: 'shipping-info', label: 'Shipping Information' },
                { value: 'custom', label: 'Custom Message' }
              ]}
              required
            />
            {operationData.notificationType === 'custom' && (
              <FormField
                label="Custom Message"
                name="message"
                type="textarea"
                value={operationData.message || ''}
                onChange={(e) => setOperationData({ ...operationData, message: e.target.value })}
                placeholder="Enter your custom message"
                rows={4}
                required
              />
            )}
          </div>
        );

      case 'delete':
      case 'cancel':
        return (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="flex items-center">
              <i className="fas fa-exclamation-triangle text-red-600 mr-2"></i>
              <p className="text-red-800 font-medium">
                Are you sure you want to {operation === 'delete' ? 'delete' : 'cancel'} {selectedItems.length} {itemType}?
              </p>
            </div>
            <p className="text-red-700 text-sm mt-2">
              This action cannot be undone.
            </p>
          </div>
        );

      default:
        return null;
    }
  };

  if (selectedItems.length === 0) {
    return (
      <div className="bg-gray-50 rounded-lg p-4 text-center">
        <i className="fas fa-info-circle text-gray-400 text-2xl mb-2"></i>
        <p className="text-gray-600">Select items to perform bulk operations</p>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900">
          <i className="fas fa-tasks mr-2 text-blue-600"></i>
          Bulk Operations ({selectedItems.length} selected)
        </h3>
        <Button
          variant="secondary"
          onClick={onCancel}
          className="text-gray-600"
        >
          Clear Selection
        </Button>
      </div>

      {error && <Alert type="error" message={error} className="mb-4" />}
      {success && <Alert type="success" message={success} className="mb-4" />}

      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-3">
        {bulkOperations[itemType]?.map((op) => (
          <Button
            key={op.id}
            onClick={() => handleOperationClick(op.id)}
            disabled={loading}
            className={`flex flex-col items-center p-3 text-sm ${
              op.danger 
                ? 'bg-red-50 text-red-700 border-red-200 hover:bg-red-100' 
                : 'bg-blue-50 text-blue-700 border-blue-200 hover:bg-blue-100'
            }`}
            variant="outline"
          >
            <i className={`${op.icon} text-lg mb-1`}></i>
            {op.label}
          </Button>
        ))}
      </div>

      {/* Operation Modal */}
      <Modal
        isOpen={showModal}
        onClose={() => setShowModal(false)}
        title={`Bulk ${operation.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}`}
      >
        <div className="space-y-4">
          <p className="text-gray-600">
            This operation will affect {selectedItems.length} {itemType}.
          </p>
          
          {renderOperationForm()}
          
          <div className="flex justify-end space-x-3 pt-4 border-t">
            <Button
              variant="secondary"
              onClick={() => setShowModal(false)}
              disabled={loading}
            >
              Cancel
            </Button>
            <Button
              onClick={handleBulkOperation}
              disabled={loading}
              className={operation === 'delete' || operation === 'cancel' ? 'bg-red-600 hover:bg-red-700' : ''}
            >
              {loading ? 'Processing...' : 'Apply Changes'}
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default BulkOperations;
