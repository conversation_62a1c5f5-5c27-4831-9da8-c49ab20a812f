import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import Button from '../ui/Button';
import FormField from '../ui/FormField';
import Alert from '../ui/Alert';
import { createProduct, updateProduct } from '../../services/productService';

/**
 * Phone Product Form Component for Phone Point Dar
 * Specialized form for adding/editing mobile phones and accessories
 */
const PhoneProductForm = ({ product = null }) => {
  const navigate = useNavigate();
  const isEditMode = !!product;

  // Phone-specific form state
  const [formData, setFormData] = useState({
    // Basic Information
    name: product?.name || '',
    brand: product?.brand || '',
    model: product?.model || '',
    description: product?.description || '',
    
    // Pricing (in Tanzanian Shillings)
    price: product?.price || 0,
    currency: 'TZS',
    
    // Phone Specifications
    operating_system: product?.operating_system || 'Android',
    storage_capacity: product?.storage_capacity || '128GB',
    screen_size: product?.screen_size || '',
    camera_specs: product?.camera_specs || '',
    battery_capacity: product?.battery_capacity || '',
    
    // Variants and Colors
    color_variants: product?.color_variants || ['Black'],
    storage_variants: product?.storage_variants || ['128GB'],
    
    // Inventory Management
    stock_quantity: product?.stock_quantity || 0,
    low_stock_threshold: product?.low_stock_threshold || 5,
    sku: product?.sku || '',
    
    // Business Information
    warranty_period: product?.warranty_period || '1 year',
    supplier: product?.supplier || '',
    cost_price: product?.cost_price || 0,
    
    // Status
    status: product?.status || 'active',
    is_featured: product?.is_featured || false,
    
    // Categories
    category: product?.category || 'smartphone',
    subcategory: product?.subcategory || ''
  });

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  // Handle form field changes
  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  // Handle array fields (colors, storage variants)
  const handleArrayChange = (field, index, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: prev[field].map((item, i) => i === index ? value : item)
    }));
  };

  const addArrayItem = (field) => {
    setFormData(prev => ({
      ...prev,
      [field]: [...prev[field], '']
    }));
  };

  const removeArrayItem = (field, index) => {
    setFormData(prev => ({
      ...prev,
      [field]: prev[field].filter((_, i) => i !== index)
    }));
  };

  // Form validation
  const validateForm = () => {
    if (!formData.name.trim()) return 'Phone name is required';
    if (!formData.brand.trim()) return 'Brand is required';
    if (!formData.model.trim()) return 'Model is required';
    if (formData.price <= 0) return 'Price must be greater than 0';
    if (formData.stock_quantity < 0) return 'Stock quantity cannot be negative';
    return null;
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    const validationError = validateForm();
    if (validationError) {
      setError(validationError);
      return;
    }

    setLoading(true);
    setError('');
    setSuccess('');

    try {
      // Prepare data for API
      const phoneData = {
        ...formData,
        product_type: 'phone',
        // Generate SKU if not provided
        sku: formData.sku || `${formData.brand.toUpperCase()}-${formData.model.replace(/\s+/g, '')}-${Date.now()}`,
        // Ensure color and storage variants are arrays
        color_variants: formData.color_variants.filter(color => color.trim()),
        storage_variants: formData.storage_variants.filter(storage => storage.trim())
      };

      let result;
      if (isEditMode) {
        result = await updateProduct(product._id, phoneData);
        setSuccess('Phone updated successfully!');
      } else {
        result = await createProduct(phoneData);
        setSuccess('Phone added successfully!');
      }

      // Redirect after success
      setTimeout(() => {
        navigate('/admin/products');
      }, 2000);

    } catch (err) {
      console.error('Error saving phone:', err);
      setError(err.message || 'Failed to save phone. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto">
      <form onSubmit={handleSubmit} className="space-y-8">
        {/* Error/Success Messages */}
        {error && <Alert type="error" message={error} />}
        {success && <Alert type="success" message={success} />}

        {/* Basic Information */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <i className="fas fa-mobile-alt mr-2 text-blue-600"></i>
            Basic Phone Information
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <FormField
              label="Phone Name *"
              name="name"
              value={formData.name}
              onChange={handleChange}
              placeholder="e.g., iPhone 15 Pro Max"
              required
            />
            
            <FormField
              label="Brand *"
              name="brand"
              type="select"
              value={formData.brand}
              onChange={handleChange}
              required
              options={[
                { value: '', label: 'Select Brand' },
                { value: 'Apple', label: 'Apple' },
                { value: 'Samsung', label: 'Samsung' },
                { value: 'Huawei', label: 'Huawei' },
                { value: 'Xiaomi', label: 'Xiaomi' },
                { value: 'Oppo', label: 'Oppo' },
                { value: 'Vivo', label: 'Vivo' },
                { value: 'Tecno', label: 'Tecno' },
                { value: 'Infinix', label: 'Infinix' },
                { value: 'Nokia', label: 'Nokia' },
                { value: 'OnePlus', label: 'OnePlus' },
                { value: 'Google', label: 'Google' },
                { value: 'Other', label: 'Other' }
              ]}
            />
            
            <FormField
              label="Model *"
              name="model"
              value={formData.model}
              onChange={handleChange}
              placeholder="e.g., 15 Pro Max"
              required
            />
            
            <FormField
              label="Category"
              name="category"
              type="select"
              value={formData.category}
              onChange={handleChange}
              options={[
                { value: 'smartphone', label: 'Smartphone' },
                { value: 'feature_phone', label: 'Feature Phone' },
                { value: 'tablet', label: 'Tablet' },
                { value: 'accessory', label: 'Accessory' },
                { value: 'smartwatch', label: 'Smartwatch' }
              ]}
            />
          </div>

          <div className="mt-6">
            <FormField
              label="Description"
              name="description"
              type="textarea"
              value={formData.description}
              onChange={handleChange}
              placeholder="Describe the phone's key features and benefits..."
              rows={4}
            />
          </div>
        </div>

        {/* Technical Specifications */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <i className="fas fa-cogs mr-2 text-blue-600"></i>
            Technical Specifications
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <FormField
              label="Operating System"
              name="operating_system"
              type="select"
              value={formData.operating_system}
              onChange={handleChange}
              options={[
                { value: 'Android', label: 'Android' },
                { value: 'iOS', label: 'iOS' },
                { value: 'HarmonyOS', label: 'HarmonyOS' },
                { value: 'Other', label: 'Other' }
              ]}
            />
            
            <FormField
              label="Storage Capacity"
              name="storage_capacity"
              type="select"
              value={formData.storage_capacity}
              onChange={handleChange}
              options={[
                { value: '32GB', label: '32GB' },
                { value: '64GB', label: '64GB' },
                { value: '128GB', label: '128GB' },
                { value: '256GB', label: '256GB' },
                { value: '512GB', label: '512GB' },
                { value: '1TB', label: '1TB' }
              ]}
            />
            
            <FormField
              label="Screen Size"
              name="screen_size"
              value={formData.screen_size}
              onChange={handleChange}
              placeholder="e.g., 6.7 inches"
            />
            
            <FormField
              label="Camera Specifications"
              name="camera_specs"
              value={formData.camera_specs}
              onChange={handleChange}
              placeholder="e.g., 48MP Triple Camera"
            />
            
            <FormField
              label="Battery Capacity"
              name="battery_capacity"
              value={formData.battery_capacity}
              onChange={handleChange}
              placeholder="e.g., 4500mAh"
            />
            
            <FormField
              label="SKU"
              name="sku"
              value={formData.sku}
              onChange={handleChange}
              placeholder="Auto-generated if empty"
            />
          </div>
        </div>

        {/* Pricing and Inventory */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <i className="fas fa-tags mr-2 text-blue-600"></i>
            Pricing & Inventory
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <FormField
              label="Selling Price (TZS) *"
              name="price"
              type="number"
              value={formData.price}
              onChange={handleChange}
              placeholder="0"
              min="0"
              required
            />

            <FormField
              label="Cost Price (TZS)"
              name="cost_price"
              type="number"
              value={formData.cost_price}
              onChange={handleChange}
              placeholder="0"
              min="0"
            />

            <FormField
              label="Stock Quantity *"
              name="stock_quantity"
              type="number"
              value={formData.stock_quantity}
              onChange={handleChange}
              placeholder="0"
              min="0"
              required
            />

            <FormField
              label="Low Stock Alert"
              name="low_stock_threshold"
              type="number"
              value={formData.low_stock_threshold}
              onChange={handleChange}
              placeholder="5"
              min="0"
            />

            <FormField
              label="Warranty Period"
              name="warranty_period"
              type="select"
              value={formData.warranty_period}
              onChange={handleChange}
              options={[
                { value: '6 months', label: '6 Months' },
                { value: '1 year', label: '1 Year' },
                { value: '2 years', label: '2 Years' },
                { value: '3 years', label: '3 Years' }
              ]}
            />

            <FormField
              label="Supplier"
              name="supplier"
              value={formData.supplier}
              onChange={handleChange}
              placeholder="Supplier name"
            />
          </div>
        </div>

        {/* Color and Storage Variants */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <i className="fas fa-palette mr-2 text-blue-600"></i>
            Available Variants
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Color Variants */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Color Variants
              </label>
              {formData.color_variants.map((color, index) => (
                <div key={index} className="flex items-center space-x-2 mb-2">
                  <input
                    type="text"
                    value={color}
                    onChange={(e) => handleArrayChange('color_variants', index, e.target.value)}
                    placeholder="Color name"
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => removeArrayItem('color_variants', index)}
                    className="text-red-600 hover:text-red-800"
                  >
                    <i className="fas fa-trash"></i>
                  </Button>
                </div>
              ))}
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => addArrayItem('color_variants')}
                className="mt-2"
              >
                <i className="fas fa-plus mr-2"></i>
                Add Color
              </Button>
            </div>

            {/* Storage Variants */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Storage Variants
              </label>
              {formData.storage_variants.map((storage, index) => (
                <div key={index} className="flex items-center space-x-2 mb-2">
                  <input
                    type="text"
                    value={storage}
                    onChange={(e) => handleArrayChange('storage_variants', index, e.target.value)}
                    placeholder="Storage capacity"
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => removeArrayItem('storage_variants', index)}
                    className="text-red-600 hover:text-red-800"
                  >
                    <i className="fas fa-trash"></i>
                  </Button>
                </div>
              ))}
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => addArrayItem('storage_variants')}
                className="mt-2"
              >
                <i className="fas fa-plus mr-2"></i>
                Add Storage
              </Button>
            </div>
          </div>
        </div>

        {/* Status and Settings */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <i className="fas fa-cog mr-2 text-blue-600"></i>
            Status & Settings
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <FormField
              label="Status"
              name="status"
              type="select"
              value={formData.status}
              onChange={handleChange}
              options={[
                { value: 'active', label: 'Active' },
                { value: 'inactive', label: 'Inactive' },
                { value: 'out_of_stock', label: 'Out of Stock' },
                { value: 'discontinued', label: 'Discontinued' }
              ]}
            />

            <div className="flex items-center space-x-4">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  name="is_featured"
                  checked={formData.is_featured}
                  onChange={handleChange}
                  className="mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <span className="text-sm font-medium text-gray-700">Featured Phone</span>
              </label>
            </div>
          </div>
        </div>

        {/* Form Actions */}
        <div className="flex justify-end space-x-4">
          <Button
            type="button"
            variant="outline"
            onClick={() => navigate('/admin/products')}
            disabled={loading}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            isLoading={loading}
            disabled={loading}
            className="bg-blue-600 hover:bg-blue-700"
          >
            <i className="fas fa-save mr-2"></i>
            {isEditMode ? 'Update Phone' : 'Add Phone'}
          </Button>
        </div>
      </form>
    </div>
  );
};

export default PhoneProductForm;
