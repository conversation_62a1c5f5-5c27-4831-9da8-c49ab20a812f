import React from 'react';
import { Link } from 'react-router-dom';
import { useDeviceDetect } from '../../utils/mobileOptimization';
import Card from '../ui/Card';
import ResponsiveImage from '../ui/ResponsiveImage';
import Button from '../ui/Button';
import { trackProductView } from '../../utils/analytics';
import { formatProductPrice } from '../../utils/priceFormatter';
import { ARIA_LABELS, announceToScreenReader } from '../../utils/accessibility';
import AccessibleButton from '../ui/AccessibleButton';

/**
 * Mobile-optimized product card component
 * 
 * @param {Object} props - Component props
 * @param {Object} props.product - Product data
 * @param {Function} props.onAddToCart - Function to call when adding to cart
 * @returns {React.ReactElement} Product card component
 */
const ProductCard = ({ product, onAddToCart }) => {
  const { isMobile, isTablet } = useDeviceDetect();
  
  // Handle click on the product card
  const handleProductClick = () => {
    trackProductView(product);
  };
  
  // Handle add to cart button click
  const handleAddToCart = (e) => {
    e.preventDefault();
    e.stopPropagation();
    if (onAddToCart) {
      onAddToCart(product);
    }
  };
  
  // Determine image dimensions based on device
  const imageWidth = isMobile ? 300 : isTablet ? 350 : 400;
  const imageHeight = isMobile ? 180 : isTablet ? 210 : 240;
  
  // Determine font sizes based on device
  const titleClass = isMobile ? 'text-lg' : 'text-xl';
  const priceClass = isMobile ? 'text-lg' : 'text-xl';
  const descriptionClass = isMobile ? 'text-sm' : 'text-base';
  
  // Determine button size based on device
  const buttonSize = isMobile ? 'sm' : 'md';
  
  return (
    <Card
      as={Link}
      to={`/products/${product._id}`}
      className="flex flex-col h-full focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
      onClick={handleProductClick}
      role="article"
      aria-label={`${product.name} - ${formatProductPrice(product.price)}`}
    >
      <div className="relative">
        <ResponsiveImage
          src={product.image || '/images/product-placeholder.jpg'}
          alt={`${product.name} product image`}
          width={imageWidth}
          height={imageHeight}
          className="w-full rounded-t-lg"
        />

        {product.product_type === 'lead_magnet' && (
          <div
            className="absolute top-2 right-2 bg-green-500 text-white px-2 py-1 rounded text-xs font-bold"
            aria-label="Free product"
          >
            FREE
          </div>
        )}
      </div>
      
      <Card.Body className="flex-grow flex flex-col">
        <h3 className={`${titleClass} font-bold mb-2 text-gray-800`}>
          {product.name}
        </h3>

        <p className={`${descriptionClass} text-gray-600 mb-4 flex-grow`}>
          {product.description.length > 100
            ? `${product.description.substring(0, 100)}...`
            : product.description}
        </p>
        
        <div className="flex items-center justify-between mt-auto">
          <span
            className={`${priceClass} font-bold text-purple-600`}
            aria-label={`Price: ${formatProductPrice(product)}`}
          >
            {formatProductPrice(product)}
          </span>

          <AccessibleButton
            size={buttonSize}
            onClick={(e) => {
              handleAddToCart(e);
              announceToScreenReader(`${product.name} added to cart`);
            }}
            ariaLabel={`Add ${product.name} to cart`}
          >
            {isMobile ? 'Add' : 'Add to Cart'}
          </AccessibleButton>
        </div>
      </Card.Body>
    </Card>
  );
};

export default ProductCard;
