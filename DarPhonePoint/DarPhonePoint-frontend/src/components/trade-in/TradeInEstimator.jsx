import React, { useState } from 'react';
import { post } from '../../api/unifiedApiClient';
import { formatPrice } from '../../utils/priceFormatter';
import Button from '../ui/Button';
import Alert from '../ui/Alert';
import { useDeviceDetect } from '../../utils/mobileOptimization';

const DEVICE_BRANDS = [
  'Apple', 'Samsung', 'Tecno', 'Infinix', 'Xiaomi', 'Oppo', 'Vivo', 'Huawei', 'OnePlus', 'Google'
];

const DEVICE_CONDITIONS = [
  { value: 'excellent', label: 'Excellent', description: 'Like new, no visible wear' },
  { value: 'good', label: 'Good', description: 'Minor signs of use, fully functional' },
  { value: 'fair', label: 'Fair', description: 'Noticeable wear but works well' },
  { value: 'poor', label: 'Poor', description: 'Heavy wear, some functionality issues' },
  { value: 'damaged', label: 'Damaged', description: 'Significant damage, limited functionality' }
];

const SCREEN_CONDITIONS = [
  { value: 'perfect', label: 'Perfect', description: 'No scratches or cracks' },
  { value: 'minor_scratches', label: 'Minor Scratches', description: 'Light scratches, barely visible' },
  { value: 'major_scratches', label: 'Major Scratches', description: 'Visible scratches' },
  { value: 'cracked', label: 'Cracked', description: 'Screen has cracks but is functional' },
  { value: 'broken', label: 'Broken', description: 'Screen is severely damaged' }
];

const FUNCTIONAL_ISSUES = [
  { value: 'none', label: 'No Issues' },
  { value: 'camera_issues', label: 'Camera Problems' },
  { value: 'speaker_issues', label: 'Speaker Problems' },
  { value: 'microphone_issues', label: 'Microphone Problems' },
  { value: 'charging_issues', label: 'Charging Problems' },
  { value: 'button_issues', label: 'Button Problems' },
  { value: 'wifi_issues', label: 'WiFi Problems' },
  { value: 'cellular_issues', label: 'Cellular Problems' },
  { value: 'touch_issues', label: 'Touch Screen Problems' }
];

const ACCESSORIES = [
  { value: 'charger', label: 'Original Charger' },
  { value: 'earphones', label: 'Original Earphones' },
  { value: 'box', label: 'Original Box' },
  { value: 'manual', label: 'User Manual' },
  { value: 'case', label: 'Phone Case' },
  { value: 'screen_protector', label: 'Screen Protector' }
];

const TradeInEstimator = ({ onEstimateComplete }) => {
  const { isMobile } = useDeviceDetect();
  const [formData, setFormData] = useState({
    deviceBrand: '',
    deviceModel: '',
    deviceCondition: '',
    screenCondition: '',
    batteryHealth: 80,
    functionalIssues: [],
    accessoriesIncluded: []
  });
  const [estimate, setEstimate] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleCheckboxChange = (name, value, checked) => {
    setFormData(prev => ({
      ...prev,
      [name]: checked 
        ? [...prev[name], value]
        : prev[name].filter(item => item !== value)
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      const response = await safeApiRequest({
        method: 'POST',
        url: '/trade-in/estimate',
        data: formData
      });

      setEstimate(response.data);
      if (onEstimateComplete) {
        onEstimateComplete(response.data);
      }
    } catch (error) {
      setError('Failed to get trade-in estimate. Please try again.');
      console.error('Trade-in estimate error:', error);
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setFormData({
      deviceBrand: '',
      deviceModel: '',
      deviceCondition: '',
      screenCondition: '',
      batteryHealth: 80,
      functionalIssues: [],
      accessoriesIncluded: []
    });
    setEstimate(null);
    setError('');
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border p-6">
      <div className="flex items-center mb-6">
        <i className="fas fa-exchange-alt text-blue-600 text-2xl mr-3"></i>
        <div>
          <h2 className="text-xl font-semibold">Trade-In Estimator</h2>
          <p className="text-gray-600 text-sm">Get an instant estimate for your device</p>
        </div>
      </div>

      {error && (
        <Alert
          type="error"
          message={error}
          onClose={() => setError('')}
          className="mb-6"
        />
      )}

      {estimate ? (
        <div className="text-center">
          <div className="bg-green-50 border border-green-200 rounded-lg p-6 mb-6">
            <i className="fas fa-check-circle text-green-600 text-3xl mb-3"></i>
            <h3 className="text-lg font-semibold text-green-800 mb-2">Estimated Trade-In Value</h3>
            <p className="text-3xl font-bold text-green-600 mb-2">
              {formatPrice(estimate.estimated_value)}
            </p>
            <p className="text-sm text-green-700">
              {estimate.device_info.brand} {estimate.device_info.model}
            </p>
            <p className="text-xs text-green-600 mt-2">{estimate.note}</p>
          </div>
          
          <div className="space-y-3">
            <Button onClick={resetForm} variant="outline" fullWidth>
              <i className="fas fa-redo mr-2"></i>
              Get Another Estimate
            </Button>
          </div>
        </div>
      ) : (
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Device Brand */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Device Brand*
            </label>
            <select
              name="deviceBrand"
              value={formData.deviceBrand}
              onChange={handleChange}
              required
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">Select Brand</option>
              {DEVICE_BRANDS.map(brand => (
                <option key={brand} value={brand}>{brand}</option>
              ))}
            </select>
          </div>

          {/* Device Model */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Device Model*
            </label>
            <input
              type="text"
              name="deviceModel"
              value={formData.deviceModel}
              onChange={handleChange}
              required
              placeholder="e.g., iPhone 14, Galaxy S23, Spark 10"
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          {/* Device Condition */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Overall Device Condition*
            </label>
            <div className="space-y-2">
              {DEVICE_CONDITIONS.map(condition => (
                <label key={condition.value} className="flex items-start">
                  <input
                    type="radio"
                    name="deviceCondition"
                    value={condition.value}
                    checked={formData.deviceCondition === condition.value}
                    onChange={handleChange}
                    className="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                  />
                  <div className="ml-3">
                    <span className="font-medium">{condition.label}</span>
                    <p className="text-sm text-gray-600">{condition.description}</p>
                  </div>
                </label>
              ))}
            </div>
          </div>

          {/* Screen Condition */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Screen Condition*
            </label>
            <div className="space-y-2">
              {SCREEN_CONDITIONS.map(condition => (
                <label key={condition.value} className="flex items-start">
                  <input
                    type="radio"
                    name="screenCondition"
                    value={condition.value}
                    checked={formData.screenCondition === condition.value}
                    onChange={handleChange}
                    className="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                  />
                  <div className="ml-3">
                    <span className="font-medium">{condition.label}</span>
                    <p className="text-sm text-gray-600">{condition.description}</p>
                  </div>
                </label>
              ))}
            </div>
          </div>

          {/* Battery Health */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Battery Health: {formData.batteryHealth}%
            </label>
            <input
              type="range"
              name="batteryHealth"
              min="0"
              max="100"
              value={formData.batteryHealth}
              onChange={handleChange}
              className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
            />
            <div className="flex justify-between text-xs text-gray-500 mt-1">
              <span>0%</span>
              <span>50%</span>
              <span>100%</span>
            </div>
          </div>

          {/* Functional Issues */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Functional Issues (Select all that apply)
            </label>
            <div className={`grid gap-2 ${isMobile ? 'grid-cols-1' : 'grid-cols-2'}`}>
              {FUNCTIONAL_ISSUES.map(issue => (
                <label key={issue.value} className="flex items-center">
                  <input
                    type="checkbox"
                    checked={formData.functionalIssues.includes(issue.value)}
                    onChange={(e) => handleCheckboxChange('functionalIssues', issue.value, e.target.checked)}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <span className="ml-2 text-sm">{issue.label}</span>
                </label>
              ))}
            </div>
          </div>

          {/* Accessories */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Included Accessories (Select all that apply)
            </label>
            <div className={`grid gap-2 ${isMobile ? 'grid-cols-1' : 'grid-cols-2'}`}>
              {ACCESSORIES.map(accessory => (
                <label key={accessory.value} className="flex items-center">
                  <input
                    type="checkbox"
                    checked={formData.accessoriesIncluded.includes(accessory.value)}
                    onChange={(e) => handleCheckboxChange('accessoriesIncluded', accessory.value, e.target.checked)}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <span className="ml-2 text-sm">{accessory.label}</span>
                </label>
              ))}
            </div>
          </div>

          <Button
            type="submit"
            disabled={loading || !formData.deviceBrand || !formData.deviceModel || !formData.deviceCondition || !formData.screenCondition}
            fullWidth
          >
            {loading ? (
              <>
                <i className="fas fa-spinner fa-spin mr-2"></i>
                Calculating...
              </>
            ) : (
              <>
                <i className="fas fa-calculator mr-2"></i>
                Get Estimate
              </>
            )}
          </Button>
        </form>
      )}
    </div>
  );
};

export default TradeInEstimator;
