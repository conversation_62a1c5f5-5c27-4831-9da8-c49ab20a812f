import React, { useState } from 'react';
import { formatPrice } from '../../utils/priceFormatter';

const PAYMENT_METHODS = [
  {
    id: 'mpesa',
    name: 'M-Pesa',
    description: 'Pay with your M-Pesa mobile money account',
    icon: 'fas fa-mobile-alt',
    color: 'green',
    requiresPhone: true,
    instructions: 'Enter your M-Pesa registered phone number'
  },
  {
    id: 'tigo_pesa',
    name: 'Tigo Pesa',
    description: 'Pay with your Tigo Pesa mobile money account',
    icon: 'fas fa-mobile-alt',
    color: 'blue',
    requiresPhone: true,
    instructions: 'Enter your Tigo Pesa registered phone number'
  },
  {
    id: 'airtel_money',
    name: 'Airtel Money',
    description: 'Pay with your Airtel Money mobile money account',
    icon: 'fas fa-mobile-alt',
    color: 'red',
    requiresPhone: true,
    instructions: 'Enter your Airtel Money registered phone number'
  },
  {
    id: 'bank_transfer',
    name: 'Bank Transfer',
    description: 'Transfer money directly to our bank account',
    icon: 'fas fa-university',
    color: 'gray',
    requiresPhone: false,
    instructions: 'Bank details will be provided after order confirmation'
  },
  {
    id: 'cash_on_delivery',
    name: 'Cash on Delivery',
    description: 'Pay with cash when your order is delivered',
    icon: 'fas fa-money-bill-wave',
    color: 'yellow',
    requiresPhone: false,
    instructions: 'Have exact change ready for delivery'
  }
];

const PaymentMethodStep = ({ data, onUpdate, total }) => {
  const [formData, setFormData] = useState(data);
  const [errors, setErrors] = useState({});

  const selectedMethod = PAYMENT_METHODS.find(method => method.id === formData.method);

  const handleMethodSelect = (methodId) => {
    const newData = {
      ...formData,
      method: methodId,
      phone: methodId === formData.method ? formData.phone : '',
      details: {}
    };
    setFormData(newData);
    onUpdate(newData);
    
    // Clear errors when method changes
    setErrors({});
  };

  const handlePhoneChange = (e) => {
    const phone = e.target.value;
    const newData = {
      ...formData,
      phone
    };
    setFormData(newData);
    onUpdate(newData);
    
    // Clear phone error
    if (errors.phone) {
      setErrors(prev => ({ ...prev, phone: '' }));
    }
  };

  const validateForm = () => {
    const newErrors = {};
    
    if (!formData.method) {
      newErrors.method = 'Please select a payment method';
    }
    
    if (selectedMethod?.requiresPhone && !formData.phone) {
      newErrors.phone = 'Phone number is required for this payment method';
    } else if (selectedMethod?.requiresPhone && formData.phone) {
      const phoneRegex = /^[0-9]{10,12}$/;
      if (!phoneRegex.test(formData.phone.replace(/[^0-9]/g, ''))) {
        newErrors.phone = 'Please enter a valid phone number';
      }
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleBlur = () => {
    validateForm();
  };

  return (
    <div>
      <h2 className="text-xl font-semibold mb-6">Payment Method</h2>
      
      {/* Order Total */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
        <div className="flex justify-between items-center">
          <span className="text-lg font-medium">Total Amount:</span>
          <span className="text-2xl font-bold text-blue-600">{formatPrice(total)}</span>
        </div>
      </div>
      
      {/* Payment Methods */}
      <div className="space-y-4 mb-6">
        {PAYMENT_METHODS.map((method) => (
          <div
            key={method.id}
            className={`border-2 rounded-lg p-4 cursor-pointer transition-all ${
              formData.method === method.id
                ? 'border-blue-500 bg-blue-50'
                : 'border-gray-200 hover:border-blue-300'
            }`}
            onClick={() => handleMethodSelect(method.id)}
          >
            <div className="flex items-center">
              <div
                className={`flex items-center justify-center w-12 h-12 rounded-full mr-4 ${
                  method.color === 'green' ? 'bg-green-100 text-green-600' :
                  method.color === 'blue' ? 'bg-blue-100 text-blue-600' :
                  method.color === 'red' ? 'bg-red-100 text-red-600' :
                  method.color === 'yellow' ? 'bg-yellow-100 text-yellow-600' :
                  'bg-gray-100 text-gray-600'
                }`}
              >
                <i className={`${method.icon} text-lg`}></i>
              </div>
              
              <div className="flex-grow">
                <h3 className="font-semibold text-gray-900">{method.name}</h3>
                <p className="text-sm text-gray-600">{method.description}</p>
              </div>
              
              <div className="ml-4">
                <div
                  className={`w-6 h-6 rounded-full border-2 flex items-center justify-center ${
                    formData.method === method.id
                      ? 'border-blue-500 bg-blue-500'
                      : 'border-gray-300'
                  }`}
                >
                  {formData.method === method.id && (
                    <i className="fas fa-check text-white text-xs"></i>
                  )}
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
      
      {errors.method && (
        <p className="text-sm text-red-600 mb-4">{errors.method}</p>
      )}
      
      {/* Payment Details */}
      {selectedMethod && (
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-6">
          <h3 className="font-semibold text-gray-900 mb-4">
            {selectedMethod.name} Details
          </h3>
          
          <div className="flex items-start mb-4">
            <i className="fas fa-info-circle text-blue-500 mt-1 mr-2"></i>
            <p className="text-sm text-gray-600">{selectedMethod.instructions}</p>
          </div>
          
          {selectedMethod.requiresPhone && (
            <div className="mb-4">
              <label htmlFor="payment_phone" className="block text-sm font-medium text-gray-700 mb-1">
                Phone Number*
              </label>
              <div className="flex">
                <div className="flex-shrink-0">
                  <select
                    className="h-full rounded-l-lg border-r-0 border-gray-300 bg-gray-50 py-2 px-3 text-gray-500"
                    disabled
                  >
                    <option>+255</option>
                  </select>
                </div>
                <input
                  type="tel"
                  id="payment_phone"
                  value={formData.phone}
                  onChange={handlePhoneChange}
                  onBlur={handleBlur}
                  className={`flex-grow rounded-r-lg px-4 py-2 border focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                    errors.phone ? 'border-red-500' : 'border-gray-300'
                  }`}
                  placeholder="Enter your phone number"
                />
              </div>
              {errors.phone && (
                <p className="mt-1 text-sm text-red-600">{errors.phone}</p>
              )}
            </div>
          )}
          
          {/* Method-specific information */}
          {selectedMethod.id === 'bank_transfer' && (
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <h4 className="font-medium text-yellow-800 mb-2">Bank Transfer Instructions</h4>
              <p className="text-sm text-yellow-700">
                After placing your order, you will receive bank account details via email and SMS. 
                Please complete the transfer within 24 hours to secure your order.
              </p>
            </div>
          )}
          
          {selectedMethod.id === 'cash_on_delivery' && (
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <h4 className="font-medium text-yellow-800 mb-2">Cash on Delivery Terms</h4>
              <ul className="text-sm text-yellow-700 space-y-1">
                <li>• Payment is due upon delivery</li>
                <li>• Please have exact change ready</li>
                <li>• Additional delivery fee may apply</li>
                <li>• Available within Dar es Salaam and major cities</li>
              </ul>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default PaymentMethodStep;
