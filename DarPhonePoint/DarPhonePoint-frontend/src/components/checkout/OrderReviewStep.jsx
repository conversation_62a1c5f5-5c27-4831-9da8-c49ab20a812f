import React from 'react';
import { formatPrice } from '../../utils/priceFormatter';
import Button from '../ui/Button';

const PAYMENT_METHOD_NAMES = {
  mpesa: 'M-Pesa',
  tigo_pesa: 'Tigo Pesa',
  airtel_money: 'Airtel Money',
  bank_transfer: 'Bank Transfer',
  cash_on_delivery: 'Cash on Delivery'
};

const OrderReviewStep = ({ cart, checkoutData, onSubmit, isSubmitting, onUpdate }) => {
  const { customer, shipping, payment } = checkoutData;
  
  return (
    <div>
      <h2 className="text-xl font-semibold mb-6">Review Your Order</h2>
      
      <div className="space-y-8">
        {/* Customer Information */}
        <div className="bg-gray-50 rounded-lg p-4">
          <div className="flex justify-between items-center mb-3">
            <h3 className="font-semibold text-gray-900">Customer Information</h3>
            <Button
              variant="text"
              size="sm"
              onClick={() => window.scrollTo(0, 0)}
              className="text-blue-600 hover:text-blue-800"
            >
              <i className="fas fa-edit mr-1"></i> Edit
            </Button>
          </div>
          
          <div className="grid md:grid-cols-2 gap-4">
            <div>
              <p className="text-sm text-gray-500">Name</p>
              <p className="font-medium">{customer.firstName} {customer.lastName}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Email</p>
              <p className="font-medium">{customer.email}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Phone</p>
              <p className="font-medium">{customer.phone}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Account Type</p>
              <p className="font-medium">{customer.isGuest ? 'Guest Checkout' : 'Registered User'}</p>
            </div>
          </div>
        </div>
        
        {/* Shipping Address */}
        <div className="bg-gray-50 rounded-lg p-4">
          <div className="flex justify-between items-center mb-3">
            <h3 className="font-semibold text-gray-900">Shipping Address</h3>
            <Button
              variant="text"
              size="sm"
              onClick={() => window.scrollTo(0, 0)}
              className="text-blue-600 hover:text-blue-800"
            >
              <i className="fas fa-edit mr-1"></i> Edit
            </Button>
          </div>
          
          <div>
            <p className="font-medium">{customer.firstName} {customer.lastName}</p>
            <p>{shipping.street_address}</p>
            <p>{shipping.city}, {shipping.state_province}</p>
            <p>{shipping.postal_code && `${shipping.postal_code}, `}{shipping.country}</p>
            {shipping.delivery_instructions && (
              <div className="mt-2">
                <p className="text-sm text-gray-500">Delivery Instructions:</p>
                <p className="text-sm">{shipping.delivery_instructions}</p>
              </div>
            )}
          </div>
        </div>
        
        {/* Payment Method */}
        <div className="bg-gray-50 rounded-lg p-4">
          <div className="flex justify-between items-center mb-3">
            <h3 className="font-semibold text-gray-900">Payment Method</h3>
            <Button
              variant="text"
              size="sm"
              onClick={() => window.scrollTo(0, 0)}
              className="text-blue-600 hover:text-blue-800"
            >
              <i className="fas fa-edit mr-1"></i> Edit
            </Button>
          </div>
          
          <div>
            <p className="font-medium">{PAYMENT_METHOD_NAMES[payment.method]}</p>
            {payment.phone && (
              <p className="text-sm">Phone: +255 {payment.phone}</p>
            )}
            
            {payment.method === 'bank_transfer' && (
              <div className="mt-2 p-3 bg-blue-50 border border-blue-100 rounded-lg text-sm">
                <p>Bank account details will be provided after order confirmation.</p>
              </div>
            )}
            
            {payment.method === 'cash_on_delivery' && (
              <div className="mt-2 p-3 bg-blue-50 border border-blue-100 rounded-lg text-sm">
                <p>Please have exact change ready for delivery.</p>
              </div>
            )}
          </div>
        </div>
        
        {/* Order Items */}
        <div className="bg-gray-50 rounded-lg p-4">
          <h3 className="font-semibold text-gray-900 mb-3">Order Items ({cart.items.length})</h3>
          
          <div className="space-y-4 mb-4">
            {cart.items.map((item) => (
              <div key={item._id} className="flex border-b pb-4">
                <div className="w-16 h-16 flex-shrink-0">
                  <img
                    src={item.product.image_url || '/placeholder-phone.jpg'}
                    alt={item.product.name}
                    className="w-full h-full object-cover rounded"
                  />
                </div>
                
                <div className="ml-4 flex-grow">
                  <div className="flex justify-between">
                    <div>
                      <h4 className="font-medium">{item.product.name}</h4>
                      <p className="text-sm text-gray-600">
                        {item.product.brand} {item.product.model}
                      </p>
                      {item.variant_sku && (
                        <p className="text-xs text-gray-500">SKU: {item.variant_sku}</p>
                      )}
                    </div>
                    <div className="text-right">
                      <p className="font-medium">{formatPrice(item.price * item.quantity)}</p>
                      <p className="text-sm text-gray-600">
                        {formatPrice(item.price)} x {item.quantity}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
          
          {/* Order Summary */}
          <div className="border-t pt-4">
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-gray-600">Subtotal</span>
                <span>{formatPrice(cart.subtotal)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Shipping</span>
                <span>{cart.shipping_cost > 0 ? formatPrice(cart.shipping_cost) : 'Free'}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Tax</span>
                <span>{formatPrice(cart.tax_amount)}</span>
              </div>
              <div className="flex justify-between font-semibold text-lg pt-2 border-t">
                <span>Total</span>
                <span className="text-blue-600">{formatPrice(cart.total)}</span>
              </div>
            </div>
          </div>
        </div>
        
        {/* Order Notes */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            <i className="fas fa-sticky-note text-blue-600 mr-2"></i>
            Order Notes (Optional)
          </label>
          <textarea
            value={checkoutData.orderNotes || ''}
            onChange={(e) => onUpdate && onUpdate({ orderNotes: e.target.value })}
            placeholder="Any special instructions for your order? (e.g., delivery preferences, gift message, etc.)"
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            maxLength={500}
          />
          <p className="text-xs text-gray-500 mt-1">
            {(checkoutData.orderNotes || '').length}/500 characters
          </p>
        </div>

        {/* Terms and Conditions */}
        <div className="bg-gray-50 rounded-lg p-4">
          <div className="flex items-start">
            <input
              type="checkbox"
              id="terms"
              className="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label htmlFor="terms" className="ml-2 block text-sm text-gray-700">
              I agree to the <a href="/terms" className="text-blue-600 hover:underline">Terms and Conditions</a> and <a href="/privacy" className="text-blue-600 hover:underline">Privacy Policy</a>. I confirm that all the information provided is accurate.
            </label>
          </div>
        </div>
        
        {/* Place Order Button */}
        <div className="text-center">
          <Button
            onClick={onSubmit}
            disabled={isSubmitting}
            className="bg-green-600 hover:bg-green-700 text-white px-8 py-3 text-lg"
            fullWidth
          >
            {isSubmitting ? (
              <>
                <i className="fas fa-spinner fa-spin mr-2"></i>
                Processing...
              </>
            ) : (
              <>
                <i className="fas fa-check-circle mr-2"></i>
                Place Order
              </>
            )}
          </Button>
          
          <p className="mt-4 text-sm text-gray-500">
            By placing your order, you agree to Phone Point Dar's terms and conditions. You will receive an order confirmation via email and SMS.
          </p>
        </div>
      </div>
    </div>
  );
};

export default OrderReviewStep;
