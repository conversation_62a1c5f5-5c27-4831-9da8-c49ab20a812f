import React, { useState, useEffect } from 'react';
import { get, post } from '../../api/unifiedApiClient';
import { safeApiRequest } from '../../api/apiClient';

const TANZANIA_REGIONS = [
  '<PERSON><PERSON><PERSON>',
  '<PERSON> es Salaam',
  'Do<PERSON><PERSON>',
  '<PERSON><PERSON><PERSON>',
  '<PERSON>ring<PERSON>',
  'Kager<PERSON>',
  'Kat<PERSON>',
  'Kigoma',
  'Kilimanjaro',
  'Lindi',
  'Manyara',
  'Mara',
  'Mbeya',
  'Morogoro',
  'Mtwara',
  'Mwanza',
  'Njombe',
  'Pemba North',
  'Pemba South',
  'Pwani',
  'Rukwa',
  'Ruvuma',
  'Shinyanga',
  '<PERSON><PERSON><PERSON>',
  'Singida',
  'Songwe',
  'Tabora',
  'Tanga',
  'Zanzibar Central/South',
  'Zanzibar North',
  'Zanzibar Urban/West'
];

const DAR_ES_SALAAM_DISTRICTS = [
  'Ilala',
  'Kinondoni',
  'Temeke',
  'Kigamboni',
  'Ubungo'
];

const ShippingAddressStep = ({ data, onUpdate }) => {
  const [formData, setFormData] = useState(data);
  const [errors, setErrors] = useState({});
  const [districts, setDistricts] = useState([]);
  const [savedAddresses, setSavedAddresses] = useState([]);
  const [loadingSavedAddresses, setLoadingSavedAddresses] = useState(false);

  useEffect(() => {
    // Set districts based on selected region
    if (formData.state_province === 'Dar es Salaam') {
      setDistricts(DAR_ES_SALAAM_DISTRICTS);
    } else {
      setDistricts([]);
    }

    // Fetch saved addresses if user is logged in
    fetchSavedAddresses();
  }, [formData.state_province]);

  const fetchSavedAddresses = async () => {
    setLoadingSavedAddresses(true);
    try {
      const response = await safeApiRequest({
        method: 'GET',
        url: '/users/addresses'
      });
      
      if (response.data && response.data.length > 0) {
        setSavedAddresses(response.data);
      }
    } catch (error) {
      console.error('Error fetching saved addresses:', error);
    } finally {
      setLoadingSavedAddresses(false);
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    const updatedFormData = {
      ...formData,
      [name]: value
    };

    setFormData(updatedFormData);

    // Update parent component with new data
    onUpdate(updatedFormData);

    // Clear error when field is edited
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};
    
    if (!formData.street_address) {
      newErrors.street_address = 'Street address is required';
    }
    
    if (!formData.city) {
      newErrors.city = 'City is required';
    }
    
    if (!formData.state_province) {
      newErrors.state_province = 'Region is required';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleBlur = () => {
    if (validateForm()) {
      onUpdate(formData);
    }
  };

  const handleSavedAddressSelect = (address) => {
    setFormData(address);
    onUpdate(address);
  };

  return (
    <div>
      <h2 className="text-xl font-semibold mb-6">Shipping Address</h2>
      
      {/* Saved Addresses Section */}
      {savedAddresses.length > 0 && (
        <div className="mb-6">
          <h3 className="text-md font-medium mb-3">Saved Addresses</h3>
          <div className="grid gap-4 md:grid-cols-2">
            {savedAddresses.map((address, index) => (
              <div
                key={index}
                className={`border rounded-lg p-4 cursor-pointer transition-all ${
                  formData.id === address.id
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 hover:border-blue-300'
                }`}
                onClick={() => handleSavedAddressSelect(address)}
              >
                <p className="font-medium">{address.name || 'Address ' + (index + 1)}</p>
                <p className="text-sm text-gray-600">{address.street_address}</p>
                <p className="text-sm text-gray-600">
                  {address.city}, {address.state_province}
                </p>
                <p className="text-sm text-gray-600">
                  {address.postal_code}, {address.country}
                </p>
              </div>
            ))}
          </div>
          <div className="mt-3 border-t pt-3">
            <p className="text-sm text-gray-500">Or enter a new address below:</p>
          </div>
        </div>
      )}
      
      {/* Address Form */}
      <div className="space-y-6">
        <div>
          <label htmlFor="street_address" className="block text-sm font-medium text-gray-700 mb-1">
            Street Address*
          </label>
          <input
            type="text"
            id="street_address"
            name="street_address"
            value={formData.street_address}
            onChange={handleChange}
            onBlur={handleBlur}
            className={`w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
              errors.street_address ? 'border-red-500' : 'border-gray-300'
            }`}
            placeholder="Enter your street address"
          />
          {errors.street_address && (
            <p className="mt-1 text-sm text-red-600">{errors.street_address}</p>
          )}
        </div>
        
        <div className="grid gap-6 md:grid-cols-2">
          <div>
            <label htmlFor="state_province" className="block text-sm font-medium text-gray-700 mb-1">
              Region*
            </label>
            <select
              id="state_province"
              name="state_province"
              value={formData.state_province}
              onChange={handleChange}
              onBlur={handleBlur}
              className={`w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                errors.state_province ? 'border-red-500' : 'border-gray-300'
              }`}
            >
              <option value="">Select Region</option>
              {TANZANIA_REGIONS.map(region => (
                <option key={region} value={region}>{region}</option>
              ))}
            </select>
            {errors.state_province && (
              <p className="mt-1 text-sm text-red-600">{errors.state_province}</p>
            )}
          </div>
          
          <div>
            <label htmlFor="city" className="block text-sm font-medium text-gray-700 mb-1">
              {formData.state_province === 'Dar es Salaam' ? 'District*' : 'City/Town*'}
            </label>
            {formData.state_province === 'Dar es Salaam' ? (
              <select
                id="city"
                name="city"
                value={formData.city}
                onChange={handleChange}
                onBlur={handleBlur}
                className={`w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                  errors.city ? 'border-red-500' : 'border-gray-300'
                }`}
              >
                <option value="">Select District</option>
                {districts.map(district => (
                  <option key={district} value={district}>{district}</option>
                ))}
              </select>
            ) : (
              <input
                type="text"
                id="city"
                name="city"
                value={formData.city}
                onChange={handleChange}
                onBlur={handleBlur}
                className={`w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                  errors.city ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="Enter your city or town"
              />
            )}
            {errors.city && (
              <p className="mt-1 text-sm text-red-600">{errors.city}</p>
            )}
          </div>
        </div>
        
        <div className="grid gap-6 md:grid-cols-2">
          <div>
            <label htmlFor="postal_code" className="block text-sm font-medium text-gray-700 mb-1">
              Postal Code (Optional)
            </label>
            <input
              type="text"
              id="postal_code"
              name="postal_code"
              value={formData.postal_code}
              onChange={handleChange}
              onBlur={handleBlur}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Enter postal code"
            />
          </div>
          
          <div>
            <label htmlFor="country" className="block text-sm font-medium text-gray-700 mb-1">
              Country
            </label>
            <input
              type="text"
              id="country"
              name="country"
              value={formData.country}
              disabled
              className="w-full px-4 py-2 border border-gray-300 rounded-lg bg-gray-50 text-gray-500"
            />
          </div>
        </div>
        
        <div>
          <label htmlFor="delivery_instructions" className="block text-sm font-medium text-gray-700 mb-1">
            Delivery Instructions (Optional)
          </label>
          <textarea
            id="delivery_instructions"
            name="delivery_instructions"
            value={formData.delivery_instructions || ''}
            onChange={handleChange}
            onBlur={handleBlur}
            rows="3"
            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            placeholder="Add any special instructions for delivery"
          ></textarea>
          <p className="mt-1 text-xs text-gray-500">
            E.g., landmark directions, security gate code, preferred delivery time
          </p>
        </div>
        
        <div className="flex items-center">
          <input
            type="checkbox"
            id="save_address"
            name="save_address"
            checked={formData.save_address || false}
            onChange={(e) => {
              setFormData(prev => ({
                ...prev,
                save_address: e.target.checked
              }));
              onUpdate({...formData, save_address: e.target.checked});
            }}
            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
          />
          <label htmlFor="save_address" className="ml-2 block text-sm text-gray-700">
            Save this address for future orders
          </label>
        </div>
      </div>
    </div>
  );
};

export default ShippingAddressStep;
