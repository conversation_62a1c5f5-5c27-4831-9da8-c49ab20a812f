import React from 'react';

const CheckoutSteps = ({ steps, currentStep, onStepClick, isMobile }) => {
  const currentIndex = steps.findIndex(step => step.id === currentStep);

  return (
    <div className="w-full">
      {isMobile ? (
        // Mobile: Vertical progress indicator
        <div className="flex flex-col space-y-4">
          {steps.map((step, index) => {
            const isActive = step.id === currentStep;
            const isCompleted = index < currentIndex;
            const isClickable = index <= currentIndex;

            return (
              <div
                key={step.id}
                className={`flex items-center p-3 rounded-lg border-2 transition-all ${
                  isActive
                    ? 'border-blue-500 bg-blue-50'
                    : isCompleted
                    ? 'border-green-500 bg-green-50'
                    : 'border-gray-200 bg-white'
                } ${isClickable ? 'cursor-pointer hover:shadow-md' : 'cursor-not-allowed opacity-60'}`}
                onClick={() => isClickable && onStepClick(step.id)}
              >
                <div
                  className={`flex items-center justify-center w-10 h-10 rounded-full mr-3 ${
                    isActive
                      ? 'bg-blue-500 text-white'
                      : isCompleted
                      ? 'bg-green-500 text-white'
                      : 'bg-gray-200 text-gray-500'
                  }`}
                >
                  {isCompleted ? (
                    <i className="fas fa-check text-sm"></i>
                  ) : (
                    <i className={`${step.icon} text-sm`}></i>
                  )}
                </div>
                <div>
                  <h3
                    className={`font-medium ${
                      isActive ? 'text-blue-700' : isCompleted ? 'text-green-700' : 'text-gray-700'
                    }`}
                  >
                    {step.title}
                  </h3>
                  <p className="text-sm text-gray-500">
                    {isCompleted ? 'Completed' : isActive ? 'Current step' : 'Pending'}
                  </p>
                </div>
              </div>
            );
          })}
        </div>
      ) : (
        // Desktop: Horizontal stepper
        <div className="flex items-center justify-between">
          {steps.map((step, index) => {
            const isActive = step.id === currentStep;
            const isCompleted = index < currentIndex;
            const isClickable = index <= currentIndex;

            return (
              <React.Fragment key={step.id}>
                <div
                  className={`flex flex-col items-center ${
                    isClickable ? 'cursor-pointer' : 'cursor-not-allowed'
                  }`}
                  onClick={() => isClickable && onStepClick(step.id)}
                >
                  {/* Step Circle */}
                  <div
                    className={`flex items-center justify-center w-12 h-12 rounded-full border-2 transition-all ${
                      isActive
                        ? 'border-blue-500 bg-blue-500 text-white'
                        : isCompleted
                        ? 'border-green-500 bg-green-500 text-white'
                        : 'border-gray-300 bg-white text-gray-400'
                    } ${isClickable ? 'hover:shadow-lg' : 'opacity-60'}`}
                  >
                    {isCompleted ? (
                      <i className="fas fa-check"></i>
                    ) : (
                      <i className={step.icon}></i>
                    )}
                  </div>

                  {/* Step Label */}
                  <div className="mt-2 text-center">
                    <p
                      className={`text-sm font-medium ${
                        isActive
                          ? 'text-blue-600'
                          : isCompleted
                          ? 'text-green-600'
                          : 'text-gray-500'
                      }`}
                    >
                      {step.title}
                    </p>
                    <p className="text-xs text-gray-400 mt-1">
                      Step {index + 1}
                    </p>
                  </div>
                </div>

                {/* Connector Line */}
                {index < steps.length - 1 && (
                  <div className="flex-1 mx-4">
                    <div
                      className={`h-0.5 transition-all ${
                        index < currentIndex ? 'bg-green-500' : 'bg-gray-300'
                      }`}
                    ></div>
                  </div>
                )}
              </React.Fragment>
            );
          })}
        </div>
      )}

      {/* Progress Bar for Mobile */}
      {isMobile && (
        <div className="mt-6">
          <div className="flex justify-between text-sm text-gray-600 mb-2">
            <span>Progress</span>
            <span>{Math.round(((currentIndex + 1) / steps.length) * 100)}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className="bg-blue-500 h-2 rounded-full transition-all duration-300"
              style={{ width: `${((currentIndex + 1) / steps.length) * 100}%` }}
            ></div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CheckoutSteps;
