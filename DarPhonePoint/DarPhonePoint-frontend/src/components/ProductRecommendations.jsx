import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { safeApiRequest } from '../api/apiClient';
import LoadingState from './ui/LoadingState';

const ProductRecommendations = ({ 
  productId = null, 
  type = 'related', 
  title = null,
  limit = 8,
  className = ""
}) => {
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    fetchRecommendations();
  }, [productId, type]);

  const fetchRecommendations = async () => {
    setLoading(true);
    setError('');

    try {
      const params = new URLSearchParams();
      if (productId) params.append('productId', productId);
      params.append('type', type);

      const response = await safeApiRequest({
        method: 'GET',
        url: `/products/recommendations?${params.toString()}`
      });

      if (response.data && response.data.data) {
        setProducts(response.data.data.slice(0, limit));
      }
    } catch (err) {
      console.error('Error fetching recommendations:', err);
      setError('Failed to load recommendations');
    } finally {
      setLoading(false);
    }
  };

  const formatPrice = (price) => {
    return new Intl.NumberFormat('sw-TZ', {
      style: 'currency',
      currency: 'TZS',
      minimumFractionDigits: 0
    }).format(price);
  };

  const getDefaultTitle = () => {
    switch (type) {
      case 'related':
        return 'Related Products';
      case 'popular':
        return 'Popular Products';
      case 'new':
        return 'New Arrivals';
      case 'sale':
        return 'On Sale';
      default:
        return 'Recommended Products';
    }
  };

  if (loading) {
    return (
      <div className={`${className}`}>
        <LoadingState text="Loading recommendations..." size="sm" />
      </div>
    );
  }

  if (error || products.length === 0) {
    return null; // Don't show anything if there's an error or no products
  }

  return (
    <div className={`${className}`}>
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-2xl font-bold text-gray-900">
          {title || getDefaultTitle()}
        </h2>
        <Link 
          to={`/products?${type === 'related' ? `category=${products[0]?.category}` : `featured=true`}`}
          className="text-blue-600 hover:text-blue-800 font-medium"
        >
          View All →
        </Link>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
        {products.map((product) => (
          <Link
            key={product._id}
            to={`/products/${product._id}`}
            className="group"
          >
            <div className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300">
              {/* Product Image */}
              <div className="relative aspect-square">
                <img
                  src={product.primary_image?.url || '/api/placeholder/300/300'}
                  alt={product.name}
                  className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                />
                {product.is_featured && (
                  <span className="absolute top-2 left-2 bg-red-500 text-white px-2 py-1 text-xs font-semibold rounded">
                    Featured
                  </span>
                )}
                {product.compare_at_price && product.compare_at_price > product.price && (
                  <span className="absolute top-2 right-2 bg-green-500 text-white px-2 py-1 text-xs font-semibold rounded">
                    Sale
                  </span>
                )}
              </div>

              {/* Product Info */}
              <div className="p-4">
                {/* Brand */}
                <div className="text-sm text-blue-600 font-medium mb-1">
                  {product.brand}
                </div>

                {/* Product Name */}
                <h3 className="font-semibold text-gray-900 mb-2 line-clamp-2 group-hover:text-blue-600 transition-colors">
                  {product.name}
                </h3>

                {/* Price */}
                <div className="flex items-center space-x-2">
                  <span className="font-bold text-blue-600">
                    {formatPrice(product.price)}
                  </span>
                  {product.compare_at_price && product.compare_at_price > product.price && (
                    <span className="text-sm text-gray-500 line-through">
                      {formatPrice(product.compare_at_price)}
                    </span>
                  )}
                </div>

                {/* Stock Status */}
                <div className="mt-2">
                  {product.in_stock ? (
                    <span className="text-xs text-green-600 font-medium">
                      ✓ In Stock
                    </span>
                  ) : (
                    <span className="text-xs text-red-600 font-medium">
                      Out of Stock
                    </span>
                  )}
                </div>
              </div>
            </div>
          </Link>
        ))}
      </div>
    </div>
  );
};

export default ProductRecommendations;
