import React, { useState } from 'react';
import { EyeIcon, EyeSlashIcon } from '@heroicons/react/24/outline';
import PasswordStrengthIndicator from './PasswordStrengthIndicator';

/**
 * Enhanced Password Input Component
 * Includes show/hide toggle and optional strength indicator
 * 
 * @param {string} value - Current password value
 * @param {function} onChange - Change handler
 * @param {string} placeholder - Input placeholder
 * @param {string} label - Input label
 * @param {boolean} showStrengthIndicator - Whether to show strength indicator
 * @param {boolean} showRequirements - Whether to show detailed requirements
 * @param {string} error - Error message to display
 * @param {boolean} required - Whether the field is required
 * @param {string} className - Additional CSS classes
 * @param {string} name - Input name attribute
 * @param {string} id - Input id attribute
 */
const PasswordInput = ({
  value = '',
  onChange,
  placeholder = 'Enter your password',
  label = 'Password',
  showStrengthIndicator = false,
  showRequirements = true,
  error = '',
  required = false,
  className = '',
  name = 'password',
  id = 'password',
  ...rest
}) => {
  const [showPassword, setShowPassword] = useState(false);

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  const handleChange = (e) => {
    if (onChange) {
      onChange(e);
    }
  };

  return (
    <div className={`space-y-2 ${className}`}>
      {/* Label */}
      {label && (
        <label 
          htmlFor={id} 
          className="block text-sm font-medium text-gray-700"
        >
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
      )}

      {/* Password Input Container */}
      <div className="relative">
        <input
          type={showPassword ? 'text' : 'password'}
          id={id}
          name={name}
          value={value}
          onChange={handleChange}
          placeholder={placeholder}
          required={required}
          className={`
            block w-full px-3 py-2 pr-10 border rounded-md shadow-sm 
            placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 
            focus:border-purple-500 transition-colors duration-200
            ${error 
              ? 'border-red-300 focus:ring-red-500 focus:border-red-500' 
              : 'border-gray-300'
            }
          `}
          {...rest}
        />
        
        {/* Show/Hide Password Toggle */}
        <button
          type="button"
          onClick={togglePasswordVisibility}
          className="absolute inset-y-0 right-0 pr-3 flex items-center hover:text-purple-600 transition-colors duration-200"
          tabIndex={-1}
        >
          {showPassword ? (
            <EyeSlashIcon className="h-5 w-5 text-gray-400" />
          ) : (
            <EyeIcon className="h-5 w-5 text-gray-400" />
          )}
        </button>
      </div>

      {/* Error Message */}
      {error && (
        <p className="text-sm text-red-600 flex items-center">
          <svg className="h-4 w-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
          {error}
        </p>
      )}

      {/* Password Strength Indicator */}
      {showStrengthIndicator && value && (
        <div className="mt-3">
          <PasswordStrengthIndicator 
            password={value} 
            showRequirements={showRequirements}
          />
        </div>
      )}
    </div>
  );
};

export default PasswordInput;
