import React from 'react';
import Button from './Button';

/**
 * ErrorState Component
 * Displays error messages with optional retry functionality
 * 
 * @param {String} message - Error message to display
 * @param {Function} onRetry - Optional retry function
 * @param {String} title - Optional error title
 * @param {String} className - Additional CSS classes
 * @param {Boolean} showIcon - Whether to show error icon
 * @param {String} type - Error type (error, warning, info)
 */
const ErrorState = ({
  message = 'Something went wrong',
  onRetry,
  title = 'Error',
  className = '',
  showIcon = true,
  type = 'error'
}) => {
  const getTypeStyles = () => {
    switch (type) {
      case 'warning':
        return {
          bgColor: 'bg-yellow-50',
          borderColor: 'border-yellow-200',
          textColor: 'text-yellow-800',
          iconColor: 'text-yellow-500',
          icon: 'fas fa-exclamation-triangle'
        };
      case 'info':
        return {
          bgColor: 'bg-blue-50',
          borderColor: 'border-blue-200',
          textColor: 'text-blue-800',
          iconColor: 'text-blue-500',
          icon: 'fas fa-info-circle'
        };
      default:
        return {
          bgColor: 'bg-red-50',
          borderColor: 'border-red-200',
          textColor: 'text-red-800',
          iconColor: 'text-red-500',
          icon: 'fas fa-exclamation-circle'
        };
    }
  };

  const styles = getTypeStyles();

  return (
    <div className={`rounded-lg border ${styles.borderColor} ${styles.bgColor} p-6 text-center ${className}`}>
      <div className="mx-auto flex items-center justify-center">
        {showIcon && (
          <div className="flex-shrink-0">
            <i className={`${styles.icon} ${styles.iconColor} text-2xl mb-4`}></i>
          </div>
        )}
      </div>
      
      <div>
        <h3 className={`text-lg font-semibold ${styles.textColor} mb-2`}>
          {title}
        </h3>
        
        <p className={`text-sm ${styles.textColor} mb-4`}>
          {message}
        </p>
        
        {onRetry && (
          <Button
            onClick={onRetry}
            variant="outline"
            className={`${
              type === 'error' ? 'border-red-300 text-red-700 hover:bg-red-50' :
              type === 'warning' ? 'border-yellow-300 text-yellow-700 hover:bg-yellow-50' :
              'border-blue-300 text-blue-700 hover:bg-blue-50'
            }`}
          >
            <i className="fas fa-redo mr-2"></i>
            Try Again
          </Button>
        )}
      </div>
    </div>
  );
};

/**
 * Compact ErrorState for inline use
 */
ErrorState.Compact = ({
  message = 'Error occurred',
  onRetry,
  className = ''
}) => {
  return (
    <div className={`flex items-center justify-between bg-red-50 border border-red-200 rounded-md p-3 ${className}`}>
      <div className="flex items-center">
        <i className="fas fa-exclamation-circle text-red-500 mr-2"></i>
        <span className="text-sm text-red-800">{message}</span>
      </div>
      {onRetry && (
        <Button
          onClick={onRetry}
          size="sm"
          variant="outline"
          className="border-red-300 text-red-700 hover:bg-red-50 ml-3"
        >
          <i className="fas fa-redo mr-1"></i>
          Retry
        </Button>
      )}
    </div>
  );
};

/**
 * Network ErrorState for connection issues
 */
ErrorState.Network = ({
  onRetry,
  className = ''
}) => {
  return (
    <ErrorState
      title="Connection Error"
      message="Unable to connect to Phone Point Dar servers. Please check your internet connection and try again."
      onRetry={onRetry}
      className={className}
      showIcon={true}
    />
  );
};

/**
 * NotFound ErrorState for 404 errors
 */
ErrorState.NotFound = ({
  message = "The page or resource you're looking for doesn't exist.",
  onRetry,
  className = ''
}) => {
  return (
    <ErrorState
      title="Not Found"
      message={message}
      onRetry={onRetry}
      type="warning"
      className={className}
    />
  );
};

/**
 * Permission ErrorState for 403 errors
 */
ErrorState.Permission = ({
  message = "You don't have permission to access this resource.",
  className = ''
}) => {
  return (
    <ErrorState
      title="Access Denied"
      message={message}
      type="warning"
      className={className}
      showIcon={true}
    />
  );
};

export default ErrorState;
