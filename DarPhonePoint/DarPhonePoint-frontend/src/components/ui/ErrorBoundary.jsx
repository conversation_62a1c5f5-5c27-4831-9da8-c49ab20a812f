import React, { Component } from 'react';
import Button from './Button';

/**
 * ErrorBoundary Component
 * Catches JavaScript errors in child components and displays a fallback UI
 */
class ErrorBoundary extends Component {
  constructor(props) {
    super(props);
    this.state = { 
      hasError: false,
      error: null,
      errorInfo: null
    };
  }

  static getDerivedStateFromError(error) {
    // Update state so the next render will show the fallback UI
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    // Log the error to an error reporting service
    console.error('Error caught by ErrorBoundary:', error, errorInfo);
    this.setState({ errorInfo });
    
    // You could also log to an error reporting service here
    // logErrorToService(error, errorInfo);
  }

  handleReset = () => {
    this.setState({ 
      hasError: false,
      error: null,
      errorInfo: null
    });
  }

  handleReportError = () => {
    // Implement error reporting logic here
    alert('Error reported to the development team. Thank you!');
    
    // In a real implementation, you would send the error to your backend
    // reportErrorToBackend(this.state.error, this.state.errorInfo);
  }

  render() {
    if (this.state.hasError) {
      // Render fallback UI
      return (
        <div className="bg-white p-6 rounded-lg shadow-md">
          <div className="flex items-center mb-4">
            <div className="flex-shrink-0">
              <svg className="h-10 w-10 text-red-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
              </svg>
            </div>
            <div className="ml-4">
              <h3 className="text-lg font-medium text-gray-900">Something went wrong</h3>
              <p className="text-sm text-gray-500">
                We've encountered an error while rendering this component.
              </p>
            </div>
          </div>
          
          {this.props.showDetails && (
            <div className="mt-4 p-4 bg-gray-50 rounded-md overflow-auto max-h-64">
              <p className="text-sm font-medium text-gray-900 mb-2">Error details:</p>
              <pre className="text-xs text-gray-700 whitespace-pre-wrap">
                {this.state.error && this.state.error.toString()}
                {this.state.errorInfo && this.state.errorInfo.componentStack}
              </pre>
            </div>
          )}
          
          <div className="mt-6 flex space-x-3">
            <Button onClick={this.handleReset}>
              Try Again
            </Button>
            <Button variant="outline" onClick={this.handleReportError}>
              Report Error
            </Button>
            {this.props.fallbackAction && (
              <Button variant="outline" onClick={this.props.fallbackAction.onClick}>
                {this.props.fallbackAction.label}
              </Button>
            )}
          </div>
        </div>
      );
    }

    // If there's no error, render children normally
    return this.props.children;
  }
}

export default ErrorBoundary;
