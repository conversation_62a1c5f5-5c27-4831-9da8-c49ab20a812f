import React, { useState, useEffect } from 'react';
import { useDeviceDetect, getOptimizedImageUrl } from '../../utils/mobileOptimization';

/**
 * Responsive image component that loads optimized images based on device
 * 
 * @param {Object} props - Component props
 * @param {string} props.src - Image source URL
 * @param {string} props.alt - Image alt text
 * @param {number} props.width - Image width
 * @param {number} props.height - Image height
 * @param {string} props.className - Additional CSS classes
 * @param {boolean} props.lazy - Whether to lazy load the image
 * @param {string} props.fallback - Fallback image URL
 * @param {Object} props.rest - Additional props to pass to the image
 * @returns {React.ReactElement} Responsive image component
 */
const ResponsiveImage = ({ 
  src, 
  alt, 
  width, 
  height, 
  className = '', 
  lazy = true,
  fallback = '/images/placeholder.jpg',
  ...rest 
}) => {
  const { isMobile, isTablet } = useDeviceDetect();
  const [isLoaded, setIsLoaded] = useState(false);
  const [error, setError] = useState(false);
  
  // Calculate responsive dimensions
  const responsiveWidth = isMobile ? Math.round(width * 0.6) : 
                          isTablet ? Math.round(width * 0.8) : 
                          width;
  
  const responsiveHeight = height ? 
                          (isMobile ? Math.round(height * 0.6) : 
                           isTablet ? Math.round(height * 0.8) : 
                           height) : 
                          undefined;
  
  // Get optimized image URL
  const optimizedSrc = getOptimizedImageUrl(src, {
    width: responsiveWidth,
    quality: isMobile ? 70 : 85
  });
  
  // Reset error state when src changes
  useEffect(() => {
    setError(false);
  }, [src]);
  
  // Handle image load
  const handleLoad = () => {
    setIsLoaded(true);
  };
  
  // Handle image error
  const handleError = () => {
    setError(true);
  };
  
  return (
    <div 
      className={`relative overflow-hidden ${className}`}
      style={{ 
        width: responsiveWidth, 
        height: responsiveHeight,
        aspectRatio: height ? width / height : undefined
      }}
    >
      {/* Loading placeholder */}
      {!isLoaded && !error && (
        <div className="absolute inset-0 bg-gray-200 animate-pulse" />
      )}
      
      {/* Actual image */}
      <img
        src={error ? fallback : optimizedSrc}
        alt={alt}
        width={responsiveWidth}
        height={responsiveHeight}
        loading={lazy ? 'lazy' : undefined}
        onLoad={handleLoad}
        onError={handleError}
        className={`w-full h-full object-cover transition-opacity duration-300 ${isLoaded ? 'opacity-100' : 'opacity-0'}`}
        {...rest}
      />
    </div>
  );
};

export default ResponsiveImage;
