import React from 'react';
import { useDeviceDetect } from '../../utils/mobileOptimization';

/**
 * Responsive container component that adapts to different screen sizes
 * 
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Child components
 * @param {string} props.className - Additional CSS classes
 * @param {boolean} props.fluid - Whether the container should be fluid (full width)
 * @param {string} props.as - HTML element to render as
 * @param {Object} props.rest - Additional props to pass to the container
 * @returns {React.ReactElement} Responsive container component
 */
const Container = ({ 
  children, 
  className = '', 
  fluid = false,
  as: Component = 'div',
  ...rest 
}) => {
  const { isMobile } = useDeviceDetect();
  
  // Base classes for the container
  const baseClasses = 'mx-auto px-4';
  
  // Width classes based on fluid prop and device type
  const widthClasses = fluid 
    ? 'w-full' 
    : isMobile 
      ? 'w-full' 
      : 'max-w-7xl';
  
  // Padding classes based on device type
  const paddingClasses = isMobile 
    ? 'py-4' 
    : 'py-6';
  
  return (
    <Component 
      className={`${baseClasses} ${widthClasses} ${paddingClasses} ${className}`}
      {...rest}
    >
      {children}
    </Component>
  );
};

export default Container;
