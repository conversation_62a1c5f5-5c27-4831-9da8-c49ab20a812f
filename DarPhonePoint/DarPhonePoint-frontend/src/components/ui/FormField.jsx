import React from 'react';

/**
 * Form<PERSON>ield Component
 * A reusable form field component that handles different input types and validation
 * 
 * @param {String} type - Input type (text, email, password, textarea, select, checkbox, radio)
 * @param {String} name - Field name
 * @param {String} label - Field label
 * @param {String} placeholder - Input placeholder
 * @param {String} value - Field value
 * @param {Function} onChange - Change handler
 * @param {Function} onBlur - Blur handler
 * @param {String} error - Validation error message
 * @param {Boolean} touched - Whether the field has been touched
 * @param {Array} options - Options for select, checkbox group, or radio group
 * @param {String} helpText - Help text to display below the field
 * @param {Boolean} required - Whether the field is required
 * @param {Boolean} disabled - Whether the field is disabled
 * @param {String} className - Additional CSS classes
 */
const FormField = ({
  type = 'text',
  name,
  label,
  placeholder,
  value,
  onChange,
  onBlur,
  error,
  touched,
  options = [],
  helpText,
  required = false,
  disabled = false,
  className = '',
  ...props
}) => {
  // Determine if we should show an error
  const showError = touched && error;
  
  // Base classes for inputs
  const inputClasses = `w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent ${
    showError
      ? 'border-red-300 text-red-900 placeholder-red-300'
      : 'border-gray-300 placeholder-gray-400'
  } ${disabled ? 'bg-gray-100 cursor-not-allowed' : ''}`;
  
  // Render different input types
  const renderInput = () => {
    switch (type) {
      case 'textarea':
        return (
          <textarea
            id={name}
            name={name}
            value={value || ''}
            onChange={onChange}
            onBlur={onBlur}
            placeholder={placeholder}
            disabled={disabled}
            className={`${inputClasses} h-32`}
            {...props}
          />
        );
        
      case 'select':
        return (
          <select
            id={name}
            name={name}
            value={value || ''}
            onChange={onChange}
            onBlur={onBlur}
            disabled={disabled}
            className={inputClasses}
            {...props}
          >
            {placeholder && (
              <option value="" disabled>
                {placeholder}
              </option>
            )}
            {options.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        );
        
      case 'checkbox':
        return (
          <div className="flex items-center">
            <input
              id={name}
              name={name}
              type="checkbox"
              checked={value || false}
              onChange={onChange}
              onBlur={onBlur}
              disabled={disabled}
              className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
              {...props}
            />
            <label htmlFor={name} className="ml-2 block text-sm text-gray-900">
              {label}
            </label>
          </div>
        );
        
      case 'radio-group':
        return (
          <div className="space-y-2">
            {options.map((option) => (
              <div key={option.value} className="flex items-center">
                <input
                  id={`${name}-${option.value}`}
                  name={name}
                  type="radio"
                  value={option.value}
                  checked={value === option.value}
                  onChange={onChange}
                  onBlur={onBlur}
                  disabled={disabled}
                  className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300"
                  {...props}
                />
                <label
                  htmlFor={`${name}-${option.value}`}
                  className="ml-2 block text-sm text-gray-900"
                >
                  {option.label}
                </label>
              </div>
            ))}
          </div>
        );
        
      case 'checkbox-group':
        return (
          <div className="space-y-2">
            {options.map((option) => (
              <div key={option.value} className="flex items-center">
                <input
                  id={`${name}-${option.value}`}
                  name={`${name}[${option.value}]`}
                  type="checkbox"
                  checked={(value || []).includes(option.value)}
                  onChange={(e) => {
                    const newValue = [...(value || [])];
                    if (e.target.checked) {
                      newValue.push(option.value);
                    } else {
                      const index = newValue.indexOf(option.value);
                      if (index !== -1) {
                        newValue.splice(index, 1);
                      }
                    }
                    onChange({
                      target: {
                        name,
                        value: newValue
                      }
                    });
                  }}
                  onBlur={onBlur}
                  disabled={disabled}
                  className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
                  {...props}
                />
                <label
                  htmlFor={`${name}-${option.value}`}
                  className="ml-2 block text-sm text-gray-900"
                >
                  {option.label}
                </label>
              </div>
            ))}
          </div>
        );
        
      default:
        return (
          <input
            id={name}
            name={name}
            type={type}
            value={value || ''}
            onChange={onChange}
            onBlur={onBlur}
            placeholder={placeholder}
            disabled={disabled}
            className={inputClasses}
            {...props}
          />
        );
    }
  };
  
  return (
    <div className={`mb-4 ${className}`}>
      {/* Don't show label for checkbox type since it's rendered with the input */}
      {type !== 'checkbox' && label && (
        <label
          htmlFor={name}
          className="block text-sm font-medium text-gray-700 mb-1"
        >
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
      )}
      
      {renderInput()}
      
      {/* Help text */}
      {helpText && !showError && (
        <p className="mt-1 text-sm text-gray-500">{helpText}</p>
      )}
      
      {/* Error message */}
      {showError && (
        <p className="mt-1 text-sm text-red-600">{error}</p>
      )}
    </div>
  );
};

export default FormField;
