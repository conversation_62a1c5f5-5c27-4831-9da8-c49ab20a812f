import React from 'react';
import { CheckIcon, XMarkIcon } from '@heroicons/react/24/outline';

/**
 * Password Strength Indicator Component
 * Provides real-time feedback on password strength with visual indicators
 * 
 * @param {string} password - The password to analyze
 * @param {boolean} showRequirements - Whether to show detailed requirements
 * @param {string} className - Additional CSS classes
 */
const PasswordStrengthIndicator = ({ 
  password = '', 
  showRequirements = true, 
  className = '' 
}) => {
  // Password strength criteria
  const criteria = [
    {
      id: 'length',
      label: 'At least 8 characters',
      test: (pwd) => pwd.length >= 8,
      weight: 2
    },
    {
      id: 'lowercase',
      label: 'One lowercase letter',
      test: (pwd) => /[a-z]/.test(pwd),
      weight: 1
    },
    {
      id: 'uppercase',
      label: 'One uppercase letter',
      test: (pwd) => /[A-Z]/.test(pwd),
      weight: 1
    },
    {
      id: 'number',
      label: 'One number',
      test: (pwd) => /\d/.test(pwd),
      weight: 1
    },
    {
      id: 'special',
      label: 'One special character (!@#$%^&*)',
      test: (pwd) => /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(pwd),
      weight: 2
    },
    {
      id: 'noCommon',
      label: 'Not a common password',
      test: (pwd) => !isCommonPassword(pwd),
      weight: 1
    }
  ];

  // Calculate password strength
  const calculateStrength = (pwd) => {
    if (!pwd) return { score: 0, level: 'none', percentage: 0 };

    const passedCriteria = criteria.filter(criterion => criterion.test(pwd));
    const totalWeight = criteria.reduce((sum, criterion) => sum + criterion.weight, 0);
    const achievedWeight = passedCriteria.reduce((sum, criterion) => sum + criterion.weight, 0);
    
    const percentage = Math.round((achievedWeight / totalWeight) * 100);
    let level = 'weak';
    let score = 1;

    if (percentage >= 90) {
      level = 'very-strong';
      score = 5;
    } else if (percentage >= 75) {
      level = 'strong';
      score = 4;
    } else if (percentage >= 50) {
      level = 'medium';
      score = 3;
    } else if (percentage >= 25) {
      level = 'weak';
      score = 2;
    } else {
      level = 'very-weak';
      score = 1;
    }

    return { score, level, percentage, passedCriteria };
  };

  // Check if password is commonly used
  const isCommonPassword = (pwd) => {
    const commonPasswords = [
      'password', '123456', '123456789', 'qwerty', 'abc123', 
      'password123', 'admin', 'letmein', 'welcome', 'monkey',
      '1234567890', 'password1', '123123', 'qwerty123'
    ];
    return commonPasswords.includes(pwd.toLowerCase());
  };

  const strength = calculateStrength(password);

  // Strength level configurations
  const strengthConfig = {
    'none': {
      color: 'bg-gray-200',
      textColor: 'text-gray-500',
      label: 'Enter password',
      barColor: 'bg-gray-200'
    },
    'very-weak': {
      color: 'bg-red-500',
      textColor: 'text-red-600',
      label: 'Very Weak',
      barColor: 'bg-red-500'
    },
    'weak': {
      color: 'bg-orange-500',
      textColor: 'text-orange-600',
      label: 'Weak',
      barColor: 'bg-orange-500'
    },
    'medium': {
      color: 'bg-yellow-500',
      textColor: 'text-yellow-600',
      label: 'Medium',
      barColor: 'bg-yellow-500'
    },
    'strong': {
      color: 'bg-blue-500',
      textColor: 'text-blue-600',
      label: 'Strong',
      barColor: 'bg-blue-500'
    },
    'very-strong': {
      color: 'bg-green-500',
      textColor: 'text-green-600',
      label: 'Very Strong',
      barColor: 'bg-green-500'
    }
  };

  const config = strengthConfig[strength.level];

  return (
    <div className={`space-y-3 ${className}`}>
      {/* Strength Bar */}
      <div className="space-y-2">
        <div className="flex justify-between items-center">
          <span className="text-sm font-medium text-gray-700">
            Password Strength
          </span>
          <span className={`text-sm font-medium ${config.textColor}`}>
            {config.label}
          </span>
        </div>
        
        {/* Progress Bar */}
        <div className="w-full bg-gray-200 rounded-full h-2 overflow-hidden">
          <div 
            className={`h-full ${config.barColor} transition-all duration-300 ease-out`}
            style={{ width: `${strength.percentage}%` }}
          />
        </div>
        
        {/* Strength Dots */}
        <div className="flex space-x-1">
          {[1, 2, 3, 4, 5].map((level) => (
            <div
              key={level}
              className={`h-1 flex-1 rounded-full transition-colors duration-200 ${
                strength.score >= level ? config.color : 'bg-gray-200'
              }`}
            />
          ))}
        </div>
      </div>

      {/* Requirements Checklist */}
      {showRequirements && password && (
        <div className="space-y-2">
          <h4 className="text-sm font-medium text-gray-700">Requirements:</h4>
          <div className="grid grid-cols-1 gap-1">
            {criteria.map((criterion) => {
              const isPassed = criterion.test(password);
              return (
                <div
                  key={criterion.id}
                  className={`flex items-center space-x-2 text-sm transition-colors duration-200 ${
                    isPassed ? 'text-green-600' : 'text-gray-500'
                  }`}
                >
                  {isPassed ? (
                    <CheckIcon className="h-4 w-4 text-green-500" />
                  ) : (
                    <XMarkIcon className="h-4 w-4 text-gray-400" />
                  )}
                  <span className={isPassed ? 'line-through' : ''}>
                    {criterion.label}
                  </span>
                </div>
              );
            })}
          </div>
        </div>
      )}

      {/* Security Tips */}
      {password && strength.level !== 'very-strong' && (
        <div className="bg-blue-50 border border-blue-200 rounded-md p-3">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-blue-800">
                Security Tip
              </h3>
              <div className="mt-1 text-sm text-blue-700">
                {getSecurityTip(strength.level)}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

// Helper function to get security tips based on strength level
const getSecurityTip = (level) => {
  const tips = {
    'very-weak': 'Your password is too weak. Add more characters and include numbers, symbols, and mixed case letters.',
    'weak': 'Consider adding special characters and making your password longer for better security.',
    'medium': 'Good start! Add a few more characters or special symbols to make it even stronger.',
    'strong': 'Great password! Consider adding one more special character to make it even more secure.',
    'very-strong': 'Excellent! Your password is very secure.'
  };
  return tips[level] || tips['weak'];
};

export default PasswordStrengthIndicator;
