import React from 'react';
import { useDeviceDetect } from '../../utils/mobileOptimization';

/**
 * Responsive grid component that adapts to different screen sizes
 * 
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Child components
 * @param {string} props.className - Additional CSS classes
 * @param {number} props.cols - Number of columns on desktop
 * @param {number} props.colsSm - Number of columns on small screens
 * @param {number} props.colsMd - Number of columns on medium screens
 * @param {number} props.gap - Gap size between grid items
 * @param {string} props.as - HTML element to render as
 * @param {Object} props.rest - Additional props to pass to the grid
 * @returns {React.ReactElement} Responsive grid component
 */
const Grid = ({ 
  children, 
  className = '', 
  cols = 3, 
  colsSm = 1, 
  colsMd = 2, 
  gap = 4,
  as: Component = 'div',
  ...rest 
}) => {
  const { isMobile, isTablet } = useDeviceDetect();
  
  // Determine the number of columns based on screen size
  const gridCols = isMobile ? colsSm : isTablet ? colsMd : cols;
  
  // Create the grid template columns style
  const gridTemplateColumns = `repeat(${gridCols}, minmax(0, 1fr))`;
  
  // Gap classes based on the gap prop
  const gapClasses = `gap-${gap}`;
  
  return (
    <Component 
      className={`grid ${gapClasses} ${className}`}
      style={{ gridTemplateColumns }}
      {...rest}
    >
      {children}
    </Component>
  );
};

export default Grid;
