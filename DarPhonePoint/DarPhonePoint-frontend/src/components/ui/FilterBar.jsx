import React, { useState } from 'react';
import { Fa<PERSON><PERSON>ch, Fa<PERSON>ilter, FaTimes } from 'react-icons/fa';
import Button from './Button';

/**
 * FilterBar Component
 * Provides a standardized filtering interface for data tables
 * 
 * @param {Array} filters - Array of filter objects with name, label, type, and options
 * @param {Function} onFilterChange - Callback when filters change
 * @param {Function} onSearch - Callback when search term changes
 * @param {Boolean} showSearch - Whether to show the search input
 * @param {String} searchPlaceholder - Placeholder text for search input
 * @param {Boolean} collapsible - Whether the filter bar can be collapsed
 */
const FilterBar = ({
  filters = [],
  onFilterChange,
  onSearch,
  showSearch = true,
  searchPlaceholder = 'Search...',
  collapsible = false
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [filterValues, setFilterValues] = useState({});
  const [showFilters, setShowFilters] = useState(!collapsible);

  // Handle search input change
  const handleSearchChange = (e) => {
    const value = e.target.value;
    setSearchTerm(value);
    if (onSearch) {
      onSearch(value);
    }
  };

  // Handle filter value change
  const handleFilterChange = (name, value) => {
    const newFilterValues = {
      ...filterValues,
      [name]: value
    };
    
    setFilterValues(newFilterValues);
    
    if (onFilterChange) {
      onFilterChange(newFilterValues);
    }
  };

  // Clear all filters
  const handleClearFilters = () => {
    setFilterValues({});
    setSearchTerm('');
    
    if (onFilterChange) {
      onFilterChange({});
    }
    
    if (onSearch) {
      onSearch('');
    }
  };

  // Toggle filters visibility
  const toggleFilters = () => {
    setShowFilters(!showFilters);
  };

  // Render filter input based on type
  const renderFilterInput = (filter) => {
    const { name, type, options } = filter;
    const value = filterValues[name] || '';
    
    switch (type) {
      case 'select':
        return (
          <select
            className="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-purple-500 focus:border-purple-500 sm:text-sm rounded-md"
            value={value}
            onChange={(e) => handleFilterChange(name, e.target.value)}
          >
            <option value="">All</option>
            {options.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        );
      
      case 'date':
        return (
          <input
            type="date"
            className="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-purple-500 focus:border-purple-500 sm:text-sm rounded-md"
            value={value}
            onChange={(e) => handleFilterChange(name, e.target.value)}
          />
        );
      
      case 'checkbox':
        return (
          <div className="flex items-center">
            <input
              type="checkbox"
              className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
              checked={value === true}
              onChange={(e) => handleFilterChange(name, e.target.checked)}
            />
            <span className="ml-2 text-sm text-gray-700">
              {filter.checkboxLabel || filter.label}
            </span>
          </div>
        );
      
      case 'text':
      default:
        return (
          <input
            type="text"
            className="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-purple-500 focus:border-purple-500 sm:text-sm rounded-md"
            value={value}
            onChange={(e) => handleFilterChange(name, e.target.value)}
            placeholder={filter.placeholder || `Filter by ${filter.label}`}
          />
        );
    }
  };

  // Check if any filters are active
  const hasActiveFilters = () => {
    return Object.keys(filterValues).some(key => filterValues[key] !== '' && filterValues[key] !== null && filterValues[key] !== undefined);
  };

  return (
    <div className="bg-white p-4 rounded-lg shadow-sm mb-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-4">
        {/* Search input */}
        {showSearch && (
          <div className="relative w-full md:w-64 mb-4 md:mb-0">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <FaSearch className="h-4 w-4 text-gray-400" />
            </div>
            <input
              type="text"
              className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-purple-500 focus:border-purple-500 sm:text-sm"
              placeholder={searchPlaceholder}
              value={searchTerm}
              onChange={handleSearchChange}
            />
            {searchTerm && (
              <button
                className="absolute inset-y-0 right-0 pr-3 flex items-center"
                onClick={() => {
                  setSearchTerm('');
                  if (onSearch) onSearch('');
                }}
              >
                <FaTimes className="h-4 w-4 text-gray-400 hover:text-gray-600" />
              </button>
            )}
          </div>
        )}

        {/* Filter toggle button for mobile */}
        {collapsible && (
          <Button
            variant="outline"
            onClick={toggleFilters}
            className="md:ml-4"
          >
            <FaFilter className="mr-2 h-4 w-4" />
            {showFilters ? 'Hide Filters' : 'Show Filters'}
          </Button>
        )}

        {/* Clear filters button */}
        {hasActiveFilters() && (
          <Button
            variant="outline"
            onClick={handleClearFilters}
            className="md:ml-4"
          >
            <FaTimes className="mr-2 h-4 w-4" />
            Clear Filters
          </Button>
        )}
      </div>

      {/* Filter inputs */}
      {showFilters && filters.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 mt-4">
          {filters.map((filter) => (
            <div key={filter.name} className="flex flex-col">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {filter.label}
              </label>
              {renderFilterInput(filter)}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default FilterBar;
