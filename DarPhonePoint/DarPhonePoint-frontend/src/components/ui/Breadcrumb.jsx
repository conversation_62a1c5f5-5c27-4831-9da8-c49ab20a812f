import React from 'react';
import { Link } from 'react-router-dom';
import { FaChevronRight, FaHome } from 'react-icons/fa';

/**
 * Breadcrumb Component
 * Displays a navigation path for the current page
 * 
 * @param {Array} items - Array of breadcrumb items with label and path
 * @param {Boolean} showHome - Whether to show the home link at the beginning
 */
const Breadcrumb = ({ items = [], showHome = true }) => {
  return (
    <nav className="flex" aria-label="Breadcrumb">
      <ol className="inline-flex items-center space-x-1 md:space-x-3">
        {showHome && (
          <li className="inline-flex items-center">
            <Link 
              to="/admin" 
              className="inline-flex items-center text-sm font-medium text-gray-700 hover:text-purple-600"
            >
              <FaHome className="mr-2 w-4 h-4" />
              Home
            </Link>
          </li>
        )}
        
        {items.map((item, index) => (
          <li key={index} className="inline-flex items-center">
            <FaChevronRight className="mx-2 w-3 h-3 text-gray-400" />
            {index === items.length - 1 ? (
              <span className="text-sm font-medium text-gray-500">
                {item.label}
              </span>
            ) : (
              <Link 
                to={item.path} 
                className="text-sm font-medium text-gray-700 hover:text-purple-600"
              >
                {item.label}
              </Link>
            )}
          </li>
        ))}
      </ol>
    </nav>
  );
};

export default Breadcrumb;
