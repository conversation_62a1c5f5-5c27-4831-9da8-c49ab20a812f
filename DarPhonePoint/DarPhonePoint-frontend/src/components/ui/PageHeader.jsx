import React from 'react';
import Breadcrumb from './Breadcrumb';

/**
 * PageHeader Component
 * Displays a consistent header for admin pages with title, description, and actions
 * 
 * @param {String} title - Page title
 * @param {String} description - Optional page description
 * @param {Array} breadcrumbs - Array of breadcrumb items
 * @param {React.ReactNode} actions - Optional action buttons
 */
const PageHeader = ({ 
  title, 
  description, 
  breadcrumbs = [], 
  actions 
}) => {
  return (
    <div className="mb-8">
      {breadcrumbs.length > 0 && (
        <div className="mb-4">
          <Breadcrumb items={breadcrumbs} />
        </div>
      )}
      
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
        <div>
          <h1 className="text-3xl font-bold mb-2">{title}</h1>
          {description && <p className="text-gray-600">{description}</p>}
        </div>
        
        {actions && (
          <div className="mt-4 md:mt-0 flex flex-wrap gap-2">
            {actions}
          </div>
        )}
      </div>
    </div>
  );
};

export default PageHeader;
