import React, { forwardRef, useId } from 'react';
import { generateId } from '../../utils/accessibility';

/**
 * Accessible Input Component
 * Provides enhanced accessibility features for form inputs
 */
const AccessibleInput = forwardRef(({
  label,
  type = 'text',
  placeholder,
  value,
  onChange,
  onBlur,
  onFocus,
  error,
  helperText,
  required = false,
  disabled = false,
  readOnly = false,
  autoComplete,
  ariaLabel,
  ariaDescribedBy,
  className = '',
  inputClassName = '',
  labelClassName = '',
  errorClassName = '',
  helperClassName = '',
  size = 'medium',
  variant = 'default',
  icon,
  iconPosition = 'left',
  ...props
}, ref) => {
  
  // Generate unique IDs for accessibility
  const inputId = useId();
  const errorId = error ? `${inputId}-error` : undefined;
  const helperId = helperText ? `${inputId}-helper` : undefined;
  
  // Combine describedby IDs
  const describedBy = [
    ariaDescribedBy,
    errorId,
    helperId
  ].filter(Boolean).join(' ') || undefined;

  // Size styles
  const sizeStyles = {
    small: 'px-3 py-1.5 text-sm',
    medium: 'px-4 py-2 text-base',
    large: 'px-5 py-3 text-lg'
  };

  // Variant styles
  const variantStyles = {
    default: 'border-gray-300 focus:border-blue-500 focus:ring-blue-500',
    error: 'border-red-300 focus:border-red-500 focus:ring-red-500',
    success: 'border-green-300 focus:border-green-500 focus:ring-green-500'
  };

  // Base input styles
  const baseInputStyles = `
    block w-full rounded-md border
    transition-colors duration-200
    focus:outline-none focus:ring-2 focus:ring-offset-0
    disabled:bg-gray-50 disabled:text-gray-500 disabled:cursor-not-allowed
    read-only:bg-gray-50 read-only:cursor-default
    ${icon ? (iconPosition === 'left' ? 'pl-10' : 'pr-10') : ''}
  `;

  // Determine variant based on error state
  const effectiveVariant = error ? 'error' : variant;

  // Combine input styles
  const inputStyles = `
    ${baseInputStyles}
    ${sizeStyles[size] || sizeStyles.medium}
    ${variantStyles[effectiveVariant] || variantStyles.default}
    ${inputClassName}
  `.trim().replace(/\s+/g, ' ');

  // Label styles
  const labelStyles = `
    block text-sm font-medium text-gray-700 mb-1
    ${required ? "after:content-['*'] after:text-red-500 after:ml-1" : ''}
    ${labelClassName}
  `.trim().replace(/\s+/g, ' ');

  // Error styles
  const errorStyles = `
    mt-1 text-sm text-red-600
    ${errorClassName}
  `.trim().replace(/\s+/g, ' ');

  // Helper text styles
  const helperStyles = `
    mt-1 text-sm text-gray-500
    ${helperClassName}
  `.trim().replace(/\s+/g, ' ');

  return (
    <div className={className}>
      {/* Label */}
      {label && (
        <label
          htmlFor={inputId}
          className={labelStyles}
        >
          {label}
          {required && (
            <span className="sr-only">
              (required)
            </span>
          )}
        </label>
      )}

      {/* Input container */}
      <div className="relative">
        {/* Left icon */}
        {icon && iconPosition === 'left' && (
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <span className="text-gray-400" aria-hidden="true">
              {icon}
            </span>
          </div>
        )}

        {/* Input field */}
        <input
          ref={ref}
          id={inputId}
          type={type}
          value={value}
          onChange={onChange}
          onBlur={onBlur}
          onFocus={onFocus}
          placeholder={placeholder}
          required={required}
          disabled={disabled}
          readOnly={readOnly}
          autoComplete={autoComplete}
          aria-label={ariaLabel}
          aria-describedby={describedBy}
          aria-invalid={error ? 'true' : 'false'}
          aria-required={required}
          className={inputStyles}
          {...props}
        />

        {/* Right icon */}
        {icon && iconPosition === 'right' && (
          <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
            <span className="text-gray-400" aria-hidden="true">
              {icon}
            </span>
          </div>
        )}

        {/* Error icon */}
        {error && (
          <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
            <svg
              className="h-5 w-5 text-red-500"
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 20 20"
              fill="currentColor"
              aria-hidden="true"
            >
              <path
                fillRule="evenodd"
                d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                clipRule="evenodd"
              />
            </svg>
          </div>
        )}
      </div>

      {/* Error message */}
      {error && (
        <p
          id={errorId}
          className={errorStyles}
          role="alert"
          aria-live="polite"
        >
          {error}
        </p>
      )}

      {/* Helper text */}
      {helperText && !error && (
        <p
          id={helperId}
          className={helperStyles}
        >
          {helperText}
        </p>
      )}
    </div>
  );
});

AccessibleInput.displayName = 'AccessibleInput';

export default AccessibleInput;
