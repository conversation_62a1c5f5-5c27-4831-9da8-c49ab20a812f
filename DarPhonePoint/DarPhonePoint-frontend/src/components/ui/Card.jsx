import React from 'react';
import { useDeviceDetect } from '../../utils/mobileOptimization';

/**
 * Responsive card component that adapts to different screen sizes
 * 
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Child components
 * @param {string} props.className - Additional CSS classes
 * @param {boolean} props.hover - Whether to apply hover effects
 * @param {string} props.as - HTML element to render as
 * @param {Object} props.rest - Additional props to pass to the card
 * @returns {React.ReactElement} Responsive card component
 */
const Card = ({ 
  children, 
  className = '', 
  hover = true,
  as: Component = 'div',
  ...rest 
}) => {
  const { isMobile } = useDeviceDetect();
  
  // Base classes for the card
  const baseClasses = 'bg-white rounded-lg overflow-hidden';
  
  // Shadow classes based on device type
  const shadowClasses = isMobile 
    ? 'shadow' 
    : 'shadow-md';
  
  // Padding classes based on device type
  const paddingClasses = isMobile 
    ? 'p-4' 
    : 'p-6';
  
  // Hover classes if hover is enabled
  const hoverClasses = hover 
    ? 'transition-transform transition-shadow duration-300 hover:shadow-lg' + 
      (isMobile ? '' : ' hover:-translate-y-1')
    : '';
  
  return (
    <Component 
      className={`${baseClasses} ${shadowClasses} ${paddingClasses} ${hoverClasses} ${className}`}
      {...rest}
    >
      {children}
    </Component>
  );
};

/**
 * Card header component
 */
Card.Header = ({ children, className = '', ...rest }) => {
  const { isMobile } = useDeviceDetect();
  
  const paddingClasses = isMobile 
    ? 'pb-3' 
    : 'pb-4';
  
  return (
    <div 
      className={`border-b border-gray-200 ${paddingClasses} mb-4 ${className}`}
      {...rest}
    >
      {children}
    </div>
  );
};

/**
 * Card body component
 */
Card.Body = ({ children, className = '', ...rest }) => {
  return (
    <div className={className} {...rest}>
      {children}
    </div>
  );
};

/**
 * Card footer component
 */
Card.Footer = ({ children, className = '', ...rest }) => {
  const { isMobile } = useDeviceDetect();
  
  const paddingClasses = isMobile 
    ? 'pt-3' 
    : 'pt-4';
  
  return (
    <div 
      className={`border-t border-gray-200 ${paddingClasses} mt-4 ${className}`}
      {...rest}
    >
      {children}
    </div>
  );
};

export default Card;
