import React from 'react';
import { useLoading } from '../../contexts/LoadingContext';

/**
 * LoadingState Component
 * Displays a loading indicator with optional text
 * Integrates with global loading context
 *
 * @param {String} text - Optional loading text
 * @param {String} size - Size of the spinner (sm, md, lg, xl)
 * @param {String} type - Type of loading state (spinner, skeleton, dots, pulse)
 * @param {Boolean} fullPage - Whether to display the loading state full page
 * @param {String} loadingKey - Key to check in loading context
 * @param {Boolean} showGlobal - Whether to show global loading state
 */
const LoadingState = ({
  text = 'Loading...',
  size = 'md',
  type = 'spinner',
  fullPage = false,
  loadingKey = null,
  showGlobal = false
}) => {
  const { isLoading, globalLoading, isAnyLoading } = useLoading();

  // Determine if we should show loading
  const shouldShowLoading = loadingKey ? isLoading(loadingKey) :
                           showGlobal ? (globalLoading || isAnyLoading()) : true;
  // Don't render if not loading
  if (!shouldShowLoading) {
    return null;
  }

  // Size classes for spinner
  const sizeClasses = {
    sm: 'h-6 w-6 border-2',
    md: 'h-10 w-10 border-2',
    lg: 'h-16 w-16 border-3',
    xl: 'h-20 w-20 border-4',
  };

  // Render spinner loading type
  const renderSpinner = () => (
    <div className={`animate-spin rounded-full ${sizeClasses[size]} border-t-purple-500 border-r-purple-500 border-b-transparent border-l-transparent`}></div>
  );

  // Render dots loading type
  const renderDots = () => (
    <div className="flex space-x-2">
      <div className="animate-bounce delay-75 w-3 h-3 bg-purple-500 rounded-full"></div>
      <div className="animate-bounce delay-150 w-3 h-3 bg-purple-500 rounded-full"></div>
      <div className="animate-bounce delay-300 w-3 h-3 bg-purple-500 rounded-full"></div>
    </div>
  );

  // Render skeleton loading type
  const renderSkeleton = () => (
    <div className="space-y-3">
      <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
      <div className="h-4 bg-gray-200 rounded animate-pulse w-5/6"></div>
      <div className="h-4 bg-gray-200 rounded animate-pulse w-4/6"></div>
    </div>
  );

  // Render pulse loading type
  const renderPulse = () => (
    <div className="flex items-center justify-center">
      <div className={`bg-purple-500 rounded-full animate-pulse ${sizeClasses[size].split(' ')[0]} ${sizeClasses[size].split(' ')[1]}`}></div>
    </div>
  );

  // Render loading indicator based on type
  const renderLoadingIndicator = () => {
    switch (type) {
      case 'dots':
        return renderDots();
      case 'skeleton':
        return renderSkeleton();
      case 'pulse':
        return renderPulse();
      case 'spinner':
      default:
        return renderSpinner();
    }
  };

  // Full page loading state
  if (fullPage) {
    return (
      <div className="fixed inset-0 flex flex-col items-center justify-center bg-white bg-opacity-80 z-50">
        {renderLoadingIndicator()}
        {text && <p className="mt-4 text-gray-600">{text}</p>}
      </div>
    );
  }

  // Inline loading state
  return (
    <div className="flex flex-col items-center justify-center py-8">
      {renderLoadingIndicator()}
      {text && <p className="mt-4 text-gray-600">{text}</p>}
    </div>
  );
};

export default LoadingState;
