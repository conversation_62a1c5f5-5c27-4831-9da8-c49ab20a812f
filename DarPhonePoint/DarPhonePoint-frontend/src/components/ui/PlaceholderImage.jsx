import React from 'react';

/**
 * Local placeholder image component to replace external via.placeholder.com calls
 * Creates a simple colored div with text as a placeholder
 */
const PlaceholderImage = ({ 
  width = 40, 
  height = 40, 
  text = 'AI', 
  backgroundColor = '#6366f1', 
  textColor = '#ffffff',
  className = '',
  style = {},
  ...props 
}) => {
  const placeholderStyle = {
    width: `${width}px`,
    height: `${height}px`,
    backgroundColor,
    color: textColor,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    fontSize: `${Math.min(width, height) * 0.4}px`,
    fontWeight: 'bold',
    borderRadius: '4px',
    flexShrink: 0,
    ...style
  };

  return (
    <div 
      className={`placeholder-image ${className}`}
      style={placeholderStyle}
      {...props}
    >
      {text}
    </div>
  );
};

export default PlaceholderImage;
