import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import AdminLayout from '../layout/AdminLayout';

/**
 * AdminRoute component - Protects routes that should only be accessible by admins
 * Redirects to dashboard if user is logged in but not an admin
 * Redirects to login if user is not logged in
 */
const AdminRoute = ({ children }) => {
  const { user, loading } = useAuth();
  const location = useLocation();

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500"></div>
      </div>
    );
  }

  // If no user is logged in, redirect to login
  if (!user) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  // If user is logged in but not an admin, redirect to dashboard
  if (user.role !== 'admin') {
    return <Navigate to="/dashboard" replace />;
  }

  // If user is an admin, render the protected admin route within AdminLayout
  return <AdminLayout>{children}</AdminLayout>;
};

export default AdminRoute;
