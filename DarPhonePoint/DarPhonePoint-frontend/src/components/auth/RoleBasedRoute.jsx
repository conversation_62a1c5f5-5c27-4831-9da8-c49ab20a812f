import React from 'react';
import { Navigate } from 'react-router-dom';
import PropTypes from 'prop-types';
import { useAuth } from '../../contexts/AuthContext';

/**
 * RoleBasedRoute component for restricting access to routes based on user roles
 * 
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Route content
 * @param {Array} props.allowedRoles - Array of roles allowed to access the route
 * @param {string} props.redirectTo - Path to redirect to if access is denied
 */
const RoleBasedRoute = ({ 
  children, 
  allowedRoles = ['admin'], 
  redirectTo = '/login' 
}) => {
  const { user, isAuthenticated, loading } = useAuth();
  
  // Show loading state while checking authentication
  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen bg-gray-50">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500"></div>
      </div>
    );
  }
  
  // Redirect to login if not authenticated
  if (!isAuthenticated) {
    return <Navigate to={redirectTo} replace />;
  }
  
  // Check if user has required role
  const hasRequiredRole = allowedRoles.some(role => {
    if (role === 'admin') return user.is_admin;
    if (role === 'user') return true; // All authenticated users have 'user' role
    return user.roles?.includes(role);
  });
  
  // Redirect to home if user doesn't have required role
  if (!hasRequiredRole) {
    return <Navigate to="/" replace />;
  }
  
  // Render children if user has required role
  return children;
};

RoleBasedRoute.propTypes = {
  children: PropTypes.node.isRequired,
  allowedRoles: PropTypes.arrayOf(PropTypes.string),
  redirectTo: PropTypes.string
};

export default RoleBasedRoute;
