import React from 'react';
import Button from '../ui/Button';

/**
 * Enhanced Error Boundary for Phone Point Dar
 * Provides comprehensive error handling with user-friendly fallbacks
 */
class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null
    };
  }

  static getDerivedStateFromError(error) {
    // Update state so the next render will show the fallback UI
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    // Generate unique error ID for tracking
    const errorId = `PPD_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    this.setState({
      error,
      errorInfo,
      errorId
    });

    // Log error details
    console.error('Phone Point Dar Error Boundary caught an error:', {
      errorId,
      error: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      props: this.props,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href
    });

    // Send error to monitoring service (if available)
    this.reportError(error, errorInfo, errorId);
  }

  reportError = async (error, errorInfo, errorId) => {
    try {
      // In a real app, you would send this to your error monitoring service
      // For now, we'll just log it
      const errorReport = {
        errorId,
        message: error.message,
        stack: error.stack,
        componentStack: errorInfo.componentStack,
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
        url: window.location.href,
        userId: this.props.userId || 'anonymous',
        sessionId: sessionStorage.getItem('sessionId') || 'unknown'
      };

      // You could send this to services like Sentry, LogRocket, etc.
      console.log('Error Report:', errorReport);
      
      // Store in localStorage for debugging
      const existingErrors = JSON.parse(localStorage.getItem('phonepoint_errors') || '[]');
      existingErrors.push(errorReport);
      
      // Keep only last 10 errors
      if (existingErrors.length > 10) {
        existingErrors.splice(0, existingErrors.length - 10);
      }
      
      localStorage.setItem('phonepoint_errors', JSON.stringify(existingErrors));
    } catch (reportingError) {
      console.error('Failed to report error:', reportingError);
    }
  };

  handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null
    });
  };

  handleReload = () => {
    window.location.reload();
  };

  handleGoHome = () => {
    window.location.href = '/';
  };

  render() {
    if (this.state.hasError) {
      const { error, errorId } = this.state;
      const { fallback: CustomFallback, level = 'page' } = this.props;

      // If a custom fallback is provided, use it
      if (CustomFallback) {
        return (
          <CustomFallback
            error={error}
            errorId={errorId}
            onRetry={this.handleRetry}
            onReload={this.handleReload}
            onGoHome={this.handleGoHome}
          />
        );
      }

      // Different error UIs based on error level
      if (level === 'component') {
        return (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 m-4">
            <div className="flex items-center">
              <i className="fas fa-exclamation-triangle text-red-500 text-xl mr-3"></i>
              <div>
                <h3 className="text-red-800 font-semibold">Component Error</h3>
                <p className="text-red-600 text-sm mt-1">
                  This section couldn't load properly. Please try refreshing.
                </p>
                {errorId && (
                  <p className="text-red-500 text-xs mt-2">Error ID: {errorId}</p>
                )}
              </div>
            </div>
            <div className="mt-3 flex space-x-2">
              <Button
                onClick={this.handleRetry}
                size="sm"
                variant="outline"
                className="text-red-600 border-red-600 hover:bg-red-50"
              >
                <i className="fas fa-redo mr-1"></i>
                Try Again
              </Button>
            </div>
          </div>
        );
      }

      // Full page error UI
      return (
        <div className="min-h-screen bg-gradient-to-br from-blue-50 to-blue-100 flex items-center justify-center p-4">
          <div className="max-w-md w-full bg-white rounded-xl shadow-lg p-8 text-center">
            {/* Phone Point Dar Logo/Icon */}
            <div className="mb-6">
              <i className="fas fa-mobile-alt text-blue-600 text-6xl mb-4"></i>
              <h1 className="text-2xl font-bold text-gray-900">Phone Point Dar</h1>
              <p className="text-gray-600">🇹🇿 Tanzania's Premier Mobile Retailer</p>
            </div>

            {/* Error Message */}
            <div className="mb-6">
              <div className="bg-red-100 rounded-lg p-4 mb-4">
                <i className="fas fa-exclamation-triangle text-red-500 text-3xl mb-2"></i>
                <h2 className="text-xl font-semibold text-red-800 mb-2">
                  Oops! Something went wrong
                </h2>
                <p className="text-red-600">
                  We're sorry for the inconvenience. Our technical team has been notified 
                  and is working to fix this issue.
                </p>
              </div>

              {/* Error Details (only in development) */}
              {process.env.NODE_ENV === 'development' && error && (
                <details className="text-left bg-gray-100 rounded p-3 text-sm">
                  <summary className="cursor-pointer font-medium text-gray-700 mb-2">
                    Technical Details (Development)
                  </summary>
                  <div className="text-gray-600">
                    <p><strong>Error:</strong> {error.message}</p>
                    {errorId && <p><strong>ID:</strong> {errorId}</p>}
                    <pre className="mt-2 text-xs overflow-auto max-h-32">
                      {error.stack}
                    </pre>
                  </div>
                </details>
              )}

              {errorId && (
                <p className="text-gray-500 text-sm mt-3">
                  Reference ID: <code className="bg-gray-100 px-2 py-1 rounded">{errorId}</code>
                </p>
              )}
            </div>

            {/* Action Buttons */}
            <div className="space-y-3">
              <Button
                onClick={this.handleRetry}
                className="w-full bg-blue-600 hover:bg-blue-700 text-white"
              >
                <i className="fas fa-redo mr-2"></i>
                Try Again
              </Button>
              
              <div className="flex space-x-3">
                <Button
                  onClick={this.handleReload}
                  variant="outline"
                  className="flex-1"
                >
                  <i className="fas fa-sync-alt mr-2"></i>
                  Reload Page
                </Button>
                
                <Button
                  onClick={this.handleGoHome}
                  variant="outline"
                  className="flex-1"
                >
                  <i className="fas fa-home mr-2"></i>
                  Go Home
                </Button>
              </div>
            </div>

            {/* Support Information */}
            <div className="mt-6 pt-6 border-t border-gray-200">
              <p className="text-gray-600 text-sm">
                Need help? Contact our support team:
              </p>
              <div className="mt-2 space-y-1 text-sm">
                <p className="text-blue-600">
                  <i className="fas fa-phone mr-1"></i>
                  +255 XXX XXX XXX
                </p>
                <p className="text-blue-600">
                  <i className="fas fa-envelope mr-1"></i>
                  <EMAIL>
                </p>
              </div>
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

/**
 * Higher-order component to wrap components with error boundary
 */
export const withErrorBoundary = (Component, errorBoundaryProps = {}) => {
  const WrappedComponent = (props) => (
    <ErrorBoundary {...errorBoundaryProps}>
      <Component {...props} />
    </ErrorBoundary>
  );

  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`;
  return WrappedComponent;
};

/**
 * Hook to manually report errors
 */
export const useErrorReporting = () => {
  const reportError = (error, context = {}) => {
    const errorId = `PPD_MANUAL_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    console.error('Manual error report:', {
      errorId,
      error: error.message || error,
      context,
      timestamp: new Date().toISOString(),
      url: window.location.href
    });

    // Store in localStorage for debugging
    try {
      const existingErrors = JSON.parse(localStorage.getItem('phonepoint_errors') || '[]');
      existingErrors.push({
        errorId,
        message: error.message || error,
        context,
        timestamp: new Date().toISOString(),
        url: window.location.href,
        type: 'manual'
      });
      
      if (existingErrors.length > 10) {
        existingErrors.splice(0, existingErrors.length - 10);
      }
      
      localStorage.setItem('phonepoint_errors', JSON.stringify(existingErrors));
    } catch (e) {
      console.error('Failed to store error report:', e);
    }

    return errorId;
  };

  return { reportError };
};

export default ErrorBoundary;
