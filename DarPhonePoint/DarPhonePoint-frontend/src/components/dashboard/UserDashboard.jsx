import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, Spin<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Tab } from 'react-bootstrap';
import { Link } from 'react-router-dom';
import api from '../../api';
import { useAuth } from '../../contexts/AuthContext';
import './UserDashboard.css';

const UserDashboard = () => {
  const { currentUser } = useAuth();
  const [orders, setOrders] = useState([]);
  const [products, setProducts] = useState([]);

  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [activeTab, setActiveTab] = useState('products');

  useEffect(() => {
    const fetchUserData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Fetch user orders
        const ordersResponse = await api.orders.getUserOrders();
        setOrders(ordersResponse.data.data);

        // Extract products from orders
        const productIds = new Set();
        const userProducts = [];

        ordersResponse.data.data.forEach(order => {
          order.products.forEach(item => {
            if (!productIds.has(item.product._id)) {
              productIds.add(item.product._id);
              userProducts.push(item.product);
            }
          });
        });

        setProducts(userProducts);



        setLoading(false);
      } catch (err) {
        console.error('Error fetching user data:', err);
        setError('Failed to load your dashboard data. Please try again later.');
        setLoading(false);
      }
    };

    if (currentUser) {
      fetchUserData();
    }
  }, [currentUser]);





  const formatDate = (dateString) => {
    const options = { year: 'numeric', month: 'long', day: 'numeric' };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  if (!currentUser) {
    return (
      <Container className="mt-5">
        <Alert variant="warning">
          Please <Link to="/login">log in</Link> to view your dashboard.
        </Alert>
      </Container>
    );
  }

  if (loading) {
    return (
      <Container className="mt-5 text-center">
        <Spinner animation="border" role="status">
          <span className="visually-hidden">Loading...</span>
        </Spinner>
      </Container>
    );
  }

  return (
    <Container className="my-5 dashboard-container">
      <h1 className="mb-4">My Dashboard</h1>

      {error && <Alert variant="danger">{error}</Alert>}

      <Tabs
        activeKey={activeTab}
        onSelect={(k) => setActiveTab(k)}
        className="mb-4"
      >
        <Tab eventKey="products" title="My Products">
          <Row>
            {products.length === 0 ? (
              <Col>
                <Alert variant="info">
                  You haven't purchased any products yet. <Link to="/products">Browse our products</Link>
                </Alert>
              </Col>
            ) : (
              products.map(product => (
                <Col md={6} lg={4} key={product._id} className="mb-4">
                  <Card className="h-100 product-card">
                    <Card.Body>
                      <Card.Title>{product.name}</Card.Title>
                      <Card.Text>{product.description.substring(0, 100)}...</Card.Text>


                    </Card.Body>
                    <Card.Footer className="d-flex justify-content-between">
                      <Button
                        variant="primary"
                        as={Link}
                        to={`/products/${product._id}`}
                      >
                        View Product
                      </Button>
                      <Button
                        variant="outline-primary"
                        as={Link}
                        to={`/downloads/${product._id}`}
                      >
                        Download
                      </Button>
                    </Card.Footer>
                  </Card>
                </Col>
              ))
            )}
          </Row>
        </Tab>

        <Tab eventKey="orders" title="My Orders">
          <Row>
            {orders.length === 0 ? (
              <Col>
                <Alert variant="info">
                  You haven't placed any orders yet. <Link to="/products">Browse our products</Link>
                </Alert>
              </Col>
            ) : (
              orders.map(order => (
                <Col md={12} key={order._id} className="mb-4">
                  <Card className="order-card">
                    <Card.Header>
                      <div className="d-flex justify-content-between">
                        <span>Order #{order._id.substring(order._id.length - 8)}</span>
                        <span>{formatDate(order.created_at)}</span>
                      </div>
                    </Card.Header>
                    <Card.Body>
                      <Row>
                        <Col md={8}>
                          <h5>Products</h5>
                          <ul className="list-unstyled">
                            {order.products.map((item, index) => (
                              <li key={index} className="mb-2">
                                {item.product.name} x {item.quantity}
                              </li>
                            ))}
                          </ul>
                        </Col>
                        <Col md={4} className="text-end">
                          <h5>Total</h5>
                          <p className="order-total">${order.total_amount.toFixed(2)}</p>
                          <span className={`order-status status-${order.status.toLowerCase()}`}>
                            {order.status}
                          </span>
                        </Col>
                      </Row>
                    </Card.Body>
                  </Card>
                </Col>
              ))
            )}
          </Row>
        </Tab>
      </Tabs>
    </Container>
  );
};

export default UserDashboard;
