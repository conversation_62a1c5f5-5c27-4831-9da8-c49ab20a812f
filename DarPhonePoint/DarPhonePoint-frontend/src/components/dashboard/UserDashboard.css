.dashboard-container {
  padding: 2rem 0;
}

.dashboard-header {
  margin-bottom: 2rem;
}

.dashboard-welcome {
  font-size: 1.5rem;
  color: #111827;
  margin-bottom: 0.5rem;
}

.dashboard-subtitle {
  color: #6b7280;
  margin-bottom: 1.5rem;
}

.dashboard-stats {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2.5rem;
}

.dashboard-stat-card {
  background-color: #ffffff;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  transition: transform 0.2s, box-shadow 0.2s;
}

.dashboard-stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.dashboard-stat-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
}

.dashboard-stat-icon.purple {
  background-color: #f5f3ff;
  color: #6b21a8;
}

.dashboard-stat-icon.blue {
  background-color: #eff6ff;
  color: #1d4ed8;
}

.dashboard-stat-icon.green {
  background-color: #ecfdf5;
  color: #047857;
}

.dashboard-stat-icon.amber {
  background-color: #fffbeb;
  color: #b45309;
}

.dashboard-stat-value {
  font-size: 1.5rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 0.25rem;
}

.dashboard-stat-label {
  color: #6b7280;
  font-size: 0.875rem;
}

.dashboard-section {
  margin-bottom: 2.5rem;
}

.dashboard-section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.dashboard-section-title {
  font-size: 1.25rem;
  color: #111827;
  font-weight: 600;
}

.dashboard-section-action {
  color: #6b21a8;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
}

.dashboard-section-action svg {
  margin-left: 0.25rem;
}

.dashboard-card {
  background-color: #ffffff;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}

.dashboard-card-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 1rem;
}

.dashboard-card-title {
  font-weight: 500;
  color: #111827;
}

.dashboard-card-subtitle {
  color: #6b7280;
  font-size: 0.875rem;
}

.dashboard-empty {
  text-align: center;
  padding: 3rem 0;
  color: #6b7280;
}

.dashboard-empty svg {
  color: #d1d5db;
  margin-bottom: 1rem;
}

.dashboard-empty-title {
  font-weight: 500;
  color: #4b5563;
  margin-bottom: 0.5rem;
}

.dashboard-empty-message {
  color: #6b7280;
  margin-bottom: 1.5rem;
}

@media (max-width: 768px) {
  .dashboard-stats {
    grid-template-columns: 1fr;
  }
  
  .dashboard-section-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .dashboard-section-action {
    margin-top: 0.5rem;
  }
}
