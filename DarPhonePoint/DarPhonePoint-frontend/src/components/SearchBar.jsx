import React, { useState, useEffect, useRef, useId } from 'react';
import { useNavigate } from 'react-router-dom';
import { safeApiRequest } from '../api/apiClient';
import { ARIA_LABELS, announceToScreenReader, addKeyboardNavigation } from '../utils/accessibility';

const SearchBar = ({ placeholder = "Search for phones, brands, or features...", className = "" }) => {
  const [query, setQuery] = useState('');
  const [suggestions, setSuggestions] = useState([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [loading, setLoading] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(-1);

  const searchRef = useRef(null);
  const suggestionsRef = useRef(null);
  const navigate = useNavigate();

  // Generate unique IDs for accessibility
  const searchId = useId();
  const suggestionsId = `${searchId}-suggestions`;
  const statusId = `${searchId}-status`;

  // Debounce search suggestions
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (query.length >= 2) {
        fetchSuggestions(query);
      } else {
        setSuggestions([]);
        setShowSuggestions(false);
      }
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [query]);

  // Handle clicks outside to close suggestions
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (searchRef.current && !searchRef.current.contains(event.target)) {
        setShowSuggestions(false);
        setSelectedIndex(-1);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const fetchSuggestions = async (searchQuery) => {
    setLoading(true);
    try {
      const response = await safeApiRequest({
        method: 'GET',
        url: `/products/search/suggestions?q=${encodeURIComponent(searchQuery)}`
      });

      if (response.data && response.data.data) {
        const { suggestions: products, brands, categories } = response.data.data;
        
        // Combine all suggestions
        const allSuggestions = [
          ...products.map(item => ({ ...item, type: 'product' })),
          ...brands.map(item => ({ ...item, type: 'brand' })),
          ...categories.map(item => ({ ...item, type: 'category' }))
        ];

        setSuggestions(allSuggestions);
        setShowSuggestions(allSuggestions.length > 0);
      }
    } catch (error) {
      console.error('Error fetching suggestions:', error);
      setSuggestions([]);
      setShowSuggestions(false);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (searchQuery = query) => {
    if (searchQuery.trim()) {
      navigate(`/products?search=${encodeURIComponent(searchQuery.trim())}`);
      setShowSuggestions(false);
      setSelectedIndex(-1);
    }
  };

  const handleSuggestionClick = (suggestion) => {
    if (suggestion.type === 'product') {
      setQuery(suggestion.suggestion);
      handleSearch(suggestion.suggestion);
    } else if (suggestion.type === 'brand') {
      navigate(`/products?brand=${encodeURIComponent(suggestion.suggestion)}`);
    } else if (suggestion.type === 'category') {
      navigate(`/products?category=${encodeURIComponent(suggestion.suggestion)}`);
    }
    setShowSuggestions(false);
    setSelectedIndex(-1);
  };

  const handleKeyDown = (e) => {
    if (!showSuggestions || suggestions.length === 0) {
      if (e.key === 'Enter') {
        handleSearch();
      }
      return;
    }

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedIndex(prev => 
          prev < suggestions.length - 1 ? prev + 1 : prev
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedIndex(prev => prev > 0 ? prev - 1 : -1);
        break;
      case 'Enter':
        e.preventDefault();
        if (selectedIndex >= 0 && selectedIndex < suggestions.length) {
          handleSuggestionClick(suggestions[selectedIndex]);
        } else {
          handleSearch();
        }
        break;
      case 'Escape':
        setShowSuggestions(false);
        setSelectedIndex(-1);
        break;
      default:
        break;
    }
  };

  const getSuggestionIcon = (type) => {
    switch (type) {
      case 'product':
        return 'fas fa-mobile-alt';
      case 'brand':
        return 'fas fa-tag';
      case 'category':
        return 'fas fa-folder';
      default:
        return 'fas fa-search';
    }
  };

  const getSuggestionLabel = (type) => {
    switch (type) {
      case 'product':
        return 'Product';
      case 'brand':
        return 'Brand';
      case 'category':
        return 'Category';
      default:
        return '';
    }
  };

  return (
    <div ref={searchRef} className={`relative ${className}`} role="search">
      <label htmlFor={searchId} className="sr-only">
        {ARIA_LABELS.search}
      </label>

      <div className="relative">
        <input
          id={searchId}
          type="text"
          placeholder={placeholder}
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          onKeyDown={handleKeyDown}
          onFocus={() => {
            if (suggestions.length > 0) {
              setShowSuggestions(true);
            }
          }}
          className="w-full px-4 py-3 pl-12 pr-12 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          aria-label={ARIA_LABELS.search}
          aria-describedby={statusId}
          aria-expanded={showSuggestions}
          aria-autocomplete="list"
          aria-controls={showSuggestions ? suggestionsId : undefined}
          aria-activedescendant={selectedIndex >= 0 ? `${suggestionsId}-${selectedIndex}` : undefined}
          autoComplete="off"
          role="combobox"
        />
        
        {/* Search Icon */}
        <div className="absolute left-4 top-1/2 transform -translate-y-1/2" aria-hidden="true">
          <i className="fas fa-search text-gray-400"></i>
        </div>

        {/* Loading Spinner */}
        {loading && (
          <div className="absolute right-4 top-1/2 transform -translate-y-1/2" aria-hidden="true">
            <i className="fas fa-spinner fa-spin text-gray-400"></i>
          </div>
        )}

        {/* Search Button */}
        {!loading && query && (
          <button
            onClick={() => handleSearch()}
            className="absolute right-4 top-1/2 transform -translate-y-1/2 text-blue-600 hover:text-blue-800 focus:outline-none focus:ring-2 focus:ring-blue-500 rounded"
            aria-label="Submit search"
            type="button"
          >
            <i className="fas fa-arrow-right" aria-hidden="true"></i>
          </button>
        )}

        {/* Screen reader status */}
        <div id={statusId} className="sr-only" aria-live="polite" aria-atomic="true">
          {loading && 'Loading search suggestions'}
          {showSuggestions && suggestions.length > 0 && `${suggestions.length} suggestions available`}
          {showSuggestions && suggestions.length === 0 && query.length >= 2 && 'No suggestions found'}
        </div>
      </div>

      {/* Suggestions Dropdown */}
      {showSuggestions && suggestions.length > 0 && (
        <div
          ref={suggestionsRef}
          id={suggestionsId}
          className="absolute top-full left-0 right-0 bg-white border border-gray-200 rounded-lg shadow-lg mt-1 z-50 max-h-96 overflow-y-auto"
          role="listbox"
          aria-label="Search suggestions"
        >
          {suggestions.map((suggestion, index) => (
            <button
              key={`${suggestion.type}-${suggestion.suggestion}-${index}`}
              id={`${suggestionsId}-${index}`}
              onClick={() => handleSuggestionClick(suggestion)}
              className={`w-full px-4 py-3 text-left hover:bg-gray-50 flex items-center justify-between transition-colors focus:outline-none focus:bg-blue-50 ${
                selectedIndex === index ? 'bg-blue-50 border-l-4 border-blue-500' : ''
              }`}
              role="option"
              aria-selected={selectedIndex === index}
              tabIndex={-1}
            >
              <div className="flex items-center space-x-3">
                <i className={`${getSuggestionIcon(suggestion.type)} text-gray-400`}></i>
                <div>
                  <div className="font-medium text-gray-900">
                    {suggestion.suggestion}
                  </div>
                  {suggestion.brand && suggestion.type === 'product' && (
                    <div className="text-sm text-gray-500">
                      {suggestion.brand}
                    </div>
                  )}
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <span className="text-xs text-gray-400 capitalize">
                  {getSuggestionLabel(suggestion.type)}
                </span>
                {suggestion.count && (
                  <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full">
                    {suggestion.count}
                  </span>
                )}
              </div>
            </button>
          ))}
          
          {/* Search all results option */}
          <button
            onClick={() => handleSearch()}
            className="w-full px-4 py-3 text-left border-t border-gray-200 hover:bg-gray-50 flex items-center space-x-3 text-blue-600 font-medium"
          >
            <i className="fas fa-search"></i>
            <span>Search for "{query}"</span>
          </button>
        </div>
      )}
    </div>
  );
};

export default SearchBar;
