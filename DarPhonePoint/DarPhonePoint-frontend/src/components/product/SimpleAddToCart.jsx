import React from 'react';
import Button from '../ui/Button';
import Alert from '../ui/Alert';
import { formatPrice } from '../../utils/priceFormatter';
import { useDeviceDetect } from '../../utils/mobileOptimization';

const SimpleAddToCart = ({ 
  product,
  selectedVariant,
  quantity,
  onQuantityChange,
  onAddToCart,
  addingToCart,
  addToCartSuccess,
  error,
  getCurrentStock,
  requiresSelection = false,
  selectionMessage = "Please make a selection above"
}) => {
  const { isMobile } = useDeviceDetect();

  const currentStock = getCurrentStock();
  const isInStock = currentStock > 0;
  const currentPrice = selectedVariant?.price || product?.price || 0;
  const totalPrice = currentPrice * quantity;

  const handleQuantityChange = (newQuantity) => {
    if (newQuantity >= 1 && newQuantity <= currentStock) {
      onQuantityChange(newQuantity);
    }
  };

  const canAddToCart = () => {
    return isInStock && !requiresSelection && quantity > 0 && quantity <= currentStock;
  };

  return (
    <div className="bg-gray-50 rounded-lg p-4">
      {/* Quantity Selection */}
      <div className="mb-4">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Quantity
        </label>
        <div className="flex items-center space-x-3">
          <button
            onClick={() => handleQuantityChange(quantity - 1)}
            disabled={quantity <= 1}
            className="w-10 h-10 border border-gray-300 rounded-md flex items-center justify-center hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            <i className="fas fa-minus text-sm"></i>
          </button>
          
          <div className="flex-1 max-w-20">
            <input
              type="number"
              min="1"
              max={currentStock}
              value={quantity}
              onChange={(e) => handleQuantityChange(parseInt(e.target.value) || 1)}
              className="w-full text-center border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
          
          <button
            onClick={() => handleQuantityChange(quantity + 1)}
            disabled={quantity >= currentStock}
            className="w-10 h-10 border border-gray-300 rounded-md flex items-center justify-center hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            <i className="fas fa-plus text-sm"></i>
          </button>
          
          <span className="text-sm text-gray-500 ml-4">
            Max: {currentStock}
          </span>
        </div>
      </div>

      {/* Price Summary */}
      <div className="mb-4 p-3 bg-white border border-gray-200 rounded-lg">
        <div className="flex justify-between items-center">
          <div>
            <p className="text-sm text-gray-600">Unit Price:</p>
            <p className="text-lg font-semibold text-gray-900">
              {formatPrice(currentPrice)}
            </p>
          </div>
          {quantity > 1 && (
            <div className="text-right">
              <p className="text-sm text-gray-600">Total ({quantity} items):</p>
              <p className="text-xl font-bold text-blue-600">
                {formatPrice(totalPrice)}
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Stock Information */}
      <div className="mb-4">
        {isInStock ? (
          <div className="flex items-center text-sm">
            <i className="fas fa-check-circle text-green-600 mr-2"></i>
            <span className="text-green-700">
              {currentStock > 10 ? 'In Stock' : `Only ${currentStock} left in stock`}
            </span>
          </div>
        ) : (
          <div className="flex items-center text-sm">
            <i className="fas fa-times-circle text-red-600 mr-2"></i>
            <span className="text-red-700">Out of Stock</span>
          </div>
        )}
      </div>

      {/* Error Messages */}
      {error && (
        <Alert
          type="error"
          message={error}
          className="mb-4"
        />
      )}

      {/* Selection Required Message */}
      {requiresSelection && (
        <Alert
          type="warning"
          message={selectionMessage}
          className="mb-4"
        />
      )}

      {/* Success Message */}
      {addToCartSuccess && (
        <Alert
          type="success"
          message="Item added to cart successfully!"
          className="mb-4"
        />
      )}

      {/* Add to Cart Button */}
      <Button
        onClick={onAddToCart}
        disabled={!canAddToCart() || addingToCart}
        className={`w-full py-3 text-lg font-semibold ${isMobile ? 'text-base' : 'text-lg'}`}
        variant={canAddToCart() ? 'primary' : 'secondary'}
      >
        {addingToCart ? (
          <>
            <i className="fas fa-spinner fa-spin mr-2"></i>
            Adding to Cart...
          </>
        ) : !isInStock ? (
          <>
            <i className="fas fa-times-circle mr-2"></i>
            Out of Stock
          </>
        ) : requiresSelection ? (
          <>
            <i className="fas fa-hand-pointer mr-2"></i>
            Make Selection First
          </>
        ) : (
          <>
            <i className="fas fa-shopping-cart mr-2"></i>
            Add to Cart
            {quantity > 1 && ` (${quantity} items)`}
          </>
        )}
      </Button>

      {/* Additional Information */}
      <div className="mt-4 space-y-2 text-xs text-gray-500">
        <div className="flex items-center">
          <i className="fas fa-truck mr-2"></i>
          <span>Free shipping on orders over TZS 100,000</span>
        </div>
        <div className="flex items-center">
          <i className="fas fa-shield-alt mr-2"></i>
          <span>Warranty included with purchase</span>
        </div>
        <div className="flex items-center">
          <i className="fas fa-undo mr-2"></i>
          <span>30-day return policy</span>
        </div>
      </div>

      {/* Bulk Purchase Info */}
      {currentStock >= 10 && (
        <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
          <h5 className="text-sm font-medium text-blue-800 mb-1">
            <i className="fas fa-boxes mr-1"></i>
            Bulk Purchase Available
          </h5>
          <p className="text-xs text-blue-700">
            Need larger quantities? Contact us for bulk pricing and availability.
          </p>
        </div>
      )}
    </div>
  );
};

export default SimpleAddToCart;
