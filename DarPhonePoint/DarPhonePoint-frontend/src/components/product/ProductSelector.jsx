import React, { useState, useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import IMEISelector from './IMEISelector';
import VariantSelector from './VariantSelector';
import SimpleAddToCart from './SimpleAddToCart';

/**
 * Main Product Selector Component
 * Determines which type of selection interface to show based on product characteristics
 */
const ProductSelector = ({ 
  product, 
  selectedVariant, 
  onVariantSelect,
  selectedPhone, 
  onPhoneSelect,
  quantity,
  onQuantityChange,
  onAddToCart,
  addingToCart,
  addToCartSuccess,
  error
}) => {
  const { user } = useAuth();

  // Determine product type and selection requirements
  const getProductType = () => {
    if (!product) return 'simple';

    // Check if product requires IMEI tracking (smartphones)
    if (product.category === 'smartphone') {
      return 'imei-required';
    }

    // Check if product has meaningful variants
    if (product.variants && product.variants.length > 1) {
      // Check if variants have different properties (not just SKU differences)
      const hasVariantDifferences = product.variants.some(variant => 
        variant.color || variant.size || variant.storage || variant.capacity || variant.length
      );
      
      if (hasVariantDifferences) {
        return 'variant-based';
      }
    }

    // Default to simple product
    return 'simple';
  };

  const productType = getProductType();

  // Check if product is in stock
  const isInStock = () => {
    if (selectedVariant) {
      return selectedVariant.stock_quantity > 0;
    }
    return product?.stock_quantity > 0;
  };

  // Get current stock level
  const getCurrentStock = () => {
    if (selectedVariant) {
      return selectedVariant.stock_quantity || 0;
    }
    return product?.stock_quantity || 0;
  };

  // Render appropriate selector based on product type
  const renderSelector = () => {
    if (!isInStock()) {
      return (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 text-center">
          <i className="fas fa-exclamation-triangle text-red-600 text-2xl mb-2"></i>
          <p className="text-red-800 font-medium">Out of Stock</p>
          <p className="text-red-600 text-sm">This product is currently unavailable</p>
        </div>
      );
    }

    switch (productType) {
      case 'imei-required':
        return (
          <div className="space-y-4">
            {/* Variant Selection for Smartphones */}
            {product.variants && product.variants.length > 1 && (
              <VariantSelector
                product={product}
                selectedVariant={selectedVariant}
                onVariantSelect={onVariantSelect}
              />
            )}
            
            {/* IMEI Selection */}
            {selectedVariant && (
              <IMEISelector
                product={product}
                selectedVariant={selectedVariant}
                onPhoneSelect={onPhoneSelect}
                selectedPhone={selectedPhone}
                user={user}
              />
            )}
            
            {/* Quantity and Add to Cart */}
            <SimpleAddToCart
              product={product}
              selectedVariant={selectedVariant}
              quantity={quantity}
              onQuantityChange={onQuantityChange}
              onAddToCart={onAddToCart}
              addingToCart={addingToCart}
              addToCartSuccess={addToCartSuccess}
              error={error}
              getCurrentStock={getCurrentStock}
              requiresSelection={!selectedPhone && user} // Require phone selection for authenticated users
              selectionMessage="Please select a specific device above"
            />
          </div>
        );

      case 'variant-based':
        return (
          <div className="space-y-4">
            {/* Variant Selection */}
            <VariantSelector
              product={product}
              selectedVariant={selectedVariant}
              onVariantSelect={onVariantSelect}
            />
            
            {/* Quantity and Add to Cart */}
            <SimpleAddToCart
              product={product}
              selectedVariant={selectedVariant}
              quantity={quantity}
              onQuantityChange={onQuantityChange}
              onAddToCart={onAddToCart}
              addingToCart={addingToCart}
              addToCartSuccess={addToCartSuccess}
              error={error}
              getCurrentStock={getCurrentStock}
              requiresSelection={!selectedVariant}
              selectionMessage="Please select a variant above"
            />
          </div>
        );

      case 'simple':
      default:
        return (
          <SimpleAddToCart
            product={product}
            selectedVariant={selectedVariant}
            quantity={quantity}
            onQuantityChange={onQuantityChange}
            onAddToCart={onAddToCart}
            addingToCart={addingToCart}
            addToCartSuccess={addToCartSuccess}
            error={error}
            getCurrentStock={getCurrentStock}
            requiresSelection={false}
          />
        );
    }
  };

  return (
    <div className="bg-white rounded-lg border p-6">
      <div className="mb-4">
        <h3 className="text-lg font-semibold text-gray-900 flex items-center">
          {productType === 'imei-required' && (
            <i className="fas fa-mobile-alt text-blue-600 mr-2"></i>
          )}
          {productType === 'variant-based' && (
            <i className="fas fa-palette text-purple-600 mr-2"></i>
          )}
          {productType === 'simple' && (
            <i className="fas fa-shopping-cart text-green-600 mr-2"></i>
          )}
          
          {productType === 'imei-required' && 'Select Your Device'}
          {productType === 'variant-based' && 'Choose Options'}
          {productType === 'simple' && 'Add to Cart'}
        </h3>
        
        {productType === 'imei-required' && (
          <p className="text-sm text-gray-600 mt-1">
            Each device is individually tracked with IMEI for warranty and authenticity
          </p>
        )}
        {productType === 'variant-based' && (
          <p className="text-sm text-gray-600 mt-1">
            Select your preferred options from the available variants
          </p>
        )}
      </div>

      {renderSelector()}
    </div>
  );
};

export default ProductSelector;
