import React from 'react';
import { formatPrice } from '../../utils/priceFormatter';
import { useDeviceDetect } from '../../utils/mobileOptimization';

const VariantSelector = ({ product, selectedVariant, onVariantSelect }) => {
  const { isMobile } = useDeviceDetect();

  if (!product?.variants || product.variants.length <= 1) {
    return null;
  }

  // Group variants by type for better organization
  const getVariantGroups = () => {
    const groups = {};
    
    product.variants.forEach(variant => {
      // Determine the primary differentiator
      if (variant.storage) {
        if (!groups.storage) groups.storage = [];
        groups.storage.push(variant);
      } else if (variant.color) {
        if (!groups.color) groups.color = [];
        groups.color.push(variant);
      } else if (variant.size) {
        if (!groups.size) groups.size = [];
        groups.size.push(variant);
      } else if (variant.capacity) {
        if (!groups.capacity) groups.capacity = [];
        groups.capacity.push(variant);
      } else if (variant.length) {
        if (!groups.length) groups.length = [];
        groups.length.push(variant);
      } else {
        if (!groups.other) groups.other = [];
        groups.other.push(variant);
      }
    });

    return groups;
  };

  const variantGroups = getVariantGroups();

  const renderVariantGroup = (groupName, variants, label) => {
    if (!variants || variants.length === 0) return null;

    return (
      <div key={groupName} className="mb-4">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          {label}
        </label>
        <div className={`grid gap-2 ${isMobile ? 'grid-cols-2' : 'grid-cols-3 md:grid-cols-4'}`}>
          {variants.map((variant) => {
            const isSelected = selectedVariant?.sku === variant.sku;
            const isOutOfStock = variant.stock_quantity === 0;
            
            return (
              <button
                key={variant.sku}
                onClick={() => !isOutOfStock && onVariantSelect(variant)}
                disabled={isOutOfStock}
                className={`
                  relative p-3 border rounded-lg text-left transition-all
                  ${isSelected 
                    ? 'border-blue-500 bg-blue-50 ring-2 ring-blue-200' 
                    : 'border-gray-200 hover:border-blue-300 hover:bg-gray-50'
                  }
                  ${isOutOfStock 
                    ? 'opacity-50 cursor-not-allowed bg-gray-100' 
                    : 'cursor-pointer'
                  }
                `}
              >
                {/* Variant Label */}
                <div className="font-medium text-sm text-gray-900">
                  {variant.name || getVariantLabel(variant, groupName)}
                </div>
                
                {/* Price if different from base */}
                {variant.price !== product.price && (
                  <div className="text-sm text-blue-600 font-medium mt-1">
                    {formatPrice(variant.price)}
                  </div>
                )}
                
                {/* Stock indicator */}
                <div className="text-xs text-gray-500 mt-1">
                  {isOutOfStock ? (
                    <span className="text-red-600">Out of stock</span>
                  ) : variant.stock_quantity <= 5 ? (
                    <span className="text-orange-600">
                      Only {variant.stock_quantity} left
                    </span>
                  ) : (
                    <span className="text-green-600">In stock</span>
                  )}
                </div>

                {/* Selected indicator */}
                {isSelected && (
                  <div className="absolute top-2 right-2">
                    <i className="fas fa-check-circle text-blue-600"></i>
                  </div>
                )}
              </button>
            );
          })}
        </div>
      </div>
    );
  };

  const getVariantLabel = (variant, groupName) => {
    switch (groupName) {
      case 'storage':
        return variant.storage;
      case 'color':
        return variant.color;
      case 'size':
        return variant.size;
      case 'capacity':
        return variant.capacity;
      case 'length':
        return variant.length;
      default:
        return variant.name || 'Option';
    }
  };

  const getGroupLabel = (groupName) => {
    switch (groupName) {
      case 'storage':
        return 'Storage Capacity';
      case 'color':
        return 'Color';
      case 'size':
        return 'Size';
      case 'capacity':
        return 'Capacity';
      case 'length':
        return 'Length';
      default:
        return 'Options';
    }
  };

  return (
    <div className="bg-gray-50 rounded-lg p-4">
      <h4 className="text-md font-semibold text-gray-900 mb-3 flex items-center">
        <i className="fas fa-palette text-purple-600 mr-2"></i>
        Choose Your Options
      </h4>
      
      {Object.entries(variantGroups).map(([groupName, variants]) =>
        renderVariantGroup(groupName, variants, getGroupLabel(groupName))
      )}

      {/* Selected variant summary */}
      {selectedVariant && (
        <div className="mt-4 p-3 bg-white border border-blue-200 rounded-lg">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-900">
                Selected: {selectedVariant.name}
              </p>
              <p className="text-sm text-gray-600">
                SKU: {selectedVariant.sku}
              </p>
            </div>
            <div className="text-right">
              <p className="text-lg font-bold text-blue-600">
                {formatPrice(selectedVariant.price)}
              </p>
              <p className="text-xs text-gray-500">
                {selectedVariant.stock_quantity} available
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Variant benefits info */}
      <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
        <h5 className="text-sm font-medium text-blue-800 mb-2">
          <i className="fas fa-info-circle mr-1"></i>
          Why Choose Specific Variants?
        </h5>
        <ul className="text-xs text-blue-700 space-y-1">
          <li>• Different variants may have different prices</li>
          <li>• Stock levels vary by variant</li>
          <li>• Ensures you get exactly what you want</li>
          <li>• Some variants may have special features</li>
        </ul>
      </div>
    </div>
  );
};

export default VariantSelector;
