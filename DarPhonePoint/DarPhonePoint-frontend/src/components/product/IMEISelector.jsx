import React, { useState, useEffect } from 'react';
import { get } from '../../api/unifiedApiClient';
import { formatPrice } from '../../utils/priceFormatter';
import Button from '../ui/Button';
import Alert from '../ui/Alert';
import { useDeviceDetect } from '../../utils/mobileOptimization';

const IMEISelector = ({ product, selectedVariant, onPhoneSelect, selectedPhone, user }) => {
  const { isMobile } = useDeviceDetect();
  const [availablePhones, setAvailablePhones] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  useEffect(() => {
    let isMounted = true;
    const abortController = new AbortController();

    const fetchAvailablePhones = async () => {
      if (!product || !selectedVariant) return;

      setLoading(true);
      setError('');

      try {
        const response = await safeApiRequest({
          method: 'GET',
          url: `/phone-inventory/phones/${product._id}/${selectedVariant.sku}`,
          signal: abortController.signal
        });

        if (isMounted) {
          setAvailablePhones(response.data || []);
        }
      } catch (error) {
        if (error.name === 'AbortError') {
          return; // Request was cancelled, don't update state
        }

        if (isMounted) {
          // For guests, don't show error - just show that IMEI selection is available after login
          if (!user) {
            setAvailablePhones([]);
          } else {
            setError('Failed to load available phones');
          }
          console.error('Error fetching available phones:', error);
        }
      } finally {
        if (isMounted) {
          setLoading(false);
        }
      }
    };

    if (product && selectedVariant) {
      fetchAvailablePhones();
    }

    return () => {
      isMounted = false;
      abortController.abort();
    };
  }, [product, selectedVariant, user]);

  const fetchAvailablePhones = async () => {
    setLoading(true);
    setError('');

    try {
      const response = await safeApiRequest({
        method: 'GET',
        url: `/phone-inventory/phones/${product._id}/${selectedVariant.sku}`
      });

      setAvailablePhones(response.data || []);
    } catch (error) {
      // For guests, don't show error - just show that IMEI selection is available after login
      if (!user) {
        setAvailablePhones([]);
      } else {
        setError('Failed to load available phones');
      }
      console.error('Error fetching available phones:', error);
    } finally {
      setLoading(false);
    }
  };

  const handlePhoneSelect = (phone) => {
    onPhoneSelect(phone);
  };

  const getConditionBadge = (condition) => {
    const badges = {
      'new': { color: 'bg-green-100 text-green-800', label: 'New' },
      'refurbished': { color: 'bg-blue-100 text-blue-800', label: 'Refurbished' },
      'used': { color: 'bg-yellow-100 text-yellow-800', label: 'Used' },
      'open_box': { color: 'bg-purple-100 text-purple-800', label: 'Open Box' },
      'damaged': { color: 'bg-red-100 text-red-800', label: 'Damaged' }
    };
    
    const badge = badges[condition] || badges['new'];
    
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${badge.color}`}>
        {badge.label}
      </span>
    );
  };

  const getWarrantyText = (months) => {
    if (months === 0) return 'No warranty';
    if (months === 12) return '1 year warranty';
    if (months === 24) return '2 years warranty';
    if (months === 36) return '3 years warranty';
    return `${months} months warranty`;
  };

  if (!product || !selectedVariant) {
    return null;
  }

  return (
    <div className="bg-white rounded-lg border p-4">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900">
          <i className="fas fa-mobile-alt text-blue-600 mr-2"></i>
          Select Your Device
        </h3>
        <Button
          variant="outline"
          size="sm"
          onClick={fetchAvailablePhones}
          disabled={loading}
        >
          <i className={`fas fa-sync-alt mr-1 ${loading ? 'fa-spin' : ''}`}></i>
          Refresh
        </Button>
      </div>

      {error && (
        <Alert
          type="error"
          message={error}
          onClose={() => setError('')}
          className="mb-4"
        />
      )}

      {loading ? (
        <div className="text-center py-8">
          <i className="fas fa-spinner fa-spin text-2xl text-blue-600 mb-2"></i>
          <p className="text-gray-600">Loading available devices...</p>
        </div>
      ) : availablePhones.length === 0 ? (
        <div className="text-center py-8">
          {!user ? (
            <>
              <i className="fas fa-mobile-alt text-2xl text-blue-600 mb-2"></i>
              <p className="text-gray-600">Individual Device Selection Available</p>
              <p className="text-sm text-gray-500 mt-1">
                Login to select specific devices with IMEI tracking
              </p>
              <p className="text-xs text-blue-600 mt-2">
                Each phone is individually tracked for warranty and authenticity
              </p>
            </>
          ) : (
            <>
              <i className="fas fa-exclamation-triangle text-2xl text-yellow-600 mb-2"></i>
              <p className="text-gray-600">No devices available for this variant</p>
              <p className="text-sm text-gray-500 mt-1">Please check back later or contact us</p>
            </>
          )}
        </div>
      ) : (
        <div className="space-y-3">
          <p className="text-sm text-gray-600 mb-3">
            {availablePhones.length} device{availablePhones.length !== 1 ? 's' : ''} available
          </p>
          
          <div className={`grid gap-3 ${isMobile ? 'grid-cols-1' : 'grid-cols-1 md:grid-cols-2'}`}>
            {availablePhones.map((phone) => (
              <div
                key={phone.imei}
                className={`border rounded-lg p-4 cursor-pointer transition-all ${
                  selectedPhone?.imei === phone.imei
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 hover:border-blue-300 hover:bg-gray-50'
                }`}
                onClick={() => handlePhoneSelect(phone)}
              >
                <div className="flex items-start justify-between">
                  <div className="flex-grow">
                    <div className="flex items-center mb-2">
                      <div
                        className={`w-4 h-4 rounded-full border-2 mr-3 flex items-center justify-center ${
                          selectedPhone?.imei === phone.imei
                            ? 'border-blue-500 bg-blue-500'
                            : 'border-gray-300'
                        }`}
                      >
                        {selectedPhone?.imei === phone.imei && (
                          <i className="fas fa-check text-white text-xs"></i>
                        )}
                      </div>
                      <div className="flex-grow">
                        <p className="font-medium text-gray-900">
                          IMEI: {phone.imei}
                        </p>
                      </div>
                    </div>
                    
                    <div className="ml-7 space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">Condition:</span>
                        {getConditionBadge(phone.condition)}
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">Warranty:</span>
                        <span className="text-sm font-medium text-gray-900">
                          {getWarrantyText(phone.warranty_months)}
                        </span>
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">Location:</span>
                        <span className="text-sm text-gray-900">
                          {phone.location || 'Main Warehouse'}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
                
                {selectedPhone?.imei === phone.imei && (
                  <div className="mt-3 pt-3 border-t border-blue-200">
                    <div className="flex items-center text-blue-600">
                      <i className="fas fa-check-circle mr-2"></i>
                      <span className="text-sm font-medium">Selected Device</span>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
          
          {selectedPhone && (
            <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded-lg">
              <div className="flex items-center">
                <i className="fas fa-info-circle text-green-600 mr-2"></i>
                <div>
                  <p className="text-sm font-medium text-green-800">
                    Device Selected: IMEI {selectedPhone.imei}
                  </p>
                  <p className="text-xs text-green-600">
                    This specific device will be reserved for you during checkout
                  </p>
                </div>
              </div>
            </div>
          )}
          
          <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <h4 className="text-sm font-medium text-blue-800 mb-2">
              <i className="fas fa-shield-alt mr-1"></i>
              IMEI Tracking Benefits
            </h4>
            <ul className="text-xs text-blue-700 space-y-1">
              <li>• Each device is individually tracked and verified</li>
              <li>• Warranty support tied to specific IMEI</li>
              <li>• Authentic device guarantee</li>
              <li>• Complete purchase history</li>
            </ul>
          </div>
        </div>
      )}
    </div>
  );
};

export default IMEISelector;
