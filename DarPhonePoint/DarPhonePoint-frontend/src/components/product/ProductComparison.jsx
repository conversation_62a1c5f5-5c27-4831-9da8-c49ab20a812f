import React, { useState, useEffect } from 'react';
import { get } from '../../api/unifiedApiClient';
import { formatPrice } from '../../utils/priceFormatter';
import Button from '../ui/Button';
import LoadingState from '../ui/LoadingState';

const ProductComparison = ({ productIds, onClose }) => {
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    fetchProducts();
  }, [productIds]);

  const fetchProducts = async () => {
    try {
      setLoading(true);
      const productPromises = productIds.map(id =>
        safeApiRequest({
          method: 'GET',
          url: `/products/${id}`
        })
      );

      const responses = await Promise.all(productPromises);
      const productsData = responses.map(response => 
        response.data?.data || response.data
      );
      
      setProducts(productsData);
    } catch (err) {
      setError('Failed to load products for comparison');
      console.error('Error fetching products:', err);
    } finally {
      setLoading(false);
    }
  };

  const getSpecificationValue = (product, spec) => {
    const specification = product.specifications?.find(s => 
      s.name.toLowerCase() === spec.toLowerCase()
    );
    return specification?.value || 'N/A';
  };

  const comparisonSpecs = [
    { key: 'brand', label: 'Brand', getValue: (p) => p.brand },
    { key: 'price', label: 'Price', getValue: (p) => formatPrice(p.price) },
    { key: 'storage', label: 'Storage', getValue: (p) => getSpecificationValue(p, 'storage') },
    { key: 'ram', label: 'RAM', getValue: (p) => getSpecificationValue(p, 'ram') },
    { key: 'display', label: 'Display Size', getValue: (p) => getSpecificationValue(p, 'display size') },
    { key: 'camera', label: 'Main Camera', getValue: (p) => getSpecificationValue(p, 'main camera') },
    { key: 'battery', label: 'Battery', getValue: (p) => getSpecificationValue(p, 'battery') },
    { key: 'os', label: 'Operating System', getValue: (p) => getSpecificationValue(p, 'operating system') },
    { key: 'warranty', label: 'Warranty', getValue: (p) => '12 months' },
    { key: 'stock', label: 'Availability', getValue: (p) => p.in_stock ? 'In Stock' : 'Out of Stock' }
  ];

  if (loading) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg p-6 max-w-4xl w-full mx-4">
          <LoadingState message="Loading product comparison..." />
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-6xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-2xl font-bold text-gray-800">
            <i className="fas fa-balance-scale text-blue-600 mr-3"></i>
            Compare Phones
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 text-2xl"
          >
            <i className="fas fa-times"></i>
          </button>
        </div>

        {error && (
          <div className="p-4 bg-red-50 border-l-4 border-red-400">
            <p className="text-red-700">{error}</p>
          </div>
        )}

        {/* Comparison Table */}
        <div className="overflow-auto max-h-[calc(90vh-120px)]">
          <table className="w-full">
            <thead className="bg-gray-50 sticky top-0">
              <tr>
                <th className="text-left p-4 font-medium text-gray-700 w-48">Specification</th>
                {products.map((product, index) => (
                  <th key={product._id} className="text-center p-4 min-w-64">
                    <div className="space-y-2">
                      <img
                        src={product.primary_image?.url || product.images?.[0]?.url || '/placeholder-phone.jpg'}
                        alt={product.name}
                        className="w-20 h-20 object-cover rounded-lg mx-auto"
                      />
                      <h3 className="font-semibold text-gray-800 text-sm">{product.name}</h3>
                      <span className="text-xs text-gray-500">{product.brand}</span>
                    </div>
                  </th>
                ))}
              </tr>
            </thead>
            <tbody>
              {comparisonSpecs.map((spec, index) => (
                <tr key={spec.key} className={index % 2 === 0 ? 'bg-gray-50' : 'bg-white'}>
                  <td className="p-4 font-medium text-gray-700 border-r">
                    {spec.label}
                  </td>
                  {products.map((product) => (
                    <td key={product._id} className="p-4 text-center text-gray-800">
                      {spec.getValue(product)}
                    </td>
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Footer */}
        <div className="p-6 border-t bg-gray-50">
          <div className="flex justify-between items-center">
            <p className="text-sm text-gray-600">
              Compare up to 3 phones side by side to make the best choice
            </p>
            <div className="flex space-x-3">
              <Button variant="secondary" onClick={onClose}>
                Close Comparison
              </Button>
              <Button onClick={() => window.open('/products', '_blank')}>
                Browse More Phones
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProductComparison;
