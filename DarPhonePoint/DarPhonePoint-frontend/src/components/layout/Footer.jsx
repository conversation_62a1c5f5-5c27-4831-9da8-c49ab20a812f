import React from 'react';
import { Link } from 'react-router-dom';

const Footer = () => {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-blue-900 text-white pt-12 pb-8">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Logo and Description */}
          <div className="col-span-1 md:col-span-1">
            <Link to="/" className="flex items-center mb-4">
              <i className="fas fa-mobile-alt mr-2 text-blue-400 text-2xl"></i>
              <span className="text-xl font-bold">Phone Point Dar</span>
            </Link>
            <p className="text-gray-300 text-sm">
              🇹🇿 Tanzania's premier mobile device retailer. Quality smartphones, tablets, accessories, and tech gadgets in Dar es Salaam.
            </p>
            <div className="mt-4 flex space-x-4">
              <a href="#" className="text-gray-300 hover:text-blue-400 transition-colors" title="Follow us on Facebook">
                <i className="fab fa-facebook text-lg"></i>
              </a>
              <a href="#" className="text-gray-300 hover:text-blue-400 transition-colors" title="Follow us on Instagram">
                <i className="fab fa-instagram text-lg"></i>
              </a>
              <a href="#" className="text-gray-300 hover:text-blue-400 transition-colors" title="Follow us on WhatsApp">
                <i className="fab fa-whatsapp text-lg"></i>
              </a>
              <a href="#" className="text-gray-300 hover:text-blue-400 transition-colors" title="Follow us on TikTok">
                <i className="fab fa-tiktok text-lg"></i>
              </a>
            </div>
          </div>

          {/* Product Categories */}
          <div className="col-span-1">
            <h3 className="text-lg font-semibold mb-4 flex items-center">
              <i className="fas fa-th-large mr-2 text-blue-400"></i>
              Categories
            </h3>
            <ul className="space-y-2">
              <li>
                <Link to="/products?category=smartphone" className="text-gray-300 hover:text-blue-400 transition-colors">📱 Smartphones</Link>
              </li>
              <li>
                <Link to="/products?category=tablet" className="text-gray-300 hover:text-blue-400 transition-colors">📟 Tablets</Link>
              </li>
              <li>
                <Link to="/products?category=accessory" className="text-gray-300 hover:text-blue-400 transition-colors">🔌 Accessories</Link>
              </li>
              <li>
                <Link to="/products?category=audio" className="text-gray-300 hover:text-blue-400 transition-colors">🎧 Audio Devices</Link>
              </li>
              <li>
                <Link to="/compare" className="text-gray-300 hover:text-blue-400 transition-colors">⚖️ Compare Phones</Link>
              </li>
            </ul>
          </div>

          {/* Customer Services */}
          <div className="col-span-1">
            <h3 className="text-lg font-semibold mb-4 flex items-center">
              <i className="fas fa-headset mr-2 text-blue-400"></i>
              Services
            </h3>
            <ul className="space-y-2">
              <li>
                <Link to="/wishlist" className="text-gray-300 hover:text-blue-400 transition-colors">💝 My Wishlist</Link>
              </li>
              <li>
                <a href="#" className="text-gray-300 hover:text-blue-400 transition-colors">🔧 Device Repair</a>
              </li>
              <li>
                <a href="#" className="text-gray-300 hover:text-blue-400 transition-colors">🔄 Trade-In Program</a>
              </li>
              <li>
                <a href="#" className="text-gray-300 hover:text-blue-400 transition-colors">🛡️ Extended Warranty</a>
              </li>
              <li>
                <a href="#" className="text-gray-300 hover:text-blue-400 transition-colors">❓ Help & FAQ</a>
              </li>
            </ul>
          </div>

          {/* Contact & Payment */}
          <div className="col-span-1">
            <h3 className="text-lg font-semibold mb-4 flex items-center">
              <i className="fas fa-phone mr-2 text-blue-400"></i>
              Contact Us
            </h3>
            <ul className="space-y-2">
              <li className="flex items-start">
                <i className="fas fa-phone mt-1 mr-2 text-blue-400"></i>
                <span className="text-gray-300">+255 123 456 789</span>
              </li>
              <li className="flex items-start">
                <i className="fab fa-whatsapp mt-1 mr-2 text-green-400"></i>
                <span className="text-gray-300">+255 987 654 321</span>
              </li>
              <li className="flex items-start">
                <i className="fas fa-envelope mt-1 mr-2 text-blue-400"></i>
                <span className="text-gray-300"><EMAIL></span>
              </li>
              <li className="flex items-start">
                <i className="fas fa-map-marker-alt mt-1 mr-2 text-blue-400"></i>
                <span className="text-gray-300">Kariakoo Market, Dar es Salaam</span>
              </li>
            </ul>
            <div className="mt-4">
              <h4 className="text-sm font-semibold mb-2 flex items-center">
                <i className="fas fa-credit-card mr-2 text-green-400"></i>
                Payment Methods
              </h4>
              <div className="flex flex-wrap gap-2">
                <span className="bg-green-600 text-white px-2 py-1 rounded text-xs">M-Pesa</span>
                <span className="bg-blue-600 text-white px-2 py-1 rounded text-xs">Tigo Pesa</span>
                <span className="bg-red-600 text-white px-2 py-1 rounded text-xs">Airtel Money</span>
                <span className="bg-gray-600 text-white px-2 py-1 rounded text-xs">Cash</span>
              </div>
            </div>
          </div>
        </div>

        <hr className="border-blue-800 my-8" />

        <div className="flex flex-col md:flex-row justify-between items-center">
          <div className="text-center md:text-left">
            <p className="text-gray-300 text-sm">
              &copy; {currentYear} Phone Point Dar. All rights reserved.
            </p>
            <p className="text-gray-400 text-xs mt-1">
              🇹🇿 Proudly serving Tanzania • Licensed Mobile Device Retailer
            </p>
          </div>
          <div className="mt-4 md:mt-0">
            <ul className="flex flex-wrap justify-center md:justify-end space-x-4 md:space-x-6">
              <li>
                <a href="#" className="text-gray-300 hover:text-blue-400 text-sm transition-colors">Privacy Policy</a>
              </li>
              <li>
                <a href="#" className="text-gray-300 hover:text-blue-400 text-sm transition-colors">Return Policy</a>
              </li>
              <li>
                <a href="#" className="text-gray-300 hover:text-blue-400 text-sm transition-colors">Warranty Terms</a>
              </li>
              <li>
                <a href="#" className="text-gray-300 hover:text-blue-400 text-sm transition-colors">Shipping Info</a>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
