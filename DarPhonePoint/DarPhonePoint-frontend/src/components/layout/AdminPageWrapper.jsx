import React from 'react';
import { Link } from 'react-router-dom';
import LoadingState from '../ui/LoadingState';
import Alert from '../ui/Alert';
import Button from '../ui/Button';

/**
 * Unified Admin Page Wrapper Component
 * Provides consistent layout, headers, breadcrumbs, loading states, and error handling
 * for all Phone Point Dar admin pages
 */
const AdminPageWrapper = ({ 
  title, 
  description, 
  breadcrumbs = [], 
  actions = null, 
  children,
  loading = false,
  error = null,
  success = null,
  onErrorClose = null,
  onSuccessClose = null,
  className = ""
}) => {
  // Format TZS currency consistently
  const formatTZS = (amount) => {
    return new Intl.NumberFormat('sw-TZ', {
      style: 'currency',
      currency: 'TZS',
      minimumFractionDigits: 0
    }).format(amount);
  };

  return (
    <div className={`min-h-screen bg-gradient-to-br from-blue-50 to-blue-100 ${className}`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {/* Page Header */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          {/* Breadcrumbs */}
          {breadcrumbs.length > 0 && (
            <nav className="flex mb-4" aria-label="Breadcrumb">
              <ol className="inline-flex items-center space-x-1 md:space-x-3">
                {breadcrumbs.map((crumb, index) => (
                  <li key={index} className="inline-flex items-center">
                    {index > 0 && (
                      <i className="fas fa-chevron-right text-gray-400 mx-2"></i>
                    )}
                    {crumb.href ? (
                      <Link
                        to={crumb.href}
                        className="text-blue-600 hover:text-blue-800 font-medium"
                      >
                        {crumb.name}
                      </Link>
                    ) : (
                      <span className="text-gray-500 font-medium">{crumb.name}</span>
                    )}
                  </li>
                ))}
              </ol>
            </nav>
          )}

          {/* Title and Description */}
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
            <div className="mb-4 md:mb-0">
              <h1 className="text-2xl font-bold text-gray-900 mb-2">{title}</h1>
              {description && (
                <p className="text-gray-600">{description}</p>
              )}
            </div>

            {/* Actions */}
            {actions && (
              <div className="flex flex-wrap gap-2">
                {actions}
              </div>
            )}
          </div>
        </div>

        {/* Alerts */}
        {error && (
          <Alert
            type="error"
            message={error}
            onClose={onErrorClose}
            className="mb-6"
          />
        )}

        {success && (
          <Alert
            type="success"
            message={success}
            onClose={onSuccessClose}
            className="mb-6"
          />
        )}

        {/* Main Content */}
        {loading ? (
          <div className="bg-white rounded-lg shadow-md p-8">
            <LoadingState 
              text="Loading Phone Point Dar data..." 
              size="lg"
              type="spinner"
            />
          </div>
        ) : (
          <div className="space-y-6">
            {children}
          </div>
        )}
      </div>
    </div>
  );
};

// Quick action button component for consistency
export const QuickActionButton = ({ 
  to, 
  icon, 
  children, 
  variant = "primary", 
  className = "",
  ...props 
}) => {
  const baseClasses = "inline-flex items-center px-4 py-2 rounded-md font-medium transition-colors";
  const variants = {
    primary: "bg-blue-600 text-white hover:bg-blue-700",
    secondary: "bg-gray-100 text-gray-700 hover:bg-gray-200",
    success: "bg-green-600 text-white hover:bg-green-700",
    warning: "bg-yellow-600 text-white hover:bg-yellow-700",
    danger: "bg-red-600 text-white hover:bg-red-700"
  };

  const buttonContent = (
    <>
      {icon && <i className={`${icon} mr-2`}></i>}
      {children}
    </>
  );

  if (to) {
    return (
      <Link 
        to={to} 
        className={`${baseClasses} ${variants[variant]} ${className}`}
        {...props}
      >
        {buttonContent}
      </Link>
    );
  }

  return (
    <Button 
      className={`${variants[variant]} ${className}`}
      {...props}
    >
      {buttonContent}
    </Button>
  );
};

// Stats card component for admin dashboards
export const AdminStatsCard = ({ 
  title, 
  value, 
  icon, 
  color = "blue", 
  subtitle = null,
  actionLink = null,
  trend = null 
}) => {
  const colorClasses = {
    blue: "bg-blue-50 text-blue-600 border-blue-200",
    green: "bg-green-50 text-green-600 border-green-200",
    yellow: "bg-yellow-50 text-yellow-600 border-yellow-200",
    red: "bg-red-50 text-red-600 border-red-200",
    purple: "bg-purple-50 text-purple-600 border-purple-200"
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-6 border-l-4 border-blue-500">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600 mb-1">{title}</p>
          <p className="text-2xl font-bold text-gray-900">{value}</p>
          {subtitle && (
            <p className="text-sm text-gray-500 mt-1">{subtitle}</p>
          )}
          {trend && (
            <div className={`text-sm mt-2 ${trend.positive ? 'text-green-600' : 'text-red-600'}`}>
              <i className={`fas fa-arrow-${trend.positive ? 'up' : 'down'} mr-1`}></i>
              {trend.value}
            </div>
          )}
        </div>
        <div className={`p-3 rounded-full ${colorClasses[color]}`}>
          <i className={`${icon} text-xl`}></i>
        </div>
      </div>
      {actionLink && (
        <div className="mt-4">
          <Link 
            to={actionLink} 
            className="text-blue-600 hover:text-blue-800 text-sm font-medium"
          >
            View Details →
          </Link>
        </div>
      )}
    </div>
  );
};

export default AdminPageWrapper;
