import React, { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';

// Icons - Using FontAwesome for consistency
import {
  FaUserCircle,
  FaBars,
  FaTimes
} from 'react-icons/fa';

/**
 * AdminLayout Component
 * Provides a consistent layout for admin pages with a sidebar navigation
 */
const AdminLayout = ({ children }) => {
  const { user } = useAuth();
  const location = useLocation();
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [expandedMenus, setExpandedMenus] = useState(new Set());

  // Navigation items with phone retail terminology and FontAwesome icons for Phone Point Dar
  const navItems = [
    {
      name: 'Dashboard',
      path: '/admin',
      icon: <i className="fas fa-tachometer-alt w-5 h-5"></i>
    },
    {
      name: 'Tech Catalog',
      path: '/admin/products',
      icon: <i className="fas fa-mobile-alt w-5 h-5 text-blue-600"></i>,
      subItems: [
        { name: 'All Tech Products', path: '/admin/products' },
        { name: 'Add New Product', path: '/admin/products/new' },
        { name: 'Categories', path: '/admin/products/categories' },
        { name: 'Brands', path: '/admin/products/brands' },
        { name: 'Bulk Import', path: '/admin/products/import' }
      ]
    },
    {
      name: 'Inventory',
      path: '/admin/inventory',
      icon: <i className="fas fa-warehouse w-5 h-5 text-blue-600"></i>,
      subItems: [
        { name: 'Stock Levels', path: '/admin/inventory' },
        { name: 'IMEI Management', path: '/admin/inventory/imei' },
        { name: 'Low Stock Alerts', path: '/admin/inventory/alerts' },
        { name: 'Stock Movements', path: '/admin/inventory/movements' }
      ]
    },
    {
      name: 'Orders',
      path: '/admin/orders',
      icon: <i className="fas fa-shopping-cart w-5 h-5 text-blue-600"></i>,
      subItems: [
        { name: 'All Orders', path: '/admin/orders' },
        { name: 'Pending Orders', path: '/admin/orders?status=pending' },
        { name: 'Processing', path: '/admin/orders?status=processing' },
        { name: 'Shipped', path: '/admin/orders?status=shipped' },
        { name: 'Delivered', path: '/admin/orders?status=delivered' }
      ]
    },
    {
      name: 'Customers',
      path: '/admin/customers',
      icon: <i className="fas fa-users w-5 h-5 text-blue-600"></i>
    },
    {
      name: 'Sales Analytics',
      path: '/admin/analytics',
      icon: <i className="fas fa-chart-line w-5 h-5 text-blue-600"></i>,
      subItems: [
        { name: 'Sales Report', path: '/admin/analytics/sales' },
        { name: 'Inventory Report', path: '/admin/analytics/inventory' },
        { name: 'Customer Insights', path: '/admin/analytics/customers' },
        { name: 'Financial Report', path: '/admin/analytics/financial' }
      ]
    },
    {
      name: 'Settings',
      path: '/admin/settings',
      icon: <i className="fas fa-cog w-5 h-5 text-blue-600"></i>
    },
    {
      name: 'Audit Logs',
      path: '/admin/audit-logs',
      icon: <i className="fas fa-history w-5 h-5 text-blue-600"></i>
    },
  ];

  // Check if a nav item is active
  const isActive = (path) => {
    if (path === '/admin') {
      return location.pathname === '/admin';
    }
    return location.pathname.startsWith(path);
  };

  // Check if any subitem is active
  const isSubItemActive = (item) => {
    if (!item.subItems) return false;
    return item.subItems.some(subItem => location.pathname === subItem.path);
  };

  // Toggle submenu expansion
  const toggleSubmenu = (path, hasSubItems) => {
    if (!hasSubItems) return;

    const newExpanded = new Set(expandedMenus);
    if (newExpanded.has(path)) {
      newExpanded.delete(path);
    } else {
      newExpanded.add(path);
    }
    setExpandedMenus(newExpanded);
  };

  // Auto-expand menus that have active subitems
  React.useEffect(() => {
    const newExpanded = new Set(expandedMenus);
    navItems.forEach(item => {
      if (item.subItems && isSubItemActive(item)) {
        newExpanded.add(item.path);
      }
    });
    setExpandedMenus(newExpanded);
  }, [location.pathname]);

  // Toggle sidebar on mobile
  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  return (
    <div className="flex h-screen bg-gray-50">
      {/* Mobile sidebar backdrop */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 z-20 bg-black bg-opacity-50 lg:hidden"
          onClick={toggleSidebar}
        ></div>
      )}

      {/* Sidebar */}
      <div
        className={`fixed inset-y-0 left-0 z-30 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0 ${
          sidebarOpen ? 'translate-x-0' : '-translate-x-full'
        }`}
      >
        <div className="flex items-center justify-between h-16 px-6 border-b">
          <Link to="/admin" className="flex items-center">
            <i className="fas fa-mobile-alt text-2xl text-blue-600 mr-2"></i>
            <span className="text-xl font-semibold text-blue-600">Phone Point Dar</span>
          </Link>
          <button
            className="p-1 text-gray-700 rounded-md lg:hidden hover:bg-gray-100"
            onClick={toggleSidebar}
          >
            <FaTimes className="w-5 h-5" />
          </button>
        </div>
        <div className="px-4 py-2 border-b">
          <div className="flex items-center py-2">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center text-blue-600">
                {user?.name?.charAt(0) || 'A'}
              </div>
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-900">{user?.name || 'Admin User'}</p>
              <p className="text-xs text-gray-500">{user?.email || '<EMAIL>'}</p>
            </div>
          </div>
        </div>
        <nav className="px-4 py-4">
          <ul className="space-y-1">
            {navItems.map((item) => (
              <li key={item.path}>
                {item.subItems ? (
                  // Menu item with submenu
                  <div>
                    <button
                      onClick={() => toggleSubmenu(item.path, true)}
                      className={`w-full flex items-center px-4 py-2 text-sm font-medium rounded-md text-left ${
                        isActive(item.path) || isSubItemActive(item)
                          ? 'bg-blue-50 text-blue-700'
                          : 'text-gray-700 hover:bg-gray-100'
                      }`}
                    >
                      <span className="mr-3">{item.icon}</span>
                      {item.name}
                      <i className={`fas fa-chevron-${expandedMenus.has(item.path) ? 'up' : 'down'} ml-auto text-xs transition-transform`}></i>
                    </button>
                    {/* Sub-navigation items */}
                    {expandedMenus.has(item.path) && (
                      <ul className="ml-8 mt-1 space-y-1">
                        {item.subItems.map((subItem) => (
                          <li key={subItem.path}>
                            <Link
                              to={subItem.path}
                              className={`block px-3 py-1 text-sm rounded transition-colors ${
                                location.pathname === subItem.path
                                  ? 'text-blue-700 bg-blue-50 font-medium'
                                  : 'text-gray-600 hover:text-blue-600 hover:bg-blue-50'
                              }`}
                              onClick={() => setSidebarOpen(false)} // Close mobile sidebar on navigation
                            >
                              {subItem.name}
                            </Link>
                          </li>
                        ))}
                      </ul>
                    )}
                  </div>
                ) : (
                  // Regular menu item without submenu
                  <Link
                    to={item.path}
                    className={`flex items-center px-4 py-2 text-sm font-medium rounded-md ${
                      isActive(item.path)
                        ? 'bg-blue-50 text-blue-700'
                        : 'text-gray-700 hover:bg-gray-100'
                    }`}
                    onClick={() => setSidebarOpen(false)} // Close mobile sidebar on navigation
                  >
                    <span className="mr-3">{item.icon}</span>
                    {item.name}
                  </Link>
                )}
              </li>
            ))}
          </ul>
        </nav>
      </div>

      {/* Main content */}
      <div className="flex flex-col flex-1 overflow-hidden">
        {/* Top header */}
        <header className="flex items-center justify-between h-16 px-6 bg-white border-b lg:hidden">
          <button
            className="p-1 text-gray-700 rounded-md hover:bg-gray-100"
            onClick={toggleSidebar}
          >
            <FaBars className="w-5 h-5" />
          </button>
          <Link to="/admin" className="flex items-center">
            <i className="fas fa-mobile-alt text-xl text-blue-600 mr-2"></i>
            <span className="text-lg font-semibold text-blue-600">Phone Point Dar</span>
          </Link>
          <div className="w-5 h-5"></div> {/* Empty div for flex spacing */}
        </header>

        {/* Page content */}
        <main className="flex-1 overflow-y-auto">
          {children}
        </main>
      </div>
    </div>
  );
};

export default AdminLayout;
