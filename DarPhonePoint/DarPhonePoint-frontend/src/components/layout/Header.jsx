import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { useLanguage } from '../../contexts/LanguageContext';
import { get } from '../../api/unifiedApiClient';
import { useDeviceDetect } from '../../utils/mobileOptimization';
import { getGuestCart } from '../../services/guestCartService';
import LanguageToggle from '../ui/LanguageToggle';
import AutocompleteSearch from '../search/AutocompleteSearch';

const Header = () => {
  const { user, logout, loading: authLoading } = useAuth();
  const { t } = useLanguage();
  const navigate = useNavigate();
  const { isMobile, isTablet } = useDeviceDetect();
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [cartItemCount, setCartItemCount] = useState(0);

  // Fetch cart item count
  useEffect(() => {
    fetchCartCount();
  }, [user]);

  const fetchCartCount = async () => {
    try {
      if (user) {
        // Authenticated user - fetch from API
        const response = await get('/cart');
        setCartItemCount(response.data.item_count || 0);
      } else {
        // Guest user - get from localStorage
        const guestCart = getGuestCart();
        const itemCount = guestCart.items.reduce((total, item) => total + item.quantity, 0);
        setCartItemCount(itemCount);
      }
    } catch (error) {
      console.error('Error fetching cart count:', error);
      // For guests, still try to get local cart count
      if (!user) {
        const guestCart = getGuestCart();
        const itemCount = guestCart.items.reduce((total, item) => total + item.quantity, 0);
        setCartItemCount(itemCount);
      }
    }
  };

  const handleLogout = () => {
    logout();
    navigate('/');
  };

  // Function to refresh cart count (can be called from other components)
  const refreshCartCount = () => {
    fetchCartCount();
  };

  // Make refreshCartCount available globally
  useEffect(() => {
    window.refreshCartCount = refreshCartCount;
    return () => {
      delete window.refreshCartCount;
    };
  }, []);

  if (authLoading) {
    return (
      <header className="bg-white shadow-sm sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4">
          <div className="flex justify-between items-center">
            <Link to="/" className="flex items-center">
              <div className="flex items-center">
                <i className="fas fa-mobile-alt text-blue-600 text-2xl mr-2"></i>
                <span className="text-xl font-bold text-blue-600">Phone Point Dar</span>
              </div>
            </Link>
          </div>
        </div>
      </header>
    );
  }

  return (
    <header className="bg-white shadow-sm sticky top-0 z-50">
      <div className="container mx-auto px-4 py-4">
        <div className="flex justify-between items-center">
          {/* Logo */}
          <Link to="/" className="flex items-center">
            <div className="flex items-center">
              <i className="fas fa-mobile-alt text-blue-600 text-2xl mr-2"></i>
              <div className="flex flex-col">
                <span className={`font-bold text-blue-600 ${isMobile ? 'text-lg' : 'text-xl'}`}>
                  Phone Point Dar
                </span>
                {!isMobile && (
                  <span className="text-xs text-gray-500">Your Mobile Store</span>
                )}
              </div>
            </div>
          </Link>

          {/* Search Bar - Desktop */}
          <div className="hidden lg:flex flex-1 max-w-lg mx-8">
            <AutocompleteSearch />
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-6">
            <Link to="/" className="text-gray-700 hover:text-blue-600 transition-colors font-medium">
              {t('nav.home')}
            </Link>
            <Link to="/products" className="text-gray-700 hover:text-blue-600 transition-colors font-medium">
              {t('nav.products')}
            </Link>

            {/* Language Toggle */}
            <LanguageToggle />

            {/* Wishlist Icon */}
            <Link to="/wishlist" className="relative text-gray-700 hover:text-blue-600 transition-colors" title="Wishlist">
              <i className="fas fa-heart text-xl"></i>
            </Link>

            {/* Cart Icon */}
            <Link to="/cart" className="relative text-gray-700 hover:text-blue-600 transition-colors">
              <i className="fas fa-shopping-cart text-xl"></i>
              {cartItemCount > 0 && (
                <span className="absolute -top-2 -right-2 bg-blue-600 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                  {cartItemCount > 99 ? '99+' : cartItemCount}
                </span>
              )}
            </Link>

            {user ? (
              <>
                <Link to="/dashboard" className="text-gray-700 hover:text-blue-600 transition-colors font-medium">
                  Dashboard
                </Link>
                <div className="relative group">
                  <button className="text-gray-700 hover:text-blue-600 transition-colors flex items-center font-medium">
                    <i className="fas fa-user-circle mr-2"></i>
                    {user.firstName || user.name?.split(' ')[0] || 'User'}
                    <i className="fas fa-chevron-down ml-1 text-xs"></i>
                  </button>
                  <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 border">
                    <Link to="/profile" className="block px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 flex items-center">
                      <i className="fas fa-user mr-2 w-4"></i>
                      Profile
                    </Link>
                    <Link to="/orders" className="block px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 flex items-center">
                      <i className="fas fa-box mr-2 w-4"></i>
                      Orders
                    </Link>
                    <Link to="/cart" className="block px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 flex items-center md:hidden">
                      <i className="fas fa-shopping-cart mr-2 w-4"></i>
                      Cart ({cartItemCount})
                    </Link>

                    {user.role === 'admin' && (
                      <Link to="/admin" className="block px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 flex items-center border-t">
                        <i className="fas fa-cog mr-2 w-4"></i>
                        Admin Dashboard
                      </Link>
                    )}
                    <button
                      onClick={handleLogout}
                      className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 border-t flex items-center"
                    >
                      <i className="fas fa-sign-out-alt mr-2 w-4"></i>
                      Logout
                    </button>
                  </div>
                </div>
              </>
            ) : (
              <>
                <Link to="/login" className="text-gray-700 hover:text-blue-600 transition-colors font-medium">
                  Login
                </Link>
                <Link to="/register" className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors font-medium">
                  Sign Up
                </Link>
              </>
            )}
          </nav>

          {/* Mobile Actions */}
          <div className="md:hidden flex items-center space-x-4">
            {/* Mobile Cart Icon */}
            <Link to="/cart" className="relative text-gray-700 hover:text-blue-600 transition-colors">
              <i className="fas fa-shopping-cart text-xl"></i>
              {cartItemCount > 0 && (
                <span className="absolute -top-2 -right-2 bg-blue-600 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                  {cartItemCount > 99 ? '99+' : cartItemCount}
                </span>
              )}
            </Link>

            {/* Mobile Menu Button */}
            <button
              className="text-gray-700 p-2"
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
            >
              {mobileMenuOpen ? (
                <i className="fas fa-times text-xl"></i>
              ) : (
                <i className="fas fa-bars text-xl"></i>
              )}
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {mobileMenuOpen && (
          <nav className="md:hidden mt-4 pb-4 border-t border-gray-200">
            <div className="space-y-1 pt-4">
              <Link
                to="/"
                className="flex items-center text-gray-700 hover:text-blue-600 hover:bg-blue-50 transition-colors py-3 px-2 rounded-lg"
                onClick={() => setMobileMenuOpen(false)}
              >
                <i className="fas fa-home mr-3 w-5"></i>
                Home
              </Link>
              <Link
                to="/products"
                className="flex items-center text-gray-700 hover:text-blue-600 hover:bg-blue-50 transition-colors py-3 px-2 rounded-lg"
                onClick={() => setMobileMenuOpen(false)}
              >
                <i className="fas fa-mobile-alt mr-3 w-5"></i>
                Products
              </Link>

              {/* Cart Link - Available for all users */}
              <Link
                to="/cart"
                className="flex items-center justify-between text-gray-700 hover:text-blue-600 hover:bg-blue-50 transition-colors py-3 px-2 rounded-lg"
                onClick={() => setMobileMenuOpen(false)}
              >
                <div className="flex items-center">
                  <i className="fas fa-shopping-cart mr-3 w-5"></i>
                  Cart
                </div>
                {cartItemCount > 0 && (
                  <span className="bg-blue-600 text-white text-xs rounded-full h-6 w-6 flex items-center justify-center">
                    {cartItemCount > 99 ? '99+' : cartItemCount}
                  </span>
                )}
              </Link>

              {user ? (
                <>
                  <Link
                    to="/dashboard"
                    className="flex items-center text-gray-700 hover:text-blue-600 hover:bg-blue-50 transition-colors py-3 px-2 rounded-lg"
                    onClick={() => setMobileMenuOpen(false)}
                  >
                    <i className="fas fa-tachometer-alt mr-3 w-5"></i>
                    Dashboard
                  </Link>
                  <Link
                    to="/profile"
                    className="flex items-center text-gray-700 hover:text-blue-600 hover:bg-blue-50 transition-colors py-3 px-2 rounded-lg"
                    onClick={() => setMobileMenuOpen(false)}
                  >
                    <i className="fas fa-user mr-3 w-5"></i>
                    Profile
                  </Link>
                  <Link
                    to="/orders"
                    className="flex items-center text-gray-700 hover:text-blue-600 hover:bg-blue-50 transition-colors py-3 px-2 rounded-lg"
                    onClick={() => setMobileMenuOpen(false)}
                  >
                    <i className="fas fa-box mr-3 w-5"></i>
                    Orders
                  </Link>

                  {/* Trade-In Link */}
                  <Link
                    to="/trade-in"
                    className="flex items-center text-gray-700 hover:text-blue-600 hover:bg-blue-50 transition-colors py-3 px-2 rounded-lg"
                    onClick={() => setMobileMenuOpen(false)}
                  >
                    <i className="fas fa-exchange-alt mr-3 w-5"></i>
                    Trade-In
                  </Link>

                  {user.role === 'admin' && (
                    <Link
                      to="/admin"
                      className="flex items-center text-gray-700 hover:text-blue-600 hover:bg-blue-50 transition-colors py-3 px-2 rounded-lg mt-2 border-t border-gray-100 pt-3"
                      onClick={() => setMobileMenuOpen(false)}
                    >
                      <i className="fas fa-cog mr-3 w-5"></i>
                      Admin Dashboard
                    </Link>
                  )}
                  <button
                    onClick={() => {
                      handleLogout();
                      setMobileMenuOpen(false);
                    }}
                    className="flex items-center w-full text-left text-gray-700 hover:text-blue-600 hover:bg-blue-50 transition-colors py-3 px-2 rounded-lg mt-2 border-t border-gray-100 pt-3"
                  >
                    <i className="fas fa-sign-out-alt mr-3 w-5"></i>
                    Logout
                  </button>
                </>
              ) : (
                <>
                  <div className="mt-4 pt-4 border-t border-gray-100">
                    <Link
                      to="/login"
                      className="flex items-center text-gray-700 hover:text-blue-600 hover:bg-blue-50 transition-colors py-3 px-2 rounded-lg"
                      onClick={() => setMobileMenuOpen(false)}
                    >
                      <i className="fas fa-sign-in-alt mr-3 w-5"></i>
                      Login
                    </Link>
                    <Link
                      to="/register"
                      className="flex items-center bg-blue-600 text-white hover:bg-blue-700 transition-colors py-3 px-2 rounded-lg mt-2"
                      onClick={() => setMobileMenuOpen(false)}
                    >
                      <i className="fas fa-user-plus mr-3 w-5"></i>
                      Sign Up
                    </Link>
                  </div>
                </>
              )}
            </div>
          </nav>
        )}
      </div>
    </header>
  );
};

export default Header;
