import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import path from 'path';

export default defineConfig({
  plugins: [
    react({
      // Optimize for automated testing
      fastRefresh: process.env.NODE_ENV !== 'test',
      babel: {
        plugins: process.env.NODE_ENV === 'test' ? [] : undefined
      }
    })
  ],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
  define: {
    global: 'globalThis',
    // Add environment variables for better error handling
    __DEV__: process.env.NODE_ENV === 'development',
    __TEST__: process.env.NODE_ENV === 'test'
  },
  // Optimize dependency pre-bundling for faster loading
  optimizeDeps: {
    include: [
      'react',
      'react-dom',
      'react-router-dom',
      'react-hot-toast',
      'axios'
    ],
    exclude: ['@vite/client', '@vite/env']
  },
  server: {
    host: '0.0.0.0', // Allow external connections for testing
    port: 5173,
    strictPort: true, // Fail if port is already in use
    cors: true, // Enable CORS for automated testing
    proxy: {
      '/api': {
        target: 'http://localhost:5001',
        changeOrigin: true,
        secure: false,
        ws: true, // Enable WebSocket proxying
        timeout: 30000,
        proxyTimeout: 30000,
        configure: (proxy, options) => {
          proxy.on('error', (err, req, res) => {
            console.log('Proxy error:', err);
          });
          proxy.on('proxyReq', (proxyReq, req, res) => {
            console.log('Sending Request to the Target:', req.method, req.url);
          });
          proxy.on('proxyRes', (proxyRes, req, res) => {
            console.log('Received Response from the Target:', proxyRes.statusCode, req.url);
          });
        },
      },
    },
    headers: {
      'X-Content-Type-Options': 'nosniff',
      'X-Frame-Options': 'SAMEORIGIN', // Changed from DENY to SAMEORIGIN for testing
      'X-XSS-Protection': '1; mode=block',
      'Referrer-Policy': 'strict-origin-when-cross-origin',
      'Permissions-Policy': 'camera=(), microphone=(), geolocation=()',
      'Access-Control-Allow-Origin': '*', // Allow all origins for testing
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization'
    },
    hmr: {
      port: 24678, // Use a different port for HMR to avoid conflicts
      overlay: false // Disable error overlay for automated testing
    }
  },
  build: {
    rollupOptions: {
      output: {
        manualChunks: (id) => {
          // Vendor chunks
          if (id.includes('node_modules')) {
            if (id.includes('react') || id.includes('react-dom')) {
              return 'react-vendor';
            }
            if (id.includes('react-router')) {
              return 'router';
            }
            if (id.includes('lucide-react') || id.includes('react-icons')) {
              return 'icons';
            }
            if (id.includes('axios') || id.includes('fetch')) {
              return 'http';
            }
            if (id.includes('bootstrap') || id.includes('react-bootstrap')) {
              return 'ui';
            }
            // Other vendor libraries
            return 'vendor';
          }

          // Feature-based chunks
          if (id.includes('/pages/admin/')) {
            return 'admin';
          }
          if (id.includes('/pages/auth/')) {
            return 'auth';
          }
          if (id.includes('/components/charts/')) {
            return 'charts';
          }
        }
      }
    },
    sourcemap: false, // Disable source maps in production
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true, // Remove console.log in production
        drop_debugger: true,
        pure_funcs: ['console.log', 'console.info', 'console.debug']
      },
      mangle: {
        safari10: true
      }
    },
    // Optimize chunk size
    chunkSizeWarningLimit: 1000,
    // IPFS-specific configuration
    assetsDir: 'assets',
    outDir: 'dist'
  },
  // Base path for IPFS deployment
  base: './',
});
