# Phone Point Dar Frontend Environment Variables

# API Configuration
VITE_API_URL=/api
VITE_API_TIMEOUT=30000
VITE_API_RETRY_ATTEMPTS=3
VITE_API_RETRY_DELAY=1000

# Feature Flags
VITE_ENABLE_CACHE=true
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_NOTIFICATIONS=true

# App Configuration
VITE_APP_NAME=Phone Point Dar
VITE_APP_VERSION=1.0.0

# Development/Production URLs (Override in production)
VITE_FRONTEND_URL=http://localhost:5173
VITE_BACKEND_URL=http://localhost:5001

# Analytics (if using external services)
VITE_GOOGLE_ANALYTICS_ID=
VITE_HOTJAR_ID=

# Payment Configuration
VITE_STRIPE_PUBLISHABLE_KEY=

# Error Tracking & Reporting
VITE_SENTRY_DSN=
VITE_ERROR_REPORTING_ENABLED=true

# Cache Configuration
VITE_CACHE_ENABLED=true
VITE_CACHE_TTL=300000

# Debug Mode (development only)
VITE_DEBUG_MODE=false

# Performance Monitoring
VITE_PERFORMANCE_MONITORING=true

# Production Configuration
# Set these for production deployment:
# VITE_API_URL=https://api.aixcelerate.og/api
# VITE_FRONTEND_URL=https://aixcelerate.og
# VITE_BACKEND_URL=https://api.aixcelerate.og
