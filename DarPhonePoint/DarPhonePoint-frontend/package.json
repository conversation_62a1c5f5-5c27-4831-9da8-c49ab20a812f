{"name": "aixcelerate-frontend", "version": "1.0.0", "private": true, "type": "module", "description": "Frontend for AIXcelerate - AI Productivity Tools Platform", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test": "vitest", "test:watch": "vitest --watch", "test:coverage": "vitest --coverage", "start": "vite", "lint": "eslint src --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint src --ext .js,.jsx,.ts,.tsx --fix"}, "keywords": ["ai", "productivity", "tools", "react", "tailwindcss"], "author": "", "license": "ISC", "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@headlessui/react": "^2.2.3", "@heroicons/react": "^2.2.0", "@mui/icons-material": "^7.1.0", "@mui/material": "^7.1.0", "@tailwindcss/postcss": "^4.1.7", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "autoprefixer": "^10.4.21", "axios": "^1.6.7", "bootstrap": "^5.3.6", "chart.js": "^4.4.9", "formik": "^2.4.6", "postcss": "^8.4.35", "react": "^18.2.0", "react-bootstrap": "^2.10.10", "react-chartjs-2": "^5.3.0", "react-dom": "^18.2.0", "react-hot-toast": "^2.5.2", "react-icons": "^4.12.0", "react-router-dom": "^6.22.1", "react-scripts": "5.0.1", "react-toastify": "^10.0.4", "recharts": "^2.15.3", "tailwindcss": "^3.4.1", "uuid": "^9.0.1", "web-vitals": "^2.1.4", "yup": "^1.6.1"}, "devDependencies": {"@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@babel/preset-env": "^7.27.2", "@babel/preset-react": "^7.27.1", "@testing-library/jest-dom": "^6.1.4", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.1", "@vitejs/plugin-react": "^4.4.1", "@vitest/ui": "^3.2.4", "babel-jest": "^29.7.0", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jsdom": "^26.1.0", "vite": "^6.3.5", "vitest": "^3.2.4"}, "jest": {"testEnvironment": "jsdom", "transform": {"^.+\\.(js|jsx)$": "babel-jest"}, "moduleNameMapper": {"^.+\\.svg$": "<rootDir>/src/__mocks__/svgMock.js", "^.+\\.(css|less|scss)$": "identity-obj-proxy"}, "setupFilesAfterEnv": ["<rootDir>/src/setupTests.js"], "transformIgnorePatterns": ["/node_modules/(?!.*\\.mjs$)"], "extensionsToTreatAsEsm": [".jsx"], "testEnvironmentOptions": {"customExportConditions": ["node", "node-addons"]}}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}