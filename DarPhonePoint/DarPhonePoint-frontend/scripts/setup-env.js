import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Create .env file if it doesn't exist
const envPath = path.join(__dirname, '..', '.env');
if (!fs.existsSync(envPath)) {
  const envContent = `VITE_API_URL=http://localhost:3000
VITE_APP_NAME=AIXcelerate
VITE_APP_VERSION=1.0.0
VITE_APP_ENV=development
VITE_APP_DEBUG=true
VITE_APP_LOG_LEVEL=debug
VITE_APP_API_TIMEOUT=30000
VITE_APP_API_RETRY_ATTEMPTS=3
VITE_APP_API_RETRY_DELAY=1000
VITE_APP_API_CACHE_TTL=300
VITE_APP_API_CACHE_ENABLED=true
VITE_APP_API_CACHE_PREFIX=aixcelerate
VITE_APP_API_CACHE_STORAGE=localStorage
VITE_APP_API_CACHE_VERSION=1
VITE_APP_API_CACHE_DEBUG=true
VITE_APP_API_CACHE_LOG_LEVEL=debug
VITE_APP_API_CACHE_LOG_ENABLED=true
VITE_APP_API_CACHE_LOG_FORMAT=json
VITE_APP_API_CACHE_LOG_FILE=api-cache.log
VITE_APP_API_CACHE_LOG_DIR=logs
VITE_APP_API_CACHE_LOG_MAX_SIZE=5242880
VITE_APP_API_CACHE_LOG_MAX_FILES=5
VITE_APP_API_CACHE_LOG_COMPRESS=true
VITE_APP_API_CACHE_LOG_TTL=86400
VITE_APP_API_CACHE_LOG_ROTATE=true
VITE_APP_API_CACHE_LOG_ROTATE_INTERVAL=86400
VITE_APP_API_CACHE_LOG_ROTATE_SIZE=5242880
VITE_APP_API_CACHE_LOG_ROTATE_KEEP=5
VITE_APP_API_CACHE_LOG_ROTATE_COMPRESS=true
VITE_APP_API_CACHE_LOG_ROTATE_TTL=86400
VITE_APP_API_CACHE_LOG_ROTATE_DEBUG=true
VITE_APP_API_CACHE_LOG_ROTATE_LOG_LEVEL=debug
VITE_APP_API_CACHE_LOG_ROTATE_LOG_ENABLED=true
VITE_APP_API_CACHE_LOG_ROTATE_LOG_FORMAT=json
VITE_APP_API_CACHE_LOG_ROTATE_LOG_FILE=api-cache-rotate.log
VITE_APP_API_CACHE_LOG_ROTATE_LOG_DIR=logs
VITE_APP_API_CACHE_LOG_ROTATE_LOG_MAX_SIZE=5242880
VITE_APP_API_CACHE_LOG_ROTATE_LOG_MAX_FILES=5
VITE_APP_API_CACHE_LOG_ROTATE_LOG_COMPRESS=true
VITE_APP_API_CACHE_LOG_ROTATE_LOG_TTL=86400`;

  fs.writeFileSync(envPath, envContent);
  console.log('Created .env file with default configuration');
}

// Create logs directory if it doesn't exist
const logsPath = path.join(__dirname, '..', 'logs');
if (!fs.existsSync(logsPath)) {
  fs.mkdirSync(logsPath);
  console.log('Created logs directory');
}

console.log('Environment setup completed successfully'); 