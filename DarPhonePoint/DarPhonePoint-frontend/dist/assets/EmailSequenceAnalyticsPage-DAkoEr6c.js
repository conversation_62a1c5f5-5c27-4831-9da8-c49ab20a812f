import{j as e}from"./ui-CLS_rXuQ.js";import{r as s,R as t,f as a,L as l}from"./router-Bmo9-suO.js";import{A as r,B as i}from"./index-DQKqxzWd.js";import{a as n}from"./emailSequenceService-vhh7EWiT.js";import{g as c}from"./analyticsService-DGKX5lwb.js";import{C as d,P as o,B as m,L as x,a as p,b as u,c as g,d as h,e as b,p as f,f as j,g as y,A as N}from"./chart-BnP3NyOz.js";import"./vendor-mk1rzcpA.js";const v="label";function w(e,s){"function"==typeof e?e(s):e&&(e.current=s)}function k(e,s){e.labels=s}function S(e,s){let t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:v;const a=[];e.datasets=s.map((s=>{const l=e.datasets.find((e=>e[t]===s[t]));return l&&s.data&&!a.includes(l)?(a.push(l),Object.assign(l,s),l):{...s}}))}function E(e){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:v;const t={labels:[],datasets:[]};return k(t,e.labels),S(t,e.datasets,s),t}function C(e,a){const{height:l=150,width:r=300,redraw:i=!1,datasetIdKey:n,type:c,data:o,options:m,plugins:x=[],fallbackContent:p,updateMode:u,...g}=e,h=s.useRef(null),b=s.useRef(null),f=()=>{h.current&&(b.current=new d(h.current,{type:c,data:E(o,n),options:m&&{...m},plugins:x}),w(a,b.current))},j=()=>{w(a,null),b.current&&(b.current.destroy(),b.current=null)};return s.useEffect((()=>{!i&&b.current&&m&&function(e,s){const t=e.options;t&&s&&Object.assign(t,s)}(b.current,m)}),[i,m]),s.useEffect((()=>{!i&&b.current&&k(b.current.config.data,o.labels)}),[i,o.labels]),s.useEffect((()=>{!i&&b.current&&o.datasets&&S(b.current.config.data,o.datasets,n)}),[i,o.datasets]),s.useEffect((()=>{b.current&&(i?(j(),setTimeout(f)):b.current.update(u))}),[i,m,o.labels,o.datasets,u]),s.useEffect((()=>{b.current&&(j(),setTimeout(f))}),[c]),s.useEffect((()=>(f(),()=>j())),[]),t.createElement("canvas",{ref:h,role:"img",height:l,width:r,...g},p)}const R=s.forwardRef(C);function O(e,a){return d.register(a),s.forwardRef(((s,a)=>t.createElement(R,{...s,ref:a,type:e})))}const q=O("line",x),A=O("bar",m),P=O("pie",o);d.register(p,u,g,h,b,f,j,y,N);const B=()=>{var t,d,o,m;const{id:x}=a(),[p,u]=s.useState(null),[g,h]=s.useState(null),[b,f]=s.useState(!0),[j,y]=s.useState(""),[N,v]=s.useState("30d");if(s.useEffect((()=>{(async()=>{try{f(!0);const e=await n(x);u(e);const s=await c(x);h(s),f(!1)}catch(e){y("Failed to load analytics. Please try again later."),f(!1)}})()}),[x,N]),b)return e.jsx("div",{className:"flex justify-center items-center min-h-screen bg-gray-50",children:e.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500"})});if(!p||!g)return e.jsx("div",{className:"bg-gray-50 py-12",children:e.jsx("div",{className:"container mx-auto px-4",children:e.jsxs("div",{className:"max-w-4xl mx-auto",children:[e.jsx(r,{type:"error",message:j||"Email sequence not found.",className:"mb-8"}),e.jsx("div",{className:"text-center",children:e.jsx(l,{to:"/admin/email-sequences",children:e.jsx(i,{children:"Back to Email Sequences"})})})]})})});const w={labels:Object.keys(g.dailyStats||{}).slice(-30),datasets:[{label:"Sent",data:Object.values(g.dailyStats||{}).map((e=>e.email_sent||0)).slice(-30),borderColor:"rgb(99, 102, 241)",backgroundColor:"rgba(99, 102, 241, 0.5)"},{label:"Opened",data:Object.values(g.dailyStats||{}).map((e=>e.email_open||0)).slice(-30),borderColor:"rgb(52, 211, 153)",backgroundColor:"rgba(52, 211, 153, 0.5)"},{label:"Clicked",data:Object.values(g.dailyStats||{}).map((e=>e.email_click||0)).slice(-30),borderColor:"rgb(251, 191, 36)",backgroundColor:"rgba(251, 191, 36, 0.5)"}]},k={labels:(null==(t=g.emailStats)?void 0:t.map((e=>`Email ${e.order}`)))||[],datasets:[{label:"Open Rate (%)",data:(null==(d=g.emailStats)?void 0:d.map((e=>parseFloat(e.openRate))))||[],backgroundColor:"rgba(52, 211, 153, 0.7)"},{label:"Click Rate (%)",data:(null==(o=g.emailStats)?void 0:o.map((e=>parseFloat(e.clickRate))))||[],backgroundColor:"rgba(251, 191, 36, 0.7)"}]},S={labels:["Sent","Opened","Clicked"],datasets:[{data:[g.sent,g.opened,g.clicked],backgroundColor:["rgba(99, 102, 241, 0.7)","rgba(52, 211, 153, 0.7)","rgba(251, 191, 36, 0.7)"],borderColor:["rgb(99, 102, 241)","rgb(52, 211, 153)","rgb(251, 191, 36)"],borderWidth:1}]};return e.jsx("div",{className:"bg-gray-50 py-12",children:e.jsx("div",{className:"container mx-auto px-4",children:e.jsxs("div",{className:"max-w-6xl mx-auto",children:[e.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center mb-8",children:[e.jsxs("div",{children:[e.jsxs("h1",{className:"text-3xl font-bold mb-2",children:[p.name," Analytics"]}),e.jsx("p",{className:"text-gray-600",children:p.description})]}),e.jsxs("div",{className:"mt-4 md:mt-0 flex space-x-4",children:[e.jsx(l,{to:`/admin/email-sequences/edit/${x}`,children:e.jsx(i,{variant:"outline",children:"Edit Sequence"})}),e.jsx(l,{to:"/admin/email-sequences",children:e.jsx(i,{variant:"outline",children:"Back to Sequences"})})]})]}),j&&e.jsx(r,{type:"error",message:j,onClose:()=>y(""),className:"mb-8"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:[e.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6",children:[e.jsx("h2",{className:"text-lg font-semibold mb-2",children:"Total Subscribers"}),e.jsx("p",{className:"text-3xl font-bold text-purple-600",children:g.users}),e.jsx("p",{className:"text-sm text-gray-500 mt-1",children:"Users in this sequence"})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6",children:[e.jsx("h2",{className:"text-lg font-semibold mb-2",children:"Open Rate"}),e.jsxs("p",{className:"text-3xl font-bold text-green-600",children:[g.openRate,"%"]}),e.jsx("p",{className:"text-sm text-gray-500 mt-1",children:"Average across all emails"})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6",children:[e.jsx("h2",{className:"text-lg font-semibold mb-2",children:"Click Rate"}),e.jsxs("p",{className:"text-3xl font-bold text-yellow-600",children:[g.clickRate,"%"]}),e.jsx("p",{className:"text-sm text-gray-500 mt-1",children:"Average across all emails"})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-8",children:[e.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6",children:[e.jsx("h2",{className:"text-lg font-semibold mb-4",children:"Daily Performance"}),e.jsx(q,{data:w,options:{responsive:!0,plugins:{legend:{position:"top"},title:{display:!0,text:"Daily Email Activity"}}}})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6",children:[e.jsx("h2",{className:"text-lg font-semibold mb-4",children:"Overall Performance"}),e.jsx(P,{data:S,options:{responsive:!0,plugins:{legend:{position:"top"},title:{display:!0,text:"Email Engagement"}}}})]})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6 mb-8",children:[e.jsx("h2",{className:"text-lg font-semibold mb-4",children:"Email Performance"}),e.jsx(A,{data:k,options:{responsive:!0,plugins:{legend:{position:"top"},title:{display:!0,text:"Open and Click Rates by Email"}}}})]}),e.jsx("div",{className:"bg-white rounded-lg shadow-md overflow-hidden mb-8",children:e.jsxs("div",{className:"p-6",children:[e.jsx("h2",{className:"text-lg font-semibold mb-4",children:"Email Details"}),e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[e.jsx("thead",{className:"bg-gray-50",children:e.jsxs("tr",{children:[e.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Email"}),e.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Sent"}),e.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Opened"}),e.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Clicked"}),e.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Open Rate"}),e.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Click Rate"})]})}),e.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:null==(m=g.emailStats)?void 0:m.map((s=>e.jsxs("tr",{children:[e.jsxs("td",{className:"px-6 py-4 whitespace-nowrap",children:[e.jsxs("div",{className:"text-sm font-medium text-gray-900",children:["Email ",s.order]}),e.jsx("div",{className:"text-sm text-gray-500",children:s.subject})]}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:s.sent}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:s.opened}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:s.clicked}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsxs("span",{className:"px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800",children:[s.openRate,"%"]})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsxs("span",{className:"px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800",children:[s.clickRate,"%"]})})]},s.id)))})]})})]})})]})})})};export{B as default};
