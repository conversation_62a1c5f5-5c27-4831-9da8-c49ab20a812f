import{j as e}from"./ui-CLS_rXuQ.js";import{f as s,u as a,r as t,L as i}from"./router-Bmo9-suO.js";import{A as l,B as r}from"./index-DQKqxzWd.js";import{a as d,d as c}from"./emailSequenceService-vhh7EWiT.js";import"./vendor-mk1rzcpA.js";const n=()=>{const{id:n}=s(),m=a(),[x,o]=t.useState(null),[h,j]=t.useState(!0),[u,g]=t.useState(""),[p,v]=t.useState("");t.useEffect((()=>{(async()=>{try{const e=await d(n);o(e),j(!1)}catch(e){g("Failed to load email sequence. Please try again later."),j(!1)}})()}),[n]);return h?e.jsx("div",{className:"flex justify-center items-center min-h-screen bg-gray-50",children:e.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500"})}):x?e.jsx("div",{className:"bg-gray-50 py-12",children:e.jsx("div",{className:"container mx-auto px-4",children:e.jsxs("div",{className:"max-w-4xl mx-auto",children:[e.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center mb-8",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold mb-2",children:x.name}),e.jsx("p",{className:"text-gray-600",children:x.description})]}),e.jsxs("div",{className:"mt-4 md:mt-0 flex space-x-4",children:[e.jsx(i,{to:`/admin/email-sequences/edit/${n}`,children:e.jsx(r,{children:"Edit Sequence"})}),e.jsx(i,{to:`/admin/email-sequences/${n}/analytics`,children:e.jsx(r,{variant:"secondary",children:"View Analytics"})}),e.jsx(r,{variant:"outline",onClick:async()=>{if(window.confirm("Are you sure you want to delete this email sequence?"))try{await c(n),v("Email sequence deleted successfully."),setTimeout((()=>{m("/admin/email-sequences")}),1500)}catch(e){g("Failed to delete email sequence. Please try again later.")}},children:"Delete"})]})]}),u&&e.jsx(l,{type:"error",message:u,onClose:()=>g(""),className:"mb-8"}),p&&e.jsx(l,{type:"success",message:p,onClose:()=>v(""),className:"mb-8"}),e.jsx("div",{className:"bg-white rounded-lg shadow-md overflow-hidden mb-8",children:e.jsx("div",{className:"p-6",children:e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[e.jsxs("div",{children:[e.jsx("h2",{className:"text-sm font-medium text-gray-500",children:"Status"}),e.jsx("p",{className:"mt-1 text-lg "+(x.isActive?"text-green-600":"text-red-600"),children:x.isActive?"Active":"Inactive"})]}),e.jsxs("div",{children:[e.jsx("h2",{className:"text-sm font-medium text-gray-500",children:"Trigger"}),e.jsx("p",{className:"mt-1 text-lg",children:x.trigger.replace("_"," ").replace(/\b\w/g,(e=>e.toUpperCase()))})]}),e.jsxs("div",{children:[e.jsx("h2",{className:"text-sm font-medium text-gray-500",children:"Created"}),e.jsx("p",{className:"mt-1 text-lg",children:new Date(x.createdAt).toLocaleDateString()})]}),e.jsxs("div",{children:[e.jsx("h2",{className:"text-sm font-medium text-gray-500",children:"Last Updated"}),e.jsx("p",{className:"mt-1 text-lg",children:new Date(x.updatedAt).toLocaleDateString()})]})]})})}),e.jsx("div",{className:"bg-white rounded-lg shadow-md overflow-hidden",children:e.jsxs("div",{className:"p-6",children:[e.jsx("h2",{className:"text-xl font-semibold mb-6",children:"Email Sequence"}),0===x.emails.length?e.jsx("div",{className:"bg-gray-50 p-6 text-center rounded-lg",children:e.jsx("p",{className:"text-gray-500",children:"No emails in this sequence yet."})}):e.jsx("div",{className:"space-y-8",children:x.emails.sort(((e,s)=>e.order-s.order)).map(((s,a)=>e.jsxs("div",{className:"relative",children:[a<x.emails.length-1&&e.jsx("div",{className:"absolute left-6 top-16 bottom-0 w-0.5 bg-gray-200"}),e.jsxs("div",{className:"flex",children:[e.jsx("div",{className:"flex-shrink-0 mr-4",children:e.jsx("div",{className:"flex items-center justify-center w-12 h-12 rounded-full bg-purple-100 text-purple-600 font-semibold",children:s.order})}),e.jsx("div",{className:"flex-grow",children:e.jsxs("div",{className:"border rounded-lg overflow-hidden "+(s.isActive?"border-gray-200":"border-gray-200 bg-gray-50"),children:[e.jsx("div",{className:"p-4 border-b border-gray-200",children:e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("h3",{className:"text-lg font-medium",children:s.subject}),e.jsxs("div",{className:"flex items-center",children:[e.jsxs("span",{className:"bg-purple-100 text-purple-800 text-xs font-semibold px-2.5 py-0.5 rounded-full mr-2",children:["Day ",s.delayDays]}),e.jsx("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium "+(s.isActive?"bg-green-100 text-green-800":"bg-red-100 text-red-800"),children:s.isActive?"Active":"Inactive"})]})]})}),e.jsx("div",{className:"p-4",children:e.jsx("div",{className:"prose max-w-none",dangerouslySetInnerHTML:{__html:s.body}})})]})})]})]},s._id||s.id)))})]})}),e.jsx("div",{className:"mt-8 text-center",children:e.jsx(i,{to:"/admin/email-sequences",children:e.jsx(r,{variant:"outline",children:"Back to Email Sequences"})})})]})})}):e.jsx("div",{className:"bg-gray-50 py-12",children:e.jsx("div",{className:"container mx-auto px-4",children:e.jsxs("div",{className:"max-w-4xl mx-auto",children:[e.jsx(l,{type:"error",message:"Email sequence not found.",className:"mb-8"}),e.jsx("div",{className:"text-center",children:e.jsx(i,{to:"/admin/email-sequences",children:e.jsx(r,{children:"Back to Email Sequences"})})})]})})})};export{n as default};
