import{j as e}from"./ui-CLS_rXuQ.js";import{r}from"./router-Bmo9-suO.js";function t({title:e,titleId:t,...s},a){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},s),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m4.5 12.75 6 6 9-13.5"}))}const s=r.forwardRef(t);function a({title:e,titleId:t,...s},a){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},s),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3.98 8.223A10.477 10.477 0 0 0 1.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.451 10.451 0 0 1 12 4.5c4.756 0 8.773 3.162 10.065 7.498a10.522 10.522 0 0 1-4.293 5.774M6.228 6.228 3 3m3.228 3.228 3.65 3.65m7.894 7.894L21 21m-3.228-3.228-3.65-3.65m0 0a3 3 0 1 0-4.243-4.243m4.242 4.242L9.88 9.88"}))}const l=r.forwardRef(a);function o({title:e,titleId:t,...s},a){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},s),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z"}),r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}))}const n=r.forwardRef(o);function i({title:e,titleId:t,...s},a){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},s),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6 18 18 6M6 6l12 12"}))}const d=r.forwardRef(i),c=({password:r="",showRequirements:t=!0,className:a=""})=>{const l=[{id:"length",label:"At least 8 characters",test:e=>e.length>=8,weight:2},{id:"lowercase",label:"One lowercase letter",test:e=>/[a-z]/.test(e),weight:1},{id:"uppercase",label:"One uppercase letter",test:e=>/[A-Z]/.test(e),weight:1},{id:"number",label:"One number",test:e=>/\d/.test(e),weight:1},{id:"special",label:"One special character (!@#$%^&*)",test:e=>/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(e),weight:2},{id:"noCommon",label:"Not a common password",test:e=>!o(e),weight:1}],o=e=>["password","123456","123456789","qwerty","abc123","password123","admin","letmein","welcome","monkey","1234567890","password1","123123","qwerty123"].includes(e.toLowerCase()),n=(e=>{if(!e)return{score:0,level:"none",percentage:0};const r=l.filter((r=>r.test(e))),t=l.reduce(((e,r)=>e+r.weight),0),s=r.reduce(((e,r)=>e+r.weight),0),a=Math.round(s/t*100);let o="weak",n=1;return a>=90?(o="very-strong",n=5):a>=75?(o="strong",n=4):a>=50?(o="medium",n=3):a>=25?(o="weak",n=2):(o="very-weak",n=1),{score:n,level:o,percentage:a,passedCriteria:r}})(r),i={none:{color:"bg-gray-200",textColor:"text-gray-500",label:"Enter password",barColor:"bg-gray-200"},"very-weak":{color:"bg-red-500",textColor:"text-red-600",label:"Very Weak",barColor:"bg-red-500"},weak:{color:"bg-orange-500",textColor:"text-orange-600",label:"Weak",barColor:"bg-orange-500"},medium:{color:"bg-yellow-500",textColor:"text-yellow-600",label:"Medium",barColor:"bg-yellow-500"},strong:{color:"bg-blue-500",textColor:"text-blue-600",label:"Strong",barColor:"bg-blue-500"},"very-strong":{color:"bg-green-500",textColor:"text-green-600",label:"Very Strong",barColor:"bg-green-500"}}[n.level];return e.jsxs("div",{className:`space-y-3 ${a}`,children:[e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"text-sm font-medium text-gray-700",children:"Password Strength"}),e.jsx("span",{className:`text-sm font-medium ${i.textColor}`,children:i.label})]}),e.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2 overflow-hidden",children:e.jsx("div",{className:`h-full ${i.barColor} transition-all duration-300 ease-out`,style:{width:`${n.percentage}%`}})}),e.jsx("div",{className:"flex space-x-1",children:[1,2,3,4,5].map((r=>e.jsx("div",{className:`h-1 flex-1 rounded-full transition-colors duration-200 ${n.score>=r?i.color:"bg-gray-200"}`},r)))})]}),t&&r&&e.jsxs("div",{className:"space-y-2",children:[e.jsx("h4",{className:"text-sm font-medium text-gray-700",children:"Requirements:"}),e.jsx("div",{className:"grid grid-cols-1 gap-1",children:l.map((t=>{const a=t.test(r);return e.jsxs("div",{className:"flex items-center space-x-2 text-sm transition-colors duration-200 "+(a?"text-green-600":"text-gray-500"),children:[a?e.jsx(s,{className:"h-4 w-4 text-green-500"}):e.jsx(d,{className:"h-4 w-4 text-gray-400"}),e.jsx("span",{className:a?"line-through":"",children:t.label})]},t.id)}))})]}),r&&"very-strong"!==n.level&&e.jsx("div",{className:"bg-blue-50 border border-blue-200 rounded-md p-3",children:e.jsxs("div",{className:"flex",children:[e.jsx("div",{className:"flex-shrink-0",children:e.jsx("svg",{className:"h-5 w-5 text-blue-400",viewBox:"0 0 20 20",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z",clipRule:"evenodd"})})}),e.jsxs("div",{className:"ml-3",children:[e.jsx("h3",{className:"text-sm font-medium text-blue-800",children:"Security Tip"}),e.jsx("div",{className:"mt-1 text-sm text-blue-700",children:m(n.level)})]})]})})]})},m=e=>{const r={"very-weak":"Your password is too weak. Add more characters and include numbers, symbols, and mixed case letters.",weak:"Consider adding special characters and making your password longer for better security.",medium:"Good start! Add a few more characters or special symbols to make it even stronger.",strong:"Great password! Consider adding one more special character to make it even more secure.","very-strong":"Excellent! Your password is very secure."};return r[e]||r.weak},u=({value:t="",onChange:s,placeholder:a="Enter your password",label:o="Password",showStrengthIndicator:i=!1,showRequirements:d=!0,error:m="",required:u=!1,className:x="",name:h="password",id:g="password",...w})=>{const[p,b]=r.useState(!1);return e.jsxs("div",{className:`space-y-2 ${x}`,children:[o&&e.jsxs("label",{htmlFor:g,className:"block text-sm font-medium text-gray-700",children:[o,u&&e.jsx("span",{className:"text-red-500 ml-1",children:"*"})]}),e.jsxs("div",{className:"relative",children:[e.jsx("input",{type:p?"text":"password",id:g,name:h,value:t,onChange:e=>{s&&s(e)},placeholder:a,required:u,className:`\n            block w-full px-3 py-2 pr-10 border rounded-md shadow-sm \n            placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 \n            focus:border-purple-500 transition-colors duration-200\n            ${m?"border-red-300 focus:ring-red-500 focus:border-red-500":"border-gray-300"}\n          `,...w}),e.jsx("button",{type:"button",onClick:()=>{b(!p)},className:"absolute inset-y-0 right-0 pr-3 flex items-center hover:text-purple-600 transition-colors duration-200",tabIndex:-1,children:p?e.jsx(l,{className:"h-5 w-5 text-gray-400"}):e.jsx(n,{className:"h-5 w-5 text-gray-400"})})]}),m&&e.jsxs("p",{className:"text-sm text-red-600 flex items-center",children:[e.jsx("svg",{className:"h-4 w-4 mr-1",fill:"currentColor",viewBox:"0 0 20 20",children:e.jsx("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})}),m]}),i&&t&&e.jsx("div",{className:"mt-3",children:e.jsx(c,{password:t,showRequirements:d})})]})};export{u as P};
