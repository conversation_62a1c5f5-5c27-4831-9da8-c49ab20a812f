import{j as e}from"./ui-CLS_rXuQ.js";import{r as s,L as r}from"./router-Bmo9-suO.js";import{j as a,k as t,l,m as n,n as i,o as d,p as c,B as o,A as m}from"./index-DQKqxzWd.js";import{P as x}from"./PageHeader-BTn9jdY4.js";import{g as u}from"./emailSequenceService-vhh7EWiT.js";import{g as h}from"./analyticsService-DGKX5lwb.js";import"./vendor-mk1rzcpA.js";const g=({size:s="medium",color:r="purple"})=>e.jsx("div",{className:"flex justify-center items-center min-h-[200px]",children:e.jsx("div",{className:`animate-spin rounded-full ${{small:"h-6 w-6",medium:"h-12 w-12",large:"h-16 w-16"}[s]} border-t-2 border-b-2 ${{purple:"border-purple-500",blue:"border-blue-500",green:"border-green-500",red:"border-red-500",gray:"border-gray-500"}[r]}`})}),p=({message:s,onClose:r})=>e.jsx("div",{className:"bg-red-50 border-l-4 border-red-500 p-4 mb-6",children:e.jsxs("div",{className:"flex items-start",children:[e.jsx("div",{className:"flex-shrink-0",children:e.jsx("svg",{className:"h-5 w-5 text-red-500",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})}),e.jsx("div",{className:"ml-3 flex-1",children:e.jsx("p",{className:"text-sm text-red-700",children:s})}),r&&e.jsx("div",{className:"ml-auto pl-3",children:e.jsx("div",{className:"-mx-1.5 -my-1.5",children:e.jsxs("button",{onClick:r,className:"inline-flex rounded-md p-1.5 text-red-500 hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500",children:[e.jsx("span",{className:"sr-only",children:"Dismiss"}),e.jsx("svg",{className:"h-5 w-5",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z",clipRule:"evenodd"})})]})})})]})}),b=()=>{const[o,m]=s.useState([]),[x,b]=s.useState({}),[j,v]=s.useState(!0),[f,N]=s.useState(null),[y,w]=s.useState("all");s.useEffect((()=>{(async()=>{try{v(!0);const e=await u();m(e);const s=e.map((e=>h(e._id))),r=await Promise.all(s),a={};e.forEach(((e,s)=>{a[e._id]=r[s]||{sent:0,opened:0,clicked:0,users:0}})),b(a),N(null)}catch(e){N("Failed to load email sequences. Please try again later.")}finally{v(!1)}})()}),[]);const _=o.filter((e=>"all"===y||e.trigger===y)),C=s=>{switch(s){case"lead_capture":return e.jsx(t,{className:"text-blue-500"});case"purchase":return e.jsx(l,{className:"text-green-500"});case"user_inactive":return e.jsx(n,{className:"text-yellow-500"});case"cart_abandoned":return e.jsx(l,{className:"text-red-500"});default:return e.jsx(c,{className:"text-gray-500"})}},S=e=>{switch(e){case"lead_capture":return"Lead Capture";case"purchase":return"Purchase";case"user_inactive":return"User Inactivity";case"cart_abandoned":return"Abandoned Cart";default:return e}};return j?e.jsx(g,{}):f?e.jsx(p,{message:f}):e.jsxs("div",{className:"bg-white shadow rounded-lg p-6",children:[e.jsxs("div",{className:"flex justify-between items-center mb-6",children:[e.jsx("h2",{className:"text-2xl font-bold text-gray-800",children:"Email Sequences"}),e.jsxs(r,{to:"/admin/email-sequences/new",className:"inline-flex items-center px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500",children:[e.jsx(a,{className:"mr-2"})," Create Sequence"]})]}),e.jsx("div",{className:"mb-6 border-b border-gray-200",children:e.jsxs("nav",{className:"flex -mb-px",children:[e.jsx("button",{onClick:()=>w("all"),className:"mr-8 py-4 px-1 border-b-2 font-medium text-sm "+("all"===y?"border-purple-500 text-purple-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"),children:"All Sequences"}),e.jsxs("button",{onClick:()=>w("lead_capture"),className:"mr-8 py-4 px-1 border-b-2 font-medium text-sm "+("lead_capture"===y?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"),children:[e.jsx(t,{className:"inline mr-1"})," Lead Capture"]}),e.jsxs("button",{onClick:()=>w("purchase"),className:"mr-8 py-4 px-1 border-b-2 font-medium text-sm "+("purchase"===y?"border-green-500 text-green-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"),children:[e.jsx(l,{className:"inline mr-1"})," Purchase"]}),e.jsxs("button",{onClick:()=>w("cart_abandoned"),className:"mr-8 py-4 px-1 border-b-2 font-medium text-sm "+("cart_abandoned"===y?"border-red-500 text-red-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"),children:[e.jsx(l,{className:"inline mr-1"})," Abandoned Cart"]}),e.jsxs("button",{onClick:()=>w("user_inactive"),className:"mr-8 py-4 px-1 border-b-2 font-medium text-sm "+("user_inactive"===y?"border-yellow-500 text-yellow-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"),children:[e.jsx(n,{className:"inline mr-1"})," User Inactivity"]})]})}),0===_.length?e.jsxs("div",{className:"text-center py-8",children:[e.jsx("p",{className:"text-gray-500",children:"No email sequences found for this category."}),e.jsxs(r,{to:"/admin/email-sequences/new",className:"inline-flex items-center mt-4 px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500",children:[e.jsx(a,{className:"mr-2"})," Create Sequence"]})]}):e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:_.map((s=>{var a,t,l,n,c;return e.jsx("div",{className:"border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200",children:e.jsxs("div",{className:"p-5",children:[e.jsxs("div",{className:"flex items-center mb-3",children:[C(s.trigger),e.jsx("span",{className:"ml-2 text-sm font-medium text-gray-500",children:S(s.trigger)}),e.jsx("span",{className:"ml-auto px-2 py-1 text-xs rounded-full "+(s.isActive?"bg-green-100 text-green-800":"bg-gray-100 text-gray-800"),children:s.isActive?"Active":"Inactive"})]}),e.jsx("h3",{className:"text-lg font-semibold text-gray-800 mb-2",children:s.name}),e.jsx("p",{className:"text-gray-600 text-sm mb-4 line-clamp-2",children:s.description}),e.jsxs("div",{className:"grid grid-cols-2 gap-4 mb-4",children:[e.jsxs("div",{className:"bg-gray-50 p-3 rounded-lg",children:[e.jsx("div",{className:"text-sm text-gray-500",children:"Emails"}),e.jsx("div",{className:"text-xl font-semibold",children:s.emails.length})]}),e.jsxs("div",{className:"bg-gray-50 p-3 rounded-lg",children:[e.jsx("div",{className:"text-sm text-gray-500",children:"Users"}),e.jsx("div",{className:"text-xl font-semibold",children:(null==(a=x[s._id])?void 0:a.users)||0})]}),e.jsxs("div",{className:"bg-gray-50 p-3 rounded-lg",children:[e.jsx("div",{className:"text-sm text-gray-500",children:"Sent"}),e.jsx("div",{className:"text-xl font-semibold",children:(null==(t=x[s._id])?void 0:t.sent)||0})]}),e.jsxs("div",{className:"bg-gray-50 p-3 rounded-lg",children:[e.jsx("div",{className:"text-sm text-gray-500",children:"Open Rate"}),e.jsxs("div",{className:"text-xl font-semibold",children:[(null==(l=x[s._id])?void 0:l.sent)?Math.round((null==(n=x[s._id])?void 0:n.opened)/(null==(c=x[s._id])?void 0:c.sent)*100):0,"%"]})]})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx(r,{to:`/admin/email-sequences/${s._id}`,className:"text-purple-600 hover:text-purple-800 font-medium text-sm",children:"View Details"}),e.jsxs("div",{className:"flex space-x-4",children:[e.jsxs(r,{to:`/admin/email-sequences/${s._id}/analytics`,className:"text-blue-600 hover:text-blue-800 font-medium text-sm flex items-center",children:[e.jsx(i,{className:"mr-1"})," Analytics"]}),e.jsxs(r,{to:`/admin/email-sequences/${s._id}/edit`,className:"text-gray-600 hover:text-gray-800 font-medium text-sm flex items-center",children:[e.jsx(d,{className:"mr-1"})," Edit"]})]})]})]})},s._id)}))})]})},j=()=>{const[a,t]=s.useState([]),[l,n]=s.useState(!0),[i,d]=s.useState(""),[c,h]=s.useState("");return s.useEffect((()=>{(async()=>{try{const e=await u();t(e),n(!1)}catch(e){d("Failed to load email sequences. Please try again later."),n(!1)}})()}),[]),l?e.jsx("div",{className:"flex justify-center items-center min-h-screen bg-gray-50",children:e.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500"})}):e.jsx("div",{className:"bg-gray-50 py-12",children:e.jsx("div",{className:"container mx-auto px-4",children:e.jsxs("div",{className:"max-w-7xl mx-auto",children:[e.jsx(x,{title:"Email Sequence Management",description:"Create and manage automated email sequences",breadcrumbs:[{label:"Email Sequences",path:"/admin/email-sequences"}],actions:e.jsx(r,{to:"/admin/email-sequences/new",children:e.jsxs(o,{children:[e.jsx("svg",{className:"h-5 w-5 mr-2",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})}),"Create New Sequence"]})})}),i&&e.jsx(m,{type:"error",message:i,onClose:()=>d(""),className:"mb-8"}),c&&e.jsx(m,{type:"success",message:c,onClose:()=>h(""),className:"mb-8"}),e.jsx(b,{})]})})})};export{j as default};
