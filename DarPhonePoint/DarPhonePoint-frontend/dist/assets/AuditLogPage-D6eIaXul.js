import{j as e}from"./ui-CLS_rXuQ.js";import{r as t}from"./router-Bmo9-suO.js";import{a,L as s,B as r,A as l}from"./index-DQKqxzWd.js";import{P as n}from"./PageHeader-BTn9jdY4.js";import{F as i,P as c}from"./Pagination-DhJHA7Qs.js";import{E as o}from"./ErrorBoundary-jAhIDQkO.js";import"./vendor-mk1rzcpA.js";const d=()=>{const[d,u]=t.useState([]),[m,x]=t.useState(!0),[p,g]=t.useState(""),[h,y]=t.useState(1),[b,v]=t.useState(1),[j,f]=t.useState({action:"",resource_type:"",start_date:"",end_date:""}),w=[{value:"",label:"All Actions"},{value:"create",label:"Create"},{value:"update",label:"Update"},{value:"delete",label:"Delete"},{value:"login",label:"Login"},{value:"logout",label:"Logout"},{value:"view",label:"View"},{value:"export",label:"Export"},{value:"import",label:"Import"},{value:"settings_change",label:"Settings Change"},{value:"password_reset",label:"Password Reset"},{value:"email_sent",label:"Email Sent"},{value:"payment_processed",label:"Payment Processed"},{value:"refund_processed",label:"Refund Processed"},{value:"other",label:"Other"}],N=[{value:"",label:"All Resources"},{value:"user",label:"User"},{value:"product",label:"Product"},{value:"order",label:"Order"},{value:"lead",label:"Lead"},{value:"email_sequence",label:"Email Sequence"},{value:"settings",label:"Settings"},{value:"analytics",label:"Analytics"},{value:"auth",label:"Authentication"},{value:"payment",label:"Payment"},{value:"other",label:"Other"}];t.useEffect((()=>{_()}),[h,j]);const _=async()=>{x(!0),g("");try{const e={page:h,limit:50,...Object.fromEntries(Object.entries(j).filter((([e,t])=>""!==t)))},t=await(async(e={})=>{try{const t=new URLSearchParams;Object.entries(e).forEach((([e,a])=>{""!==a&&null!=a&&t.append(e,a)}));const s=t.toString(),r=s?`/audit-logs?${s}`:"/audit-logs",l=await a("get",r);return l.data||l}catch(p){return{data:[],total:0,pages:1,page:1}}})(e);Array.isArray(t)?(u(t),v(1)):t&&t.data&&Array.isArray(t.data)?(u(t.data),v(t.pages||Math.ceil(t.total/e.limit)||1)):(u([]),v(1)),x(!1)}catch(e){g("Failed to load audit logs. Please try again later."),x(!1)}},S=e=>{try{const t=new Date(e);return isNaN(t.getTime())?"Invalid Date":new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit",second:"2-digit"}).format(t)}catch(t){return"Invalid Date"}},k=e=>{if(!e)return"Unknown";const t=w.find((t=>t.value===e));return t?t.label:"string"==typeof e?e:"Unknown"},A=e=>{if(!e)return"Unknown";const t=N.find((t=>t.value===e));return t?t.label:"string"==typeof e?e:"Unknown"},P=e=>{switch(e){case"create":return"bg-green-100 text-green-800";case"update":return"bg-blue-100 text-blue-800";case"delete":return"bg-red-100 text-red-800";case"login":case"logout":return"bg-purple-100 text-purple-800";case"view":default:return"bg-gray-100 text-gray-800";case"settings_change":return"bg-yellow-100 text-yellow-800";case"payment_processed":case"refund_processed":return"bg-indigo-100 text-indigo-800"}};return m&&0===d.length?e.jsx(s,{fullPage:!0,text:"Loading audit logs..."}):e.jsx("div",{className:"bg-gray-50 py-12",children:e.jsx("div",{className:"container mx-auto px-4",children:e.jsxs("div",{className:"max-w-7xl mx-auto",children:[e.jsx(n,{title:"Audit Logs",description:"View and filter system activity logs",breadcrumbs:[{label:"Audit Logs",path:"/admin/audit-logs"}],actions:e.jsxs(r,{variant:"outline",onClick:_,children:[e.jsx("svg",{className:"h-5 w-5 mr-2",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})}),"Refresh"]})}),p&&e.jsx(l,{type:"error",message:p,onClose:()=>g(""),className:"mb-8"}),e.jsx(o,{children:e.jsx(i,{showSearch:!1,filters:[{name:"action",label:"Action",type:"select",options:w},{name:"resource_type",label:"Resource Type",type:"select",options:N},{name:"start_date",label:"Start Date",type:"date"},{name:"end_date",label:"End Date",type:"date"}],onFilterChange:e=>{f(e),y(1)}})}),e.jsx("div",{className:"bg-white rounded-lg shadow-md overflow-hidden",children:e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[e.jsx("thead",{className:"bg-gray-50",children:e.jsxs("tr",{children:[e.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Date & Time"}),e.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"User"}),e.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Action"}),e.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Resource"}),e.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Description"})]})}),e.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:m?e.jsx("tr",{children:e.jsx("td",{colSpan:"5",className:"px-6 py-4 text-center",children:e.jsx("div",{className:"flex justify-center",children:e.jsx("div",{className:"animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-purple-500"})})})}):0===d.length?e.jsx("tr",{children:e.jsx("td",{colSpan:"5",className:"px-6 py-4 text-center text-gray-500",children:"No logs found matching your criteria."})}):d.map((t=>e.jsxs("tr",{className:"hover:bg-gray-50",children:[e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:t.created_at?S(t.created_at):"N/A"}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:t.user?e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"flex-shrink-0 h-8 w-8 bg-purple-100 rounded-full flex items-center justify-center",children:e.jsx("span",{className:"text-sm font-medium text-purple-800",children:t.user.name?t.user.name.charAt(0).toUpperCase():"U"})}),e.jsxs("div",{className:"ml-4",children:[e.jsx("div",{className:"text-sm font-medium text-gray-900",children:t.user.name||"Unknown User"}),e.jsx("div",{className:"text-sm text-gray-500",children:t.user.email||""})]})]}):e.jsx("span",{className:"text-sm text-gray-500",children:"System"})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsx("span",{className:`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${P(t.action||"unknown")}`,children:t.action?k(t.action):"Unknown"})}),e.jsxs("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:[e.jsx("div",{className:"text-sm text-gray-900",children:t.resource_type?A(t.resource_type):"N/A"}),t.resource_id&&e.jsxs("div",{className:"text-xs text-gray-500",children:["ID: ",t.resource_id]})]}),e.jsx("td",{className:"px-6 py-4 text-sm text-gray-500",children:t.description||"No description available"})]},t._id||`log-${Math.random()}`)))})]})})}),b>1&&e.jsx(c,{currentPage:h,totalPages:b,totalItems:d.length,pageSize:50,onPageChange:y})]})})})};export{d as default};
