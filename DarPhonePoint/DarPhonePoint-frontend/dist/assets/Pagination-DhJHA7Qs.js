import{j as e}from"./ui-CLS_rXuQ.js";import{r as s,R as l}from"./router-Bmo9-suO.js";import{q as r,r as a,B as t,v as n,w as o,x as i,y as c}from"./index-DQKqxzWd.js";const d=({filters:l=[],onFilterChange:o,onSearch:i,showSearch:c=!0,searchPlaceholder:d="Search...",collapsible:m=!1})=>{const[x,u]=s.useState(""),[p,h]=s.useState({}),[g,b]=s.useState(!m),f=(e,s)=>{const l={...p,[e]:s};h(l),o&&o(l)},y=s=>{const{name:l,type:r,options:a}=s,t=p[l]||"";switch(r){case"select":return e.jsxs("select",{className:"block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-purple-500 focus:border-purple-500 sm:text-sm rounded-md",value:t,onChange:e=>f(l,e.target.value),children:[e.jsx("option",{value:"",children:"All"}),a.map((s=>e.jsx("option",{value:s.value,children:s.label},s.value)))]});case"date":return e.jsx("input",{type:"date",className:"block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-purple-500 focus:border-purple-500 sm:text-sm rounded-md",value:t,onChange:e=>f(l,e.target.value)});case"checkbox":return e.jsxs("div",{className:"flex items-center",children:[e.jsx("input",{type:"checkbox",className:"h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded",checked:!0===t,onChange:e=>f(l,e.target.checked)}),e.jsx("span",{className:"ml-2 text-sm text-gray-700",children:s.checkboxLabel||s.label})]});default:return e.jsx("input",{type:"text",className:"block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-purple-500 focus:border-purple-500 sm:text-sm rounded-md",value:t,onChange:e=>f(l,e.target.value),placeholder:s.placeholder||`Filter by ${s.label}`})}};return e.jsxs("div",{className:"bg-white p-4 rounded-lg shadow-sm mb-6",children:[e.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center mb-4",children:[c&&e.jsxs("div",{className:"relative w-full md:w-64 mb-4 md:mb-0",children:[e.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:e.jsx(r,{className:"h-4 w-4 text-gray-400"})}),e.jsx("input",{type:"text",className:"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-purple-500 focus:border-purple-500 sm:text-sm",placeholder:d,value:x,onChange:e=>{const s=e.target.value;u(s),i&&i(s)}}),x&&e.jsx("button",{className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>{u(""),i&&i("")},children:e.jsx(a,{className:"h-4 w-4 text-gray-400 hover:text-gray-600"})})]}),m&&e.jsxs(t,{variant:"outline",onClick:()=>{b(!g)},className:"md:ml-4",children:[e.jsx(n,{className:"mr-2 h-4 w-4"}),g?"Hide Filters":"Show Filters"]}),Object.keys(p).some((e=>""!==p[e]&&null!==p[e]&&void 0!==p[e]))&&e.jsxs(t,{variant:"outline",onClick:()=>{h({}),u(""),o&&o({}),i&&i("")},className:"md:ml-4",children:[e.jsx(a,{className:"mr-2 h-4 w-4"}),"Clear Filters"]})]}),g&&l.length>0&&e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 mt-4",children:l.map((s=>e.jsxs("div",{className:"flex flex-col",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:s.label}),y(s)]},s.name)))})]})},m=({currentPage:s=1,totalPages:r=1,totalItems:a=0,pageSize:t=10,onPageChange:n,onPageSizeChange:d,pageSizeOptions:m=[10,25,50,100]})=>{const x=e=>{e<1||e>r||n&&n(e)},u=(s-1)*t+1,p=Math.min(u+t-1,a);return e.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-center bg-white px-4 py-3 border-t border-gray-200 sm:px-6",children:[e.jsxs("div",{className:"flex flex-col md:flex-row items-center mb-4 md:mb-0",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("span",{className:"text-sm text-gray-700 mr-2",children:"Show"}),e.jsx("select",{className:"border-gray-300 rounded-md shadow-sm focus:border-purple-500 focus:ring focus:ring-purple-200 focus:ring-opacity-50",value:t,onChange:e=>{const s=parseInt(e.target.value,10);d&&d(s)},children:m.map((s=>e.jsx("option",{value:s,children:s},s)))})]}),e.jsxs("div",{className:"text-sm text-gray-700 ml-0 md:ml-4 mt-2 md:mt-0",children:["Showing ",e.jsx("span",{className:"font-medium",children:a>0?u:0})," to"," ",e.jsx("span",{className:"font-medium",children:p})," of"," ",e.jsx("span",{className:"font-medium",children:a})," results"]})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("button",{onClick:()=>x(s-1),disabled:1===s,className:"relative inline-flex items-center px-2 py-2 rounded-md border "+(1===s?"border-gray-300 bg-white text-gray-300 cursor-not-allowed":"border-gray-300 bg-white text-gray-700 hover:bg-gray-50"),children:[e.jsx("span",{className:"sr-only",children:"Previous"}),e.jsx(o,{className:"h-4 w-4"})]}),e.jsx("div",{className:"hidden md:flex mx-2",children:(()=>{const e=[];if(r<=5)for(let s=1;s<=r;s++)e.push(s);else{e.push(1);let l=Math.max(2,s-1),a=Math.min(r-1,s+1);s<=2?a=4:s>=r-1&&(l=r-3),l>2&&e.push("ellipsis1");for(let s=l;s<=a;s++)e.push(s);a<r-1&&e.push("ellipsis2"),r>1&&e.push(r)}return e})().map(((r,a)=>e.jsx(l.Fragment,{children:"ellipsis1"===r||"ellipsis2"===r?e.jsx("span",{className:"relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-gray-700",children:e.jsx(i,{className:"h-3 w-3"})}):e.jsx("button",{onClick:()=>x(r),className:"relative inline-flex items-center px-4 py-2 border "+(r===s?"border-purple-500 bg-purple-50 text-purple-600 z-10":"border-gray-300 bg-white text-gray-700 hover:bg-gray-50"),children:r})},a)))}),e.jsxs("button",{onClick:()=>x(s+1),disabled:s===r||0===r,className:"relative inline-flex items-center px-2 py-2 rounded-md border "+(s===r||0===r?"border-gray-300 bg-white text-gray-300 cursor-not-allowed":"border-gray-300 bg-white text-gray-700 hover:bg-gray-50"),children:[e.jsx("span",{className:"sr-only",children:"Next"}),e.jsx(c,{className:"h-4 w-4"})]})]})]})};export{d as F,m as P};
