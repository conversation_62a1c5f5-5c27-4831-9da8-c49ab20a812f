import{j as e}from"./ui-CLS_rXuQ.js";import{r as t,L as a}from"./router-Bmo9-suO.js";import{a as s,B as r,A as l,I as n}from"./index-DQKqxzWd.js";import"./vendor-mk1rzcpA.js";const i=()=>{const[i,d]=t.useState([]),[c,o]=t.useState([]),[m,x]=t.useState(!0),[u,p]=t.useState(""),[h,j]=t.useState(""),[g,f]=t.useState("all"),[y,w]=t.useState("all"),[b,v]=t.useState(!1);t.useEffect((()=>{(async()=>{try{const e=await(async()=>{const e=await s("get","/leads");return e.data||e})();let t=[];e.data&&Array.isArray(e.data)?t=e.data:Array.isArray(e)?t=e:e.data&&e.data.data&&Array.isArray(e.data.data)&&(t=e.data.data),t.sort(((e,t)=>new Date(t.created_at)-new Date(e.created_at))),d(t),o(t),x(!1)}catch(e){p("Failed to load leads. Please try again later."),x(!1)}})()}),[]),t.useEffect((()=>{let e=[...i];if(h){const t=h.toLowerCase();e=e.filter((e=>e.name&&e.name.toLowerCase().includes(t)||e.email&&e.email.toLowerCase().includes(t)))}"all"!==g&&(e=e.filter((e=>e.status===g))),"all"!==y&&(e=e.filter((e=>e.source===y))),o(e)}),[h,g,y,i]);const N=async(e,t)=>{try{await(async(e,t)=>{const a=await s("put",`/leads/${e}/status`,{status:t});return a.data||a})(e,t);const a=i.map((a=>a._id===e?{...a,status:t}:a));d(a)}catch(a){p("Failed to update lead status. Please try again later.")}};if(m)return e.jsx("div",{className:"flex justify-center items-center min-h-screen bg-gray-50",children:e.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500"})});const S=["all",...new Set(i.map((e=>e.source)))];return e.jsx("div",{className:"bg-gray-50 py-12",children:e.jsx("div",{className:"container mx-auto px-4",children:e.jsxs("div",{className:"max-w-7xl mx-auto",children:[e.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center mb-8",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold mb-2",children:"Lead Management"}),e.jsx("p",{className:"text-gray-600",children:"Manage and track your leads"})]}),e.jsx("div",{className:"mt-4 md:mt-0",children:e.jsxs(r,{onClick:async()=>{v(!0);try{const e=(e=>{const t=e.map((e=>[e.name||"",e.email||"",e.source||"",e.status||"",new Date(e.created_at).toLocaleString()]));return[["Name","Email","Source","Status","Created At"].join(","),...t.map((e=>e.join(",")))].join("\n")})(c),t=new Blob([e],{type:"text/csv;charset=utf-8;"}),a=URL.createObjectURL(t),s=document.createElement("a");s.href=a,s.setAttribute("download",`leads_export_${(new Date).toISOString().split("T")[0]}.csv`),document.body.appendChild(s),s.click(),document.body.removeChild(s),v(!1)}catch(e){p("Failed to export leads. Please try again later."),v(!1)}},isLoading:b,children:[e.jsx("svg",{className:"h-5 w-5 mr-2",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"})}),"Export CSV"]})})]}),u&&e.jsx(l,{type:"error",message:u,onClose:()=>p(""),className:"mb-8"}),e.jsx("div",{className:"bg-white rounded-lg shadow-md p-6 mb-8",children:e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[e.jsx("div",{children:e.jsx(n,{label:"Search",id:"search",type:"text",placeholder:"Search by name or email",value:h,onChange:e=>j(e.target.value)})}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"status-filter",className:"block text-sm font-medium text-gray-700 mb-1",children:"Status"}),e.jsxs("select",{id:"status-filter",className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent",value:g,onChange:e=>f(e.target.value),children:[e.jsx("option",{value:"all",children:"All Statuses"}),e.jsx("option",{value:"new",children:"New"}),e.jsx("option",{value:"contacted",children:"Contacted"}),e.jsx("option",{value:"converted",children:"Converted"}),e.jsx("option",{value:"unsubscribed",children:"Unsubscribed"})]})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"source-filter",className:"block text-sm font-medium text-gray-700 mb-1",children:"Source"}),e.jsx("select",{id:"source-filter",className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent",value:y,onChange:e=>w(e.target.value),children:S.map((t=>e.jsx("option",{value:t,children:"all"===t?"All Sources":t.charAt(0).toUpperCase()+t.slice(1)},t)))})]})]})}),e.jsx("div",{className:"bg-white rounded-lg shadow-md overflow-hidden",children:e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[e.jsx("thead",{className:"bg-gray-50",children:e.jsxs("tr",{children:[e.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Lead"}),e.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Source"}),e.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),e.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Date"}),e.jsx("th",{scope:"col",className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),e.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:c.length>0?c.map((t=>e.jsxs("tr",{children:[e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"flex-shrink-0 h-10 w-10 bg-purple-100 rounded-full flex items-center justify-center",children:e.jsx("span",{className:"text-purple-600 font-semibold",children:t.name?t.name.charAt(0).toUpperCase():t.email.charAt(0).toUpperCase()})}),e.jsxs("div",{className:"ml-4",children:[e.jsx("div",{className:"text-sm font-medium text-gray-900",children:t.name||"No Name"}),e.jsx("div",{className:"text-sm text-gray-500",children:t.email})]})]})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsx("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800",children:t.source||"Unknown"})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsxs("select",{className:"text-xs font-medium rounded-full px-2.5 py-1 border-0 "+("new"===t.status?"bg-green-100 text-green-800":"contacted"===t.status?"bg-blue-100 text-blue-800":"converted"===t.status?"bg-purple-100 text-purple-800":"bg-red-100 text-red-800"),value:t.status||"new",onChange:e=>N(t._id,e.target.value),children:[e.jsx("option",{value:"new",children:"New"}),e.jsx("option",{value:"contacted",children:"Contacted"}),e.jsx("option",{value:"converted",children:"Converted"}),e.jsx("option",{value:"unsubscribed",children:"Unsubscribed"})]})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:new Date(t.created_at).toLocaleDateString()}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium",children:e.jsxs("div",{className:"flex justify-end space-x-2",children:[e.jsx(a,{to:`/admin/leads/${t._id}`,className:"text-indigo-600 hover:text-indigo-900",children:"View"}),e.jsx("a",{href:`mailto:${t.email}?subject=Follow up from AIXcelerate&body=Hello ${t.name||"there"},\n\nThank you for your interest in AIXcelerate. I wanted to follow up with you personally.\n\nBest regards,\nAIXcelerate Team`,className:"text-blue-600 hover:text-blue-900",title:`Send email to ${t.email}`,children:"Email"})]})})]},t._id))):e.jsx("tr",{children:e.jsx("td",{colSpan:"5",className:"px-6 py-4 text-center text-gray-500",children:"No leads found matching your filters."})})})]})})})]})})})};export{i as default};
