import{j as e}from"./ui-CLS_rXuQ.js";import{f as s,u as a,r as t,L as l}from"./router-Bmo9-suO.js";import{u as r,B as d,A as o,s as n}from"./index-DQKqxzWd.js";import"./vendor-mk1rzcpA.js";const i=()=>{const{user:i}=r(),{id:c}=s(),m=a(),[u,h]=t.useState([]),[x,p]=t.useState(!0),[j,g]=t.useState(""),[f,w]=t.useState({}),[v,b]=t.useState(null),y=async e=>{try{if(b(e),f[e])return window.open(f[e],"_blank"),void b(null);const s=(await n({method:"GET",url:`/downloads/${e}`})).data.downloadUrl;w((a=>({...a,[e]:s}))),window.open(s,"_blank"),b(null)}catch(s){g("Failed to generate download link. Please try again later."),b(null)}};return t.useEffect((()=>{if(c)return y(c),void m("/downloads");(async()=>{try{const e=await n({method:"GET",url:"/orders"});if(!e.data.success||!e.data.data||0===e.data.data.length)return h([]),void p(!1);const s=e.data.data.filter((e=>"completed"===e.payment_status));if(0===s.length)return h([]),void p(!1);const a=[],t=new Set;s.forEach((e=>{e.product&&!t.has(e.product._id)&&(t.add(e.product._id),a.push(e.product))})),h(a),p(!1)}catch(e){g("Failed to load your downloads. Please try again later."),p(!1)}})()}),[i,c,m]),x?e.jsx("div",{className:"flex justify-center items-center min-h-screen bg-gray-50",children:e.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500"})}):e.jsx("div",{className:"bg-gray-50 py-12",children:e.jsx("div",{className:"container mx-auto px-4",children:e.jsxs("div",{className:"max-w-5xl mx-auto",children:[e.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center mb-8",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold mb-2",children:"Your Downloads"}),e.jsx("p",{className:"text-gray-600",children:"Access your purchased products"})]}),e.jsx("div",{className:"mt-4 md:mt-0",children:e.jsx(l,{to:"/dashboard",children:e.jsx(d,{variant:"outline",children:"Back to Dashboard"})})})]}),j&&e.jsx(o,{type:"error",message:j,onClose:()=>g(""),className:"mb-8"}),u.length>0?e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:u.map((s=>e.jsx("div",{className:"bg-white rounded-lg shadow-md overflow-hidden",children:e.jsxs("div",{className:"p-6",children:[e.jsx("div",{className:"flex items-center justify-center h-40 bg-gray-100 rounded-md mb-4",children:e.jsx("div",{className:"flex items-center justify-center w-full h-full bg-gradient-to-br from-purple-100 to-blue-100 text-purple-600 font-semibold text-lg",children:s.name.substring(0,2).toUpperCase()})}),e.jsx("h3",{className:"text-lg font-bold mb-2",children:s.name}),e.jsx("p",{className:"text-gray-600 text-sm mb-4",children:s.description?s.description.substring(0,100)+"...":"No description available"}),e.jsxs(d,{onClick:()=>y(s._id),fullWidth:!0,isLoading:v===s._id,children:[e.jsx("svg",{className:"h-5 w-5 mr-2",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z",clipRule:"evenodd"})}),"Download"]})]})},s._id)))}):e.jsxs("div",{className:"bg-white rounded-lg shadow-md p-8 text-center",children:[e.jsx("svg",{className:"h-16 w-16 text-gray-400 mx-auto mb-4",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"})}),e.jsx("h2",{className:"text-2xl font-bold mb-4",children:"No Downloads Available"}),e.jsx("p",{className:"text-gray-600 mb-6",children:"You haven't purchased any products yet."}),e.jsx("div",{className:"flex justify-center",children:e.jsx(l,{to:"/products",children:e.jsx(d,{children:"Browse Products"})})})]})]})})})};export{i as default};
