import{j as e}from"./ui-CLS_rXuQ.js";import{r as s,u as a,b as t,L as r}from"./router-Bmo9-suO.js";import{u as i,c as l,A as m,I as n,B as d}from"./index-DQKqxzWd.js";import"./vendor-mk1rzcpA.js";const o=()=>{const[o,c]=s.useState({email:"",password:""}),[x,u]=s.useState({}),[h,f]=s.useState(!1),[p,j]=s.useState(!1),{login:b,error:g,setError:y,user:v}=i(),N=a(),w=t();s.useEffect((()=>{v&&N("/dashboard")}),[v,N]),s.useEffect((()=>{y("")}),[y]),s.useEffect((()=>{var e,s;(null==(e=w.state)?void 0:e.message)&&(l.success(w.state.message),(null==(s=w.state)?void 0:s.email)&&c((e=>({...e,email:w.state.email}))))}),[w.state]);const S=e=>{c({...o,[e.target.name]:e.target.value}),x[e.target.name]&&u({...x,[e.target.name]:""})};return e.jsxs("div",{className:"min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8",children:[e.jsxs("div",{className:"sm:mx-auto sm:w-full sm:max-w-md",children:[e.jsx("div",{className:"flex justify-center",children:e.jsxs(r,{to:"/",className:"flex items-center",children:[e.jsx("span",{className:"logo-mark",children:"AI"}),e.jsx("span",{className:"text-xl font-bold",children:"Xcelerate"})]})}),e.jsx("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900",children:"Sign in to your account"}),e.jsxs("p",{className:"mt-2 text-center text-sm text-gray-600",children:["Or"," ",e.jsx(r,{to:"/register",className:"font-medium text-purple-600 hover:text-purple-500",children:"create a new account"})]})]}),e.jsx("div",{className:"mt-8 sm:mx-auto sm:w-full sm:max-w-md",children:e.jsxs("div",{className:"bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10",children:[g&&e.jsxs("div",{className:"mb-6",children:[e.jsx(m,{type:"error",message:"object"==typeof g?g.message:g,onClose:()=>y("")}),"object"==typeof g&&g.requiresVerification&&g.email&&e.jsxs("div",{className:"mt-3 p-3 bg-blue-50 border border-blue-200 rounded-md",children:[e.jsx("p",{className:"text-sm text-blue-800 mb-2",children:"Need to verify your email? We can resend the verification email."}),e.jsx("button",{onClick:()=>(async e=>{try{j(!0);const s=await fetch("/api/auth/resend-verification",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e})}),a=await s.json();a.success?(l.success("Verification email sent! Please check your inbox."),y("")):l.error(a.message||"Failed to resend verification email")}catch(s){l.error("Failed to resend verification email")}finally{j(!1)}})(g.email),disabled:p,className:"text-sm font-medium text-blue-600 hover:text-blue-500 disabled:opacity-50",children:p?"Sending...":"Resend verification email"})]})]}),e.jsxs("form",{className:"space-y-6",onSubmit:async e=>{var s,a;if(e.preventDefault(),(()=>{const e={};return o.email?/\S+@\S+\.\S+/.test(o.email)||(e.email="Email is invalid"):e.email="Email is required",o.password||(e.password="Password is required"),u(e),0===Object.keys(e).length})()){f(!0);try{await b(o);const e=(null==(a=null==(s=w.state)?void 0:s.from)?void 0:a.pathname)||"/dashboard";N(e)}catch(t){}finally{f(!1)}}},children:[e.jsx(n,{label:"Email address",id:"email",name:"email",type:"email",autoComplete:"email",value:o.email,onChange:S,error:x.email,required:!0}),e.jsx(n,{label:"Password",id:"password",name:"password",type:"password",autoComplete:"current-password",value:o.password,onChange:S,error:x.password,required:!0}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("input",{id:"remember_me",name:"remember_me",type:"checkbox",className:"h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"}),e.jsx("label",{htmlFor:"remember_me",className:"ml-2 block text-sm text-gray-900",children:"Remember me"})]}),e.jsx("div",{className:"text-sm",children:e.jsx("a",{href:"#",className:"font-medium text-purple-600 hover:text-purple-500",children:"Forgot your password?"})})]}),e.jsx("div",{children:e.jsx(d,{type:"submit",fullWidth:!0,size:"lg",isLoading:h,children:"Sign in"})})]}),e.jsxs("div",{className:"mt-6",children:[e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute inset-0 flex items-center",children:e.jsx("div",{className:"w-full border-t border-gray-300"})}),e.jsx("div",{className:"relative flex justify-center text-sm",children:e.jsx("span",{className:"px-2 bg-white text-gray-500",children:"Or continue with"})})]}),e.jsxs("div",{className:"mt-6 grid grid-cols-2 gap-3",children:[e.jsx("div",{children:e.jsxs("a",{href:"#",className:"w-full inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-500 hover:bg-gray-50",children:[e.jsx("span",{className:"sr-only",children:"Sign in with Google"}),e.jsx("i",{className:"fab fa-google text-red-500"})]})}),e.jsx("div",{children:e.jsxs("a",{href:"#",className:"w-full inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-500 hover:bg-gray-50",children:[e.jsx("span",{className:"sr-only",children:"Sign in with Facebook"}),e.jsx("i",{className:"fab fa-facebook text-blue-600"})]})})]})]})]})})]})};export{o as default};
