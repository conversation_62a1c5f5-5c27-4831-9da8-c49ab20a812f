import{j as e}from"./ui-CLS_rXuQ.js";import{f as s,u as r,r as a,L as t}from"./router-Bmo9-suO.js";import{A as l,B as d,s as n}from"./index-DQKqxzWd.js";import"./vendor-mk1rzcpA.js";const i=()=>{var i,o,c,m,x,h,u,p,j;const{id:g}=s(),v=r(),[b,N]=a.useState(null),[y,f]=a.useState(!0),[w,k]=a.useState(""),P=e=>e?e.status?e.status:"completed"===e.payment_status?"completed":"processing":"unknown";return a.useEffect((()=>{(async()=>{try{const e=await n({method:"GET",url:`/orders/${g}`}),s=e.data&&e.data.data?e.data.data:e.data;N(s),f(!1)}catch(e){k("Failed to load order details. Please try again later."),f(!1)}})()}),[g]),y?e.jsx("div",{className:"flex justify-center items-center min-h-screen bg-gray-50",children:e.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500"})}):w?e.jsx("div",{className:"bg-gray-50 py-12",children:e.jsx("div",{className:"container mx-auto px-4",children:e.jsxs("div",{className:"max-w-3xl mx-auto",children:[e.jsx(l,{type:"error",message:w,onClose:()=>k(""),className:"mb-8"}),e.jsx("div",{className:"flex justify-center",children:e.jsx(d,{onClick:()=>v("/orders"),variant:"outline",children:"Back to Orders"})})]})})}):b?e.jsx("div",{className:"bg-gray-50 py-12",children:e.jsx("div",{className:"container mx-auto px-4",children:e.jsxs("div",{className:"max-w-3xl mx-auto",children:[e.jsxs("div",{className:"flex justify-between items-center mb-8",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold mb-2",children:"Order Details"}),e.jsxs("p",{className:"text-gray-600",children:["Order #",b._id.substring(0,8)]})]}),e.jsx(d,{onClick:()=>v("/orders"),variant:"outline",children:"Back to Orders"})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow-md overflow-hidden mb-8",children:[e.jsx("div",{className:"px-6 py-4 border-b border-gray-200 bg-gray-50",children:e.jsx("h2",{className:"text-xl font-bold",children:"Order Summary"})}),e.jsx("div",{className:"p-6",children:e.jsxs("div",{className:"grid grid-cols-2 gap-6 mb-6",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500 mb-1",children:"Order Date"}),e.jsx("p",{className:"font-medium",children:new Date(b.created_at).toLocaleDateString()})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500 mb-1",children:"Order Status"}),(()=>{const s=P(b);return e.jsx("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium "+("completed"===s?"bg-green-100 text-green-800":"processing"===s?"bg-yellow-100 text-yellow-800":"cancelled"===s?"bg-red-100 text-red-800":"bg-gray-100 text-gray-800"),children:"completed"===s?"Completed":"processing"===s?"Processing":"cancelled"===s?"Cancelled":s})})()]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500 mb-1",children:"Payment Status"}),e.jsx("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium "+("completed"===b.payment_status?"bg-green-100 text-green-800":"bg-yellow-100 text-yellow-800"),children:"completed"===b.payment_status?"Paid":"Pending"})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500 mb-1",children:"Total Amount"}),e.jsxs("p",{className:"text-xl font-bold",children:["$",(_=b,_&&(_.amount||_.total)||0).toFixed(2)]})]})]})})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow-md overflow-hidden mb-8",children:[e.jsx("div",{className:"px-6 py-4 border-b border-gray-200 bg-gray-50",children:e.jsx("h2",{className:"text-xl font-bold",children:"Product Details"})}),e.jsxs("div",{className:"p-6",children:[b.product?e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"flex-shrink-0 h-20 w-20 bg-gray-200 rounded-md overflow-hidden",children:e.jsx("img",{src:`https://via.placeholder.com/80?text=${encodeURIComponent(((null==(i=b.product)?void 0:i.name)||"NA").substring(0,2))}`,alt:(null==(o=b.product)?void 0:o.name)||"Product",className:"h-20 w-20 object-cover"})}),e.jsxs("div",{className:"ml-6 flex-1",children:[e.jsx("h3",{className:"text-lg font-bold",children:(null==(c=b.product)?void 0:c.name)||"Product Unavailable"}),e.jsx("p",{className:"text-gray-500 mb-2",children:(null==(m=b.product)?void 0:m.product_type)||"Unknown"}),e.jsxs("p",{className:"text-gray-700",children:[(null==(h=null==(x=b.product)?void 0:x.description)?void 0:h.substring(0,100))||"No description available","..."]})]}),e.jsx("div",{className:"ml-6",children:e.jsxs("p",{className:"text-lg font-bold",children:["$",(null==(p=null==(u=b.product)?void 0:u.price)?void 0:p.toFixed(2))||"0.00"]})})]}):e.jsx("div",{className:"text-center py-8",children:e.jsx("p",{className:"text-gray-500",children:"Product information is no longer available."})}),"completed"===P(b)&&(null==(j=b.product)?void 0:j._id)&&e.jsx("div",{className:"mt-6 flex justify-end",children:e.jsxs(t,{to:`/downloads/${b.product._id}`,className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500",children:[e.jsx("svg",{className:"h-5 w-5 mr-2",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"})}),"Download Product"]})})]})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow-md overflow-hidden",children:[e.jsx("div",{className:"px-6 py-4 border-b border-gray-200 bg-gray-50",children:e.jsx("h2",{className:"text-xl font-bold",children:"Customer Information"})}),e.jsx("div",{className:"p-6",children:e.jsxs("div",{className:"grid grid-cols-2 gap-6",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500 mb-1",children:"Name"}),e.jsx("p",{className:"font-medium",children:b.customer_name||"N/A"})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500 mb-1",children:"Email"}),e.jsx("p",{className:"font-medium",children:b.customer_email})]})]})})]})]})})}):e.jsx("div",{className:"bg-gray-50 py-12",children:e.jsx("div",{className:"container mx-auto px-4",children:e.jsxs("div",{className:"max-w-3xl mx-auto text-center",children:[e.jsx("h2",{className:"text-2xl font-bold mb-4",children:"Order Not Found"}),e.jsx("p",{className:"text-gray-600 mb-6",children:"The order you're looking for doesn't exist or you don't have permission to view it."}),e.jsx(d,{onClick:()=>v("/orders"),variant:"outline",children:"Back to Orders"})]})})});var _};export{i as default};
