import{j as e}from"./ui-CLS_rXuQ.js";import{r as s,L as l}from"./router-Bmo9-suO.js";import{u as a,B as t,A as d,s as r}from"./index-DQKqxzWd.js";import"./vendor-mk1rzcpA.js";const i=()=>{var i;const{user:n}=a(),[c,o]=s.useState([]),[m,x]=s.useState([]),[h,u]=s.useState(!0),[p,j]=s.useState(""),v=e=>e?e.status?String(e.status).toLowerCase():e.payment_status?"completed"===e.payment_status?"completed":"processing":"pending":"unknown",g=e=>e&&(e.amount||e.total)||0;return s.useEffect((()=>{(async()=>{try{const e=await r({method:"GET",url:"/orders"});let s=[];e.data&&e.data.data?s=e.data.data:e.data&&(s=e.data),Array.isArray(s)||(s=[]),s.sort(((e,s)=>new Date(s.created_at)-new Date(e.created_at))),o(s),x(s.slice(0,3)),u(!1)}catch(e){j("Failed to load your data. Please try again later."),u(!1)}})()}),[]),h?e.jsx("div",{className:"flex justify-center items-center min-h-screen bg-gray-50",children:e.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500"})}):e.jsx("div",{className:"bg-gray-50 py-12",children:e.jsx("div",{className:"container mx-auto px-4",children:e.jsxs("div",{className:"max-w-5xl mx-auto",children:[e.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center mb-8",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold mb-2",children:"Dashboard"}),e.jsxs("p",{className:"text-gray-600",children:["Welcome back, ",null==n?void 0:n.name,"!"]})]}),e.jsx("div",{className:"mt-4 md:mt-0",children:e.jsx(l,{to:"/products",children:e.jsx(t,{children:"Browse Products"})})})]}),p&&e.jsx(d,{type:"error",message:p,onClose:()=>j(""),className:"mb-8"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:[e.jsx("div",{className:"bg-white rounded-lg shadow-md p-6",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"p-3 rounded-full bg-purple-100 text-purple-600 mr-4",children:e.jsx("svg",{className:"h-6 w-6",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"})})}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Total Orders"}),e.jsx("p",{className:"text-2xl font-bold",children:c.length})]})]})}),e.jsx("div",{className:"bg-white rounded-lg shadow-md p-6",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"p-3 rounded-full bg-green-100 text-green-600 mr-4",children:e.jsx("svg",{className:"h-6 w-6",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})})}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Account Type"}),e.jsx("p",{className:"text-2xl font-bold capitalize",children:(null==n?void 0:n.user_type)||"Free"})]})]})}),e.jsx("div",{className:"bg-white rounded-lg shadow-md p-6",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"p-3 rounded-full bg-blue-100 text-blue-600 mr-4",children:e.jsx("svg",{className:"h-6 w-6",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"})})}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Available Downloads"}),e.jsx("p",{className:"text-2xl font-bold",children:(null==(i=null==n?void 0:n.purchased_products)?void 0:i.length)||0})]})]})})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8",children:[e.jsx("div",{className:"md:col-span-2",children:e.jsxs("div",{className:"bg-white rounded-lg shadow-md overflow-hidden",children:[e.jsx("div",{className:"px-6 py-4 border-b border-gray-200",children:e.jsx("h2",{className:"text-xl font-bold",children:"Recent Orders"})}),e.jsxs("div",{className:"p-6",children:[m.length>0?e.jsx("div",{className:"divide-y divide-gray-200",children:m.filter((e=>e.product&&e.product.name)).map((s=>{var a,d;return e.jsxs("div",{className:"py-4",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium",children:(null==(a=s.product)?void 0:a.name)||"Product Unavailable"}),e.jsx("p",{className:"text-sm text-gray-500",children:new Date(s.created_at).toLocaleDateString()})]}),e.jsx("div",{children:e.jsx("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium "+("completed"===v(s)?"bg-green-100 text-green-800":"processing"===v(s)?"bg-yellow-100 text-yellow-800":"cancelled"===v(s)?"bg-red-100 text-red-800":"bg-gray-100 text-gray-800"),children:"completed"===v(s)?"Completed":"processing"===v(s)?"Processing":"cancelled"===v(s)?"Cancelled":v(s)})})]}),e.jsxs("div",{className:"mt-2 flex justify-between items-center",children:[e.jsxs("p",{className:"text-sm font-medium text-purple-600",children:["$",g(s).toFixed(2)]}),"completed"===v(s)&&(null==(d=s.product)?void 0:d._id)&&e.jsx(l,{to:`/downloads/${s.product._id}`,children:e.jsx(t,{variant:"outline",size:"sm",children:"Download"})})]})]},s._id)}))}):e.jsxs("div",{className:"text-center py-4",children:[e.jsx("p",{className:"text-gray-500",children:"You haven't placed any orders yet."}),e.jsx("div",{className:"mt-4",children:e.jsx(l,{to:"/products",children:e.jsx(t,{variant:"outline",children:"Browse Products"})})})]}),c.length>3&&e.jsx("div",{className:"mt-6 text-center",children:e.jsx(l,{to:"/orders",className:"text-purple-600 hover:text-purple-500 font-medium",children:"View All Orders"})})]})]})}),e.jsxs("div",{className:"md:col-span-1",children:[e.jsxs("div",{className:"bg-white rounded-lg shadow-md overflow-hidden mb-8",children:[e.jsx("div",{className:"px-6 py-4 border-b border-gray-200",children:e.jsx("h2",{className:"text-xl font-bold",children:"Account Details"})}),e.jsxs("div",{className:"p-6",children:[e.jsxs("div",{className:"mb-4",children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Name"}),e.jsx("p",{className:"font-medium",children:null==n?void 0:n.name})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Email"}),e.jsx("p",{className:"font-medium",children:null==n?void 0:n.email})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Member Since"}),e.jsx("p",{className:"font-medium",children:(null==n?void 0:n.created_at)?new Date(n.created_at).toLocaleDateString():(new Date).toLocaleDateString()})]}),e.jsx("div",{className:"mt-6",children:e.jsx(l,{to:"/profile",children:e.jsx(t,{variant:"outline",fullWidth:!0,children:"Edit Profile"})})})]})]}),e.jsx("div",{className:"bg-gradient-primary rounded-lg shadow-md overflow-hidden text-white",children:e.jsxs("div",{className:"p-6",children:[e.jsx("h2",{className:"text-xl font-bold mb-4",children:"Upgrade Your Account"}),e.jsx("p",{className:"mb-6",children:"Get access to premium resources and exclusive content."}),e.jsx(l,{to:"/products",children:e.jsx(t,{className:"bg-white text-purple-600 hover:bg-gray-100",fullWidth:!0,children:"View Premium Plans"})})]})})]})]})]})})})};export{i as default};
