import{j as e}from"./ui-CLS_rXuQ.js";import{r as s,R as t,f as a,u as r}from"./router-Bmo9-suO.js";import{I as l,B as n,A as i}from"./index-DQKqxzWd.js";import{E as d,a as c,u as o,c as m}from"./emailSequenceService-vhh7EWiT.js";import"./vendor-mk1rzcpA.js";const u=({email:a,onSave:r,onCancel:i})=>{const[d,c]=s.useState(a.variants||[]),[o,m]=s.useState(a.isABTest||!1),[u,x]=s.useState({subject:"",body:"",isActive:!0,weight:50});t.useEffect((()=>{o&&0===d.length&&c([{_id:"variant-a",subject:a.subject,body:a.body,isActive:!0,weight:50,stats:{sent:0,opened:0,clicked:0}}])}),[o,a,d.length]);const h=(e,s,t)=>{const a=[...d];a[e][s]=t,c(a)},b=(e,s)=>{x({...u,[e]:s})};return e.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6",children:[e.jsxs("div",{className:"mb-6",children:[e.jsx("h2",{className:"text-xl font-semibold mb-4",children:"A/B Testing"}),e.jsxs("div",{className:"flex items-center mb-4",children:[e.jsx("input",{id:"isABTest",type:"checkbox",checked:o,onChange:()=>{m(!o)},className:"h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"}),e.jsx("label",{htmlFor:"isABTest",className:"ml-2 block text-sm text-gray-700",children:"Enable A/B testing for this email"})]}),e.jsx("p",{className:"text-sm text-gray-500",children:"A/B testing allows you to test different versions of your email to see which performs better."})]}),o&&e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-medium mb-4",children:"Variants"}),d.map(((s,t)=>e.jsxs("div",{className:"border border-gray-200 rounded-lg p-4 mb-4",children:[e.jsxs("div",{className:"flex justify-between items-start mb-4",children:[e.jsxs("h4",{className:"text-md font-medium",children:["Variant ",String.fromCharCode(65+t)]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"mr-4",children:e.jsxs("label",{className:"inline-flex items-center",children:[e.jsx("input",{type:"checkbox",checked:s.isActive,onChange:e=>h(t,"isActive",e.target.checked),className:"h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"}),e.jsx("span",{className:"ml-2 text-sm text-gray-700",children:"Active"})]})}),e.jsx("button",{type:"button",onClick:()=>(e=>{if(d.length<=1)return void alert("You must have at least one variant for A/B testing");const s=[...d];s.splice(e,1),c(s)})(t),className:"text-red-600 hover:text-red-800",disabled:d.length<=1,children:e.jsx("svg",{className:"h-5 w-5",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})})})]})]}),e.jsxs("div",{className:"grid grid-cols-1 gap-4 mb-4",children:[e.jsx(l,{label:"Subject",value:s.subject,onChange:e=>h(t,"subject",e.target.value),required:!0}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Email Body"}),e.jsx("textarea",{rows:"5",value:s.body,onChange:e=>h(t,"body",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent",required:!0})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Weight (%)"}),e.jsx("input",{type:"number",min:"1",max:"100",value:s.weight,onChange:e=>h(t,"weight",parseInt(e.target.value,10)),className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"})]})]}),s.stats&&e.jsxs("div",{className:"bg-gray-50 p-3 rounded-lg",children:[e.jsx("h5",{className:"text-sm font-medium text-gray-700 mb-2",children:"Performance"}),e.jsxs("div",{className:"grid grid-cols-3 gap-4",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-xs text-gray-500",children:"Sent"}),e.jsx("p",{className:"text-sm font-medium",children:s.stats.sent})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-xs text-gray-500",children:"Open Rate"}),e.jsx("p",{className:"text-sm font-medium",children:s.stats.sent>0?`${(s.stats.opened/s.stats.sent*100).toFixed(1)}%`:"0%"})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-xs text-gray-500",children:"Click Rate"}),e.jsx("p",{className:"text-sm font-medium",children:s.stats.opened>0?`${(s.stats.clicked/s.stats.opened*100).toFixed(1)}%`:"0%"})]})]})]})]},s._id))),e.jsxs("div",{className:"border border-dashed border-gray-300 rounded-lg p-4 mb-6",children:[e.jsx("h4",{className:"text-md font-medium mb-4",children:"Add New Variant"}),e.jsxs("div",{className:"grid grid-cols-1 gap-4",children:[e.jsx(l,{label:"Subject",value:u.subject,onChange:e=>b("subject",e.target.value)}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Email Body"}),e.jsx("textarea",{rows:"5",value:u.body,onChange:e=>b("body",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx("input",{id:"newVariantIsActive",type:"checkbox",checked:u.isActive,onChange:e=>b("isActive",e.target.checked),className:"h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"}),e.jsx("label",{htmlFor:"newVariantIsActive",className:"ml-2 block text-sm text-gray-700",children:"Active"})]}),e.jsx(n,{type:"button",onClick:()=>{u.subject&&u.body?(c([...d,{_id:`variant-${Date.now()}`,...u,stats:{sent:0,opened:0,clicked:0}}]),x({subject:"",body:"",isActive:!0,weight:50})):alert("Subject and body are required for new variants")},children:"Add Variant"})]})]})]}),e.jsxs("div",{className:"flex justify-end space-x-4 mt-6",children:[e.jsx(n,{type:"button",variant:"outline",onClick:i,children:"Cancel"}),e.jsx(n,{type:"button",onClick:()=>{if(o){if(100!==d.reduce(((e,s)=>e+s.weight),0))return void alert("The total weight of all variants must equal 100%")}const e={...a,isABTest:o,variants:o?d:[]};r(e)},children:"Save Changes"})]})]})},x=()=>{const{id:t}=a(),x=r(),h=!!t,[b,p]=s.useState(new d),[g,j]=s.useState(h),[y,v]=s.useState(!1),[f,N]=s.useState(""),[w,k]=s.useState(""),[C,A]=s.useState("details"),[S,q]=s.useState(null),[E,B]=s.useState({subject:"",body:"",delayDays:0,isActive:!0});s.useEffect((()=>{(async()=>{if(h)try{const e=await c(t);p(e),j(!1)}catch(e){N("Failed to load email sequence. Please try again later."),j(!1)}})()}),[t,h]);const _=e=>{const{name:s,value:t,type:a,checked:r}=e.target;p((e=>{const l=new d(e.toJSON());return l[s]="checkbox"===a?r:t,l}))},D=e=>{const{name:s,value:t,type:a,checked:r}=e.target;B((e=>({...e,[s]:"checkbox"===a?r:t})))},T=(e,s,t)=>{p((a=>{const r=new d(a.toJSON()),l=r.emails.find((s=>s.id===e||s._id===e));return l&&(null===s&&"object"==typeof t?r.updateEmail(e,t):r.updateEmail(e,{...l,[s]:t})),r}))};return g?e.jsx("div",{className:"flex justify-center items-center min-h-screen bg-gray-50",children:e.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500"})}):e.jsx("div",{className:"bg-gray-50 py-12",children:e.jsx("div",{className:"container mx-auto px-4",children:e.jsxs("div",{className:"max-w-4xl mx-auto",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h1",{className:"text-3xl font-bold mb-2",children:h?"Edit Email Sequence":"Create Email Sequence"}),e.jsx("p",{className:"text-gray-600",children:h?"Update your automated email sequence":"Create a new automated email sequence"})]}),f&&e.jsx(i,{type:"error",message:f,onClose:()=>N(""),className:"mb-8"}),w&&e.jsx(i,{type:"success",message:w,onClose:()=>k(""),className:"mb-8"}),e.jsxs("div",{className:"bg-white rounded-lg shadow-md overflow-hidden",children:[e.jsx("div",{className:"border-b border-gray-200",children:e.jsxs("nav",{className:"flex -mb-px",children:[e.jsx("button",{className:"py-4 px-6 text-center border-b-2 font-medium text-sm "+("details"===C?"border-purple-500 text-purple-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"),onClick:()=>A("details"),children:"Sequence Details"}),e.jsxs("button",{className:"py-4 px-6 text-center border-b-2 font-medium text-sm "+("emails"===C?"border-purple-500 text-purple-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"),onClick:()=>A("emails"),children:["Emails (",b.emails.length,")"]}),S&&e.jsx("button",{className:"py-4 px-6 text-center border-b-2 font-medium text-sm "+("ab-testing"===C?"border-purple-500 text-purple-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"),onClick:()=>A("ab-testing"),children:"A/B Testing"})]})}),e.jsxs("form",{onSubmit:async e=>{e.preventDefault(),v(!0),N(""),k("");try{if(0===b.emails.length)throw new Error("At least one email is required in the sequence.");let e;e=h?await o(t,b):await m(b),k(`Email sequence ${h?"updated":"created"} successfully!`),setTimeout((()=>{x("/admin/email-sequences")}),1500)}catch(s){N(s.message||`Failed to ${h?"update":"create"} email sequence. Please try again later.`)}finally{v(!1)}},children:["details"===C&&e.jsx("div",{className:"p-6",children:e.jsxs("div",{className:"grid grid-cols-1 gap-6",children:[e.jsx(l,{label:"Sequence Name",id:"name",name:"name",value:b.name,onChange:_,required:!0}),e.jsxs("div",{children:[e.jsxs("label",{htmlFor:"description",className:"block text-sm font-medium text-gray-700 mb-1",children:["Description ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsx("textarea",{id:"description",name:"description",rows:"3",value:b.description,onChange:_,className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent",required:!0})]}),e.jsxs("div",{children:[e.jsxs("label",{htmlFor:"trigger",className:"block text-sm font-medium text-gray-700 mb-1",children:["Trigger ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsxs("select",{id:"trigger",name:"trigger",value:b.trigger,onChange:_,className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent",required:!0,children:[e.jsx("option",{value:"lead_capture",children:"Lead Capture"}),e.jsx("option",{value:"purchase",children:"Purchase"}),e.jsx("option",{value:"user_inactive",children:"User Inactivity"}),e.jsx("option",{value:"cart_abandoned",children:"Abandoned Cart"}),e.jsx("option",{value:"course_completed",children:"Course Completion"})]})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx("input",{id:"isActive",name:"isActive",type:"checkbox",checked:b.isActive,onChange:_,className:"h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"}),e.jsx("label",{htmlFor:"isActive",className:"ml-2 block text-sm text-gray-700",children:"Active (sequence will be sent to users)"})]})]})}),"ab-testing"===C&&S&&e.jsxs("div",{className:"p-6",children:[e.jsxs("div",{className:"flex justify-between items-center mb-6",children:[e.jsxs("h2",{className:"text-lg font-semibold",children:["A/B Testing for Email #",S.order]}),e.jsx(n,{variant:"outline",onClick:()=>{A("emails"),q(null)},children:"Back to Emails"})]}),e.jsx(u,{email:S,onSave:e=>{T(e._id||e.id,null,e),A("emails"),q(null)},onCancel:()=>{A("emails"),q(null)}})]}),"emails"===C&&e.jsxs("div",{className:"p-6",children:[e.jsx("h2",{className:"text-lg font-semibold mb-4",children:"Emails in Sequence"}),0===b.emails.length?e.jsx("div",{className:"bg-gray-50 p-6 text-center rounded-lg mb-6",children:e.jsx("p",{className:"text-gray-500",children:"No emails in this sequence yet. Add your first email below."})}):e.jsx("div",{className:"space-y-6 mb-8",children:b.emails.map(((s,t)=>e.jsxs("div",{className:"border border-gray-200 rounded-lg p-4",children:[e.jsxs("div",{className:"flex justify-between items-start mb-4",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsxs("span",{className:"bg-purple-100 text-purple-800 text-xs font-semibold px-2.5 py-0.5 rounded-full mr-2",children:["Day ",s.delayDays]}),e.jsx("h3",{className:"text-lg font-medium",children:s.subject}),s.isABTest&&e.jsx("span",{className:"ml-2 bg-blue-100 text-blue-800 text-xs font-semibold px-2.5 py-0.5 rounded-full",children:"A/B Test"})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"mr-4",children:e.jsxs("label",{className:"inline-flex items-center",children:[e.jsx("input",{type:"checkbox",checked:s.isActive,onChange:e=>T(s._id||s.id,"isActive",e.target.checked),className:"h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"}),e.jsx("span",{className:"ml-2 text-sm text-gray-700",children:"Active"})]})}),e.jsx("button",{type:"button",className:"text-blue-600 hover:text-blue-800 mr-2",onClick:()=>{q(s),A("ab-testing")},children:e.jsx("svg",{className:"h-5 w-5",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"})})}),e.jsx("button",{type:"button",onClick:()=>{return e=s._id||s.id,void p((s=>{const t=new d(s.toJSON());return t.removeEmail(e),t}));var e},className:"text-red-600 hover:text-red-800",children:e.jsx("svg",{className:"h-5 w-5",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})})})]})]}),e.jsx("div",{className:"mb-4",children:e.jsx("div",{className:"bg-gray-50 p-3 rounded-lg text-sm text-gray-700",dangerouslySetInnerHTML:{__html:s.body}})}),e.jsxs("div",{className:"flex items-center justify-end",children:[e.jsx("label",{className:"text-sm text-gray-700 mr-2",children:"Send after"}),e.jsx("input",{type:"number",min:"0",value:s.delayDays,onChange:e=>T(s._id||s.id,"delayDays",parseInt(e.target.value,10)),className:"w-16 px-2 py-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"}),e.jsx("span",{className:"ml-2 text-sm text-gray-700",children:"days"})]})]},s._id||s.id)))}),e.jsxs("div",{className:"border-t border-gray-200 pt-6",children:[e.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Add New Email"}),e.jsxs("div",{className:"grid grid-cols-1 gap-4",children:[e.jsx(l,{label:"Email Subject",id:"subject",name:"subject",value:E.subject,onChange:D}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"body",className:"block text-sm font-medium text-gray-700 mb-1",children:"Email Body"}),e.jsx("textarea",{id:"body",name:"body",rows:"5",value:E.body,onChange:D,className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent",placeholder:"HTML content is supported"})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{htmlFor:"delayDays",className:"block text-sm font-medium text-gray-700 mb-1",children:"Send After (days)"}),e.jsx("input",{type:"number",id:"delayDays",name:"delayDays",min:"0",value:E.delayDays,onChange:D,className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"})]}),e.jsxs("div",{className:"flex items-center mt-6",children:[e.jsx("input",{id:"newEmailIsActive",name:"isActive",type:"checkbox",checked:E.isActive,onChange:D,className:"h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"}),e.jsx("label",{htmlFor:"newEmailIsActive",className:"ml-2 block text-sm text-gray-700",children:"Active"})]})]}),e.jsx("div",{children:e.jsx(n,{type:"button",onClick:()=>{E.subject&&E.body?(p((e=>{const s=new d(e.toJSON());return s.addEmail(E),s})),B({subject:"",body:"",delayDays:0,isActive:!0})):N("Email subject and body are required.")},className:"w-full",children:"Add Email to Sequence"})})]})]})]}),e.jsxs("div",{className:"bg-gray-50 px-6 py-4 flex justify-end space-x-4",children:[e.jsx(n,{type:"button",variant:"outline",onClick:()=>x("/admin/email-sequences"),children:"Cancel"}),e.jsx(n,{type:"submit",isLoading:y,children:h?"Update Sequence":"Create Sequence"})]})]})]})]})})})};export{x as default};
