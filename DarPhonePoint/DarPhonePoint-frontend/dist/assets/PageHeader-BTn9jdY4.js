import{j as e}from"./ui-CLS_rXuQ.js";import{L as s}from"./router-Bmo9-suO.js";import{z as l,y as t}from"./index-DQKqxzWd.js";const a=({items:a=[],showHome:m=!0})=>e.jsx("nav",{className:"flex","aria-label":"Breadcrumb",children:e.jsxs("ol",{className:"inline-flex items-center space-x-1 md:space-x-3",children:[m&&e.jsx("li",{className:"inline-flex items-center",children:e.jsxs(s,{to:"/admin",className:"inline-flex items-center text-sm font-medium text-gray-700 hover:text-purple-600",children:[e.jsx(l,{className:"mr-2 w-4 h-4"}),"Home"]})}),a.map(((l,m)=>e.jsxs("li",{className:"inline-flex items-center",children:[e.jsx(t,{className:"mx-2 w-3 h-3 text-gray-400"}),m===a.length-1?e.jsx("span",{className:"text-sm font-medium text-gray-500",children:l.label}):e.jsx(s,{to:l.path,className:"text-sm font-medium text-gray-700 hover:text-purple-600",children:l.label})]},m)))]})}),m=({title:s,description:l,breadcrumbs:t=[],actions:m})=>e.jsxs("div",{className:"mb-8",children:[t.length>0&&e.jsx("div",{className:"mb-4",children:e.jsx(a,{items:t})}),e.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold mb-2",children:s}),l&&e.jsx("p",{className:"text-gray-600",children:l})]}),m&&e.jsx("div",{className:"mt-4 md:mt-0 flex flex-wrap gap-2",children:m})]})]});export{m as P};
