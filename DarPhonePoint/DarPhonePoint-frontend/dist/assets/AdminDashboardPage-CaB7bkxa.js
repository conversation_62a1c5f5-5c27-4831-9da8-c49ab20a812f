import{j as e}from"./ui-CLS_rXuQ.js";import{r as s,L as t}from"./router-Bmo9-suO.js";import{s as a,u as r,B as d,A as l}from"./index-DQKqxzWd.js";import{P as n}from"./PageHeader-BTn9jdY4.js";import"./vendor-mk1rzcpA.js";const i=()=>{const{user:i}=r(),[c,o]=s.useState({totalUsers:0,totalOrders:0,totalRevenue:0,totalLeads:0,recentOrders:[],recentLeads:[]}),[m,x]=s.useState(!0),[h,u]=s.useState("");return s.useEffect((()=>{(async()=>{try{if(!localStorage.getItem("token"))return u("You must be logged in to view this page."),void x(!1);const e=await(async()=>{var e,s,t,r;try{let l={data:[]};try{const s=await a({method:"GET",url:"/users"});l=(null==(e=s.data)?void 0:e.data)||s.data||s}catch(d){}const n=Date.now(),i=await a({method:"GET",url:`/orders/admin/all?_t=${n}`}),c=(null==(s=i.data)?void 0:s.data)||i.data||i;let o={data:[]};try{const e=await a({method:"GET",url:"/leads"});o=(null==(t=e.data)?void 0:t.data)||e.data||e}catch(d){}const m=Array.isArray(c)?c:[],x=m.reduce(((e,s)=>"completed"===s.payment_status?e+(s.amount||0):e),0),h=m.sort(((e,s)=>new Date(s.created_at)-new Date(e.created_at))).slice(0,3).map((e=>{var s;return{_id:e._id,customer:(null==(s=e.user)?void 0:s.name)||"Guest",product:e.product||{name:"Unknown Product"},amount:e.amount||0,created_at:e.created_at,payment_status:e.payment_status||"pending"}})),u=Array.isArray(o)?o:o.data||[],p=u.sort(((e,s)=>new Date(s.created_at)-new Date(e.created_at))).slice(0,3).map((e=>({_id:e._id,name:e.name,email:e.email,source:e.source||"unknown",created_at:e.created_at})));return{totalUsers:Array.isArray(l)?l.length:(null==(r=l.data)?void 0:r.length)||0,totalOrders:m.length,totalRevenue:x,totalLeads:u.length,recentOrders:h,recentLeads:p}}catch(h){throw h}})();if(!e)return o({totalUsers:0,totalOrders:0,totalRevenue:0,totalLeads:0,recentOrders:[],recentLeads:[]}),void x(!1);o(e),x(!1)}catch(e){u("Failed to load admin data. Please try again later."),x(!1),o({totalUsers:0,totalOrders:0,totalRevenue:0,totalLeads:0,recentOrders:[],recentLeads:[]})}})()}),[]),m?e.jsx("div",{className:"flex justify-center items-center min-h-screen bg-gray-50",children:e.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500"})}):e.jsx("div",{className:"bg-gray-50 py-12",children:e.jsx("div",{className:"container mx-auto px-4",children:e.jsxs("div",{className:"max-w-7xl mx-auto",children:[e.jsx(n,{title:"Admin Dashboard",description:`Welcome, ${null==i?void 0:i.name}!`,breadcrumbs:[],actions:e.jsx(t,{to:"/admin/analytics",children:e.jsx(d,{children:"View Analytics"})})}),h&&e.jsx(l,{type:"error",message:h,onClose:()=>u(""),className:"mb-8"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8",children:[e.jsx("div",{className:"bg-white rounded-lg shadow-md p-6",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"p-3 rounded-full bg-purple-100 text-purple-600 mr-4",children:e.jsx("svg",{className:"h-6 w-6",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})})}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Total Users"}),e.jsx("p",{className:"text-2xl font-bold",children:c.totalUsers})]})]})}),e.jsx("div",{className:"bg-white rounded-lg shadow-md p-6",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"p-3 rounded-full bg-blue-100 text-blue-600 mr-4",children:e.jsx("svg",{className:"h-6 w-6",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"})})}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Total Orders"}),e.jsx("p",{className:"text-2xl font-bold",children:c.totalOrders})]})]})}),e.jsx("div",{className:"bg-white rounded-lg shadow-md p-6",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"p-3 rounded-full bg-green-100 text-green-600 mr-4",children:e.jsx("svg",{className:"h-6 w-6",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Total Revenue"}),e.jsxs("p",{className:"text-2xl font-bold",children:["$",c.totalRevenue.toFixed(2)]})]})]})}),e.jsx("div",{className:"bg-white rounded-lg shadow-md p-6",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"p-3 rounded-full bg-yellow-100 text-yellow-600 mr-4",children:e.jsx("svg",{className:"h-6 w-6",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"})})}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Total Leads"}),e.jsx("p",{className:"text-2xl font-bold",children:c.totalLeads})]})]})})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8",children:[e.jsxs("div",{className:"bg-white rounded-lg shadow-md overflow-hidden",children:[e.jsxs("div",{className:"px-6 py-4 border-b border-gray-200 flex justify-between items-center",children:[e.jsx("h2",{className:"text-xl font-bold",children:"Recent Orders"}),e.jsx(t,{to:"/admin/orders",className:"text-sm text-purple-600 hover:text-purple-800",children:"View All"})]}),e.jsx("div",{className:"p-6",children:c.recentOrders.length>0?e.jsx("div",{className:"divide-y divide-gray-200",children:c.recentOrders.map((s=>e.jsx("div",{className:"py-4",children:e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium",children:s.product.name}),e.jsxs("p",{className:"text-sm text-gray-500",children:[s.customer," • ",new Date(s.created_at).toLocaleDateString()]})]}),e.jsx("div",{children:e.jsxs("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium "+("completed"===s.payment_status?"bg-green-100 text-green-800":"bg-yellow-100 text-yellow-800"),children:["$",s.amount.toFixed(2)]})})]})},s._id)))}):e.jsx("p",{className:"text-gray-500 text-center py-4",children:"No recent orders"})})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow-md overflow-hidden",children:[e.jsxs("div",{className:"px-6 py-4 border-b border-gray-200 flex justify-between items-center",children:[e.jsx("h2",{className:"text-xl font-bold",children:"Recent Leads"}),e.jsx(t,{to:"/admin/leads",className:"text-sm text-purple-600 hover:text-purple-800",children:"View All"})]}),e.jsx("div",{className:"p-6",children:c.recentLeads.length>0?e.jsx("div",{className:"divide-y divide-gray-200",children:c.recentLeads.map((s=>e.jsx("div",{className:"py-4",children:e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium",children:s.name}),e.jsxs("p",{className:"text-sm text-gray-500",children:[s.email," • ",new Date(s.created_at).toLocaleDateString()]})]}),e.jsx("div",{children:e.jsx("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800",children:s.source})})]})},s._id)))}):e.jsx("p",{className:"text-gray-500 text-center py-4",children:"No recent leads"})})]})]})]})})})};export{i as default};
