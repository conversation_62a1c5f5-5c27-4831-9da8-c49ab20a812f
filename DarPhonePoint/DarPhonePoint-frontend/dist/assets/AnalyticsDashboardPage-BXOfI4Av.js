import{j as e,P as t}from"./ui-CLS_rXuQ.js";import{r as s}from"./router-Bmo9-suO.js";import{F as r,g as l,h as a,i,L as n,A as o}from"./index-DQKqxzWd.js";import{P as d}from"./PageHeader-BTn9jdY4.js";import{E as c}from"./ErrorBoundary-jAhIDQkO.js";import{C as x,r as h}from"./chart-BnP3NyOz.js";import{f as m}from"./analyticsService-DGKX5lwb.js";import{a as u}from"./analytics-cUfjo-76.js";import"./vendor-mk1rzcpA.js";const p=({data:t,labels:s,title:r,color:l="#6366F1",height:a=200,showGrid:i=!1,showArea:n=!0,showPoints:o=!0})=>{if(!t||0===t.length)return e.jsx("div",{className:"flex flex-col h-full justify-center items-center bg-gray-50 rounded-lg p-4",children:e.jsx("p",{className:"text-gray-500 text-sm",children:"No data available"})});const d=Math.max(...t),c=d>0?1.1*d:10,x=1.1*Math.min(0,...t),h=100,m=a,u=t.map(((e,s)=>`${s/(t.length-1)*h},${m-(e-x)/(c-x)*m}`)).join(" "),p=n?`\n    M 0,${m}\n    L ${u}\n    L 100,${m}\n    Z\n  `:"",g=i?e.jsxs(e.Fragment,{children:[[.25,.5,.75].map(((t,s)=>e.jsx("line",{x1:"0",y1:m*t,x2:h,y2:m*t,stroke:"#E5E7EB",strokeWidth:"0.5",strokeDasharray:"2,2"},s))),s.map(((t,r)=>{if(0===r||r===s.length-1)return null;const l=r/(s.length-1)*h;return e.jsx("line",{x1:l,y1:"0",x2:l,y2:m,stroke:"#E5E7EB",strokeWidth:"0.5",strokeDasharray:"2,2"},r)}))]}):null,y=o?t.map(((s,r)=>{const a=r/(t.length-1)*h,i=m-(s-x)/(c-x)*m;return e.jsx("circle",{cx:a,cy:i,r:"1.5",fill:"white",stroke:l,strokeWidth:"1.5"},r)})):null,j=[];for(let e=0;e<5;e++){const t=Math.round(c*(1-e/4));j.push(t)}return e.jsxs("div",{className:"h-full",children:[r&&e.jsx("h3",{className:"text-sm font-medium text-gray-500 mb-1",children:r}),e.jsxs("div",{className:"flex",children:[e.jsx("div",{className:"flex flex-col justify-between text-xs text-gray-400 pr-2",children:j.map(((t,s)=>e.jsx("span",{children:t},s)))}),e.jsxs("div",{className:"flex-1",children:[e.jsx("div",{className:"relative",style:{height:`${a}px`},children:e.jsxs("svg",{viewBox:`0 0 100 ${m}`,className:"w-full h-full",children:[g,j.map(((t,s)=>{const r=s/4*m;return e.jsx("line",{x1:"0",y1:r,x2:h,y2:r,stroke:"#E5E7EB",strokeWidth:"0.5",strokeDasharray:"2,2"},`y-grid-${s}`)})),n&&e.jsx("path",{d:p,fill:l,fillOpacity:"0.1"}),e.jsx("polyline",{points:u,fill:"none",stroke:l,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),y]})}),s&&s.length>0&&e.jsx("div",{className:"flex justify-between text-xs text-gray-400 mt-1",children:s.map(((t,s)=>e.jsx("span",{children:t},s)))})]})]})]})};p.propTypes={data:t.arrayOf(t.number).isRequired,labels:t.arrayOf(t.string).isRequired,title:t.string,color:t.string,height:t.number,showGrid:t.bool,showArea:t.bool,showPoints:t.bool},x.register(...h);const g=({data:t,labels:r,title:l,colors:a=["#8B5CF6","#3B82F6","#10B981","#F59E0B","#EF4444"],height:i=300,horizontal:n=!1,stacked:o=!1,showGrid:d=!0,options:c={}})=>{const h=s.useRef(null),m=s.useRef(null);return s.useEffect((()=>{var e;if(m.current&&m.current.destroy(),!t||!r||0===t.length||0===r.length)return;const s=h.current.getContext("2d");let i=[];return i=Array.isArray(t[0])?t.map(((e,t)=>{var s;return{label:(null==(s=c.datasetLabels)?void 0:s[t])||`Dataset ${t+1}`,data:e,backgroundColor:a[t%a.length],borderColor:a[t%a.length],borderWidth:1}})):[{label:(null==(e=c.datasetLabels)?void 0:e[0])||l,data:t,backgroundColor:Array.isArray(a)?a:[a],borderColor:Array.isArray(a)?a:[a],borderWidth:1}],m.current=new x(s,{type:n?"horizontalBar":"bar",data:{labels:r,datasets:i},options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{display:i.length>1||c.showLegend,position:"top"},title:{display:!!l,text:l},tooltip:{mode:"index",intersect:!1}},scales:{x:{stacked:o,grid:{display:d},title:{display:!!c.xAxisTitle,text:c.xAxisTitle}},y:{stacked:o,grid:{display:d},title:{display:!!c.yAxisTitle,text:c.yAxisTitle},beginAtZero:!0}},...c}}),()=>{m.current&&m.current.destroy()}}),[t,r,l,a,i,n,o,d,c]),e.jsx("div",{style:{height:`${i}px`,width:"100%"},children:e.jsx("canvas",{ref:h})})},y=({data:t,labels:r,title:l,colors:a=["rgba(139, 92, 246, 0.5)","rgba(59, 130, 246, 0.5)","rgba(16, 185, 129, 0.5)"],height:i=300,stacked:n=!1,showGrid:o=!0,options:d={}})=>{const c=s.useRef(null),h=s.useRef(null);return s.useEffect((()=>{var e;if(h.current&&h.current.destroy(),!t||!r||0===t.length||0===r.length)return;const s=c.current.getContext("2d");let i=[];if(Array.isArray(t[0]))i=t.map(((e,t)=>{var s;const r=a[t%a.length],l=r.replace("0.5","1");return{label:(null==(s=d.datasetLabels)?void 0:s[t])||`Dataset ${t+1}`,data:e,backgroundColor:r,borderColor:l,borderWidth:1,fill:!0,tension:.4}}));else{const s=a[0],r=s.replace("0.5","1");i=[{label:(null==(e=d.datasetLabels)?void 0:e[0])||l,data:t,backgroundColor:s,borderColor:r,borderWidth:1,fill:!0,tension:.4}]}return h.current=new x(s,{type:"line",data:{labels:r,datasets:i},options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{display:i.length>1||d.showLegend,position:"top"},title:{display:!!l,text:l},tooltip:{mode:"index",intersect:!1}},scales:{x:{grid:{display:o},title:{display:!!d.xAxisTitle,text:d.xAxisTitle}},y:{stacked:n,grid:{display:o},title:{display:!!d.yAxisTitle,text:d.yAxisTitle},beginAtZero:!0}},elements:{line:{tension:.4}},...d}}),()=>{h.current&&h.current.destroy()}}),[t,r,l,a,i,n,o,d]),e.jsx("div",{style:{height:`${i}px`,width:"100%"},children:e.jsx("canvas",{ref:c})})},j=({data:t,labels:s,colors:r=["#8B5CF6","#3B82F6","#10B981","#F59E0B","#EF4444"],title:l="",showPercentage:a=!0,showLegend:i=!0,size:n=200,innerRadius:o=0})=>{if(!t||0===t.length)return e.jsx("div",{className:"flex flex-col h-full justify-center items-center bg-gray-50 rounded-lg p-4",children:e.jsx("p",{className:"text-gray-500 text-sm",children:"No data available"})});const d=t.reduce(((e,t)=>e+t),0);let c=0;const x=t.map((e=>Math.round(e/d*100)));return e.jsxs("div",{className:"h-full",children:[l&&e.jsx("h3",{className:"text-sm font-medium text-gray-500 mb-2",children:l}),e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"relative",style:{width:`${n}px`,height:`${n}px`},children:e.jsxs("svg",{viewBox:"0 0 100 100",className:"w-full h-full",children:[t.map(((t,l)=>{const a=t/d,i=c+360*a,n=50,h=c*Math.PI/180,m=i*Math.PI/180,u=50+n*Math.cos(h),p=50+n*Math.sin(h),g=50+n*Math.cos(m),y=50+n*Math.sin(m),j=a>.5?1:0;let b;if(o>0){const e=o/2;b=[`M ${u} ${p}`,`A 50 50 0 ${j} 1 ${g} ${y}`,`L ${50+e*Math.cos(m)} ${50+e*Math.sin(m)}`,`A ${e} ${e} 0 ${j} 0 ${50+e*Math.cos(h)} ${50+e*Math.sin(h)}`,"Z"].join(" ")}else b=["M 50 50",`L ${u} ${p}`,`A 50 50 0 ${j} 1 ${g} ${y}`,"Z"].join(" ");return c=i,e.jsx("path",{d:b,fill:r[l%r.length],stroke:"#fff",strokeWidth:"1",className:"transition-opacity duration-200 hover:opacity-80",children:e.jsxs("title",{children:[s[l],": ",x[l],"%"]})},l)})),a&&t.length<=5&&t.map(((t,s)=>{if(x[s]<5)return null;const r=t/d,l=c+360*r/2;c+=360*r;const a=o>0?50+o/4:30,i=50+a*Math.cos(l*Math.PI/180),n=50+a*Math.sin(l*Math.PI/180);return e.jsxs("text",{x:i,y:n,textAnchor:"middle",dominantBaseline:"middle",fill:"#fff",fontSize:"10",fontWeight:"bold",children:[x[s],"%"]},`text-${s}`)}))]})}),i&&e.jsx("div",{className:"ml-4 flex-1",children:s.map(((t,s)=>e.jsxs("div",{className:"flex items-center mb-1",children:[e.jsx("div",{className:"w-3 h-3 rounded-full mr-2",style:{backgroundColor:r[s%r.length]}}),e.jsxs("span",{className:"text-xs text-gray-600",children:[t,": ",x[s],"%"]})]},s)))})]})]})};j.propTypes={data:t.arrayOf(t.number).isRequired,labels:t.arrayOf(t.string).isRequired,colors:t.arrayOf(t.string),title:t.string,showPercentage:t.bool,showLegend:t.bool,size:t.number,innerRadius:t.number};const b=({data:t,xLabels:r,yLabels:l,title:a,colorScale:i="purple",height:n=400,options:o={}})=>{const d=s.useRef(null),c={purple:["#F8FAFC","#F1F5F9","#E2E8F0","#CBD5E1","#94A3B8","#64748B","#475569","#334155","#1E293B"],blue:["#F0F9FF","#E0F2FE","#BAE6FD","#7DD3FC","#38BDF8","#0EA5E9","#0284C7","#0369A1","#075985"],green:["#F0FDF4","#DCFCE7","#BBF7D0","#86EFAC","#4ADE80","#22C55E","#16A34A","#15803D","#166534"],red:["#FEF2F2","#FEE2E2","#FECACA","#FCA5A5","#F87171","#EF4444","#DC2626","#B91C1C","#991B1B"]},x=(e,t,s)=>{const r=c[i]||c.purple;if(0===e)return r[0];const l=(e-t)/(s-t);return r[Math.min(Math.floor(l*(r.length-1))+1,r.length-1)]},{processedData:h,minValue:m,maxValue:u}=(()=>{if(!t||!r||!l||0===t.length)return{processedData:[],minValue:0,maxValue:0};let e=Number.MAX_VALUE,s=Number.MIN_VALUE;t.forEach((t=>{t.forEach((t=>{t<e&&(e=t),t>s&&(s=t)}))})),0===s&&(s=1);return{processedData:t.map(((e,t)=>e.map(((e,s)=>({x:s,y:t,value:e,day:r[s],hour:l[t]}))))),minValue:e,maxValue:s}})(),p=40,g=40,y=60,j=80,b=j+g+40*((null==r?void 0:r.length)||7),f=n;return e.jsx("div",{ref:d,style:{width:"100%",height:`${n}px`},children:t&&0!==t.length?e.jsxs("svg",{width:"100%",height:"100%",viewBox:`0 0 ${b} ${f}`,children:[a&&e.jsx("text",{x:b/2,y:20,textAnchor:"middle",className:"text-sm font-medium fill-gray-700",children:a}),null==l?void 0:l.map(((t,s)=>e.jsx("text",{x:j-10,y:p+16*s+8,textAnchor:"end",dominantBaseline:"middle",className:"text-xs fill-gray-600",children:t},`hour-${s}`))),null==r?void 0:r.map(((t,s)=>e.jsx("text",{x:j+40*s+20,y:f-y+20,textAnchor:"middle",dominantBaseline:"middle",className:"text-xs fill-gray-600",children:t},`day-${s}`))),h.map(((t,s)=>t.map(((t,r)=>e.jsx("g",{children:e.jsx("rect",{x:j+40*r,y:p+16*s,width:39,height:15,fill:x(t.value,m,u),stroke:"#fff",strokeWidth:"1",className:"hover:stroke-gray-400 hover:stroke-2 cursor-pointer",children:e.jsx("title",{children:`${t.hour} on ${t.day}: ${t.value} events`})})},`cell-${s}-${r}`))))),o.xAxisTitle&&e.jsx("text",{x:b/2,y:f-10,textAnchor:"middle",className:"text-sm fill-gray-600",children:o.xAxisTitle}),o.yAxisTitle&&e.jsx("text",{x:15,y:f/2,textAnchor:"middle",transform:`rotate(-90, 15, ${f/2})`,className:"text-sm fill-gray-600",children:o.yAxisTitle}),e.jsxs("g",{transform:`translate(${b-g-100}, ${p})`,children:[e.jsx("text",{x:"0",y:"-5",className:"text-xs fill-gray-600",children:"Activity Level"}),c[i].slice(0,5).map(((t,s)=>e.jsxs("g",{transform:`translate(${15*s}, 0)`,children:[e.jsx("rect",{width:"12",height:"12",fill:t,stroke:"#fff",strokeWidth:"1"}),0===s&&e.jsx("text",{x:"0",y:"25",className:"text-xs fill-gray-500",children:"Low"}),4===s&&e.jsx("text",{x:"-5",y:"25",className:"text-xs fill-gray-500",children:"High"})]},`legend-${s}`)))]})]}):e.jsx("div",{className:"flex items-center justify-center h-full text-gray-500",children:"No data available"})})},f=({children:t,title:n,description:o,actions:d=[],allowDownload:c=!0,allowFullscreen:x=!0,className:h=""})=>{const[m,u]=s.useState(!1),[p,g]=s.useState(!1);return e.jsxs("div",{id:`chart-${n.replace(/\s+/g,"-").toLowerCase()}`,className:`bg-white rounded-lg shadow-md overflow-hidden ${m?"fixed inset-0 z-50 flex flex-col":h}`,children:[e.jsxs("div",{className:"px-6 py-4 border-b border-gray-200 flex justify-between items-center",children:[e.jsxs("div",{children:[e.jsx("h2",{className:"text-lg font-semibold text-gray-900",children:n}),o&&e.jsx("p",{className:"text-sm text-gray-500 mt-1",children:o})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[c&&e.jsx("button",{onClick:()=>{const e=document.querySelector(`#chart-${n.replace(/\s+/g,"-").toLowerCase()} canvas`);if(!e)return;const t=document.createElement("a");t.download=`${n.replace(/\s+/g,"-").toLowerCase()}-${(new Date).toISOString().split("T")[0]}.png`,t.href=e.toDataURL("image/png"),document.body.appendChild(t),t.click(),document.body.removeChild(t)},className:"p-1 text-gray-500 hover:text-gray-700 rounded-full hover:bg-gray-100",title:"Download chart",children:e.jsx(r,{className:"w-4 h-4"})}),x&&e.jsx("button",{onClick:()=>{u(!m)},className:"p-1 text-gray-500 hover:text-gray-700 rounded-full hover:bg-gray-100",title:m?"Exit fullscreen":"View fullscreen",children:m?e.jsx(l,{className:"w-4 h-4"}):e.jsx(a,{className:"w-4 h-4"})}),d.length>0&&e.jsxs("div",{className:"relative",children:[e.jsx("button",{onClick:()=>{g(!p)},className:"p-1 text-gray-500 hover:text-gray-700 rounded-full hover:bg-gray-100",title:"More options",children:e.jsx(i,{className:"w-4 h-4"})}),p&&e.jsx("div",{className:"absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10",children:e.jsx("div",{className:"py-1",children:d.map(((t,s)=>e.jsxs("button",{onClick:()=>{t.onClick(),g(!1)},className:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:[t.icon&&e.jsx("span",{className:"mr-2",children:t.icon}),t.label]},s)))})})]})]})]}),e.jsx("div",{className:"p-6 "+(m?"flex-grow":""),children:t})]})},v=({title:t,value:s,icon:r,bgColor:l="bg-blue-100",textColor:a="text-blue-600",trend:i=null,trendValue:n=0,trendLabel:o="vs. previous period",className:d=""})=>{let c="",x=null;return"up"===i?(c="text-green-600",x=e.jsx("svg",{className:"h-3 w-3",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 10l7-7m0 0l7 7m-7-7v18"})})):"down"===i&&(c="text-red-600",x=e.jsx("svg",{className:"h-3 w-3",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 14l-7 7m0 0l-7-7m7 7V3"})})),e.jsx("div",{className:`bg-white rounded-lg shadow-md p-6 ${d}`,children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:`p-3 rounded-full ${l} ${a} mr-4`,children:r}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:t}),e.jsx("p",{className:"text-2xl font-bold",children:s}),i&&e.jsxs("div",{className:`flex items-center mt-1 text-xs ${c}`,children:[x,e.jsxs("span",{className:"ml-1 font-medium",children:[n,"%"]}),e.jsx("span",{className:"ml-1 text-gray-500",children:o})]})]})]})})};v.propTypes={title:t.string.isRequired,value:t.oneOfType([t.string,t.number]).isRequired,icon:t.node.isRequired,bgColor:t.string,textColor:t.string,trend:t.oneOf(["up","down",null]),trendValue:t.number,trendLabel:t.string,className:t.string};const w=()=>e.jsx("svg",{className:"h-6 w-6",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})}),N=()=>e.jsx("svg",{className:"h-6 w-6",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"})}),k=()=>e.jsx("svg",{className:"h-6 w-6",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})}),A=()=>e.jsx("svg",{className:"h-6 w-6",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"})}),C=()=>e.jsx("svg",{className:"h-6 w-6",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"})}),$=()=>{var t,r;const[l,a]=s.useState("30d"),[i,x]=s.useState(null),[h,$]=s.useState(null),[M,L]=s.useState(!0),[F,B]=s.useState(!0),[E,D]=s.useState("");return s.useEffect((()=>{(async()=>{L(!0);try{const e=await m(l);x(e),L(!1)}catch(e){D("Failed to load analytics data. Please try again later."),L(!1)}})()}),[l]),s.useEffect((()=>{(async()=>{B(!0);try{const e="7d"===l?7:"90d"===l?90:30,t=await u.getWeeklyActivityHeatmap(e);$(t),B(!1)}catch(e){B(!1)}})()}),[l]),M?e.jsx(n,{fullPage:!0,text:"Loading analytics data..."}):e.jsx("div",{className:"bg-gray-50 py-12",children:e.jsx("div",{className:"container mx-auto px-4",children:e.jsxs("div",{className:"max-w-7xl mx-auto",children:[e.jsx(d,{title:"Analytics Dashboard",description:"Track your business performance",breadcrumbs:[{label:"Analytics",path:"/admin/analytics"}],actions:e.jsxs("div",{className:"inline-flex rounded-md shadow-sm",children:[e.jsx("button",{type:"button",className:`px-4 py-2 text-sm font-medium rounded-l-md ${"7d"===l?"bg-purple-600 text-white":"bg-white text-gray-700 hover:bg-gray-50"} border border-gray-300`,onClick:()=>a("7d"),children:"7 Days"}),e.jsx("button",{type:"button",className:`px-4 py-2 text-sm font-medium ${"30d"===l?"bg-purple-600 text-white":"bg-white text-gray-700 hover:bg-gray-50"} border-t border-b border-gray-300`,onClick:()=>a("30d"),children:"30 Days"}),e.jsx("button",{type:"button",className:`px-4 py-2 text-sm font-medium rounded-r-md ${"90d"===l?"bg-purple-600 text-white":"bg-white text-gray-700 hover:bg-gray-50"} border border-gray-300`,onClick:()=>a("90d"),children:"90 Days"})]})}),E&&e.jsx(o,{type:"error",message:E,onClose:()=>D(""),className:"mb-8"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8",children:[e.jsx(v,{title:"Revenue",value:`$${i.summary.totalRevenue.toFixed(2)}`,icon:e.jsx(w,{}),bgColor:"bg-green-100",textColor:"text-green-600"}),e.jsx(v,{title:"Orders",value:i.summary.totalOrders,icon:e.jsx(N,{}),bgColor:"bg-blue-100",textColor:"text-blue-600"}),e.jsx(v,{title:"Users",value:i.summary.totalUsers,icon:e.jsx(k,{}),bgColor:"bg-purple-100",textColor:"text-purple-600"}),e.jsx(v,{title:"Leads",value:i.summary.totalLeads,icon:e.jsx(A,{}),bgColor:"bg-yellow-100",textColor:"text-yellow-600"}),e.jsx(v,{title:"Conversion Rate",value:`${i.summary.conversionRate}%`,icon:e.jsx(C,{}),bgColor:"bg-red-100",textColor:"text-red-600"})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8 mb-8",children:[e.jsx(c,{children:e.jsx(f,{title:"Revenue",description:"Revenue over time",actions:[{label:"View Detailed Report",onClick:()=>{}},{label:"Compare to Previous Period",onClick:()=>{}}],children:e.jsx(y,{data:i.revenueData,labels:i.dateLabels,colors:["rgba(16, 185, 129, 0.5)"],height:250,showGrid:!0,options:{yAxisTitle:"Revenue ($)",datasetLabels:["Revenue"]}})})}),e.jsx(c,{children:e.jsx(f,{title:"Orders",description:"Orders over time",actions:[{label:"View All Orders",onClick:()=>{}}],children:e.jsx(g,{data:i.ordersData,labels:i.dateLabels,colors:["#3B82F6"],height:250,showGrid:!0,options:{yAxisTitle:"Orders",datasetLabels:["Orders"]}})})}),e.jsx(c,{children:e.jsx(f,{title:"New Users",description:"User growth over time",actions:[{label:"View User Details",onClick:()=>{}}],children:e.jsx(p,{data:i.usersData,labels:i.dateLabels,color:"#8B5CF6",height:250,showGrid:!0,options:{yAxisTitle:"New Users",datasetLabels:["Users"]}})})}),e.jsx(c,{children:e.jsx(f,{title:"New Leads",description:"Lead acquisition over time",actions:[{label:"View Lead Sources",onClick:()=>{}}],children:e.jsx(p,{data:i.leadsData,labels:i.dateLabels,color:"#F59E0B",height:250,showGrid:!0,options:{yAxisTitle:"New Leads",datasetLabels:["Leads"]}})})})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6 mb-8",children:[e.jsx("h2",{className:"text-lg font-semibold mb-4",children:"Product Performance"}),e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[e.jsx("thead",{className:"bg-gray-50",children:e.jsxs("tr",{children:[e.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Product"}),e.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Sales"}),e.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Revenue"}),e.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Conversion Rate"})]})}),e.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:i.productPerformance.map(((t,s)=>e.jsxs("tr",{children:[e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:t.name}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:t.sales}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:t.revenue>0?`$${"number"==typeof t.revenue?t.revenue.toFixed(2):t.revenue}`:"Free"}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:"N/A"!==t.conversionRate?`${t.conversionRate}%`:"N/A"})]},s)))})]})})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8",children:[e.jsx(c,{children:e.jsx(f,{title:"Traffic Sources",description:"Where your visitors come from",children:e.jsx(j,{data:i.trafficSources.data,labels:i.trafficSources.labels,colors:["#8B5CF6","#3B82F6","#10B981","#F59E0B"],size:220})})}),e.jsx(c,{children:e.jsx(f,{title:"Device Types",description:"What devices your visitors use",children:e.jsx(j,{data:i.deviceTypes.data,labels:i.deviceTypes.labels,colors:["#8B5CF6","#3B82F6","#10B981"],size:220})})})]}),e.jsx("div",{className:"mt-8",children:e.jsx(c,{children:e.jsx(f,{title:"Weekly Activity Heatmap",description:"User activity patterns by day and hour "+(h?`(${h.totalEvents} events)`:""),actions:[{label:"View Activity Details",onClick:()=>{}}],children:F?e.jsxs("div",{className:"flex items-center justify-center h-64",children:[e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"}),e.jsx("span",{className:"ml-2 text-gray-600",children:"Loading heatmap..."})]}):e.jsx(b,{data:(null==h?void 0:h.heatmapData)||[[0,0,0,0,0,0,0],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0]],xLabels:(null==(t=null==h?void 0:h.labels)?void 0:t.days)||["Mon","Tue","Wed","Thu","Fri","Sat","Sun"],yLabels:(null==(r=null==h?void 0:h.labels)?void 0:r.hours)||["12AM","1AM","2AM","3AM","4AM","5AM","6AM","7AM","8AM","9AM","10AM","11AM","12PM","1PM","2PM","3PM","4PM","5PM","6PM","7PM","8PM","9PM","10PM","11PM"],colorScale:"purple",height:400,options:{xAxisTitle:"Day of Week",yAxisTitle:"Time of Day"}})})})})]})})})};export{$ as default};
