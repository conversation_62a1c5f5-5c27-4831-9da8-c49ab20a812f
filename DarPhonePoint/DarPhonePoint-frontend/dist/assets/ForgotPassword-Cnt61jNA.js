import{j as e}from"./ui-CLS_rXuQ.js";import{r as s,L as a}from"./router-Bmo9-suO.js";import{F as t,a as r,b as l,E as i,c as m}from"./index-DYatNWYn.js";import{c as d,a as o}from"./index.esm-BIYjD6qP.js";import"./vendor-mk1rzcpA.js";import"./hoist-non-react-statics.cjs-CnJfjGH_.js";import"./analytics-cUfjo-76.js";import"./index-DQKqxzWd.js";const n=()=>{const[n,c]=s.useState(""),[x,u]=s.useState(""),[p,h]=s.useState(!1),j=d({email:o().email("Invalid email address").required("Email is required")});return p?e.jsx("div",{className:"min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8",children:e.jsxs("div",{className:"sm:mx-auto sm:w-full sm:max-w-md",children:[e.jsx("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900",children:"Check your email"}),e.jsx("div",{className:"mt-8 sm:mx-auto sm:w-full sm:max-w-md",children:e.jsxs("div",{className:"bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10",children:[e.jsx("div",{className:"mb-4 bg-green-50 border border-green-200 text-green-600 px-4 py-3 rounded",children:n}),e.jsx("p",{className:"text-sm text-gray-600 mb-4",children:"We've sent a password reset link to your email address. Please check your inbox and follow the instructions to reset your password."}),e.jsx("div",{className:"text-center",children:e.jsx(a,{to:"/login",className:"font-medium text-purple-600 hover:text-purple-500",children:"Back to login"})})]})})]})}):e.jsxs("div",{className:"min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8",children:[e.jsxs("div",{className:"sm:mx-auto sm:w-full sm:max-w-md",children:[e.jsx("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900",children:"Forgot your password?"}),e.jsx("p",{className:"mt-2 text-center text-sm text-gray-600",children:"Enter your email address and we'll send you a link to reset your password."})]}),e.jsx("div",{className:"mt-8 sm:mx-auto sm:w-full sm:max-w-md",children:e.jsxs("div",{className:"bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10",children:[x&&e.jsx("div",{className:"mb-4 bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded",children:x}),e.jsx(t,{initialValues:{email:""},validationSchema:j,onSubmit:async(e,{setSubmitting:s})=>{var a,t;try{u(""),c("");const s=await m.auth.requestPasswordReset(e.email);s.success?(c("Password reset email sent! Please check your inbox."),h(!0)):u(s.message||"Failed to send reset email")}catch(r){u((null==(t=null==(a=r.response)?void 0:a.data)?void 0:t.message)||"Failed to send reset email")}finally{s(!1)}},children:({isSubmitting:s})=>e.jsxs(r,{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:"Email address"}),e.jsxs("div",{className:"mt-1",children:[e.jsx(l,{id:"email",name:"email",type:"email",autoComplete:"email",className:"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-purple-500 focus:border-purple-500",placeholder:"Enter your email address"}),e.jsx(i,{name:"email",component:"div",className:"mt-1 text-sm text-red-600"})]})]}),e.jsx("div",{children:e.jsx("button",{type:"submit",disabled:s,className:"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-50",children:s?"Sending...":"Send reset link"})}),e.jsx("div",{className:"text-center",children:e.jsx(a,{to:"/login",className:"font-medium text-purple-600 hover:text-purple-500",children:"Back to login"})})]})})]})})]})};export{n as default};
