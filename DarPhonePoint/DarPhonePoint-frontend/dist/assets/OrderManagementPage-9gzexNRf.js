import{j as e}from"./ui-CLS_rXuQ.js";import{r as t,L as a}from"./router-Bmo9-suO.js";import{a as s,L as r,B as l,A as n}from"./index-DQKqxzWd.js";import{P as i}from"./PageHeader-BTn9jdY4.js";import{F as d,P as o}from"./Pagination-DhJHA7Qs.js";import{E as c}from"./ErrorBoundary-jAhIDQkO.js";import"./vendor-mk1rzcpA.js";const m=()=>{const[m,x]=t.useState([]),[u,p]=t.useState([]),[h,g]=t.useState(!0),[w,j]=t.useState(""),[y,v]=t.useState(""),[f,N]=t.useState("all"),[b,k]=t.useState("all"),[_,L]=t.useState(!1),[D,S]=t.useState(1),[C,P]=t.useState(10);t.useEffect((()=>{(async()=>{try{if(!localStorage.getItem("token"))return j("You must be logged in to view this page."),void g(!1);const e=await(async()=>{const e=await s("get","/orders/admin/all");return e.data||e})(),t=(e||[]).map((e=>{var t;return{...e,customer_email:e.customer_email||(null==(t=e.user)?void 0:t.email)||"Unknown",payment_status:e.payment_status||"pending",created_at:e.created_at||new Date,updated_at:e.updated_at||new Date}}));x(t),p(t),g(!1)}catch(e){j("Failed to load orders. Please try again later."),g(!1)}})()}),[]),t.useEffect((()=>{let e=[...m];if(y){const t=y.toLowerCase();e=e.filter((e=>{var a,s;return(null==(a=e.user)?void 0:a.name)&&e.user.name.toLowerCase().includes(t)||(null==(s=e.user)?void 0:s.email)&&e.user.email.toLowerCase().includes(t)||e.customer_email&&e.customer_email.toLowerCase().includes(t)||e.transaction_id&&e.transaction_id.toLowerCase().includes(t)}))}if("all"!==f&&(e=e.filter((e=>e.payment_status===f))),"all"!==b){const t=new Date,a=new Date(t.getFullYear(),t.getMonth(),t.getDate());if("today"===b)e=e.filter((e=>new Date(e.created_at)>=a));else if("week"===b){const t=new Date(a);t.setDate(t.getDate()-7),e=e.filter((e=>new Date(e.created_at)>=t))}else if("month"===b){const t=new Date(a);t.setMonth(t.getMonth()-1),e=e.filter((e=>new Date(e.created_at)>=t))}}p(e),S(1)}),[y,f,b,m]);const M=async(e,t)=>{try{await(async(e,t)=>{const a=await s("put",`/orders/admin/${e}/status`,{status:t});return a.data||a})(e,t);const a=m.map((a=>a._id===e?{...a,payment_status:t}:a));x(a),p((a=>a.map((a=>a._id===e?{...a,payment_status:t}:a))))}catch(a){j("Failed to update order status. Please try again later.")}};if(h)return e.jsx(r,{fullPage:!0,text:"Loading orders..."});const B=u.reduce(((e,t)=>"completed"===t.payment_status?e+t.amount:e),0);return e.jsx("div",{className:"bg-gray-50 py-12",children:e.jsx("div",{className:"container mx-auto px-4",children:e.jsxs("div",{className:"max-w-7xl mx-auto",children:[e.jsx(i,{title:"Order Management",description:"Manage and track your orders",breadcrumbs:[{label:"Orders",path:"/admin/orders"}],actions:e.jsxs(l,{onClick:async()=>{L(!0);try{const e=(e=>{const t=e.map((e=>{var t,a;return[e._id,(null==(t=e.user)?void 0:t.name)||"Guest",e.customer_email,null==(a=e.product)?void 0:a.name,`$${e.amount}`,e.payment_status,new Date(e.created_at).toLocaleDateString()]}));return[["Order ID","Customer","Email","Product","Amount","Status","Date"].join(","),...t.map((e=>e.join(",")))].join("\n")})(u),t=new Blob([e],{type:"text/csv;charset=utf-8;"}),a=URL.createObjectURL(t),s=document.createElement("a");s.href=a,s.setAttribute("download",`orders_export_${(new Date).toISOString().split("T")[0]}.csv`),document.body.appendChild(s),s.click(),document.body.removeChild(s),L(!1)}catch(e){j("Failed to export orders. Please try again later."),L(!1)}},isLoading:_,children:[e.jsx("svg",{className:"h-5 w-5 mr-2",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"})}),"Export CSV"]})}),w&&e.jsx(n,{type:"error",message:w,onClose:()=>j(""),className:"mb-8"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8",children:[e.jsx("div",{className:"bg-white rounded-lg shadow-md p-6",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"p-3 rounded-full bg-purple-100 text-purple-600 mr-4",children:e.jsx("svg",{className:"h-6 w-6",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"})})}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Total Orders"}),e.jsx("p",{className:"text-2xl font-bold",children:u.length})]})]})}),e.jsx("div",{className:"bg-white rounded-lg shadow-md p-6",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"p-3 rounded-full bg-green-100 text-green-600 mr-4",children:e.jsx("svg",{className:"h-6 w-6",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Total Revenue"}),e.jsxs("p",{className:"text-2xl font-bold",children:["$",B]})]})]})}),e.jsx("div",{className:"bg-white rounded-lg shadow-md p-6",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"p-3 rounded-full bg-blue-100 text-blue-600 mr-4",children:e.jsx("svg",{className:"h-6 w-6",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})})}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Completed Orders"}),e.jsx("p",{className:"text-2xl font-bold",children:u.filter((e=>"completed"===e.payment_status)).length})]})]})}),e.jsx("div",{className:"bg-white rounded-lg shadow-md p-6",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"p-3 rounded-full bg-yellow-100 text-yellow-600 mr-4",children:e.jsx("svg",{className:"h-6 w-6",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})})}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Pending Orders"}),e.jsx("p",{className:"text-2xl font-bold",children:u.filter((e=>"pending"===e.payment_status)).length})]})]})})]}),e.jsx(c,{children:e.jsx(d,{showSearch:!0,searchPlaceholder:"Search by name, email, or transaction ID",onSearch:v,filters:[{name:"status",label:"Status",type:"select",options:[{value:"all",label:"All Statuses"},{value:"pending",label:"Pending"},{value:"completed",label:"Completed"},{value:"failed",label:"Failed"},{value:"refunded",label:"Refunded"}]},{name:"dateRange",label:"Date Range",type:"select",options:[{value:"all",label:"All Time"},{value:"today",label:"Today"},{value:"week",label:"Last 7 Days"},{value:"month",label:"Last 30 Days"}]}],onFilterChange:e=>{N(e.status||"all"),k(e.dateRange||"all")}})}),e.jsx(c,{showDetails:!1,fallbackAction:{label:"Refresh Data",onClick:()=>window.location.reload()},children:e.jsxs("div",{className:"bg-white rounded-lg shadow-md overflow-hidden",children:[e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[e.jsx("thead",{className:"bg-gray-50",children:e.jsxs("tr",{children:[e.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Order ID"}),e.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Customer"}),e.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Product"}),e.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Amount"}),e.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),e.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Date"}),e.jsx("th",{scope:"col",className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),e.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:u.length>0?u.slice((D-1)*C,D*C).map((t=>{var s,r;return e.jsxs("tr",{children:[e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:t._id}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsx("div",{className:"flex items-center",children:e.jsxs("div",{className:"ml-4",children:[e.jsx("div",{className:"text-sm font-medium text-gray-900",children:(null==(s=t.user)?void 0:s.name)||"Guest"}),e.jsx("div",{className:"text-sm text-gray-500",children:t.customer_email})]})})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:null==(r=t.product)?void 0:r.name}),e.jsxs("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-medium",children:["$",t.amount]}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsxs("select",{className:"text-xs font-medium rounded-full px-2.5 py-1 border-0 "+("completed"===t.payment_status?"bg-green-100 text-green-800":"pending"===t.payment_status?"bg-yellow-100 text-yellow-800":"failed"===t.payment_status?"bg-red-100 text-red-800":"bg-gray-100 text-gray-800"),value:t.payment_status,onChange:e=>M(t._id,e.target.value),children:[e.jsx("option",{value:"pending",children:"Pending"}),e.jsx("option",{value:"completed",children:"Completed"}),e.jsx("option",{value:"failed",children:"Failed"}),e.jsx("option",{value:"refunded",children:"Refunded"})]})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:new Date(t.created_at).toLocaleDateString()}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium",children:e.jsxs("div",{className:"flex justify-end space-x-2",children:[e.jsx(a,{to:`/admin/orders/${t._id}`,className:"text-indigo-600 hover:text-indigo-900",children:"View"}),e.jsx("a",{href:`mailto:${t.customer_email}?subject=Regarding your order ${t._id}&body=Hello,\n\nThank you for your order. We wanted to follow up regarding your recent purchase.\n\nBest regards,\nAIXcelerate Team`,className:"text-blue-600 hover:text-blue-900",title:`Send email to ${t.customer_email}`,children:"Email"})]})})]},t._id)})):e.jsx("tr",{children:e.jsx("td",{colSpan:"7",className:"px-6 py-4 text-center text-gray-500",children:e.jsxs("div",{className:"py-8",children:[e.jsx("svg",{className:"mx-auto h-12 w-12 text-gray-400 mb-4",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"})}),e.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No orders yet"}),e.jsx("p",{className:"text-gray-500",children:y||"all"!==f||"all"!==b?"No orders found matching your filters. Try adjusting your search criteria.":"Orders will appear here once customers start making purchases."})]})})})})]})}),e.jsx(o,{currentPage:D,totalPages:Math.ceil(u.length/C),totalItems:u.length,pageSize:C,onPageChange:S,onPageSizeChange:e=>{P(e),S(1)}})]})})]})})})};export{m as default};
