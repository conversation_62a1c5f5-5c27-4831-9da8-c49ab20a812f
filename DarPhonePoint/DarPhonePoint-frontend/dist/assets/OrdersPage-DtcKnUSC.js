import{j as e}from"./ui-CLS_rXuQ.js";import{r as t,L as s}from"./router-Bmo9-suO.js";import{B as r,A as a,s as l}from"./index-DQKqxzWd.js";import"./vendor-mk1rzcpA.js";const n=()=>{const[n,d]=t.useState([]),[o,i]=t.useState(!0),[c,x]=t.useState(""),[m,p]=t.useState("all");t.useEffect((()=>{(async()=>{try{const e=await l({method:"GET",url:`/orders?_t=${Date.now()}`});let t=[];e.data&&e.data.data?t=e.data.data:e.data&&(t=e.data),Array.isArray(t)||(t=[]),d(t),i(!1)}catch(e){x("Failed to load your orders. Please try again later."),i(!1)}})()}),[]);const u=e=>e?e.status?e.status:"completed"===e.payment_status?"completed":"processing":"unknown",h=e=>e&&(e.amount||e.total)||0,g="all"===m?n:n.filter((e=>u(e)===m));return o?e.jsx("div",{className:"flex justify-center items-center min-h-screen bg-gray-50",children:e.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500"})}):e.jsx("div",{className:"bg-gray-50 py-12",children:e.jsx("div",{className:"container mx-auto px-4",children:e.jsxs("div",{className:"max-w-5xl mx-auto",children:[e.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center mb-8",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold mb-2",children:"Your Orders"}),e.jsx("p",{className:"text-gray-600",children:"View and manage your orders"})]}),e.jsx("div",{className:"mt-4 md:mt-0",children:e.jsx(s,{to:"/dashboard",children:e.jsx(r,{variant:"outline",children:"Back to Dashboard"})})})]}),c&&e.jsx(a,{type:"error",message:c,onClose:()=>x(""),className:"mb-8"}),e.jsx("div",{className:"mb-8 flex justify-center",children:e.jsxs("div",{className:"inline-flex rounded-md shadow-sm",children:[e.jsx("button",{type:"button",className:"px-4 py-2 text-sm font-medium rounded-l-md "+("all"===m?"bg-purple-600 text-white":"bg-white text-gray-700 hover:bg-gray-50"),onClick:()=>p("all"),children:"All Orders"}),e.jsx("button",{type:"button",className:"px-4 py-2 text-sm font-medium "+("completed"===m?"bg-purple-600 text-white":"bg-white text-gray-700 hover:bg-gray-50"),onClick:()=>p("completed"),children:"Completed"}),e.jsx("button",{type:"button",className:"px-4 py-2 text-sm font-medium "+("processing"===m?"bg-purple-600 text-white":"bg-white text-gray-700 hover:bg-gray-50"),onClick:()=>p("processing"),children:"Processing"}),e.jsx("button",{type:"button",className:"px-4 py-2 text-sm font-medium rounded-r-md "+("cancelled"===m?"bg-purple-600 text-white":"bg-white text-gray-700 hover:bg-gray-50"),onClick:()=>p("cancelled"),children:"Cancelled"})]})}),g.length>0?e.jsx("div",{className:"bg-white rounded-lg shadow-md overflow-hidden",children:e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[e.jsx("thead",{className:"bg-gray-50",children:e.jsxs("tr",{children:[e.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Order ID"}),e.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Product"}),e.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Date"}),e.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Total"}),e.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),e.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),e.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:g.filter((e=>e&&e._id&&e.product&&e.product.name)).map((t=>{var r,a,l,n,d;try{return e.jsxs("tr",{children:[e.jsxs("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:[t._id.substring(0,8),"..."]}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"flex-shrink-0 h-10 w-10 bg-gradient-to-br from-purple-100 to-blue-100 rounded-md flex items-center justify-center",children:e.jsx("span",{className:"text-purple-600 font-semibold text-sm",children:(null==(a=null==(r=t.product)?void 0:r.name)?void 0:a.substring(0,2).toUpperCase())||"NA"})}),e.jsxs("div",{className:"ml-4",children:[e.jsx("div",{className:"text-sm font-medium text-gray-900",children:(null==(l=t.product)?void 0:l.name)||"Product Unavailable"}),e.jsx("div",{className:"text-sm text-gray-500",children:(null==(n=t.product)?void 0:n.product_type)||"Unknown"})]})]})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:new Date(t.created_at).toLocaleDateString()}),e.jsxs("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:[e.jsxs("span",{className:"text-gray-900 font-semibold",children:["$",h(t).toFixed(2)]}),"completed"===t.payment_status&&e.jsx("span",{className:"ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800",children:"Paid"})]}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:(()=>{const s=u(t);return e.jsx("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium "+("completed"===s?"bg-green-100 text-green-800":"processing"===s?"bg-yellow-100 text-yellow-800":"cancelled"===s?"bg-red-100 text-red-800":"bg-gray-100 text-gray-800"),children:"completed"===s?"Completed":"processing"===s?"Processing":"cancelled"===s?"Cancelled":s})})()}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:e.jsxs("div",{className:"flex space-x-2",children:["completed"===u(t)&&(null==(d=t.product)?void 0:d._id)&&e.jsxs(s,{to:`/downloads/${t.product._id}`,className:"inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded shadow-sm text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500",children:[e.jsx("svg",{className:"h-4 w-4 mr-1",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"})}),"Download"]}),e.jsxs(s,{to:`/orders/${t._id}`,className:"inline-flex items-center px-3 py-1 border border-gray-300 text-xs font-medium rounded shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500",children:[e.jsxs("svg",{className:"h-4 w-4 mr-1",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:[e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"}),e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"})]}),"Details"]})]})})]},t._id)}catch(o){return null}}))})]})})}):e.jsxs("div",{className:"bg-white rounded-lg shadow-md p-8 text-center",children:[e.jsx("svg",{className:"h-16 w-16 text-gray-400 mx-auto mb-4",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"})}),e.jsx("h2",{className:"text-2xl font-bold mb-4",children:"No Orders Found"}),e.jsx("p",{className:"text-gray-600 mb-6",children:"all"===m?"You haven't placed any orders yet.":`You don't have any ${m} orders.`}),e.jsx("div",{className:"flex justify-center",children:e.jsx(s,{to:"/products",children:e.jsx(r,{children:"Browse Products"})})})]})]})})})};export{n as default};
