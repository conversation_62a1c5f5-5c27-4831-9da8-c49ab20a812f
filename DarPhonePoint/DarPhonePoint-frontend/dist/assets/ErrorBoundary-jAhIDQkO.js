var r=Object.defineProperty,e=(e,t,s)=>((e,t,s)=>t in e?r(e,t,{enumerable:!0,configurable:!0,writable:!0,value:s}):e[t]=s)(e,"symbol"!=typeof t?t+"":t,s);import{j as t}from"./ui-CLS_rXuQ.js";import{r as s}from"./router-Bmo9-suO.js";import{B as o}from"./index-DQKqxzWd.js";class n extends s.Component{constructor(r){super(r),e(this,"handleReset",(()=>{this.setState({hasError:!1,error:null,errorInfo:null})})),e(this,"handleReportError",(()=>{alert("Error reported to the development team. Thank you!")})),this.state={hasError:!1,error:null,errorInfo:null}}static getDerivedStateFromError(r){return{hasError:!0,error:r}}componentDidCatch(r,e){this.setState({errorInfo:e})}render(){return this.state.hasError?t.jsxs("div",{className:"bg-white p-6 rounded-lg shadow-md",children:[t.jsxs("div",{className:"flex items-center mb-4",children:[t.jsx("div",{className:"flex-shrink-0",children:t.jsx("svg",{className:"h-10 w-10 text-red-500",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:t.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"})})}),t.jsxs("div",{className:"ml-4",children:[t.jsx("h3",{className:"text-lg font-medium text-gray-900",children:"Something went wrong"}),t.jsx("p",{className:"text-sm text-gray-500",children:"We've encountered an error while rendering this component."})]})]}),this.props.showDetails&&t.jsxs("div",{className:"mt-4 p-4 bg-gray-50 rounded-md overflow-auto max-h-64",children:[t.jsx("p",{className:"text-sm font-medium text-gray-900 mb-2",children:"Error details:"}),t.jsxs("pre",{className:"text-xs text-gray-700 whitespace-pre-wrap",children:[this.state.error&&this.state.error.toString(),this.state.errorInfo&&this.state.errorInfo.componentStack]})]}),t.jsxs("div",{className:"mt-6 flex space-x-3",children:[t.jsx(o,{onClick:this.handleReset,children:"Try Again"}),t.jsx(o,{variant:"outline",onClick:this.handleReportError,children:"Report Error"}),this.props.fallbackAction&&t.jsx(o,{variant:"outline",onClick:this.props.fallbackAction.onClick,children:this.props.fallbackAction.label})]})]}):this.props.children}}export{n as E};
