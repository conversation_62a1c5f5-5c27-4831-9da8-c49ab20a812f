#!/usr/bin/env node

/**
 * Service Cleanup Script
 * Removes unnecessary over-engineered services to simplify architecture
 */

const fs = require('fs');
const path = require('path');

console.log('🧹 Starting Service Architecture Cleanup...\n');

// List of unnecessary services to remove (keeping core functionality)
const unnecessaryServices = [
  'advancedCacheService.js',
  'enhancedMonitoringService.js', 
  'databaseOptimizationService.js',
  'cdnService.js',
  'disasterRecoveryService.js',
  'backupSchedulerService.js',
  'cacheOptimizationService.js',
  'performanceMonitoringService.js',
  'productionMonitoringService.js',
  'queryOptimizationService.js',
  'securityMonitoringService.js',
  'sessionManagementService.js',
  'webhookBatchProcessor.js'
];

// Core services to keep
const coreServices = [
  'auditLogService.js',
  'cacheService.js', 
  'emailService.js',
  'emailSender.js',
  'emailTemplateService.js',
  'emailQueueService.js',
  'jwtBlacklistService.js',
  'monitoringService.js',
  'paymentService.js',
  'phoneInventoryService.js',
  'smsService.js',
  'tanzaniaPaymentService.js',
  'whatsappService.js',
  'mockPaymentService.js',
  'orderNotificationService.js',
  'inputSanitizationService.js',
  'errorLoggingService.js',
  'databaseHealthService.js'
];

const servicesDir = path.join(__dirname, 'DarPhonePoint-backend/services');

console.log('📋 Services Analysis:');
console.log(`   Core services to keep: ${coreServices.length}`);
console.log(`   Unnecessary services to remove: ${unnecessaryServices.length}\n`);

// Check which services actually exist
const existingServices = fs.readdirSync(servicesDir);
const servicesToRemove = unnecessaryServices.filter(service => 
  existingServices.includes(service)
);

console.log('🗑️ Removing unnecessary services:');
servicesToRemove.forEach(service => {
  const servicePath = path.join(servicesDir, service);
  try {
    // Move to backup instead of deleting
    const backupDir = path.join(__dirname, 'DarPhonePoint-backend/services/backup');
    if (!fs.existsSync(backupDir)) {
      fs.mkdirSync(backupDir, { recursive: true });
    }
    
    const backupPath = path.join(backupDir, service);
    fs.renameSync(servicePath, backupPath);
    console.log(`   ✅ Moved ${service} to backup`);
  } catch (error) {
    console.log(`   ⚠️ Could not move ${service}: ${error.message}`);
  }
});

console.log('\n✅ Core services remaining:');
const remainingServices = fs.readdirSync(servicesDir).filter(file => 
  file.endsWith('.js') && !file.startsWith('.')
);
remainingServices.forEach(service => {
  console.log(`   📦 ${service}`);
});

// Update middleware imports to remove references to deleted services
console.log('\n🔧 Updating middleware references...');

const middlewareFiles = [
  'DarPhonePoint-backend/middleware/performanceMiddleware.js',
  'DarPhonePoint-backend/middleware/performanceTrackingMiddleware.js'
];

middlewareFiles.forEach(middlewareFile => {
  const middlewarePath = path.join(__dirname, middlewareFile);
  if (fs.existsSync(middlewarePath)) {
    try {
      let content = fs.readFileSync(middlewarePath, 'utf8');
      
      // Remove imports of deleted services
      unnecessaryServices.forEach(service => {
        const serviceName = service.replace('.js', '');
        const importPattern = new RegExp(`const\\s+${serviceName}\\s*=\\s*require\\([^)]+\\);?\\s*`, 'g');
        content = content.replace(importPattern, '');
      });
      
      fs.writeFileSync(middlewarePath, content);
      console.log(`   ✅ Updated ${middlewareFile}`);
    } catch (error) {
      console.log(`   ⚠️ Could not update ${middlewareFile}: ${error.message}`);
    }
  }
});

console.log('\n📊 Cleanup Summary:');
console.log(`   Services moved to backup: ${servicesToRemove.length}`);
console.log(`   Core services remaining: ${remainingServices.length}`);
console.log(`   Architecture simplified: ${Math.round((servicesToRemove.length / (servicesToRemove.length + remainingServices.length)) * 100)}% reduction`);

console.log('\n🎉 Service architecture cleanup completed!');
console.log('\n📋 Next steps:');
console.log('1. Restart the backend server');
console.log('2. Test core functionality');
console.log('3. Monitor for any missing service errors');
console.log('4. Services are backed up in services/backup/ if needed');

console.log('\n✅ Cleanup script completed successfully!');
