# Phone Point Dar Production Setup Guide

This guide will help you deploy Phone Point Dar to a production environment.

## 🚀 Quick Start

```bash
# 1. Clone the repository
git clone <your-repo-url>
cd DarPhonePoint

# 2. Configure production environment
cp DarPhonePoint-backend/.env.example DarPhonePoint-backend/.env.production
# Edit the .env.production file with your production values

# 3. Run the deployment script
./scripts/deploy-production.sh
```

## 📋 Prerequisites

### System Requirements
- **OS**: Ubuntu 20.04+ or CentOS 8+ (recommended)
- **RAM**: Minimum 4GB, recommended 8GB+
- **Storage**: Minimum 50GB SSD
- **CPU**: 2+ cores recommended
- **Network**: Stable internet connection

### Software Requirements
- Docker 20.10+
- Docker Compose 2.0+
- Git 2.0+
- Nginx (if not using containerized version)
- SSL certificates (Let's Encrypt recommended)

## 🔧 Detailed Setup

### 1. Server Preparation

```bash
# Update system packages
sudo apt update && sudo apt upgrade -y

# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# Logout and login again to apply Docker group changes
```

### 2. Environment Configuration

Create and configure your production environment file:

```bash
cp DarPhonePoint-backend/.env.example DarPhonePoint-backend/.env.production
```

**Critical Environment Variables to Configure:**

```env
# Database
MONGODB_URI=mongodb://mongodb:27017/phonepointdar
MONGODB_ROOT_USERNAME=your-secure-username
MONGODB_ROOT_PASSWORD=your-secure-password

# Security
JWT_SECRET=your-super-secure-jwt-secret-at-least-32-characters
BCRYPT_SALT_ROUNDS=12

# Email Service
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# SMS Service (Beem Africa)
SMS_ENABLED=true
BEEM_API_KEY=your-beem-africa-api-key
BEEM_SOURCE_ADDR=PhonePointDar

# WhatsApp Business API
WHATSAPP_ENABLED=true
WHATSAPP_ACCESS_TOKEN=your-whatsapp-access-token
WHATSAPP_PHONE_NUMBER_ID=your-phone-number-id

# Domain and SSL
DOMAIN=phonepointdar.com
REACT_APP_API_URL=https://phonepointdar.com/api
```

### 3. SSL Certificate Setup

#### Option A: Let's Encrypt (Recommended)

```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx

# Obtain SSL certificate
sudo certbot --nginx -d phonepointdar.com -d www.phonepointdar.com

# Copy certificates to project
sudo mkdir -p ssl
sudo cp /etc/letsencrypt/live/phonepointdar.com/fullchain.pem ssl/phonepointdar.com.crt
sudo cp /etc/letsencrypt/live/phonepointdar.com/privkey.pem ssl/phonepointdar.com.key
sudo chown $USER:$USER ssl/*
```

#### Option B: Custom SSL Certificate

```bash
# Create ssl directory
mkdir -p ssl

# Copy your certificates
cp your-certificate.crt ssl/phonepointdar.com.crt
cp your-private-key.key ssl/phonepointdar.com.key
```

### 4. Database Setup

```bash
# Create MongoDB data directory
sudo mkdir -p /data/mongodb
sudo chown $USER:$USER /data/mongodb

# Create MongoDB configuration
mkdir -p mongodb
cat > mongodb/mongod.conf << EOF
storage:
  dbPath: /data/db
  journal:
    enabled: true

systemLog:
  destination: file
  logAppend: true
  path: /var/log/mongodb/mongod.log

net:
  port: 27017
  bindIp: 0.0.0.0

security:
  authorization: enabled

replication:
  replSetName: rs0
EOF
```

### 5. Firewall Configuration

```bash
# Configure UFW firewall
sudo ufw enable
sudo ufw allow ssh
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw allow 5000/tcp  # Backend API (if needed)
```

### 6. Deployment

```bash
# Make deployment script executable
chmod +x scripts/deploy-production.sh

# Run deployment
./scripts/deploy-production.sh
```

## 🔍 Monitoring and Maintenance

### Health Checks

```bash
# Check application health
./scripts/deploy-production.sh health

# Check container status
docker-compose -f docker-compose.production.yml ps

# View logs
docker-compose -f docker-compose.production.yml logs -f
```

### Monitoring Services

Access monitoring dashboards:
- **Grafana**: http://your-domain:3000
- **Prometheus**: http://your-domain:9090
- **Kibana**: http://your-domain:5601

### Backup and Recovery

```bash
# Manual backup
docker exec phonepointdar-mongodb mongodump --out /backup/$(date +%Y%m%d)

# Restore from backup
docker exec phonepointdar-mongodb mongorestore /backup/YYYYMMDD
```

## 🔒 Security Checklist

- [ ] Strong passwords for all services
- [ ] SSL certificates properly configured
- [ ] Firewall rules configured
- [ ] Database authentication enabled
- [ ] Regular security updates
- [ ] Backup strategy implemented
- [ ] Monitoring and alerting configured

## 📱 Tanzania-Specific Configuration

### SMS Provider Setup (Beem Africa)

1. Register at [Beem Africa](https://beem.africa)
2. Get API credentials
3. Configure in `.env.production`:
   ```env
   BEEM_API_KEY=your-api-key
   BEEM_SOURCE_ADDR=PhonePointDar
   ```

### WhatsApp Business API

1. Set up WhatsApp Business Account
2. Get API credentials from Meta
3. Configure webhook URL: `https://your-domain/api/whatsapp/webhook`

### Payment Integration

Configure Tanzania payment methods:
- M-Pesa integration
- Tigo Pesa integration
- Airtel Money integration

## 🚨 Troubleshooting

### Common Issues

1. **Container won't start**
   ```bash
   docker-compose -f docker-compose.production.yml logs service-name
   ```

2. **Database connection issues**
   ```bash
   docker exec phonepointdar-mongodb mongosh --eval "db.runCommand({ping: 1})"
   ```

3. **SSL certificate issues**
   ```bash
   sudo certbot renew --dry-run
   ```

4. **Performance issues**
   ```bash
   docker stats
   htop
   ```

### Log Locations

- Application logs: `/var/log/phonepointdar/`
- Nginx logs: `/var/log/nginx/`
- MongoDB logs: `/var/log/mongodb/`
- Docker logs: `docker-compose logs`

## 📞 Support

For production support:
- Check logs first
- Review monitoring dashboards
- Contact development team with specific error messages
- Include system information and logs when reporting issues

## 🔄 Updates and Maintenance

### Regular Updates

```bash
# Pull latest code and deploy
git pull origin main
./scripts/deploy-production.sh

# Update system packages monthly
sudo apt update && sudo apt upgrade -y

# Renew SSL certificates (automated with Let's Encrypt)
sudo certbot renew
```

### Scaling

To scale the application:
1. Use Docker Swarm or Kubernetes
2. Set up load balancer
3. Configure database replication
4. Implement CDN for static assets

---

**🎉 Congratulations!** Your Phone Point Dar application is now running in production!

Visit your domain to see the live application and monitor the dashboards to ensure everything is running smoothly.
