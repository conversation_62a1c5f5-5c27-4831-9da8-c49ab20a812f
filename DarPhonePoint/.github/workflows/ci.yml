name: Phone Point Dar CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  test-backend:
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        node-version: [18.x, 20.x]
    
    services:
      mongodb:
        image: mongo:7.0
        ports:
          - 27017:27017
        options: >-
          --health-cmd "mongosh --eval 'db.runCommand({ping: 1})'"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Setup Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'
        cache-dependency-path: DarPhonePoint-backend/package-lock.json
    
    - name: Install backend dependencies
      working-directory: ./DarPhonePoint-backend
      run: npm ci
    
    - name: Run backend linting
      working-directory: ./DarPhonePoint-backend
      run: npm run lint
    
    - name: Run backend tests
      working-directory: ./DarPhonePoint-backend
      run: npm run test:ci
      env:
        NODE_ENV: test
        MONGODB_URI: mongodb://localhost:27017/phonepointdar_test
        JWT_SECRET: test-jwt-secret-for-ci
        JWT_EXPIRES_IN: 7d
        BCRYPT_SALT_ROUNDS: 10
    
    - name: Upload backend coverage reports
      uses: codecov/codecov-action@v3
      with:
        file: ./DarPhonePoint-backend/coverage/lcov.info
        flags: backend
        name: backend-coverage
        fail_ci_if_error: false

  test-frontend:
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        node-version: [18.x, 20.x]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Setup Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'
        cache-dependency-path: DarPhonePoint-frontend/package-lock.json
    
    - name: Install frontend dependencies
      working-directory: ./DarPhonePoint-frontend
      run: npm ci
    
    - name: Run frontend linting
      working-directory: ./DarPhonePoint-frontend
      run: npm run lint
    
    - name: Run frontend tests
      working-directory: ./DarPhonePoint-frontend
      run: npm run test:ci
      env:
        CI: true
    
    - name: Build frontend
      working-directory: ./DarPhonePoint-frontend
      run: npm run build
    
    - name: Upload frontend coverage reports
      uses: codecov/codecov-action@v3
      with:
        file: ./DarPhonePoint-frontend/coverage/lcov.info
        flags: frontend
        name: frontend-coverage
        fail_ci_if_error: false

  security-scan:
    runs-on: ubuntu-latest
    needs: [test-backend, test-frontend]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: '.'
        format: 'sarif'
        output: 'trivy-results.sarif'
    
    - name: Upload Trivy scan results to GitHub Security tab
      uses: github/codeql-action/upload-sarif@v2
      if: always()
      with:
        sarif_file: 'trivy-results.sarif'

  deploy-staging:
    runs-on: ubuntu-latest
    needs: [test-backend, test-frontend, security-scan]
    if: github.ref == 'refs/heads/develop' && github.event_name == 'push'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20.x'
    
    - name: Build frontend for staging
      working-directory: ./DarPhonePoint-frontend
      run: |
        npm ci
        npm run build
      env:
        REACT_APP_API_URL: ${{ secrets.STAGING_API_URL }}
        REACT_APP_ENVIRONMENT: staging
    
    - name: Deploy to staging
      run: |
        echo "Deploying to staging environment..."
        # Add your staging deployment commands here
        # Example: rsync, docker deploy, etc.

  deploy-production:
    runs-on: ubuntu-latest
    needs: [test-backend, test-frontend, security-scan]
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20.x'
    
    - name: Build frontend for production
      working-directory: ./DarPhonePoint-frontend
      run: |
        npm ci
        npm run build
      env:
        REACT_APP_API_URL: ${{ secrets.PRODUCTION_API_URL }}
        REACT_APP_ENVIRONMENT: production
    
    - name: Deploy to production
      run: |
        echo "Deploying to production environment..."
        # Add your production deployment commands here
        # Example: docker deploy, kubernetes, etc.
    
    - name: Notify deployment success
      if: success()
      run: |
        echo "Production deployment successful!"
        # Add notification logic (Slack, email, etc.)

  performance-test:
    runs-on: ubuntu-latest
    needs: [deploy-staging]
    if: github.ref == 'refs/heads/develop'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20.x'
    
    - name: Install k6
      run: |
        sudo gpg -k
        sudo gpg --no-default-keyring --keyring /usr/share/keyrings/k6-archive-keyring.gpg --keyserver hkp://keyserver.ubuntu.com:80 --recv-keys C5AD17C747E3415A3642D57D77C6C491D6AC1D69
        echo "deb [signed-by=/usr/share/keyrings/k6-archive-keyring.gpg] https://dl.k6.io/deb stable main" | sudo tee /etc/apt/sources.list.d/k6.list
        sudo apt-get update
        sudo apt-get install k6
    
    - name: Run performance tests
      working-directory: ./DarPhonePoint-backend
      run: |
        k6 run tests/performance/load-test.js
      env:
        API_BASE_URL: ${{ secrets.STAGING_API_URL }}

  notify-completion:
    runs-on: ubuntu-latest
    needs: [test-backend, test-frontend, security-scan]
    if: always()
    
    steps:
    - name: Notify team
      run: |
        if [ "${{ needs.test-backend.result }}" == "success" ] && [ "${{ needs.test-frontend.result }}" == "success" ]; then
          echo "✅ All tests passed! Phone Point Dar is ready for deployment."
        else
          echo "❌ Some tests failed. Please check the logs."
        fi
