version: '3.8'

services:
  # Phone Point Dar Backend API
  phonepointdar-backend:
    build:
      context: ./DarPhonePoint-backend
      dockerfile: Dockerfile.production
    container_name: phonepointdar-backend
    restart: unless-stopped
    environment:
      - NODE_ENV=production
      - PORT=5000
    env_file:
      - ./DarPhonePoint-backend/.env.production
    ports:
      - "5000:5000"
    depends_on:
      - mongodb
      - redis
    volumes:
      - ./uploads:/app/uploads
      - ./logs:/app/logs
    networks:
      - phonepointdar-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Phone Point Dar Frontend
  phonepointdar-frontend:
    build:
      context: ./DarPhonePoint-frontend
      dockerfile: Dockerfile.production
      args:
        - REACT_APP_API_URL=${REACT_APP_API_URL}
        - REACT_APP_ENVIRONMENT=production
    container_name: phonepointdar-frontend
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./ssl:/etc/nginx/ssl:ro
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - phonepointdar-backend
    networks:
      - phonepointdar-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # MongoDB Database
  mongodb:
    image: mongo:7.0
    container_name: phonepointdar-mongodb
    restart: unless-stopped
    environment:
      - MONGO_INITDB_ROOT_USERNAME=${MONGODB_ROOT_USERNAME}
      - MONGO_INITDB_ROOT_PASSWORD=${MONGODB_ROOT_PASSWORD}
      - MONGO_INITDB_DATABASE=phonepointdar
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
      - ./mongodb/mongod.conf:/etc/mongod.conf:ro
      - ./mongodb/init-scripts:/docker-entrypoint-initdb.d:ro
    networks:
      - phonepointdar-network
    command: ["mongod", "--config", "/etc/mongod.conf"]
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.runCommand({ping: 1})"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis Cache
  redis:
    image: redis:7.2-alpine
    container_name: phonepointdar-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./redis/redis.conf:/etc/redis/redis.conf:ro
    networks:
      - phonepointdar-network
    command: ["redis-server", "/etc/redis/redis.conf"]
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx Load Balancer (if scaling)
  nginx-lb:
    image: nginx:alpine
    container_name: phonepointdar-nginx-lb
    restart: unless-stopped
    ports:
      - "8080:80"
    volumes:
      - ./nginx/load-balancer.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - phonepointdar-backend
    networks:
      - phonepointdar-network
    profiles:
      - load-balancer

  # Monitoring with Prometheus
  prometheus:
    image: prom/prometheus:latest
    container_name: phonepointdar-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    networks:
      - phonepointdar-network
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    profiles:
      - monitoring

  # Grafana Dashboard
  grafana:
    image: grafana/grafana:latest
    container_name: phonepointdar-grafana
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_ADMIN_PASSWORD}
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    networks:
      - phonepointdar-network
    depends_on:
      - prometheus
    profiles:
      - monitoring

  # Log Management with ELK Stack
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    container_name: phonepointdar-elasticsearch
    restart: unless-stopped
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    ports:
      - "9200:9200"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    networks:
      - phonepointdar-network
    profiles:
      - logging

  logstash:
    image: docker.elastic.co/logstash/logstash:8.11.0
    container_name: phonepointdar-logstash
    restart: unless-stopped
    volumes:
      - ./logging/logstash.conf:/usr/share/logstash/pipeline/logstash.conf:ro
      - ./logs:/var/log/phonepointdar:ro
    networks:
      - phonepointdar-network
    depends_on:
      - elasticsearch
    profiles:
      - logging

  kibana:
    image: docker.elastic.co/kibana/kibana:8.11.0
    container_name: phonepointdar-kibana
    restart: unless-stopped
    ports:
      - "5601:5601"
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    networks:
      - phonepointdar-network
    depends_on:
      - elasticsearch
    profiles:
      - logging

  # Backup Service
  backup:
    image: alpine:latest
    container_name: phonepointdar-backup
    restart: "no"
    volumes:
      - mongodb_data:/backup/mongodb:ro
      - redis_data:/backup/redis:ro
      - ./uploads:/backup/uploads:ro
      - ./backups:/backups
      - ./scripts/backup.sh:/backup.sh:ro
    networks:
      - phonepointdar-network
    command: ["/backup.sh"]
    profiles:
      - backup

volumes:
  mongodb_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  elasticsearch_data:
    driver: local

networks:
  phonepointdar-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
